<?xml version="1.0" encoding="UTF-8"?>
<results version="2">
    <cppcheck version="1.90"/>
    <errors>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="87" column="2"/>
        </error>
        <error id="pointerSize" severity="warning" msg="Size of pointer &apos;szBuf&apos; used instead of size of its data." verbose="Size of pointer &apos;szBuf&apos; used instead of size of its data. This is likely to lead to a buffer overflow. You probably intend to write &apos;sizeof(*szBuf)&apos;." cwe="467">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="187" column="9"/>
        </error>
        <error id="pointerSize" severity="warning" msg="Size of pointer &apos;szBuf&apos; used instead of size of its data." verbose="Size of pointer &apos;szBuf&apos; used instead of size of its data. This is likely to lead to a buffer overflow. You probably intend to write &apos;sizeof(*szBuf)&apos;." cwe="467">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="2201" column="9"/>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="846" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="873" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="913" column="22"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="973" column="22"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: quickData.pDevice" verbose="Uninitialized struct member: quickData.pDevice" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1175" column="24"/>
            <symbol>quickData.pDevice</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1227" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1264" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1377" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1403" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1446" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1472" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1512" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1536" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1576" column="23"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1615" column="23"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1653" column="23"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1697" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1724" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1763" column="23"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1805" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1829" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1869" column="23"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1911" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1935" column="24"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/IEC104Coder.cpp" line="1976" column="23"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="virtualCallInConstructor" severity="warning" msg="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CTcpClientHandler()&apos; at line 94. Dynamic binding is not used." verbose="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CTcpClientHandler()&apos; at line 94. Dynamic binding is not used.">
            <location file0="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/TcpClientHandler.cpp" file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/TcpClientHandler.h" line="46" column="15" info="DisConnect is a virtual function"/>
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/TcpClientHandler.cpp" line="94" column="2" info="Calling DisConnect"/>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: a2v.nDataSize" verbose="Uninitialized struct member: a2v.nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/IEC104/iec104driver/WriteMsg.cpp" line="444" column="22"/>
            <symbol>a2v.nDataSize</symbol>
        </error>
        <error id="invalidPrintfArgType_sint" severity="warning" msg="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;unsigned int&apos;." verbose="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;unsigned int&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/abdrv/PlcTag.cpp" line="1109" column="17"/>
        </error>
        <error id="invalidPrintfArgType_sint" severity="warning" msg="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;unsigned int&apos;." verbose="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;unsigned int&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/abdrv/PlcTag.cpp" line="1135" column="17"/>
        </error>
        <error id="invalidPrintfArgType_sint" severity="warning" msg="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;unsigned int&apos;." verbose="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;unsigned int&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/abdrv/PlcTag.cpp" line="1163" column="13"/>
        </error>
        <error id="invalidPrintfArgType_sint" severity="warning" msg="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;unsigned int&apos;." verbose="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;unsigned int&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/abdrv/PlcTag.cpp" line="1168" column="13"/>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: section.bitRelOffset" verbose="Uninitialized struct member: section.bitRelOffset" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/abdrv/PlcTag.cpp" line="799" column="30"/>
            <symbol>section.bitRelOffset</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: section.dimension0" verbose="Uninitialized struct member: section.dimension0" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/abdrv/PlcTag.cpp" line="799" column="30"/>
            <symbol>section.dimension0</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: bitsection.bitStringCount" verbose="Uninitialized struct member: bitsection.bitStringCount" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/abdrv/PlcTag.cpp" line="1079" column="20"/>
            <symbol>bitsection.bitStringCount</symbol>
        </error>
        <error id="uninitStructMember" severity="error" msg="Uninitialized struct member: bitsection.bitRelOffset" verbose="Uninitialized struct member: bitsection.bitRelOffset" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/abdrv/PlcTag.cpp" line="1081" column="61"/>
            <symbol>bitsection.bitRelOffset</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/CtrlProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/CtrlProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/TaskBatchUpdateData.h" line="90" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/CtrlProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/CtrlProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/CtrlProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DataBlock.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DataBlock.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/TaskBatchUpdateData.h" line="90" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DataBlock.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DataBlock.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DataBlock.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Device.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Device.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Device.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Device.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="nullPointerRedundantCheck" severity="warning" msg="Either the condition &apos;if(pLinkMsg)&apos; is redundant or there is possible null pointer dereference: pLinkMsg." verbose="Either the condition &apos;if(pLinkMsg)&apos; is redundant or there is possible null pointer dereference: pLinkMsg." cwe="476">
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriver/Device.cpp" line="637" column="3" info="Null pointer dereference"/>
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriver/Device.cpp" line="638" column="6" info="Assuming that condition &apos;if(pLinkMsg)&apos; is not redundant"/>
            <symbol>pLinkMsg</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DeviceGroup.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DeviceGroup.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DeviceGroup.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DeviceGroup.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DeviceScanTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DeviceScanTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DeviceScanTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DeviceScanTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Driver.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Driver.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/TaskBatchUpdateData.h" line="90" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Driver.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Driver.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Driver.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DrvConfigLoader.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DrvConfigLoader.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/TaskBatchUpdateData.h" line="90" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DrvConfigLoader.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DrvConfigLoader.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DrvConfigLoader.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DrvCtrlTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/TaskBatchUpdateData.h" line="90" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DrvCtrlTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DrvCtrlTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/DrvCtrlTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/LinkMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/LinkMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/LinkMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/LinkMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Message.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Message.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Message.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Message.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/MsgProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/MsgProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/MsgProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/MsgProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/NopMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/NopMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/NopMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/NopMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/ParseTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/ParseTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/ParseTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/ParseTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/ReadMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/ReadMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/TaskBatchUpdateData.h" line="90" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/ReadMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/ReadMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/ReadMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CReadMsg&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CReadMsg&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriver/ReadMsg.cpp" line="43" column="2"/>
            <symbol>CReadMsg</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CReadMsg&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CReadMsg&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriver/ReadMsg.cpp" line="43" column="2"/>
            <symbol>CReadMsg</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Server.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Server.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/TaskBatchUpdateData.h" line="90" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Server.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Server.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/Server.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/TagDataBlockBuilder.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/TagDataBlockBuilder.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/TagDataBlockBuilder.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CTagDataBlockBuilder&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CTagDataBlockBuilder&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriver/TagDataBlockBuilder.cpp" line="26" column="3"/>
            <symbol>CTagDataBlockBuilder</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CTagDataBlockBuilder&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CTagDataBlockBuilder&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriver/TagDataBlockBuilder.cpp" line="26" column="3"/>
            <symbol>CTagDataBlockBuilder</symbol>
        </error>
        <error id="duplicateAssignExpression" severity="style" msg="Same expression used in consecutive assignments of &apos;nOutTagNum&apos; and &apos;nGrpNum&apos;." verbose="Finding variables &apos;nOutTagNum&apos; and &apos;nGrpNum&apos; that are assigned the same expression is suspicious and might indicate a cut and paste or logic error. Please examine this code carefully to determine if it is correct." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriver/TagDataBlockBuilder.cpp" line="160" column="15"/>
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriver/TagDataBlockBuilder.cpp" line="161" column="15"/>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/TaskBatchUpdateData.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/TaskBatchUpdateData.h" line="90" column="3"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/TaskBatchUpdateData.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/TaskBatchUpdateData.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/TaskBatchUpdateData.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/WriteMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/CIPStruct.h" line="547" column="3"/>
        </error>
        <error id="memsetValueOutOfRange" severity="warning" msg="The 2nd memset() argument &apos;65535&apos; doesn&apos;t fit into an &apos;unsigned char&apos;." verbose="The 2nd memset() argument &apos;65535&apos; doesn&apos;t fit into an &apos;unsigned char&apos;. The 2nd parameter is passed as an &apos;int&apos;, but the function fills the block of memory using the &apos;unsigned char&apos; conversion of this value." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriver/WriteMsg.cpp" line="165" column="23"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/WriteMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/WriteMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriver/WriteMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriver/IOI.h" line="54" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/CtrlProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/CtrlProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/CtrlProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/CtrlProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/CtrlProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/CtrlProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/CtrlProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DataBlock.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DataBlock.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DataBlock.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DataBlock.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DataBlock.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DataBlock.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DataBlock.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Device.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Device.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Device.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Device.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Device.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Device.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Device.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="nullPointerRedundantCheck" severity="warning" msg="Either the condition &apos;if(pLinkMsg)&apos; is redundant or there is possible null pointer dereference: pLinkMsg." verbose="Either the condition &apos;if(pLinkMsg)&apos; is redundant or there is possible null pointer dereference: pLinkMsg." cwe="476">
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Device.cpp" line="617" column="3" info="Null pointer dereference"/>
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Device.cpp" line="618" column="6" info="Assuming that condition &apos;if(pLinkMsg)&apos; is not redundant"/>
            <symbol>pLinkMsg</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DeviceGroup.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DeviceGroup.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DeviceGroup.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DeviceGroup.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DeviceGroup.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DeviceScanTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DeviceScanTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DeviceScanTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DeviceScanTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DeviceScanTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DeviceScanTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DeviceScanTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Driver.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Driver.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Driver.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Driver.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Driver.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Driver.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Driver.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvConfigLoader.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvConfigLoader.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvConfigLoader.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvConfigLoader.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvConfigLoader.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvCtrlTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvCtrlTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvCtrlTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvCtrlTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvCtrlTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/LinkMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/LinkMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/LinkMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/LinkMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/LinkMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/LinkMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/LinkMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Message.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Message.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Message.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Message.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Message.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Message.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Message.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/MsgProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/MsgProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/MsgProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/MsgProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/MsgProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/MsgProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/MsgProcessTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/NopMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/NopMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/NopMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/NopMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/NopMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ParseTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ParseTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ParseTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ParseTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ParseTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ParseTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ParseTask.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ReadMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ReadMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ReadMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ReadMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ReadMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CReadMsg&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CReadMsg&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ReadMsg.cpp" line="41" column="2"/>
            <symbol>CReadMsg</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CReadMsg&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CReadMsg&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ReadMsg.cpp" line="41" column="2"/>
            <symbol>CReadMsg</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ReadMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/ReadMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Server.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Server.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Server.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Server.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Server.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Server.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/Server.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/TagDataBlockBuilder.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/TagDataBlockBuilder.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/TagDataBlockBuilder.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/TagDataBlockBuilder.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/TagDataBlockBuilder.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CTagDataBlockBuilder&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CTagDataBlockBuilder&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/TagDataBlockBuilder.cpp" line="39" column="3"/>
            <symbol>CTagDataBlockBuilder</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CTagDataBlockBuilder&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CTagDataBlockBuilder&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/TagDataBlockBuilder.cpp" line="39" column="3"/>
            <symbol>CTagDataBlockBuilder</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/TagDataBlockBuilder.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/TagDataBlockBuilder.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="memsetValueOutOfRange" severity="warning" msg="The 2nd memset() argument &apos;65535&apos; doesn&apos;t fit into an &apos;unsigned char&apos;." verbose="The 2nd memset() argument &apos;65535&apos; doesn&apos;t fit into an &apos;unsigned char&apos;. The 2nd parameter is passed as an &apos;int&apos;, but the function fills the block of memory using the &apos;unsigned char&apos; conversion of this value." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/WriteMsg.cpp" line="170" column="23"/>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/WriteMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_szRawAddrBuf&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/WriteMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_szRawAddrBuf</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lAddrBufSize&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/WriteMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lAddrBufSize</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/WriteMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lIndex</symbol>
        </error>
        <error id="operatorEqVarError" severity="warning" msg="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." verbose="Member variable &apos;CIOI::m_lSliceIndex&apos; is not assigned a value in &apos;CIOI::operator=&apos;." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/WriteMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/IOI.h" line="57" column="9"/>
            <symbol>CIOI::m_lSliceIndex</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/WriteMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CCIPMessage&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cip/cipdriverx/WriteMsg.cpp" file="/home/<USER>/dr/Source/drivers/cip/cipdriverx/DrvLogHelper.h" line="26" column="3"/>
            <symbol>CCIPMessage</symbol>
        </error>
        <error id="virtualCallInConstructor" severity="warning" msg="Virtual function &apos;Disconnect&apos; is called from destructor &apos;~CCodesysDevice()&apos; at line 105. Dynamic binding is not used." verbose="Virtual function &apos;Disconnect&apos; is called from destructor &apos;~CCodesysDevice()&apos; at line 105. Dynamic binding is not used.">
            <location file0="/home/<USER>/dr/Source/drivers/codesysdrv/codesysdrv/codesysdevice.cpp" file="/home/<USER>/dr/Source/drivers/codesysdrv/codesysdrv/codesysdevice.h" line="144" column="15" info="Disconnect is a virtual function"/>
            <location file="/home/<USER>/dr/Source/drivers/codesysdrv/codesysdrv/codesysdevice.cpp" line="105" column="2" info="Calling Disconnect"/>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/CVDriverCommon.cpp" file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TaskBatchUpdateData.h" line="124" column="3"/>
        </error>
        <error id="invalidPrintfArgType_sint" severity="warning" msg="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;signed long&apos;." verbose="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;signed long&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/Device.cpp" line="509" column="6"/>
        </error>
        <error id="invalidPrintfArgType_sint" severity="warning" msg="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;signed long&apos;." verbose="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;signed long&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/Device.cpp" line="513" column="6"/>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/MainTask.cpp" file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TaskBatchUpdateData.h" line="124" column="3"/>
        </error>
        <error id="virtualCallInConstructor" severity="warning" msg="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CSerialPort()&apos; at line 758. Dynamic binding is not used." verbose="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CSerialPort()&apos; at line 758. Dynamic binding is not used.">
            <location file0="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/SerialPort.cpp" file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/SerialPort.h" line="64" column="15" info="DisConnect is a virtual function"/>
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/SerialPort.cpp" line="758" column="2" info="Calling DisConnect"/>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CSerialPort&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CSerialPort&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/SerialPort.cpp" line="71" column="2"/>
            <symbol>CSerialPort</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CSerialPort&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CSerialPort&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/SerialPort.cpp" line="71" column="2"/>
            <symbol>CSerialPort</symbol>
        </error>
        <error id="virtualCallInConstructor" severity="warning" msg="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CSerialPort()&apos; at line 116. Dynamic binding is not used." verbose="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CSerialPort()&apos; at line 116. Dynamic binding is not used.">
            <location file0="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/SerialPort.cpp" file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/SerialPort.h" line="64" column="15" info="DisConnect is a virtual function"/>
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/SerialPort.cpp" line="116" column="2" info="Calling DisConnect"/>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CTagDataBlockBuilder&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CTagDataBlockBuilder&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TagDataBlockBuilder.cpp" line="238" column="3"/>
            <symbol>CTagDataBlockBuilder</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CTagDataBlockBuilder&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CTagDataBlockBuilder&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TagDataBlockBuilder.cpp" line="238" column="3"/>
            <symbol>CTagDataBlockBuilder</symbol>
        </error>
        <error id="duplicateAssignExpression" severity="style" msg="Same expression used in consecutive assignments of &apos;nOutTagNum&apos; and &apos;nGrpNum&apos;." verbose="Finding variables &apos;nOutTagNum&apos; and &apos;nGrpNum&apos; that are assigned the same expression is suspicious and might indicate a cut and paste or logic error. Please examine this code carefully to determine if it is correct." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TagDataBlockBuilder.cpp" line="778" column="15"/>
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TagDataBlockBuilder.cpp" line="779" column="15"/>
        </error>
        <error id="duplicateAssignExpression" severity="style" msg="Same expression used in consecutive assignments of &apos;nOutTagNum&apos; and &apos;nGrpNum&apos;." verbose="Finding variables &apos;nOutTagNum&apos; and &apos;nGrpNum&apos; that are assigned the same expression is suspicious and might indicate a cut and paste or logic error. Please examine this code carefully to determine if it is correct." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TagDataBlockBuilder.cpp" line="801" column="15"/>
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TagDataBlockBuilder.cpp" line="802" column="15"/>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file0="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TaskBatchUpdateData.cpp" file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TaskBatchUpdateData.h" line="124" column="3"/>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CTcpClientHandler&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CTcpClientHandler&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TcpClientHandler.cpp" line="56" column="2"/>
            <symbol>CTcpClientHandler</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CTcpClientHandler&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CTcpClientHandler&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TcpClientHandler.cpp" line="56" column="2"/>
            <symbol>CTcpClientHandler</symbol>
        </error>
        <error id="virtualCallInConstructor" severity="warning" msg="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CTcpClientHandler()&apos; at line 84. Dynamic binding is not used." verbose="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CTcpClientHandler()&apos; at line 84. Dynamic binding is not used.">
            <location file0="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TcpClientHandler.cpp" file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TcpClientHandler.h" line="48" column="15" info="DisConnect is a virtual function"/>
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TcpClientHandler.cpp" line="84" column="2" info="Calling DisConnect"/>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CTcpServerHandler&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CTcpServerHandler&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TcpServerHandler.cpp" line="51" column="2"/>
            <symbol>CTcpServerHandler</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CTcpServerHandler&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CTcpServerHandler&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TcpServerHandler.cpp" line="51" column="2"/>
            <symbol>CTcpServerHandler</symbol>
        </error>
        <error id="virtualCallInConstructor" severity="warning" msg="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CUdpServerHandler()&apos; at line 30. Dynamic binding is not used." verbose="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CUdpServerHandler()&apos; at line 30. Dynamic binding is not used.">
            <location file0="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/UdpServerHandler.cpp" file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/UdpServerHandler.h" line="23" column="15" info="DisConnect is a virtual function"/>
            <location file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/UdpServerHandler.cpp" line="30" column="2" info="Calling DisConnect"/>
        </error>
        <error id="nullPointerRedundantCheck" severity="warning" msg="Either the condition &apos;fd==0&apos; is redundant or there is possible null pointer dereference: fd." verbose="Either the condition &apos;fd==0&apos; is redundant or there is possible null pointer dereference: fd." cwe="476">
            <location file="/home/<USER>/dr/Source/drivers/fileblockdrv/TestTool/main-fileBlockTest.cpp" line="61" column="11" info="Null pointer dereference"/>
            <location file="/home/<USER>/dr/Source/drivers/fileblockdrv/TestTool/main-fileBlockTest.cpp" line="59" column="10" info="Assuming that condition &apos;fd==0&apos; is not redundant"/>
            <symbol>fd</symbol>
        </error>
        <error id="nullPointer" severity="error" msg="Null pointer dereference" verbose="Null pointer dereference" cwe="476">
            <location file="/home/<USER>/dr/Source/drivers/fileblockdrv/TestTool/main-fileBlockTest.cpp" line="61" column="11" info="Null pointer dereference"/>
        </error>
        <error id="nullPointerRedundantCheck" severity="warning" msg="Either the condition &apos;fd==0&apos; is redundant or there is possible null pointer dereference: fd." verbose="Either the condition &apos;fd==0&apos; is redundant or there is possible null pointer dereference: fd." cwe="476">
            <location file="/home/<USER>/dr/Source/drivers/filetagdrv/TestTool/main-fileTagTest.cpp" line="115" column="11" info="Null pointer dereference"/>
            <location file="/home/<USER>/dr/Source/drivers/filetagdrv/TestTool/main-fileTagTest.cpp" line="113" column="10" info="Assuming that condition &apos;fd==0&apos; is not redundant"/>
            <symbol>fd</symbol>
        </error>
        <error id="nullPointer" severity="error" msg="Null pointer dereference" verbose="Null pointer dereference" cwe="476">
            <location file="/home/<USER>/dr/Source/drivers/filetagdrv/TestTool/main-fileTagTest.cpp" line="115" column="11" info="Null pointer dereference"/>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CGeTcpClientHandler&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CGeTcpClientHandler&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/ge/gedrv/getcpclienthandler.cpp" line="36" column="2"/>
            <symbol>CGeTcpClientHandler</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CGeTcpClientHandler&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CGeTcpClientHandler&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/ge/gedrv/getcpclienthandler.cpp" line="36" column="2"/>
            <symbol>CGeTcpClientHandler</symbol>
        </error>
        <error id="virtualCallInConstructor" severity="warning" msg="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CGeTcpClientHandler()&apos; at line 67. Dynamic binding is not used." verbose="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CGeTcpClientHandler()&apos; at line 67. Dynamic binding is not used.">
            <location file0="/home/<USER>/dr/Source/drivers/ge/gedrv/getcpclienthandler.cpp" file="/home/<USER>/dr/Source/drivers/ge/gedrv/getcpclienthandler.h" line="50" column="15" info="DisConnect is a virtual function"/>
            <location file="/home/<USER>/dr/Source/drivers/ge/gedrv/getcpclienthandler.cpp" line="67" column="2" info="Calling DisConnect"/>
        </error>
        <error id="nullPointerRedundantCheck" severity="warning" msg="Either the condition &apos;!pDataBlock&apos; is redundant or there is possible null pointer dereference: pDataBlock." verbose="Either the condition &apos;!pDataBlock&apos; is redundant or there is possible null pointer dereference: pDataBlock." cwe="476">
            <location file="/home/<USER>/dr/Source/drivers/icgdrv/icgdriver/iCentroGate.cpp" line="206" column="90" info="Null pointer dereference"/>
            <location file="/home/<USER>/dr/Source/drivers/icgdrv/icgdriver/iCentroGate.cpp" line="211" column="7" info="Assuming that condition &apos;!pDataBlock&apos; is not redundant"/>
            <symbol>pDataBlock</symbol>
        </error>
        <error id="invalidScanfArgType_int" severity="warning" msg="%x in format string (no. 1) requires &apos;unsigned int *&apos; but the argument type is &apos;unsigned char *&apos;." verbose="%x in format string (no. 1) requires &apos;unsigned int *&apos; but the argument type is &apos;unsigned char *&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/melsec/melsecdrv/melsec.cpp" line="443" column="2"/>
        </error>
        <error id="invalidScanfArgType_int" severity="warning" msg="%x in format string (no. 1) requires &apos;unsigned int *&apos; but the argument type is &apos;unsigned char *&apos;." verbose="%x in format string (no. 1) requires &apos;unsigned int *&apos; but the argument type is &apos;unsigned char *&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/melsec/melsecdrv/melsec.cpp" line="764" column="2"/>
        </error>
        <error id="invalidPrintfArgType_sint" severity="warning" msg="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;unsigned int&apos;." verbose="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;unsigned int&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/mqttdrv/mqttdrv.cpp" line="278" column="9"/>
        </error>
        <error id="invalidPrintfArgType_sint" severity="warning" msg="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;unsigned int&apos;." verbose="%d in format string (no. 1) requires &apos;int&apos; but the argument type is &apos;unsigned int&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/mqttdrv/mqttdrv.cpp" line="282" column="9"/>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;COmronTcpClientHandler&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;COmronTcpClientHandler&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/omron/omrondrv/omronTcpClientHandler.cpp" line="49" column="2"/>
            <symbol>COmronTcpClientHandler</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;COmronTcpClientHandler&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;COmronTcpClientHandler&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/omron/omrondrv/omronTcpClientHandler.cpp" line="49" column="2"/>
            <symbol>COmronTcpClientHandler</symbol>
        </error>
        <error id="virtualCallInConstructor" severity="warning" msg="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~COmronTcpClientHandler()&apos; at line 85. Dynamic binding is not used." verbose="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~COmronTcpClientHandler()&apos; at line 85. Dynamic binding is not used.">
            <location file0="/home/<USER>/dr/Source/drivers/omron/omrondrv/omronTcpClientHandler.cpp" file="/home/<USER>/dr/Source/drivers/omron/omrondrv/omronTcpClientHandler.h" line="46" column="15" info="DisConnect is a virtual function"/>
            <location file="/home/<USER>/dr/Source/drivers/omron/omrondrv/omronTcpClientHandler.cpp" line="85" column="2" info="Calling DisConnect"/>
        </error>
        <error id="nullPointerRedundantCheck" severity="warning" msg="Either the condition &apos;if(pItemResult)&apos; is redundant or there is possible null pointer dereference: pItemResult." verbose="Either the condition &apos;if(pItemResult)&apos; is redundant or there is possible null pointer dereference: pItemResult." cwe="476">
            <location file="/home/<USER>/dr/Source/drivers/opc/DataBlock.cpp" line="479" column="12" info="Null pointer dereference"/>
            <location file="/home/<USER>/dr/Source/drivers/opc/DataBlock.cpp" line="491" column="4" info="Assuming that condition &apos;if(pItemResult)&apos; is not redundant"/>
            <symbol>pItemResult</symbol>
        </error>
        <error id="nullPointerRedundantCheck" severity="warning" msg="Either the condition &apos;if(pItemResult)&apos; is redundant or there is possible null pointer dereference: pItemResult." verbose="Either the condition &apos;if(pItemResult)&apos; is redundant or there is possible null pointer dereference: pItemResult." cwe="476">
            <location file="/home/<USER>/dr/Source/drivers/opc/DataBlock.cpp" line="480" column="9" info="Null pointer dereference"/>
            <location file="/home/<USER>/dr/Source/drivers/opc/DataBlock.cpp" line="491" column="4" info="Assuming that condition &apos;if(pItemResult)&apos; is not redundant"/>
            <symbol>pItemResult</symbol>
        </error>
        <error id="invalidScanfArgType_int" severity="warning" msg="%x in format string (no. 1) requires &apos;unsigned int *&apos; but the argument type is &apos;signed int *&apos;." verbose="%x in format string (no. 1) requires &apos;unsigned int *&apos; but the argument type is &apos;signed int *&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/opcua/opcuadrv/opcua.cpp" line="570" column="3"/>
        </error>
        <error id="uselessAssignmentPtrArg" severity="warning" msg="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" verbose="Assignment of function parameter has no effect outside the function. Did you forget dereferencing it?" cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/opcua/opcuadrv/opcuadevice.cpp" line="741" column="3"/>
        </error>
        <error id="uninitvar" severity="error" msg="Uninitialized variable: lRet" verbose="Uninitialized variable: lRet" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/opcua/opcuadrv/opcuadevice.cpp" line="825" column="9"/>
            <symbol>lRet</symbol>
        </error>
        <error id="uninitvar" severity="error" msg="Uninitialized variable: nDataSize" verbose="Uninitialized variable: nDataSize" cwe="908">
            <location file="/home/<USER>/dr/Source/drivers/rda/cvrdadriver/RDADriver.cpp" line="933" column="32"/>
            <symbol>nDataSize</symbol>
        </error>
        <error id="bufferAccessOutOfBounds" severity="error" msg="Buffer is accessed out of bounds: temp" verbose="Buffer is accessed out of bounds: temp" cwe="788">
            <location file="/home/<USER>/dr/Source/drivers/s7fwdrv/S7fwDrv.cpp" line="11" column="17" info="Buffer overrun"/>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;CTcpClientHandler&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CTcpClientHandler&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/s7fwdrv/TcpClientHandler.cpp" line="39" column="2"/>
            <symbol>CTcpClientHandler</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;CTcpClientHandler&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;CTcpClientHandler&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/s7fwdrv/TcpClientHandler.cpp" line="39" column="2"/>
            <symbol>CTcpClientHandler</symbol>
        </error>
        <error id="virtualCallInConstructor" severity="warning" msg="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CTcpClientHandler()&apos; at line 65. Dynamic binding is not used." verbose="Virtual function &apos;DisConnect&apos; is called from destructor &apos;~CTcpClientHandler()&apos; at line 65. Dynamic binding is not used.">
            <location file0="/home/<USER>/dr/Source/drivers/s7fwdrv/TcpClientHandler.cpp" file="/home/<USER>/dr/Source/drivers/cvdriverframe/cvdrivercommon/TcpClientHandler.h" line="48" column="15" info="DisConnect is a virtual function"/>
            <location file="/home/<USER>/dr/Source/drivers/s7fwdrv/TcpClientHandler.cpp" line="65" column="2" info="Calling DisConnect"/>
        </error>
        <error id="invalidScanfArgType_int" severity="warning" msg="%x in format string (no. 1) requires &apos;unsigned int *&apos; but the argument type is &apos;unsigned char *&apos;." verbose="%x in format string (no. 1) requires &apos;unsigned int *&apos; but the argument type is &apos;unsigned char *&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/sgcidriver/sgcidriver/sgcidriver.cpp" line="992" column="4"/>
        </error>
        <error id="invalidScanfArgType_int" severity="warning" msg="%x in format string (no. 1) requires &apos;unsigned int *&apos; but the argument type is &apos;unsigned char *&apos;." verbose="%x in format string (no. 1) requires &apos;unsigned int *&apos; but the argument type is &apos;unsigned char *&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/sgcidriver/sgcidriver/sgcidriver.cpp" line="994" column="4"/>
        </error>
        <error id="invalidScanfArgType_int" severity="warning" msg="%x in format string (no. 1) requires &apos;unsigned int *&apos; but the argument type is &apos;unsigned char *&apos;." verbose="%x in format string (no. 1) requires &apos;unsigned int *&apos; but the argument type is &apos;unsigned char *&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/sgcidriver/sgcidriver/sgcidriver.cpp" line="996" column="4"/>
        </error>
        <error id="nullPointerRedundantCheck" severity="warning" msg="Either the condition &apos;m_pContext!=NULL&apos; is redundant or there is possible null pointer dereference: m_pContext." verbose="Either the condition &apos;m_pContext!=NULL&apos; is redundant or there is possible null pointer dereference: m_pContext." cwe="476">
            <location file="/home/<USER>/dr/Source/drivers/sgcidriver/sgcidriver/sgcidriver.cpp" line="260" column="95" info="Null pointer dereference"/>
            <location file="/home/<USER>/dr/Source/drivers/sgcidriver/sgcidriver/sgcidriver.cpp" line="268" column="19" info="Assuming that condition &apos;m_pContext!=NULL&apos; is not redundant"/>
            <symbol>m_pContext</symbol>
        </error>
        <error id="nullPointerRedundantCheck" severity="warning" msg="Either the condition &apos;pDevice!=NULL&apos; is redundant or there is possible null pointer dereference: pDevice." verbose="Either the condition &apos;pDevice!=NULL&apos; is redundant or there is possible null pointer dereference: pDevice." cwe="476">
            <location file="/home/<USER>/dr/Source/drivers/siemenss7drv/DeviceGroup.cpp" line="375" column="3" info="Null pointer dereference"/>
            <location file="/home/<USER>/dr/Source/drivers/siemenss7drv/DeviceGroup.cpp" line="377" column="14" info="Assuming that condition &apos;pDevice!=NULL&apos; is not redundant"/>
            <symbol>pDevice</symbol>
        </error>
        <error id="nullPointerRedundantCheck" severity="warning" msg="Either the condition &apos;pDevice!=NULL&apos; is redundant or there is possible null pointer dereference: pDevice." verbose="Either the condition &apos;pDevice!=NULL&apos; is redundant or there is possible null pointer dereference: pDevice." cwe="476">
            <location file="/home/<USER>/dr/Source/drivers/siemenss7drv/DeviceGroup.cpp" line="387" column="52" info="Null pointer dereference"/>
            <location file="/home/<USER>/dr/Source/drivers/siemenss7drv/DeviceGroup.cpp" line="365" column="22" info="Assignment &apos;pDevice=*iter&apos;, assigned value is 0"/>
            <location file="/home/<USER>/dr/Source/drivers/siemenss7drv/DeviceGroup.cpp" line="377" column="14" info="Assuming that condition &apos;pDevice!=NULL&apos; is not redundant"/>
            <symbol>pDevice</symbol>
        </error>
        <error id="cppcheckError" severity="error" msg="Analysis failed. If the code is valid then please report this failure." verbose="Analysis failed. If the code is valid then please report this failure.">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/core/s7_micro_client.cpp" line="2739" column="0"/>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;TServersManager&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TServersManager&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/core/s7_partner.cpp" line="58" column="5"/>
            <symbol>TServersManager</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;TServersManager&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TServersManager&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/core/s7_partner.cpp" line="58" column="5"/>
            <symbol>TServersManager</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;TConnectionServer&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TConnectionServer&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/core/s7_partner.cpp" line="199" column="5"/>
            <symbol>TConnectionServer</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;TConnectionServer&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TConnectionServer&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/core/s7_partner.cpp" line="199" column="5"/>
            <symbol>TConnectionServer</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;TSnap7Partner&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TSnap7Partner&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/core/s7_partner.cpp" line="399" column="5"/>
            <symbol>TSnap7Partner</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;TSnap7Partner&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TSnap7Partner&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/core/s7_partner.cpp" line="399" column="5"/>
            <symbol>TSnap7Partner</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;TSnap7Server&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TSnap7Server&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/core/s7_server.cpp" line="1735" column="2"/>
            <symbol>TSnap7Server</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;TSnap7Server&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TSnap7Server&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/core/s7_server.cpp" line="1735" column="2"/>
            <symbol>TSnap7Server</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;TMsgSocket&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TMsgSocket&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/sys/snap_msgsock.cpp" line="84" column="5"/>
            <symbol>TMsgSocket</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;TMsgSocket&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TMsgSocket&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/sys/snap_msgsock.cpp" line="84" column="5"/>
            <symbol>TMsgSocket</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;TMsgEventQueue&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TMsgEventQueue&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/sys/snap_tcpsrvr.cpp" line="37" column="5"/>
            <symbol>TMsgEventQueue</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;TMsgEventQueue&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TMsgEventQueue&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/sys/snap_tcpsrvr.cpp" line="37" column="5"/>
            <symbol>TMsgEventQueue</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;TCustomMsgServer&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TCustomMsgServer&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/sys/snap_tcpsrvr.cpp" line="186" column="5"/>
            <symbol>TCustomMsgServer</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;TCustomMsgServer&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TCustomMsgServer&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/sys/snap_tcpsrvr.cpp" line="186" column="5"/>
            <symbol>TCustomMsgServer</symbol>
        </error>
        <error id="noCopyConstructor" severity="warning" msg="Class &apos;TCustomMsgServer&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TCustomMsgServer&apos; does not have a copy constructor which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/sys/snap_tcpsrvr.cpp" line="184" column="5"/>
            <symbol>TCustomMsgServer</symbol>
        </error>
        <error id="noOperatorEq" severity="warning" msg="Class &apos;TCustomMsgServer&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." verbose="Class &apos;TCustomMsgServer&apos; does not have a operator= which is recommended since it has dynamic memory/resource allocation(s)." cwe="398">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7/sys/snap_tcpsrvr.cpp" line="184" column="5"/>
            <symbol>TCustomMsgServer</symbol>
        </error>
        <error id="invalidScanfArgType_int" severity="warning" msg="%i in format string (no. 1) requires &apos;int *&apos; but the argument type is &apos;unsigned short *&apos;." verbose="%i in format string (no. 1) requires &apos;int *&apos; but the argument type is &apos;unsigned short *&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7driver/snap7drv.cpp" line="3029" column="3"/>
        </error>
        <error id="invalidScanfArgType_int" severity="warning" msg="%i in format string (no. 1) requires &apos;int *&apos; but the argument type is &apos;unsigned short *&apos;." verbose="%i in format string (no. 1) requires &apos;int *&apos; but the argument type is &apos;unsigned short *&apos;." cwe="686">
            <location file="/home/<USER>/dr/Source/drivers/snap7drv/snap7driver/snap7drv.cpp" line="3030" column="3"/>
        </error>
        <error id="pointerSize" severity="warning" msg="Size of pointer &apos;pSendBuffer&apos; used instead of size of its data." verbose="Size of pointer &apos;pSendBuffer&apos; used instead of size of its data. This is likely to lead to a buffer overflow. You probably intend to write &apos;sizeof(*pSendBuffer)&apos;." cwe="467">
            <location file="/home/<USER>/dr/Source/drivers/tdcwritedrv/tdcdrv.cpp" line="470" column="9"/>
        </error>
    </errors>
</results>
