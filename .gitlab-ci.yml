variables:
  BUILD_DIR: "build"
  TARGET_USER: "dsf"
  SSH_PASSWORD: "dsf123!@#"
  LOCAL_DIR: "/home/<USER>/autotestdir"
  TARGET_IP: "************"

stages:          # List of stages for jobs, and their order of execution
  - build
  - analyze
  # - unitTest
  # - coverage
  # - coverageSDK
  # - deploy
  - autotest
  - dailyftp

default:
  before_script:
    - export SHORT_SHA=${CI_COMMIT_SHA:0:8}
    - export BUILD_VERSION=${CI_COMMIT_TAG:-$SHORT_SHA}
    - echo "CI_COMMIT_TAG:${CI_COMMIT_TAG}"
    - echo "SHORT_SHA:${SHORT_SHA}"
    - echo "BUILD_VERSION:${BUILD_VERSION}"

build-job:
  stage: build
  variables:
    GIT_STARATGEY: clone
  tags:
    - linux2
  # before_script:
  #   - export SHORT_SHA=${CI_COMMIT_SHA:0:8}
  #   - export BUILD_VERSION=${CI_COMMIT_TAG:-$SHORT_SHA}
  #   - echo "CI_COMMIT_TAG:${CI_COMMIT_TAG}"
  #   - echo "SHORT_SHA:${SHORT_SHA}"
  #   - echo "BUILD_VERSION:${BUILD_VERSION}"
  script:
      - cd setup/ 
      - build.sh
      # - ./build.sh 2>&1 | tee ./build.log 
      # - sshpass -p 'Dsf@448' scp -o StrictHostKeyChecking=no build.log root@*************:/root/.jenkins/workspace/Dsf-Build
      - cd ../
      - pwd
      - echo "添加路径，拷贝配置文件夹docker/ executable/ library/ setup/ projects/ source下自动化测试相关文件夹"
      # - export DR_ROOT=$(pwd)"/executable"
      # - export LD_LIBRARY_PATH=$(pwd)/library:$LD_LIBRARY_PATH
      - cp -r $(pwd)/Repo/copy_force/projects   ./
      - cp -r $(pwd)/Repo/copy_force/executable/config  ./executable/
      - echo "打包文件"
      - tar -czvf  build/dr.tar.gz docker/ executable/ library/ setup/ projects/ Source/AutoTest
  artifacts:
    paths:
      - build/dr.tar.gz
    expire_in: 1 week

build-job_redhat_139:
  stage: build
  variables:
    GIT_STRATEGY: clone
  tags:
    - redhat_139
  script:
      - cd setup/ 
      # - ./build.sh > build.log 2>&1
      - ./build.sh 2>&1 | tee ./build.log 
       #- sshpass -p 'Dsf@448' scp -o StrictHostKeyChecking=no build.log root@*************:/root/.jenkins/workspace/Dsf-Build
      - cd ../
      - pwd
      - echo "添加路径，拷贝配置文件夹docker/ executable/ library/ setup/ projects/ source下自动化测试相关文件夹"
      - export DR_ROOT=$(pwd)"/executable"
      - export LD_LIBRARY_PATH=$(pwd)/library:$LD_LIBRARY_PATH
      - cp -r $(pwd)/Repo/copy_force/projects   ./
      - cp -r $(pwd)/Repo/copy_force/executable/config  ./executable/
      - echo "打包文件"
      - tar -czvf  build/dr.tar.gz docker/ executable/ library/ setup/ projects/ Source/AutoTest
  artifacts:
    paths:
      - build/dr.tar.gz
    expire_in: 1 week

analyze-job:   
  stage: analyze
  tags:
    - linux2
  script:      
    - echo "Checking cppcheck version..."
    - find Source -type f \( -name '*.c' -o -name '*.cpp' \) -exec cppcheck --enable=warning --quiet --xml  --suppress=unknownMacro 
     --suppress=missingIncludeSystem --suppress=uninitMemberVar  --suppress=uninitVar --suppress=unusedFunction --suppress=variableScope 
     --suppress=redundantAssignment --suppress=redundantCondition --force {} + 2> report-cppcheck.xml
    - sshpass -p 'Dsf@448' scp -o StrictHostKeyChecking=no report-cppcheck.xml root@*************:/root/.jenkins/workspace/Dsf-cppcheck
    - echo "Cppcheck the code over"
  artifacts:
    paths:
      - report-cppcheck.xml
    expire_in: 1 day
  when: on_success


# deploy-job:
#   stage: deploy
#   tags:
#     - linux2
#   artifacts:
#     paths:
#       - build/dr.tar.gz
#   script:
#     - echo "清空远程服务器 ${LOCAL_DIR}/dr 目录"
#     - sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no "$TARGET_USER"@$TARGET_IP "rm -rf ${LOCAL_DIR}/dr/*"
#     - echo "发送文件到远程机器"
#     - sshpass -p "$SSH_PASSWORD" scp -o StrictHostKeyChecking=no build/dr.tar.gz "$TARGET_USER"@$TARGET_IP:${LOCAL_DIR}/dr
#     - echo "解压远程压缩文件"
#     - sshpass -p "$SSH_PASSWORD" ssh -o StrictHostKeyChecking=no "$TARGET_USER"@$TARGET_IP "tar -xzvf ${LOCAL_DIR}/dr/dr.tar.gz -C ${LOCAL_DIR}/dr"
#   when: on_success

#已移动到Jenkins任务中，不再影响主流水线的执行。
# autotest-job:
#   stage: autotest
#   tags:
#     - linux2
#   artifacts:
#     paths:
#       - build/dr.tar.gz
#   script:
#     - echo "清空远程服务器 ${LOCAL_DIR}/dr 目录"
#     - sshpass -p "$SSH_PASSWORD" ssh -p 10103 -o StrictHostKeyChecking=no "$TARGET_USER"@$TARGET_IP "rm -rf ${LOCAL_DIR}/dr/*"
#     - echo "发送文件到远程机器"
#     - sshpass -p "$SSH_PASSWORD" scp -P 10103 -o StrictHostKeyChecking=no build/dr.tar.gz "$TARGET_USER"@$TARGET_IP:${LOCAL_DIR}/dr
#     - echo "解压远程压缩文件"
#     - sshpass -p "$SSH_PASSWORD" ssh -p 10103 -o StrictHostKeyChecking=no "$TARGET_USER"@$TARGET_IP "tar -xzvf ${LOCAL_DIR}/dr/dr.tar.gz -C ${LOCAL_DIR}/dr"
#     - echo "开始通知远程机器执行 auto.sh 脚本"
#     - sshpass -p "$SSH_PASSWORD" ssh -p 10103 -o StrictHostKeyChecking=no "$TARGET_USER"@$TARGET_IP "cd ${LOCAL_DIR}/dr/setup && bash auto.sh"
#   # rules:
#   #   - if: '$GITLAB_USER_NAME == "马屹松" || $GITLAB_USER_NAME == "万宇"'
#   when: on_success


dailyftp-job:
  stage: dailyftp
  tags:
    - linux2
  dependencies:
    - build-job
  script:
    - echo "当前目录"
    - pwd
    - echo "执行 ftp_dsf.sh 脚本"
    - cd setup/
    - ./ftp_dsf.sh "daily" $BUILD_VERSION
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" || $CI_COMMIT_REF_NAME == "wzq_cicd"'

dailyftp-job_redhat_139:
  stage: dailyftp
  tags:
    - redhat_139
  dependencies:
    - build-job_redhat_139
  script:
    - echo "当前目录"
    - date
    - pwd
    - echo "执行 ftp_dsf.sh 脚本"
    - cd setup/
    - ./ftp_dsf.sh "daily" $BUILD_VERSION
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" || $CI_COMMIT_REF_NAME == "wzq_cicd"'

tagftp-job:
  stage: dailyftp
  tags:
    - linux2
  dependencies:
    - build-job
  script:
    - echo "Tag 构建触发 $CI_COMMIT_TAG"
    - date
    - pwd
    - echo "执行 ftp_dsf.sh 脚本"
    - cd setup/
    - ./ftp_dsf.sh "tag" $BUILD_VERSION
  rules:
    - if: '$CI_COMMIT_TAG'

  
tagftp-job_redhat_139:
  stage: dailyftp
  tags:
    - redhat_139
  dependencies:
    - build-job_redhat_139
  script:
    - echo "Tag 构建触发 $CI_COMMIT_TAG"
    - date
    - pwd
    - echo "执行 ftp_dsf.sh 脚本"
    - cd setup/
    - ./ftp_dsf.sh "tag" $BUILD_VERSION
  rules:
    - if: '$CI_COMMIT_TAG'