<?xml version="1.0" encoding="utf-8"?>
<logc version = "3.0">
	<config>
		<reload>1</reload>
		<reloadperiod>60</reloadperiod>
	</config>

	<rollingpolicy name="sizerolling" type="sizewin" maxsize="4096000" maxnum="50" />	
	<layout name="basic" type="basic" datetype="dot" errcode="has" codelocation="has" threadid="no" processname="no" />

	<appender name="KernelFile" type="rollingfile" logdir="ihd/Log/" prefix="Kernel" rollingpolicy="sizerolling" layout="basic" />
	<appender name="KernelConsole" type="console" layout="basic" />
	<appender name="NetMgrFile" type="rollingfile" logdir="ihd/Log/" prefix="NetMgr" rollingpolicy="sizerolling" layout="basic" />
	<appender name="NetMgrConsole" type="console" layout="basic" />
	<appender name="RyFile" type="rollingfile" logdir="ihd/Log/" prefix="ry" rollingpolicy="sizerolling" layout="basic" />
	<appender name="RyConsole" type="console" layout="basic" />
	<appender name="StatsScheFile" type="rollingfile" logdir="ihd/Log/" prefix="StatSche" rollingpolicy="sizerolling" layout="basic" />
	<appender name="StatsScheConsole" type="console" layout="basic" />  
	<appender name="ConfigSyncSvcFile" type="rollingfile" logdir="ihd/Log/" prefix="ConfigSyncSvc" rollingpolicy="sizerolling" layout="basic" />
	<appender name="ConfigSyncSvcConsole" type="console" layout="basic" />
	<appender layout="basic" logdir="ihd/Log/" name="SysInfoSvcFile" prefix="SysInfoSvc" rollingpolicy="sizerolling" type="rollingfile"/>
	<appender layout="basic" name="SysInfoSvcConsole" type="console"/>

	<category name="iHyperDB.Kernel">
		<output appender="KernelFile" priority="info" />
		<output appender="KernelConsole" priority="error" />
	</category>
	<category name="iHyperDB.NetMgr">
		<output appender="NetMgrFile" priority="info" />
		<output appender="NetMgrConsole" priority="error" />
	</category>
	<category name="iHyperDB.SecurityServer">
		<output appender="NetMgrFile" priority="info" />
		<output appender="NetMgrConsole" priority="error" />
	</category>

	<category name="iHyperDB.StatsSche">
		<output appender="StatsScheFile" priority="info" />
		<output appender="StatsScheConsole" priority="error" />
	</category>

	<category name="iHyperDB.ConfigSyncSvc">
		<output appender="ConfigSyncSvcFile" priority="info" />
		<output appender="ConfigSyncSvcConsole" priority="error" />
	</category>

	<category name="iHyperDB.Redundancy">
		<output appender="RyFile" priority="info" />
		<output appender="RyConsole" priority="error" />
	</category>
</logc>
