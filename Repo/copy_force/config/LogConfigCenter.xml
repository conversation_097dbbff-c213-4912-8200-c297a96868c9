<?xml version="1.0" encoding="utf-8"?>
<logc version = "1.2">
  <config>
    <reload>1</reload>
    <reloadperiod>60</reloadperiod>
  </config>

  <rollingpolicy name="sizerolling" type="sizewin" maxsize="4096000" maxnum="50" />
  <layout name="basic" type="basic" datetype="dot" errcode="has" codelocation="has" threadid="no" processname="no" />

  <appender name="ConfigCenterFile" type="rollingfile" logdir="ihd/Log/" prefix="ConfigCenter" rollingpolicy="sizerolling" layout="basic" />
  <appender name="ConfigCenterConsole" type="console" layout="basic" />
  
  <category name="iHyperDB3.ConfigCenter">
    <output appender="ConfigCenterFile" priority="info" />
    <output appender="ConfigCenterConsole" priority="error" />
  </category>
</logc>
