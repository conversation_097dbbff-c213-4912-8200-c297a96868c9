<?xml version="1.0" ?><iHyperDBSrv version="3.1">
  <GlobalCfg BuildVersion="3.6.1_Build_20181130_win32" EnableExportBigData="0" EnableRedundancy="0"/>
  <System CPUNum="1" Memory="32"/>
  <Kernel>
    <SeqFile FilePath="../data/SeqFile" FlushPeriod="600" Size="256"/>
    <ArchiveFile ArchiveFilePath="../data/Archive" ArchiveWindowSize="2000" BlockCacheFilePath="../data/BlockCache" BlockSize="4096" BtreeNodeSize="512" Num="50" Size="2048"/>
    <TimeStamp DiscardOldData="1" DiscardOldDataTimeDevSec="604800" Enable="1" MaxTimeDevSec="18000000"/>
  </Kernel>
  <Alarm FilePath="../data/Alarm"/>
  <ProcComm>
    <ShareMem BlockSize="4096" TotalSize="64"/>
  </ProcComm>
  <NetMgr>
    <Network Port="60011"/>
    <MemPool Size="2"/>
    <Connection InterDataTimeout="300" NeedCheck="0"/>
  </NetMgr>
  <Redundancy>
    <HostAddr IP="" Port=""/>
    <BackUpAddr IP="" Port=""/>
    <LocalGateWay IP=""/>
    <CacheFile Path="../data/Ry" SizeMb="4096"/>
    <HeatBeat ConnCheckCnt="3" ConnTimeoutSec="1" InterMs="1000"/>
  </Redundancy>
  <SysInfo NeedCollect="1" UpdateInterval="30">
    <Collector UpdateStatusInter="60"/>
    <ChangeHis ClearChangeHisInter="30"/>
  </SysInfo>
  <CCServer bakeip="" bakeport="" identity="-1" localhostIP="" mainip="127.0.0.1" mainport="60010" syncperiod="20"/>
</iHyperDBSrv>
