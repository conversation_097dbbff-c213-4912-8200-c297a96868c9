<?xml version="1.0" encoding="GB2312"?>
<CRS version="5.0">
    <logictrigger checkperiod_ms="1000"/>
	<!-- The IP address of centralized configuration service, is only valid in iCV mode -->
	<ccserverip>127.0.0.1</ccserverip>
	<!-- The IP address of authority service -->
	<amserverip>127.0.0.1</amserverip>
	<!-- The port of authority service -->
	<amserverport>50000</amserverport>
	<!-- Application type to support. ICV and iFix are supported.-->
	<apptype>iCV</apptype>
	<!-- Whether to use the classification of the query query -->
	<!-- 0 - Do not use the classification, query on all items -->
	<!-- 1 - Using the classification, query in the selected classification -->
   	<usesetquery>0</usesetquery>
	<!-- Whether to query the recursive term -->
	<!-- 0 - No query next level directory, only the selected directory -->
	<!-- 1 - Query the selected directory and the directory of all sub directories -->
	<includelower>1</includelower>
</CRS>
