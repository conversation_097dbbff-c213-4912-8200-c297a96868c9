<?xml version="1.0" encoding="GB2312" ?>
<odbctransfer>
		<engineset>
				<dbengine name="dbengine1" />
		</engineset>
    <odbcset>
      <odbc name="sql" dsn="sql.db" username="" password="" desp="sql" type="SQLite" cacherecords="1000" batch="1" writemode="TIMER" writetimeout="10000" />
      <!-- configuration with characterset utf8 -->
      <odbc name="mysqlds" connectionname="localmysql" dsn="127.0.0.1@test:utf8" username="root" password="abcdef" desp="" type="MySQL" cacherecords="1000" batch="1" writemode="TIMER" writetimeout="1" />
      <!--		<odbc name="odbc1" dsn="168.2.8.189@" username="sa" password="sa-123" desp="cv5odbctransfer" type="sqlserver" cacherecords="10000" batch="3" writemode="INSTANT" writequantity="10" writetimeout="15000"/>
        <odbc name="odbc3" dsn="cvmysql" username="root" password="" desp="cv5odbctransfer" engine="dbengine1" writemode="QUANTITY" writequantity="10" writetimeout="15000"/>
        <odbc name="odbc2" dsn="cvaccess" username="" password="" desp="cv5odbctransfer" writemode="TIMER" writetimeout="20000"/>
        <odbc name="odbc4" dsn="cvoracle" username="cv" password="cv" desp="cv5odbctransfer" />
        <odbc name="odbc5" dsn="cvpostgresql" username="postgresql" password="postgresql" desp="" />   -->
    </odbcset>
    <odbcalarm odbcname="sql" enable="0" name="a" tablename="t_alarm" desp="">
        <alarmareas>
            <alarmarea name="ALL" />
        </alarmareas>
    </odbcalarm>
    <odbcevent odbcname="sql" enable="0" name="e" tablename="t_event" desp="">
        <alarmareas>
            <alarmarea name="ALL" />
        </alarmareas>
    </odbcevent>
    <odbcdata odbcname="sql">
        <scada name="added_objectname1" enable="1">
          <!--      <group name="g1" desp="" tablename="t_data" scan_intv="1000" scan_phs="0" transferonchange="0" manualrule="0">
                <tag name="added_objectname1.ai" desc="" type="AI" />            
            </group>  -->
        </scada>
    </odbcdata>
  <scriptset>
    <script name="eventscript"></script>
    <script name="alarmscript"></script>
    <script name="datascript"></script>
  </scriptset>
</odbctransfer>
