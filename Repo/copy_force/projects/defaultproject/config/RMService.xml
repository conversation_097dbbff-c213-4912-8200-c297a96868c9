﻿<RMService sequence="0">
	<Strategy>
		<!--  Interval in msec  -->
		<Interval>200</Interval>
		<Threshold>5</Threshold>
	</Strategy>
	<PeerCommunicator type="DSFRMUDPComm">
		<PortList>
			<Port>8765</Port>
			<Port>8766</Port>
		</PortList>
		<PeerList>
			<Peer sequence='0'>
				<IP id="0">127.0.0.1</IP>
			</Peer>
			<!-- <Peer sequence='1'>
				<IP id="0"></IP>
			</Peer> -->
			<Ping UsePing="0">
				<IP></IP>
				<Interval>1</Interval>
				<Threshold>3</Threshold>
				<Timeout>1</Timeout>
			</Ping>
		</PeerList>
		<!--  心跳专线配置  -->
		<HeartbeatNetwork>
			<Peer sequence="0">
				<IP id="0" />
			</Peer>
			<Peer sequence="1">
				<IP id="0" />
			</Peer>
		</HeartbeatNetwork>
	</PeerCommunicator>
	<!--  核心服务配置  -->
	<!--  <CoreServices>
        <Service name="dsfdataservice" checkTimer="5000"/>
        <Service name="dsfacquisitionservice" checkTimer="5000"/>
    </CoreServices>  -->
</RMService>