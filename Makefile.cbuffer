# Makefile for CBuffer Concurrent Test
CXX = g++
CXXFLAGS = -std=c++11 -Wall -g -O2 -pthread
LIBS = -lpthread

TARGET = test_cbuffer_concurrent
SOURCE = test_cbuffer_concurrent.cpp

# 主要构建目标
$(TARGET): $(SOURCE)
	$(CXX) $(CXXFLAGS) -o $(TARGET) $(SOURCE) $(LIBS)

# 清理
clean:
	rm -f $(TARGET)

# 运行测试
test: $(TARGET)
	./$(TARGET)

# 运行压力测试
stress-test: $(TARGET)
	@echo "Running stress test (may take longer)..."
	./$(TARGET)

.PHONY: clean test stress-test

# 默认目标
all: $(TARGET)
