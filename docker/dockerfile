FROM ubuntu:20.04_dsf_base

SHELL ["/bin/bash", "-c"]

RUN echo -e '#!/bin/bash\n\necho "0" > /proc/sys/kernel/core_uses_pid\necho "core.%e" > /proc/sys/kernel/core_pattern' > /etc/core_pattern_init.sh \
 && chmod +x /etc/core_pattern_init.sh

 ## 基础镜像中的dr 和 redis 删掉
RUN  rm -rf /home/<USER>/dr
RUN  rm -rf /home/<USER>/dsf-redis

COPY ubuntu2004_dsf.tar.gz /home/<USER>/.
RUN tar -xzf /home/<USER>/ubuntu2004_dsf.tar.gz -C /home/<USER>
    &&  rm /home/<USER>/ubuntu2004_dsf.tar.gz   \
    &&  chown -R dsf:dsf /home/<USER>/dr/   \
    &&  chown -R dsf:dsf /home/<USER>/dsf-redis/
    
CMD ["/bin/bash", "-c", "/home/<USER>/dr/setup/dsf_install.sh ; /home/<USER>/dr/setup/dsf_start.sh; exec /bin/bash"]

###docker build -t ubuntu:20.04_dsf .