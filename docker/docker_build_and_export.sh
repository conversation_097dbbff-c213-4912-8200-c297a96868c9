#!/bin/bash
SOFTWARE_FILE="ubuntu2004_dsf.tar.gz" ## used in dockerfile
BASE_IMAGE_NAME="ubuntu:20.04_dsf_base"
BUILD_IMAGE_NAME="ubuntu:20.04_dsf"
BASE_IMAGE_FILE="ubuntu_20.04_dsf_images_base_0329.tar.gz"
BUILD_IMAGE_FILE="ubuntu_20.04_dsf_images_new_$(date +%Y%m%d%H%M%S).tar.gz" # default name

function check_software_files() {

    local softfile="$1"
    if [ ! -f "${softfile}" ]; then
        echo "Error: No software file [${softfile}]specified."
        exit 1
    fi
    cp -rf ${softfile} ./${SOFTWARE_FILE}
    echo "cp -rf ${softfile} ./${SOFTWARE_FILE}"

}
function check_image_exists() {
    local img="$1"
    if docker images --format "{{.Repository}}:{{.Tag}}" | grep -q ${img}; then
        return 0
    else
        return 1
    fi
}

function build_new_image() {

    if ! check_image_exists ${BASE_IMAGE_NAME}; then
        echo "Base Image ${BASE_IMAGE_NAME} not exists."
        exit 1
    fi
    echo "Base Image ${BASE_IMAGE_NAME} exists."

    echo "Building new image ${BUILD_IMAGE_NAME} ..."
    docker build -t ${BUILD_IMAGE_NAME} .
    if ! check_image_exists ${BUILD_IMAGE_NAME}; then
        echo "Build Image ${BUILD_IMAGE_NAME} failed."
        exit 1
    fi
    return 0
}

function get_image_fle_name() {
    local filename="$1"
    # filename="dsf_20250408_155809_SP2503.3_0.1.0_Ubuntu20.04_Sprint.tar.gz"
    # SP2503.3_0.1.0_202504081558_Ubuntu20.04_Sprint

    basename="${filename%%.tar.gz}" # 去掉后缀
    IFS='_' read -r prefix date time version1 version2 os_name build_type <<<"$basename"

    formatted="${version1}_${version2}_${date}${time:0:4}_${os_name}_${build_type}_image.tar.gz"
    BUILD_IMAGE_FILE="${formatted}"
}
function export_image() {
    docker save $BUILD_IMAGE_NAME >$BUILD_IMAGE_FILE
    if [ $? -ne 0 ]; then
        echo "Export Image ${BUILD_IMAGE_NAME} failed."
        exit 1
    fi
    echo "Export Image ${BUILD_IMAGE_NAME} to ${BUILD_IMAGE_FILE} success."
}

function clear() {
    rm -rf ${SOFTWARE_FILE}
    echo "rm -rf ${SOFTWARE_FILE}"
}

function main() {
    local softfile="$1"
    check_software_files $softfile

    build_new_image

    get_image_fle_name $softfile
    export_image

    clear
}

main "$@"
