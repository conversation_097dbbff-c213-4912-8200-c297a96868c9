#!/usr/bin/env bash

SCRIPTS_DIR=$(dirname "$(realpath "$0")")
DR_DIR=$(dirname "${SCRIPTS_DIR}")
echo "SCRIPTS_DIR: ${SCRIPTS_DIR}"
echo "DR_DIR: ${DR_DIR}"

CONTAINER_NAME="ubuntu_20.04_dsf"
CONTAINER_HOSTNAME="dsf_dev"
CONTAINER_USER="dsf"
DOCKER_IMG="ubuntu:20.04_dsf"
LOCAL_SHARE_DIR="${HOME}/docker_shared_dir"
REMOTE_SHARE_DIR="/home/<USER>/docker_shared_dir"
WORK_PATH="/home/<USER>/"


function run_container() {

    docker run -itd \
        --privileged \
        --name="${CONTAINER_NAME}" \
        --network host \
        --hostname "${CONTAINER_HOSTNAME}" \
        --add-host "${CONTAINER_HOSTNAME}:127.0.0.1" \
        --user "${CONTAINER_USER}" \
        -v "${LOCAL_SHARE_DIR}:${REMOTE_SHARE_DIR}" \
        -w "${WORK_PATH}" \
        ubuntu:20.04_dsf 

    if [ $? -ne 0 ]; then
        error "Failed to start docker container \"${DEV_CONTAINER}\" based on image: ${DEV_IMAGE}"
        exit 1
    fi
}
function remove_container_if_exists() {
    local container="$1"
    if docker ps -a --format '{{.Names}}' | grep -q "${container}"; then
        echo "Removing existing  container: ${container}"
        docker stop "${container}" >/dev/null
        docker rm -v -f "${container}" 2>/dev/null
    fi
}
function main() {
    remove_container_if_exists "${CONTAINER_NAME}"
    run_container

    echo " To login into the newly created ${DEV_CONTAINER} container, please run the following command:"
    echo " bash docker/docker_into.sh"
    echo " Enjoy!"
}

main
