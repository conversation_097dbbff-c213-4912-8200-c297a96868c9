#!/bin/bash
# $MYVERSION
#boost need install:sudo yum -y install libquadmath-devel
MYSUBVERSION=$(date "+%Y%m%d");
echo "username:${USER} hostname:${HOSTNAME}";

if [ -f /etc/os-release ]; then
    . /etc/os-release

    if [ "$ID" = "ubuntu" ]; then
        MYREPO="ubuntu20_4"
    elif [ "$ID" = "rhel" -o "$ID" = "centos" ]; then
        MYREPO="redhat7_8"
    else
        MYREPO="unknown"
    fi
else
    MYREPO="unknown"
fi

echo "MYREPO=$MYREPO"

ws=$(pwd)/../
cd $ws
echo $(pwd)
export DRDIR=$(pwd)"/Source/"

export LD_LIBRARY_PATH=$(pwd)"/executable":$(pwd)"/library"

MYFLAG='-DCMAKE_{C,CXX}_FLAGS=-m64 -DCMAKE_{EXE,SHARED}_LINKER_FLAGS=-m64'



########### check repo ###########
if [ -d "Repo/$MYREPO" ]; then
 find Repo/$MYREPO/ -name '*.tar.gz' -exec tar -zxf {} \; > /dev/null
else
 echo "cant find Repo Repo/$MYREPO."
 exit 100
fi
echo $(pwd)
echo "tar completed"
########### Compile ###########
[ ! -d build ] && mkdir build
cd build
echo $(pwd)
cmake -version
cmake  -G "Unix Makefiles" -DCMAKE_INSTALL_PREFIX=source -DCMAKE_BUILD_TYPE=RelWithDebInfo $MYFLAG $DRDIR
make -k -j
if [ $? -eq 0 ]; then
 echo "make completed"
else
 echo "make failed"
 exit 101
fi


