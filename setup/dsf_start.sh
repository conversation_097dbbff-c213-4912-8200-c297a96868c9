#!/bin/bash
SCRIPT_NAME=$(readlink -f "$0")
SCRIPT_DIR=$(dirname "$SCRIPT_NAME")
cd $SCRIPT_DIR
source ${SCRIPT_DIR}/env.sh
echo "DR_ROOT: $DR_ROOT"

function create_drv_link() {
    cd ${DR_ROOT}
    drivers=("dsftxdrv" "icedrv" "abdrv" "codesysdrv" "modbusdrv" "snap7drv" "opcuadrv" "icgdrv" "melsecdrv" "internaldrv")
    for drv in "${drivers[@]}"; do
        DRV_PATH=${DR_ROOT}/drivers/${drv}
        TDRV_NAME="${DR_ROOT}/drivers/${drv}/${drv}"
        if [ ! -e "${TDRV_NAME}" ]; then
            cd ${DRV_PATH}
            cp ${DR_ROOT}/drdriver-******** .
            ln -s drdriver-******** ${drv}
        fi
    done
}

function start_redis() {
    # REDIS_DIR="/home/<USER>/dsf-redis"
    REDIS_DIR="${DR_ROOT}/../../dsf-redis"
    REDIS_SERVER="$REDIS_DIR/redis-server"
    REDIS_CONF="$REDIS_DIR/redis_6380.conf"
    REDIS_PORT=6380
    REDIS_PID_FILE="$REDIS_DIR/redis.pid"
    # 检查 redis-server 是否正在运行
    check_scripts=$(ps -ef | grep redis-server | grep $REDIS_PORT | grep -v grep | wc -l)
    if [ ${check_scripts} -gt 0 ]; then
        check_scripts_pid=$(ps -ef | grep redis-server | grep $REDIS_PORT | grep -v grep | awk '{print $2}')
        echo "reids-server is running, pid=${check_scripts_pid}"
    else
        # nohup $REDIS_SERVER --protected-mode no --port $REDIS_PORT --pidfile $REDIS_PID_FILE >/dev/null 2>&1 &
        nohup $REDIS_SERVER $REDIS_CONF >/dev/null 2>&1 &
        sleep 1 # 稍等一会儿以确保已启动
        NEW_PID=$(ps -ef | grep "$REDIS_SERVER" | grep "$REDIS_PORT" | grep -v grep | awk '{print $2}')
        if [[ -n "$NEW_PID" ]]; then
            echo "redis-server started successfully with PID $NEW_PID"
        else
            echo "Failed to start redis-server!"
        fi
    fi

}

function start_process() {
    cd ${DR_ROOT}
    processes=("dsfdeployservice" "dsfprocessmgr" "dsfsourcemonitor")
    for process in "${processes[@]}"; do
        check_scripts=$(ps -ef | grep $process | grep -v grep | wc -l)
        if [ ${check_scripts} -gt 0 ]; then
            check_scripts_pid=$(ps -ef | grep $process | grep -v grep | awk '{print $2}')
            echo " ${process} is running, pid=${check_scripts_pid}"
        else
            nohup "${DR_ROOT}/${process}" >/dev/null 2>&1 &
            echo " ${process} started with PID $!"
        fi

    done

}

function record() {
    local RECODR_DIR=dsf_start_record
    cd ~
    mkdir -p $RECODR_DIR
    cd $RECODR_DIR
    find . -mtime +30 -type f -delete

    filename=$(date "+%Y%m%d_%H%M%S")
    touch $filename

}

function main() {
    #can remove
    record

    echo "start dsf"
    cd ${DR_ROOT}

    start_redis

    create_drv_link

    start_process
}

main
