#!/bin/bash

##############################################################
# Filename        generate_dsf_service.sh
# Copyright       Shanghai Baosight Software Co., Ltd.
# Description      Generate DSF service script for RHEL
#
# Author          wuzheqiang
# Version         06/24/2025    wuzheqiang    Initial Version
##############################################################

MYREPO="unknown"
if [ -f /etc/os-release ]; then
  . /etc/os-release

  if [ "$ID" = "rhel" -o "$ID" = "centos" ]; then
    OS_TYPE="redhat7"
  else
    OS_TYPE="unknown"
  fi
fi

if [ "$OS_TYPE" = "unknown" ]; then
  echo "❌ $0 Unsupported OS type: $ID"
  exit 0
fi

SERVICE_NAME="dsf-runtime" # 可以根据项目动态设置
SCRIPT_NAME=$(readlink -f "$0")
SCRIPT_DIR=$(dirname "$SCRIPT_NAME")

DSF_START_SCRIPT="$SCRIPT_DIR/dsf_start.sh"
DSF_STOP_SCRIPT="$SCRIPT_DIR/dsf_stop.sh"
LOGFILE="/var/log/${SERVICE_NAME}.log"
SERVICE_FILE="/etc/init.d/$SERVICE_NAME"

echo "Creating DSF init script at ${SERVICE_FILE}"
echo "Service LOGFILE: ${LOGFILE}"
echo "Using start script: ${DSF_START_SCRIPT}"
echo "Using stop script: ${DSF_STOP_SCRIPT}"

sudo tee "${SERVICE_FILE}" >/dev/null <<EOF
#!/bin/bash
#
# ${SERVICE_NAME}      Start/Stop DSF service
#
# chkconfig: 2345 99 01   
# description: Starts and stops the DSF service

DSF_START_SCRIPT="${DSF_START_SCRIPT}"
DSF_STOP_SCRIPT="${DSF_STOP_SCRIPT}"
LOGFILE="${LOGFILE}"

start() {
    echo "Starting DSF..."
    echo "\$(date '+%Y-%m-%d %H:%M:%S') starting DSF..." >> "\$LOGFILE"
    nohup bash "\$DSF_START_SCRIPT" >> "\$LOGFILE" 2>&1 &
    echo "DSF started."
}

stop() {
    echo "Stopping DSF..."
    echo "\$(date '+%Y-%m-%d %H:%M:%S')" Stopping DSF >> "\$LOGFILE"
    nohup bash "\$DSF_STOP_SCRIPT" >> "\$LOGFILE" 2>&1 &
    echo "DSF stopped."
}

status() {
    ps -ef | grep executable | grep -v grep
}

case "\$1" in
  start)
    start
    ;;
  stop)
    stop
    ;;
  restart)
    stop
    sleep 10
    start
    ;;
  status)
    status
    ;;
  *)
    echo "Usage: \$0 {start|stop|restart|status}"
esac

exit 0
EOF

# 设置权限并启用服务
sudo chmod +x "$SERVICE_FILE"
sudo chkconfig --add "$SERVICE_NAME"
sudo chkconfig "$SERVICE_NAME" on

echo "DSF service [$SERVICE_NAME] installed and enabled."

# sudo chkconfig dsf-back off         # 关闭开机启动
# sudo chkconfig --del dsf-back       # 删除服务项
# sudo rm -f /etc/init.d/dsf-back     # 删除服务脚本文件
# sudo chkconfig --list               # 查看服务列表
