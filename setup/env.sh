#!/bin/bash
SCRIPT_NAME="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# echo "SCRIPT_NAME: $SCRIPT_NAME"

WORKSPACE_DIR=$(dirname "$SCRIPT_NAME")

# echo "WORKSPACE_DIR: $WORKSPACE_DIR"

if [[ ":$LD_LIBRARY_PATH:" != *":${WORKSPACE_DIR}/library:"* ]]; then
    export LD_LIBRARY_PATH="${WORKSPACE_DIR}/library:$LD_LIBRARY_PATH"
fi
if [[ ":$LD_LIBRARY_PATH:" != *":${WORKSPACE_DIR}/library/ihd3:"* ]]; then
    export LD_LIBRARY_PATH="${WORKSPACE_DIR}/library/ihd3:$LD_LIBRARY_PATH"
fi

if [[ ":$LD_LIBRARY_PATH:" != *":${WORKSPACE_DIR}/executable:"* ]]; then
    export LD_LIBRARY_PATH="${WORKSPACE_DIR}/executable:$LD_LIBRARY_PATH"
fi

export DR_ROOT="${WORKSPACE_DIR}/executable"

echo "DR_ROOT: $DR_ROOT"
echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"
