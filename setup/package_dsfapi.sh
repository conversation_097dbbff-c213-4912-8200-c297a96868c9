#
# Filename package_dsfapi.sh
# Copyright Shanghai Baosight Software Co., Ltd.
# Description package the dsfapi headers and library files to dsfapi_package.tar.gz
#
# Author wuzheqiang
# Version 10/17/2024 wuzheqiang Initial Version
###############################################################################

#!/bin/bash

# Get the full path of the script and the directory it resides in
SCRIPT_NAME=$(readlink -f "$0")
SCRIPT_DIR=$(dirname "$SCRIPT_NAME")

echo "SCRIPT_NAME: $SCRIPT_NAME"
echo "SCRIPT_DIR: $SCRIPT_DIR"

# Define the workspace directory (one level up from the script directory)
WORKSPACE_DIR=$(dirname "$SCRIPT_DIR")
echo "WORKSPACE_DIR: $WORKSPACE_DIR"

# Define the source and header directories
SOURCE_LID="${WORKSPACE_DIR}/executable"
HEADER_DIR="${WORKSPACE_DIR}/include"
DRSDK_DIR="${WORKSPACE_DIR}/Source/DRSdk/dsfapi"

echo "SOURCE_LID: $SOURCE_LID"
echo "DRSDK_DIR: $DRSDK_DIR"

# Define the library file name and initialize default SDK version
LIB_NAME="${SOURCE_LID}/libdsfapi.so"
SDK_VERSION="1.0.0" # Default value, will be updated based on the actual libdsfapi.so version

# Check if the library file exists
if [[ ! -f "${LIB_NAME}" ]]; then
    echo "Cannot find lib: ${LIB_NAME}."
    exit 1
fi
# Loop through files matching the library name pattern and extract version
for file in $(ls ${LIB_NAME}*); do
    if [[ $file =~ libdsfapi\.so\.[0-9]+\.[0-9]+\.[0-9]+ ]]; then
        # Use awk to extract the version from the file name
        SDK_VERSION=$(echo "$file" | awk -F '.' '{print $3"."$4"."$5}')
    fi
done

if [[ -z "$1" ]]; then
    echo "No version specified, using default version: $SDK_VERSION"
else
    SDK_VERSION="$1"
    echo "Using specified version: $SDK_VERSION"
fi
echo "SDK_VERSION: $SDK_VERSION"

if [[ -f /etc/os-release ]]; then
    . /etc/os-release

    if [[ "$ID" == "ubuntu" ]]; then
        MYREPO="Ubuntu20.04"
    elif [[ "$ID" == "rhel" || "$ID" == "centos" ]]; then
        MYREPO="Redhat7.8"
    else
        MYREPO="unknown"
    fi
else
    MYREPO="unknown"
fi

# Define the SDK package name and directory based on the extracted version
SDK_NAME="dsfapi"
SDK_PACKAGE_NAME="${SDK_NAME}_${SDK_VERSION}_$(date +%Y%m%d%H%M)_${MYREPO}"
SDK_DIR="${SOURCE_LID}/${SDK_PACKAGE_NAME}"
echo "SDK_PACKAGE_NAME: $SDK_PACKAGE_NAME"
echo "SDK_DIR: $SDK_DIR"

# Create the directory structure for the SDK
mkdir -p "${SDK_DIR}/lib"
mkdir -p "${SDK_DIR}/include"
mkdir -p "${SDK_DIR}/include/errcode"

# Copy the library files and header files into the SDK package
cp -rf ${LIB_NAME}* ${SDK_DIR}/lib/
cp -rf ${DRSDK_DIR}/*.h ${SDK_DIR}/include/
cp -rf ${HEADER_DIR}/data_types.h ${SDK_DIR}/include/
cp -rf ${HEADER_DIR}/errcode/error_code.h ${SDK_DIR}/include/errcode

# Create a tar.gz archive of the SDK package
echo "Creating ${SDK_PACKAGE_NAME}.tar.gz..."
OUTPUT_DIR=${SOURCE_LID}
echo "OUTPUT_DIR: $OUTPUT_DIR"
cd ${SOURCE_LID}
tar -zcvf "${OUTPUT_DIR}/${SDK_PACKAGE_NAME}.tar.gz" $SDK_PACKAGE_NAME
cd -

# Remove the temporary SDK directory after packaging
rm -rf $SDK_DIR
echo "SDK build success: ${SDK_PACKAGE_NAME}.tar.gz"
