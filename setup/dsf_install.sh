#!/bin/bash

##############################################################
# Filename        dsf_install.sh
# Copyright       Shanghai Baosight Software Co., Ltd.
# Description     used to install dsf
#
# Author          wuzheqiang
# Version         06/24/2025    wuzheqiang    Initial Version
##############################################################

SCRIPT_NAME=$(readlink -f "$0")
SCRIPT_DIR=$(dirname "$SCRIPT_NAME")
cd $SCRIPT_DIR
# source ${SCRIPT_DIR}/env.sh
# echo "DR_ROOT: $DR_ROOT"

# dsf/dr/setup/dsf_install.sh
DSF_DIR=$(readlink -f "${SCRIPT_DIR}/../..")
DR_DIR=$(readlink -f "${DSF_DIR}/dr")
DSF_REDIS=$(readlink -f "${DSF_DIR}/dsf-redis")

echo "DSF_DIR: $DSF_DIR"
echo "DR_DIR: $DR_DIR"
echo "DSF_REDIS: $DSF_REDIS"

#安装依赖检查
function install_check() {
    # 获取当前用户和用户组
    CURRENT_USER=$(whoami)
    CURRENT_GROUP=$(id -gn "$CURRENT_USER")
    echo "当前用户: $CURRENT_USER"
    echo "当前用户组: $CURRENT_GROUP"

    # 检查是否有 dr 目录
    if [ ! -d "$DR_DIR" ]; then
        echo "❌ dr directory not exist"
        exit
    fi
    # 修改目录的属主和属组
    chown -R "$CURRENT_USER":"$CURRENT_GROUP" "$DR_DIR"

    # 检查是否有 dsf-redis 目录
    if [ ! -d "$DSF_REDIS" ]; then
        echo "❌ dsf-redis directory not exist"
        exit
    fi
    # 修改目录的属主和属组
    chown -R "$CURRENT_USER":"$CURRENT_GROUP" "$DSF_REDIS"

    # 检查一些必要的软件是否已经安装
    if ! command -v gcc &>/dev/null; then
        echo "❌ gcc is not installed. Please install gcc and try again."
        # exit
    fi

    echo "✅ install_check success"
}
#增加自启动服务
function gen_dsf_service() {
    bash $SCRIPT_DIR/generate_dsf_service.sh
    if [ $? -ne 0 ]; then
        echo "❌ generate dsf service failed"
        exit
    fi

    echo "✅ gen_dsf_service success"
}
#修改redis配置文件
function modify_redis_conf() {
    local orig_redis_conf_file="$DSF_REDIS/redis.conf"
    if [ ! -f "$orig_redis_conf_file" ]; then
        echo "❌ redis.conf file not found: $orig_redis_conf_file"
        exit
    fi
    local redis_conf_file="$DSF_REDIS/redis_6380.conf"
    cp -f "$orig_redis_conf_file" "$redis_conf_file"
    if [ $? -ne 0 ]; then
        echo "❌ Failed to copy redis.conf file to redis_6380.conf"
        exit
    fi

    # 修改端口（将默认 6379 改为 6380）
    sed -i 's/^port .*/port 6380/' "$redis_conf_file"

    # 修改bind ip
    sed -i '/^bind/c  bind * -::*' "$redis_conf_file"

    # 关闭保护模式（默认开启时，只允许本地访问）
    sed -i 's/^protected-mode .*/protected-mode no/' "$redis_conf_file"

    # 开启并配置 unixsocket
    local socket_path="$DSF_REDIS/redis.sock"
    sed -i "s|^#\? *unixsocket .*|unixsocket $socket_path|" "$redis_conf_file"
    sed -i "s/^#\? *unixsocketperm .*/unixsocketperm 770/" "$redis_conf_file"

    # 去除持久化
    # 清除 RDB 快照规则（替代删除多行 save）
    grep -q '^save ' "$redis_conf_file" && sed -i '/^save /d' "$redis_conf_file"
    sed -i 's/^# *save *""/save ""/' "$redis_conf_file"
    # 关闭 AOF 持久化
    sed -i 's/^appendonly .*/appendonly no/' "$redis_conf_file"
    # 防止因 bgsave 错误阻止写入
    sed -i 's/^stop-writes-on-bgsave-error .*/stop-writes-on-bgsave-error no/' "$redis_conf_file"

    echo "✅ redis_6380.conf modified success"
    return 0
}

function main() {
    #安装依赖检查
    install_check

    #增加自启动服务
    gen_dsf_service

    #修改redis配置文件
    modify_redis_conf

    #其他
    echo "✅ dsf_install.sh success"
}

main
