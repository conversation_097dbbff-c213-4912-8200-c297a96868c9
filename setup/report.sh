cd $(find $HOME -type d -name "UnitTest" | head -n 1)
rm -rf build
mkdir build
cd build 
cmake .. && make -j8
echo "make ok"
#去yml里复制好的dr/projects/defaultproject里面把config复制到当前目录（build）下
cp -r ../../../projects/defaultproject/config .

cd DRAcquisitionService_Unit
./dracquisitiontest --gtest_output=xml:report_DRAcquisitionService.xml
sshpass -p 'Dsf@448' scp -o StrictHostKeyChecking=no report*.xml root@*************:/root/.jenkins/workspace/Dsf-test
echo "DRAcquisitionService unit test report build ok"

cd ../DRDataService_Unit
./servicebasetest --gtest_output=xml:report_DRDataService.xml
sshpass -p 'Dsf@448' scp -o StrictHostKeyChecking=no report*.xml root@*************:/root/.jenkins/workspace/Dsf-test
echo "DRDataService unit test report build ok"


cd ../DRSdk_Unit
./sdktest --gtest_output=xml:report_DRSdk.xml
sshpass -p 'Dsf@448' scp -o StrictHostKeyChecking=no report*.xml root@*************:/root/.jenkins/workspace/Dsf-test
echo "DRSdk unit test report build ok"

cd ../DRDeployer_Unit
./drdeploytest --gtest_output=xml:report_DRDeployer.xml
sshpass -p 'Dsf@448' scp -o StrictHostKeyChecking=no report*.xml root@*************:/root/.jenkins/workspace/Dsf-test
echo "DRDeployer unit test report build ok"

echo "coverage"
cd ../
pwd
make coverage_DRSdk_Unit
make coverage_DRAcquisitionService_Unit
make coverage_DRDataService_Unit
make coverage_DRDeployer_Unit


lcov --add-tracefile coverage_DRAcquisitionService_Unit.info --add-tracefile coverage_DRDataService_Unit.info --add-tracefile coverage_DRSdk_Unit.info --add-tracefile coverage_DRDeployer_Unit.info -output-file coverage.info

lcov --remove coverage.info '/user/*' --output-file coverage.info

gcovr -r ../ --object-directory . --xml --output test_code_coverage.xml


sshpass -p 'Dsf@448' ssh -o StrictHostKeyChecking=no root@************* "rm -rf /root/.jenkins/workspace/Dsf-cover/*"
sshpass -p 'Dsf@448' scp -o StrictHostKeyChecking=no -r ../../../Source root@*************:/root/.jenkins/workspace/Dsf-cover
sshpass -p 'Dsf@448' scp -o StrictHostKeyChecking=no -r ../../../Source/UnitTest/* root@*************:/root/.jenkins/workspace/Dsf-cover

sshpass -p 'Dsf@448' ssh -o StrictHostKeyChecking=no root@************* "cp -r  /root/.jenkins/workspace/Dsf-cover/Source/* /root/.jenkins/workspace/Dsf-cover/"
sshpass -p 'Dsf@448' scp -o StrictHostKeyChecking=no -r ../../../include root@*************:/root/.jenkins/workspace/Dsf-cover
sshpass -p 'Dsf@448' ssh -o StrictHostKeyChecking=no root@************* "cp -r  /root/.jenkins/workspace/Dsf-cover/include/* /root/.jenkins/workspace/Dsf-cover/"
sshpass -p 'Dsf@448' scp -o StrictHostKeyChecking=no -r test_code_coverage.xml root@*************:/root/.jenkins/workspace/Dsf-cover

cd ../
pwd
rm -rf build
