#
# Filename package_dsfapi.sh
# Copyright Shanghai Baosight Software Co., Ltd.
# Description: package the dsf  and upload ftp server
#
# Author wuzheqiang
# Version 03/14/2025 wuzheqiang Initial Version
###############################################################################

#!/bin/bash

SCRIPT_NAME=$(readlink -f "$0")
SCRIPT_DIR=$(dirname "$SCRIPT_NAME")
echo "SCRIPT_DIR: $SCRIPT_DIR"
WORKSPACE_DIR=$(dirname "$SCRIPT_DIR")
echo "WORKSPACE_DIR: $WORKSPACE_DIR"
CUR_TIME=$(date +%Y%m%d_%H%M%S)
TARGET_NAME="dsf_${CUR_TIME}"
DEFAULT_TYPE="Daily"   #default
BUILD_VERSION="DIRTY"  #default
VERSION_INFO="UNKNOWN" #default

VERSION_INFO_TXT="UNKNOWN"
VERSION_TXT="UNKNOWN"
SP_VERSION_TXT="UNKNOWN"
SP_VERSION_NUM_TXT="UNKNOWN"
CUR_TIME_TXT=$(date +%Y%m%d%H%M)

function package() {

    if [[ -f /etc/os-release ]]; then
        . /etc/os-release

        if [[ "$ID" == "ubuntu" ]]; then
            MYREPO="Ubuntu20.04"
        elif [[ "$ID" == "rhel" || "$ID" == "centos" ]]; then
            MYREPO="Redhat7.8"
        else
            MYREPO="unknown"
        fi
    else
        MYREPO="unknown"
    fi
    VERSION_INFO="${TARGET_NAME}_${BUILD_VERSION}_${MYREPO}_${DEFAULT_TYPE}"
    TARGET_NAME="${VERSION_INFO}.tar.gz"
    echo "TARGET_NAME: $TARGET_NAME"

    ##add version.txt to dr
    mkdir -p dsf-runtime && rm -rf dsf-runtime/* && cd dsf-runtime
    cp "$WORKSPACE_DIR/build/dr.tar.gz" .
    tar zxf dr.tar.gz && rm dr.tar.gz

    # if [ "$MYREPO" == "Redhat7.8" ]; then
    #     processmgr_file="projects/defaultproject/config/ProcessMgr.xml"
    #     # 如果没有找到 dsfdataarchive，才插入
    #     if ! grep -q 'ModuleName="dsfdataarchive"' "$processmgr_file"; then
    #         sed -i '/ModuleName="dsfdrvctrl"/a\        <Process ModuleName="dsfdataarchive" DisplayName="ihd archive" Platform="3" SleepTime="2" RestartEnable='\''1'\'' Type="1" Label="rm"></Process>' "$processmgr_file"
    #         echo "dsfdataarchive inserted into ${processmgr_file}"
    #     fi
    # fi
    VERSION_INFO_TXT="${VERSION_TXT}.${SP_VERSION_NUM_TXT}.${CUR_TIME_TXT}"
    echo "${VERSION_INFO_TXT}" >version.txt

    ##add dsf -redis
    cd $WORKSPACE_DIR
    if [ "$MYREPO" == "Redhat7.8" ]; then
        tar -xvf external/redhat_redis7.4.2.tar.gz -C .

    elif [ "$MYREPO" == "Ubuntu20.04" ]; then
        tar -xvf external/ubuntu_redis7.4.2.tar.gz -C .
    fi

    #the same as .gitlab_ci build and added version.txt
    mv dsf-runtime dr
    tar -czvf ${TARGET_NAME} dr dsf-redis
    if [ $? -ne 0 ]; then
        echo "package failed."
        exit 1
    fi

    rm -rf dsf-redis
    rm -rf dr
    if [ $? -eq 0 ]; then
        echo "package finished successfully."
    else
        echo "package failed."
        exit 1
    fi
    ls -lrt $TARGET_NAME
    echo "package end "
}

function upload() {
    FTP_HOST="***********"
    FTP_USER="dsfadmin"
    FTP_PASS="AdminDsf288"
    FTP_REMOTE_PATH="/files/DSF_artifacts/daily/"

    if [ -z "$1" ]; then
        echo "Error: $1 file not found."
        exit 1
    fi

    LOCAL_FILE="$1"

    echo "Start to upload $LOCAL_FILE to FTP server $FTP_HOST:$FTP_REMOTE_PATH"

    output=$(
        ftp -inv "$FTP_HOST" <<EOF
user $FTP_USER $FTP_PASS
binary
passive

cd $FTP_REMOTE_PATH
put $LOCAL_FILE
bye
EOF
    )

    echo "$output" # 方便调试
    # 检查是否包含常见错误
    if echo "$output" | grep -q "Not connected."; then
        echo "Upload failed: not connected."
        exit 1
    elif echo "$output" | grep -q "No such file"; then
        echo "Upload failed: file or path error."
        exit 1
    else
        echo "Upload finished successfully."
    fi
}

function splitByUnderline() {
    SP_VERSION_TXT=$(echo "$BUILD_VERSION" | cut -d'_' -f1) # 提取 _ 前部分
    VERSION_TXT=$(echo "$BUILD_VERSION" | cut -d'_' -f2)    # 提取 _ 后部分
    SP_VERSION_NUM_TXT="${SP_VERSION_TXT#SP}"               # 删除开头的 "SP"
    echo "$SP_VERSION_NUM_TXT"                              # 输出: 2505
    echo "SP_VERSION_TXT: $SP_VERSION_TXT"                  # 输出: SP2505.2
    echo "VERSION_TXT: $VERSION_TXT"                        # 输出: 0.3.1
}

function main() {
    if [ $# -lt 2 ]; then
        echo "Usage: $0 <tag|daily> <version>"
        exit 1
    fi

    if [ "$1" == "tag" ]; then
        DEFAULT_TYPE="Sprint"
    fi
    BUILD_VERSION="$2"
    splitByUnderline
    cd $WORKSPACE_DIR
    package
    upload $TARGET_NAME
    if [ $? -eq 0 ]; then
        rm -rf $TARGET_NAME
    fi
}

main "$@"
