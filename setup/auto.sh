#输出当前路径
echo "当前路径: $(pwd)"
#回到dr目录
cd ..
#自动化测试用到的配置文件复制过去，有待商榷,这个是天行的配置文件
# rsync -av --delete ../autoProjectConfig/ ./projects/defaultproject/config/
# 测试ICG驱动时放开这个
# rsync -av --delete ../autoProjectConfig_ICG/ ./projects/defaultproject/config/

sshpass -p 'dsf123!@#' scp -o StrictHostKeyChecking=no -r dsf@**************:/home/<USER>/dr/projects $DR_PATH/


echo $(pwd)
export DR_PATH=$(pwd)
echo "DR目录: $DR_PATH"
#原有的env.sh为什么只加了library的路径？因为仅仅在运行时需要，路径只需要library
#但这里，编译后拷过来的库里面动态链接的路径是编译时那台机器上的，如果不在LD_LIBRARY_PATH中加上executable的路径就会导致在这台机上运行时找不到库
#所以这里需要把executable的路径也加到LD_LIBRARY_PATH中
export DR_ROOT=$(pwd)"/executable"
export LD_LIBRARY_PATH=$(pwd)"/executable":$(pwd)"/library":$(pwd)"/library/ihd3"
# 输出调试信息，检查环境变量
# echo "LD_LIBRARY_PATH: $LD_LIBRARY_PATH"
# echo "DR_ROOT: $DR_ROOT"
# echo "ldd情况"
# ldd ./executable/libDRDeploySDK.so

# 为每个驱动生成drdriver软链接
drivers=("dsftxdrv" "abdrv" "codesysdrv" "modbusdrv" "snap7drv" "opcuadrv" "icgdrv" "melsecdrv" "internaldrv")
for drv in "${drivers[@]}"; do
    DRV_PATH=${DR_ROOT}/drivers/${drv}
    TDRV_NAME="${DR_ROOT}/drivers/${drv}/${drv}"
    if [ ! -e "${TDRV_NAME}" ]; then
	    cd ${DRV_PATH}
	    cp ${DR_ROOT}/drdriver-3.20.0.0 .
	    ln -s drdriver-3.20.0.0 ${drv}
    fi
done

cd ${DR_PATH}



echo "进入AutoTest测试文件夹"
cd Source/AutoTest
echo "当前所在测试文件夹路径: $(pwd)"
#删除AutoCommon里旧的allure-results文件夹
rm -rf ./AutoCommon/allure-results

../../setup/dsf_start.sh

sleep 5

#所有模块自动化测试的结果统一放到一个文件夹下，在AutoCommon下，一起上传到jenkins服务器上

#在AutoTest路径下执行各模块下的自动化测试，把结果放在AutoCommon下的allure-results文件夹下
# pytest --alluredir=./AutoCommon/allure-results -vv DRAcquisitionService_Auto/test_txdrv.py
pytest --alluredir=./AutoCommon/allure-results -vv Dsfapi_Auto/test_dsfapi_read.py
pytest --alluredir=./AutoCommon/allure-results -vv DROpcuaPublish_Auto/test.py
# pytest --alluredir=./AutoCommon/allure-results -vv DRAcquisitionService_Auto/test_modbusdrv.py
# pytest --alluredir=./AutoCommon/allure-results -vv DRAcquisitionService_Auto/test_icgdrv.py
# pytest --alluredir=./AutoCommon/allure-results -vv DRAcquisitionService_Auto/test_opcuadrv.py
# pytest --alluredir=./AutoCommon/allure-results -vv DRAcquisitionService_Auto/test_melsecdrv.py
# pytest --alluredir=./AutoCommon/allure-results -vv DRAcquisitionService_Auto/test_acquisition.py
pytest --alluredir=./AutoCommon/allure-results -vv Dsfapi_Auto/test_dsfapi_modbusdrv.py
pytest --alluredir=./AutoCommon/allure-results -vv Dsfapi_Auto/test_dsfapi_snap7drv.py
pytest --alluredir=./AutoCommon/allure-results -vv Dsfapi_Auto/test_dsfapi_icgdrv.py
#warning: 这个测试用例会模拟下发一个配置文件，会导致配置文件被覆盖，所以一定要放在最后执行！！！
pytest --alluredir=./AutoCommon/allure-results -vv DSFSourceMonitor_Auto/test_DSFSourceMonitor.py
pytest --alluredir=./AutoCommon/allure-results -vv DRDeployer_Auto/test_deploy.py


# 删除远程服务器上 allure-results 文件夹
# sshpass -p 'Dsf@448' ssh -o StrictHostKeyChecking=no root@************* "rm -rf /root/.jenkins/workspace/DSF_AUTO_TEST/*"
#上传自动化执行结果
# sshpass -p 'Dsf@448' scp -o StrictHostKeyChecking=no -r ./AutoCommon/allure-results root@*************:/root/.jenkins/workspace/DSF_AUTO_TEST

#最后每次调用一轮测试的时候，执行jenkins的远程构建
# cd "$DR_PATH/Source/AutoTest/AutoCommon"

# bash allure_triger.sh