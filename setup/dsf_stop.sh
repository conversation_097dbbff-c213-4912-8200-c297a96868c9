#!/bin/bash

SCRIPT_NAME=$(readlink -f "$0")
SCRIPT_DIR=$(dirname "$SCRIPT_NAME")
cd $SCRIPT_DIR

echo "Stop dsf processmgr begin...  "
source ${SCRIPT_DIR}/env.sh
echo "DR_ROOT: $DR_ROOT"
nohup ${DR_ROOT}/dsfpmrapi_quit >/dev/null 2>&1 &
sleep 4

processes=("dsfdeployservice" "dsfsourcemonitor")
for process in "${processes[@]}"; do
    check_scripts=$(ps -ef | grep $process | grep -v grep | wc -l)
    if [ ${check_scripts} -gt 0 ]; then
        check_scripts_pid=$(ps -ef | grep $process | grep -v grep | awk '{print $2}')
        kill -9 $check_scripts_pid
        echo "Stop dsf $process end "
    else
        echo " ${process} not running"
    fi
done

# 关闭端口为6380的redis-server
check_scripts=$(ps -ef | grep redis-server | grep 6380 | grep -v grep | wc -l)
if [ ${check_scripts} -gt 0 ]; then
    check_scripts_pid=$(ps -ef | grep redis-server | grep 6380 | grep -v grep | awk '{print $2}')
    kill -9 $check_scripts_pid
    echo "Stop dsf redis-server end "
else
    echo " redis-server not running"
fi

echo "Stop dsf processmgr end "
