
#ifndef _CV_DATA_TYPE_HEADER_
#define _CV_DATA_TYPE_HEADER_

// data types for IODATA.type field
#define DT_ASCII			0		// ASCII string, maximum: 127
#define DT_SINT16			1		// 16 bit Signed Integer value
#define DT_UINT16			2		// 16 bit Unsigned Integer value
#define DT_FLT				3		// 32 bit IEEE float
#define DT_BIT				4		// 1 bit value
#define DT_TIME				5		// 4 byte TIME (H:M:S:T)
#define DT_ULONG			6		// 32 bit integer value
#define DT_SLONG			7		// 32 bit signed integer value
#define DT_DBL				8		// 64 bit double
#define DT_BLOB				9		// blob, maximum 65535
#define DT_CHAR				10		// 8 bit signed integer value
#define DT_UCHAR			11		// 8 bit unsigned integer value
#define DT_INT64			12		// 64 bit signed integer value
#define DT_UINT64			13		// 64 bit unsigned integer value
#define DT_LTIME			14		// 64 bit time (second + usecond)
#define DT_BYTE			    15		// 1 bit value
#define DT_WORLD			16		// 16 bit unsigned value
#define DT_DWORLD			17		// 32 bit unsigned value
#define DT_LWORLD			18		// 64 bit unsigned value
#define DT_CHAR_			19		// 1 bit value
#define DT_UDT			    20		// Total number of Datatypes
#define DT_DATE             21      // 32 bit unsigned integer value
#define DT_TOD              22      // 32 bit unsigned integer value, TIME OF DAY
#define DT_DT               23      // 32 bit unsigned integer value, DATE AND TIME
#define DT_WCHAR            24      // utf-16
#define DT_WSTRING          25      // utf-16 string, maximum 16382
#define DT_LDATE            26      // 64 bit unsigned integer value
#define DT_LTOD             27      // 64 bit unsigned integer value, LTIME OF DAY
#define DT_LDT              28      // 64 bit unsigned integer value, LDATE AND TIME
#define CV_MAX_NUMBER_DATATYPE			    29		// Total number of Datatypes

// USER SPECIFIED DATATYPES
#define DT_QUALITY			16
#define DT_SUBQUALITY		17
#define DT_VTQ				18
// #define CV_MAX_NUMBER_DATATYPE_TOTAL	19
#define CV_MAX_NUMBER_DATATYPE_TOTAL	29


#define DT_SIZE_ASCII		256		// max length of ASCII
#define DT_SIZE_TIMESTR		24		// strlen("2008-01-01 12:12:12.000") + 1

#endif // _CV_DATA_TYPE_HEADER_
