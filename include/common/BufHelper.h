/**************************************************************
 *  Filename:    BufHelper.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: <PERSON>uffer Helper - A Very Simple Smart Pointer To Handle Buffer Creation and Destory.
 *				 DO NOT SURPPORT OBJECT ARRAY CREATION YET!!!
 *  @author:     chenzhiquan
 *  @version     06/05/2008  chenzhiquan  Initial Version
**************************************************************/
#ifndef _BUF_HELPER_H_
#define _BUF_HELPER_H_

#include "CommHelper.h"

#ifndef NULL
#	define NULL 0
#endif

template<class Type>
class CBufferHelper
{
	Type* m_szBuf;	// Pointer To Store Buffer
	size_t m_nSize;	// Array Size
public:
	/**
	 *  Constructor. Allocate A buffer with size of nSize.
	 *
	 *  @param  -[in,out]  size_t  nSize: [size]
	 *
	 *  @version     06/05/2008  chenzhiquan  Initial Version.
	 */
	CBufferHelper(size_t nSize)
	{

		NewArray(nSize);
	}

	/**
	 *  Default Constructor. No Buffer Allocated
	 *
	 *  @version     06/05/2008  chenzhiquan  Initial Version.
	 */
	CBufferHelper()
	{
		m_szBuf = NULL;
		m_nSize = 0;
	}
	
	/**
	 *  Allocate a nSize Large Buffer.
	 *
	 *  @param  -[in,out]  size_t  nSize: [size]
	 *
	 *  @version     06/05/2008  chenzhiquan  Initial Version.
	 */
	void NewArray( size_t nSize )
	{
		m_szBuf = new Type[nSize];
		if (m_szBuf)
		{
			m_nSize = nSize;
			//return CVE_SUCCESS;
		}
		else
		{
			m_nSize = 0;
		}
	}

	/**
	 *  Set Allocated Array.
	 *
	 *  @param  -[in,out]  Type*  pArray: [array of type]
	 *  @param  -[in,out]  size_t  nSize: [size of array]
	 *
	 *  @version     06/05/2008  chenzhiquan  Initial Version.
	 */
	void SetArray(Type* pArray, size_t nSize)
	{
		DelArray();
		m_szBuf = pArray;
		m_nSize = nSize;
	}

	/**
	 *  Get Size Of Array.
	 *
	 *  @return size of array.
	 *
	 *  @version     06/05/2008  chenzhiquan  Initial Version.
	 */
	size_t size() 
	{
		return m_nSize;
	}

	/**
	 *  Get Array Pointer.
	 *
	 *
	 *  @version     06/05/2008  chenzhiquan  Initial Version.
	 */
	Type* GetArray()
	{
		return m_szBuf;
	}

	/**
	 *  Destructor.
	 *
	 *
	 *  @version     06/05/2008  chenzhiquan  Initial Version.
	 */
	virtual ~CBufferHelper()
	{
		DelArray();
	}

	/**
	 *  Delete Allocated Buffer.
	 *
	 *
	 *  @version     06/05/2008  chenzhiquan  Initial Version.
	 */
	void DelArray()
	{
		if (m_nSize)
		{
			SAFE_DELETE_ARRAY(m_szBuf);
			m_nSize = 0;
		}
	}

	Type& GetItem(int nIndex)
	{
		return m_szBuf[nIndex];
	}
	
};

#endif// _BUF_HELPER_H_
