/**************************************************************
*  Filename:    RMAPI.h
*  Copyright:   Shanghai Baosight Software Co., Ltd.
*
*  Description: RM API .
*
*  @author:     chen<PERSON>quan
*  @version     05/29/2008  chenzhiquan  Initial Version
**************************************************************/
#ifndef _RMDS_API_H_
#define _RMDS_API_H_

#include "common/RMAPIDef.h"
#include "errcode/error_code.h"
#include <stddef.h>

#ifdef _WIN32
#	ifdef rmdsapi_EXPORTS
#		define RMDSAPI_DLLEXPORT __declspec(dllexport)
#	else
#		define RMDSAPI_DLLEXPORT __declspec(dllimport)
#	endif
#else //_WIN32
#	define R<PERSON>SAPI_DLLEXPORT 
#endif //_WIN32

#ifndef IN
#	define IN
#endif

typedef long (* RMDSAPI_CallBack)(void*, long, const char*, size_t); 

// Initialize
RMDSAPI_DLLEXPORT long RMDSAPI_Init();

/**
 *  Send Record To Peer Node.
 *
 *  @param  -[in,out]  IN unsigned short  nSysID: [sub system id to identify application]
 *  @param  -[in,out]  IN long  nType: [ type id will send to peer as given]
 *  @param  -[in,out]  IN char*  szBuf: [ buffer ]
 *  @param  -[in,out]  IN unsigned long  nlength: [ length of buffer ]
 *
 *  @version     06/05/2008  chenzhiquan  Initial Version.
 */
RMDSAPI_DLLEXPORT long RMDS_SendRecord2Peer(IN unsigned short nSysID, IN long nType, IN const char* szBuf, IN unsigned long nlength);

/**
 *  Register A Callback Function On nSysID Msg Queue.
 *
 *  @param  -[in,out]  IN long  nSysID: [sub system id to identify application]
 *  @param  -[in,out]  IN RMDSAPI_CallBack  pfCallBack: [call back function]
 *  @param  -[in,out]  IN void*  pParam: [param pass to callback function]
 *
 *  @version     06/05/2008  chenzhiquan  Initial Version.
 */
RMDSAPI_DLLEXPORT long RMDS_RegRecvCallBack(IN unsigned short nSysID, IN RMDSAPI_CallBack pfCallBack, IN void* pParam);

/**
 *  Finalize.
 *	IMPORTANT!!!  RMDSAPI_Fini should be called when using RMDS_RegRecvCallBack
 *                Or Internal Thread cannot exit normally
 *
 *  @version     06/05/2008  chenzhiquan  Initial Version.
 */
RMDSAPI_DLLEXPORT long RMDSAPI_Fini();

#endif// _RMDS_API_H_
