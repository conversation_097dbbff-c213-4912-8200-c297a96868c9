/**************************************************************
 *  Filename:    CV_Shm_Hash_Map_Manager_T.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Hash Map Manager On Share Memory, Modified from ACE_Hash_Map_Manager_Ex.
 *
 *  @author:     chen<PERSON>quan
 *  @version     06/26/2008  chenzhiquan  Initial Version
**************************************************************/

#ifndef CV_Shm_Hash_Map_MANAGER_T_H
#define CV_Shm_Hash_Map_MANAGER_T_H
#include /**/ "ace/pre.h"

#include "ace/config-all.h"

#if !defined (ACE_LACKS_PRAGMA_ONCE)
# pragma once
#endif /* ACE_LACKS_PRAGMA_ONCE */

#include "ace/Default_Constants.h"
#include "ace/Functor_T.h"
#include "ace/Log_Msg.h"
#include "ace/Malloc_Base.h"
#include "ace/Guard_T.h"
#include <ace/Global_Macros.h>
#include <ace/Process_Mutex.h>
#include <ace/Malloc_T.h>
#include <ace/MMAP_Memory_Pool.h>
#include <ace/PI_Malloc.h>

typedef ACE_Allocator_Adapter<ACE_Malloc_T <ACE_MMAP_MEMORY_POOL,
											ACE_Process_Mutex,
											ACE_PI_Control_Block>
							 > ALLOCATOR;

#define CV_SHM_BASE_ADDR (char *)alloc->alloc().base_addr()

ACE_BEGIN_VERSIONED_NAMESPACE_DECL

/**
 * @class CV_Shm_Hash_Map_Entry
 *
 * @brief Define an entry in the hash table.
 */
template <class EXT_ID, class INT_ID>
class CV_Shm_Hash_Map_Entry
{
public:
  // = Initialization and termination methods.
  /// Constructor.
  CV_Shm_Hash_Map_Entry (const EXT_ID &ext_id,
                      const INT_ID &int_id,
                      CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *next = 0,
                      CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *prev = 0);

  /// Constructor.
  CV_Shm_Hash_Map_Entry (CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *next,
                      CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *prev);

  # if ! defined (ACE_HAS_BROKEN_NOOP_DTORS)
  /// Destructor.
  ~CV_Shm_Hash_Map_Entry (void){};
  #endif /* ! defined (ACE_HAS_BROKEN_NOOP_DTORS) */

  /// Key used to look up an entry.
  EXT_ID ext_id_;

  /// The contents of the entry itself.
  INT_ID int_id_;

  /// Pointer to the next item in the bucket of overflow nodes.
  CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *next_;

  /// Pointer to the prev item in the bucket of overflow nodes.
  CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *prev_;

  /// Dump the state of an object.
  void dump (void) const;
};

template <class EXT_ID, class INT_ID, class ALLOCATOR_T>
CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *Offset2RealEntry(CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *pOffset, ALLOCATOR_T *alloc)
{
	return (CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *) ((long)pOffset + CV_SHM_BASE_ADDR);
}
template <class EXT_ID, class INT_ID, class ALLOCATOR_T>
CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *Real2OffsetEntry(CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *pReal, ALLOCATOR_T *alloc)
{
	return (CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *) ((char *)pReal - CV_SHM_BASE_ADDR);
}


// Forward decl.
template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T>
class CV_Shm_Hash_Map_Iterator_Base_Ex;

// Forward decl.
template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T>
class CV_Shm_Hash_Map_Iterator_Ex;

// Forward decl.
template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T>
class CV_Shm_Hash_Map_Reverse_Iterator_Ex;


/**
 * @class CV_Shm_Hash_Map_Manager_Ex
 *
 * @brief Define a map abstraction that efficiently associates
 * <EXT_ID>s with <INT_ID>s.
 *
 * This implementation of a map uses a hash table.  Key hashing
 * is achieved through the HASH_KEY object and key comparison is
 * achieved through the COMPARE_KEYS object.
 * This class uses an <ACE_Allocator> to allocate memory.  The
 * user can make this a persistent class by providing an
 * <ACE_Allocator> with a persistable memory pool.
 */

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T>
class CV_Shm_Hash_Map_Manager
{
public:
  friend class CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>;
  friend class CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>;
  friend class CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>;

  typedef EXT_ID
          KEY;
  typedef INT_ID
          VALUE;
  typedef ACE_LOCK lock_type;
  typedef CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID>
          ENTRY;

  // = ACE-style iterator typedefs.
  typedef CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
          ITERATOR;
  typedef CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
          REVERSE_ITERATOR;
  // = STL-style iterator typedefs.
  typedef CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
          iterator;
  typedef CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
          reverse_iterator;

  typedef CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
		  CV_Shm_Hash_Map_Manager_Allocator;
  // = Initialization and termination methods.

  /// Initialize a <Hash_Map_Manager_Ex> with default size.
   CV_Shm_Hash_Map_Manager (ALLOCATOR_T *alloc, CV_Shm_Hash_Map_Manager_Allocator **ptrOut): 
	   table_ (0),
	   total_size_ (0),
	   cur_size_ (0)
	{
		if (this->open (alloc, ACE_DEFAULT_MAP_SIZE, ptrOut) == -1)
			ACE_ERROR ((LM_ERROR,  ("CV_Shm_Hash_Map_Manager_Ex\n")));
	}

  /// Initialize a <Hash_Map_Manager_Ex> with size <length>.
  CV_Shm_Hash_Map_Manager (size_t size, ALLOCATOR_T *alloc, CV_Shm_Hash_Map_Manager_Allocator **ptrOut):
		table_ (0),
		total_size_ (0),
		cur_size_ (0)
	{
		if (this->open (alloc, size, ptrOut) == -1)
			ACE_ERROR ((LM_ERROR, ("CV_Shm_Hash_Map_Manager_Ex\n")));
	};

  /// Initialize a <Hash_Map_Manager_Ex> with <size> elements.
  int open (ALLOCATOR_T *alloc, size_t size = ACE_DEFAULT_MAP_SIZE, CV_Shm_Hash_Map_Manager_Allocator **ptrOut = NULL);

  /// Close down a <Hash_Map_Manager_Ex> and release dynamically allocated
  /// resources.
  int close ( ALLOCATOR_T *alloc)
	{
		ACE_WRITE_GUARD_RETURN (ACE_LOCK, ace_mon, this->lock_, -1);
	  
		return this->close_i (alloc);
	}

  /// Removes all the entries in <Map_Manager_Ex>.
  int unbind_all (ALLOCATOR_T *alloc)
  {
	  ACE_WRITE_GUARD_RETURN (ACE_LOCK, ace_mon, this->lock_, -1);		  
	  return this->unbind_all_i (alloc);
  }

  /// Cleanup the <Hash_Map_Manager_Ex>.
  ~CV_Shm_Hash_Map_Manager (void);

  /**
   * Associate <ext_id> with <int_id>.  If <ext_id> is already in the
   * map then the <CV_Shm_Hash_Map_Entry> is not changed.  Returns 0 if a
   * new entry is bound successfully, returns 1 if an attempt is made
   * to bind an existing entry, and returns -1 if failures occur.
   */
  int bind (const EXT_ID &ext_id, const INT_ID &int_id, ALLOCATOR_T *alloc)
	{
	  ACE_WRITE_GUARD_RETURN (ACE_LOCK, ace_mon, this->lock_, -1);
	  
	  return this->bind_i (ext_id, int_id, alloc);
	}


  /// Locate <ext_id> and pass out parameter via <int_id>.
  /// Return 0 if found, returns -1 if not found.
  int find (const EXT_ID &ext_id,
            INT_ID &int_id,
			ALLOCATOR_T *alloc) const
	{
	  CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> *nc_this =
		  const_cast <CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> *>
		  (this);
	  
	  ACE_READ_GUARD_RETURN (ACE_LOCK, ace_mon, nc_this->lock_, -1);
	  
	  return nc_this->find_i (ext_id, int_id, alloc);
	}

  /**
   * Unbind (remove) the <ext_id> from the map.  Don't return the
   * <int_id> to the caller (this is useful for collections where the
   * <int_id>s are *not* dynamically allocated...)
   */
  int unbind (const EXT_ID &ext_id, ALLOCATOR_T *alloc);

  /// Break any association of <ext_id>.  Returns the value of <int_id>
  /// in case the caller needs to deallocate memory. Return 0 if the
  /// unbind was successfully, and returns -1 if failures occur.
  int unbind (const EXT_ID &ext_id,
              INT_ID &int_id, ALLOCATOR_T *alloc);

  /// Returns the current number of CV_Shm_Hash_Map_Entry objects in the
  /// hash table.
  size_t current_size (void) const
	{
		return this->cur_size_;
	}

  /// Return the size of the array that's used to point to the
  /// linked lists of CV_Shm_Hash_Map_Entry objects in the hash table.
  size_t total_size (void) const
  {
	  return this->total_size_;
  }

  /**
   * Returns a reference to the underlying <ACE_LOCK>.  This makes it
   * possible to acquire the lock explicitly, which can be useful in
   * some cases if you instantiate the <ACE_Atomic_Op> with an
   * <ACE_Recursive_Mutex> or <ACE_Process_Mutex>, or if you need to
   * guard the state of an iterator.
   * @note The right name would be <lock>, but HP/C++ will choke on that!
   */
  ACE_LOCK &mutex (void)
	{
	  ACE_TRACE ("CV_Shm_Hash_Map_Manager_Ex::mutex");
	  return this->lock_;
	}

  /// Dump the state of an object.
  void dump (void) const;

  // = STL styled iterator factory functions.

  /// Return forward iterator.
  CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK,  ALLOCATOR_T> begin (ALLOCATOR_T *alloc);
  CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK,  ALLOCATOR_T> end (ALLOCATOR_T *alloc);

  /// Return reverse iterator.
  CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> rbegin (ALLOCATOR_T *alloc);
  CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> rend (ALLOCATOR_T *alloc);

protected:
  // = The following methods do the actual work.

  /// Returns 1 if <id1> == <id2>, else 0.  This is defined as a
  /// separate method to facilitate template specialization.
  int equal (const EXT_ID &id1, const EXT_ID &id2);

  /// Compute the hash value of the <ext_id>.  This is defined as a
  /// separate method to facilitate template specialization.
  u_long hash (const EXT_ID &ext_id);

  // = These methods assume locks are held by private methods.

  /// Performs bind.  Must be called with locks held.
  int bind_i (const EXT_ID &ext_id, const INT_ID &int_id, ALLOCATOR_T *alloc)
	{
	  CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *temp;
	  
	  return this->bind_i (ext_id, int_id, temp, alloc);
	}

  /// Performs bind.  Must be called with locks held.
  int bind_i (const EXT_ID &ext_id,
              const INT_ID &int_id,
              CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *&entry, 
			  ALLOCATOR_T *alloc);

  /// Performs a find of <int_id> using <ext_id> as the key.  Must be
  /// called with locks held.
  int find_i (const EXT_ID &ext_id,
              INT_ID &int_id, ALLOCATOR_T *alloc);

  /// Performs a find using <ext_id> as the key.  Must be called with
  /// locks held.
  int find_i (const EXT_ID &ext_id,
              CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *&entry, ALLOCATOR_T *alloc);


  /// Performs unbind.  Must be called with locks held.
  int unbind_i (const EXT_ID &ext_id, ALLOCATOR_T *alloc);
  int unbind_i (const EXT_ID &ext_id, INT_ID &int_id, ALLOCATOR_T *alloc);

  /// Performs unbind.  Must be called with locks held.
  int unbind_i (CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *entry, ALLOCATOR_T *alloc);

  /**
   * Resize the map.  Must be called with locks held.
   * @note This method should never be called more than once or else all the
   * hashing will get screwed up as the size will change.
   */
  int create_buckets (size_t size, ALLOCATOR_T *alloc, CV_Shm_Hash_Map_Manager_Allocator **ptrOut);

  /// Close down a <Map_Manager_Ex>.  Must be called with
  /// locks held.
  int close_i ( ALLOCATOR_T *alloc);

  /// Removes all the entries in <Map_Manager_Ex>.  Must be called with
  /// locks held.
  int unbind_all_i (ALLOCATOR_T *alloc);

  /// Pointer to a memory allocator.
  //ALLOCATOR_T *allocator_;

  /// Synchronization variable for the MT_SAFE <CV_Shm_Hash_Map_Manager_Ex>.
  ACE_LOCK lock_;

  /// Function object used for hashing keys.
  HASH_KEY hash_key_;

  /// Function object used for comparing keys.
  COMPARE_KEYS compare_keys_;

protected:
  /// Returns the <CV_Shm_Hash_Map_Entry> that corresponds to <ext_id>.
  int shared_find (const EXT_ID &ext_id,
                   CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *&entry,
                   size_t &loc, ALLOCATOR_T *alloc);

  /// Accessor of the underlying table
  CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *table (ALLOCATOR_T *alloc)
  {
	  return Offset2RealEntry(table_, alloc);
  }

  /// Accessor of the current size attribute
  size_t cur_size (void) const;

private:
  /**
   * Array of <CV_Shm_Hash_Map_Entry> *s, each of which points to an
   * <CV_Shm_Hash_Map_Entry> that serves as the beginning of a linked
   * list of <EXT_ID>s that hash to that bucket.
   */
  CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *table_;

  /// Total size of the hash table.
  size_t total_size_;

  /// Current number of entries in the table
  /// @note That this can be larger than <total_size_> due to the
  /// bucket chaining).
  size_t cur_size_;

  // = Disallow these operations.
  // Hack alert... Sun C++ 5.4 can't hack explicit templates with this
  // in place. Please remove this when taking out explicit templates.
#if !(defined (__SUNPRO_CC) && (__SUNPRO_CC <= 0x540) && \
      defined (ACE_HAS_EXPLICIT_TEMPLATE_INSTANTIATION))
  ACE_UNIMPLEMENTED_FUNC (void operator= (const CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID,  HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &))
#endif /* __SUNPRO_CC <= 0x540 && !ACE_HAS_EXPLICIT_TEMPLATE_INSTANTIATION */

  ACE_UNIMPLEMENTED_FUNC (CV_Shm_Hash_Map_Manager (const CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID,  HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &))
};

/**
 * @class CV_Shm_Hash_Map_Iterator_Base_Ex
 *
 * @brief Base iterator for the <CV_Shm_Hash_Map_Manager_Ex>
 *
 * This class factors out common code from its templatized
 * subclasses.
 */
template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T>
class CV_Shm_Hash_Map_Iterator_Base_Ex
{
public:
  // = Initialization method.
  /// Contructor.  If head != 0, the iterator constructed is positioned
  /// at the head of the map, it is positioned at the end otherwise.
  CV_Shm_Hash_Map_Iterator_Base_Ex (CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &mm, 
								 ALLOCATOR_T *alloc,
                                 int head);

  // = ITERATION methods.

  /// Pass back the next <entry> that hasn't been seen in the Set.
  /// Returns 0 when all items have been seen, else 1.
  int next (CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *&next_entry) const;

  /// Returns 1 when all items have been seen, else 0.
  int done (void) const;

  /// Returns a reference to the interal element <this> is pointing to.
  CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID>& operator* (void) const;

  /// Returns reference the Hash_Map_Manager_Ex that is being iterated
  /// over.
  CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>& map (void);

  /// Check if two iterators point to the same position
  bool operator== (const CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &) const;
  bool operator!= (const CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &) const;

  /// Declare the dynamic allocation hooks.
  ACE_ALLOC_HOOK_DECLARE;

protected:
  /// Move forward by one element in the set.  Returns 0 when there's
  /// no more item in the set after the current items, else 1.
  int forward_i (void);

  /// Move backward by one element in the set.  Returns 0 when there's
  /// no more item in the set before the current item, else 1.
  int reverse_i (void);

  /// Dump the state of an object.
  void dump_i (void) const;

  /// Map we are iterating over.
  CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> *map_man_;

  /// Keeps track of how far we've advanced in the table.
  ssize_t index_;

  ALLOCATOR_T *alloc_;

  /// Keeps track of how far we've advanced in a linked list in each
  /// table slot.
  CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *next_;
};


/**
 * @class CV_Shm_Hash_Map_Iterator_Ex
 *
 * @brief Forward iterator for the <CV_Shm_Hash_Map_Manager_Ex>.
 *
 * This class does not perform any internal locking of the
 * <CV_Shm_Hash_Map_Manager_Ex> it is iterating upon since locking is
 * inherently inefficient and/or error-prone within an STL-style
 * iterator.  If you require locking, you can explicitly use an
 * ACE_Guard or ACE_Read_Guard on the <CV_Shm_Hash_Map_Manager_Ex>'s
 * internal lock, which is accessible via its <mutex> method.
 */
template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T>
class CV_Shm_Hash_Map_Iterator_Ex : public CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
{
public:
  // = Initialization method.
  CV_Shm_Hash_Map_Iterator_Ex (CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &mm,
							ALLOCATOR_T *alloc,
                            int tail = 0);

  // = Iteration methods.
  /// Move forward by one element in the set.  Returns 0 when all the
  /// items in the set have been seen, else 1.
  int advance (void);

  /// Dump the state of an object.
  void dump (void) const;

  // = STL styled iteration, compare, and reference functions.

  /// Prefix advance.
  CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &operator++ (void);

  /// Postfix advance.
  CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> operator++ (int);

  /// Prefix reverse.
  CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &operator-- (void);

  /// Postfix reverse.
  CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> operator-- (int);

  /// Declare the dynamic allocation hooks.
  ACE_ALLOC_HOOK_DECLARE;
};

/**
 * @class CV_Shm_Hash_Map_Reverse_Iterator_Ex
 *
 * @brief Reverse iterator for the <CV_Shm_Hash_Map_Manager_Ex>.
 *
 * This class does not perform any internal locking of the
 * <CV_Shm_Hash_Map_Manager_Ex> it is iterating upon since locking is
 * inherently inefficient and/or error-prone within an STL-style
 * iterator.  If you require locking, you can explicitly use an
 * ACE_Guard or ACE_Read_Guard on the <CV_Shm_Hash_Map_Manager_Ex>'s
 * internal lock, which is accessible via its <mutex> method.
 */
template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T>
class CV_Shm_Hash_Map_Reverse_Iterator_Ex : public CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
{
public:
  // = Initialization method.
  CV_Shm_Hash_Map_Reverse_Iterator_Ex (CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &mm,
									ALLOCATOR_T *alloc,
                                    int head = 0);

  // = Iteration methods.
  /// Move forward by one element in the set.  Returns 0 when all the
  /// items in the set have been seen, else 1.
  int advance (void);

  /// Dump the state of an object.
  void dump (void) const;

  // = STL styled iteration, compare, and reference functions.

  /// Prefix reverse.
  CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &operator++ (void);

  /// Postfix reverse.
  CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> operator++ (int);

  /// Prefix advance.
  CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &operator-- (void);

  /// Postfix advance.
  CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> operator-- (int);

  /// Declare the dynamic allocation hooks.
  ACE_ALLOC_HOOK_DECLARE;
};

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK,class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::~CV_Shm_Hash_Map_Manager (void)
{
  //this->close ();
	//TODO:
}


template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK,class ALLOCATOR_T> ACE_INLINE u_long
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::hash (const EXT_ID &ext_id)
{
  return this->hash_key_ (ext_id);
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK,class ALLOCATOR_T> ACE_INLINE int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::equal (const EXT_ID &id1,
                                                                                  const EXT_ID &id2)
{
  return this->compare_keys_ (id1, id2);
}


template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK,class ALLOCATOR_T> ACE_INLINE int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::unbind_i (const EXT_ID &ext_id, ALLOCATOR_T *alloc)
{
  INT_ID int_id;

  return this->unbind_i (ext_id, int_id, alloc);
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK,class ALLOCATOR_T> ACE_INLINE int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::unbind (const EXT_ID &ext_id,
                                                                                   INT_ID &int_id, ALLOCATOR_T *alloc)
{
  ACE_WRITE_GUARD_RETURN (ACE_LOCK, ace_mon, this->lock_, -1);

  return this->unbind_i (ext_id, int_id, alloc);
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK,class ALLOCATOR_T> ACE_INLINE int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::unbind (const EXT_ID &ext_id, ALLOCATOR_T *alloc)
{
  ACE_WRITE_GUARD_RETURN (ACE_LOCK, ace_mon, this->lock_, -1);

  return this->unbind_i (ext_id, alloc) == -1 ? -1 : 0;
}


template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::find_i (const EXT_ID &ext_id,
                                                                                   INT_ID &int_id, ALLOCATOR_T *alloc)
{
  CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *entry;

  size_t dummy;
  if (this->shared_find (ext_id, entry, dummy, alloc) == -1)
    return -1;
  else
    {
      int_id = entry->int_id_;
      return 0;
    }
}



template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::find_i (const EXT_ID &ext_id,
                                                                                   CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *&entry,
																				   ALLOCATOR_T *alloc)
{
  size_t dummy;
  return this->shared_find (ext_id, entry, dummy, alloc);
}


template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::begin (ALLOCATOR_T *alloc)
{
  return CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> (*this, alloc);
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::end (ALLOCATOR_T *alloc)
{
  return CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> (*this, alloc, 1);
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::rbegin (ALLOCATOR_T *alloc)
{
  return CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> (*this, alloc);
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::rend (ALLOCATOR_T *alloc)
{
  return CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> (*this, alloc, 1);
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
size_t
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::cur_size (void) const
{
  return this->cur_size_;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::CV_Shm_Hash_Map_Iterator_Base_Ex (CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &mm,
																												ALLOCATOR_T *alloc,
                                                                                                                int head)
  : map_man_ (&mm),
	alloc_ (alloc),
    index_ (head != 0 ? -1 : (ssize_t) mm.total_size_),
    next_ (0)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::CV_Shm_Hash_Map_Iterator_Base_Ex");

  if (mm.table_ != 0)
    this->next_ = Real2OffsetEntry(&mm.table(alloc_)[head != 0 ? 0 : mm.total_size_ - 1], alloc_);
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE int
CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::next (CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *&entry) const
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::next");

  if (this->map_man_->table_ != 0
      && this->index_ < static_cast<ssize_t> (this->map_man_->total_size_)
      && this->index_ >= 0
      && Offset2RealEntry(this->next_, alloc_) != &this->map_man_->table(alloc_)[this->index_])
    {
      entry = Offset2RealEntry(this->next_, alloc_);
      return 1;
    }
  else
    return 0;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE int
CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::done (void) const
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::done");

  return this->map_man_->table_ == 0
    || this->index_ >= static_cast<ssize_t> (this->map_man_->total_size_)
    || this->index_ < 0;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> &
CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::operator* (void) const
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::operator*");
  CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *retv = 0;

  int result = this->next (retv);

  ACE_UNUSED_ARG (result);
  ACE_ASSERT (result != 0);

  return *retv;
}

// Returns the reference to the hash_map_manager_ex that is being
// iterated over.
template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>&
CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::map (void)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::map");
  return *this->map_man_;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE bool
CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::operator== (const CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &rhs) const
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::operator==");
  return this->map_man_ == rhs.map_man_
    && this->index_ == rhs.index_
    && this->next_ == rhs.next_;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE bool
CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::operator!= (const CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &rhs) const
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::operator!=");
  return this->next_ != rhs.next_
    || this->index_ != rhs.index_
    || this->map_man_ != rhs.map_man_;
}



ACE_ALLOC_HOOK_DEFINE(CV_Shm_Hash_Map_Iterator_Ex)

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE void
CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::dump (void) const
{
#if defined (ACE_HAS_DUMP)
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::dump");

  this->dump_i ();
#endif /* ACE_HAS_DUMP */
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::CV_Shm_Hash_Map_Iterator_Ex (CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &mm, ALLOCATOR_T *alloc,
                                                                                                      int tail)
  : CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> (mm, alloc,
                                                                                     tail == 0 ? 1 : 0)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::CV_Shm_Hash_Map_Iterator_Ex");
  if (tail == 0)
    this->forward_i ();
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE int
CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::advance (void)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::advance");
  return this->forward_i ();
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &
CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::operator++ (void)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::operator++ (void)");

  this->forward_i ();
  return *this;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::operator++ (int)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::operator++ (int)");

  CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> retv (*this);
  ++*this;
  return retv;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &
CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::operator-- (void)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::operator-- (void)");

  this->reverse_i ();
  return *this;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::operator-- (int)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::operator-- (int)");

  CV_Shm_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> retv (*this);
  --*this;
  return retv;
}


ACE_ALLOC_HOOK_DEFINE(CV_Shm_Hash_Map_Reverse_Iterator_Ex)

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE void
CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::dump (void) const
{
#if defined (ACE_HAS_DUMP)
  ACE_TRACE ("CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::dump");

  this->dump_i ();
#endif /* ACE_HAS_DUMP */
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::CV_Shm_Hash_Map_Reverse_Iterator_Ex (CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &mm, ALLOCATOR_T *alloc, int head)
  : CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> (mm, alloc, head)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::CV_Shm_Hash_Map_Reverse_Iterator_Ex");
  if (head == 0)
    this->reverse_i ();
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE int
CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::advance (void)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::advance");
  return this->reverse_i ();
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &
CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::operator++ (void)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::operator++ (void)");

  this->reverse_i ();
  return *this;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::operator++ (int)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::operator++ (int)");

  CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> retv (*this);
  ++*this;
  return retv;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> &
CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::operator-- (void)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::operator-- (void)");

  this->forward_i ();
  return *this;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> ACE_INLINE
CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>
CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::operator-- (int)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::operator-- (int)");

  CV_Shm_Hash_Map_Reverse_Iterator_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> retv (*this);
  --*this;
  return retv;
}

template <class EXT_ID, class INT_ID>
CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID>::CV_Shm_Hash_Map_Entry (CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *next,
                                                        CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *prev)
  : next_ (next),
    prev_ (prev)
{
}

template <class EXT_ID, class INT_ID>
CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID>::CV_Shm_Hash_Map_Entry (const EXT_ID &ext_id,
                                                        const INT_ID &int_id,
                                                        CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *next,
                                                        CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *prev)
  : ext_id_ (ext_id),
    int_id_ (int_id),
    next_ (next),
    prev_ (prev)
{
}


template <class EXT_ID, class INT_ID> void
CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID>::dump (void) const
{
#if defined (ACE_HAS_DUMP)
  ACE_DEBUG ((LM_DEBUG, ACE_BEGIN_DUMP, this));
  ACE_DEBUG ((LM_DEBUG,   ("next_ = %d"), this->next_));
  ACE_DEBUG ((LM_DEBUG,   ("prev_ = %d"), this->prev_));
  ACE_DEBUG ((LM_DEBUG, ACE_END_DUMP));
#endif /* ACE_HAS_DUMP */
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> void
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::dump (void) const
{
#if defined (ACE_HAS_DUMP)
  ACE_DEBUG ((LM_DEBUG, ACE_BEGIN_DUMP, this));
  ACE_DEBUG ((LM_DEBUG,   ("total_size_ = %d"), this->total_size_));
  ACE_DEBUG ((LM_DEBUG,   ("\ncur_size_ = %d"), this->cur_size_));
  this->allocator_->dump ();
  this->lock_.dump ();
  ACE_DEBUG ((LM_DEBUG, ACE_END_DUMP));
#endif /* ACE_HAS_DUMP */
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::create_buckets (size_t size, ALLOCATOR_T *alloc, CV_Shm_Hash_Map_Manager_Allocator **ptrOut)
{
  size_t bytes = size * sizeof (CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID>);
  void *ptr;

  long nOffset = (char *)this - CV_SHM_BASE_ADDR;

  ACE_ALLOCATOR_RETURN (ptr,
                        alloc->malloc (bytes),
                        -1);

  CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> *pThis 
	  = (CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> *) (CV_SHM_BASE_ADDR + nOffset);

	CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> * pTable;

	pTable = (CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *) ptr;
	pThis->table_ = Real2OffsetEntry(pTable, alloc);

	pThis->total_size_ = size;

  // Initialize each entry in the hash table to be a circular linked
  // list with the dummy node in the front serving as the anchor of
  // the list.
  for (size_t i = 0; i < size; i++)
    new (&pTable[i]) CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> (&pThis->table_[i],
                                                               &pThis->table_[i]);
  *ptrOut = pThis;

  return 0;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::open (ALLOCATOR_T *alloc, size_t size, CV_Shm_Hash_Map_Manager_Allocator **ptrOut)
{
  ACE_WRITE_GUARD_RETURN (ACE_LOCK, ace_mon, this->lock_, -1);

  // Calling this->close_i () to ensure we release previous allocated
  // memory before allocating new one.
  ACE_ASSERT (alloc != 0);

  this->close_i (alloc);

  // This assertion is here to help track a situation that shouldn't
  // happen, but did with Sun C++ 4.1 (before a change to this class
  // was made: it used to have an enum that was supposed to be defined
  // to be ACE_DEFAULT_MAP_SIZE, but instead was defined to be 0).
  ACE_ASSERT (size != 0);

  return this->create_buckets (size, alloc, ptrOut);
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::close_i (ALLOCATOR_T *alloc)
{
  // Protect against "double-deletion" in case the destructor also
  // gets called.
  if (this->table_ != 0)
    {
		CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> * pTable;
		pTable = Offset2RealEntry(this->table_, alloc);
      // Remove all the entries.
      this->unbind_all_i (alloc);

      // Iterate through the buckets cleaning up the sentinels.
      for (size_t i = 0; i < this->total_size_; i++)
        {
          // Destroy the dummy entry.
          CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *entry = &pTable[i];
          // The "if" second argument results in a no-op instead of
          // deallocation.
          ACE_DES_FREE_TEMPLATE2 (entry, alloc->free,
                                  CV_Shm_Hash_Map_Entry, EXT_ID, INT_ID);
        }

      // Reset size.
      this->total_size_ = 0;

      // Free table memory.
      alloc->free (pTable);

      // Should be done last...
      this->table_ = 0;
    }

  return 0;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::unbind_all_i (ALLOCATOR_T *alloc)
{
  // Iterate through the entire map calling the destuctor of each
  // <CV_Shm_Hash_Map_Entry>.

	CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> * pTable;
	//pTable = (CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *) ((long)this->table_ + CV_SHM_BASE_ADDR);
	pTable = table(alloc);

  for (size_t i = 0; i < this->total_size_; i++)
    {
      for (CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *temp_ptr = Offset2RealEntry(pTable[i].next_, alloc);
           temp_ptr != &pTable[i];
           )
        {
          CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *hold_ptr = temp_ptr;
          temp_ptr = Offset2RealEntry(temp_ptr->next_, alloc);

          // Explicitly call the destructor.
          ACE_DES_FREE_TEMPLATE2 (hold_ptr, alloc->free,
                                  CV_Shm_Hash_Map_Entry, EXT_ID, INT_ID);
        }

      // Restore the sentinel.
	  pTable[i].next_ = Real2OffsetEntry(&pTable[i], alloc);
      pTable[i].prev_ = Real2OffsetEntry(&pTable[i], alloc);
    }

  this->cur_size_ = 0;

  return 0;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::bind_i (const EXT_ID &ext_id,
                                                                                   const INT_ID &int_id,
                                                                                   CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *&entry,
																				   ALLOCATOR_T *alloc)
{
  size_t loc;
  int result = this->shared_find (ext_id, entry, loc, alloc);

  if (result == -1)
    {
      void *ptr;
      // Not found.
		//CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID>* pOffsetTable = this->table_;
		
		long nOffset = (char *)this - CV_SHM_BASE_ADDR;

      ACE_ALLOCATOR_RETURN (ptr,
                            alloc->malloc (sizeof (CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID>)),
                            -1);

	  CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> *pThis 
		  = (CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T> *) (CV_SHM_BASE_ADDR + nOffset);

      entry = new (ptr) CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> (ext_id,
                                                            int_id,
                                                            pThis->table(alloc)[loc].next_,
                                                            Real2OffsetEntry(&Offset2RealEntry(pThis->table_, alloc)[loc], alloc));
      Offset2RealEntry(pThis->table_, alloc)[loc].next_ = Real2OffsetEntry(entry, alloc);
      Offset2RealEntry(entry->next_, alloc)->prev_ = Real2OffsetEntry(entry, alloc);

      pThis->cur_size_++;
      return 0;
    }
  else
    return 1;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::unbind_i (const EXT_ID &ext_id,
                                                                                     INT_ID &int_id, ALLOCATOR_T *alloc)
{
  CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *temp;

  size_t loc;
  int result = this->shared_find (ext_id, temp, loc, alloc);

  if (result == -1)
    {
      errno = ENOENT;
      return -1;
    }

  int_id = temp->int_id_;

  return this->unbind_i (temp, alloc);
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::unbind_i (CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *entry, ALLOCATOR_T *alloc)
{
  Offset2RealEntry(entry->next_, alloc)->prev_ = entry->prev_;
  Offset2RealEntry(entry->prev_, alloc)->next_ = entry->next_;

  // Explicitly call the destructor.
  ACE_DES_FREE_TEMPLATE2 (entry, alloc->free,
                          CV_Shm_Hash_Map_Entry, EXT_ID, INT_ID);
  this->cur_size_--;
  return 0;
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> int
CV_Shm_Hash_Map_Manager<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::shared_find (const EXT_ID &ext_id,
                                                                                        CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *&entry,
                                                                                        size_t &loc,
																						ALLOCATOR_T *alloc)
{
  loc = this->hash (ext_id) % this->total_size_;

  CV_Shm_Hash_Map_Entry<EXT_ID, INT_ID> *temp = Offset2RealEntry(table(alloc)[loc].next_, alloc);

  while (temp != &table(alloc)[loc] && this->equal (temp->ext_id_, ext_id) == 0)
    temp = Offset2RealEntry(temp->next_, alloc);

  if (temp == &table(alloc)[loc])
    {
      errno = ENOENT;
      return -1;
    }
  else
    {
      entry = temp;
      return 0;
    }
	return 0;
}


// ------------------------------------------------------------

ACE_ALLOC_HOOK_DEFINE(CV_Shm_Hash_Map_Iterator_Base_Ex)

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> void
CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::dump_i (void) const
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::dump_i");

  ACE_DEBUG ((LM_DEBUG, ACE_BEGIN_DUMP, this));
  ACE_DEBUG ((LM_DEBUG,   ("index_ = %d "), this->index_));
  ACE_DEBUG ((LM_DEBUG,   ("next_ = %x"), this->next_));
  ACE_DEBUG ((LM_DEBUG, ACE_END_DUMP));
}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> int
CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::forward_i (void)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::forward_i");

  if (this->map_man_->table_ == 0)
    return -1;
  // Handle initial case specially.
  else if (this->index_ == -1)
    {
      this->index_++;
      return this->forward_i ();
    }
  else if (this->index_ >= static_cast<ssize_t> (this->map_man_->total_size_))
    return 0;

  this->next_ = Offset2RealEntry(this->next_, alloc_)->next_;
  if (this->next_ == Real2OffsetEntry(&Offset2RealEntry(this->map_man_->table_, alloc_)[this->index_], alloc_))
    {
      while (++this->index_ < static_cast<ssize_t> (this->map_man_->total_size_))
        {
          this->next_ = this->map_man_->table(alloc_)[this->index_].next_;
          if (Offset2RealEntry(this->next_, alloc_) != &this->map_man_->table(alloc_)[this->index_])
            break;
        }
    }

  return this->index_ < static_cast<ssize_t> (this->map_man_->total_size_);

}

template <class EXT_ID, class INT_ID, class HASH_KEY, class COMPARE_KEYS, class ACE_LOCK, class ALLOCATOR_T> int
CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR_T>::reverse_i (void)
{
  ACE_TRACE ("CV_Shm_Hash_Map_Iterator_Base_Ex<EXT_ID, INT_ID, HASH_KEY, COMPARE_KEYS, ACE_LOCK, ALLOCATOR>::reverse_i");

  if (this->map_man_->table_ == 0)
    return -1;
  else if (this->index_ == static_cast<ssize_t> (this->map_man_->total_size_))
    {
      this->index_--;
      return this->reverse_i ();
    }
  else if (this->index_ < 0)
    return 0;

  this->next_ = Offset2RealEntry(this->next_, alloc_)->prev_;
  if (Offset2RealEntry(this->next_, alloc_) == &this->map_man_->table(alloc_)[this->index_])
    {
      while (--this->index_ >= 0)
        {
          this->next_ = this->map_man_->table(alloc_)[this->index_].prev_;
          if (Offset2RealEntry(this->next_, alloc_) != &this->map_man_->table(alloc_)[this->index_])
            break;
        }
    }

  return this->index_ >= 0;
}


// ------------------------------------------------------------


ACE_END_VERSIONED_NAMESPACE_DECL


#include /**/ "ace/post.h"
#endif /* CV_Shm_Hash_Map_MANAGER_T_H */
