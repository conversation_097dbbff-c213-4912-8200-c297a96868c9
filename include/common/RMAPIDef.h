#ifndef _RM_API_DEF_H_
#define _RM_API_DEF_H_
// RM Object status
#define ICV_RM_OBJ_STATUS_READY			1
#define ICV_RM_OBJ_STATUS_NOT_READY		2

//RETURN CODE OF REDUN<PERSON>NCY MANAGEMENT
#define RM_STATUS_INACTIVE		0
#define RM_STATUS_ACTIVE		1
#define RM_STATUS_UNAVALIBLE	2

//RETURN CODE OF REDUNDANCY MANAGEMENT
#define RM_STATUS_CTRL_INACTIVE		RM_STATUS_INACTIVE
#define RM_STATUS_CTRL_ACTIVE		RM_STATUS_ACTIVE
#define RM_STATUS_CTRL_NIL			RM_STATUS_UNAVALIBLE + 1
#define RM_STATUS_CTRL_ACK			RM_STATUS_UNAVALIBLE + 2

//获取到的核心服务总状态
#define RM_CORESERVICE_STATUS_INACTIVE    0
#define RM_CORESERVICE_STATUS_ACTIVE      1
#define RM_CORESERVICE_STATUS_UNAVALIBLE  2
//CallBack Function Type
//typedef long(*RMCallBack)(long);

#endif//_RM_API_DEF_H_
