#ifndef _CV_INT_H
#define _CV_INT_H

#if defined _MSC_VER && _MSC_VER < 1600 // for msvc6
	#include <basetsd.h>
	typedef UINT32 uint32_t;
	typedef UINT64 uint64_t;
	typedef INT32  int32_t;
	typedef INT64 int64_t;
	typedef unsigned short	uint16_t;
	typedef short	int16_t;
	typedef unsigned char	uint8_t;
	typedef char	int8_t;
#else
	//#ifdef SIZE_MAX
	//#undef SIZE_MAX
	//#endif//SIZE_MAX

	#include <stdint.h>
#endif//_WIN32

#endif
