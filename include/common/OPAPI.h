﻿#ifndef __SMART_OP_API_H_
#define __SMART_OP_API_H_

#include "common/TypeCast.h"
#include "common/CV_Time.h"
#include "common/CVLog.h"
#include "ace/Time_Value.h"
#include "ace/OS_NS_sys_time.h"
#include "processdb/RDADef.h"
#include "hdProcComm.h"
#include "data_types.h"
#include "errcode/error_code.h"
#include "errcode/hd_error_code.h"
#include <list>
#include <map>
#include <string>
using namespace std;
#ifdef _WIN32
#define OP_API extern "C" __declspec(dllexport)
#else
#define OP_API extern "C"
#endif


/*
	OP API 通信协议:
	W = WORD = int32
	SH = Short = int16；
	B = Byte = char
	VER =版本号
	OP = 操作类型
	NUM = 子包数量
	UNITLEN = 每个公共块中子包（当前为追踪点配置信息）长度-注意为定长

	|------共享内存公共块-------------------------------------------------------| |------------------------共享内存普通区域数据域 -----------------------|
	|--B0-B1--|--B2-B3-|--B4-B7--|--B8---B11--|---------------------------------| |------------------------数据域示例------------------------------------|
	|--VER----|--OP----|---NUM---|----UNITLEN-|---实际配置数据128+32+32+32+32---| |---数据子包个数-4Btye--|--当前包号4Btye-|-本包内容长4Btye-|包内容自定义---|

	采样点包内容示例
	|-TagName-160Byte-|-ModName-32Byte-|-Status-2Byte-|-Time-8Btye-|-Msg长度--Msg内容|
*/


#define OP_PACE_VER  001						//当前版本001
#define OP_PACK_TRACE_TAG 1
#define TRACE_TAG_CFG_TYPE 1
#define TRACE_TAG_VER_LEN 2
#define TRACE_TAG_OP_LEN 2
#define TRACE_TAG_NUM_START_POS 4
#define TRACE_TAG_UNITLEN_START_POS 8
#define TRACE_TAG_START_POS	12
#define TRACE_DATA_PACKAGE_NUM_POS 4
#define TRACE_DATA_PACK_LEN_POS 8
#define TRACE_DATA_PACK_START_POS 12
#define TRACE_DATA_UNIT_LEN 256
#define TRACE_DATA_PACK_TIME_START	160
#define TRACE_DATA_PACK_STATUS_START 224
#define TIMECHARLEN	32							//unsigned long long MAX=18446744073709551615
#define STATUSCHARLEN 16
#define TAG_STATUS_TURE 1

#define FULL_TAG_CFG_BUF_LEN	(1024*1024)
#define UNIT_TAG_CFG_LEN 1024

//op errorcode update

#define OP_TAG_TRACE_BUF_NULL -1
#define OP_DATA_TYPE_UNKOWN -2
#define OP_DATA_TOO_LONG -3
#define OP_TAG_TRACE_CALLBACK_NULL -4


// proccomm 及queue的相关参数定义
#define SMART_OP_API_SHARE_MEM_NAME				"smartop-share"
#define SMART_OP_API_SHARE_MEM_SIZE_M			32		// 共享内存的size，单位是M
#define SMART_OP_API_SHARE_MEM_BLOCK_SIZE_BYTE	4096	// 以此block_size分割共享内存
#define SMART_OP_API_PROCCOMM_QUEUEID_1			100
#define SMART_OP_API_COMMONBLOCK_SIZE_BYTE		1024*1024

//模块名称最大长度
#define OP_TRACE_INFO_MODEL_NAME_MAXLEN 32
#define OP_TAG_MSG_STR_LEN 512

#ifndef OP_PROCCOMMQUEUEHANDLE
#define OP_PROCCOMMQUEUEHANDLE
typedef void* OPCommQueueHandle;
#endif


typedef int16 OP_Type;							// 1 = 数据包类型，追踪点， 2 = todo
typedef int16 OP_VER_TYPE;



//  example
//	"tagName":"tag1",
//	"moudle":"rda",
//	"status":1,
//	"message":"是否收到数据:vtq",最长1024字节
//	"date":"2021-08-08 14:22:33",
//#pragma  pack(1)
typedef struct {
	char  szTagName[PDB_MAX_TAG_NAME_LEN + PDB_MAX_FIELD_NAME_LEN];
	char  szModName[OP_TRACE_INFO_MODEL_NAME_MAXLEN];
	int16  bStatus;
	ACE_Time_Value time;
	char   strMsg[OP_TAG_MSG_STR_LEN];
}TRACE_TAG_MSG;
#define TAG_MSG_LEN 1256  //按1256计算

typedef struct{
	char szTagName[PDB_MAX_TAG_NAME_LEN];
	char szFieldName[PDB_MAX_FIELD_NAME_LEN];
	int32 nTagID;
	uint16 nFieldID;
}OP_TRACE_TAG_Record;



typedef struct Trace_Tag_Info{
	char szTagName[PDB_MAX_TAG_NAME_LEN];
	char szFieldName[PDB_MAX_FIELD_NAME_LEN];
	int32 nTagID;
	uint16 nFieldID;
	ACE_Time_Value startTime;
	ACE_Time_Value endTime;
	int16 nTracestatus;
}TRACE_TAG_CONFIG;

typedef struct Trace_Tag_Info_Mid{
	char szTagName[PDB_MAX_TAG_NAME_LEN];
	char szFieldName[PDB_MAX_FIELD_NAME_LEN];
	int32 nTagID;
	uint16 nFieldID;
	long startTime;
	long endTime;
	int nTracestatus;
}TRACE_TAG_CONFIG_MID;


//#pragma  pack()

/**
*	获取需要追踪点的相关信息：点名，时间，并进行处理，然后写入共享内存固定位置，主要由RDAServer调用
*   处理方法：
*   1)如果同一个点有多个界面需要跟踪，则最终配置取endTime比较大的
*   2)需要记录时间差，用于后面推送时补齐推送的时间
*	@param[in] listTraceTags		tag点list表，点结构见上.
*	@param[in] startTime			开始时间:秒tv_sec和微秒tv_usec,
*	@param[in] endTime				结束时间:秒tv_sec和微秒tv_usec
*	@param[in] nTracestatus			跟踪状态 1：开始 0：结束.
*	@return
*		- ==0 成功
*		- !=0 出现异常
*/
OP_API long OP_API_Write_Tracing_Tags_Info(const std::list<OP_TRACE_TAG_Record> &listTraceTags, 
									const ACE_Time_Value &startTime, 
									const ACE_Time_Value &endTime, 
									int16 nTracestatus
									);


/**
*	由数据源模块(驱动模块)调用数据源往该结构写入追踪点最新信息，
*  （预留扩展接口，发送给smartop时添加消息头部，包含类别、版本等信息，见协议)
*	@param[in] listTraceTag
*	@return
*		- ==0 成功
*		- !=0 出现异常
*/
OP_API long OP_API_Write_Trace_Tags_Msg(const TRACE_TAG_MSG &traceTagMsg);





/**
*	OPAPI周期性调用，往该结构写入追踪点最新信息，
*  （预留扩展接口，发送给smartop时添加消息头部，包含类别、版本等信息，见协议)
*	@param[in] listTraceTag
*	@return
*		- ==0 成功
*		- !=0 出现异常
*/
OP_API long OP_API_Check_CFG_Update( );

/**
*	OPAPI相关共享内存初始化，客户端服务端都调用
*	@return
*		- ==0 成功
*		- !=0 出现异常
*/
OP_API long OP_API_Init();

/**
客户端调用
*/
typedef int (*TAG_TRACE_CALLBACK_PTR_TYPE)(const std::list<OP_TRACE_TAG_Record> &allTraceTags);
OP_API long OP_API_Client_Init(TAG_TRACE_CALLBACK_PTR_TYPE tag_trace_callback);

//smart op 调用 OP_API_Release之前调用
OP_API long OP_API_Server_Release();

/**
客户端调用
*/
OP_API long OP_API_Client_Release();
/**
*	OPAPI相关共享内存释放，客户端服务端都要调用
*	@return
*		- ==0 成功
*		- !=0 出现异常
*/
OP_API long OP_API_Release();





/*给SMART OP调用的接口*/
OP_API long OP_API_Notify_New_Trace_Info( );
OP_API long OP_API_Read_Trace_Tags_Msg(char **pRecvBuf, int32 *realRecvLen);



#endif //__SMART_OP_API_H_