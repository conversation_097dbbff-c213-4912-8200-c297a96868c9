#ifndef _LOCAL_REAL_TIME_DATA_HEADER_
#define _LOCAL_REAL_TIME_DATA_HEADER_

#include "common/CV_Time.h"
#include "common/LRDADef.h"
#include "ace/Timer_Queue.h"

/************************************************************************/
/*  Global interface APIs: external local db accessor                   */
/************************************************************************/

/**
 *  Initialize LRDA.
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Init();

/**
 *  Release.
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Release();

/**
 *  Define A group For Operation.
 *
 *  @param  -[in]  PFN_AsyncCallback  pfnAsyncCallBack: [callback function]
 *  @param  -[in]  void*  pUsrData: [used for callback function]
 *  @param  -[in]  const char* szOperator: [Operator Name]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API HLrdaGroup LRDA_Group_Define(PFN_AsyncCallback pfnAsyncCallBack, void* pUsrData, const char* szOperator = 0);

/**
 *  Add Node/Tag/Field To Group.
 *
 *  @param  -[in]  HLrdaGroup  hGrp: [Group Handle]
 *  @param  -[in]  const char*  szTagName: [Tag Name]
 *  @param  -[in]  const char*  szFieldName: [Field Name]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API HLocalNTF LRDA_Group_AddNtf(HLrdaGroup hGrp, const char *szTagName, const char *szFieldName);

/**
 *  Add Ntf To Group.
 *  same as the upper one, 
 *    unless it returns whether tag.field is added.
 *
 *  @param  -[in]  HLrdaGroup  hGrp: [Group Handle]
 *  @param  -[in]  const char*  szTagName: [Tag Name]
 *  @param  -[in]  const char*  szFieldName: [Field Name]
 *  @param  -[out]  HLocalNTF*  hNtf: [hNtf for out]
 *  @return ICV_SUCCESS if add success
 *    EC_ICV_PDB_PB_ALREADY_EXIST if already added ntf
 *	  EC_ICV_PDB_INVALID_PARAMETER if given pointer is NULL
 *
 *  @version     08/06/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Group_AddNtf_Ex(HLrdaGroup hGrp, const char *szTagName, const char *szFieldName, HLocalNTF *hNtf);

//HLocalNTF LRDA_Group_AddNtf(HLrdaGroup hGrp, const char *szNode,
//			   const char *szTagName, const char *szFieldName);

/**
 *  Remove Node/Tag/Field From Group.
 *
 *  @param  -[in]  HLrdaGroup  hGrp: [Group Handle]
 *  @param  -[in]  HLocalNTF  hLocalNtf: [Local NTF Handle]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Group_RemoveNtf(HLrdaGroup hGrp, HLocalNTF hLocalNtf);

/**
 *  Lookup Group.
 *  Find Node with it's number and field with it's field index.
 *
 *  @param  -[in]  HLrdaGroup  hGroup: [Group Handle]
 *  @param  -[in]  long  nFast: [Fast Lookup Or Lookup whole Group, 
 *								 In Fast Lookup Mode, successfully 
 *								 lookuped ntf will not been lookuped]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Group_Lookup(HLrdaGroup hGroup, long nFast);

/**
 *  Read Group.
 *
 *  @param  -[in]  HLrdaGroup  hGroup: [Group Handle]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Group_Read(HLrdaGroup hGroup);

/**
 *  Get Float Value.
 *
 *  @param  -[in]  HLrdaGroup  hGroup: [Group Handle]
 *  @param  -[in]  HLocalNTF  hNtf: [Local NTF Handle]
 *  @param  -[in,out]  float&  fltVal: [float value]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Group_Ntf_Get_Float(HLrdaGroup hGroup, HLocalNTF hNtf, float &fltVal);

/**
 *  Get Ascii Value.
 *
 *  @param  -[in]  HLrdaGroup  hGroup: [Group Handle]
 *  @param  -[in]  HLocalNTF  hNtf: [Local NTF Handle]
 *  @param  -[in,out]  char*  szBuf: [buffer to store ascii data]
 *  @param  -[in]  size_t  usBufLen: [buffer length]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Group_Ntf_Get_Ascii(HLrdaGroup hGroup, HLocalNTF hNtf, char *szBuf, size_t usBufLen);

/**
 *  Get Double Value Of NTF.
 *
 *  @param  -[in]  HLrdaGroup  hGroup: [Group Handle]
 *  @param  -[in]  HLocalNTF  hNtf: [Local NTF Handle]
 *  @param  -[in,out]  double&  dblVal: [double value]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Group_Ntf_Get_Double(HLrdaGroup hGroup, HLocalNTF hNtf, double &dblVal);
/**
 *  Get Long Value Of NTF.
 *
 *  @param  -[in]  HLrdaGroup  hGroup: [Group Handle]
 *  @param  -[in]  HLocalNTF  hNtf: [Local NTF Handle]
 *  @param  -[in,out]  long&  nVal: [long value]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Group_Ntf_Get_long(HLrdaGroup hGroup, HLocalNTF hNtf, long &nVal);

/**
 *  (get value, time stamp and quality).
 *
 *  @param  -[in]  HLrdaGroup  hGroup: [group handle to lookup]
 *  @param  -[in]  HLocalNTF  hNtf: [assume to F_CV, field info is ignored, ONLY BlkType&BlkIndex is used]
 *  @param  -[in,out]  char*  szBuf: [data buffer]
 *  @param  -[out]  unsigned char&  usDataType: [out:datatype]
 *  @param  -[in,out]  unsigned short&  usBufLen: [in: sizeof(szBuf), out: data copied]
 *  @param  -[in,out]  timeval*  ptmStamp: [NULL if ignored]
 *  @param  -[in,out]  unsigned short*  pusQuality: [NULL if ignored]
 *  @return (ERROR CODE).
 *
 *  @version  09/06/2007  chenshengyu  Initial Version.
 */
LRDA_API long LRDA_Group_Ntf_Get_VTQ(HLrdaGroup hGroup, HLocalNTF hNtf, char *szBuf, unsigned char &usDataType, size_t &usBufLen, TCV_TimeStamp *ptmStamp, unsigned short *pusQuality);

/**
 *  Set Float Value Of NTF.
 *
 *  @param  -[in,out]  HLrdaGroup  hGrp: [Group Handle]
 *  @param  -[in,out]  HLocalNTF  hLocalNtf: [Local NTF Handle]
 *  @param  -[in,out]  float  fValue: [float value]
 *  @param  -[in,out]  bool  bNeedAck: [If Acknowledge Need]
 *  @param  -[in,out]  long&  nAckTicket: [Acknowledge Ticket]
 *  @param  -[in,out]  long  msTimeOut: [Timeout time in ms]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Group_Ntf_Set_Float(HLrdaGroup hGrp, HLocalNTF hLocalNtf,
							  float fValue, bool bNeedAck,
							  long *pnAckTicket, long msTimeOut);

/**
 *  Set Ascii Value Of NTF.
 *
 *  @param  -[in,out]  HLrdaGroup  hGrp: [Group Handle]
 *  @param  -[in,out]  HLocalNTF  hLocalNtf: [Local NTF Handle]
 *  @param  -[in,out]  const char*  szDataBuf: [data buffer]
 *  @param  -[in,out]  long  nDataBufLen: [data buffer length]
 *  @param  -[in,out]  bool  bNeedAck: [If Acknowledge Need]
 *  @param  -[in,out]  long&  nAckTicket: [Acknowledge Ticket]
 *  @param  -[in,out]  long  msTimeOut: [Timeout time in ms]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Group_Ntf_Set_Ascii(HLrdaGroup hGrp, HLocalNTF hLocalNtf,
							  const char *szDataBuf, long nDataBufLen,
							  bool bNeedAck, long *pnAckTicket, long msTimeOut);

/**
 *  Set Double Value Of NTF.
 *
 *  @param  -[in,out]  HLrdaGroup  hGroup: [Group Handle]
 *  @param  -[in,out]  HLocalNTF  hNtf: [Local NTF Handle]
 *  @param  -[in,out]  double  dblVal: [double value]
 *  @param  -[in,out]  bool  bNeedAck: [If Acknowledge Need]
 *  @param  -[in,out]  long&  nAckTicket: [Acknowledge Ticket]
 *  @param  -[in,out]  long  msTimeOut: [Timeout time in ms]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Group_Ntf_Set_Double(HLrdaGroup hGroup, HLocalNTF hNtf,
							   double dblVal, bool bNeedAck,
							   long *pnAckTicket, long msTimeOut);

/**
 *  Set Long Value Of  NTF.
 *
 *  @param  -[in,out]  HLrdaGroup  hGroup: [Group Handle]
 *  @param  -[in,out]  HLocalNTF  hNtf: [Local NTF Handle]
 *  @param  -[in,out]  long  nVal: [long value]
 *  @param  -[in,out]  bool  bNeedAck: [If Acknowledge Need]
 *  @param  -[in,out]  long&  nAckTicket: [Acknowledge Ticket]
 *  @param  -[in,out]  long  msTimeOut: [Timeout time in ms]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Group_Ntf_Set_long(HLrdaGroup hGroup, HLocalNTF hNtf,
							 long nVal, bool bNeedAck,
							 long *pnAckTicket, long msTimeOut);

/**
 *  Async Write Value To NTF.
 *
 *  @param  -[in]  const char*  szBlkName: [block name]
 *  @param  -[in]  const char*  szFldName: [field name]
 *  @param  -[in]  const char*  szDataBuf: [data buffer]
 *  @param  -[in]  long  nDataBufLen: [data buffer length]
 *  @param  -[in]  unsigned short  usDataType: [data type]
 *  @param  -[in]  bool  bNeedAck: [If Acknowledge Need]
 *  @param  -[in]  long&  nAckTicket: [Acknowledge Ticket]
 *  @param  -[in]  long  msTimeOut: [Timeout time in ms]
 *  @param  -[in]  PFN_AsyncCallback  pfnCallBack: [Call back function]
 *  @param  -[in]  void*  pUsrData: [callback function param]
 *  @param  -[in]  const char* szOperator: [operator]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Async_Write(const char *szBlkName, const char *szFldName, const char *szDataBuf, 
					  long nDataBufLen, unsigned char ucDataType, bool bNeedAck, 
					  long *pnAckTicket, long msTimeOut, PFN_AsyncCallback pfnCallBack, void *pUsrData, const char *szOperator);

LRDA_API long LRDA_Async_Write_Ex(const char *szBlkName, const char *szFldName, const char *szDataBuf, 
							   long nDataBufLen, unsigned char ucDataType, bool bNeedAck, 
							   long *pnAckTicket, long msTimeOut, PFN_AsyncCallback pfnCallBack, void *pUsrData, const char *szOperator, const char *szDesc);

LRDA_API long LRDA_Batch_Async_Write(unsigned long nBatchNum, NTFInfo *pInfoList, bool bNeedAck, long *pnAckTicket, long msTimeOut, 
								PFN_AsyncCallback pfnCallBack, void *pUsrData, const char *szOperator, const char *szDesc);

LRDA_API long LRDA_Async_Write_Rda(const char *szBlkName, const char *szFldName, const char *szDataBuf, 
	long nDataBufLen, unsigned char ucDataType, bool bNeedAck, 
	long *pnAckTicket, long msTimeOut, PFN_AsyncCallback pfnCallBack, void *pUsrData, const char *szOperator, const char *szDesc,
	const char *szAppName, ACE_Time_Value tmFireTime, ACE_Time_Value tmRdastubTime);

LRDA_API long LRDA_Batch_Async_Write_Rda(unsigned long nBatchNum, NTFInfo *pInfoList, bool bNeedAck, long *pnAckTicket, long msTimeOut, 
	PFN_AsyncCallback pfnCallBack, void *pUsrData, const char *szOperator, const char *szDesc,
	const char *szAppName, ACE_Time_Value tmFireTime, ACE_Time_Value tmRdastubTime);

/**
 *  Release Group.
 *
 *  @param  -[in]  HLrdaGroup  hGrp: [Group Handler]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API void LRDA_Group_Release(HLrdaGroup hGrp);

/**
 *  Get ProcessBlock List.
 *
 *  @return (EnumBlockResult Hanlde)
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API HEnumBlkResult LRDA_Enum_PB_Get_List();

/**
 *  Dump ProcessBlock List. pArrayBlkRec should allocated enough momery outside
 *
 *  @param  -[in,out]  HEnumBlkResult  hResult: [EnumBlockResult Hanlde]
 *  @param  -[in,out]  EnumBlkRec*  pArrayBlkRec: [EnumBlkRec Array]
 *  @param  -[in,out]  size_t  nSize: [size of pArrayBlk Aquired]
 *  @return size of EnumBlkRec Returned.
 *
 *  @version     07/29/2008  chenzhiquan  Initial Version.
 */
LRDA_API size_t LRDA_Enum_PB_Dump_List(HEnumBlkResult hResult, EnumBlkRec *pArrayBlkRec, size_t nSize);

/**
 *  Next ProcessBlock.
 *
 *  @param  -[in]  HEnumBlkResult  hResult: [EnumBlockResult Hanlde]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API const EnumBlkRec* LRDA_Enum_PB_Next(HEnumBlkResult hResult);

/**
 *  Get Size Of ProcessBlock List.
 *
 *  @param  -[in]  HEnumBlkResult  hResult: [EnumBlockResult Hanlde]
 *  @return (size of process block list)
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API size_t LRDA_Enum_PB_Size_Of_List(HEnumBlkResult hResult);

/**
 *  Free ProcessBlock List.
 *
 *  @param  -[in]  HEnumBlkResult  hResult: [EnumBlockResult Hanlde]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Enum_PB_Free_List(HEnumBlkResult hResult);

/**
 *  Get Field List of ProcessBlock Type.
 *
 *  @param  -[in]  unsigned char    ucBlkType: [Process Block Type]
 *  @return (Enum Field Result Hanlde)
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API HEnumFldResult LRDA_Enum_PB_Fld_Get_List_From_Type(unsigned char ucBlkType);


/**
 *  Dump ProcessBlockField.
 *
 *  @param  -[in,out]  HEnumFldResult  hResult: [comment]
 *  @param  -[in,out]  EnumFldRec*  pFldRecArray: [comment]
 *  @param  -[in,out]  size_t  nSize: [comment]
 *	@return Dumped Size of EnumFldRec
 *  @version     07/29/2008  chenzhiquan  Initial Version.
 */
LRDA_API size_t LRDA_Enum_PB_Fld_Dump_List(HEnumFldResult hResult, EnumFldRec *pFldRecArray, size_t nSize);

/**
 *  Get Field List of ProcessBlock.
 *
 *  @return (Enum Field Result Hanlde)
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API HEnumFldResult LRDA_Enum_PB_Fld_Get_List_From_Blk(const char* szBlkName);

/**
 *  Get Next Field Record.
 *
 *  @param  -[in,out]  HEnumFldResult  hResult: [Enum Field Result Hanlde]
 *  @return (Enum Field Record.)
 *
 *  @version     07/09/2008  chenzhiquan  Initial Version.
 */
LRDA_API const EnumFldRec* LRDA_Enum_PB_Fld_Next(HEnumFldResult hResult);

/**
 *  Get size of field list.
 *
 *  @param  -[in,out]  HEnumFldResult  hResult: [Enum Field Result Hanlde]
 *  @return size of Field List.
 *
 *  @version     07/09/2008  chenzhiquan  Initial Version.
 */
LRDA_API size_t LRDA_Enum_PB_Fld_Size_Of_List(HEnumFldResult hResult);

/**
 *  Free Field List.
 *  Must Be Called After finish enum. otherwize will cause memory leak.
 *
 *  @param  -[in,out]  HEnumFldResult  hResult: [Enum Field Result Hanlde]
 *  @return ERROR CODE.
 *
 *  @version     07/09/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Enum_PB_Fld_Free_List(HEnumFldResult hResult);


/**
 *  Get Redundance Status.
 *
 *  @param  -[in,out]  int*  pnStatus: [Status out]
 *  @return ICV_SUCCESS if success, else return ErrorCode.
 *
 *  @version     07/22/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Get_RM_Status(int *pnStatus);

/**
 *  $(Desp) register rm group.
 *  $(Detail) .
 *
 *  @return		LRDA_API long.
 *
 *  @version	4/10/2014  baoyuansong  Initial Version.
 */
LRDA_API long LRDA_RM_Force_Group_Lookup();

/**
 *  Sync Read Value To NTF.
 *
 *  @param  -[in]  const char*  szBlkName: [block name]
 *  @param  -[in]  const char*  szFldName: [field name]
 *  @param  -[out]  char*  szDataBuf: [data buffer]
 *  @param  -[in, out]  long  nDataBufLen: [data buffer length]
 *  @param  -[out]  unsigned short  *usDataType: [data type]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Sync_Read(const char *szBlkName, const char *szFldName, char *szDataBuf, 
					  unsigned short *usDataBufLen, unsigned char *pucDataType);

/**
 *  Sync Read Value To NTF.
 *
 *  @param  -[in]  const char*  szBlkName: [block name]
 *  @param  -[in]  const char*  szFldName: [field name]
 *  @param  -[out]  const char*  szDataBuf: [data buffer]
 *  @param  -[in, out]  long  nDataBufLen: [data buffer length]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
LRDA_API long LRDA_Sync_Read_Ascii(const char *szBlkName, 
								   const char *szFldName, 
								   char *szDataBuf, 
								   unsigned short *usDataBufLen);

// for information query
LRDA_API long LRDA_Query_AlarmArea_List(AlarmAreaInfo** ppAAList, long* plNumber);
LRDA_API void LRDA_Query_Free_AlarmArea_List(AlarmAreaInfo** ppResult);

LRDA_API long LRDA_Query_SubSystem_List(SubSystemInfo** ppSSList, long* plNumber);
LRDA_API void LRDA_Query_Free_SubSystem_List(SubSystemInfo** ppResult);

LRDA_API long LRDA_Query_ClassInfo_List(ClassInfo** ppClsList, long* plNumber);
LRDA_API void LRDA_Query_Free_Class_List(ClassInfo** ppResult);

//
LRDA_API HLocalNTF LRDA_Group_AddNtf2( HLrdaGroup hGrp, const char *szTagName, const char *szFieldName, unsigned long& nNtfPos );
LRDA_API long LRDA_Group_Ntf_Get_Ascii2( HLrdaGroup hGroup, HLocalNTF hNtf, char *szBuf, size_t usBufLen, unsigned long& nNtfPos );
LRDA_API long LRDA_Group_Ntf_Get_Double2(HLrdaGroup hGroup, HLocalNTF hNtf, double &dblVal, unsigned long& nNtfPos );
LRDA_API long LRDA_Group_Ntf_Get_long2(HLrdaGroup hGroup, HLocalNTF hNtf, long &nVal, unsigned long& nNtfPos);
LRDA_API long LRDA_Group_Ntf_Get_Float2(HLrdaGroup hGroup, HLocalNTF hNtf, float &fltVal, unsigned long& nNtfPos);

#endif // _LOCAL_REAL_TIME_DATA_HEADER_
