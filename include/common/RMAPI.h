/**************************************************************
 *  Filename:    RMAPI.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: RM API .
 *
 *  @author:     chenzhiquan
 *  @version     05/29/2008  chenzhiquan  Initial Version
**************************************************************/
#ifndef _RM_API_H_
#define _RM_API_H_

#include "common/RMAPIDef.h"

#ifdef _WIN32

#	ifdef cv6rmapi_EXPORTS
#		define RMAPI_DLLEXPORT extern "C" __declspec(dllexport)
#	else
#		define RMAPI_DLLEXPORT extern "C" __declspec(dllimport)
#	endif

#else //_WIN32

#	define RMAPI_DLLEXPORT extern "C"

#endif //_WIN32

// Initialize
RMAPI_DLLEXPORT long RMAPI_Init();

// Reinitialize
RMAPI_DLLEXPORT long RMAPI_ReInit();

/**
 *  Get RM Status.
 *  get ACTIVE/INACTIVE.
 *
 *  @param  -[in,out]  long*  pStatus: [RMStatus]
 *  @return ICV_SUCCESS if success.
 *
 *  @version     05/29/2008  chenzhiquan  Initial Version.
 */
RMAPI_DLLEXPORT long GetRMStatus(long* pnStatus);

/**
 *  Set Redundancy Status Manualy.
 *
 *  @param  -[in,out]  long  nStatus: [Status To Ctrl]
 *  @return ICV_SUCCESS if success.
 *
 *  @version     05/29/2008  chenzhiquan  Initial Version.
 */
RMAPI_DLLEXPORT long SetRMStatus(long nStatus);

//RMAPI_DLLEXPORT long RegRMStatusCallback(RMCallBack* pfCallBack);
/**
 *  Update object status.
 *
 *  @param  -[in]  long  nStatus: Object's status
 *  @param  -[in] const char *pszObjName:  Object Name.
 *  @return - 0 if success
 */
RMAPI_DLLEXPORT long SetObjStatus(const char *pszObjName, long nStatus);
/**
 *  Object need synchronize data or not.
 *
 *  @param  -[in] const char *pszObjName:  Object Name.
 *  @param  -[out]  bool *pnNeedSync: Need synchronize data or not
 *  @return - 0 if success
 */
RMAPI_DLLEXPORT long ObjNeedSync(const char *pszObjName, bool *pnNeedSync);
/**
 *  Object need do service or not.
 *
 *  @param  -[in] const char *pszObjName:  Object Name.
 *  @param  -[out]  bool *pnNeedDoSvc: Need do service or not
 *  @return - 0 if success
 */
RMAPI_DLLEXPORT long ObjNeedDoSvc(const char *pszObjName, bool *pnNeedDoSvc);
/**
 *  Get object RM info.
 *
 *  @param  -[in] const char *pszObjName:  Object Name.
 *  @param  -[out]  bool bNeedSync: Need synchronize data or not
 *  @param  -[out]  bool bPrim:	Current object is primer object or not
 *  @param  -[out] long bSingle: Current object runmode is single or not
 *  @return - 0 if success
 */
RMAPI_DLLEXPORT long GetObjRMInfo(const char *pszObjName, bool *bNeedSync, bool *bPrim, long *bSingle);
/**
 *  Get object RM status.
 *
 *  @param  -[in,out]  long  pnRMStatus: Object's RM status
 *  @param  -[in] const char *pszObjName:  Object Name.
 *  @return -0 if success
 */
RMAPI_DLLEXPORT long GetObjRMStatus(const char *pszObjName, long* pnRMStatus);

#endif// _RM_API_H_
