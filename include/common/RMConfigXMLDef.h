#ifndef _RM_CONFIG_XML_DEF_H_
#define _RM_CONFIG_XML_DEF_H_

// For RM Service Config.
// Node Names.
#define RM_SVC_CFG_XML_ROOT_NODE		ACE_TEXT("RMService")
#define RM_SVC_CFG_XML_STRATEGY_NODE	ACE_TEXT("Strategy")
#define RM_SVC_CFG_XML_INTERVAL_NODE	ACE_TEXT("Interval")
#define RM_SVC_CFG_XML_THRESHOLD_NODE	ACE_TEXT("Threshold")
#define RM_SVC_CFG_XML_PEER_COMM_NODE	ACE_TEXT("PeerCommunicator")
#define RM_SVC_CFG_XML_PORT_LIST_NODE	ACE_TEXT("PortList")
#define RM_SVC_CFG_XML_PORT_NODE		ACE_TEXT("Port")
#define RM_SVC_CFG_XML_PEER_LIST_NODE	ACE_TEXT("PeerList")
#define RM_SVC_CFG_XML_PEER_NODE		ACE_TEXT("Peer")
#define RM_SVC_CFG_XML_IP_NODE			ACE_TEXT("IP")
#define RM_SVC_CFG_XML_RMOBJECT_NODE	ACE_TEXT("RMObject")
#define RM_SVC_CFG_XML_PINGIP_NODE      ACE_TEXT("IP")
#define RM_SVC_CFG_XML_PINGTIMEOUT_NODE ACE_TEXT("Timeout")
#define RM_SVC_CFG_XML_PINGTHRESHOLD_NODE  ACE_TEXT("Threshold")
//#define RM_SVC_CFG_XML_TimeAfterPingSuccess_NODE ACE_TEXT("Interval")
#define RM_SVC_CFG_XML_PINGINTERVAL_NODE   ACE_TEXT("Interval")
#define RM_SVC_CFG_XML_PING_NODE        ACE_TEXT("Ping")
#define RM_SVC_CFG_XML_HEARTBEAT_STYLE  ACE_TEXT("heartbeatstyle")


//Attr Names.
#define RM_SVC_CFG_XML_SEQUENCE_ATTR	ACE_TEXT("sequence")
#define RM_SVC_CFG_XML_TYPE_ATTR		ACE_TEXT("type")
#define RM_SVC_CFG_XML_ID_ATTR			ACE_TEXT("id")
#define RM_SVC_CFG_XML_NAME_ATTR		ACE_TEXT("name")
#define RM_SVC_CFG_XML_PRIM_SEQ_ATTR	ACE_TEXT("primseq")
#define RM_SVC_CFG_XML_RUNMODE_ATTR		ACE_TEXT("single")
#define RM_SVC_CFG_XML_NEED_SYNC_ATTR	ACE_TEXT("needsync")
#define RM_SVC_CFG_XML_USEPING_ATTR     ACE_TEXT("UsePing")

// For RM DataSync Config.
//Node Names.
#define RM_DS_CFG_XML_ROOT_NODE			ACE_TEXT("RMDataSync")
#define RM_DS_CFG_XML_SQLITE_CP_DB_NODE	ACE_TEXT("SQLiteCopyDB")
#define RM_DS_CFG_XML_COPY_DB_NODE	ACE_TEXT("CopyDB")
#define RM_DS_CFG_XML_TABLE_NAME_NODE	ACE_TEXT("TableName")

// Attr Names.
#define RM_DS_CFG_XML_INTERVAL_ATTR		ACE_TEXT("interval")
#define RM_DS_CFG_XML_DB_NAME_ATTR		ACE_TEXT("dbname")

#define DEFAULT_RM_SVC_FILE_NAME		ACE_TEXT("RMService")
#define DEFAULT_RM_DS_FILE_NAME			ACE_TEXT("RMDataSync")

#endif// _RM_CONFIG_XML_DEF_H_
