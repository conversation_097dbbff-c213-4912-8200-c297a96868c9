/**************************************************************
 *  Filename:    HashMap.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Hash<PERSON>ap simplified usage of
 *    ACE_Hash_Map_Manager_Ex and defined the 
 *    string key..
 *
 *  @author:     chen<PERSON>quan
 *  @version     07/19/2007  chenzhiquan  Initial Version
**************************************************************/
#ifndef _HASH_MAP_H
#define _HASH_MAP_H

#include <ace/ACE.h>
#include <ace/RW_Mutex.h>
#include <ace/Null_Mutex.h>
#include <ace/Hash_Map_Manager.h>
#include <ace/Functor.h>
#include "common/stl_inc.h"

template<class EXT_ID, class INT_ID>
class Hash_Map :
      public ACE_Hash_Map_Manager_Ex<EXT_ID, INT_ID,
      ACE_Hash<EXT_ID>, ACE_Equal_To<EXT_ID>, ACE_RW_Mutex>
{
public:
	Hash_Map(size_t size = ACE_DEFAULT_MAP_SIZE) : ACE_Hash_Map_Manager_Ex<EXT_ID, INT_ID,
      ACE_Hash<EXT_ID>, ACE_Equal_To<EXT_ID>, ACE_RW_Mutex>(size)
	{

	}
	typedef ACE_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, ACE_Hash<EXT_ID>, ACE_Equal_To<EXT_ID>, ACE_RW_Mutex> _Iterator;
	typedef ACE_Hash_Map_Entry<EXT_ID, INT_ID> _Entry;
};

template<class EXT_ID, class INT_ID>
class Hash_Map_NoMutex:
      public ACE_Hash_Map_Manager_Ex<EXT_ID, INT_ID,
		  ACE_Hash<EXT_ID>, ACE_Equal_To<EXT_ID>, ACE_Null_Mutex>
	  {
public:
	Hash_Map_NoMutex(size_t size = ACE_DEFAULT_MAP_SIZE) : ACE_Hash_Map_Manager_Ex<EXT_ID, INT_ID,
		ACE_Hash<EXT_ID>, ACE_Equal_To<EXT_ID>, ACE_Null_Mutex>(size)
	{
		
	}

	typedef ACE_Hash_Map_Iterator_Ex<EXT_ID, INT_ID, ACE_Hash<EXT_ID>, ACE_Equal_To<EXT_ID>, ACE_Null_Mutex> _Iterator;
	typedef ACE_Hash_Map_Entry<EXT_ID, INT_ID> _Entry;
};

#if !defined(__MINGW32__)
ACE_BEGIN_VERSIONED_NAMESPACE_DECL

 //Specialize the hash functor.
//  template<>
//  class ACE_Hash<string>
//  {
//  public:
//    u_long operator() (const string kt) const
//    {
//  	  return ACE::hash_pjw(kt.c_str());
//    }
//  };
//  
//   //Specialize the equality functor.
//  template<>
//  class ACE_Equal_To<string>
//  {
//  public:
//    int operator() (const string& val1,
//                    const string& val2) const
//    {
//      return (val1 == val2);
//    }
//  };

ACE_END_VERSIONED_NAMESPACE_DECL

#endif //__MINGW32__
#endif//_HASH_MAP_H
