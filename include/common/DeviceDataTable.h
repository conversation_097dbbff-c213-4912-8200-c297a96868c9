#ifndef DEVICEDATATABLE_H
#define DEVICEDATATABLE_H

#include "common/DriverInfoArea.h"
#include "common/DIT.h"

#define INVALID_HANDLE NULL
typedef void * HANDLE;

DIT_EXPORT HANDLE Initialize(IN char *pDriverName);
DIT_EXPORT void UnInitialize(IN HANDLE hDeviceData);
DIT_EXPORT long GetDevicesConfig(IN HANDLE hDeviceData, OUT DEVICE_CONFIG *pDeviceConfig, OUT int *pDeviceCount);
DIT_EXPORT long GetDataBlocksOfDevice(IN HANDLE hDeviceData, IN const char *pDeviceName, OUT DATABLOCK_CONFIG *pDataBlockCfg, OUT int *pDataBlockNum);
DIT_EXPORT long GetDataBlockData(IN HANDLE hDeviceData, IN long nBlockNumber, IN long nByteOffset,
	IN long nBytesCount, OUT void* pData, OUT unsigned short *pnQuality, OUT TCV_TimeStamp *pTimeStamp,
	OUT int *pRequestCount, OUT int *pWriteControlCount);
DIT_EXPORT long GetDataBlockLength(IN HANDLE hDeviceData, IN long nBlockNumber, OUT long *pnBlockSize);

DIT_EXPORT long GetDataBlockOfDevice(IN HANDLE hDeviceData, IN const char *pDeviceName, IN const char *pBlockName,
	OUT DATABLOCK_CONFIG *pDataBlockCfg);
DIT_EXPORT long SetDataBlockData(IN HANDLE hDeviceData, IN const char *szDeviceName, IN const char *szBlkName, 
	IN long nByteOffset, IN long nBitOffset, IN long nBitCount, IN long lDataBufLen,  IN void* pData);

#endif 
