#ifndef OS_EVENT_H
#define OS_EVENT_H
#include "os.h"

#ifdef WIN32
#define os_event_wait(hEvent) WaitForSingleObject(hEvent, INFINITE)
#define os_event_signal(hEvent) SetEvent(hEvent)
#else
#define  os_event_wait(hEvent)		sem_wait(hEvent->pSema)
#define  os_event_signal(hEvent)	sem_post(hEvent->pSema)
#endif

/**
* @brief		init event
* @param [in]	pEvent event handle pointer
* @param [in]	szEventName event name
* @return		RD_SUCCESS if success, else EC_RD_EVENT_INIT;
* @version		2009/10/19 Huang Songxin Initial version
*/ 
OS_FUNEXPORT int32 os_event_init(EventHandle* pEvent, const char* szEventName);
/**
* @brief		release event
* @param [in]	pEvent event handle pointer
* @param [in]	szEventName event name
* @return		RD_SUCCESS if success, else EC_RD_EVENT_RELEASE;
* @version		2009/10/19 Huang Songxin Initial version
*/
OS_FUNEXPORT int32 os_event_destroy(EventHandle hEvent);

/**
* @brief		release event
* @param [in]	phEvent event handle pointer
* @param [in]	szEventName event name
* @return		RD_SUCCESS if success, else EC_RD_EVENT_RELEASE;
* @version		2009/10/19 Huang Songxin Initial version
*/

OS_FUNEXPORT int32 os_event_timedwait(EventHandle hEvent, int32 nSecTimeout, int32 nMSecTimeout);

#ifdef __hpux
OS_FUNEXPORT int32 os_thread_event_init(ThreadEventHandle* pEvent);

OS_FUNEXPORT int32 os_thread_event_timedwait(ThreadEventHandle hEvent, int32 nSecTimeout, int32 nMSecTimeout);

OS_FUNEXPORT int32 os_thread_event_signal(ThreadEventHandle hEvent);

OS_FUNEXPORT int32 os_thread_event_destroy(ThreadEventHandle hEvent);
#endif

#endif
