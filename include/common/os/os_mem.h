/** 
 * @file
 * @brief		memory mapping operation function
 * <AUTHOR>
 * @date		2009/10/21
 * @version		Initial Version
 *
 */
#ifndef OS_MEMORY_H
#define OS_MEMORY_H

#include "os.h"
#include "data_types.h"

// create memory file mapping
OS_FUNEXPORT int os_mem_filemap_create(HANDLE hFile, uint64 nSize, const char *szMappingName, FileMapHandle *pFileMap);
// Mapping a part of a file
OS_FUNEXPORT int os_mem_fileview_map(FileMapHandle hFileMapping, uint64 nOffsetPos, 
						  size_t nNumMapBytes, void** pMapView);
// unmapping the view
OS_FUNEXPORT int os_mem_fileview_unmap(void* pMapView, size_t nNumMapBytes);
// close file mapping
OS_FUNEXPORT int os_mem_filemap_close(FileMapHandle hFileMapping);

OS_FUNEXPORT int32 os_mem_filemap_flushview(void* pBaseAddr, int64 nBytes);


/**
* @brief		Create shared memory
* @param [in]	nSize size of shared memory
* @param [in]	szShmName shared memory name
* @return		shared memory handle if success; INVALID_HANDLE, if fail
* @version		2009/10/29 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int32 os_mem_sharemem_create(uint32 nSize, const char *szShmName, MemMapHandle *pHandle);

/**
* @brief		Map shared memory
* @param [in]	hMapping handle created by os_mem_sharemem_create
* @return		memory address if success; NULL if fail
* @version		2009/10/29 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int os_mem_sharemem_map(HANDLE hMapping, void** pShareMem);

/**
* @brief		Detach shared memory
* @param [in]	pShm shared memory address
* @return		RD_SUCCESS if success; error code if fail
* @version		2009/10/29 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int os_mem_sharemem_unmap(void* pShm);

/**
* @brief		Close shared memory
* @param [in]	hMapping handle created by os_mem_sharemem_create
* @return		RD_SUCCESS if success; error code if fail
* @version		2009/10/29 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int os_mem_sharemem_close(MemMapHandle hMapping);

#endif
