/**************************************************************
*  Filename:    os_process.h
*  Copyright:   Shanghai Baosight Software Co., Ltd.
*.
*  @author:     <PERSON><PERSON>
*  @version     09/29/2009  Initial Version
*  
*  Description: process functions.
**************************************************************/

#ifndef _PROCESS_H
#define	_PROCESS_H

#include "os.h"

OS_FUNEXPORT int32 os_process_spawn(const char *szFileName, const char *argv, ProcessHandle *pProcess);
OS_FUNEXPORT int32 os_process_wait(ProcessHandle hProcess);
OS_FUNEXPORT int32 os_process_terminate(ProcessHandle hProcess);
OS_FUNEXPORT int32 os_process_get_curr_id(void);
OS_FUNEXPORT int32 os_process_get_id_by_name(const char* szProcessName, int32* pProcessID);
#endif
