/** 
 * @file
 * @brief		page memory pool for memory segments
 * <AUTHOR>
 * @date		2010/05/13
 * @version		Initial Version
 *
 * 16 groups of memory segments with size 8, 16, 24, 32,...
 */
#ifndef MEM_SEG_H_
#define MEM_SEG_H_
#include "data_types.h"
#include "hd_system.h"
#include <stdlib.h>
#include <assert.h>
#ifndef  WIN32
#include <string.h>
#endif
#include "os_lock.h"
#include "os_time.h"
#include "definition/def_const.h"
#include "error_code.h"

#ifdef ALIGNMENT
#	define LEN_SEG_TYPE sizeof(long) 
typedef long SEG_TYPE;
#else
#   define LEN_SEG_TYPE sizeof(char)
typedef  char SEG_TYPE;
#endif

#define	MEM_SEG_TYPE_NUM		16			/*max memory segment size is 128 bytes */
#define MEM_SEG_INCREASE_SIZE	8
#define ROUND_UP(bytes) (((bytes)+7) & ~7)	/* make bytes can be divided by 8 */

#ifndef MEM_ALLOC_RETRY_TIME
	#define MEM_ALLOC_RETRY_TIME	500 /* millisecond */
#endif

typedef struct 
{
	char* PageStackTop;
	char* SegStackTop[MEM_SEG_TYPE_NUM];
	SPINLOCK lock;
	int32 UsedPageNum;
	int32 MaxPageNum;
}MemSegVar;

static inline HDHANDLE mem_seg_init(int32 nMemPoolSize)
{
	int32 i;
	int32 nRet;
	MemSegVar *pVar;
	
	pVar = (MemSegVar *)malloc(sizeof(MemSegVar));
	if (pVar == NULL)
		return NULL;

	nRet = os_spinlock_init(&pVar->lock);
	if (nRet != RD_SUCCESS)
	{
		free(pVar);
		return NULL;
	}

	for (i = 0; i < MEM_SEG_TYPE_NUM; i++)
	{
		pVar->SegStackTop[i] = NULL;
	}
	pVar->PageStackTop = NULL;
	pVar->UsedPageNum = 0;
	pVar->MaxPageNum = (BYTE_PER_MB / PAGE_SIZE) * nMemPoolSize;

	return (HDHANDLE)pVar;
}

static inline void mem_seg_fini(HDHANDLE hMemSeg)
{
	MemSegVar *pVar = (MemSegVar*)hMemSeg;
	if (pVar == NULL)
		return;
	os_spinlock_destroy(&pVar->lock);

	while (pVar->PageStackTop != NULL)
	{
		char* pPage = pVar->PageStackTop;
		memcpy(&pVar->PageStackTop, pPage, POINTER_SIZE);
		free(pPage);
	}

	free(pVar);
}

static inline char* mem_seg_alloc(HDHANDLE hMemSeg, int32 nSize)
{
	int32 nSegSize = ROUND_UP(nSize);
	SEG_TYPE nSegType = (SEG_TYPE)(nSegSize / MEM_SEG_INCREASE_SIZE - 1);
	char *pMem, *pPage;
	int32 nRemainSize;
	MemSegVar *pVar = (MemSegVar*)hMemSeg;
	SEG_TYPE nTmpSeg;
	assert(pVar != NULL);

	/* allocate large memory */
	if (nSize > MEM_SEG_TYPE_NUM * MEM_SEG_INCREASE_SIZE)
	{
		while (true) 
		{
			pMem = (char*)malloc(nSize + LEN_SEG_TYPE);
			if (pMem == NULL)
				os_sleep(MEM_ALLOC_RETRY_TIME);
			else
				break;
		}
		nTmpSeg = MEM_SEG_TYPE_NUM;
		memcpy(pMem, &nTmpSeg, sizeof(SEG_TYPE));
		pMem += LEN_SEG_TYPE;
		return pMem;
	}

	/* allocate small memory segment */
	os_spinlock_acquire(&pVar->lock);
	if (pVar->SegStackTop[nSegType] != NULL) /* if still has memory in segment pool */
	{
		pMem = pVar->SegStackTop[nSegType];
		memcpy(&pVar->SegStackTop[nSegType], pMem, POINTER_SIZE);
		os_spinlock_release(&pVar->lock);
		return pMem;
	}

	/* if no memory in segment pool */
	if (pVar->UsedPageNum >= pVar->MaxPageNum) /* if reach pool maximal size */
	{
		os_spinlock_release(&pVar->lock);
		while (true)
		{
			pMem = (char *)malloc(nSize + LEN_SEG_TYPE);
			if (pMem == NULL)
				os_sleep(MEM_ALLOC_RETRY_TIME);
			else
				break;
		}
		nTmpSeg = MEM_SEG_TYPE_NUM;
		memcpy(pMem, &nTmpSeg, sizeof(SEG_TYPE));
		pMem += LEN_SEG_TYPE;
		return pMem;
	}

	/* if no memory in segment pool and has not reach pool maximal size */
	nRemainSize = PAGE_SIZE - POINTER_SIZE;
	while (true)
	{
		pPage = (char *)malloc(PAGE_SIZE);
		if (pPage == NULL)
			os_sleep(MEM_ALLOC_RETRY_TIME);
		else
			break;
	}
	memcpy(pPage, &pVar->PageStackTop, POINTER_SIZE);
	pVar->PageStackTop = pPage;
	pPage += POINTER_SIZE;
	while (nRemainSize >= nSegSize + (int32)LEN_SEG_TYPE) /* divide page */
	{
		memcpy(pPage, &nSegType, sizeof(SEG_TYPE));
		pPage += LEN_SEG_TYPE;
		memcpy(pPage, &pVar->SegStackTop[nSegType], POINTER_SIZE);
		pVar->SegStackTop[nSegType] = pPage;
		pPage += nSegSize;
		nRemainSize -= (nSegSize + LEN_SEG_TYPE);
	}
	pVar->UsedPageNum ++;
	pMem = pVar->SegStackTop[nSegType];
	memcpy(&pVar->SegStackTop[nSegType], pMem, POINTER_SIZE);
	os_spinlock_release(&pVar->lock);
	return pMem;
}

static inline void mem_seg_free(HDHANDLE hMemSeg, char* pMem)
{
	SEG_TYPE nSegType;
	MemSegVar *pVar = (MemSegVar*)hMemSeg;
	assert(pVar != NULL);

	memcpy(&nSegType, pMem-LEN_SEG_TYPE, sizeof(SEG_TYPE));
	//nSegType = *(SEG_TYPE*)(pMem- LEN_SEG_TYPE);
	if (nSegType < MEM_SEG_TYPE_NUM)
	{
		os_spinlock_acquire(&pVar->lock);
		memcpy(pMem, &pVar->SegStackTop[nSegType], POINTER_SIZE);
		pVar->SegStackTop[nSegType] = (char*)(pMem);
		os_spinlock_release(&pVar->lock);
	}
	else
	{
		free(pMem - LEN_SEG_TYPE);
	}
}

#endif

