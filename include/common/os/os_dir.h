/** 
* @file
* @brief		directory operation function
* <AUTHOR>
* @date			2009/10/13
* @version		Initial Version
* Create directory, pass through files under the directory.
*/

#ifndef OS_DIR_H
#define OS_DIR_H

#include "os.h"

#define DIR_MAX_PATH_NAME_LEN	260

/**
* @brief		Find first file name under the directory, 
				using os_dir_find_next_file can get all files under a directory
* @param [in]	szPathName path name
* @param [out]	szFileName file name returned, this is only valid when function returns RD_SUCCESS
* @param [out]	pDir structure pointer which is useful when get next file
* @return		RD_SUCCESS, if success; error code if fail.
* @see			os_dir_find_next_file
* @version		2009/10/13 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT int32 os_dir_find_first_file(const char *szPathName, char *szFileName, OSDIR *pDir);

/**
* @brief		Find next file name under the directory
* @param [out]	szFileName file name returned, this is only valid when function returns RD_SUCCESS
* @param [in,out]	pDir structure pointer which is useful when get next file
* @return		RD_SUCCESS, if success; error if fail.
* @see			os_dir_find_first_file
* @note			this function must be called after calling os_dir_find_first_file
* @version		2009/10/13 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT int32 os_dir_find_next_file(char *szFileName, const OSDIR *pDir);

/**
* @brief		create directory
* @param [in]	szPathName the directory name
* @return		RD_SUCCESS if success; EC_RD_OS_DIR_CREATE, otherwise
* @version		2009/10/13 Chunfeng Shen initial version
*/ 
OS_FUNEXPORT int32 os_dir_create(const char *szPathName);

/**
* @brief		delete all files under directory
* @param [in]	szPathName the directory name
* @return		RD_SUCCESS, if success; Error code if fail
* @version		2009/10/13 Chunfeng Shen initial version
*/ 
OS_FUNEXPORT int32 os_dir_delete_files(const char * szPathName);

/**
* @brief		remove a directory
* @param [in]	szPathName the directory name
* @return		RD_SUCCESS, if success; EC_RD_OS_DIR_REMOVE if fail.
* @version		2009/10/13 Chunfeng Shen initial version
*/ 
OS_FUNEXPORT int32 os_dir_remove(const char * szPathName);

/**
* @brief		get current process path
* @param [in]	szPathName  the pathname buffer pointer
* @param [in]	nSize		the pathname buffer size
* @return		the real path name size
* @version		2009/10/29 Huang Songxin initial version
* @note			the pathname size usually is the max path size
*/
OS_FUNEXPORT int32 os_dir_get_curr_process_path(char* szPathName, int32 nSize);

#endif
