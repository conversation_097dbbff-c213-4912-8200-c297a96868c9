/**************************************************************
*  Filename:    thread.h
*  Copyright:   Shanghai Baosight Software Co., Ltd.
*.
*  @author:     <PERSON><PERSON> 
*  @version     02/20/2009  Initial Version
*   
*  Description: thread functions.
**************************************************************/

#ifndef _IHYPERDB_THREAD_H
#define _IHYPERDB_THREAD_H

#include "data_types.h"
#include "os.h"

/**
* @brief		Create a thread
* @param [out]	pThreadHandle thread handle which will be returned
* @param [in]	thrFunc Start address of a function that begins execution of a new thread
* @param [in]	pArg Argument list to be passed to a new thread or NULL
* @return		RD_SUCCESS, if success; EC_RD_THREAD_CREATE if fail.
* @version		2009/03/17 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT int os_thread_create(ThreadHandle *pThreadHandle, THR_FUNC thrFunc, void* pArg);

OS_FUNEXPORT int os_thread_create_with_threadid(ThreadHandle *pThreadHandle,
								   THR_FUNC thrFunc, 
								   void* pArg, ThreadID * pthreadID);
/**
* @brief		Wait a thread to terminate
* @param [in]	hThread thread handle
* @return		RD_SUCCESS, if success; EC_RD_THREAD_JOIN if fail.
* @version		2009/10/15 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT int os_thread_join(ThreadHandle hThread);

OS_FUNEXPORT int os_thread_join_with_msgwait(ThreadHandle hThread);

/**
* @brief		Test a thread is active or not
* @param [in]	hThread thread handle
* @param [out]	pActive true if the thread is active
* @return		RD_SUCCESS, if success; EC_RD_THREAD_TEST_ACTIVE if fail.
* @version		2009/10/16 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int os_thread_test_active(ThreadHandle hThread, bool *pActive);

OS_FUNEXPORT ThreadID os_thread_get_id(void);

OS_FUNEXPORT int os_thread_terminate(ThreadHandle hThread);
#endif
