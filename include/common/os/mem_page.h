/** 
 * @file
 * @brief		page memory pool
 * <AUTHOR>
 * @date		2010/05/13
 * @version		Initial Version
 *
 * Copyright: Shanghai Baosight Software Co., Ltd.
 */
#ifndef MEM_PAGE_H_
#define MEM_PAGE_H_

#include "data_types.h"
#include "hd_system.h"
#include "definition/def_const.h"
#include "stdlib.h"
#include "os_lock.h"
#include "os_time.h"
#include <string.h>

#ifndef MEM_ALLOC_RETRY_TIME
	#define MEM_ALLOC_RETRY_TIME	500 /* millisecond */
#endif

typedef struct 
{
	int32 nMaxPageNum;
	SPINLOCK lock;
	char* pTopPointer;
	int32 nPageNum;
}MemPageVar;

/**
 * @brief		initialize page memory pool
 * @param [in]	nPoolSize total size with unit of MB
 * @return		handle of memory page pool
 * @version		2010/05/13 Chunfeng Shen Initial version
*/
static inline HDHANDLE mem_page_init(int32 nPoolSize)
{
	int32 nRet;
	MemPageVar *pVar = (MemPageVar *)malloc(sizeof(MemPageVar));
	if (pVar == NULL)
	{
		return NULL;
	}
	nRet = os_spinlock_init(&pVar->lock);
	if (nRet != RD_SUCCESS)
	{
		free(pVar);
		return NULL;
	}

	pVar->nMaxPageNum = (nPoolSize) * (BYTE_PER_MB / PAGE_SIZE);
	pVar->nPageNum = 0;
	pVar->pTopPointer = NULL;

	return (HDHANDLE)pVar;
}

/**
 * @brief		free the page memory pool
 * @param [in]	hMemPoolPage handle of memory page pool
 * @version		2010/05/13 Chunfeng Shen Initial version
 */
static inline void mem_page_fini(HDHANDLE hMemPoolPage)
{
	MemPageVar *pVar = (MemPageVar*)hMemPoolPage;
	if (pVar == NULL)
	{
		return;
	}

	while (pVar->pTopPointer != NULL)
	{
		char* pPointer = pVar->pTopPointer;
		memcpy(&pVar->pTopPointer, pPointer, POINTER_SIZE);
		free(pPointer);
	}

	os_spinlock_destroy(&pVar->lock);
	free(pVar);
}

/**
 * @brief		allocate a memory page with size of page
 * @param [in]	hMemPoolPage handle of memory page pool
 * @return		pointer of a memory page
 * @version		2010/05/13 Chunfeng Shen Initial version
 */
static inline char* mem_page_alloc(HDHANDLE hMemPoolPage)
{
	char *pPage;
	MemPageVar *pVar = (MemPageVar*)hMemPoolPage;

	os_spinlock_acquire(&pVar->lock);
	if (pVar->pTopPointer != NULL)
	{
		pPage = pVar->pTopPointer;
		memcpy(&pVar->pTopPointer, pPage, POINTER_SIZE);
		os_spinlock_release(&pVar->lock);
	}
	else
	{
		pVar->nPageNum++;
		os_spinlock_release(&pVar->lock);
		do
		{
			pPage = (char*)malloc(PAGE_SIZE);
			if (pPage == NULL)
			{
				os_sleep(MEM_ALLOC_RETRY_TIME);
			}
		} while(pPage == NULL);
	}
	return pPage;
}

/**
 * @brief		free a memory segment with size of page
 * @param [in]	hMemPoolPage handle of memory page pool
 * @param [in]	pPage pointer of memory page
 * @version		2010/05/13 Chunfeng Shen Initial version
 */
static inline void mem_page_free(HDHANDLE hMemPoolPage, char* pPage)
{
	MemPageVar *pVar = (MemPageVar*)hMemPoolPage;

	os_spinlock_acquire(&pVar->lock);
	if (pVar->nPageNum <= pVar->nMaxPageNum)
	{
		memcpy(pPage, &pVar->pTopPointer, POINTER_SIZE);
		pVar->pTopPointer = pPage;
		os_spinlock_release(&pVar->lock);
	}
	else
	{
		pVar->nPageNum--;
		os_spinlock_release(&pVar->lock);
		free(pPage);
	}
}

#endif

