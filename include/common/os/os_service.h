/** 
 * @file
 * @brief		add service to operator system
 * <AUTHOR>
 * @date		2010/10/20
 * @version		Initial Version
 */
#ifndef _SERVICE_H_
#define _SERVICE_H_

#include "data_types.h"
#include "os.h"

/**
 * @brief		Install a service to operator system
 * @param [in]	szSvcName service name
 * @return		RD_SUCCESS, if success; error code if fail.
 * @version		2010/10/20 Chunfeng Shen Initial version
 */ 
OS_FUNEXPORT int32 service_install(const char *szSvcName);

/**
 * @brief		Remove a service from operator system
 * @param [in]	szSvcName service name
 * @return		RD_SUCCESS, if success; error code if fail.
 * @version		2010/10/20 Chunfeng Shen Initial version
 */ 
OS_FUNEXPORT int32 service_delete(const char *szSvcName);

/**
 * @brief		Begin a service
 * @param [in]	szSvcName service name
 * @return		RD_SUCCESS, if success; error code if fail.
 * @version		2010/10/20 Chunfeng Shen Initial version
 * @note			This function should be called in the front of main function
 */ 
OS_FUNEXPORT int32 service_begin(const char* szSvcName, int* pServiceExit);

/**
* @brief		End a service
* @version		2010/10/20 Chunfeng Shen Initial version
* @note			This function should be called at the back of main function
*/ 
OS_FUNEXPORT void service_end(void);

#endif
