/** 
* @file
* @brief		mutex, spin lock and semaphone
* <AUTHOR>
* @date			2009/10/15
* @version		Initial Version 
* Provide thread lock and process lock
*/
#ifndef OS_LOCK_H
#define OS_LOCK_H
#include "os.h"

// #define SPINLOCK_HAS_LOCK	1
// #define SPINLOCK_NO_LOCK	0

#ifdef _WIN32
	typedef HANDLE SemaHandle;
	#define os_mutex_acquire(hMutex)	EnterCriticalSection(hMutex)
	#define os_mutex_release(hMutex)	LeaveCriticalSection(hMutex)

	typedef CRITICAL_SECTION SPINLOCK;
// 	typedef char SpinlockHandle;
// 	#define os_spinlock_acquire__(pSpinLock, NO) {				\
// 		char *pSpinInfoLocalPointer = (pSpinLock);				\
// 		__asm{													\
// 			__asm mov ah, 0h									\
// 			__asm spinacquire##NO:	mov al, 1h					\
// 			__asm mov ebx, dword ptr[pSpinInfoLocalPointer]		\
// 			__asm lock cmpxchg byte ptr [ebx], ah				\
// 			__asm jz spinacquire_fini##NO						\
// 			__asm rep nop										\
// 			__asm jmp spinacquire##NO							\
// 			__asm spinacquire_fini##NO:							\
// 		}														\
// 	}
// 	#define os_spinlock_acquire_(pSpinLock, NO)	os_spinlock_acquire__(pSpinLock, NO)
// 	#define os_spinlock_acquire(pSpinLock)		os_spinlock_acquire_(pSpinLock, __LINE__)
// 	#define os_spinlock_release(pSpinLock) {					\
// 		char *pSpinInfoLocalPointer = (pSpinLock);				\
// 		__asm{													\
// 			__asm mov eax, dword ptr[pSpinInfoLocalPointer]		\
// 			__asm lock inc byte ptr[eax]						\
// 		}														\
// 	}

	typedef struct  
	{
		CRITICAL_SECTION lock;
		int nNumReadersHoldLock;	/* number of readers holding the lock */
		int nNumWaitWriters;		/* Number of waiting writers */
		int nNumWaitReaders;		/* Number of waiting readers */ 
		HANDLE hReadEvent;
		HANDLE hWriteEvent;
	}RwMutexInfo, *RwMutexHandle;
	typedef HANDLE ProcMutexHandle;
#else
#include <pthread.h>
#if defined(ARM_LINUX) || defined(__hpux)
typedef pthread_mutex_t* RwMutexHandle;
#else
typedef pthread_rwlock_t* RwMutexHandle;
#endif
	
	typedef int SemaHandle;
	typedef pthread_mutex_t ProcMutex;
	typedef struct  
	{
	   ProcMutex* pMutex;
	   pthread_mutexattr_t attr;
	   int shmid;
	}ProcMutexInfo, *ProcMutexHandle;
	#define os_mutex_acquire(pMutexInfo)	pthread_mutex_lock(pMutexInfo)
	#define os_mutex_release(pMutexInfo)	pthread_mutex_unlock(pMutexInfo)

	
#if defined(ARM_LINUX) || defined(__hpux)
	typedef pthread_mutex_t SPINLOCK;
#else
	typedef pthread_spinlock_t SPINLOCK;
#endif
// 	typedef char SpinlockHandle;
// 	void os_spinlock_acquire(SpinlockHandle *pSpinLock);
// 	void os_spinlock_release(SpinlockHandle *pSpinLock);
#endif

OS_FUNEXPORT int32 os_spinlock_init(SPINLOCK *pSpinLock);
OS_FUNEXPORT int32 os_spinlock_destroy(SPINLOCK *pSpinLock);
OS_FUNEXPORT int32 os_spinlock_acquire(SPINLOCK *pSpinLock);
OS_FUNEXPORT int32 os_spinlock_release(SPINLOCK *pSpinLock);

/**
* @brief		Initialize a mutex
* @param [in, out]	pMutexHandle	pointer of mutex info
* @return		RD_SUCCESS if success; error code, if fail
* @note			::os_mutex_destroy must be called after calling this function successfully
* @version		2009/10/15 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT int32 os_mutex_init(MutexHandle *pMutexHandle);

/**
* @brief		Destroy a mutex
* @param [in]	hMutex	mutex handle, it cannot be NULL
* @version		2009/10/15 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT void os_mutex_destroy(MutexHandle hMutex);

/**
* @brief		Initialize a read-write mutex
* @param [in, out]	pRwMutexHandle	pointer of rwmutex info
* @return		RD_SUCCESS if success, error code if fail
* @note			::os_rwmutex_destroy must be called after calling this function successfully
* @version		2009/10/19 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int32 os_rwmutex_init(RwMutexHandle *pRwMutexHandle);

/**
* @brief		Destroy a read-write mutex
* @param [in]	hRwMutex	pointer of rwmutex info which cannot be NULL
* @version		2009/10/19 Chunfeng Shen Initial version
*/
OS_FUNEXPORT void os_rwmutex_destroy(RwMutexHandle hRwMutex);

/**
* @brief		Acquire a read mutex
* @param [in]	hRwMutex pointer of mutex info
* @return		RD_SUCCESS, if success; error code if fail
* @version		2009/10/19 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int32 os_rwmutex_acquire_read(RwMutexHandle hRwMutex);

/**
* @brief		Acquire a write mutex
* @param [in]	hRwMutex	mutex handle
* @return		RD_SUCCESS, if success; error code if fail
* @version		2009/10/19 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int32 os_rwmutex_acquire_write(RwMutexHandle hRwMutex);

/**
* @brief		Release a read-write mutex
* @param [in]	hRwMutex	mutex handle
* @return		RD_SUCCESS if success, error code if fail
* @version		2009/10/19 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int32 os_rwmutex_release(RwMutexHandle hRwMutex);

/**
* @brief		Init a process semaphore
* @param [in]	szSemaName semaphore name
* @param [in]	nInitCount semaphore initial count
* @param [in,out]	pSemaHandle	semaphore handle pointer
* @return		RD_SUCCESS, if success; error code, otherwise
* @version		2009/11/07 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int32 os_proc_sema_init(const char* szSemaName, int32 nInitCount, SemaHandle *pSemaHandle);

/**
* @brief		destroy a process semaphore
* @param [in]	hSema	semaphore handle
* @return		RD_SUCCESS, if success; error code, otherwise
* @version		2009/11/07 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int32 os_proc_sema_destroy(SemaHandle hSema);

/**
* @brief		Acquire a process semaphore
* @param [in]	hSema	semaphore handle
* @return		RD_SUCCESS if success; EC_RD_SEMA_ACQUIRE, if fail
* @version		2009/11/07 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int32 os_proc_sema_acquire(SemaHandle hSema);

/**
* @brief		tryacquire a process semaphore
* @param [in]	hSema	semaphore handle
* @return		RD_SUCCESS if success; EC_RD_SEMA_TRYAQUIRE, if can not acquire the sema
* @version		2009/11/07 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int32 os_proc_sema_tryacquire(SemaHandle hSema);

/**
* @brief		release a process semaphore
* @param [in]	hSema	semaphore handle
* @return		RD_SUCCESS, if success; EC_RD_SEMA_RELEASE, if fail
* @version		2009/11/07 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int32 os_proc_sema_release(SemaHandle hSema);

OS_FUNEXPORT int32 os_proc_mutex_init(const char* szMutexName, ProcMutexHandle* phMutex);

OS_FUNEXPORT int32 os_proc_mutex_acquire(ProcMutexHandle hMutex);

OS_FUNEXPORT int32 os_proc_mutex_release(ProcMutexHandle hMutex);

OS_FUNEXPORT int32 os_proc_mutex_destroy(ProcMutexHandle hMutex);

#endif
