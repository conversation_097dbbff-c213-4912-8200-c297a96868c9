/**************************************************************
 *  Filename:    os.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  @author:     <PERSON><PERSON>
 *  @version     01/04/2009  shench<PERSON><PERSON>  Initial Version
                 06/12/2009  huangsongxin  version2
 *  
 *  Description: Define basic data structures for operator system
**************************************************************/
#ifndef OPERATE_SYSTEM_H
#define OPERATE_SYSTEM_H

#ifndef _LARGEFILE64_SOURCE
#define _LARGEFILE64_SOURCE
#endif

#include "data_types.h"

#ifndef OS_FUNEXPORT
	#ifdef _WIN32
		#ifdef OS_EXPORTS
			#define OS_EXPORTS_C __declspec(dllexport)
		#else
			#define OS_EXPORTS_C
		#endif
	#else
		#define OS_EXPORTS_C
	#endif
	#ifdef __cplusplus
		#define OS_FUNEXPORT extern "C" OS_EXPORTS_C
	#else
		#define OS_FUNEXPORT OS_EXPORTS_C
	#endif
#endif

#ifndef WIN32
	#ifndef SOCKET
		#define SOCKET int
	#endif
#else 
	#include "winsock2.h"
	#include "windows.h"
#endif

#ifdef WIN32
#	include <fcntl.h>
#else
#	include <stdio.h>
#	include <sys/types.h>
#	include <unistd.h>
#	include <sys/stat.h>
#	include <fcntl.h>
#	include <dirent.h>
#	include <semaphore.h>
#	include <sys/mman.h>
#	include <pthread.h>
#endif

#define MB_BYTE         1024*1024			//MB
#define GB_BYTE         1024*1024*1024		//GB
#define KB_BYTE			1024				//KB

 #ifndef NULL
 #	define NULL		((void*)0)
 #endif

/************************************************************************/
/*                             Windows                                  */
/************************************************************************/
#ifdef WIN32
	#define ThreadMutex CRITICAL_SECTION
	// file
#ifndef F_OK
	#define F_OK	0    //file existing
#endif
#ifndef W_OK
	#define W_OK	2	 //file write
#endif
#ifndef R_OK
	#define R_OK	4	 //file read
#endif
	
	// directory
	typedef struct OSDIR
	{
		HANDLE hFindFile;
	} OSDIR;

	// thread
	typedef DWORD ThreadID;
	typedef HANDLE ThreadHandle;
	typedef unsigned int (__stdcall *THR_FUNC)(void *);
	// process
	typedef PROCESS_INFORMATION ProcessHandle;
	// lock
	typedef CRITICAL_SECTION* MutexHandle;
	typedef HANDLE EventHandle;
	#define INVALID_EVENT_HANDLE (EventHandle)NULL
	// map
	typedef HANDLE FileMapHandle;
	typedef HANDLE MemMapHandle;
	#define INVALID_MEMMAP_HANDLE	(MemMapHandle)NULL

/************************************************************************/
/*                             Unix                                    */
/************************************************************************/
#else
	#ifndef INVALID_SOCKET
		#define INVALID_SOCKET	-1
	#endif
	typedef int HANDLE;
	#define  INVALID_HANDLE_VALUE (HANDLE)-1
	// directory
	typedef struct OSDIR
	{
		DIR* pDir;
	} OSDIR;
	// thread
	typedef pthread_t ThreadID;
	typedef pthread_t ThreadHandle;
	typedef void *(*THR_FUNC)(void*);
	// lock
	typedef pthread_mutex_t* MutexHandle;
	// event
	typedef struct
	{
		sem_t sema;
		sem_t *pSema;
		char* szName;
	}EventInfo, *EventHandle;
	#define INVALID_EVENT_HANDLE (EventHandle)NULL

	typedef struct
	{
		pthread_mutex_t  mutex;
		pthread_cond_t cond;
	}ThreadEventInfo, *ThreadEventHandle;
	#define INVALID_THREAD_EVENT_HANDLE (ThreadEventHandle)NULL
	// process
	typedef pid_t ProcessHandle;
	// file map
	typedef int FileMapHandle;
	typedef int MemMapHandle;
	#define INVALID_MEMMAP_HANDLE	(MemMapHandle)-1
#endif

#define INVALID_SEMA_HANDLE			(SemaHandle)0
#define INVALID_MUTEX_HANDLE		(MutexHandle)0
#define INVALID_RW_MUTEX_HANDLE		(RwMutexHandle)0
#define INVALID_FILEMAP_HANDLE		(FileMapHandle)0
#define INVALID_THREAD_HANDLE		(ThreadHandle)0
#define HD_OS_INVALID_FILE_HANDLE	(HANDLE)-1

#endif // OPERATE_SYSTEM_H

