/** 
 * @file
 * @brief		file operation function
 * <AUTHOR>
 * @date		2009/03/18
 * @version		Initial Version
 *
 * Create file, read and write file.
 */
#ifndef OS_FILE_H
#define OS_FILE_H

#include "os.h"
#include "data_types.h"

/**
* @brief		Open a file
* @param [in]	szFileName file name
* @param [in]	nMode O_RDONLY, O_WRONLY, O_RDWR; O_CREAT, O_TRUNC, O_EXCL
* @return		File handle, if success; INVALID_HANDLE if fail.
* @version		2009/03/17 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT int32 os_file_open(const char *szFileName, int32 nMode, HANDLE* pFile);

/**
* @brief		Close a file handle
* @param [in]	HANDLE hHandle: file handle
* @return		RD_SUCCESS, if success; EC_RD_OS_FILE_CLOSE, otherwise
* @version		2009/03/21 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int32 os_file_close(HANDLE hFile);

/**
* @brief		Judge whether the file exists,or whether the file can be read or written
* @param [in]	szFileName: file name
* @param [in]	nMode: R_OK | W_OK | F_OK(File Existing)
* @return		0 if the file has the given mode;
				-1 if the named file does not exist or does not have the given mode
* @version		2009/03/23 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT int32 os_file_access(const char* szFileName, int32 nMode);

/**
* @brief		Seek file pointer which is 64 bits
* @param [in]	hHandle file handle
* @param [in]	nOffset offset
* @param [in]	nRelative SEEK_SET | SEEK_CUR | SEEK_END
* @return		RD_SUCCESS if success; EC_RD_OS_FILE_SEEK, if fail
* @version		2009/03/17 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT int32 os_file_seek64(HANDLE hFile, int64 nOffset, uint32 nRelative);

/**
* @brief		write data to file
* @param [in]	hFile file handle
* @param [in]	pBuf data buffer pointer
* @param [in]	nByte number of written bytes
* @return		nBytes if success; -1, if fail
* @version		2009/03/21 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT int32 os_file_write(HANDLE hFile, const void *pBuf, int32 nBytesToWrite, int32 *pBytesWritten);

/**
* @brief		read file
* @param [in]	hHandle file handle
* @param [in]	pBuf data buffer pointer
* @param [in]	nBytes number of reading bytes
* @return		read bytes if success; -1, if fail
* @version		2009/03/21 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT int32 os_file_read(HANDLE hFile, void *pBuf, int32 nBytesToRead, int32 *pBytesRead);

/**
* @brief		seek and read file
* @param [in]	hHandle file handle
* @param [in]	nOffset offset
* @param [in]	pBuf data buffer pointer
* @param [in]	nBytes number of reading bytes
* @return		RD_SUCCESS, if success; EC_RD_OS_FILE_SEEK or EC_RD_OS_FILE_READ, if fail
* @note			file lock should be applied to this function
* @version		2009/07/08 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT int32 os_file_seek_read(HANDLE hFile, int64 nOffset, void* pBuf, int32 nBytesToRead, int32 *pBytesRead);

/**
* @brief		seek and write file
* @param [in]	hHandle file handle
* @param [in]	nOffset offset
* @param [in]	pBuf data buffer pointer
* @param [in]	nBytes number of writing bytes
* @return		RD_SUCCESS, if success; EC_RD_OS_FILE_SEEK or EC_RD_OS_FILE_WRITE, if fail
* @note			file lock should be applied to this function
* @version		2009/07/08 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT int32 os_file_seek_write(HANDLE hHandle, int64 nOffset, const void* pBuf, int32 nBytesToWrite, int32 *pBytesWritten);

/**
* @brief		Get File Size
* @param [in]	hFile file handle
* @param [out]	pnFileSize file size
* @return		RD_SUCCESS if success; EC_RD_OS_FILE_SEEK, otherwise
* @version		2009/10/13 Chunfeng Shen  Initial version  Create
*/ 
OS_FUNEXPORT int32 os_file_get_size(HANDLE hFile, int64* pnFileSize);

/**
* @brief		truncate the file from the end of file
* @param [in]	hFile handle of the file
* @param [in]	nOffset truncate the file from nOffset position
* @return		RD_SUCCESS, if success;other means fail.
* @note			file lock should be applied to this function
* @version		2009/10/13 Chunfeng Shen Initial version
*/
OS_FUNEXPORT int32 os_file_truncate(HANDLE hFile, int64 nOffset);

/**
* @brief		Flush file cache to disk
* @param [in]	hHandle file handle
* @return		RD_SUCCESS, if flush success; EC_RD_OS_FILE_FLUSH, otherwise
* @version		2009/04/09 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT int32 os_file_flush(HANDLE hFile);


// Get disk free space
OS_FUNEXPORT  int32 os_get_disk_free_space(const void* szPathName, int64 *pSize);
// Get atomic write bytes
OS_FUNEXPORT int32 os_get_atomic_write_bytes(void);

// get page size
OS_FUNEXPORT int32 os_get_page_size(void);

//get total physical memory size(Kbytes)
OS_FUNEXPORT uint32 os_get_total_memory_size(void);

OS_FUNEXPORT void os_delete_files_in_directory(const char * szPathName, const char* szFileType);

#endif
