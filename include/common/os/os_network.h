/** 
* @file
* @brief		network 
* <AUTHOR> 
* @date		2010/05/11
* @version		Initial Version
*
* 
*/
#ifndef _OS_NETWORK_H_
#define _OS_NETWORK_H_

#include "data_types.h"
#include "os/os.h"

#ifndef _WIN32
	#include <sys/socket.h>
	#include <sys/select.h>
#endif

#ifndef SD_RECEIVE
#	define SD_RECEIVE		0x00
#endif
#ifndef SD_SEND
#	define SD_SEND			0x01
#endif
#ifndef SD_BOTH
#	define SD_BOTH			0x02
#endif

struct timeval;

#ifdef _WIN32
struct fd_set;
#endif

//convert ip addr from char* to uint32
OS_FUNEXPORT int32 os_network_ip2int(const char* szIp, uint32 *ipint);

OS_FUNEXPORT int32 os_network_get_socket_ip(SOCKET sock, uint32 *pIP);


//convert ip addr from uint32 to char*
OS_FUNEXPORT int32 os_network_int2ip(uint32 ipin<PERSON>, char* szIp);

OS_FUNEXPORT uint64 os_network_generate_srvid(uint32 nIpV4, uint16 nPort);

OS_FUNEXPORT int32 os_network_startup(void);
OS_FUNEXPORT int32 os_network_clean(void);
OS_FUNEXPORT int32 os_network_connect(SOCKET sock, const struct sockaddr* pAddr, int32 nAddrLen);
OS_FUNEXPORT int32 os_network_connect_timeout(SOCKET sock, const struct sockaddr* pAddr, int32 nAddrLen, struct timeval *timeout);
OS_FUNEXPORT int32 os_network_send(SOCKET sock, const void* pBuf, int32 nLen, int32 nFlags, int32 *pSentBytes);
OS_FUNEXPORT int32 os_network_recv(SOCKET sock, void* pBuf, int32 nLen, int32 nFlags, int32 *pRecvBytes);
OS_FUNEXPORT int32 os_network_shutdown(SOCKET sock, int32 nHow);
OS_FUNEXPORT int32 os_network_closesocket(SOCKET sock);
OS_FUNEXPORT int32 os_network_select(int32 fds, fd_set *readfds, fd_set *writefds, fd_set *errorfds, int32 *sock_num, struct timeval *timeout);
OS_FUNEXPORT int32 os_network_setsock_blocking(SOCKET sock, bool bBlocking);
OS_FUNEXPORT int32 os_network_socket(int32 nDomain, int32 nType, int32 nProtocol, SOCKET *pSocket);
OS_FUNEXPORT int32 os_network_getsockopt(SOCKET s, int32 nLevel, int32 nOptName, char *pOptVal, int32 *pOptLen);
OS_FUNEXPORT int32 os_network_get_localip(uint32 **pIP, int32* pIPNum);
OS_FUNEXPORT void os_network_free_localip(uint32 *pIP);
OS_FUNEXPORT int32 os_network_get_local_mac_addr(char * szMacAddr);
OS_FUNEXPORT int32 os_network_get_host_by_name(char * szHostName, char *szIp);

#endif //_OS_NETWORK_H_
