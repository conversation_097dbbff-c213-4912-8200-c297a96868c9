#ifndef _CV_TIME_QUEUE_M_H_
#define _CV_TIME_QUEUE_M_H_

#include "ace/pre.h"
#include "ace/Timer_Wheel_T.h"
#include "ace/High_Res_Timer.h"
#include "ace/Event_Handler_Handle_Timeout_Upcall.h"

template <class TYPE, class FUNCTOR, class ACE_LOCK>
class CV_Timer_Queue_T : public ACE_Timer_Wheel_T<TYPE, FUNCTOR, ACE_LOCK>
{
protected:

public:
	// spoke_count 1000 as default, 50ms resolution as default
	CV_Timer_Queue_T(u_int spoke_count = 1000,
		u_int resolution = 50) : ACE_Timer_Wheel_T<TYPE, FUNCTOR, ACE_LOCK>(spoke_count, resolution)
	{
		//ACE_High_Res_Timer::global_scale_factor();
		this->gettimeofday(ACE_High_Res_Timer::gettimeofday_hr);
	}
	virtual ~CV_Timer_Queue_T() // add virtual destructor for avoiding memory leak when deleting pointer of base class
	{

	}
};

typedef ACE_Event_Handler_Handle_Timeout_Upcall 
	    Upcall;

typedef CV_Timer_Queue_T<ACE_Event_Handler *,
	Upcall,
	ACE_SYNCH_RECURSIVE_MUTEX>
	CV_Timer_Queue;


#endif//_CV_TIME_QUEUE_M_H_

