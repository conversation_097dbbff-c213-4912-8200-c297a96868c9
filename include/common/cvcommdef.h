#ifndef _COMMLIB_CVCOMM_DEF_H_
#define _COMMLIB_CVCOMM_DEF_H_

#include "common/cvdefine.h"

#ifndef _WIN32
#include "sys/types.h"
#endif

////////////////////���º궨�������CVCommLib��ʹ�õĺ궨��, �������ú궨���Ѿ�����cvdefine.h //////////////////////
#define ICV_PROCMGR_DEFAULT_TCPPORT		55004
#define ICV_DRVCTRL_DEFAULT_TCPPORT	    55007
#define ICV_RMSVC_DEFAULT_UDPPORT		55009
#define ICV_DRACQUISITIONSERVICE_DEFAULT_TCPPORT	55014

#define ICV_HD3CONFIGCENTER_EXE_NAME  "hd3ConfigCenter"
#define ICV_HDNETMGR_EXE_NAME  "hdNetMgr"



#define ICV_IS_CENTER_SERVER			1	// ���ķ����־
#define ICV_IS_SUBCENTER_SERVER			0	// �����ķ����־
#define ICV_IS_UNKNOW_SERVER			-1	// δ֪�����־

// iCV������������ӦiCV��Executable
#define ICV_EXEC_PATH					"DR_ROOT"
// iCV������������ӦiCV�ĸ�Ŀ¼
#define ICV_HOME_PATH					"DR_HOME"
// dsf-redis path 
#define DSF_REDIS_PATH					"dsf-redis"
// iCV����������unix������ʹ�ã�ָ����ǰ���еĹ����ļ�Ŀ¼
#define ICV_PROJECT_PATH				"DR_PROJECT_PATH"
// iCV ��unix�µ�ȱʡ ICV_HOME
#define ICV_UNIX_DEFAULT_HOME_PATH		"/icv"

#define ICV_EXE_DIR_NAME				"executable"
#define ICV_RUNTIME_DATA_DIR_NAME		"rtd"
#define ICV_TEMP_DATA_DIR_NAME			"temp"
#define ICV_PROJECTS_DIR_NAME			"projects"
#define ICV_CONFIG_DIR_NAME				"config"
#define ICV_PROJ_DATA_DIR_NAME			"data"
#define ICV_LOG_DIR_NAME				"log"
#define ICV_PROJ_DATA_HTD_DIR_NAME		"htd"


// ע����ָ����ǰ���еĹ�����
#define ICV_PROJECTS_REGISTRY_KEY	"SOFTWARE\\iCentroView\\Projects"
#define ICV_CUR_PROJECT_VALUE_NAME  "CurProject"

// ����������ļ���
#define ICV_ACTIVE_PROJ_CONFIG_FILE_NAME	"ActiveProject.xml"
#define ICV_XML_DEFINE_ACTIVE_PROJECT		"ActiveProject"
#define ICV_XML_DEFINE_PROJECT_PATH		"ProjectPath"

// iCV������ʱ�ļ����Ŀ¼����ָ���û�������ʱʹ��$(ICV_ROOT)/RTDĿ¼
#define ICV_RTD_PATH					"ICV_RTD_PATH"

// project.cvpro���������Ϣ����ĺ�
#define DEFAULT_PROJ_CONFIG_FILE_NAME		"project.cvpro"
#define ICV_XML_DEFINE_PROJ					"Project"
#define ICV_XML_DEFINE_LOGPATH				"logpath"
#define ICV_XML_DEFINE_HTDPATH				"htdpath"

// SCADALst.xml���������Ϣ����ĺ�
#define DEFAULT_SCADALST_FILE_NAME				"SCADALst.xml"
#define iCV_XML_DEFINE_SCADALST					"scadalist"
#define iCV_XML_DEFINE_SCADALST_SCADA			"scada"
#define iCV_XML_DEFINE_SCADALST_SCADA_ISCENTER	"iscenter"
#define iCV_XML_DEFINE_SCADALST_SCADA_MAINIP	"mainip"
#define iCV_XML_DEFINE_SCADALST_SCADA_BAKEIP	"bakeip"
#define iCV_XML_DEFINE_SCADALST_VERSION			"version"
#define iCV_XML_DEFINE_SCADALST_SCADA_IPLIST    "iplist"
#define iCV_XML_DEFINE_SCADALST_SCADA_IPLIST_IP		"ip"
#define iCV_XML_DEFINE_SCADALST_SCADA_IPLIST_CURIP	"curip"

// Port.xml���������Ϣ�� 
#define iCV_SERVICE_DEFAULT_PORT				0
#define DEFAULT_PORT_FILE_NAME					"Port.xml"
#define iCV_XML_DEFINE_PORT						"Port"
#define iCV_XML_DEFINE_SERVICE					"Service"
#define iCV_XML_DEFINE_NAME						"name"

// SCADACfg.xml���������Ϣ�� 
#define DEFAULT_SCADACFG_FILE_NAME				"DrVCtrlCfg.xml"
#define iCV_XML_DEFINE_DSF					"DSF"
#define ICV_NIL_RM_SEQUENCE						-1
#define ICV_MAIN_RM_SEQUENCE					0

// �������������ļ�
#define DEFAULT_ALMCATEGORY_FILE_NAME			"AlarmCategoryCfg.csv"

// HMICfg.xml���������Ϣ�� 
#define HMICFG_DEFAULT_FILENAME					"HMICfg.xml"
#define HMICFG_NODENAME_SCADATOACCESS			"scadatoaccess"
#define HMICFG_NODENAME_SCADA					"scada"
#define CONTROL_CFG_FILENAME					"controlcfg.xml"

//DeviceLst.xml��غ�
#define DEFAULT_DEVICELST_FILE_NAME					"DeviceLst.xml"
#define iCV_XML_DEFINE_DEVICELST_DRIVERS	"drivers"
#define iCV_XML_DEFINE_DEVICELST_DRIVER	"driver"
#define iCV_XML_DEFINE_DEVICELST_DEVICE	"device"
#define iCV_XML_DEFINE_DEVICELST_NAME	"name"
#define iCV_XML_DEFINE_DEVICELST_DESC	"desc"

//DSF
#define DSF_XML_DEFINE_DATA_DEFINITION	"DataDefinition.xml"
#define DSF_XML_DEFINE_NODE_CONFIG	"DSFNodeConfig.xml"
#define DSF_XML_DEFINE_LOCAL_VARIABLES	"LocalVariables.xml"
#define DSF_XML_DEFINE_MODELDEFINITION	"ModelDefinition.xml"

typedef struct tagAM_IPAddr 
{
	char szIPAddr[ICV_HOSTNAMESTRING_MAXLEN+1];			/**<  IP��ַ.  */
	void* hQueue;
	long lConnStatus;
}AM_IPAddr;

typedef struct tag_AM_Node 
{
	char szIPAddr[ICV_HOSTNAMESTRING_MAXLEN];			//����IP��ַ. 
	char szBakIPAddr[ICV_HOSTNAMESTRING_MAXLEN];		//����IP��ַ.
	long lNodeType;							//1 or 0
	long lConnStatus;						//1 or 0
	long lBakConnStatus;					//1 or 0
	long lLastModifiedTime;
}AM_Node;

typedef struct tagHMINODEINFO
{
	char szHMIName[ICV_NODENAME_MAXLEN];
	char szScadaName[ICV_NODENAME_MAXLEN];
	char szMainScadaIP[ICV_HOSTNAMESTRING_MAXLEN];
	char szBakeScadaIP[ICV_HOSTNAMESTRING_MAXLEN];
}HMINODEINFO;

typedef struct controlTimeOut
{
	bool bT1Switch;
	long lT1TimeOut;
	bool bT2Switch;
	long lT2TimeOut;
}CONTROLTIMEOUT;

#define OEM_VERSION_FILE		"\\config\\oem_version.xml"
#define IHD_DEFAULT_IP			"127.0.0.1"
#define IHD_DEFAULT_PORT		5678
#endif//_COMMLIB_CVCOMM_DEF_H_
