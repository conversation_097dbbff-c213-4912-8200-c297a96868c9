#ifndef _COMMON_TYPE_CAST_H_
#define _COMMON_TYPE_CAST_H_

#include "common/cvcomm.hxx"
#include "common/cv_datatype.h"
#include "common/CV_Time.h"

#define PBFIELD_INVALID_VALUE	(unsigned short)-1

namespace cvcommon
{

//CVCOMM_API unsigned short dataTypeSize[];
extern CVCOMM_API unsigned short dataTypeSize[];
/**
*  (copy a string with edge check).
*  (Detail).
*
*  @param  -[in,out]  char*  szDest: [dest]
*  @param  -[in,out]  const char*  szSrc: [source string]
*  @param  -[in,out]  long  nMaxDestLen: [including the trailing len]
*  @return (Return).
*
*  @version  03/28/2007  chenshengyu  Initial Version.
*/
CVCOMM_API long Safe_StrNCopy(char * szDest, const char * szSource, size_t nDestBuffLen);

/**
 *  Cast TimeStamp to Buffer.
 *
 *  @param  -[in,out]  ACE_TCHAR  date_and_time[]: [buffer to out]
 *  @param  -[in]  int  date_and_timelen: [length of buffer]
 *  @param  -[in]  ACE_Time_Value  cur_time: [time value]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
CVCOMM_API char *	CastTimeToASCII (char date_and_time[],	int date_and_timelen, const TCV_TimeStamp &cur_time);

/**
 *  Cast Type To ASCII* Buffer.
 *
 *  @param  -[in,out]  string&  str: [string]
 *  @param  -[in,out]  const char*  szSrcData: [source buffer]
 *  @param  -[in,out]  char  cSrcDataType: [src data type]
 *
 *  @version     07/09/2008  chenzhiquan  Initial Version.
 */
CVCOMM_API long		CastCVTypeToASCII(const char *szSrcData, size_t nSrcSize, unsigned char cDataType, char *szDest, size_t nDestSize);

CVCOMM_API const char * GetCtrlCmdResultDesc( long nResult );

CVCOMM_API const char * GetQualityDesc(unsigned short usQuality);

CVCOMM_API const char * GetQualitySubStatusDesc(unsigned short usQuality);

CVCOMM_API const char * GetQualitySubStatusDescEx(unsigned short usQuality);

CVCOMM_API unsigned int HexDumpBuf( const unsigned char *szBuffer, unsigned int nBuffeLen, char *szHexBuf, unsigned int *pnHexBufLen );
}

#define Safe_CopyString(x, y, z)	cvcommon::Safe_StrNCopy(x, y, z)
#define CastTime2Buffer(x, y, z)	cvcommon::CastTimeToASCII(x,y,z)
#define CastTypeToASCII(a,b,c,d,e)	cvcommon::CastCVTypeToASCII(a,b,c,d,e)
#define g_DTSize	cvcommon::dataTypeSize

#endif //_COMMON_TYPE_CAST_H_

