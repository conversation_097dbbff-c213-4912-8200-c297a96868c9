
#ifndef _SIMPLE_THREAD_QUEUE_H_
#define _SIMPLE_THREAD_QUEUE_H_

#include <list>
#include "errcode/error_code.h"
#include <boost/thread/mutex.hpp>
#include <boost/thread/condition.hpp>
#include "CV_Time.h"

// #ifdef WIN32
// #	if defined(ETIME) && (ETIME != ERROR_SEM_TIMEOUT)
// #		undef ETIME
// #	endif
// #   if !defined (ETIME)
// #     define ETIME                  ERROR_SEM_TIMEOUT
// #   endif /* !ETIME */
// #endif/*WIN32*/

template<class Type>
class CSimpleThreadQueue
{
public:
	typedef typename std::list<Type>::iterator QueuePosition;

public:
	CSimpleThreadQueue() : m_size(0) {};
	  ~CSimpleThreadQueue(){};
	  
	// Enqueue Item
	int enqueue(Type item);

	// Dequeue Item.
	int dequeue(Type &item, TCV_TimeStamp *tv = 0);

	// Enqueue Head
	int enqueue_head(Type item);

	// Return Queue Size
	size_t size();
protected:
	std::list<Type> m_list;		// List To Store Obj.
	boost::mutex m_mutex; // Thread Mutex
	boost::condition m_cond;
	//ACE_Thread_Condition<ACE_Thread_Mutex> m_cond; // Thread Condition
	//ACE_Atomic_Op<ACE_Thread_Mutex, size_t> m_size;
	long m_size;
};


template<class Type>
int CSimpleThreadQueue<Type>::dequeue(Type &item, TCV_TimeStamp *tv)
{
	boost::mutex::scoped_lock sl(m_mutex);
	if (! m_list.empty() )
	{
		item = m_list.front();
		m_list.pop_front();
		-- m_size;
		return ICV_SUCCESS;
	}

	m_cond.wait(m_mutex);

	if (! m_list.empty() )
	{
		item = m_list.front();
		m_list.pop_front();
		-- m_size;
		return ICV_SUCCESS;
	}
	else
	{
		return -1;
	}
	
	return ICV_SUCCESS;
}


template<class Type>
int CSimpleThreadQueue<Type>::enqueue_head(Type item)
{
	boost::mutex::scoped_lock sl(m_mutex);
	m_list.push_front(item);
	++ m_size;
	m_cond.notify_one();

	return ICV_SUCCESS;
}



template<class Type>
int CSimpleThreadQueue<Type>::enqueue(Type item)
{
	boost::mutex::scoped_lock sl(m_mutex);
	m_list.push_back(item);
	++ m_size;
	m_cond.notify_one();
	return ICV_SUCCESS;
}


template<class Type>
size_t CSimpleThreadQueue<Type>::size()
{
//	size_t nSize;
//	mutex_.acquire();
//	nSize = list_.size();
//	mutex_.release();
//	return size();
	//return m_size.value(); //m_list.size();
	return m_size;
}

#endif//_SIMPLE_THREAD_QUEUE_H_
