// @doc Quality
//		
// @module Quality.h - OPC Data Quality Bit definitions |
//
// This file contains the definition of the OPC Data Quality Bits.
// It is for use in the 7.0 drivers.
//

//	(C) COPYRIGHT 1996 INTELLUTION INC. ALL RIGHTS RESERVED
//
//	This software contains information which represents trade secrets of 
//	Intellution and may not be copied or disclosed to others except as 
//	provided in the	license with Intellution.
//
// Maintenance
//	Version	  Date		Who		What
//	-------	--------	---		------
//	7.0		07/25/97	jra		Created
//

#ifndef QUALITY_H
#define QUALITY_H	1

// WORD quality - Used to set the entire quality to bad, for example.
#define	WORD_QUALITY_BAD		0x00	// 00000000
#define QUALITY_IS_GOOD(qua_stat)	(((qua_stat)>>6) == 3)

#define	WORD_QUALITY_GOOD		0xC0	// 11000000
#define	WORD_QUALITY_UNCERTAIN	0x40	// 01000000

#if defined(__hpux) || defined(_hpux) || defined(hpux) || defined(_AIX) || defined(__sun) || defined(sun)
//#define	WORD_QUALITY_GOOD		0xC000	// 11000000000000000
//#define	WORD_QUALITY_UNCERTAIN	0x4000	// 01000000000000000
#pragma pack(1)
typedef struct
{
	unsigned short nLeftOver  : 8;
	unsigned short nQuality   : 2;		// 0..3   (2 bits)
	unsigned short nSubStatus : 4;		// 0..15  (4 bits)
	unsigned short nLimit     : 2;		// 0..3   (2 bits)
} QUALITY_STATE;
#pragma pack()

#else

// definition of the OPC Quality State
typedef struct
{
    unsigned short nLimit     : 2;		// 0..3   (2 bits)
    unsigned short nSubStatus : 4;		// 0..15  (4 bits)
    unsigned short nQuality   : 2;		// 0..3   (2 bits)
	unsigned short nLeftOver  : 8;
} QUALITY_STATE;
#endif

// Definition of Quality Values
#define QUALITY_BAD				0
#define QUALITY_UNCERTAIN		1
#define QUALITY_GOOD			3

// Substatus for BAD, ��OPC�е�Substatusһ��
#define SS_NON_SPECIFIC			0		// No specific reason
#define SS_CONFIG_ERROR			1		// ���ô���,                       Configuration Error
#define SS_NOT_CONNECTED		2		// δ��������Դ,                   Input not connected
#define SS_DEVICE_FAILURE		3		// �豸����,                       Protocol Error cause of Failure
#define SS_SENSOR_FAILURE		4		// ����������,                     Sensor failure
#define SS_LAST_KNOWN			5		// ͨ�Ź��ϣ��ṩ���һ����Чֵ,   Latched data
#define SS_COMM_FAILURE			6		// ͨ�Ź��ϣ����ṩ���һ����Чֵ, Time-out Error cause of Failure
#define SS_OUT_OF_SERVICE		7		// ���ڷ���Χ��,                 Out of Service 
#define SS_SCAN_UNSUPPORT		8		// ao,do do not scan
#define SS_SUBSCRIBE_TAG_TOOMANY	9	// 向PLC订阅的点太多了

// SubstatusEx for BAD, nLeftOver�ֶΣ�nQuality=QUALITY_BADʱ��Ч
// ���ֶ�Ϊ0ʱ����OPC�е�Substatusһ�£���0�����iCV��չ�Ĵ�����Ϣ
// ͨ����Ϣ
#define CV_LV_NO_SPECIFIC						0	//������״̬
// �����жϵķ�֧, 000111XX
#define CV_LV_IO_NUMBER_LIMIT					1	//IO��������,   IO Number Exceed Limit
#define CV_LV_SCHEDULE_FAILURE					2	//��������ʧ��, Schedule Block Failed
#define CV_LV_SCAN_DISABLED						3	//��ֹɨ��, scan disabled
// ���ô���ķ�֧, 000001XX
#define CV_LV_DRIVERNAME_EMPTY					1   //������Ϊ��
#define CV_LV_DRIVER_NOTFOUND					2	//�Ҳ�����Ӧ����
#define CV_LV_ADDRESSLEN_TOOLONG				3	//IO��ַ���ȹ���
#define CV_LV_ADDRESS_BEGINFROMDIGITAL   		4	//�Ƿ�����:�����ֿ�ͷ
#define CV_LV_ADDRESS_SEGNUM_TOOSMALL   		5	//��ַ�������󣬲�ֳ��ĵ�ַ�β���
#define CV_LV_ADDRESS_SEGNUM_TOOMUCH			6	//��ַ�������󣬲�ֳ��ĵ�ַ�ι���
#define CV_LV_DEVICENAME_EMPTY   				7	//�豸��Ϊ��
#define CV_LV_IOADDRESS_EMPTY   				8	//�豸��ַΪ��
#define CV_LV_IOADDRESS_EXCEEDLIMIT				9	//�豸��ַ������Χ
#define CV_LV_IOADDRESS_BITOFFSET_NOTEMPTY		10	//λƫ������Ч
#define CV_LV_IOADDRESS_DIGITALTAG_NOBITOFFSET	11	//ȱ��λƫ����
#define CV_LA_IOADDRESS_BITOFFSET_EXCEEDLIMIT	12 // ������ȱ��λƫ����
#define CV_LV_IOADDRESS_BYTEOFFSET_EXCEEDLIMIT	13	//�ֽ�ƫ������Ч
#define CV_LV_IOADDRESS_DATALENGTH_TOOSMALL		14	//��ַ�����������ݿ鳤�ȹ�С
#define CV_LV_IOADDRESS_DATALENGTH_TOOMUCH		15	//��ַ�����������ݿ鳤�ȹ���
#define CV_LV_DEVICE_NOTFOUND					16	//�Ҳ�����Ӧ�豸
#define CV_LV_DATABLOCK_NOTFOUND				17	//�Ҳ�����Ӧ���ݿ�
#define CV_LV_RELATIONALBLK_NOTFOUND			18	//�Ҳ��������ı���	
#define CV_LV_CA_EXPRESSION_INVALID				19	//�Ƿ���CA����ʽ
// �豸���ϵķ�֧, 000011XX
#define CV_LV_RELATIONALBLK_QUALITYBAD			1	//��������������ΪBAD
#define CV_LV_CA_CALC_FAILURE					2	//����ʽ����ʧ��


// Substatus for UNCERTAIN
#define SS_NON_SPECIFIC			0		// No specific reason 
#define SS_LAST_USABLE			1		// Last usable value
#define	SS_SENSOR_NOT_ACCURATE	4		// Sensor not accurate
#define SS_EGU_UNITS_EXCEEDED	5		// Engineering Units exceeded
#define SS_SUBNORMAL			6		// Sub-normal

// Substatus for GOOD
#define SS_LOCAL_OVERRIDE		6		// Local override

// Limit Field
#define LIMIT_NOT_LIMITED		0		// Value is free to move
#define LIMIT_LOW_LIMIT			1		// Value is pegged to low limit
#define LIMIT_HIGH_LIMIT		2		// Value is pegged to high limit
#define LIMIT_CONSTANT_LIMIT	3		// Value cannot move


//Quality��Ӧ���ַ���˵��
#define QUALITY_BAD_DESC			"BAD"
#define QUALITY_UNCERTAIN_DESC		"UNCERTAIN"
#define QUALITY_GOOD_DESC			"GOOD"

//δ����ȡֵ���ַ���˵��
#define QUALITY_UNDEFINED_DESC		"N/A"
#define SS_UNDEFINED_DESC			"δ����״̬"

//SubQuality��Ӧ���ַ���˵��
#define SS_NON_SPECIFIC_GOOD_DESC "GOOD"
#define SS_LOCAL_OVERRIDE_DESC  "LOCAL OVERRIDE"

#define SS_NON_SPECIFIC_UNCERTAIN_DESC	"NON_SPECIFIC_UNCERTAIN" //"δ֪ԭ��" 
#define SS_LAST_USABLE_DESC			"LAST_USABLE" //"���Ŀ���ֵ"
#define	SS_SENSOR_NOT_ACCURATE_DESC	 "SENSOR_NOT_ACCURATE" //"������ȡֵ����"
#define SS_EGU_UNITS_EXCEEDED_DESC	"EGU_UNITS_EXCEEDED"//"���̵�Ԫ����"
#define SS_SUBNORMAL_DESC			"SUBNORMAL"//"��ԴӦ���п�������Դ����"

#define SS_NON_SPECIFIC_BAD_DESC		"NON_SPECIFIC_BAD"//"δ֪����"
#define SS_CONFIG_ERROR_DESC			"CONFIG_ERROR"//"���ô���"
#define SS_NOT_CONNECTED_DESC		"NOT_CONNECTED"//"δ�����豸������Դ"
#define SS_DEVICE_FAILURE_DESC		"DEVICE_FAILURE"//"�豸����"
#define SS_SENSOR_FAILURE_DESC		"SENSOR_FAILURE"//"����������"
#define SS_LAST_KNOWN_DESC			"LAST_KNOWN"//"ͨ�Ź��ϣ�����ֵ����"
#define SS_COMM_FAILURE_DESC		"COMM_FAILURE"	//"ͨ�Ź��ϣ�����ֵ������"
#define SS_OUT_OF_SERVICE_DESC		"OUT_OF_SERVICE"//"�޷��ṩ����"
#define SS_SCAN_UNSUPPORT_DESC			"SCAN_UNSUPPORT"//"��ֹɨ��"
#define SS_SUBSCRIBE_TAG_TOOMANY_DESC		"SUBSCRIBE_TAG_TOOMANY" // 向PLC订阅的点太多了

//LeftOver���ַ���˵��
#define CV_LV_IO_NUMBER_LIMIT_DESC					"IO_NUMBER_LIMIT"//"IO��������"
#define CV_LV_SCHEDULE_FAILURE_DESC					"SCHEDULE_FAILURE"//"��ʱɨ��ʧ��"
#define CV_LV_SCAN_DISABLED_DESC					"SCAN_DISABLED"//"��ֹɨ��"
#define CV_LV_DRIVER_NOTFOUND_DESC					"DRIVER_NOTFOUND"//"�Ҳ�����Ӧ����"
#define CV_LV_ADDRESSLEN_TOOLONG_DESC				"ADDRESSLEN_TOOLONG"//"��ַ���ȹ���"
#define CV_LV_ADDRESS_BEGINFROMDIGITAL_DESC   		"ADDRESS_BEGINFROMDIGITAL"//"��ַ�������ֿ�ͷ"
#define CV_LV_ADDRESS_SEGNUM_TOOSMALL_DESC   		"ADDRESS_SEGNUM_TOOSMALL"//"��ֳ��ĵ�ַ�β���"
#define CV_LV_ADDRESS_SEGNUM_TOOMUCH_DESC			"ADDRESS_SEGNUM_TOOMUCH"//"��ֳ��ĵ�ַ�ι���"
#define CV_LV_DRIVERNAME_EMPTY_DESC					"DRIVERNAME_EMPTY"//"������Ϊ��"
#define CV_LV_DEVICENAME_EMPTY_DESC   				"DEVICENAME_EMPTY"//"�豸��Ϊ��"
#define CV_LV_IOADDRESS_EMPTY_DESC   				"IOADDRESS_EMPTY"//"�豸��ַΪ��"
#define CV_LV_IOADDRESS_EXCEEDLIMIT_DESC			"IOADDRESS_EXCEEDLIMIT"//"�豸��ַ�γ���������Χ"
#define CV_LV_IOADDRESS_BITOFFSET_NOTEMPTY_DESC		"IOADDRESS_BITOFFSET_NOTEMPTY"//"��ַ������λƫ����"
#define CV_LV_IOADDRESS_DIGITALTAG_NOBITOFFSET_DESC	"IOADDRESS_DIGITALTAG_NOBITOFFSET"//"ȱ��λƫ����"
#define CV_LA_IOADDRESS_BITOFFSET_EXCEEDLIMIT_DESC	"IOADDRESS_BITOFFSET_EXCEEDLIMIT"	//"λƫ��������������Χ"
#define CV_LV_IOADDRESS_BYTEOFFSET_EXCEEDLIMIT_DESC	"IOADDRESS_BYTEOFFSET_EXCEEDLIMIT"//"�ֽ�ƫ��������������Χ"
#define CV_LV_IOADDRESS_DATALENGTH_TOOSMALL_DESC	"IOADDRESS_DATALENGTH_TOOSMALL"//"���ݿ鳤�ȹ�С"
#define CV_LV_IOADDRESS_DATALENGTH_TOOMUCH_DESC		"IOADDRESS_DATALENGTH_TOOMUCH"//"���ݿ鳤�ȹ���"
#define CV_LV_DEVICE_NOTFOUND_DESC					"DEVICE_NOTFOUND"//"�Ҳ�����Ӧ�豸"
#define CV_LV_DATABLOCK_NOTFOUND_DESC				"DATABLOCK_NOTFOUND"//"�Ҳ�����Ӧ���ݿ�"
#define CV_LV_RELATIONALBLK_NOTFOUND_DESC			"RELATIONALBLK_NOTFOUND"//"�Ҳ��������ı���"	
#define CV_LV_CA_EXPRESSION_INVALID_DESC			"CA_EXPRESSION_INVALID"//"�Ƿ���CA����ʽ"
#define CV_LV_RELATIONALBLK_QUALITYBAD_DESC			"RELATIONALBLK_QUALITYBAD"//"��������������ΪBAD"
#define CV_LV_CA_CALC_FAILURE_DESC					"CA_CALC_FAILURE"//"����ʽ����ʧ��"


#endif
