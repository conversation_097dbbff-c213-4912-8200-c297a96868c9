/*!***********************************************************
 *  @file        LRDADef.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  @brief       (delcare all structures).
 *
 *  @author:     chenshengyu
 *  @version     08/29/2007  chenshengyu  Initial Version
 *  @version     08/09/2010  chenzhiquan  Change data type def to fix len types.
**************************************************************/

#ifndef _LRDA_DEF_HEADER_
#define _LRDA_DEF_HEADER_

#include "processdb/DbMdlSchemaDef.h"
#include "common/cv_datatype.h"
#include "processdb/EnumDef.h"
#include "common/cv_int.h"

#define ASYNCALLBACK_CTRL_ACK	10 // @pVoid is a TCtrlCmd_Ack
#define ASYNCALLBACK_DATA_ACK	11

#ifdef _WIN32

#ifdef cv6lrda_EXPORTS
#define LRDA_API extern "C" __declspec(dllexport)
#else
#define LRDA_API extern "C" __declspec(dllimport)
#endif

#else//_WIN32

#define LRDA_API extern "C"

#endif//_WIN32

typedef void (* PFN_AsyncCallback)(long nType, void *pCallBackData, long nLen, void *pUsrData);

//#pragma pack(push, 1)
//#pragma pack(1)
/************************************************************************/
/*    alarm priority                                                    */
/************************************************************************/

// *  @version  09/06/2007  chenshengyu  (moved to AlmDef.h).

/*
 **********************************************************
!struct: Local NTF
 *
 **********************************************************/

typedef	struct
{
	unsigned char	cDataType;		/* raw data type	 	*/
	unsigned char	cBlkType;		/* database block type 	*/
	int16_t			sFieldIndex;	/* field index 			*/
	int16_t			sArrayIndex;	/* starting index for arrays	*/
	uint16_t		sDataSize;		/* total UCHAR count 	*/
	long			nBlkIndex;		/* block index 			*/
	char			*szDataBuf;		/* pointer to data buffer		*/
} LocalNTF, *HLocalNTF;

/* Common FMMS Access Structure	*/
typedef struct						/* FMS_ACCESS_REC				*/
{
//	short		   sVersion ;		/* FMMS version (internal use)	*/
	TDbMdlVersion	  dbMdl ;		/* runtime/add/del version (internal use)	*/
	void			 * pNai ;		/* Destination node's NAI		*/
	char		   ** ppTag ;		/* Array of ptrs to tag names	*/
	char         ** ppField ;		/* Array of ptrs to field names	*/
	LocalNTF       ** ppVsp ;		/* Array of VSP's				*/
	char         * pDataBuf ;		/* Data Buffer 					*/

	uint32_t     ** ppError ;	    /* Array of error codes			*/
	uint32_t        nBufSiz ;	    /* Size of Data Buffer			*/
	uint32_t          pdbsn ;	    /* Process Data Base Serial #	*/
	uint32_t           pkey ;		/* Protection Key				*/

	uint16_t	      nItem ;		/* Number of data items			*/

	/* new fields to manage group refresh rates and timeout nm012494 */

	uint16_t 		usRefresh;		/* approx. group read interval */
   	uint32_t		do_not_use ;	/* Used for RDA  ebj102395 	*/
	uint32_t 		spare[ 5 ];   	/* spare - now 5 ebj102395	*/

	/* async-read & async-write callback */
	PFN_AsyncCallback	pfnAsyncCallBack;
	void*				pUsrData;		// the corresponding user data with pfnAsyncCallBack
	char				szOperator[PDB_MAX_OPERATOR_NAME];

}	FastAccessRec ;

// local LRDA group handle: for internal use only
typedef FastAccessRec*	HLrdaGroup;

// local LRDA NTF handle: for internal use only
typedef LocalNTF*		HLocalNTF;


//#pragma pack(pop)
//#pragma pack()
//TODO: alignment

#endif // _LRDA_DEF_HEADER_

