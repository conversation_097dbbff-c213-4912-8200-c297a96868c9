
import pytest
import asyncio
import pdb;
import os;
import logging

from AutoCommon.allure_setup import *


logging.info(f'这是测试用例的info')
logging.warning(f'这是测试用例的warning')
logging.error(f'这是测试用例的error')


# Use the allure_setup decorator
@allure_setup("DSFR-449", is_async=False)
def test_jira_summary_449():
    summary, _ = get_jira_summary("DSFR-449")
    logging.info(f"Summary:{summary}")
    assert summary == "【测试用例】opcua 写数据--多种数据类型【武哲强】" # Replace with the actual expected summary

@allure_setup("DSFR-395", is_async=False)
def test_jira_summary_395():
    summary, _ = get_jira_summary("DSFR-395")
    logging.info(f"Summary:{summary}")
    assert summary == "opcua数据发布支持写操作--wincc 读写【武哲强】" # Replace with the actual expected summary



# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test.py"])
  