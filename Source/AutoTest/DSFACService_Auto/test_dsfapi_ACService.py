import os
import pytest
import redis

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

# 程序路径（获取dr目录路径, 当前路径为dr/Source/AutoTest）
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"

dsfapi = None       # 全局变量
g_default_val_2Byte = 0x0102
g_default_val_4Byte = 0x01020304
g_default_val_8Byte = 0x0102030405060708
g_PLC_IPAddr = "************"   # PLC IP地址
# g_local_passwd = "Dsf@448"
g_local_passwd = "dsf123!@#"
g_redis_IPAddr = "127.0.0.1"    # redis IP地址
g_redis_port = 6380             # redis端口
g_conn_stat_key = "DSF_STATION_1.SYS::TXDRV_DEVICE0_CONNECTSTATUS"  # 连接状态key

# 设置iptables禁止本地访问PLC
def disable_local_access(IPAddr:str):
    proc = subprocess.Popen(['sudo', '-S', 'iptables', '-A', 'OUTPUT', '-d', IPAddr, '-j', 'DROP'],
                        stdin=subprocess.PIPE)
    proc.communicate(input=g_local_passwd.encode())     # 输入密码

# 删除iptables规则
def delete_iptables_rule(IPAddr:str):
    proc = subprocess.Popen(['sudo', '-S', 'iptables', '-D', 'OUTPUT', '-d', IPAddr, '-j', 'DROP'],
                        stdin=subprocess.PIPE)
    # proc.communicate(input=g_local_passwd.encode())     # 输入密码

# 测试之前执行
def before_test():
    print("Execute before the test starts")

    global dsfapi           #声明使用全局变量

    print(f"Loading DSFAPI library from: {application_path}")
    dsfapi = loadDSFAPILibrary(application_path)
    if not dsfapi : return

    print("Initialize DSFAPI")
    initDSFAPI(dsfapi)

# 测试之后执行
def after_test():
    print("Execute after the test ends")
    print("Free DSFAPI")
    dsfapi.freeDRSdkContext()

# 测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()   # 在测试开始前执行
    yield           # 这里是测试正在执行的地方
    after_test()    # 在测试结束后执行

# 测试用例
class TestACService():
    # 采集服务: autotest验证对驱动读取数据进行字节序调整(INT)
    @allure_setup("DSFR-1021", is_async=False)
    def test_jira_summary_byteorder_int(self):
        summary, _ = get_jira_summary("DSFR-1021")
        logging.info(f"Summary: {summary}")

        tag_name = "STD::AC_BYTEORDER_R_INT"
        tag_type = "INT"
        assert str(g_default_val_2Byte) == read_value(dsfapi, tag_name, tag_type)


    #采集服务: autotest验证对驱动写入控制命令进行字节序调整
    @allure_setup("DSFR-1022", is_async=False)
    def test_jira_summary_byteorder_write(self):
        summary, _ = get_jira_summary("DSFR-1022")
        logging.info(f"Summary: {summary}")

        tag_names = ["STD::AC_BYTEORDER_W_INT", "STD::AC_BYTEORDER_W_UINT", "STD::AC_BYTEORDER_W_DINT",
                    "STD::AC_BYTEORDER_W_UDINT", "STD::AC_BYTEORDER_W_LINT", "STD::AC_BYTEORDER_W_ULINT",
                    "STD::AC_BYTEORDER_W_WORD", "STD::AC_BYTEORDER_W_DWORD", "STD::AC_BYTEORDER_W_LWORD",
                    "STD::AC_BYTEORDER_W_REAL", "STD::AC_BYTEORDER_W_LREAL"]
        for t in tag_names:
            tag_type = t.split("_")[-1]             # 获取类型
            write_value(dsfapi, t, tag_type, 1)
            val = read_value(dsfapi, t, tag_type)
            write_value(dsfapi, t, tag_type, 0)     # 恢复初始值
            assert '1' == val



    #采集服务: autotest验证对象组成数据正确性(UDT)
    @allure_setup("DSFR-1023", is_async=False)
    def test_jira_summary_udt_read(self):
        summary, _ = get_jira_summary("DSFR-1023")
        logging.info(f"Summary: {summary}")

        tag_name = "AC_TEST_R_UDT"
        udt_names = ["TBOOL", "TBYTE", "TCHAR", "TSINT", "TUSINT", "TINT", "TUINT", "TDINT", "TUDINT",
                     "TLINT", "TULINT", "TWORD", "TDWORD", "TLWORD", "TSTRING", "TREAL", "TLREAL",
                     "TTIME", "TLTIME", "TDATE", "TTOD", "TDT"]
        for t in udt_names:
            tag_name_full = f"STD::{tag_name}.{t}"
            tag_type = t[1:]                        # 获取类型
            assert '1' == read_value(dsfapi, tag_name_full, tag_type)


    #采集服务: autotest验证对象组成数据正确性(ARRAY)
    @allure_setup("DSFR-1024", is_async=False)
    def test_jira_summary_int_array_read(self):
        summary, _ = get_jira_summary("DSFR-1024")
        logging.info(f"Summary: {summary}")

        tag_name = "AC_ARRAY_R_INT"
        tag_type = 'INT'
        startIdx = 2
        endIdx = 10
        for i in range(startIdx, endIdx + 1):
            tag_name_full = f"STD::{tag_name}[{i}]"
            assert '1' == read_value(dsfapi, tag_name_full, tag_type)


    # 采集服务: autotest验证对驱动读取数据进行字节序调整(UINT)
    @allure_setup("DSFR-1026", is_async=False)
    def test_jira_summary_byteorder_uint(self):
        summary, _ = get_jira_summary("DSFR-1026")
        logging.info(f"Summary: {summary}")

        tag_name = "STD::AC_BYTEORDER_R_UINT"
        tag_type = "UINT"
        assert str(g_default_val_2Byte) == read_value(dsfapi, tag_name, tag_type)


    # 采集服务: autotest验证对驱动读取数据进行字节序调整(WORD)
    @allure_setup("DSFR-1027", is_async=False)
    def test_jira_summary_byteorder_word(self):
        summary, _ = get_jira_summary("DSFR-1027")
        logging.info(f"Summary: {summary}")

        tag_name = "STD::AC_BYTEORDER_R_WORD"
        tag_type = "WORD"
        assert str(g_default_val_2Byte) == read_value(dsfapi, tag_name, tag_type)


    # 采集服务: autotest验证对驱动读取数据进行字节序调整(DWORD)
    @allure_setup("DSFR-1028", is_async=False)
    def test_jira_summary_byteorder_dword(self):
        summary, _ = get_jira_summary("DSFR-1028")
        logging.info(f"Summary: {summary}")

        tag_name = "STD::AC_BYTEORDER_R_DWORD"
        tag_type = "DWORD"
        assert str(g_default_val_4Byte) == read_value(dsfapi, tag_name, tag_type)


    # 采集服务: autotest验证对驱动读取数据进行字节序调整(LWORD)
    @allure_setup("DSFR-1029", is_async=False)
    def test_jira_summary_byteorder_lword(self):
        summary, _ = get_jira_summary("DSFR-1029")
        logging.info(f"Summary: {summary}")

        tag_name = "STD::AC_BYTEORDER_R_LWORD"
        tag_type = "LWORD"
        assert str(g_default_val_8Byte) == read_value(dsfapi, tag_name, tag_type)


    # 采集服务: autotest验证对驱动读取数据进行字节序调整(DINT)
    @allure_setup("DSFR-1030", is_async=False)
    def test_jira_summary_byteorder_dint(self):
        summary, _ = get_jira_summary("DSFR-1030")
        logging.info(f"Summary: {summary}")

        tag_name = "STD::AC_BYTEORDER_R_DINT"
        tag_type = "DINT"
        assert str(g_default_val_4Byte) == read_value(dsfapi, tag_name, tag_type)


    # 采集服务: autotest验证对驱动读取数据进行字节序调整(UDINT)
    @allure_setup("DSFR-1031", is_async=False)
    def test_jira_summary_byteorder_udint(self):
        summary, _ = get_jira_summary("DSFR-1031")
        logging.info(f"Summary: {summary}")

        tag_name = "STD::AC_BYTEORDER_R_UDINT"
        tag_type = "UDINT"
        assert str(g_default_val_4Byte) == read_value(dsfapi, tag_name, tag_type)


    # 采集服务: autotest验证对驱动读取数据进行字节序调整(LINT)
    @allure_setup("DSFR-1032", is_async=False)
    def test_jira_summary_byteorder_lint(self):
        summary, _ = get_jira_summary("DSFR-1032")
        logging.info(f"Summary: {summary}")

        tag_name = "STD::AC_BYTEORDER_R_LINT"
        tag_type = "LINT"
        assert str(g_default_val_8Byte) == read_value(dsfapi, tag_name, tag_type)


    # 采集服务: autotest验证对驱动读取数据进行字节序调整(ULINT)
    @allure_setup("DSFR-1033", is_async=False)
    def test_jira_summary_byteorder_ulint(self):
        summary, _ = get_jira_summary("DSFR-1033")
        logging.info(f"Summary: {summary}")

        tag_name = "STD::AC_BYTEORDER_R_ULINT"
        tag_type = "ULINT"
        assert str(g_default_val_8Byte) == read_value(dsfapi, tag_name, tag_type)


    # 采集服务: autotest验证对驱动读取数据进行字节序调整(REAL)
    @allure_setup("DSFR-1034", is_async=False)
    def test_jira_summary_byteorder_real(self):
        summary, _ = get_jira_summary("DSFR-1035")
        logging.info(f"Summary: {summary}")

        tag_name = "STD::AC_BYTEORDER_R_REAL"
        tag_type = "REAL"
        assert str(1) == read_value(dsfapi, tag_name, tag_type)


    # 采集服务: autotest验证对驱动读取数据进行字节序调整(LREAL)
    @allure_setup("DSFR-1035", is_async=False)
    def test_jira_summary_byteorder_lreal(self):
        summary, _ = get_jira_summary("DSFR-1035")
        logging.info(f"Summary: {summary}")

        tag_name = "STD::AC_BYTEORDER_R_LREAL"
        tag_type = "LREAL"
        assert str(1) == read_value(dsfapi, tag_name, tag_type)


    # 采集服务: autotest验证对象组成数据正确性(UDT内嵌套ARRAY)
    @allure_setup("DSFR-1086", is_async=False)
    def test_jira_summary_inner_array(self):
        summary, _ = get_jira_summary("DSFR-1086")
        logging.info(f"Summary: {summary}")
        
        tag_name = "AC_TEST_R_ARRAY_UDT.INNER_ARRAY_INT"
        tag_type = 'INT'
        startIdx = 2
        endIdx = 10
        for i in range(startIdx, endIdx + 1):
            tag_name_full = f"STD::{tag_name}[{i}]"
            assert '1' == read_value(dsfapi, tag_name_full, tag_type)


    # 采集服务: autotest验证对象组成数据正确性(结构体ARRAY)
    @allure_setup("DSFR-1087", is_async=False)
    def test_jira_summary_udt_array(self):
        summary, _ = get_jira_summary("DSFR-1087")
        logging.info(f"Summary: {summary}")
        
        obj_name = "AC_TEST_R_UDT_ARRAY1"
        startIdx = 2
        endIdx = 10
        udt_names = ["TBOOL", "TBYTE", "TCHAR", "TSINT", "TUSINT", "TINT", "TUINT", "TDINT", "TUDINT",
                     "TLINT", "TULINT", "TWORD", "TDWORD", "TLWORD", "TSTRING", "TREAL", "TLREAL",
                     "TTIME", "TLTIME", "TDATE", "TTOD", "TDT"] 
        for i in range(startIdx, endIdx + 1):
            tag_name = f"STD::{obj_name}[{i}]"
            for t in udt_names:
                tag_name_full = f"{tag_name}.{t}"
                tag_type = t[1:]                        # 获取类型
                assert '1' == read_value(dsfapi, tag_name_full, tag_type)



    # 采集服务: autotest验证驱动连接状态异常时删除设备对应对象数据
    @allure_setup("DSFR-1025", is_async=False)
    def test_jira_summary_connect_status_del(self):
        summary, _ = get_jira_summary("DSFR-1025")
        logging.info(f"Summary: {summary}")

        disable_local_access(g_PLC_IPAddr)                              # 禁止本地访问PLC
        r = redis.Redis(host=g_redis_IPAddr, port=g_redis_port)
        connStat = r.get(g_conn_stat_key)                               # 连接状态
        cnt = 0
        while connStat.decode() != "\x00":
            time.sleep(1)
            connStat = r.get(g_conn_stat_key)
            cnt += 1
            if cnt > 120:
                break
        delete_iptables_rule(g_PLC_IPAddr)                              # 恢复iptables规则
        assert connStat.decode() == "\x00"                              # 确认连接状态为0
        key = 'STD::AC_*'           
        matched_keys = list(r.scan_iter(key))                           # 确认对象数据不存在
        assert 0 == len(matched_keys)


# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfapi_ACService.py"])