import os
import pytest
import requests
import json
import time

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *
from datetime import datetime, timedelta

# 测试之前和测试之后执行方法
# @pytest.fixture(scope="session", autouse=True)

def call_url_with_body(url, body: dict, method="POST"):
    headers = {"Content-Type": "application/json"}
    if method.upper() == "POST":
        response = requests.post(url, json=body, headers=headers)
    elif method.upper() == "GET":
        response = requests.get(url, json=body, headers=headers)
    else:
        raise ValueError("Unsupported method")
    return response.status_code, response.text

# 测试用例
class TestDSFSourceMonitor():
    
    # 1.主题监控功能
    @allure_setup("DSFR-486", is_async=False)
    def test_jira_summary1(self):
        summary, _ = get_jira_summary("DSFR-486")
        logging.info(f"Summary: {summary}")

        url = "http://127.0.0.1:55020/Monitor/TopicInfo"
        body = {"TopicName": ["STD::NGVS_SM_TEST_REC", "STD::NGVS_SM_TEST_SEND", "TEST_EMPTY"]}
        method = "POST"
        status, resp = call_url_with_body(url, body, method)

        result = [
                    {
                        "pattern": "Receiver",
                        "pub_ip": "************",
                        "pub_port": 6003,
                        "rec_ip": "**************",
                        "rec_port": 9000,
                        "status": "1",
                        "topic_name": "STD::NGVS_SM_TEST_REC"
                    },
                    {
                        "pattern": "Publisher",
                        "pub_ip": "**************",
                        "pub_port": 6000,
                        "rec_ip": "",
                        "rec_port": "",
                        "status": "1",
                        "topic_name": "STD::NGVS_SM_TEST_SEND"
                    },
                    {
                        "pattern": "",
                        "pub_ip": "",
                        "pub_port": "",
                        "rec_ip": "",
                        "rec_port": "",
                        "status": "-2",
                        "topic_name": "TEST_EMPTY"
                    }
                ]
        resp_dict = json.loads(resp.strip())

        time.sleep(1)  # 等待1秒钟
        assert resp_dict == result


    #2.节点状态监控功能
    @allure_setup("DSFR-509", is_async=False)
    def test_jira_summary2(self):
        summary, _ = get_jira_summary("DSFR-509")
        logging.info(f"Summary: {summary}")

        url = "http://127.0.0.1:55020/Monitor/NodeStatus"
        body = {"NodeIp" : "127.0.0.1"}
        method = "POST"
        status, resp = call_url_with_body(url, body, method)

        result = [
                    {
                        "node_name": "DSF_STATION_1",
                        "node_rm": 0,
                        "node_rm_ip": "**************",
                        "node_rm_sequence": "0",
                        "node_status": "Running",
                        "node_type": "Master",
                        "processes": [
                            {
                                "proc_description": "数据服务",
                                "proc_name": "dsfdataservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "采集服务",
                                "proc_name": "dsfacquisitionservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "冗余服务",
                                "proc_name": "dsfrmservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "驱动控制服务",
                                "proc_name": "dsfdrvctrl",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "OPC UA发布服务",
                                "proc_name": "dsfopcuapublish",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "数据文件服务",
                                "proc_name": "dsfdatafileservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "ihd转储",
                                "proc_name": "dsfdataarchive",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "AB PLC驱动",
                                "proc_name": "abdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "CODESYS驱动",
                                "proc_name": "codesysdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "天行驱动",
                                "proc_name": "dsftxdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "ICG驱动",
                                "proc_name": "icgdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "中间变量驱动",
                                "proc_name": "internaldrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "三菱PLC驱动",
                                "proc_name": "melsecdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "Modbus驱动",
                                "proc_name": "modbusdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "OPC UA驱动",
                                "proc_name": "opcuadrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "Siemens S7驱动",
                                "proc_name": "snap7drv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            }
                        ]
                    }
                ]
        resp_dict = json.loads(resp.strip())

        time.sleep(1)  # 等待1秒钟
        assert 200 == status
        assert resp_dict == result
    
    
     # 3.进程资源历史查询
    @allure_setup("DSFR-672", is_async=False)
    def test_jira_summary3(self):
        summary, _ = get_jira_summary("DSFR-672")
        logging.info(f"Summary: {summary}")
        time.sleep(5)  # 等待5秒钟，防止前面操作导致进程关闭，写入历史数据不对
        url = "http://127.0.0.1:55020/Monitor/ProcessHisSource"
        
        now = datetime.now()
        end_time = now.strftime("%Y-%m-%d %H:%M:%S")
        start_time = (now - timedelta(seconds=11)).strftime("%Y-%m-%d %H:%M:%S")

        body = {
                    "ProcessName":"dsfdataservice",
                    "ProcessType":"System",
                    "StartTime": start_time,
                    "EndTime": end_time,
                    "IntervalTime": ""
                }

        method = "GET"
        status, resp = call_url_with_body(url, body, method)
        resp_dict = json.loads(resp.strip())
        result = len(resp_dict["cpu_usage"])
        print("start_time:", start_time)
        print("end_time:", end_time)
        time.sleep(1)  # 等待1秒钟
        assert 200 == status
        assert 2<=result<=3
    
    
    #4.节点进程启停功能
    @allure_setup("DSFR-506", is_async=False)
    def test_jira_summary4(self):
        summary, _ = get_jira_summary("DSFR-506")
        logging.info(f"Summary: {summary}")

        #1.关闭单个进程
        result1 = {
                    "message": "System dsfacquisitionservice stop successfully!",
                    "status": "success"
                  }
        result1_v = [
                    {
                        "node_name": "DSF_STATION_1",
                        "node_rm": 0,
                        "node_rm_ip": "**************",
                        "node_rm_sequence": "0",
                        "node_status": "Running",
                        "node_type": "Master",
                        "processes": [
                            {
                                "proc_description": "数据服务",
                                "proc_name": "dsfdataservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "采集服务",
                                "proc_name": "dsfacquisitionservice",
                                "proc_status": "Stopped",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "冗余服务",
                                "proc_name": "dsfrmservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "驱动控制服务",
                                "proc_name": "dsfdrvctrl",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "OPC UA发布服务",
                                "proc_name": "dsfopcuapublish",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "数据文件服务",
                                "proc_name": "dsfdatafileservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "ihd转储",
                                "proc_name": "dsfdataarchive",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "AB PLC驱动",
                                "proc_name": "abdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "CODESYS驱动",
                                "proc_name": "codesysdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "天行驱动",
                                "proc_name": "dsftxdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "ICG驱动",
                                "proc_name": "icgdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "中间变量驱动",
                                "proc_name": "internaldrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "三菱PLC驱动",
                                "proc_name": "melsecdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "Modbus驱动",
                                "proc_name": "modbusdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "OPC UA驱动",
                                "proc_name": "opcuadrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "Siemens S7驱动",
                                "proc_name": "snap7drv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            }
                        ]
                    }
                ]
        url = "http://127.0.0.1:55020/Monitor/NodeControl"
        body = {"NodeIp":"127.0.0.1", "ProcessType":"System", "ProcessName":"dsfacquisitionservice", "Control":"stop"}
        method = "POST"
        status1, resp1 = call_url_with_body(url, body, method)
        resp_dict1 = json.loads(resp1.strip())
        time.sleep(5)  # 等待5秒钟
        url = "http://127.0.0.1:55020/Monitor/NodeStatus"
        body = {"NodeIp" : "127.0.0.1"}
        method = "POST"
        status1_v, resp1_v = call_url_with_body(url, body, method)
        resp_dict1_v = json.loads(resp1_v.strip())
        time.sleep(1)  # 等待1秒钟
        
        #2.启动单个进程
        result2 = {
                    "message": "System dsfacquisitionservice start successfully!",
                    "status": "success"
                  }
        result2_v = [
                    {
                        "node_name": "DSF_STATION_1",
                        "node_rm": 0,
                        "node_rm_ip": "**************",
                        "node_rm_sequence": "0",
                        "node_status": "Running",
                        "node_type": "Master",
                        "processes": [
                            {
                                "proc_description": "数据服务",
                                "proc_name": "dsfdataservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "采集服务",
                                "proc_name": "dsfacquisitionservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "冗余服务",
                                "proc_name": "dsfrmservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "驱动控制服务",
                                "proc_name": "dsfdrvctrl",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "OPC UA发布服务",
                                "proc_name": "dsfopcuapublish",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "数据文件服务",
                                "proc_name": "dsfdatafileservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "ihd转储",
                                "proc_name": "dsfdataarchive",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "AB PLC驱动",
                                "proc_name": "abdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "CODESYS驱动",
                                "proc_name": "codesysdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "天行驱动",
                                "proc_name": "dsftxdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "ICG驱动",
                                "proc_name": "icgdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "中间变量驱动",
                                "proc_name": "internaldrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "三菱PLC驱动",
                                "proc_name": "melsecdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "Modbus驱动",
                                "proc_name": "modbusdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "OPC UA驱动",
                                "proc_name": "opcuadrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "Siemens S7驱动",
                                "proc_name": "snap7drv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            }
                        ]
                    }
                ]
        url = "http://127.0.0.1:55020/Monitor/NodeControl"
        body = {"NodeIp":"127.0.0.1", "ProcessType":"System", "ProcessName":"dsfacquisitionservice", "Control":"run"}
        method = "POST"
        status2, resp2 = call_url_with_body(url, body, method)
        resp_dict2 = json.loads(resp2.strip())
        time.sleep(10)  # 等待10秒钟
        url = "http://127.0.0.1:55020/Monitor/NodeStatus"
        body = {"NodeIp" : "127.0.0.1"}
        method = "POST"
        status2_v, resp2_v = call_url_with_body(url, body, method)
        resp_dict2_v = json.loads(resp2_v.strip())
        time.sleep(1)  # 等待1秒钟
        
        
        #3.关闭所有进程
        result3 = {
                    "message": "All process on this node stop successfully!",
                    "status": "success"
                  }
        result3_v = [
                    {
                        "node_name": "DSF_STATION_1",
                        "node_rm": 0,
                        "node_rm_ip": "**************",
                        "node_rm_sequence": "0",
                        "node_status": "Stopped",
                        "node_type": "Slave",
                        "processes": [
                            {
                                "proc_description": "数据服务",
                                "proc_name": "dsfdataservice",
                                "proc_status": "Stopped",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "采集服务",
                                "proc_name": "dsfacquisitionservice",
                                "proc_status": "Stopped",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "冗余服务",
                                "proc_name": "dsfrmservice",
                                "proc_status": "Stopped",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "驱动控制服务",
                                "proc_name": "dsfdrvctrl",
                                "proc_status": "Stopped",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "OPC UA发布服务",
                                "proc_name": "dsfopcuapublish",
                                "proc_status": "Stopped",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "数据文件服务",
                                "proc_name": "dsfdatafileservice",
                                "proc_status": "Stopped",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "ihd转储",
                                "proc_name": "dsfdataarchive",
                                "proc_status": "Stopped",
                                "proc_type": "System"
                            }
                        ]
                    }
                ]
        url = "http://127.0.0.1:55020/Monitor/NodeControl"
        body = {"NodeIp":"127.0.0.1", "ProcessType":"", "ProcessName":"", "Control":"stop"}
        method = "POST"
        status3, resp3 = call_url_with_body(url, body, method)
        resp_dict3 = json.loads(resp3.strip())
        time.sleep(15)  # 等待15秒钟
        url = "http://127.0.0.1:55020/Monitor/NodeStatus"
        body = {"NodeIp" : "127.0.0.1"}
        method = "POST"
        status3_v, resp3_v = call_url_with_body(url, body, method)
        resp_dict3_v = json.loads(resp3_v.strip())
        time.sleep(1)  # 等待1秒钟
        
        # #4.启动所有进程
        result4 = {
                    "message": "All process on this node start successfully!",
                    "status": "success"
                  }
        result4_v = [
                    {
                        "node_name": "DSF_STATION_1",
                        "node_rm": 0,
                        "node_rm_ip": "**************",
                        "node_rm_sequence": "0",
                        "node_status": "Running",
                        "node_type": "Master",
                        "processes": [
                            {
                                "proc_description": "数据服务",
                                "proc_name": "dsfdataservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "采集服务",
                                "proc_name": "dsfacquisitionservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "冗余服务",
                                "proc_name": "dsfrmservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "驱动控制服务",
                                "proc_name": "dsfdrvctrl",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "OPC UA发布服务",
                                "proc_name": "dsfopcuapublish",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "数据文件服务",
                                "proc_name": "dsfdatafileservice",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "ihd转储",
                                "proc_name": "dsfdataarchive",
                                "proc_status": "Running",
                                "proc_type": "System"
                            },
                            {
                                "proc_description": "AB PLC驱动",
                                "proc_name": "abdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "CODESYS驱动",
                                "proc_name": "codesysdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "天行驱动",
                                "proc_name": "dsftxdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "ICG驱动",
                                "proc_name": "icgdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "中间变量驱动",
                                "proc_name": "internaldrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "三菱PLC驱动",
                                "proc_name": "melsecdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "Modbus驱动",
                                "proc_name": "modbusdrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "OPC UA驱动",
                                "proc_name": "opcuadrv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            },
                            {
                                "proc_description": "Siemens S7驱动",
                                "proc_name": "snap7drv",
                                "proc_status": "Running",
                                "proc_type": "Driver"
                            }
                        ]
                    }
                ]
        url = "http://127.0.0.1:55020/Monitor/NodeControl"
        body = {"NodeIp":"127.0.0.1", "ProcessType":"", "ProcessName":"", "Control":"run"}
        method = "POST"
        status4, resp4 = call_url_with_body(url, body, method)
        resp_dict4 = json.loads(resp4.strip())
        time.sleep(10)  # 等待10秒钟
        url = "http://127.0.0.1:55020/Monitor/NodeStatus"
        body = {"NodeIp" : "127.0.0.1"}
        method = "POST"
        status4_v, resp4_v = call_url_with_body(url, body, method)
        resp_dict4_v = json.loads(resp4_v.strip())
        time.sleep(5)  # 等待5秒钟
        
        
        assert resp_dict1 == result1
        assert resp_dict1_v == result1_v
        assert resp_dict2 == result2
        assert resp_dict2_v == result2_v
        assert resp_dict3 == result3
        assert resp_dict3_v == result3_v
        assert resp_dict4 == result4
        assert resp_dict4_v == result4_v
    
    
    # # # 4.主备冗余切换功能
    # # @allure_setup("DSFR-536", is_async=False)
    # # def test_jira_summary4(self):
    # #     summary, _ = get_jira_summary("DSFR-563")
    # #     logging.info(f"Summary: {summary}")

    # #     url = "http://127.0.0.1:55020/Monitor/RMSwitch"
    # #     body = {"NodeIp" : "127.0.0.1"}
    # #     method = "GET"
    # #     status, resp = call_url_with_body(url, body, method)

    # #     result = {
    # #                 "status": "success",
    # #                 "message": "switch rmstatus successful!"
    # #             }
    # #     resp_dict = json.loads(resp.strip())

    # #     time.sleep(1)  # 等待1秒钟
    # #     assert 200 == status
    # #     assert resp_dict == result
    
    
   
        
        
    # 6.进程资源历史查询
    @allure_setup("DSFR-680", is_async=False)
    def test_jira_summary6(self):
        summary, _ = get_jira_summary("DSFR-680")
        logging.info(f"Summary: {summary}")

        url = "http://127.0.0.1:55020/Monitor/ProcessSource"
        
        body = {
                    "ProcessName":"dsfdataservice",
                    "ProcessType":"System"
                }

        method = "GET"
        now = datetime.now()
        status, resp = call_url_with_body(url, body, method)
        
        resp_dict = json.loads(resp.strip())
        result_time_str = resp_dict["timestamps"]
        result_cpu = resp_dict["cpu_usage"]
        result_io = resp_dict["io_usage"]
        result_mem = resp_dict["memory_usage"]
        result_thread = resp_dict["thread_count"]
        result_time = datetime.strptime(result_time_str, "%Y-%m-%d %H:%M:%S")
        print("now:", now)
        print("result_time:", result_time)
        # 计算时间差（单位：秒）
        diff = abs((now - result_time).total_seconds())

        assert 200 == status
        assert result_cpu != " "
        assert result_io != " "
        assert result_mem != " "
        assert result_thread != " "
        assert diff <= 6
    
    
    
    # 7.监控服务:支持前端导入许可证
    @allure_setup("DSFR-686", is_async=False)
    def test_jira_summary7(self):
        summary, _ = get_jira_summary("DSFR-686")
        logging.info(f"Summary: {summary}")

        url = "http://127.0.0.1:55020/Monitor/LicDownload"
        
        # 1.下发非法许可证
        xml_path1 = "/home/<USER>/autotestdir/config_bak/license_invalid.xml"
        with open(xml_path1, "r", encoding="utf-8") as f:
            xml_content = f.read()
        body1 = {
                    "FileName":"license.xml",
                    "FileContent":xml_content
                }
        method1 = "POST"
        status, resp1 = call_url_with_body(url, body1, method1)
        result1 = {
                    "status": "INVALID",
                    "message": "license download refused"
                }
        resp_dict1 = json.loads(resp1.strip())
        time.sleep(1)  # 等待1秒钟
        
        # 2.下发合法许可证
        xml_path2 = "/home/<USER>/autotestdir/config_bak/license.xml"
        with open(xml_path2, "r", encoding="utf-8") as f:
            xml_content = f.read()
        body2 = {
                    "FileName":"license.xml",
                    "FileContent":xml_content
                }
        method2 = "POST"
        status, resp2 = call_url_with_body(url, body2, method2)
        result2 = {
                    "status": "NORMAL",
                    "message": "license download success"
                }
        resp_dict2 = json.loads(resp2.strip())
        time.sleep(1) 
        assert resp_dict1 == result1
        assert resp_dict2 == result2
    
    
    #8. 获取版本接口测试
    @allure_setup("DSFR-956", is_async=False)
    def test_jira_summary8(self):
        summary, _ = get_jira_summary("DSFR-956")
        logging.info(f"Summary: {summary}")

        url = "http://127.0.0.1:55020/Monitor/GetDSFRVersion"
        method = "GET"
        body = {}
        status, resp = call_url_with_body(url, body, method)

        result = {"Version":"0.5.0.2507.1.202506251522"}    
        resp_dict = json.loads(resp.strip())
        time.sleep(1)  # 等待1秒钟
        assert resp_dict == result
        
        
    

# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_DSFSourceMonitor.py"])