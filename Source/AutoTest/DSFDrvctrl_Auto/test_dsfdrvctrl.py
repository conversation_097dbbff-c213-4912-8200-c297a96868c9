import os
import pytest
import requests

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
config_path = f"{application_path}/projects/defaultproject/config"
#全局变量
dsfsourcemonitor=None

#控制驱动
# param1 驱动名称
# param2 行为
def control_driver(driver_name,action):
    url = "http://127.0.0.1:55020/Monitor/NodeControl"
    body = {"NodeIp":"127.0.0.1", "ProcessType":"Driver", "ProcessName":driver_name, "Control":action}
    method = "POST"
    status, resp = call_url_with_body(url, body, method)
    resp_dict = json.loads(resp.strip())
    return resp_dict

def call_url_with_body(url, body: dict, method="POST"):
    headers = {"Content-Type": "application/json"}
    if method.upper() == "POST":
        response = requests.post(url, json=body, headers=headers)
    elif method.upper() == "GET":
        response = requests.get(url, json=body, headers=headers)
    else:
        raise ValueError("Unsupported method")
    return response.status_code, response.text

#测试之前
def before_test():
    print("测试开始前执行")
    # 声明要使用全局变量
    global dsfsourcemonitor

    # 启动dsfsourcemonitor进程
    process_name="dsfsourcemonitor"
    if not is_process_running(process_name):
        print(f"启动{process_name}进程")
        command=f"./executable/{process_name}"
        dsfsourcemonitor=start_program(command,application_path)
        time.sleep(5)

#测试之后
def after_test():
    print("测试结束后执行")

    #关闭dsfsourcemonitor进程
    if dsfsourcemonitor!=None:
        print("关闭dsfsourcemonitor进程")
        stop_program(dsfsourcemonitor)

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

class TestDSFDrvctrl():

    @allure_setup("DSFR-1414", is_async=False)
    def test_jira_summary_1414(self):
        summary, _ = get_jira_summary("DSFR-1414")
        # assert summary == "autotest验证dsfdrvctrl能否正常启动"
        
        #验证
        result=is_process_running("dsfdrvctrl")
        assert True==result

    @allure_setup("DSFR-1415", is_async=False)
    def test_jira_summary_1415(self):
        summary, _ = get_jira_summary("DSFR-1415")
        # assert summary == "autotest验证dsfdrvctrl能否正常启动abdrv"
        
        #验证
        result=is_process_running("abdrv")
        assert True==result

    @allure_setup("DSFR-1416", is_async=False)
    def test_jira_summary_1416(self):
        summary, _ = get_jira_summary("DSFR-1416")
        # assert summary == "autotest验证dsfdrvctrl能否正常启动codesysdrv"
        
        #验证
        result=is_process_running("codesysdrv")
        assert True==result

    @allure_setup("DSFR-1417", is_async=False)
    def test_jira_summary_1417(self):
        summary, _ = get_jira_summary("DSFR-1417")
        # assert summary == "autotest验证dsfdrvctrl能否正常启动dsftxdrv"
        
        #验证
        result=is_process_running("dsftxdrv")
        assert True==result

    @allure_setup("DSFR-1418", is_async=False)
    def test_jira_summary_1418(self):
        summary, _ = get_jira_summary("DSFR-1418")
        # assert summary == "autotest验证dsfdrvctrl能否正常启动icgdrv"
        
        #验证
        result=is_process_running("icgdrv")
        assert True==result

    @allure_setup("DSFR-1419", is_async=False)
    def test_jira_summary_1419(self):
        summary, _ = get_jira_summary("DSFR-1419")
        # assert summary == "autotest验证dsfdrvctrl能否正常启动internaldrv"
        
        #验证
        result=is_process_running("internaldrv")
        assert True==result

    @allure_setup("DSFR-1420", is_async=False)
    def test_jira_summary_1420(self):
        summary, _ = get_jira_summary("DSFR-1420")
        # assert summary == "autotest验证dsfdrvctrl能否正常启动melsecdrv"
        
        #验证
        result=is_process_running("melsecdrv")
        assert True==result

    @allure_setup("DSFR-1421", is_async=False)
    def test_jira_summary_1421(self):
        summary, _ = get_jira_summary("DSFR-1421")
        # assert summary == "autotest验证dsfdrvctrl能否正常启动modbusdrv"
        
        #验证
        result=is_process_running("modbusdrv")
        assert True==result

    @allure_setup("DSFR-1422", is_async=False)
    def test_jira_summary_1422(self):
        summary, _ = get_jira_summary("DSFR-1422")
        # assert summary == "autotest验证dsfdrvctrl能否正常启动opcuadrv"
        
        #验证
        result=is_process_running("opcuadrv")
        assert True==result

    @allure_setup("DSFR-1423", is_async=False)
    def test_jira_summary_1423(self):
        summary, _ = get_jira_summary("DSFR-1423")
        # assert summary == "autotest验证dsfdrvctrl能否正常启动snap7drv"
        
        #验证
        result=is_process_running("snap7drv")
        assert True==result

    @allure_setup("DSFR-1424", is_async=False)
    def test_jira_summary_1424(self):
        summary, _ = get_jira_summary("DSFR-1424")
        # assert summary == "autotest验证abdrv异常关闭，dsfdrvctrl能否重新拉起abdrv"
        
        stop_program_by_name("abdrv")

        time.sleep(5)

        #验证
        result=is_process_running("abdrv")
        assert True==result

    @allure_setup("DSFR-1425", is_async=False)
    def test_jira_summary_1425(self):
        summary, _ = get_jira_summary("DSFR-1425")
        # assert summary == "autotest验证codesysdrv异常关闭，dsfdrvctrl能否重新拉起codesysdrv"
        
        stop_program_by_name("codesysdrv")

        time.sleep(5)

        #验证
        result=is_process_running("codesysdrv")
        assert True==result

    @allure_setup("DSFR-1426", is_async=False)
    def test_jira_summary_1426(self):
        summary, _ = get_jira_summary("DSFR-1426")
        # assert summary == "autotest验证codesysdrv异常关闭，dsfdrvctrl能否重新拉起codesysdrv"
        
        stop_program_by_name("codesysdrv")

        time.sleep(5)

        #验证
        result=is_process_running("codesysdrv")
        assert True==result

    @allure_setup("DSFR-1427", is_async=False)
    def test_jira_summary_1427(self):
        summary, _ = get_jira_summary("DSFR-1427")
        # assert summary == "autotest验证dsftxdrv异常关闭，dsfdrvctrl能否重新拉起dsftxdrv"
        
        stop_program_by_name("dsftxdrv")

        time.sleep(5)

        #验证
        result=is_process_running("dsftxdrv")
        assert True==result

    @allure_setup("DSFR-1428", is_async=False)
    def test_jira_summary_1428(self):
        summary, _ = get_jira_summary("DSFR-1428")
        # assert summary == "autotest验证icgdrv异常关闭，dsfdrvctrl能否重新拉起icgdrv"
        
        stop_program_by_name("icgdrv")

        time.sleep(5)

        #验证
        result=is_process_running("icgdrv")
        assert True==result

    @allure_setup("DSFR-1429", is_async=False)
    def test_jira_summary_1429(self):
        summary, _ = get_jira_summary("DSFR-1429")
        # assert summary == "autotest验证internaldrv异常关闭，dsfdrvctrl能否重新拉起internaldrv"
        
        stop_program_by_name("internaldrv")

        time.sleep(5)

        #验证
        result=is_process_running("internaldrv")
        assert True==result

    @allure_setup("DSFR-1430", is_async=False)
    def test_jira_summary_1430(self):
        summary, _ = get_jira_summary("DSFR-1430")
        # assert summary == "autotest验证melsecdrv异常关闭，dsfdrvctrl能否重新拉起melsecdrv"
        
        stop_program_by_name("melsecdrv")

        time.sleep(5)

        #验证
        result=is_process_running("melsecdrv")
        assert True==result

    @allure_setup("DSFR-1431", is_async=False)
    def test_jira_summary_1431(self):
        summary, _ = get_jira_summary("DSFR-1431")
        # assert summary == "autotest验证modbusdrv异常关闭，dsfdrvctrl能否重新拉起modbusdrv"
        
        stop_program_by_name("modbusdrv")

        time.sleep(5)

        #验证
        result=is_process_running("modbusdrv")
        assert True==result

    @allure_setup("DSFR-1432", is_async=False)
    def test_jira_summary_1432(self):
        summary, _ = get_jira_summary("DSFR-1432")
        # assert summary == "autotest验证opcuadrv异常关闭，dsfdrvctrl能否重新拉起opcuadrv"
        
        stop_program_by_name("opcuadrv")

        time.sleep(5)

        #验证
        result=is_process_running("opcuadrv")
        assert True==result

    @allure_setup("DSFR-1433", is_async=False)
    def test_jira_summary_1433(self):
        summary, _ = get_jira_summary("DSFR-1433")
        # assert summary == "autotest验证snap7drv异常关闭，dsfdrvctrl能否重新拉起snap7drv"
        
        stop_program_by_name("snap7drv")

        time.sleep(5)

        #验证
        result=is_process_running("snap7drv")
        assert True==result

    @allure_setup("DSFR-1434", is_async=False)
    def test_jira_summary_1434(self):
        summary, _ = get_jira_summary("DSFR-1434")
        # assert summary == "autotest验证dsfdrvctrl关闭驱动功能是否正常"

        #停止驱动
        driver_name="modbus"
        resp_dict=control_driver(driver_name,"stop")

        #结果信息
        message = {
                    'message': f"Driver {driver_name} stop successfully!",
                    "status": "success"
                }
        
        time.sleep(5)
        #验证
        assert resp_dict==message

        #验证
        result=get_program_status_by_name(driver_name)
        assert False==result
        
    @allure_setup("DSFR-1435", is_async=False)
    def test_jira_summary_1435(self):
        summary, _ = get_jira_summary("DSFR-1435")
        # assert summary == "autotest验证dsfdrvctrl启动驱动功能是否正常"

        #启动驱动
        driver_name="modbus"
        resp_dict=control_driver(driver_name,"run")

        #结果信息
        message = {
                    'message': f"Driver {driver_name} start successfully!",
                    "status": "success"
                }
        
        time.sleep(5)
        #验证
        assert resp_dict==message

        #验证
        result=is_process_running(driver_name)
        assert True==result

# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfdrvctrl.py"])