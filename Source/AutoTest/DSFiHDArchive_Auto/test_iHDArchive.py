import pytest
import psutil
import shutil
import sys
import time

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

sys.path.append("DSFiHDArchive_Auto/iHDSDK")       #将 iHDSDK/ 加入 Python 模块搜索路径
from iHDSDK import server, hyperdb #, security, record, dataprovider

g_ihd_process_name = 'dsfdataarchive'
g_ihd_server_ipaddr = '**************'
g_ihd_server_port = 5673
g_ihd_server_username = 'admin'
g_ihd_server_passwd = 'admin'
g_ihd_config_file_name = 'dsf_archive_tags.json'
g_ihd_config_original_file_name = 'dsf_archive_tags_ori.json'
g_ihd_config_modified_file_name = 'dsf_archive_tags_mod.json'

g_ihd_autotest_folder_name = 'DSFiHDArchive_Auto'

ihdapi = None

# 指定logging输出到stdout
logging.basicConfig(
    level=logging.INFO,
    stream=sys.stdout,
    format='%(asctime)s [%(levelname)s] %(message)s'
)

def GetDSFConfigPath():
    path = os.getenv('DR_ROOT')
    if path:
        path = os.path.join(path, "..", "projects", "defaultproject", "config")
        return path
    else:
        return None

def CopyFile(src: str, dst: str):
    try:
        shutil.copy(src, dst)
    except Exception as e:
        logging.error(f"Error copying file from {src} to {dst}: {e}")
        return False
    else:
        logging.info(f"File copied from {src} to {dst} successfully.")
        return True
    
def KillProcByName(name):
    for proc in psutil.process_iter(attrs=["pid", "name"]):
        if proc.info["name"] == name:
            logging.info(f"Killing {name} (pid={proc.pid})")
            proc.kill()


class TestSDK:
    def ConnectServer(self):
        self.myServer = server.Server(g_ihd_server_ipaddr, g_ihd_server_port, '', g_ihd_server_port)
        self.myServer.connect()
        self.myServer.login(g_ihd_server_username, g_ihd_server_passwd)
        self.myTagMgr = self.myServer.tag_mgr
        self.myDateProvider = self.myServer.data_provider

    def AddTag(self, tagname: str, tagtype: str):
        attr = {'tagname': tagname, 'tagtype': tagtype}
        try:
            self.myTagMgr.add_tag(**attr)
        except Exception as e:
            logging.error("add_tag error: ", e.errcode)
        else:
            logging.info("adding tag succeeds")

    def GetTag(self, tagname: str):
        try:
            tag_buf = self.myTagMgr.get_tag(tagname)
        except Exception as e:
            logging.error("get_tag error", e)
            return tag_buf, e.args
        else:
            logging.info("getting tag:", tagname, " succeeds")
            return tag_buf, [0]
    
    # ihd sdk会缓存之前测点的id，其他进程删除测点后会导致id无效，此时应调用此方法
    def GetTagWithoutIdCache(self, tagname: str):
        try:
            conds = [('tagname', '=', tagname)]
            iterate = self.myTagMgr.query_tags_cond(conds, 0XFFFFFFFFFFFFFFFF)
        except Exception as e:
            logging.error("query_tags_cond error", e)
            return iterate, e.args
        else:
            logging.info("getting tag:", tagname, " succeeds")
            return iterate.get_next(), [0]

    def Disconnet(self):
        try:
            self.myServer.disconnect()
        except Exception as e:
            logging.error("disconnect error: ", e.errcode)
        else:
            logging.info("disconnecting succeeds")

# 测试之前执行
def before_test():
    logging.info("Execute before the test starts")

    global ihdapi           #声明使用全局变量

    logging.info("ihd connect")
    ihdapi = TestSDK()
    ihdapi.ConnectServer()  #连接到iHD服务器

# 测试之后执行
def after_test():
    logging.info("Execute after the test ends")
    logging.info("recover dsfdataarchive config file")
    srcPath = os.path.join(os.getcwd(), g_ihd_autotest_folder_name, g_ihd_config_original_file_name)
    CopyFile(srcPath, os.path.join(GetDSFConfigPath(), g_ihd_config_file_name))
    KillProcByName(g_ihd_process_name)
    
    logging.info("ihd disconnect")
    ihdapi.Disconnet()


# 测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()   # 在测试开始前执行
    yield           # 这里是测试正在执行的地方
    after_test()    # 在测试结束后执行


# ## a dictionary for describing tag's type as digital format
# tag_type_dict = {'int8'    : 0,
#                  'int16'   : 1,
#                  'int32'   : 2,
#                  'float32' : 3,
#                  'float64' : 4,
#                  'digital' : 5,
#                  'string'  : 6,
#                  'blob'    : 7,
#                  }

# // Tag点类型
# enum HD3_TAG_TYPE {
# 	HD3_TAG_TYPE_MIN = 0,
# 	HD3_TAG_TYPE_INT8 = HD3_TAG_TYPE_MIN,
# 	HD3_TAG_TYPE_INT16,
# 	HD3_TAG_TYPE_INT32,
# 	HD3_TAG_TYPE_FLOAT32,
# 	HD3_TAG_TYPE_FLOAT64,
# 	HD3_TAG_TYPE_DIGITAL,
# 	HD3_TAG_TYPE_STRING,
# 	HD3_TAG_TYPE_BLOB,
# 	HD3_TAG_TYPE_MAX = HD3_TAG_TYPE_BLOB
# };

# // The unsigned type( and word) is not supported. Expand length
#     // 1byte
#     {"BOOL", {HD3_TAG_TYPE_DIGITAL}},
#     {"SINT", {HD3_TAG_TYPE_INT8}},
#     {"CHAR", {HD3_TAG_TYPE_INT8}},                     //
#     {"BYTE", {HD3_TAG_TYPE_INT8, HD3_TAG_TYPE_INT16}}, //
#     {"USINT", {HD3_TAG_TYPE_INT8, HD3_TAG_TYPE_INT16}},
#     // 2bytes
#     {"INT", {HD3_TAG_TYPE_INT16}},
#     {"UINT", {HD3_TAG_TYPE_INT16, HD3_TAG_TYPE_INT32}},
#     {"WORD", {HD3_TAG_TYPE_INT16, HD3_TAG_TYPE_INT32}}, //
#     // 4bytes
#     {"TIME", {HD3_TAG_TYPE_INT32}}, //
#     {"DINT", {HD3_TAG_TYPE_INT32}},
#     // float
#     {"REAL", {HD3_TAG_TYPE_FLOAT32}},
#     {"LREAL", {HD3_TAG_TYPE_FLOAT64}},
#     // others
#     {"STRING", {HD3_TAG_TYPE_STRING}}, //


# 测试用例
class TestArchiveService():
    # autotest验证DataArchive转储到iHD类型(BOOL)
    @allure_setup("DSFR-975", is_async=False)
    def test_jira_summary_ihd_bool(self):
        summary, _ = get_jira_summary("DSFR-975")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag("STD.TBOOL")
        record = tag.get_snapshot()
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'digital' and record.value == '1'


    # autotest验证DataArchive转储到iHD类型(BYTE)
    @allure_setup("DSFR-976", is_async=False)
    def test_jira_summary_ihd_byte(self):
        summary, _ = get_jira_summary("DSFR-976")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag("STD.TBYTE")
        record = tag.get_snapshot()
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'int16' and record.value == '1'


    # autotest验证DataArchive转储到iHD类型(SINT)
    @allure_setup("DSFR-977", is_async=False)
    def test_jira_summary_ihd_sint(self):
        summary, _ = get_jira_summary("DSFR-977")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag("STD.TSINT")
        record = tag.get_snapshot()
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'int8' and record.value == '1'


    # autotest验证DataArchive转储到iHD类型(USINT)
    @allure_setup("DSFR-978", is_async=False)
    def test_jira_summary_ihd_usint(self):
        summary, _ = get_jira_summary("DSFR-978")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag("STD.TUSINT")
        record = tag.get_snapshot()
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'int16' and record.value == '1'


    # autotest验证DataArchive转储到iHD类型(CHAR)
    @allure_setup("DSFR-979", is_async=False)
    def test_jira_summary_ihd_char(self):
        summary, _ = get_jira_summary("DSFR-979")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag("STD.TCHAR")
        record = tag.get_snapshot()
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'int8' and record.value == '49'


    # autotest验证DataArchive转储到iHD类型(INT)
    @allure_setup("DSFR-980", is_async=False)
    def test_jira_summary_ihd_int(self):
        summary, _ = get_jira_summary("DSFR-980")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag("STD.TINT")
        record = tag.get_snapshot()
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'int16' and record.value == '1'


    # autotest验证DataArchive转储到iHD类型(UINT)
    @allure_setup("DSFR-981", is_async=False)
    def test_jira_summary_ihd_uint(self):
        summary, _ = get_jira_summary("DSFR-981")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag("STD.TUINT")
        record = tag.get_snapshot()
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'int32' and record.value == '1'


    # autotest验证DataArchive转储到iHD类型(WORD)
    @allure_setup("DSFR-982", is_async=False)
    def test_jira_summary_ihd_word(self):
        summary, _ = get_jira_summary("DSFR-982")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag("STD.TWORD")
        record = tag.get_snapshot()
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'int32' and record.value == '1'


    # autotest验证DataArchive转储到iHD类型(TIME)
    @allure_setup("DSFR-983", is_async=False)
    def test_jira_summary_ihd_time(self):
        summary, _ = get_jira_summary("DSFR-983")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag("STD.TTIME")
        record = tag.get_snapshot()
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'int32' and record.value == '1'


    # autotest验证DataArchive转储到iHD类型(DINT)
    @allure_setup("DSFR-984", is_async=False)
    def test_jira_summary_ihd_dint(self):
        summary, _ = get_jira_summary("DSFR-984")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag("STD.TDINT")
        record = tag.get_snapshot()
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'int32' and record.value == '1'


    # autotest验证DataArchive转储到iHD类型(REAL)
    @allure_setup("DSFR-985", is_async=False)
    def test_jira_summary_ihd_real(self):
        summary, _ = get_jira_summary("DSFR-985")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag("STD.TREAL")
        record = tag.get_snapshot()
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'float32' and record.value == '1.0'


    # autotest验证DataArchive转储到iHD类型(LREAL)
    @allure_setup("DSFR-986", is_async=False)
    def test_jira_summary_ihd_lreal(self):
        summary, _ = get_jira_summary("DSFR-986")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag("STD.TLREAL")
        record = tag.get_snapshot()
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'float64' and record.value == '1.0'


    # # autotest验证DataArchive转储到iHD类型(STRING)
    # @allure_setup("DSFR-987", is_async=False)
    # def test_jira_summary_ihd_string(self):
    #     summary, _ = get_jira_summary("DSFR-987")
    #     logging.info(f"Summary: {summary}")

    #     tag = ihdapi.GetTag("STD.TSTRING")
    #     assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'string'


    # autotest验证DataArchive配置测点类型改变后重新生成
    @allure_setup("DSFR-1014", is_async=False)
    def test_jira_summary_ihd_change_tagtype(self):
        summary, _ = get_jira_summary("DSFR-1014")
        logging.info(f"Summary: {summary}")
        
        logging.info("modify dsfdataarchive config file...")
        srcPath = os.path.join(os.getcwd(), g_ihd_autotest_folder_name, g_ihd_config_modified_file_name)
        dstPath = os.path.join(GetDSFConfigPath(), g_ihd_config_file_name)
        CopyFile(srcPath, dstPath)
        logging.info("restart dsfdataarchive service...")
        KillProcByName(g_ihd_process_name)
        ihdapi.Disconnet()
        time.sleep(15)
        ihdapi.ConnectServer()
        tag, _ = ihdapi.GetTagWithoutIdCache('STD.TDINT')
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and hyperdb.tag_type_trans_dict[tag.tagtype] == 'int16'


    # autotest验证DataArchive配置测点属性改变后同步属性
    @allure_setup("DSFR-1015", is_async=False)
    def test_jira_summary_ihd_change_property(self):
        summary, _ = get_jira_summary("DSFR-1015")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag('STD.TLREAL')
        assert tag.compdev == 10 and tag.compmaxtime == 28000 and tag.tagtype == 'float64'


    #autotest验证DataArchive初始化时新增测点
    @allure_setup("DSFR-1017", is_async=False)
    def test_jira_summary_ihd_add_tag(self):
        summary, _ = get_jira_summary("DSFR-1017")
        logging.info(f"Summary: {summary}")

        tag, _ = ihdapi.GetTag("STD.TBYTE1")
        assert tag.compdev == 0 and tag.compmaxtime == 28800 and tag.tagtype == 'int16'


    #autotest验证DataArchive配置修改后删除测点
    @allure_setup("DSFR-1018", is_async=False)
    def test_jira_summary_ihd_del_tag(self):
        summary, _ = get_jira_summary("DSFR-1018")
        logging.info(f"Summary: {summary}")

        tag, e = ihdapi.GetTagWithoutIdCache("STD.TBYTE")
        assert tag == None
