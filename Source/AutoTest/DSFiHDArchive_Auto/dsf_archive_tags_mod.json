[{"name": "iHyperDB_1", "address": "**************", "port": 5673, "bak_address": "", "bak_port": 0, "timeout": 5, "username": "admin", "password": "admin", "tags": [{"tagname": "STD::TDINT", "type": "INT", "archive_tagname": "STD.TDINT", "annotation": "", "compress_deviation": 0, "compress_max_time": 28800, "interval": 1000, "after_changes": false}, {"tagname": "STD::TLREAL", "type": "LREAL", "archive_tagname": "STD.TLREAL", "annotation": "", "compress_deviation": 10, "compress_max_time": 28000, "interval": 1000, "after_changes": false}, {"tagname": "STD::TBYTE", "type": "BYTE", "archive_tagname": "STD.TBYTE1", "annotation": "", "compress_deviation": 0, "compress_max_time": 28800, "interval": 1000, "after_changes": false}, {"tagname": "STD::TCHAR", "type": "CHAR", "archive_tagname": "STD.TCHAR", "annotation": "", "compress_deviation": 0, "compress_max_time": 28800, "interval": 1000, "after_changes": false}, {"tagname": "STD::TSINT", "type": "SINT", "archive_tagname": "STD.TSINT", "annotation": "", "compress_deviation": 0, "compress_max_time": 28800, "interval": 1000, "after_changes": false}, {"tagname": "STD::TBOOL", "type": "BOOL", "archive_tagname": "STD.TBOOL", "annotation": "", "compress_deviation": 0, "compress_max_time": 28800, "interval": 1000, "after_changes": false}, {"tagname": "STD::TREAL", "type": "REAL", "archive_tagname": "STD.TREAL", "annotation": "", "compress_deviation": 0, "compress_max_time": 28800, "interval": 1000, "after_changes": false}, {"tagname": "STD::TUINT", "type": "UINT", "archive_tagname": "STD.TUINT", "annotation": "", "compress_deviation": 0, "compress_max_time": 28800, "interval": 1000, "after_changes": false}, {"tagname": "STD::TINT", "type": "INT", "archive_tagname": "STD.TINT", "annotation": "", "compress_deviation": 0, "compress_max_time": 28800, "interval": 1000, "after_changes": false}, {"tagname": "STD::TUSINT", "type": "USINT", "archive_tagname": "STD.TUSINT", "annotation": "", "compress_deviation": 0, "compress_max_time": 28800, "interval": 1000, "after_changes": false}, {"tagname": "STD::TWORD", "type": "WORD", "archive_tagname": "STD.TWORD", "annotation": "", "compress_deviation": 0, "compress_max_time": 28800, "interval": 1000, "after_changes": false}, {"tagname": "STD::TTIME", "type": "TIME", "archive_tagname": "STD.TTIME", "annotation": "", "compress_deviation": 0, "compress_max_time": 28800, "interval": 1000, "after_changes": false}]}]