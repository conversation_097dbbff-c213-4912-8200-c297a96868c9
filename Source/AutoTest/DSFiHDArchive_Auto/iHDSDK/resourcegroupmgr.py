#!/usr/bin/env python
# -*- coding: gb2312 -*-

import ctypes
from ctypes import *
import hyperdb 
import resourcegroup
import sys

## ResourceGroupMgr is a operator class through which programmers can use to do all
# operators about ReousourceGroup.
# @note Programmers needn't create a ResourceGroupMgr object. Instead, ResourceGroupMgr object
# can be accessed from Server class
class ResourceGroupMgr(object):
    
    ## Add a group to the server
    # @param name: group name
    # @param desc: description of a group
    def add_resource_group(self, name, desc, access_type):
        if type(name) != str or type(desc) != str or type(access_type) != int:
            raise TypeError
        if not name.strip():
            raise TypeError
        resgroup_buf = hyperdb.HD3ResourceGroup()
        resgroup_buf.szGroupName = name.encode('utf-8')
        resgroup_buf.szGroupDesc = desc.encode('utf-8')
        resgroup_buf.nAccessType = access_type
        resgroup_buf.nParentId = 0
        resgroup_buf.nLevel = 0
        
        hyperdb.api.sc3_add_resource_group.argtypes = [POINTER(hyperdb.HD3ResourceGroup)]
        retcode = hyperdb.api.sc3_add_resource_group(resgroup_buf)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        
    ## Delete a group from the server
    # @param name: group name
    def delete_resource_group(self, name):
        if type(name) != str :
            raise TypeError

        hyperdb.api.sc3_delete_resource_group.argtypes = [c_char_p]
        retcode = hyperdb.api.sc3_delete_resource_group(name.encode('utf-8'))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
    
    ## Get all groups from the server
    # @return: a List of group objects
    def get_all_resource_groups(self):  
        resgroup = hyperdb.HD3ResourceGroup()
        hditer = c_void_p()
        
        hyperdb.api.sc3_query_all_resource_groups.argtypes = [POINTER(c_void_p)]  
        retcode = hyperdb.api.sc3_query_all_resource_groups(byref(hditer))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        
        iterate = hyperdb.Iterate(hditer, resgroup)
        return iterate
    

    ## Modify the resource group
    # @param : resource group name, new description of the user group, new access type
    def modify_resource_group(self, name, desc, access_type):
        if type(name) != str or type(desc) != str or type(access_type) != int:
            raise TypeError
        if not name.strip():
            raise TypeError
        resgroup_buf = hyperdb.HD3ResourceGroup()
        resgroup_buf.szGroupName = name.encode('utf-8')
        resgroup_buf.szGroupDesc = desc.encode('utf-8')
        resgroup_buf.nAccessType = access_type
        resgroup_buf.nParentId = 0
        resgroup_buf.nLevel = 0

        hyperdb.api.sc3_modify_resource_group.argtypes = [POINTER(hyperdb.HD3ResourceGroup)]
        retcode = hyperdb.api.sc3_modify_resource_group(resgroup_buf)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        
    def add_resourcegroup_to_group(self, resourcegroup_name, group_name):
        if type(resourcegroup_name) != str or type(group_name) != str:
            raise TypeError
        
        hyperdb.api.sc3_add_resource_group_to_group.argtypes = [c_char_p, c_char_p]
        retcode = hyperdb.api.sc3_add_resource_group_to_group(resourcegroup_name.encode('utf-8'), group_name.encode('utf-8'))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

    def delete_resourcegroup_from_group(self, resourcegroup_name, group_name):
        if type(resourcegroup_name) != str or type(group_name) != str:
            raise TypeError
        
        hyperdb.api.sc3_delete_resource_group_from_group.argtypes = [c_char_p, c_char_p]
        retcode = hyperdb.api.sc3_delete_resource_group_from_group(resourcegroup_name.encode('utf-8'), group_name.encode('utf-8'))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        
    def get_resource_groups_of_group(self, group_name):  
        resgroup = hyperdb.HD3ResourceGroup()
        hditer = c_void_p()
        
        hyperdb.api.sc3_query_resource_groups_of_group.argtypes = [c_char_p, POINTER(c_void_p)]  
        retcode = hyperdb.api.sc3_query_resource_groups_of_group(group_name.encode('utf-8'), byref(hditer))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        
        iterate = hyperdb.Iterate(hditer, resgroup)
        return iterate

    def add_resource_group_to_tags(self, num, tagnames, resource_group_name):
        nTagNum = c_int32(num)
        pTagIDArray = (num * c_uint32)()
        pErrCodeArray1 = (num * c_int32)()
        class TagNameArray(ctypes.Structure):
            _fields_ = [("names", ctypes.c_char * (128 * 2) * num)]

        tag_names = TagNameArray()

        for i in range(num):
            tag_name = tagnames[i].encode('utf-8')[:128]
            ctypes.memmove(tag_names.names[i], tag_name, len(tag_name))

        hyperdb.api.tag3_query_ids_by_names.argtypes = [c_int32, ctypes.c_char * (128 * 2) * num, POINTER(c_uint32),
                                                        POINTER(c_int32)]
        retcode = hyperdb.api.tag3_query_ids_by_names(nTagNum, tag_names.names, pTagIDArray, pErrCodeArray1)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

        pErrCodeArray2 = (num * c_int32)()
        class ResourceGroupNameArray(ctypes.Structure):
            _fields_ = [("names", ctypes.c_char * (32 * 2) * num)]
        resourcegroup_names = ResourceGroupNameArray()
        for i in range(num):
            resourcegroup_name = resource_group_name.encode('utf-8')[:32]
            ctypes.memmove(resourcegroup_names.names[i], resourcegroup_name, len(resourcegroup_name))

        hyperdb.api.sc3_add_resource_groups_to_tags.argtypes = [c_int32, ctypes.c_char * (32 * 2) * num, POINTER(c_uint32), POINTER(c_int32)]
        retcode = hyperdb.api.sc3_add_resource_groups_to_tags(nTagNum, resourcegroup_names.names, pTagIDArray, pErrCodeArray2)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

    def delete_resource_group_from_tags(self, num, tagnames, resource_group_name):
        nTagNum = c_int32(num)
        pTagIDArray = (num * c_uint32)()
        pErrCodeArray1 = (num * c_int32)()
        class TagNameArray(ctypes.Structure):
            _fields_ = [("names", ctypes.c_char * (128 * 2) * num)]

        tag_names = TagNameArray()

        for i in range(num):
            tag_name = tagnames[i].encode('utf-8')[:128]
            ctypes.memmove(tag_names.names[i], tag_name, len(tag_name))

        hyperdb.api.tag3_query_ids_by_names.argtypes = [c_int32, ctypes.c_char * (128 * 2) * num, POINTER(c_uint32),
                                                        POINTER(c_int32)]
        retcode = hyperdb.api.tag3_query_ids_by_names(nTagNum, tag_names.names, pTagIDArray, pErrCodeArray1)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

        pErrCodeArray2 = (num * c_int32)()
        class ResourceGroupNameArray(ctypes.Structure):
            _fields_ = [("names", ctypes.c_char * (32 * 2) * num)]
        resourcegroup_names = ResourceGroupNameArray()
        for i in range(num):
            resourcegroup_name = resource_group_name.encode('utf-8')[:32]
            ctypes.memmove(resourcegroup_names.names[i], resourcegroup_name, len(resourcegroup_name))
        hyperdb.api.sc3_delete_resource_groups_from_tags.argtypes = [c_int32, ctypes.c_char * (32 * 2) * num, POINTER(c_uint32), POINTER(c_int32)]
        retcode = hyperdb.api.sc3_delete_resource_groups_from_tags(nTagNum, resourcegroup_names.names, pTagIDArray, pErrCodeArray2)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
