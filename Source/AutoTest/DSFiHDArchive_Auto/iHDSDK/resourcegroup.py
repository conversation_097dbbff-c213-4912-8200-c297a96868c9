#!/usr/bin/env python
# -*- coding: gb2312 -*-

## Operator for a special Security
# @note: Programmers needn't create a Security object. Instead, Trust object
# can be accessed from SecurityMgr class
class Resourcegroup(object):
    def __init__(self, resourcegroupname, resourcegroupdesc, accesstype):
        self.resourcegroupname = resourcegroupname
        self.resourcegroupdesc = resourcegroupdesc
        self.accesstype = accesstype