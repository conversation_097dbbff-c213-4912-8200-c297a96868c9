#!/usr/bin/env python
import ctypes
from ctypes import *
import hyperdb
import tag
import sys
import record
from datetime import datetime
import time


## TagMgr is a operator class through which programmers can use to do all
# operators about tag.
# @note: Programmers needn't create a TagMgr object. Instead, TagMgr object
# can be accessed from Server class
class TagMgr(object):
    def __init__(self, server):
        self.server = server

    # add a new tag to server
    # @param attributes: attributes is dictionary, such as 'tagname = 'testtag', tagtype = 'int8'
    # @note: the input attributes should include 'tagname' and 'tagtype', the attributes'names can be
    # seen in tag.py
    def add_tag(self, **attributes):
        tag_prop_buf = hyperdb.TagProp()
        if 'tagname' not in attributes or 'tagtype' not in attributes:
            raise ArgumentError

        comm_mask = 0
        tag_ext_mask = 0

        tag_prop_buf.szTagName = attributes['tagname'].encode('utf-8')
        if not tag_prop_buf.szTagName.strip():
            raise ArgumentError
        comm_mask = hyperdb.get_comm_mask('tagname', comm_mask)

        try:
            tag_prop_buf.nTagType = hyperdb.tag_type_dict[attributes['tagtype']]
        except:
            raise ArgumentError
        comm_mask = hyperdb.get_comm_mask('tagtype', comm_mask)

        if 'archiving' in attributes:
            tag_prop_buf.bArchiving = attributes['archiving']
            comm_mask = hyperdb.get_comm_mask('archiving', comm_mask)

        if 'compdev' in attributes:
            tag_prop_buf.fCompDev = attributes['compdev']
            comm_mask = hyperdb.get_comm_mask('compdev', comm_mask)
        if 'compmaxtime' in attributes:
            tag_prop_buf.nCompMaxTime = attributes['compmaxtime']
            comm_mask = hyperdb.get_comm_mask('compmaxtime', comm_mask)
        if 'comptype' in attributes:
            tag_prop_buf.nCompType = attributes['comptype']
            comm_mask = hyperdb.get_comm_mask('comptype', comm_mask)

        if 'recaccess' in attributes:
            tag_prop_buf.nRecAccess = attributes['recaccess']
            comm_mask = hyperdb.get_comm_mask('recaccess', comm_mask)
        if 'recnormalgroup' in attributes:
            tag_prop_buf.szRecNormalGroup = attributes['recnormalgroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('recnormalgroup', comm_mask)
        if 'recownergroup' in attributes:
            tag_prop_buf.szRecOwnerGroup = attributes['recownergroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('recownergroup', comm_mask)

        if 'descriptor' in attributes:
            tag_prop_buf.szDescriptor = attributes['descriptor'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)
        if 'digitalset' in attributes:
            tag_prop_buf.szDigitalSet = attributes['digitalset'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('digitalset', comm_mask)
        if 'engunit' in attributes:
            tag_prop_buf.szEngUnits = attributes['engunit'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('engunit', comm_mask)
        if 'excmaxtime' in attributes:
            tag_prop_buf.nExcMaxTime = attributes['excmaxtime']
            comm_mask = hyperdb.get_comm_mask('excmaxtime', comm_mask)
        if 'excdev' in attributes:
            tag_prop_buf.fExcDev = attributes['excdev']
            comm_mask = hyperdb.get_comm_mask('excdev', comm_mask)

        if 'tagaccess' in attributes:
            tag_prop_buf.nTagAccess = attributes['tagaccess']
            comm_mask = hyperdb.get_comm_mask('tagaccess', comm_mask)
        if 'tagnormalgroup' in attributes:
            tag_prop_buf.szTagNormalGroup = attributes['tagnormalgroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('tagnormalgroup', comm_mask)
        if 'tagownergroup' in attributes:
            tag_prop_buf.szTagOwnerGroup = attributes['tagownergroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('tagownergroup', comm_mask)

        if 'scan' in attributes:
            tag_prop_buf.bScan = attributes['scan']
            comm_mask = hyperdb.get_comm_mask('scan', comm_mask)
        if 'span' in attributes:
            tag_prop_buf.fSpan = attributes['span']
            comm_mask = hyperdb.get_comm_mask('span', comm_mask)
        if 'minvalue' in attributes:
            tag_prop_buf.fMinValue = attributes['minvalue']
            comm_mask = hyperdb.get_comm_mask('minvalue', comm_mask)
        if 'tagavaliable' in attributes:
            tag_prop_buf.nTagAvaliable = attributes['tagavaliable']
            comm_mask = hyperdb.get_comm_mask('tagavaliable', comm_mask)
        if 'opcstate' in attributes:
            tag_prop_buf.nOpcState = attributes['opcstate']
            comm_mask = hyperdb.get_comm_mask('opcstate', comm_mask)

        if 'imvstring' in attributes:
            tag_prop_buf.szImvString = attributes['imvstring'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('imvstring', comm_mask)
        if 'extstring1' in attributes:
            tag_prop_buf.szExtString1 = attributes['extstring1'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('extstring1', comm_mask)
        if 'extstring2' in attributes:
            tag_prop_buf.szExtString2 = attributes['extstring2'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('extstring2', comm_mask)

        if 'param1' in attributes:
            tag_prop_buf.nParam1 = attributes['param1']
            comm_mask = hyperdb.get_comm_mask('param1', comm_mask)
        if 'param2' in attributes:
            tag_prop_buf.nParam2 = attributes['param2']
            comm_mask = hyperdb.get_comm_mask('param2', comm_mask)
        if 'param3' in attributes:
            tag_prop_buf.nParam3 = attributes['param3']
            comm_mask = hyperdb.get_comm_mask('param3', comm_mask)
        if 'param4' in attributes:
            tag_prop_buf.nParam4 = attributes['param4']
            comm_mask = hyperdb.get_comm_mask('param4', comm_mask)
        if 'param5' in attributes:
            tag_prop_buf.nParam5 = attributes['param5']
            comm_mask = hyperdb.get_comm_mask('param5', comm_mask)

        if 'instrumentaddress' in attributes:
            tag_prop_buf.szInstrumentAddress = attributes['instrumentaddress'].encode('utf-8')
            tag_ext_mask = hyperdb.get_tag_ext_mask('instrumentaddress', tag_ext_mask)
        if 'instrumentaddresstype' in attributes:
            tag_prop_buf.nInstrumentAddressType = c_uint8(attributes['instrumentaddresstype'])
            tag_ext_mask = hyperdb.get_tag_ext_mask('instrumentaddresstype', tag_ext_mask)
        if 'collectorname' in attributes:
            tag_prop_buf.szCollectorName = attributes['collectorname'].encode('utf-8')
            tag_ext_mask = hyperdb.get_tag_ext_mask('collectorname', tag_ext_mask)

        if 'devicename' in attributes:
            tag_prop_buf.szDeviceName = attributes['devicename'].encode('utf-8')
            tag_ext_mask = hyperdb.get_tag_ext_mask('devicename', tag_ext_mask)

        if 'scangroupname' in attributes:
            tag_prop_buf.szScanGroupName = attributes['scangroupname'].encode('utf-8')
            tag_ext_mask = hyperdb.get_tag_ext_mask('scangroupname', tag_ext_mask)

        if 'dataorder' in attributes:
            tag_prop_buf.nDataOrder = attributes['dataorder']
            tag_ext_mask = hyperdb.get_tag_ext_mask('dataorder', tag_ext_mask)

        mask = hyperdb.HD3Mask()
        mask.commmask = c_int64(comm_mask)
        mask.extmask = c_int64(tag_ext_mask)

        tagid = c_uint32()

        hyperdb.api.pt3_add_tag.argtypes = [POINTER(hyperdb.TagProp), POINTER(hyperdb.HD3Mask), c_char_p, c_void_p]
        retcode = hyperdb.api.pt3_add_tag(byref(tag_prop_buf), byref(mask), None, byref(tagid))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return tagid

    ##添加计算点
    # @param attributes: 字典
    # @note1: 输入的属性必须包括'tagname','tagtype','sourcetagname','calcmode','calcperiod'以及'calcexpr';
    # 具体属性见CalcTagProp
    # @note2:计算点模式
    # HD3_CALC_MODE = {
    # 'HD3_CALC_MODE_MIN': 0,
    # 'HD3_CALC_MODE_POLL': 1,  # 计算模式——轮询
    # 'HD3_CALC_MODE_NOTIFY': 1, # 计算模式——通知
    # 'HD3_CALC_MODE_MAX': 1 # 计算模式的最大值，用来校验
    # }
    # @return: calc_tagid
    def add_calc_tag(self, **attributes):
        calc_tag_prop_buf = hyperdb.CalcTagProp()
        if 'tagname' not in attributes or 'tagtype' not in attributes:
            raise ArgumentError

        comm_mask = 0
        calc_tag_ext_mask = 0

        calc_tag_prop_buf.szTagName = attributes['tagname'].encode('utf-8')
        if not calc_tag_prop_buf.szTagName.strip():
            raise ArgumentError
        comm_mask = hyperdb.get_comm_mask('tagname', comm_mask)

        try:
            calc_tag_prop_buf.nTagType = hyperdb.tag_type_dict[attributes['tagtype']]
        except:
            raise ArgumentError
        comm_mask = hyperdb.get_comm_mask('tagtype', comm_mask)

        if 'archiving' in attributes:
            calc_tag_prop_buf.bArchiving = attributes['archiving']
            comm_mask = hyperdb.get_comm_mask('archiving', comm_mask)

        if 'changedate' in attributes:
            calc_tag_prop_buf.nChangeDate = attributes['changedate']
            comm_mask = hyperdb.get_comm_mask('changedate', comm_mask)
        if 'changer' in attributes:
            calc_tag_prop_buf.szChanger = attributes['changer'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('changer', comm_mask)

        if 'compdev' in attributes:
            calc_tag_prop_buf.fCompDev = attributes['compdev']
            comm_mask = hyperdb.get_comm_mask('compdev', comm_mask)
        if 'compmaxtime' in attributes:
            calc_tag_prop_buf.nCompMaxTime = attributes['compmaxtime']
            comm_mask = hyperdb.get_comm_mask('compmaxtime', comm_mask)
        if 'comptype' in attributes:
            calc_tag_prop_buf.nCompType = attributes['comptype']
            comm_mask = hyperdb.get_comm_mask('comptype', comm_mask)

        if 'creationdate' in attributes:
            calc_tag_prop_buf.nCreationDate = attributes['creationdate']
            comm_mask = hyperdb.get_comm_mask('creationdate', comm_mask)
        if 'creator' in attributes:
            calc_tag_prop_buf.szCreator = attributes['creator'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('creator', comm_mask)

        if 'recaccess' in attributes:
            calc_tag_prop_buf.nRecAccess = attributes['recaccess']
            comm_mask = hyperdb.get_comm_mask('recaccess', comm_mask)
        if 'recnormalgroup' in attributes:
            calc_tag_prop_buf.szRecNormalGroup = attributes['recnormalgroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('recnormalgroup', comm_mask)
        if 'recownergroup' in attributes:
            calc_tag_prop_buf.szRecOwnerGroup = attributes['recownergroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('recownergroup', comm_mask)

        if 'descriptor' in attributes:
            calc_tag_prop_buf.szDescriptor = attributes['descriptor'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)
        if 'digitalset' in attributes:
            calc_tag_prop_buf.szDigitalSet = attributes['digitalset'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('digitalset', comm_mask)
        if 'engunit' in attributes:
            calc_tag_prop_buf.szEngUnits = attributes['engunit'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('engunit', comm_mask)
        if 'excmaxtime' in attributes:
            calc_tag_prop_buf.fExcMaxTime = attributes['excmaxtime']
            comm_mask = hyperdb.get_comm_mask('excmaxtime', comm_mask)
        if 'excdev' in attributes:
            calc_tag_prop_buf.fExcDev = attributes['excdev']
            comm_mask = hyperdb.get_comm_mask('excdev', comm_mask)

        if 'tagid' in attributes:
            calc_tag_prop_buf.nTagID = attributes['tagid']
            comm_mask = hyperdb.get_comm_mask('tagid', comm_mask)
        if 'tagaccess' in attributes:
            calc_tag_prop_buf.nTagAccess = attributes['tagaccess']
            comm_mask = hyperdb.get_comm_mask('tagaccess', comm_mask)
        if 'tagnormalgroup' in attributes:
            calc_tag_prop_buf.szTagNormalGroup = attributes['tagnormalgroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('tagnormalgroup', comm_mask)
        if 'tagownergroup' in attributes:
            calc_tag_prop_buf.szTagOwnerGroup = attributes['tagownergroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('tagownergroup', comm_mask)

        if 'scan' in attributes:
            calc_tag_prop_buf.bScan = attributes['scan']
            comm_mask = hyperdb.get_comm_mask('scan', comm_mask)
        if 'span' in attributes:
            calc_tag_prop_buf.fSpan = attributes['span']
            comm_mask = hyperdb.get_comm_mask('span', comm_mask)
        if 'minvalue' in attributes:
            calc_tag_prop_buf.fMinValue = attributes['minvalue']
            comm_mask = hyperdb.get_comm_mask('minvalue', comm_mask)
        if 'tagavaliable' in attributes:
            calc_tag_prop_buf.nTagAvaliable = attributes['tagavaliable']
            comm_mask = hyperdb.get_comm_mask('tagavaliable', comm_mask)
        if 'tagclass' in attributes:
            calc_tag_prop_buf.nTagClass = attributes['tagclass']
            comm_mask = hyperdb.get_comm_mask('tagclass', comm_mask)

        if 'imvstring' in attributes:
            calc_tag_prop_buf.szImvString = attributes['imvstring'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('imvstring', comm_mask)
        if 'extstring1' in attributes:
            calc_tag_prop_buf.szExtString1 = attributes['extstring1'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('extstring1', comm_mask)
        if 'extstring2' in attributes:
            calc_tag_prop_buf.szExtString2 = attributes['extstring2'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('extstring2', comm_mask)

        if 'param1' in attributes:
            calc_tag_prop_buf.nParam1 = attributes['param1']
            comm_mask = hyperdb.get_comm_mask('param1', comm_mask)
        if 'param2' in attributes:
            calc_tag_prop_buf.nParam2 = attributes['param2']
            comm_mask = hyperdb.get_comm_mask('param2', comm_mask)
        if 'param3' in attributes:
            calc_tag_prop_buf.nParam3 = attributes['param3']
            comm_mask = hyperdb.get_comm_mask('param3', comm_mask)
        if 'param4' in attributes:
            calc_tag_prop_buf.nParam4 = attributes['param4']
            comm_mask = hyperdb.get_comm_mask('param4', comm_mask)
        if 'param5' in attributes:
            calc_tag_prop_buf.nParam5 = attributes['param5']
            comm_mask = hyperdb.get_comm_mask('param5', comm_mask)
        if 'opcstate' in attributes:
            calc_tag_prop_buf.nOpcState = attributes['opcstate']
            comm_mask = hyperdb.get_comm_mask('opcstate', comm_mask)

        if 'calcmode' in attributes:
            calc_tag_prop_buf.nCalcMode = attributes['calcmode']
            calc_tag_ext_mask = hyperdb.get_calc_ext_mask('calcmode', calc_tag_ext_mask)
        if 'calcperiod' in attributes:
            calc_tag_prop_buf.nCalcPeriod = attributes['calcperiod']
            calc_tag_ext_mask = hyperdb.get_calc_ext_mask('calcperiod', calc_tag_ext_mask)
        if 'calcexpr' in attributes:
            calc_tag_prop_buf.szCalcExpr = attributes['calcexpr'].encode('utf-8')
            calc_tag_ext_mask = hyperdb.get_calc_ext_mask('calcexpr', calc_tag_ext_mask)
        if 'calchistory' in attributes:
            calc_tag_prop_buf.bCalcHistory = attributes['calchistory']
            calc_tag_ext_mask = hyperdb.get_calc_ext_mask('calchistory', calc_tag_ext_mask)
        if 'calcstartdate' in attributes:
            calc_tag_prop_buf.nCalcStartDate = attributes['calcstartdate']
            calc_tag_ext_mask = hyperdb.get_calc_ext_mask('calcstartdate', calc_tag_ext_mask)
        if 'calcenddate' in attributes:
            calc_tag_prop_buf.nCalcEndDate = attributes['calcenddate']
            calc_tag_ext_mask = hyperdb.get_calc_ext_mask('calcenddate', calc_tag_ext_mask)
        if 'haveendtime' in attributes:
            calc_tag_prop_buf.bHaveEndTime = attributes['haveendtime']
            calc_tag_ext_mask = hyperdb.get_calc_ext_mask('haveendtime', calc_tag_ext_mask)
        if 'sourcetagname' in attributes:
            calc_tag_prop_buf.szSourceTagName = attributes['sourcetagname'].encode('utf-8')
            calc_tag_ext_mask = hyperdb.get_calc_ext_mask('sourcetagname', calc_tag_ext_mask)

        mask = hyperdb.HD3Mask()
        mask.commmask = c_int64(comm_mask)
        mask.extmask = c_int64(calc_tag_ext_mask)

        calc_tagid = c_uint32()

        hyperdb.api.ca3_add_tag.argtypes = [POINTER(hyperdb.CalcTagProp), POINTER(hyperdb.HD3Mask), c_char_p, c_void_p]
        retcode = hyperdb.api.ca3_add_tag(byref(calc_tag_prop_buf), byref(mask), None, byref(calc_tagid))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return calc_tagid

    ## modify Tag's properties
    # @param attributes: attributes is a dictionary including attributes be set to a tag
    # @note: the dictionary must follow special formation,such as 'tagname = 'python',tagtype = 'int8', descriptor = 'pythontest',
    # some properties cann't be modified,such as 'tagname','tagid'
    def modify_tag_attributes(self, **attributes):
        tag_prop_buf = hyperdb.TagProp()
        comm_mask = 0
        ext_mask = 0
        tagid = c_uint32()

        if 'tagid' in attributes:
            tagid = attributes['tagid']
        if 'tagname' in attributes:
            tagname = attributes['tagname']
            tagid = self.get_tag(tagname).tagid

        # comm attributes
        if 'archiving' in attributes:
            tag_prop_buf.bArchiving = int(attributes['archiving'])
            comm_mask = hyperdb.get_comm_mask('archiving', comm_mask)

        if 'compdev' in attributes:
            tag_prop_buf.fCompDev = attributes['compdev']
            comm_mask = hyperdb.get_comm_mask('compdev', comm_mask)
        if 'compmaxtime' in attributes:
            tag_prop_buf.nCompMaxTime = attributes['compmaxtime']
            comm_mask = hyperdb.get_comm_mask('compmaxtime', comm_mask)
        if 'comptype' in attributes:
            tag_prop_buf.nCompType = attributes['comptype']
            comm_mask = hyperdb.get_comm_mask('comptype', comm_mask)

        if 'recaccess' in attributes:
            tag_prop_buf.nRecAccess = attributes['recaccess']
            comm_mask = hyperdb.get_comm_mask('recaccess', comm_mask)
        if 'recnormalgroup' in attributes:
            tag_prop_buf.szRecNormalGroup = attributes['recnormalgroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('recnormalgroup', comm_mask)
        if 'recownergroup' in attributes:
            tag_prop_buf.szRecOwnerGroup = attributes['recownergroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('recownergroup', comm_mask)

        if 'descriptor' in attributes:
            tag_prop_buf.szDescriptor = attributes['descriptor'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)
        if 'digitalset' in attributes:
            tag_prop_buf.szDigitalSet = attributes['digitalset'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('digitalset', comm_mask)
        if 'engunit' in attributes:
            tag_prop_buf.szEngUnits = attributes['engunit'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('engunit', comm_mask)
        if 'excmaxtime' in attributes:
            tag_prop_buf.nExcMaxTime = attributes['excmaxtime']
            comm_mask = hyperdb.get_comm_mask('excmaxtime', comm_mask)
        if 'excdev' in attributes:
            tag_prop_buf.fExcDev = attributes['excdev']
            comm_mask = hyperdb.get_comm_mask('excdev', comm_mask)

        if 'tagaccess' in attributes:
            tag_prop_buf.nTagAccess = attributes['tagaccess']
            comm_mask = hyperdb.get_comm_mask('tagaccess', comm_mask)
        if 'tagnormalgroup' in attributes:
            tag_prop_buf.szTagNormalGroup = attributes['tagnormalgroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('tagnormalgroup', comm_mask)
        if 'tagownergroup' in attributes:
            tag_prop_buf.szTagOwnerGroup = attributes['tagownergroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('tagownergroup', comm_mask)

        if 'scan' in attributes:
            tag_prop_buf.bScan = attributes['scan']
            comm_mask = hyperdb.get_comm_mask('scan', comm_mask)
        if 'span' in attributes:
            tag_prop_buf.fSpan = attributes['span']
            comm_mask = hyperdb.get_comm_mask('span', comm_mask)
        if 'minvalue' in attributes:
            tag_prop_buf.fMinValue = attributes['minvalue']
            comm_mask = hyperdb.get_comm_mask('minvalue', comm_mask)

        if 'tagavaliable' in attributes:
            tag_prop_buf.nTagAvaliable = attributes['tagavaliable']
            comm_mask = hyperdb.get_comm_mask('tagavaliable', comm_mask)
        if 'opcstate' in attributes:
            tag_prop_buf.nOpcState = attributes['opcstate']
            comm_mask = hyperdb.get_comm_mask('opcstate', comm_mask)

        if 'imvstring' in attributes:
            tag_prop_buf.szImvString = attributes['imvstring'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('imvstring', comm_mask)

        if 'extstring1' in attributes:
            tag_prop_buf.szExtString1 = attributes['extstring1'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('extstring1', comm_mask)

        if 'extstring2' in attributes:
            tag_prop_buf.szExtString2 = attributes['extstring2'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('extstring2', comm_mask)

        if 'param1' in attributes:
            tag_prop_buf.nParam1 = attributes['param1']
            comm_mask = hyperdb.get_comm_mask('param1', comm_mask)
        if 'param2' in attributes:
            tag_prop_buf.nParam2 = attributes['param2']
            comm_mask = hyperdb.get_comm_mask('param2', comm_mask)
        if 'param3' in attributes:
            tag_prop_buf.nParam3 = attributes['param3']
            comm_mask = hyperdb.get_comm_mask('param3', comm_mask)
        if 'param4' in attributes:
            tag_prop_buf.nParam4 = attributes['param4']
            comm_mask = hyperdb.get_comm_mask('param4', comm_mask)
        if 'param5' in attributes:
            tag_prop_buf.nParam5 = attributes['param5']
            comm_mask = hyperdb.get_comm_mask('param5', comm_mask)

        # ext attributes
        if 'instrumentaddress' in attributes:
            tag_prop_buf.szInstrumentAddress = attributes['instrumentaddress'].encode('utf-8')
            ext_mask = hyperdb.get_tag_ext_mask('instrumentaddress', ext_mask)
        if 'instrumentaddresstype' in attributes:
            tag_prop_buf.nInstrumentAddressType  = c_uint8(attributes['instrumentaddresstype'])
            ext_mask = hyperdb.get_tag_ext_mask('instrumentaddresstype', ext_mask)
        if 'collectorname' in attributes:
            tag_prop_buf.szCollectorName = attributes['collectorname'].encode('utf-8')
            ext_mask = hyperdb.get_tag_ext_mask('collectorname', ext_mask)

        if 'devicename' in attributes:
            tag_prop_buf.szDeviceName = attributes['devicename'].encode('utf-8')
            ext_mask = hyperdb.get_tag_ext_mask('devicename', ext_mask)

        if 'scangroupname' in attributes:
            tag_prop_buf.szScanGroupName = attributes['scangroupname'].encode('utf-8')
            ext_mask = hyperdb.get_tag_ext_mask('scangroupname', ext_mask)

        if 'dataorder' in attributes:
            tag_prop_buf.nDataOrder = attributes['dataorder']
            ext_mask = hyperdb.get_tag_ext_mask('dataorder', ext_mask)

        mask = hyperdb.HD3Mask()
        mask.commmask = c_int64(comm_mask)
        mask.extmask = c_int64(ext_mask)

        hyperdb.api.pt3_modify_tag_prop.argtypes = [c_uint32, POINTER(hyperdb.TagProp), POINTER(hyperdb.HD3Mask)]
        retcode = hyperdb.api.pt3_modify_tag_prop(tagid, byref(tag_prop_buf), mask)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return hyperdb.hd_sucess

    ''' 
    # add a list of new normal tags to server
    # @param props: a list of dictionary, such as 
                    [{'tagname': 'tag0',
                      'tagtype': 'int32'
                     },{'tagname': 'tag1',
                      'tagtype': 'int32'
                     }]
    # @note: the input dictionary attributes should include param of TagProp
    # seen in tag.py
    '''

    def add_tags(self, props):
        number = len(props)
        tag_prop_buf = (number * hyperdb.TagProp)()

        count = 0
        mask = (number * hyperdb.HD3Mask)()
        for attributes in props:
            if 'tagname' not in attributes or 'tagtype' not in attributes:
                raise ArgumentError

            comm_mask = 0
            tag_ext_mask = 0

            ##Python3字符串（str类型）是以 Unicode 编码的，而字节串（bytes类型）是以字节序列编码的。
            # 例如，您可以使用以下方法将字符串转换为字节串：
            # string = "hello"
            # bytes_string = string.encode()
            tag_prop_buf[count].szTagName = attributes['tagname'].encode()
            if not tag_prop_buf[count].szTagName.strip():
                raise ArgumentError
            comm_mask = hyperdb.get_comm_mask('tagname', comm_mask)

            tag_prop_buf[count].nTagType = hyperdb.tag_type_dict[attributes['tagtype']]
            comm_mask = hyperdb.get_comm_mask('tagtype', comm_mask)

            if 'archiving' in attributes:
                tag_prop_buf[count].bArchiving = attributes['archiving']
                comm_mask = hyperdb.get_comm_mask('archiving', comm_mask)

            if 'compdev' in attributes:
                tag_prop_buf[count].fCompDev = attributes['compdev']
                comm_mask = hyperdb.get_comm_mask('compdev', comm_mask)
            if 'compmaxtime' in attributes:
                tag_prop_buf[count].nCompMaxTime = attributes['compmaxtime']
                comm_mask = hyperdb.get_comm_mask('compmaxtime', comm_mask)
            if 'comptype' in attributes:
                tag_prop_buf[count].nCompType = attributes['comptype']
                comm_mask = hyperdb.get_comm_mask('comptype', comm_mask)

            if 'recaccess' in attributes:
                tag_prop_buf[count].nRecAccess = attributes['recaccess']
                comm_mask = hyperdb.get_comm_mask('recaccess', comm_mask)
            if 'recnormalgroup' in attributes:
                tag_prop_buf[count].szRecNormalGroup = attributes['recnormalgroup'].encode()
                comm_mask = hyperdb.get_comm_mask('recnormalgroup', comm_mask)
            if 'recownergroup' in attributes:
                tag_prop_buf[count].szRecOwnerGroup = attributes['recownergroup'].encode()
                comm_mask = hyperdb.get_comm_mask('recownergroup', comm_mask)

            if 'descriptor' in attributes:
                tag_prop_buf[count].szDescriptor = attributes['descriptor'].encode()
                comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)
            if 'digitalset' in attributes:
                tag_prop_buf[count].szDigitalSet = attributes['digitalset'].encode()
                comm_mask = hyperdb.get_comm_mask('digitalset', comm_mask)
            if 'engunit' in attributes:
                tag_prop_buf[count].szEngUnits = attributes['engunit'].encode()
                comm_mask = hyperdb.get_comm_mask('engunit', comm_mask)
            if 'excmaxtime' in attributes:
                tag_prop_buf[count].fExcMaxTime = attributes['excmaxtime']
                comm_mask = hyperdb.get_comm_mask('excmaxtime', comm_mask)
            if 'excdev' in attributes:
                tag_prop_buf[count].fExcDev = attributes['excdev']
                comm_mask = hyperdb.get_comm_mask('excdev', comm_mask)

            if 'tagaccess' in attributes:
                tag_prop_buf[count].nTagAccess = attributes['tagaccess']
                comm_mask = hyperdb.get_comm_mask('tagaccess', comm_mask)
            if 'tagnormalgroup' in attributes:
                tag_prop_buf[count].szTagNormalGroup = attributes['tagnormalgroup'].encode()
                comm_mask = hyperdb.get_comm_mask('tagnormalgroup', comm_mask)
            if 'tagownergroup' in attributes:
                tag_prop_buf[count].szTagOwnerGroup = attributes['tagownergroup'].encode()
                comm_mask = hyperdb.get_comm_mask('tagownergroup', comm_mask)

            if 'scan' in attributes:
                tag_prop_buf[count].bScan = attributes['scan']
                comm_mask = hyperdb.get_comm_mask('scan', comm_mask)
            if 'span' in attributes:
                tag_prop_buf[count].fSpan = attributes['span']
                comm_mask = hyperdb.get_comm_mask('span', comm_mask)
            if 'minvalue' in attributes:
                tag_prop_buf[count].fMinValue = attributes['minvalue']
                comm_mask = hyperdb.get_comm_mask('minvalue', comm_mask)
            if 'tagavaliable' in attributes:
                tag_prop_buf[count].nTagAvaliable = attributes['tagavaliable']
                comm_mask = hyperdb.get_comm_mask('tagavaliable', comm_mask)
            if 'opcstate' in attributes:
                tag_prop_buf[count].nOpcState = attributes['opcstate']
                comm_mask = hyperdb.get_comm_mask('opcstate', comm_mask)

            if 'imvstring' in attributes:
                tag_prop_buf[count].szImvString = attributes['imvstring'].encode()
                comm_mask = hyperdb.get_comm_mask('imvstring', comm_mask)
            if 'extstring1' in attributes:
                tag_prop_buf[count].szExtString1 = attributes['extstring1'].encode()
                comm_mask = hyperdb.get_comm_mask('extstring1', comm_mask)
            if 'extstring2' in attributes:
                tag_prop_buf[count].szExtString2 = attributes['extstring2'].encode()
                comm_mask = hyperdb.get_comm_mask('extstring2', comm_mask)

            if 'param1' in attributes:
                tag_prop_buf[count].nParam1 = attributes['param1']
                comm_mask = hyperdb.get_comm_mask('param1', comm_mask)
            if 'param2' in attributes:
                tag_prop_buf[count].nParam2 = attributes['param2']
                comm_mask = hyperdb.get_comm_mask('param2', comm_mask)
            if 'param3' in attributes:
                tag_prop_buf[count].nParam3 = attributes['param3']
                comm_mask = hyperdb.get_comm_mask('param3', comm_mask)
            if 'param4' in attributes:
                tag_prop_buf[count].nParam4 = attributes['param4']
                comm_mask = hyperdb.get_comm_mask('param4', comm_mask)
            if 'param5' in attributes:
                tag_prop_buf[count].nParam5 = attributes['param5']
                comm_mask = hyperdb.get_comm_mask('param5', comm_mask)

            if 'instrumentaddress' in attributes:
                tag_prop_buf[count].szInstrumentAddress = attributes['instrumentaddress'].encode()
                tag_ext_mask = hyperdb.get_tag_ext_mask('instrumentaddress', tag_ext_mask)
            if 'instrumentaddresstype' in attributes:
                tag_prop_buf[count].nInstrumentAddressType = c_uint8(attributes['instrumentaddresstype'])
                tag_ext_mask = hyperdb.get_tag_ext_mask('instrumentaddresstype', tag_ext_mask)
            if 'collectorname' in attributes:
                tag_prop_buf[count].szCollectorName = attributes['collectorname'].encode()
                tag_ext_mask = hyperdb.get_tag_ext_mask('collectorname', tag_ext_mask)

            if 'devicename' in attributes:
                tag_prop_buf[count].szDeviceName = attributes['devicename'].encode()
                tag_ext_mask = hyperdb.get_tag_ext_mask('devicename', tag_ext_mask)

            if 'scangroupname' in attributes:
                tag_prop_buf[count].szScanGroupName = attributes['scangroupname'].encode()
                tag_ext_mask = hyperdb.get_tag_ext_mask('scangroupname', tag_ext_mask)

            if 'dataorder' in attributes:
                tag_prop_buf[count].nDataOrder = attributes['dataorder']
                tag_ext_mask = hyperdb.get_tag_ext_mask('dataorder', tag_ext_mask)

            # mask = hyperdb.HD3Mask()
            mask[count].commmask = c_int64(comm_mask)
            mask[count].extmask = c_int64(tag_ext_mask)
            # tagmask.append(mask)
            count += 1

        tagids = (number * c_uint32)()
        errorcode = (number * c_int32)()
        hyperdb.api.pt3_add_tags.argtypes = [c_int32, POINTER(hyperdb.TagProp), POINTER(hyperdb.HD3Mask), c_char_p,
                                             POINTER(c_uint32), POINTER(c_int32)]
        retcode = hyperdb.api.pt3_add_tags(number, tag_prop_buf, mask, None, tagids, errorcode)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return tagids

    # get a instance of tag from server by tagname
    # @param tagname: the name of the tag which will be get
    # @return: return a instance of tag
    def get_tag(self, tagname):
        if type(tagname) != str:
            raise TypeError

        tagid = c_uint32()
        tagname_buf = tagname.encode('utf-8')
        tag_prop_buf = hyperdb.TagProp()

        hyperdb.api.tag3_query_id_by_name.argtypes = (c_char_p, c_void_p)
        retcode = hyperdb.api.tag3_query_id_by_name(tagname_buf, byref(tagid))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

        hyperdb.api.pt3_query_tag_prop.argtypes = [c_uint32, POINTER(hyperdb.TagProp)]
        retcode = hyperdb.api.pt3_query_tag_prop(tagid, byref(tag_prop_buf))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

        tag_buf = tag.Tag()
        tag_buf.tagname = tag_prop_buf.szTagName.decode('utf-8')
        tag_buf.archiving = tag_prop_buf.bArchiving
        tag_buf.changedate = tag_prop_buf.nChangeDate
        tag_buf.changer = tag_prop_buf.szChanger.decode('utf-8')

        tag_buf.compdev = tag_prop_buf.fCompDev
        tag_buf.compmaxtime = tag_prop_buf.nCompMaxTime
        tag_buf.comptype = tag_prop_buf.nCompType
        tag_buf.creationdate = tag_prop_buf.nCreationDate
        tag_buf.creator = tag_prop_buf.szCreator.decode('utf-8')

        tag_buf.recaccess = tag_prop_buf.nRecAccess
        tag_buf.recnormalgroup = tag_prop_buf.szRecNormalGroup.decode('utf-8')
        tag_buf.recownergroup = tag_prop_buf.szRecOwnerGroup.decode('utf-8')

        tag_buf.descriptor = tag_prop_buf.szDescriptor.decode('utf-8')
        tag_buf.digitalset = tag_prop_buf.szDigitalSet.decode('utf-8')
        tag_buf.engunit = tag_prop_buf.szEngUnits.decode('utf-8')
        tag_buf.excmaxtime = tag_prop_buf.nExcMaxTime
        tag_buf.excdev = tag_prop_buf.fExcDev

        tag_buf.tagid = tag_prop_buf.nTagID

        for value in hyperdb.tag_type_dict:
            if hyperdb.tag_type_dict[value] == tag_prop_buf.nTagType:
                tag_buf.tagtype = value

        tag_buf.tagaccess = tag_prop_buf.nTagAccess
        tag_buf.tagnormalgroup = tag_prop_buf.szTagNormalGroup.decode('utf-8')
        tag_buf.tagownergroup = tag_prop_buf.szTagOwnerGroup.decode('utf-8')

        tag_buf.scan = tag_prop_buf.bScan
        tag_buf.span = tag_prop_buf.fSpan

        tag_buf.minvalue = tag_prop_buf.fMinValue
        tag_buf.tagclass = tag_prop_buf.nTagClass
        tag_buf.tagavaliable = tag_prop_buf.nTagAvaliable

        tag_buf.param1 = tag_prop_buf.nParam1
        tag_buf.param2 = tag_prop_buf.nParam2
        tag_buf.param3 = tag_prop_buf.nParam3
        tag_buf.param4 = tag_prop_buf.nParam4
        tag_buf.param5 = tag_prop_buf.nParam5
        tag_buf.opcstate = tag_prop_buf.nOpcState

        tag_buf.imvstring = tag_prop_buf.szImvString.decode('utf-8')
        tag_buf.extstring1 = tag_prop_buf.szExtString1.decode('utf-8')
        tag_buf.extstring2 = tag_prop_buf.szExtString2.decode('utf-8')

        tag_buf.instrumentaddress = tag_prop_buf.szInstrumentAddress.decode('utf-8')
        tag_buf.instrumentaddresstype = tag_prop_buf.nInstrumentAddressType

        tag_buf.collectorname = tag_prop_buf.szCollectorName.decode('utf-8')
        tag_buf.collectorid = tag_prop_buf.nCollectorID

        tag_buf.devicename = tag_prop_buf.szDeviceName.decode('utf-8')
        tag_buf.deviceid = tag_prop_buf.nDeviceID

        tag_buf.scangroupname = tag_prop_buf.szScanGroupName.decode('utf-8')
        tag_buf.scangroupid = tag_prop_buf.nScanGroupID
        tag_buf.dataorder = tag_prop_buf.nDataOrder

        return tag_buf

    # get tag's type from server by tagID
    # @param tagID: the ID of the tag
    # @return: return the type of tag(such as 'int8')
    def get_tagType(self, tagID):
        # if (type(tagID) != int):
        #     raise TypeError
        hdTagID = c_uint32(tagID)
        nTagType = c_uint8()

        hyperdb.api.pt3_query_tag_type.argtypes = [c_uint32, POINTER(c_uint8)]
        retcode = hyperdb.api.pt3_query_tag_type(hdTagID, nTagType)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

        rtnTagType = hyperdb.tag_type_trans_dict[nTagType.value]
        return rtnTagType

    ## Get tag IDs by tag Names
    # @param tagnames: the list of tagname
    # @return: two lists, one is tagID List another is errorcode List
    # the totalErrCode can be hyperdb.hd_sucess or hyperdb.EC_HD_API_QUERY_TAGS_FAILED
    def get_tagIDs(self, tagnames):
        number = len(tagnames)
        hdTagIDs = (number * c_uint32)()
        errors = (number * c_int32)()
        tagID = c_uint32()

        for index in range(number):
            tagname_buf = tagnames[index].encode('utf-8')
            hyperdb.api.tag3_query_id_by_name.argtypes = (c_char_p, c_void_p)
            retcode = hyperdb.api.tag3_query_id_by_name(tagname_buf, byref(tagID))
            if (retcode != hyperdb.hd_sucess):
                retcode = hyperdb.EC_HD_API_QUERY_TAGS_FAILED
            hdTagIDs[index] = tagID
            errors[index] = retcode

        tagIDList = []
        errCodeList = []
        for id in hdTagIDs:
            tagIDList.append(id)
        for errcode in errors:
            errCodeList.append(errcode)

        return (retcode, tagIDList, errCodeList)

    # delete a tag from server by its name
    # @param tagname: the name of the tag
    def delete_tag(self, tagname):
        "delete a tag by name"
        if type(tagname) != str:
            raise TypeError

        tagid = c_uint32()
        tagname_buf = tagname.encode('utf-8')

        hyperdb.api.tag3_query_id_by_name.argtypes = [c_char_p, c_void_p]
        retcode = hyperdb.api.tag3_query_id_by_name(tagname_buf, byref(tagid))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

        hyperdb.api.tag3_delete_tag.argtypes = [c_uint32]
        retcode = hyperdb.api.tag3_delete_tag(tagid)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return hyperdb.hd_sucess

    # delete tags from server by its name
    # @param tagnames: the names of tags
    def delete_tags(self, tagnames):
        "delete tags by names"
        num = len(tagnames)
        nTagNum = c_int32(num)
        pTagIDArray = (num * c_uint32)()
        pErrCodeArray1 = (num * c_int32)()

        class TagNameArray(ctypes.Structure):
            _fields_ = [("names", ctypes.c_char * (128 * 2) * num)]

        # 准备实际调用函数时的参数
        tag_names = TagNameArray()

        # # 填充tag_names
        for i in range(num):
            tag_name = tagnames[i].encode('utf-8')[:128]
            ctypes.memmove(tag_names.names[i], tag_name, len(tag_name))

        hyperdb.api.tag3_query_ids_by_names.argtypes = [c_int32, ctypes.c_char * (128 * 2) * num, POINTER(c_uint32),
                                                        POINTER(c_int32)]
        retcode = hyperdb.api.tag3_query_ids_by_names(nTagNum, tag_names.names, pTagIDArray, pErrCodeArray1)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

        pErrCodeArray2 = (num * c_int32)()

        hyperdb.api.tag3_delete_tags.argtypes = [c_int32, POINTER(c_uint32), POINTER(c_int32)]
        retcode = hyperdb.api.tag3_delete_tags(nTagNum, pTagIDArray, pErrCodeArray2)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

    ##根据普通点名称查询ID
    # @param tagname: the name of tag
    # @return: tagid
    def query_id_by_name(self, tagname):
        if type(tagname) != str:
            raise TypeError

        pnTagID = c_uint32()
        tagname_buf = tagname.encode('utf-8')

        hyperdb.api.tag3_query_id_by_name.argtypes = (c_char_p, c_void_p)
        retcode = hyperdb.api.tag3_query_id_by_name(tagname_buf, byref(pnTagID))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return pnTagID.value

    ##根据普通点名称批量查询ID
    # @param tagname: the names of tags
    # @return: tagids
    def query_ids_by_names(self, tagnames):
        num = len(tagnames)
        nTagNum = c_int32(num)
        pTagIDArray = (num * c_uint32)()
        pErrCodeArray = (num * c_int32)()
        class TagNameArray(ctypes.Structure):
            _fields_ = [("names", ctypes.c_char * (128 * 2) * num)]

        # 准备实际调用函数时的参数
        tag_names = TagNameArray()

        # # 填充tag_names
        for i in range(num):
            tag_name = tagnames[i].encode('utf-8')[:128]
            ctypes.memmove(tag_names.names[i], tag_name, len(tag_name))

        hyperdb.api.tag3_query_ids_by_names.argtypes = [c_int32, ctypes.c_char * (128 * 2) * num, POINTER(c_uint32),
                                                        POINTER(c_int32)]
        retcode = hyperdb.api.tag3_query_ids_by_names(nTagNum, tag_names.names, pTagIDArray, pErrCodeArray)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return pTagIDArray, pErrCodeArray

    ##条件查询满足条件的tag点的个数
    # @param nTagClass: Tag类别
    # @param conds: the conditions used to query tag's num, such as
    #     [('tagname','like','*')]
    # @return: the number of tags which satisfied the conds
    def query_tag_num_cond(self, nTagClass, conds):
        number = len(conds)
        conds_buf = (number * hyperdb.HD3FilterItem)()
        condset = hyperdb.HD3FilterItemSet()
        pnTagNum = c_int32()
        nTagClass = c_int8(nTagClass)

        count = 0
        for cond in conds:
            try:
                conds_buf[count].nPropItemID = hyperdb.tag_col_dict[cond[0]]
                conds_buf[count].nRelation = hyperdb.relation_dict[cond[1]]
                conds_buf[count].szValue = cond[2].encode('utf-8')
            except KeyError:
                raise hyperdb.HDError(errcode=0, errinfo='the key is not exist!')
            count = count + 1
        condset.nSize = c_int32(number)
        condset.pItem = conds_buf

        hyperdb.api.tag3_query_tag_num_cond.argtypes = [c_int8, POINTER(hyperdb.HD3FilterItemSet), POINTER(c_int32)]
        retcode = hyperdb.api.tag3_query_tag_num_cond(nTagClass, byref(condset), byref(pnTagNum))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)

        return pnTagNum.value

    ##条件查询满足条件的tag点的个数
    # @param userName: 用户名（用于权限过滤）
    # @param nTagClass: Tag类别
    # @param conds: the conditions used to query tag's num, such as
    #     [('tagname','like','*')]
    # @return: the number of tags which satisfied the conds
    def query_tag_num_cond_access(self, userName, nTagClass, conds):
        username_buf = userName.encode('utf-8')
        number = len(conds)
        conds_buf = (number * hyperdb.HD3FilterItem)()
        condset = hyperdb.HD3FilterItemSet()
        pnTagNum = c_int32()
        nTagClass = c_int8(nTagClass)

        count = 0
        for cond in conds:
            try:
                conds_buf[count].nPropItemID = hyperdb.tag_col_dict[cond[0]]
                conds_buf[count].nRelation = hyperdb.relation_dict[cond[1]]
                conds_buf[count].szValue = cond[2].encode('utf-8')
            except KeyError:
                raise hyperdb.HDError(errcode=0, errinfo='the key is not exist!')
            count = count + 1
        condset.nSize = c_int32(number)
        condset.pItem = conds_buf

        hyperdb.api.tag3_query_tag_num_cond_access.argtypes = [c_char_p, c_int8, POINTER(hyperdb.HD3FilterItemSet), POINTER(c_int32)]
        retcode = hyperdb.api.tag3_query_tag_num_cond_access(username_buf, nTagClass, byref(condset), byref(pnTagNum))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)

        return pnTagNum.value

    ## 根据多个点名称批量查询查询点的基本信息（ID、类型和类别）
    # @param tagclassmask: Tag类别掩码
    # @param tagnum: Tag点个数
    # @param tagnames: Tag点名
    # @return: tag_info_array, pErrCodeArray: 查询到的tag点基本信息以及错误码数组
    def query_tags_basic_info_by_name(self, tagclassmask, tagnum, tagnames):
        if type(tagnum) != int:
            raise TypeError
        nTagClassMask = c_int64(tagclassmask)
        nTagNum = c_int32(tagnum)
        pInfoArray = (tagnum * hyperdb.HD3TagBasicInfo)()
        pErrCodeArray = (tagnum * c_int32)()
        for i in range(tagnum):
            pInfoArray[i].szTagName = tagnames[i].encode('utf-8')

        hyperdb.api.tag3_query_tags_basic_info_by_name.argtypes = [c_int64, c_int32, POINTER(hyperdb.HD3TagBasicInfo),
                                                                   POINTER(c_int32)]
        retcode = hyperdb.api.tag3_query_tags_basic_info_by_name(nTagClassMask, nTagNum, pInfoArray, pErrCodeArray)

        # if (hyperdb.hd_sucess != retcode):
        #     raise hyperdb.HDError(retcode)
        tag_info_array = []
        for i in range(tagnum):
            tag_info = tag.HD3TagBasicInfo()
            tag_info.tagclass = pInfoArray[i].nTagClass
            tag_info.tagtype = pInfoArray[i].nTagType
            tag_info.tagid = pInfoArray[i].nTagID
            tag_info.tagname = pInfoArray[i].szTagName.decode('utf-8')
            tag_info_array.append(tag_info)
        return (retcode,tag_info_array, pErrCodeArray)

    ##指定TagClass，根据TagID查询Tag公共属性
    # @param: tagclassmask:Tag类别掩码，具体如下：
    # HD3_TAG_CLASS = {
    # 	'HD3_TAG_CLASS_MIN': 0,
    # 	'HD3_TAG_CLASS_BASIC': 0,
    # 	'HD3_TAG_CLASS_ALARM': 1,
    # 	'HD3_TAG_CLASS_STATS': 2,
    # 	'HD3_TAG_CLASS_CALC': 3,
    # 	'HD3_TAG_CLASS_SPC': 4,
    # 	'HD3_TAG_CLASS_SYS': 5,
    # 	'HD3_TAG_CLASS_MAX': 5,
    # }
    # @param: tagnum:Tag点个数
    # @param: tagids:Tag点ID
    # @return:Tag公共属性列表
    def query_tags_common_prop(self, tagclassmask, tagnum, tagids):
        if type(tagnum) != int:
            raise TypeError
        nTagClassMask = c_int64(tagclassmask)
        nTagNum = c_int32(tagnum)
        nPropMask = c_int64(0XFFFFFFFFFFFFFFFF)
        pPropArray = (tagnum * hyperdb.HD3CommTagProp)()
        pErrCodeArray = (tagnum * c_int32)()
        for i in range(tagnum):
            pPropArray[i].nTagID = tagids[i]

        hyperdb.api.tag3_query_tags_common_prop.argtypes = [c_int64, c_int32, c_int64,
                                                            POINTER(hyperdb.HD3CommTagProp), POINTER(c_int32)]
        retcode = hyperdb.api.tag3_query_tags_common_prop(nTagClassMask, nTagNum, nPropMask,
                                                          pPropArray, pErrCodeArray)
        # if (hyperdb.hd_sucess != retcode):
        #     raise hyperdb.HDError(retcode)

        return retcode, self.convert_to_commtag_list(pPropArray), pErrCodeArray

    ##查询满足查询条件的Tag,普通点，报警点，统计点，计算点，SPC报警点都能查询
    # @param conds:查询条件,例如：[('tagname','like','*')]
    # @param tagclassmask:Tag类别掩码
    # HD3_TAG_CLASS_MASK = {
    #     'HD3M_TAG_CLASS_BASIC': 1 << HD3_TAG_CLASS['HD3_TAG_CLASS_BASIC'],
    #     'HD3M_TAG_CLASS_ALARM': 1 << HD3_TAG_CLASS['HD3_TAG_CLASS_ALARM'],
    #     'HD3M_TAG_CLASS_STATS': 1 << HD3_TAG_CLASS['HD3_TAG_CLASS_STATS'],
    #     'HD3M_TAG_CLASS_CALC': 1 << HD3_TAG_CLASS['HD3_TAG_CLASS_CALC'],
    #     'HD3M_TAG_CLASS_SPC': 1 << HD3_TAG_CLASS['HD3_TAG_CLASS_SPC'],
    #     'HD3M_TAG_CLASS_SYS': 1 << HD3_TAG_CLASS['HD3_TAG_CLASS_SYS'],
    #     'HD3M_ALL': 0XFFFFFFFFFFFFFFFF
    # }
    # @return: return a iterator of tags
    def query_tags_cond(self, conds, tagclassmask):
        conds_num = len(conds)
        nTagClassMask = c_int64(tagclassmask)
        conds_buf = (conds_num * hyperdb.HD3FilterItem)()
        pSet = hyperdb.HD3FilterItemSet()
        phResutSet = c_void_p()

        count = 0
        for cond in conds:
            try:
                conds_buf[count].nPropItemID = hyperdb.tag_col_dict[cond[0]]
                conds_buf[count].nRelation = hyperdb.relation_dict[cond[1]]
                if cond[0] == 'tagtype':
                    conds_buf[count].szValue = str(hyperdb.tag_type_dict[cond[2]]).encode('utf-8')
                else:
                    conds_buf[count].szValue = cond[2].encode('utf-8')
            except KeyError:
                raise hyperdb.HDError(errcode=0, errinfo='the key is not exist!')

            count = count + 1
        pSet.nSize = c_int32(conds_num)
        pSet.pItem = conds_buf

        # mask
        comm_mask = 0
        comm_mask = hyperdb.get_comm_mask('tagname', comm_mask)
        comm_mask = hyperdb.get_comm_mask('archiving', comm_mask)
        comm_mask = hyperdb.get_comm_mask('changedate', comm_mask)
        comm_mask = hyperdb.get_comm_mask('changer', comm_mask)
        comm_mask = hyperdb.get_comm_mask('compdev', comm_mask)
        comm_mask = hyperdb.get_comm_mask('compmaxtime', comm_mask)
        comm_mask = hyperdb.get_comm_mask('comptype', comm_mask)
        comm_mask = hyperdb.get_comm_mask('creationdate', comm_mask)
        comm_mask = hyperdb.get_comm_mask('creator', comm_mask)
        comm_mask = hyperdb.get_comm_mask('recaccess', comm_mask)
        comm_mask = hyperdb.get_comm_mask('recnormalgroup', comm_mask)
        comm_mask = hyperdb.get_comm_mask('recownergroup', comm_mask)
        comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)
        comm_mask = hyperdb.get_comm_mask('digitalset', comm_mask)
        comm_mask = hyperdb.get_comm_mask('engunit', comm_mask)
        comm_mask = hyperdb.get_comm_mask('excmaxtime', comm_mask)
        comm_mask = hyperdb.get_comm_mask('excdev', comm_mask)
        comm_mask = hyperdb.get_comm_mask('tagid', comm_mask)
        comm_mask = hyperdb.get_comm_mask('tagtype', comm_mask)
        comm_mask = hyperdb.get_comm_mask('tagaccess', comm_mask)
        comm_mask = hyperdb.get_comm_mask('tagnormalgroup', comm_mask)
        comm_mask = hyperdb.get_comm_mask('tagownergroup', comm_mask)
        comm_mask = hyperdb.get_comm_mask('scan', comm_mask)
        comm_mask = hyperdb.get_comm_mask('span', comm_mask)
        comm_mask = hyperdb.get_comm_mask('minvalue', comm_mask)
        comm_mask = hyperdb.get_comm_mask('tagclass', comm_mask)
        comm_mask = hyperdb.get_comm_mask('tagavaliable', comm_mask)
        comm_mask = hyperdb.get_comm_mask('param1', comm_mask)
        comm_mask = hyperdb.get_comm_mask('param2', comm_mask)
        comm_mask = hyperdb.get_comm_mask('param3', comm_mask)
        comm_mask = hyperdb.get_comm_mask('param4', comm_mask)
        comm_mask = hyperdb.get_comm_mask('param5', comm_mask)
        comm_mask = hyperdb.get_comm_mask('opcstate', comm_mask)
        comm_mask = hyperdb.get_comm_mask('imvstring', comm_mask)
        comm_mask = hyperdb.get_comm_mask('extstring1', comm_mask)
        comm_mask = hyperdb.get_comm_mask('extstring2', comm_mask)

        nPropMask = c_int64(comm_mask)
        hyperdb.api.tag3_query_tags_cond.argtypes = [c_int64, POINTER(hyperdb.HD3FilterItemSet), c_int64,
                                                     POINTER(c_void_p)]
        retcode = hyperdb.api.tag3_query_tags_cond(nTagClassMask, byref(pSet), nPropMask, byref(phResutSet))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)

        com_tag_prop = hyperdb.HD3CommTagProp()
        iterate = hyperdb.Iterate(phResutSet, com_tag_prop)
        return iterate

    # query tags which satisfy the conditions
    # @param conds: the conditions used to query tags which satisfied with
    # @return: return a iterator of tags
    def pt3_query_tags_cond(self, conds):
        number = len(conds)
        conds_buf = (number * hyperdb.HD3FilterItem)()
        condset = hyperdb.HD3FilterItemSet()

        count = 0
        for cond in conds:
            try:
                conds_buf[count].nPropItemID = hyperdb.tag_col_dict[cond[0]]
                conds_buf[count].nRelation = hyperdb.relation_dict[cond[1]]
                if cond[0] == 'tagtype':
                    conds_buf[count].szValue = str(hyperdb.tag_type_dict[cond[2]]).encode('utf-8')
                else:
                    conds_buf[count].szValue = cond[2].encode('utf-8')
            except KeyError:
                raise hyperdb.HDError(errcode=0, errinfo='the key is not exist!')

            count = count + 1

        condset.nSize = c_int32(number)
        condset.pItem = conds_buf

        hditer = c_void_p()
        mask = hyperdb.HD3Mask()
        mask.commmask = c_int64(0XFFFFFFFFFFFFFFFF)
        mask.extmask = c_int64(0XFFFFFFFFFFFFFFFF)

        hyperdb.api.pt3_query_tags_cond.argtypes = [POINTER(hyperdb.HD3FilterItemSet), POINTER(hyperdb.HD3Mask),
                                                    POINTER(c_void_p)]
        retcode = hyperdb.api.pt3_query_tags_cond(byref(condset), byref(mask), byref(hditer))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)

        tag_prop = hyperdb.TagProp()
        iterate = hyperdb.Iterate(hditer, tag_prop)
        return iterate

    ''' 
    # add a list of new calctags to server
    # @param props: a list of dictionary, such as 
                    [{'tagname': 'catag0',
                      'tagtype': 'int32',
                      'szCalcExpr': '\'srctag\'+1',
                     'szSourceTagName':'srctag',
                     'nCalcMode' : 0,
                     'nCalcPeriod' :5
                     },{'tagname': 'catag1',
                      'tagtype': 'int32',
                      'szCalcExpr': '\'srctag\'+1',
                     'szSourceTagName':'srctag',
                     'nCalcMode' : 0,
                     'nCalcPeriod' :5
                     }]
    # @note: the input dictionary attributes should include para of CalcTagProp
    # seen in tag.py
    '''

    def add_calc_tags(self, props):
        number = len(props)
        tag_prop_buf = (number * hyperdb.CalcTagProp)()

        count = 0
        mask = (number * hyperdb.HD3Mask)()
        for attributes in props:
            if 'tagname' not in attributes or 'tagtype' not in attributes:
                raise ArgumentError

            comm_mask = 0
            tag_ext_mask = 0

            tag_prop_buf[count].szTagName = attributes['tagname'].encode('utf-8')
            if not tag_prop_buf[count].szTagName.strip():
                raise ArgumentError
            comm_mask = hyperdb.get_comm_mask('tagname', comm_mask)

            tag_prop_buf[count].nTagType = hyperdb.tag_type_dict[attributes['tagtype']]
            comm_mask = hyperdb.get_comm_mask('tagtype', comm_mask)

            if 'archiving' in attributes:
                tag_prop_buf[count].bArchiving = attributes['archiving']
                comm_mask = hyperdb.get_comm_mask('archiving', comm_mask)

            if 'compdev' in attributes:
                tag_prop_buf[count].fCompDev = attributes['compdev']
                comm_mask = hyperdb.get_comm_mask('compdev', comm_mask)
            if 'compmaxtime' in attributes:
                tag_prop_buf[count].nCompMaxTime = attributes['compmaxtime']
                comm_mask = hyperdb.get_comm_mask('compmaxtime', comm_mask)
            if 'comptype' in attributes:
                tag_prop_buf[count].nCompType = attributes['comptype']
                comm_mask = hyperdb.get_comm_mask('comptype', comm_mask)

            if 'recaccess' in attributes:
                tag_prop_buf[count].nRecAccess = attributes['recaccess']
                comm_mask = hyperdb.get_comm_mask('recaccess', comm_mask)
            if 'recnormalgroup' in attributes:
                tag_prop_buf[count].szRecNormalGroup = attributes['recnormalgroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('recnormalgroup', comm_mask)
            if 'recownergroup' in attributes:
                tag_prop_buf[count].szRecOwnerGroup = attributes['recownergroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('recownergroup', comm_mask)

            if 'descriptor' in attributes:
                tag_prop_buf[count].szDescriptor = attributes['descriptor'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)
            if 'digitalset' in attributes:
                tag_prop_buf[count].szDigitalSet = attributes['digitalset'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('digitalset', comm_mask)
            if 'engunit' in attributes:
                tag_prop_buf[count].szEngUnits = attributes['engunit'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('engunit', comm_mask)
            if 'excmaxtime' in attributes:
                tag_prop_buf[count].fExcMaxTime = attributes['excmaxtime']
                comm_mask = hyperdb.get_comm_mask('excmaxtime', comm_mask)
            if 'excdev' in attributes:
                tag_prop_buf[count].fExcDev = attributes['excdev']
                comm_mask = hyperdb.get_comm_mask('excdev', comm_mask)

            if 'tagaccess' in attributes:
                tag_prop_buf[count].nTagAccess = attributes['tagaccess']
                comm_mask = hyperdb.get_comm_mask('tagaccess', comm_mask)
            if 'tagnormalgroup' in attributes:
                tag_prop_buf[count].szTagNormalGroup = attributes['tagnormalgroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('tagnormalgroup', comm_mask)
            if 'tagownergroup' in attributes:
                tag_prop_buf[count].szTagOwnerGroup = attributes['tagownergroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('tagownergroup', comm_mask)

            if 'scan' in attributes:
                tag_prop_buf[count].bScan = attributes['scan']
                comm_mask = hyperdb.get_comm_mask('scan', comm_mask)
            if 'span' in attributes:
                tag_prop_buf[count].fSpan = attributes['span']
                comm_mask = hyperdb.get_comm_mask('span', comm_mask)
            if 'minvalue' in attributes:
                tag_prop_buf[count].fMinValue = attributes['minvalue']
                comm_mask = hyperdb.get_comm_mask('minvalue', comm_mask)
            if 'tagavaliable' in attributes:
                tag_prop_buf[count].nTagAvaliable = attributes['tagavaliable']
                comm_mask = hyperdb.get_comm_mask('tagavaliable', comm_mask)
            if 'opcstate' in attributes:
                tag_prop_buf[count].nOpcState = attributes['opcstate']
                comm_mask = hyperdb.get_comm_mask('opcstate', comm_mask)

            if 'imvstring' in attributes:
                tag_prop_buf[count].szImvString = attributes['imvstring'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('imvstring', comm_mask)
            if 'extstring1' in attributes:
                tag_prop_buf[count].szExtString1 = attributes['extstring1'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('extstring1', comm_mask)
            if 'extstring2' in attributes:
                tag_prop_buf[count].szExtString2 = attributes['extstring2'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('extstring2', comm_mask)

            if 'param1' in attributes:
                tag_prop_buf[count].nParam1 = attributes['param1']
                comm_mask = hyperdb.get_comm_mask('param1', comm_mask)
            if 'param2' in attributes:
                tag_prop_buf[count].nParam2 = attributes['param2']
                comm_mask = hyperdb.get_comm_mask('param2', comm_mask)
            if 'param3' in attributes:
                tag_prop_buf[count].nParam3 = attributes['param3']
                comm_mask = hyperdb.get_comm_mask('param3', comm_mask)
            if 'param4' in attributes:
                tag_prop_buf[count].nParam4 = attributes['param4']
                comm_mask = hyperdb.get_comm_mask('param4', comm_mask)
            if 'param5' in attributes:
                tag_prop_buf[count].nParam5 = attributes['param5']
                comm_mask = hyperdb.get_comm_mask('param5', comm_mask)

            if 'calcmode' in attributes:
                tag_prop_buf[count].nCalcMode = attributes['calcmode']
                tag_ext_mask = hyperdb.get_calc_ext_mask('calcmode', tag_ext_mask)
            if 'calcperiod' in attributes:
                tag_prop_buf[count].nCalcPeriod = attributes['calcperiod']
                tag_ext_mask = hyperdb.get_calc_ext_mask('calcperiod', tag_ext_mask)
            if 'calcexpr' in attributes:
                tag_prop_buf[count].szCalcExpr = attributes['calcexpr'].encode('utf-8')
                tag_ext_mask = hyperdb.get_calc_ext_mask('calcexpr', tag_ext_mask)

            if 'calchistory' in attributes:
                tag_prop_buf[count].bCalcHistory = attributes['calchistory']
                tag_ext_mask = hyperdb.get_calc_ext_mask('calchistory', tag_ext_mask)

            if 'calcstartdate' in attributes:
                tag_prop_buf[count].nCalcStartDate = attributes['calcstartdate']
                tag_ext_mask = hyperdb.get_calc_ext_mask('calcstartdate', tag_ext_mask)

            if 'calcenddate' in attributes:
                tag_prop_buf[count].nCalcEndDate = attributes['calcenddate']
                tag_ext_mask = hyperdb.get_calc_ext_mask('calcenddate', tag_ext_mask)

            if 'haveendtime' in attributes:
                tag_prop_buf[count].bHaveEndTime = attributes['haveendtime']
                tag_ext_mask = hyperdb.get_calc_ext_mask('haveendtime', tag_ext_mask)

            if 'sourcetagname' in attributes:
                tag_prop_buf[count].szSourceTagName = attributes['sourcetagname'].encode('utf-8')
                tag_ext_mask = hyperdb.get_calc_ext_mask('sourcetagname', tag_ext_mask)

            # mask = hyperdb.HD3Mask()
            mask[count].commmask = c_int64(comm_mask)
            mask[count].extmask = c_int64(tag_ext_mask)
            # tagmask.append(mask)
            count += 1

        tagids = (number * c_uint32)()
        errorcode = (number * c_int32)()
        hyperdb.api.ca3_add_tags.argtypes = [c_int32, POINTER(hyperdb.CalcTagProp), POINTER(hyperdb.HD3Mask), c_char_p,
                                             POINTER(c_uint32), POINTER(c_int32)]
        retcode = hyperdb.api.ca3_add_tags(number, tag_prop_buf, mask, None, tagids, errorcode)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return tagids

    # get a instance of calctag from server by tagname
    # @param calc_tagname: the name of the calctag which will be get
    # @return: return a instance of calctag
    def query_calc_tag_prop(self, calc_tagname):
        if type(calc_tagname) != str:
            raise TypeError

        calc_tagid = c_uint32()
        calc_tagname_buf = calc_tagname.encode('utf-8')
        calc_tag_prop_buf = hyperdb.CalcTagProp()

        hyperdb.api.tag3_query_id_by_name.argtypes = (c_char_p, c_void_p)
        retcode = hyperdb.api.tag3_query_id_by_name(calc_tagname_buf, byref(calc_tagid))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

        hyperdb.api.ca3_query_tag_prop.argtypes = [c_uint32, POINTER(hyperdb.CalcTagProp)]
        retcode = hyperdb.api.ca3_query_tag_prop(calc_tagid, byref(calc_tag_prop_buf))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

        calc_tag_buf = tag.CalcTag()
        calc_tag_buf.tagname = calc_tag_prop_buf.szTagName.decode('utf-8')
        calc_tag_buf.archiving = calc_tag_prop_buf.bArchiving
        calc_tag_buf.changedate = calc_tag_prop_buf.nChangeDate
        calc_tag_buf.changer = calc_tag_prop_buf.szChanger.decode('utf-8')

        calc_tag_buf.compdev = calc_tag_prop_buf.fCompDev
        calc_tag_buf.compmaxtime = calc_tag_prop_buf.nCompMaxTime
        calc_tag_buf.comptype = calc_tag_prop_buf.nCompType
        calc_tag_buf.creationdate = calc_tag_prop_buf.nCreationDate
        calc_tag_buf.creator = calc_tag_prop_buf.szCreator.decode('utf-8')

        calc_tag_buf.recaccess = calc_tag_prop_buf.nRecAccess
        calc_tag_buf.recnormalgroup = calc_tag_prop_buf.szRecNormalGroup.decode('utf-8')
        calc_tag_buf.recownergroup = calc_tag_prop_buf.szRecOwnerGroup.decode('utf-8')

        calc_tag_buf.descriptor = calc_tag_prop_buf.szDescriptor.decode('utf-8')
        calc_tag_buf.digitalset = calc_tag_prop_buf.szDigitalSet.decode('utf-8')
        calc_tag_buf.engunit = calc_tag_prop_buf.szEngUnits.decode('utf-8')
        calc_tag_buf.excmaxtime = calc_tag_prop_buf.nExcMaxTime
        calc_tag_buf.excdev = calc_tag_prop_buf.fExcDev

        calc_tag_buf.tagid = calc_tag_prop_buf.nTagID

        for value in hyperdb.tag_type_dict:
            if hyperdb.tag_type_dict[value] == calc_tag_prop_buf.nTagType:
                calc_tag_buf.tagtype = value

        calc_tag_buf.tagaccess = calc_tag_prop_buf.nTagAccess
        calc_tag_buf.tagnormalgroup = calc_tag_prop_buf.szTagNormalGroup.decode('utf-8')
        calc_tag_buf.tagownergroup = calc_tag_prop_buf.szTagOwnerGroup.decode('utf-8')

        calc_tag_buf.scan = calc_tag_prop_buf.bScan
        calc_tag_buf.span = calc_tag_prop_buf.fSpan

        calc_tag_buf.minvalue = calc_tag_prop_buf.fMinValue
        calc_tag_buf.tagclass = calc_tag_prop_buf.nTagClass
        calc_tag_buf.tagavaliable = calc_tag_prop_buf.nTagAvaliable

        calc_tag_buf.param1 = calc_tag_prop_buf.nParam1
        calc_tag_buf.param2 = calc_tag_prop_buf.nParam2
        calc_tag_buf.param3 = calc_tag_prop_buf.nParam3
        calc_tag_buf.param4 = calc_tag_prop_buf.nParam4
        calc_tag_buf.param5 = calc_tag_prop_buf.nParam5
        calc_tag_buf.opcstate = calc_tag_prop_buf.nOpcState

        calc_tag_buf.imvstring = calc_tag_prop_buf.szImvString.decode('utf-8')
        calc_tag_buf.extstring1 = calc_tag_prop_buf.szExtString1.decode('utf-8')
        calc_tag_buf.extstring2 = calc_tag_prop_buf.szExtString2.decode('utf-8')

        calc_tag_buf.calcmode = calc_tag_prop_buf.nCalcMode
        calc_tag_buf.ncalcperiod = calc_tag_prop_buf.nCalcPeriod
        calc_tag_buf.calcexpr = calc_tag_prop_buf.szCalcExpr.decode('utf-8')
        calc_tag_buf.calchistory = calc_tag_prop_buf.bCalcHistory
        calc_tag_buf.calcstartdate = calc_tag_prop_buf.nCalcStartDate
        calc_tag_buf.calcenddate = calc_tag_prop_buf.nCalcEndDate
        calc_tag_buf.haveendtime = calc_tag_prop_buf.bHaveEndTime
        calc_tag_buf.sourcetagname = calc_tag_prop_buf.szSourceTagName.decode('utf-8')

        return calc_tag_buf

    '''
    ## modify CalcTag's properties
    # @param prop: prop is a dictionary including attributes be set to a calctag 
    # @note: the dictionary must follow special formation,such as
                    [{'tagname':'catag0',
                       'calcperiod' :6,
                       'calcexpr': '\'srctag\'+2',
                       'descriptor':'123321'
                       }]
      tagname is used to confirm which tag is needed to modify
    # some properties cann't be modified,such as 'tagname'
    '''

    def modify_calc_tag_prop(self, prop):
        calc_tag_prop_buf = hyperdb.CalcTagProp()
        calc_tagid = c_uint32()
        mask = hyperdb.HD3Mask()
        comm_mask = 0
        tag_ext_mask = 0
        if 'tagid' in prop:
            calc_tagid = prop['tagid']
        if 'tagname' in prop:
            tagname = prop['tagname']
            calc_tagid = self.get_tag(tagname).tagid
        if 'descriptor' in prop:
            calc_tag_prop_buf.szDescriptor = prop['descriptor'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)
        if 'calcperiod' in prop:
            calc_tag_prop_buf.nCalcPeriod = prop['calcperiod']
            tag_ext_mask = hyperdb.get_calc_ext_mask('calcperiod', tag_ext_mask)
        if 'calcexpr' in prop:
            calc_tag_prop_buf.szCalcExpr = prop['calcexpr'].encode('utf-8')
            tag_ext_mask = hyperdb.get_calc_ext_mask('calcexpr', tag_ext_mask)

        mask.commmask = c_int64(comm_mask)
        mask.extmask = c_int64(tag_ext_mask)
        hyperdb.api.ca3_modify_tag_prop.argtypes = [c_uint32, POINTER(hyperdb.CalcTagProp), POINTER(hyperdb.HD3Mask)]
        retcode = hyperdb.api.ca3_modify_tag_prop(calc_tagid, byref(calc_tag_prop_buf), byref(mask))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return hyperdb.hd_sucess

    '''
    ## modify CalcTags' properties
    # @param attributes: props is a list of dictionary including attributes be set to a calctag 
    # @note: the dictionary must follow special formation,such as
                    [{'tagname':'catag0',
                       'calcperiod' :6,
                       'calcexpr': '\'srctag\'+2',
                       'descriptor':'123321'
                       },
                      {'tagname':'catag1',
                       'calcperiod' :7,
                       'calcexpr': '\'srctag\'+3',
                       'descriptor':'234432'
                       }]
      tagname is used to confirm which tag is needed to modify
    # some properties cann't be modified,such as 'tagname'
    '''

    def modify_calc_tags_prop(self, props):
        number = len(props)
        tag_prop_buf = (number * hyperdb.CalcTagProp)()
        tagids = (number * c_uint32)()
        count = 0
        mask = (number * hyperdb.HD3Mask)()
        for attributes in props:
            comm_mask = 0
            tag_ext_mask = 0
            if 'tagid' in attributes:
                tagids[count] = attributes['tagid']
            if 'tagname' in attributes:
                tagname = attributes['tagname']
                tagids[count] = self.get_tag(tagname).tagid
            if tagids[count] == '':
                continue
            if 'descriptor' in attributes:
                tag_prop_buf[count].szDescriptor = attributes['descriptor'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)

            if 'calcperiod' in attributes:
                tag_prop_buf[count].nCalcPeriod = attributes['calcperiod']
                tag_ext_mask = hyperdb.get_calc_ext_mask('calcperiod', tag_ext_mask)
            if 'calcexpr' in attributes:
                tag_prop_buf[count].szCalcExpr = attributes['calcexpr'].encode('utf-8')
                tag_ext_mask = hyperdb.get_calc_ext_mask('calcexpr', tag_ext_mask)

            # mask = hyperdb.HD3Mask()
            mask[count].commmask = c_int64(comm_mask)
            mask[count].extmask = c_int64(tag_ext_mask)
            # tagmask.append(mask)
            count += 1
        errorcode = (number * c_int32)()
        hyperdb.api.ca3_modify_tags_prop.argtypes = [c_int32, POINTER(c_uint32), POINTER(hyperdb.CalcTagProp),
                                                     POINTER(hyperdb.HD3Mask), POINTER(c_int32)]
        retcode = hyperdb.api.ca3_modify_tags_prop(number, tagids, tag_prop_buf, mask, errorcode)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return hyperdb.hd_sucess

    '''
    ## modify PtTags' properties
    # @param attributes: props is a list of dictionary including attributes be set to a pt tag 
    # @note: the dictionary must follow special formation,such as
                    [{'tagname':'tag0',
                       'descriptor':'123321'
                       },
                      {'tagname':'tag1',
                       'descriptor':'234432'
                       }]
      tagname is used to confirm which tag is needed to modify
    # some properties cann't be modified,such as 'tagname'
    '''

    def modify_tags_attributes(self, props):
        number = len(props)
        tag_prop_buf = (number * hyperdb.TagProp)()
        tagids = (number * c_uint32)()
        count = 0
        mask = (number * hyperdb.HD3Mask)()
        for attributes in props:
            comm_mask = 0
            tag_ext_mask = 0
            if 'tagid' in attributes:
                tagids[count] = attributes['tagid']
            if 'tagname' in attributes:
                tagname = attributes['tagname']
                tagids[count] = self.get_tag(tagname).tagid
            if tagids[count] == '':
                continue
            # comm attributes
            if 'archiving' in attributes:
                tag_prop_buf[count].bArchiving = int(attributes['archiving'])
                comm_mask = hyperdb.get_comm_mask('archiving', comm_mask)

            if 'compdev' in attributes:
                tag_prop_buf[count].fCompDev = attributes['compdev']
                comm_mask = hyperdb.get_comm_mask('compdev', comm_mask)
            if 'compmaxtime' in attributes:
                tag_prop_buf[count].nCompMaxTime = attributes['compmaxtime']
                comm_mask = hyperdb.get_comm_mask('compmaxtime', comm_mask)
            if 'comptype' in attributes:
                tag_prop_buf[count].nCompType = attributes['comptype']
                comm_mask = hyperdb.get_comm_mask('comptype', comm_mask)

            if 'recaccess' in attributes:
                tag_prop_buf[count].nRecAccess = attributes['recaccess']
                comm_mask = hyperdb.get_comm_mask('recaccess', comm_mask)
            if 'recnormalgroup' in attributes:
                tag_prop_buf[count].szRecNormalGroup = attributes['recnormalgroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('recnormalgroup', comm_mask)
            if 'recownergroup' in attributes:
                tag_prop_buf[count].szRecOwnerGroup = attributes['recownergroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('recownergroup', comm_mask)

            if 'descriptor' in attributes:
                tag_prop_buf[count].szDescriptor = attributes['descriptor'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)
            if 'digitalset' in attributes:
                tag_prop_buf[count].szDigitalSet = attributes['digitalset'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('digitalset', comm_mask)
            if 'engunit' in attributes:
                tag_prop_buf[count].szEngUnits = attributes['engunit'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('engunit', comm_mask)
            if 'excmaxtime' in attributes:
                tag_prop_buf[count].fExcMaxTime = attributes['excmaxtime']
                comm_mask = hyperdb.get_comm_mask('excmaxtime', comm_mask)
            if 'excdev' in attributes:
                tag_prop_buf[count].fExcDev = attributes['excdev']
                comm_mask = hyperdb.get_comm_mask('excdev', comm_mask)

            if 'tagaccess' in attributes:
                tag_prop_buf[count].nTagAccess = attributes['tagaccess']
                comm_mask = hyperdb.get_comm_mask('tagaccess', comm_mask)
            if 'tagnormalgroup' in attributes:
                tag_prop_buf[count].szTagNormalGroup = attributes['tagnormalgroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('tagnormalgroup', comm_mask)
            if 'tagownergroup' in attributes:
                tag_prop_buf[count].szTagOwnerGroup = attributes['tagownergroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('tagownergroup', comm_mask)

            if 'scan' in attributes:
                tag_prop_buf[count].bScan = attributes['scan']
                comm_mask = hyperdb.get_comm_mask('scan', comm_mask)
            if 'span' in attributes:
                tag_prop_buf[count].fSpan = attributes['span']
                comm_mask = hyperdb.get_comm_mask('span', comm_mask)
            if 'minvalue' in attributes:
                tag_prop_buf[count].fMinValue = attributes['minvalue']
                comm_mask = hyperdb.get_comm_mask('minvalue', comm_mask)

            if 'tagavaliable' in attributes:
                tag_prop_buf[count].nTagAvaliable = attributes['tagavaliable']
                comm_mask = hyperdb.get_comm_mask('tagavaliable', comm_mask)
            if 'opcstate' in attributes:
                tag_prop_buf[count].nOpcState = attributes['opcstate']
                comm_mask = hyperdb.get_comm_mask('opcstate', comm_mask)

            if 'imvstring' in attributes:
                tag_prop_buf[count].szImvString = attributes['imvstring'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('imvstring', comm_mask)

            if 'extstring1' in attributes:
                tag_prop_buf[count].szExtString1 = attributes['extstring1'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('extstring1', comm_mask)

            if 'extstring2' in attributes:
                tag_prop_buf[count].szExtString2 = attributes['extstring2'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('extstring2', comm_mask)

            if 'param1' in attributes:
                tag_prop_buf[count].nParam1 = attributes['param1']
                comm_mask = hyperdb.get_comm_mask('param1', comm_mask)
            if 'param2' in attributes:
                tag_prop_buf[count].nParam2 = attributes['param2']
                comm_mask = hyperdb.get_comm_mask('param2', comm_mask)
            if 'param3' in attributes:
                tag_prop_buf[count].nParam3 = attributes['param3']
                comm_mask = hyperdb.get_comm_mask('param3', comm_mask)
            if 'param4' in attributes:
                tag_prop_buf[count].nParam4 = attributes['param4']
                comm_mask = hyperdb.get_comm_mask('param4', comm_mask)
            if 'param5' in attributes:
                tag_prop_buf[count].nParam5 = attributes['param5']
                comm_mask = hyperdb.get_comm_mask('param5', comm_mask)

            # ext attributes
            if 'instrumentaddress' in attributes:
                tag_prop_buf[count].szInstrumentAddress = attributes['instrumentaddress'].encode('utf-8')
                tag_ext_mask = hyperdb.get_tag_ext_mask('instrumentaddress', tag_ext_mask)
            if 'instrumentaddresstype' in attributes:
                tag_prop_buf[count].nInstrumentAddressType = c_uint8(attributes['instrumentaddresstype'])
                tag_ext_mask = hyperdb.get_tag_ext_mask('instrumentaddresstype', tag_ext_mask)
            if 'collectorname' in attributes:
                tag_prop_buf[count].szCollectorName = attributes['collectorname'].encode('utf-8')
                tag_ext_mask = hyperdb.get_tag_ext_mask('collectorname', tag_ext_mask)

            if 'devicename' in attributes:
                tag_prop_buf[count].szDeviceName = attributes['devicename'].encode('utf-8')
                tag_ext_mask = hyperdb.get_tag_ext_mask('devicename', tag_ext_mask)

            if 'scangroupname' in attributes:
                tag_prop_buf[count].szScanGroupName = attributes['scangroupname'].encode('utf-8')
                tag_ext_mask = hyperdb.get_tag_ext_mask('scangroupname', tag_ext_mask)

            if 'dataorder' in attributes:
                tag_prop_buf[count].nDataOrder = attributes['dataorder']
                tag_ext_mask = hyperdb.get_tag_ext_mask('dataorder', tag_ext_mask)

            # mask = hyperdb.HD3Mask()
            mask[count].commmask = c_int64(comm_mask)
            mask[count].extmask = c_int64(tag_ext_mask)
            # tagmask.append(mask)
            count += 1
        errorcode = (number * c_int32)()
        hyperdb.api.pt3_modify_tags_prop.argtypes = [c_int32, POINTER(c_uint32), POINTER(hyperdb.TagProp),
                                                     POINTER(hyperdb.HD3Mask), POINTER(c_int32)]
        retcode = hyperdb.api.pt3_modify_tags_prop(number, tagids, tag_prop_buf, mask, errorcode)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return hyperdb.hd_sucess

    '''
    # query calctags which satisfy the conditions
    # @param conds: the conditions used to query calctags which satisfied with ,such as
    [('tagname','like','*')]
    # @return: return a iterator of calctags
    '''

    def query_calc_tags_cond(self, conds):
        number = len(conds)
        conds_buf = (number * hyperdb.HD3FilterItem)()
        condset = hyperdb.HD3FilterItemSet()

        count = 0
        for cond in conds:
            try:
                conds_buf[count].nPropItemID = hyperdb.tag_col_dict[cond[0]]
                conds_buf[count].nRelation = hyperdb.relation_dict[cond[1]]
                if cond[0] == 'tagtype':
                    conds_buf[count].szValue = str(hyperdb.tag_type_dict[cond[2]]).encode('utf-8')
                else:
                    conds_buf[count].szValue = cond[2].encode('utf-8')
            except KeyError:
                raise hyperdb.HDError(errcode=0, errinfo='the key is not exist!')

            count = count + 1

        condset.nSize = c_int32(number)
        condset.pItem = conds_buf

        hditer = c_void_p()
        mask = hyperdb.HD3Mask()
        mask.commmask = c_int64(0XFFFFFFFFFFFFFFFF)
        mask.extmask = c_int64(0XFFFFFFFFFFFFFFFF)

        hyperdb.api.ca3_query_tags_cond.argtypes = [POINTER(hyperdb.HD3FilterItemSet), POINTER(hyperdb.HD3Mask),
                                                    POINTER(c_void_p)]
        retcode = hyperdb.api.ca3_query_tags_cond(byref(condset), byref(mask), byref(hditer))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)

        tag_prop = hyperdb.CalcTagProp()
        iterate = hyperdb.Iterate(hditer, tag_prop)
        return iterate

    ##添加统计点
    # @param attributes: 字典stats_attr = {'tagname': 'sttag0',
    #                      'tagtype': 'float64',
    #                      'sourcetagname':'srctag',
    #                      'samplestartdate': 0,
    #                      'sampleinterval':10,
    #                      'samplemode':0,
    #                      'statstype': 0,
    #                      'calcperiod':1,
    #                      'scanperiod':10,
    #                      'sampletype':0,
    #                      'samplestartdate': 1
    #                      }
    # 具体属性见StatsTagProp
    # @return: stats_tagid

    def add_stats_tag(self, **attributes):
        stats_tag_prop_buf = hyperdb.StatsTagProp()
        if 'tagname' not in attributes or 'tagtype' not in attributes:
            raise ArgumentError

        comm_mask = 0
        stats_tag_ext_mask = 0

        stats_tag_prop_buf.szTagName = attributes['tagname'].encode('utf-8')
        if not stats_tag_prop_buf.szTagName.strip():
            raise ArgumentError
        comm_mask = hyperdb.get_comm_mask('tagname', comm_mask)

        try:
            stats_tag_prop_buf.nTagType = hyperdb.tag_type_dict[attributes['tagtype']]
        except:
            raise ArgumentError
        comm_mask = hyperdb.get_comm_mask('tagtype', comm_mask)

        if 'archiving' in attributes:
            stats_tag_prop_buf.bArchiving = attributes['archiving']
            comm_mask = hyperdb.get_comm_mask('archiving', comm_mask)

        if 'changedate' in attributes:
            stats_tag_prop_buf.nChangeDate = attributes['changedate']
            comm_mask = hyperdb.get_comm_mask('changedate', comm_mask)
        if 'changer' in attributes:
            stats_tag_prop_buf.szChanger = attributes['changer'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('changer', comm_mask)

        if 'compdev' in attributes:
            stats_tag_prop_buf.fCompDev = attributes['compdev']
            comm_mask = hyperdb.get_comm_mask('compdev', comm_mask)
        if 'compmaxtime' in attributes:
            stats_tag_prop_buf.nCompMaxTime = attributes['compmaxtime']
            comm_mask = hyperdb.get_comm_mask('compmaxtime', comm_mask)
        if 'comptype' in attributes:
            stats_tag_prop_buf.nCompType = attributes['comptype']
            comm_mask = hyperdb.get_comm_mask('comptype', comm_mask)

        if 'creationdate' in attributes:
            stats_tag_prop_buf.nCreationDate = attributes['creationdate']
            comm_mask = hyperdb.get_comm_mask('creationdate', comm_mask)
        if 'creator' in attributes:
            stats_tag_prop_buf.szCreator = attributes['creator'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('creator', comm_mask)

        if 'recaccess' in attributes:
            stats_tag_prop_buf.nRecAccess = attributes['recaccess']
            comm_mask = hyperdb.get_comm_mask('recaccess', comm_mask)
        if 'recnormalgroup' in attributes:
            stats_tag_prop_buf.szRecNormalGroup = attributes['recnormalgroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('recnormalgroup', comm_mask)
        if 'recownergroup' in attributes:
            stats_tag_prop_buf.szRecOwnerGroup = attributes['recownergroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('recownergroup', comm_mask)

        if 'descriptor' in attributes:
            stats_tag_prop_buf.szDescriptor = attributes['descriptor'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)
        if 'digitalset' in attributes:
            stats_tag_prop_buf.szDigitalSet = attributes['digitalset'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('digitalset', comm_mask)
        if 'engunit' in attributes:
            stats_tag_prop_buf.szEngUnits = attributes['engunit'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('engunit', comm_mask)
        if 'excmaxtime' in attributes:
            stats_tag_prop_buf.fExcMaxTime = attributes['excmaxtime']
            comm_mask = hyperdb.get_comm_mask('excmaxtime', comm_mask)
        if 'excdev' in attributes:
            stats_tag_prop_buf.fExcDev = attributes['excdev']
            comm_mask = hyperdb.get_comm_mask('excdev', comm_mask)

        if 'tagid' in attributes:
            stats_tag_prop_buf.nTagID = attributes['tagid']
            comm_mask = hyperdb.get_comm_mask('tagid', comm_mask)
        if 'tagaccess' in attributes:
            stats_tag_prop_buf.nTagAccess = attributes['tagaccess']
            comm_mask = hyperdb.get_comm_mask('tagaccess', comm_mask)
        if 'tagnormalgroup' in attributes:
            stats_tag_prop_buf.szTagNormalGroup = attributes['tagnormalgroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('tagnormalgroup', comm_mask)
        if 'tagownergroup' in attributes:
            stats_tag_prop_buf.szTagOwnerGroup = attributes['tagownergroup'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('tagownergroup', comm_mask)

        if 'scan' in attributes:
            stats_tag_prop_buf.bScan = attributes['scan']
            comm_mask = hyperdb.get_comm_mask('scan', comm_mask)
        if 'span' in attributes:
            stats_tag_prop_buf.fSpan = attributes['span']
            comm_mask = hyperdb.get_comm_mask('span', comm_mask)
        if 'minvalue' in attributes:
            stats_tag_prop_buf.fMinValue = attributes['minvalue']
            comm_mask = hyperdb.get_comm_mask('minvalue', comm_mask)
        if 'tagavaliable' in attributes:
            stats_tag_prop_buf.nTagAvaliable = attributes['tagavaliable']
            comm_mask = hyperdb.get_comm_mask('tagavaliable', comm_mask)
        if 'tagclass' in attributes:
            stats_tag_prop_buf.nTagClass = attributes['tagclass']
            comm_mask = hyperdb.get_comm_mask('tagclass', comm_mask)

        if 'imvstring' in attributes:
            stats_tag_prop_buf.szImvString = attributes['imvstring'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('imvstring', comm_mask)
        if 'extstring1' in attributes:
            stats_tag_prop_buf.szExtString1 = attributes['extstring1'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('extstring1', comm_mask)
        if 'extstring2' in attributes:
            stats_tag_prop_buf.szExtString2 = attributes['extstring2'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('extstring2', comm_mask)

        if 'param1' in attributes:
            stats_tag_prop_buf.nParam1 = attributes['param1']
            comm_mask = hyperdb.get_comm_mask('param1', comm_mask)
        if 'param2' in attributes:
            stats_tag_prop_buf.nParam2 = attributes['param2']
            comm_mask = hyperdb.get_comm_mask('param2', comm_mask)
        if 'param3' in attributes:
            stats_tag_prop_buf.nParam3 = attributes['param3']
            comm_mask = hyperdb.get_comm_mask('param3', comm_mask)
        if 'param4' in attributes:
            stats_tag_prop_buf.nParam4 = attributes['param4']
            comm_mask = hyperdb.get_comm_mask('param4', comm_mask)
        if 'param5' in attributes:
            stats_tag_prop_buf.nParam5 = attributes['param5']
            comm_mask = hyperdb.get_comm_mask('param5', comm_mask)
        if 'opcstate' in attributes:
            stats_tag_prop_buf.nOpcState = attributes['opcstate']
            comm_mask = hyperdb.get_comm_mask('opcstate', comm_mask)

        if 'sourcetagname' in attributes:
            stats_tag_prop_buf.szSourceTagName = attributes['sourcetagname'].encode('utf-8')
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('sourcetagname', stats_tag_ext_mask)
        if 'statstype' in attributes:
            stats_tag_prop_buf.nStatsType = attributes['statstype']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('statstype', stats_tag_ext_mask)
        if 'samplestartdate' in attributes:
            stats_tag_prop_buf.nSampleStartDate = attributes['samplestartdate']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('samplestartdate', stats_tag_ext_mask)
        if 'samplemode' in attributes:
            stats_tag_prop_buf.nSampleMode = attributes['samplemode']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('samplemode', stats_tag_ext_mask)
        if 'sampleinterval' in attributes:
            stats_tag_prop_buf.nSampleInterval = attributes['sampleinterval']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('sampleinterval', stats_tag_ext_mask)
        if 'samplebeginoffset' in attributes:
            stats_tag_prop_buf.nSampleBeginOffset = attributes['samplebeginoffset']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('samplebeginoffset', stats_tag_ext_mask)
        if 'sampleendoffset' in attributes:
            stats_tag_prop_buf.nSampleEndOffset = attributes['sampleendoffset']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('sampleendoffset', stats_tag_ext_mask)
        if 'samplemonthbeginday' in attributes:
            stats_tag_prop_buf.nSampleMonthBeginDay = attributes['samplemonthbeginday']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('samplemonthbeginday', stats_tag_ext_mask)
        if 'samplemonthendday' in attributes:
            stats_tag_prop_buf.nSampleMonthEndDay = attributes['samplemonthendday']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('samplemonthendday', stats_tag_ext_mask)
        if 'begintimestamp' in attributes:
            stats_tag_prop_buf.bBeginTimeStamp = attributes['begintimestamp']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('begintimestamp', stats_tag_ext_mask)
        if 'timestampoffset' in attributes:
            stats_tag_prop_buf.nTimeStampOffset = attributes['timestampoffset']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('timestampoffset', stats_tag_ext_mask)
        if 'scanperiod' in attributes:
            stats_tag_prop_buf.nScanPeriod = attributes['scanperiod']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('scanperiod', stats_tag_ext_mask)
        if 'pctgood' in attributes:
            stats_tag_prop_buf.fPctGood = attributes['pctgood']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('pctgood', stats_tag_ext_mask)
        if 'filterexpr' in attributes:
            stats_tag_prop_buf.szFilterExpr = attributes['filterexpr'].encode('utf-8')
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('filterexpr', stats_tag_ext_mask)
        if 'dropbadmode' in attributes:
            stats_tag_prop_buf.nDropBadMode = attributes['dropbadmode']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('dropbadmode', stats_tag_ext_mask)
        if 'countparam1' in attributes:
            stats_tag_prop_buf.dCountParam1 = attributes['countparam1']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('countparam1', stats_tag_ext_mask)
        if 'countparam2' in attributes:
            stats_tag_prop_buf.dCountParam2 = attributes['countparam2']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('countparam2', stats_tag_ext_mask)
        if 'sampletype' in attributes:
            stats_tag_prop_buf.nSampleType = attributes['sampletype']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('sampletype', stats_tag_ext_mask)
        if 'interpoffset' in attributes:
            stats_tag_prop_buf.nInterpOffset = attributes['interpoffset']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('interpoffset', stats_tag_ext_mask)
        if 'interpperiod' in attributes:
            stats_tag_prop_buf.nInterpPeriod = attributes['interpperiod']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('interpperiod', stats_tag_ext_mask)
        if 'conversiontype' in attributes:
            stats_tag_prop_buf.nConversionType = attributes['conversiontype']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('conversiontype', stats_tag_ext_mask)
        if 'conversion' in attributes:
            stats_tag_prop_buf.dConversion = attributes['conversion']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('conversion', stats_tag_ext_mask)
        if 'filterfailmode' in attributes:
            stats_tag_prop_buf.nFilterFailMode = attributes['filterfailmode']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('filterfailmode', stats_tag_ext_mask)
        if 'calcperiod' in attributes:
            stats_tag_prop_buf.nCalcPeriod = attributes['calcperiod']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('calcperiod', stats_tag_ext_mask)
        if 'reset' in attributes:
            stats_tag_prop_buf.bReset = attributes['reset']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('reset', stats_tag_ext_mask)
        if 'usebadvalue' in attributes:
            stats_tag_prop_buf.bUseBadValue = attributes['usebadvalue']
            stats_tag_ext_mask = hyperdb.get_stats_ext_mask('usebadvalue', stats_tag_ext_mask)

        mask = hyperdb.HD3Mask()
        mask.commmask = c_int64(comm_mask)
        mask.extmask = c_int64(stats_tag_ext_mask)

        stats_tagid = c_uint32()

        hyperdb.api.st3_add_tag.argtypes = [POINTER(hyperdb.StatsTagProp), POINTER(hyperdb.HD3Mask), c_char_p, c_void_p]
        retcode = hyperdb.api.st3_add_tag(byref(stats_tag_prop_buf), byref(mask), None, byref(stats_tagid))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return stats_tagid

    ''' 
    # add a list of new statstags to server
    # @param props: a list of dictionary, such as 
                    [{'tagname': 'sttag0',
                      'tagtype': 'float64',
                     'szSourceTagName':'srctag',
                     'nSampleStartDate': 0,
                     'nSampleInterval':10,
                     'nSampleMode':0,
                     'nStatsType': 0,
                     'nCalcPeriod':1,
                     'nScanPeriod':10,
                     'nSampleType':0,
                     'nSampleStartDate':1
                     },{'tagname': 'sttag1',
                      'tagtype': 'float64',
                     'szSourceTagName':'srctag',
                     'nSampleStartDate': 0,
                     'nSampleInterval':10,
                     'nSampleMode':0,
                     'nStatsType': 0,
                     'nCalcPeriod':2,
                     'nScanPeriod':10,
                     'nSampleType':0,
                     'nSampleStartDate':1
                     }]
    # @note: the input dictionary attributes should include para of StatsTagProp
    # seen in tag.py
    '''

    def add_stats_tags(self, props):
        number = len(props)
        stats_tag_prop_buf = (number * hyperdb.StatsTagProp)()

        count = 0
        mask = (number * hyperdb.HD3Mask)()
        for attributes in props:
            if 'tagname' not in attributes or 'tagtype' not in attributes:
                raise ArgumentError

            comm_mask = 0
            stats_tag_ext_mask = 0

            stats_tag_prop_buf[count].szTagName = attributes['tagname'].encode('utf-8')
            if not stats_tag_prop_buf[count].szTagName.strip():
                raise ArgumentError
            comm_mask = hyperdb.get_comm_mask('tagname', comm_mask)

            stats_tag_prop_buf[count].nTagType = hyperdb.tag_type_dict[attributes['tagtype']]
            comm_mask = hyperdb.get_comm_mask('tagtype', comm_mask)

            if 'archiving' in attributes:
                stats_tag_prop_buf[count].bArchiving = attributes['archiving']
                comm_mask = hyperdb.get_comm_mask('archiving', comm_mask)

            if 'compdev' in attributes:
                stats_tag_prop_buf[count].fCompDev = attributes['compdev']
                comm_mask = hyperdb.get_comm_mask('compdev', comm_mask)
            if 'compmaxtime' in attributes:
                stats_tag_prop_buf[count].nCompMaxTime = attributes['compmaxtime']
                comm_mask = hyperdb.get_comm_mask('compmaxtime', comm_mask)
            if 'comptype' in attributes:
                stats_tag_prop_buf[count].nCompType = attributes['comptype']
                comm_mask = hyperdb.get_comm_mask('comptype', comm_mask)

            if 'recaccess' in attributes:
                stats_tag_prop_buf[count].nRecAccess = attributes['recaccess']
                comm_mask = hyperdb.get_comm_mask('recaccess', comm_mask)
            if 'recnormalgroup' in attributes:
                stats_tag_prop_buf[count].szRecNormalGroup = attributes['recnormalgroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('recnormalgroup', comm_mask)
            if 'recownergroup' in attributes:
                stats_tag_prop_buf[count].szRecOwnerGroup = attributes['recownergroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('recownergroup', comm_mask)

            if 'descriptor' in attributes:
                stats_tag_prop_buf[count].szDescriptor = attributes['descriptor'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)
            if 'digitalset' in attributes:
                stats_tag_prop_buf[count].szDigitalSet = attributes['digitalset'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('digitalset', comm_mask)
            if 'engunit' in attributes:
                stats_tag_prop_buf[count].szEngUnits = attributes['engunit'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('engunit', comm_mask)
            if 'excmaxtime' in attributes:
                stats_tag_prop_buf[count].fExcMaxTime = attributes['excmaxtime']
                comm_mask = hyperdb.get_comm_mask('excmaxtime', comm_mask)
            if 'excdev' in attributes:
                stats_tag_prop_buf[count].fExcDev = attributes['excdev']
                comm_mask = hyperdb.get_comm_mask('excdev', comm_mask)

            if 'tagaccess' in attributes:
                stats_tag_prop_buf[count].nTagAccess = attributes['tagaccess']
                comm_mask = hyperdb.get_comm_mask('tagaccess', comm_mask)
            if 'tagnormalgroup' in attributes:
                stats_tag_prop_buf[count].szTagNormalGroup = attributes['tagnormalgroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('tagnormalgroup', comm_mask)
            if 'tagownergroup' in attributes:
                stats_tag_prop_buf[count].szTagOwnerGroup = attributes['tagownergroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('tagownergroup', comm_mask)

            if 'scan' in attributes:
                stats_tag_prop_buf[count].bScan = attributes['scan']
                comm_mask = hyperdb.get_comm_mask('scan', comm_mask)
            if 'span' in attributes:
                stats_tag_prop_buf[count].fSpan = attributes['span']
                comm_mask = hyperdb.get_comm_mask('span', comm_mask)
            if 'minvalue' in attributes:
                stats_tag_prop_buf[count].fMinValue = attributes['minvalue']
                comm_mask = hyperdb.get_comm_mask('minvalue', comm_mask)
            if 'tagavaliable' in attributes:
                stats_tag_prop_buf[count].nTagAvaliable = attributes['tagavaliable']
                comm_mask = hyperdb.get_comm_mask('tagavaliable', comm_mask)
            if 'opcstate' in attributes:
                stats_tag_prop_buf[count].nOpcState = attributes['opcstate']
                comm_mask = hyperdb.get_comm_mask('opcstate', comm_mask)

            if 'imvstring' in attributes:
                stats_tag_prop_buf[count].szImvString = attributes['imvstring'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('imvstring', comm_mask)
            if 'extstring1' in attributes:
                stats_tag_prop_buf[count].szExtString1 = attributes['extstring1'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('extstring1', comm_mask)
            if 'extstring2' in attributes:
                stats_tag_prop_buf[count].szExtString2 = attributes['extstring2'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('extstring2', comm_mask)

            if 'param1' in attributes:
                stats_tag_prop_buf[count].nParam1 = attributes['param1']
                comm_mask = hyperdb.get_comm_mask('param1', comm_mask)
            if 'param2' in attributes:
                stats_tag_prop_buf[count].nParam2 = attributes['param2']
                comm_mask = hyperdb.get_comm_mask('param2', comm_mask)
            if 'param3' in attributes:
                stats_tag_prop_buf[count].nParam3 = attributes['param3']
                comm_mask = hyperdb.get_comm_mask('param3', comm_mask)
            if 'param4' in attributes:
                stats_tag_prop_buf[count].nParam4 = attributes['param4']
                comm_mask = hyperdb.get_comm_mask('param4', comm_mask)
            if 'param5' in attributes:
                stats_tag_prop_buf[count].nParam5 = attributes['param5']
                comm_mask = hyperdb.get_comm_mask('param5', comm_mask)

            if 'sourcetagname' in attributes:
                stats_tag_prop_buf[count].szSourceTagName = attributes['sourcetagname'].encode('utf-8')
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('sourcetagname', stats_tag_ext_mask)

            if 'statstype' in attributes:
                stats_tag_prop_buf[count].nStatsType = attributes['statstype']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('statstype', stats_tag_ext_mask)

            if 'samplestartdate' in attributes:
                stats_tag_prop_buf[count].nSampleStartDate = attributes['samplestartdate']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('samplestartdate', stats_tag_ext_mask)

            if 'samplemode' in attributes:
                stats_tag_prop_buf[count].nSampleMode = attributes['samplemode']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('samplemode', stats_tag_ext_mask)

            if 'sampleinterval' in attributes:
                stats_tag_prop_buf[count].nSampleInterval = attributes['sampleinterval']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('sampleinterval', stats_tag_ext_mask)

            if 'samplebeginoffset' in attributes:
                stats_tag_prop_buf[count].nSampleBeginOffset = attributes['samplebeginoffset']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('samplebeginoffset', stats_tag_ext_mask)

            if 'sampleendoffset' in attributes:
                stats_tag_prop_buf[count].nSampleEndOffset = attributes['sampleendoffset']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('sampleendoffset', stats_tag_ext_mask)

            if 'samplemonthbeginday' in attributes:
                stats_tag_prop_buf[count].nSampleMonthBeginDay = attributes['samplemonthbeginday']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('samplemonthbeginday', stats_tag_ext_mask)

            if 'samplemonthendday' in attributes:
                stats_tag_prop_buf[count].nSampleMonthEndDay = attributes['samplemonthendday']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('samplemonthendday', stats_tag_ext_mask)

            if 'begintimestamp' in attributes:
                stats_tag_prop_buf[count].bBeginTimeStamp = attributes['begintimestamp']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('begintimestamp', stats_tag_ext_mask)

            if 'timestampoffset' in attributes:
                stats_tag_prop_buf[count].nTimeStampOffset = attributes['timestampoffset']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('timestampoffset', stats_tag_ext_mask)

            if 'scanperiod' in attributes:
                stats_tag_prop_buf[count].nScanPeriod = attributes['scanperiod']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('scanperiod', stats_tag_ext_mask)

            if 'pctgood' in attributes:
                stats_tag_prop_buf[count].fPctGood = attributes['pctgood']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('pctgood', stats_tag_ext_mask)

            if 'filterexpr' in attributes:
                stats_tag_prop_buf[count].szFilterExpr = attributes['filterexpr'].encode('utf-8')
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('filterexpr', stats_tag_ext_mask)

            if 'dropbadmode' in attributes:
                stats_tag_prop_buf[count].nDropBadMode = attributes['dropbadmode']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('dropbadmode', stats_tag_ext_mask)

            if 'countparam1' in attributes:
                stats_tag_prop_buf[count].dCountParam1 = attributes['countparam1']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('countparam1', stats_tag_ext_mask)

            if 'countparam2' in attributes:
                stats_tag_prop_buf[count].dCountParam2 = attributes['countparam2']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('countparam2', stats_tag_ext_mask)

            if 'sampletype' in attributes:
                stats_tag_prop_buf[count].nSampleType = attributes['sampletype']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('sampletype', stats_tag_ext_mask)

            if 'interpoffset' in attributes:
                stats_tag_prop_buf[count].nInterpOffset = attributes['interpoffset']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('interpoffset', stats_tag_ext_mask)

            if 'interpperiod' in attributes:
                stats_tag_prop_buf[count].nInterpPeriod = attributes['interpperiod']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('interpperiod', stats_tag_ext_mask)

            if 'conversionType' in attributes:
                stats_tag_prop_buf[count].nConversionType = attributes['conversionType']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('conversionType', stats_tag_ext_mask)

            if 'conversion' in attributes:
                stats_tag_prop_buf[count].dConversion = attributes['conversion']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('conversion', stats_tag_ext_mask)

            if 'filterFailMode' in attributes:
                stats_tag_prop_buf[count].nFilterFailMode = attributes['filterFailMode']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('filterFailMode', stats_tag_ext_mask)

            if 'calcperiod' in attributes:
                stats_tag_prop_buf[count].nCalcPeriod = attributes['calcperiod']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('calcperiod', stats_tag_ext_mask)

            if 'reset' in attributes:
                stats_tag_prop_buf[count].bReset = attributes['reset']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('reset', stats_tag_ext_mask)

            if 'usebadvalue' in attributes:
                stats_tag_prop_buf[count].bUseBadValue = attributes['usebadvalue']
                stats_tag_ext_mask = hyperdb.get_stats_ext_mask('usebadvalue', stats_tag_ext_mask)

                # mask = hyperdb.HD3Mask()
            mask[count].commmask = c_int64(comm_mask)
            mask[count].extmask = c_int64(stats_tag_ext_mask)
            # tagmask.append(mask)
            count += 1

        tagids = (number * c_uint32)()
        errorcode = (number * c_int32)()
        hyperdb.api.st3_add_tags.argtypes = [c_int32, POINTER(hyperdb.StatsTagProp), POINTER(hyperdb.HD3Mask), c_char_p,
                                             POINTER(c_uint32), POINTER(c_int32)]
        retcode = hyperdb.api.st3_add_tags(number, stats_tag_prop_buf, mask, None, tagids, errorcode)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return tagids

    '''
    ## modify StatsTags' properties
    # @param props: props is a dictionary including attributes be set to a statstag 
    # @note: the dictionary must follow special formation,such as
                    {'tagname':'sttag0',
                       'descriptor' :'123',
                       }
      tagname is used to confirm which tag is needed to modify
    # some properties cann't be modified,such as 'tagname'
    '''

    def modify_stats_tag_attributes(self, props):
        tag_prop_buf = hyperdb.StatsTagProp()
        tagid = c_uint32()
        mask = hyperdb.HD3Mask()
        comm_mask = 0
        tag_ext_mask = 0

        if 'tagid' in props:
            tagid = props['tagid']
        if 'tagname' in props:
            tagname = props['tagname']
            tagid = self.get_tag(tagname).tagid
        if 'descriptor' in props:
            tag_prop_buf.szDescriptor = props['descriptor'].encode('utf-8')
            comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)

        mask.commmask = c_int64(comm_mask)
        mask.extmask = c_int64(tag_ext_mask)
        hyperdb.api.st3_modify_tag_prop.argtypes = [c_uint32, POINTER(hyperdb.StatsTagProp),
                                                    POINTER(hyperdb.HD3Mask)]
        retcode = hyperdb.api.st3_modify_tag_prop(tagid, tag_prop_buf, mask)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return hyperdb.hd_sucess

    '''
    ## modify StatsTags' properties
    # @param attributes: props is a list of dictionary including attributes be set to a statstag 
    # @note: the dictionary must follow special formation,such as
                    [{'tagname':'sttag0',
                       'descriptor' :'123',
                       },
                      {'tagname':'sttag1',
                       'descriptor' :'234',
                       }]
      tagname is used to confirm which tag is needed to modify
    # some properties cann't be modified,such as 'tagname'
    '''

    def modify_stats_tags_attributes(self, props):
        number = len(props)
        tag_prop_buf = (number * hyperdb.StatsTagProp)()
        tagids = (number * c_uint32)()
        count = 0
        mask = (number * hyperdb.HD3Mask)()
        for attributes in props:
            comm_mask = 0
            tag_ext_mask = 0
            if 'tagid' in attributes:
                tagids[count] = attributes['tagid']
            if 'tagname' in attributes:
                tagname = attributes['tagname']
                tagids[count] = self.get_tag(tagname).tagid
            if tagids[count] == '':
                continue

            if 'descriptor' in attributes:
                tag_prop_buf[count].szDescriptor = attributes['descriptor'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)

            # mask = hyperdb.HD3Mask()
            mask[count].commmask = c_int64(comm_mask)
            mask[count].extmask = c_int64(tag_ext_mask)
            # tagmask.append(mask)
            count += 1
        errorcode = (number * c_int32)()
        hyperdb.api.st3_modify_tags_prop.argtypes = [c_int32, POINTER(c_uint32), POINTER(hyperdb.StatsTagProp),
                                                     POINTER(hyperdb.HD3Mask), POINTER(c_int32)]
        retcode = hyperdb.api.st3_modify_tags_prop(number, tagids, tag_prop_buf, mask, errorcode)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return hyperdb.hd_sucess

    # get a instance of statstag from server by tagname
    # @param stats_tagname: the name of the statstag which will be get
    # @return: return a instance of statstag
    def query_stats_tag_prop(self, stats_tagname):
        if type(stats_tagname) != str:
            raise TypeError

        stats_tagid = c_uint32()
        stats_tagname_buf = stats_tagname.encode('utf-8')
        stats_tag_prop_buf = hyperdb.StatsTagProp()

        hyperdb.api.tag3_query_id_by_name.argtypes = (c_char_p, c_void_p)
        retcode = hyperdb.api.tag3_query_id_by_name(stats_tagname_buf, byref(stats_tagid))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

        hyperdb.api.st3_query_tag_prop.argtypes = [c_uint32, POINTER(hyperdb.StatsTagProp)]
        retcode = hyperdb.api.st3_query_tag_prop(stats_tagid, byref(stats_tag_prop_buf))
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

        stats_tag_buf = tag.StatsTag()
        stats_tag_buf.tagname = stats_tag_prop_buf.szTagName.decode('utf-8')
        stats_tag_buf.archiving = stats_tag_prop_buf.bArchiving
        stats_tag_buf.changedate = stats_tag_prop_buf.nChangeDate
        stats_tag_buf.changer = stats_tag_prop_buf.szChanger.decode('utf-8')

        stats_tag_buf.compdev = stats_tag_prop_buf.fCompDev
        stats_tag_buf.compmaxtime = stats_tag_prop_buf.nCompMaxTime
        stats_tag_buf.comptype = stats_tag_prop_buf.nCompType
        stats_tag_buf.creationdate = stats_tag_prop_buf.nCreationDate
        stats_tag_buf.creator = stats_tag_prop_buf.szCreator.decode('utf-8')

        stats_tag_buf.recaccess = stats_tag_prop_buf.nRecAccess
        stats_tag_buf.recnormalgroup = stats_tag_prop_buf.szRecNormalGroup.decode('utf-8')
        stats_tag_buf.recownergroup = stats_tag_prop_buf.szRecOwnerGroup.decode('utf-8')

        stats_tag_buf.descriptor = stats_tag_prop_buf.szDescriptor.decode('utf-8')
        stats_tag_buf.digitalset = stats_tag_prop_buf.szDigitalSet.decode('utf-8')
        stats_tag_buf.engunit = stats_tag_prop_buf.szEngUnits.decode('utf-8')
        stats_tag_buf.excmaxtime = stats_tag_prop_buf.nExcMaxTime
        stats_tag_buf.excdev = stats_tag_prop_buf.fExcDev

        stats_tag_buf.tagid = stats_tag_prop_buf.nTagID

        for value in hyperdb.tag_type_dict:
            if hyperdb.tag_type_dict[value] == stats_tag_prop_buf.nTagType:
                stats_tag_buf.tagtype = value

        stats_tag_buf.tagaccess = stats_tag_prop_buf.nTagAccess
        stats_tag_buf.tagnormalgroup = stats_tag_prop_buf.szTagNormalGroup.decode('utf-8')
        stats_tag_buf.tagownergroup = stats_tag_prop_buf.szTagOwnerGroup.decode('utf-8')

        stats_tag_buf.scan = stats_tag_prop_buf.bScan
        stats_tag_buf.span = stats_tag_prop_buf.fSpan

        stats_tag_buf.minvalue = stats_tag_prop_buf.fMinValue
        stats_tag_buf.tagclass = stats_tag_prop_buf.nTagClass
        stats_tag_buf.tagavaliable = stats_tag_prop_buf.nTagAvaliable

        stats_tag_buf.param1 = stats_tag_prop_buf.nParam1
        stats_tag_buf.param2 = stats_tag_prop_buf.nParam2
        stats_tag_buf.param3 = stats_tag_prop_buf.nParam3
        stats_tag_buf.param4 = stats_tag_prop_buf.nParam4
        stats_tag_buf.param5 = stats_tag_prop_buf.nParam5
        stats_tag_buf.opcstate = stats_tag_prop_buf.nOpcState

        stats_tag_buf.imvstring = stats_tag_prop_buf.szImvString.decode('utf-8')
        stats_tag_buf.extstring1 = stats_tag_prop_buf.szExtString1.decode('utf-8')
        stats_tag_buf.extstring2 = stats_tag_prop_buf.szExtString2.decode('utf-8')

        stats_tag_buf.sourcetagname = stats_tag_prop_buf.szSourceTagName.decode('utf-8')
        stats_tag_buf.statstype = stats_tag_prop_buf.nStatsType
        stats_tag_buf.samplestartdate = stats_tag_prop_buf.nSampleStartDate
        stats_tag_buf.samplemode = stats_tag_prop_buf.nSampleMode
        stats_tag_buf.sampleinterval = stats_tag_prop_buf.nSampleInterval
        stats_tag_buf.samplebeginoffset = stats_tag_prop_buf.nSampleBeginOffset
        stats_tag_buf.sampleendoffset = stats_tag_prop_buf.nSampleEndOffset
        stats_tag_buf.samplemonthbeginday = stats_tag_prop_buf.nSampleMonthBeginDay
        stats_tag_buf.samplemonthendday = stats_tag_prop_buf.nSampleMonthEndDay
        stats_tag_buf.begintimestamp = stats_tag_prop_buf.bBeginTimeStamp
        stats_tag_buf.timestampoffset = stats_tag_prop_buf.nTimeStampOffset
        stats_tag_buf.scanperiod = stats_tag_prop_buf.nScanPeriod
        stats_tag_buf.pctgood = stats_tag_prop_buf.fPctGood
        stats_tag_buf.filterexpr = stats_tag_prop_buf.szFilterExpr.decode('utf-8')
        stats_tag_buf.dropbadmode = stats_tag_prop_buf.nDropBadMode
        stats_tag_buf.countparam1 = stats_tag_prop_buf.dCountParam1
        stats_tag_buf.countparam2 = stats_tag_prop_buf.dCountParam2
        stats_tag_buf.sampletype = stats_tag_prop_buf.nSampleType
        stats_tag_buf.interpoffset = stats_tag_prop_buf.nInterpOffset
        stats_tag_buf.interpperiod = stats_tag_prop_buf.nInterpPeriod
        stats_tag_buf.conversiontype = stats_tag_prop_buf.nConversionType
        stats_tag_buf.conversion = stats_tag_prop_buf.dConversion
        stats_tag_buf.filterfailmode = stats_tag_prop_buf.nFilterFailMode
        stats_tag_buf.calcperiod = stats_tag_prop_buf.nCalcPeriod
        stats_tag_buf.reset = stats_tag_prop_buf.bReset
        stats_tag_buf.usebadvalue = stats_tag_prop_buf.bUseBadValue

        return stats_tag_buf

    '''
    # query statstags which satisfy the conditions
    # @param conds: the conditions used to query statstags which satisfied with ,such as
    [('tagname','like','*')]
    # @return: return a iterator of statstags
    '''

    def query_stats_tags_cond(self, conds):
        number = len(conds)
        conds_buf = (number * hyperdb.HD3FilterItem)()
        condset = hyperdb.HD3FilterItemSet()

        count = 0
        for cond in conds:
            try:
                conds_buf[count].nPropItemID = hyperdb.tag_col_dict[cond[0]]
                conds_buf[count].nRelation = hyperdb.relation_dict[cond[1]]
                if cond[0] == 'tagtype':
                    conds_buf[count].szValue = str(hyperdb.tag_type_dict[cond[2]]).encode('utf-8')
                else:
                    conds_buf[count].szValue = cond[2].encode('utf-8')
            except KeyError:
                raise hyperdb.HDError(errcode=0, errinfo='the key is not exist!')

            count = count + 1

        condset.nSize = c_int32(number)
        condset.pItem = conds_buf

        hditer = c_void_p()
        mask = hyperdb.HD3Mask()
        mask.commmask = c_int64(0XFFFFFFFFFFFFFFFF)
        mask.extmask = c_int64(0XFFFFFFFFFFFFFFFF)

        hyperdb.api.st3_query_tags_cond.argtypes = [POINTER(hyperdb.HD3FilterItemSet), POINTER(hyperdb.HD3Mask),
                                                    POINTER(c_void_p)]
        retcode = hyperdb.api.st3_query_tags_cond(byref(condset), byref(mask), byref(hditer))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)

        tag_prop = hyperdb.StatsTagProp()
        iterate = hyperdb.Iterate(hditer, tag_prop)
        return iterate

    ''' 
    # add a list of new alarm tags to server
    # @param props: a list of dictionary, such as 
                    [{'tagname': 'amtag0',
                      'tagtype': 'int32',
                      'sourcetagname': 'int8'
                     },{'tagname': 'amtag1',
                      'tagtype': 'int32',
                      'sourcetagname': 'int8'
                     }]
    # @note: the input dictionary attributes should include param of AlarmTagProp
    # seen in tag.py
    '''

    def add_alarm_tags(self, props):
        number = len(props)
        tag_prop_buf = (number * hyperdb.AlarmTagProp)()

        count = 0
        mask = (number * hyperdb.HD3Mask)()
        for attributes in props:
            if 'tagname' not in attributes:
                raise ArgumentError

            comm_mask = 0
            tag_ext_mask = 0

            tag_prop_buf[count].szTagName = attributes['tagname'].encode('utf-8')
            if not tag_prop_buf[count].szTagName.strip():
                raise ArgumentError
            comm_mask = hyperdb.get_comm_mask('tagname', comm_mask)

            tag_prop_buf[count].nTagType = 5  # digital
            comm_mask = hyperdb.get_comm_mask('tagtype', comm_mask)

            if 'archiving' in attributes:
                tag_prop_buf[count].bArchiving = attributes['archiving']
                comm_mask = hyperdb.get_comm_mask('archiving', comm_mask)

            if 'compdev' in attributes:
                tag_prop_buf[count].fCompDev = attributes['compdev']
                comm_mask = hyperdb.get_comm_mask('compdev', comm_mask)
            if 'compmaxtime' in attributes:
                tag_prop_buf[count].nCompMaxTime = attributes['compmaxtime']
                comm_mask = hyperdb.get_comm_mask('compmaxtime', comm_mask)
            if 'comptype' in attributes:
                tag_prop_buf[count].nCompType = attributes['comptype']
                comm_mask = hyperdb.get_comm_mask('comptype', comm_mask)

            if 'recaccess' in attributes:
                tag_prop_buf[count].nRecAccess = attributes['recaccess']
                comm_mask = hyperdb.get_comm_mask('recaccess', comm_mask)
            if 'recnormalgroup' in attributes:
                tag_prop_buf[count].szRecNormalGroup = attributes['recnormalgroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('recnormalgroup', comm_mask)
            if 'recownergroup' in attributes:
                tag_prop_buf[count].szRecOwnerGroup = attributes['recownergroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('recownergroup', comm_mask)

            if 'descriptor' in attributes:
                tag_prop_buf[count].szDescriptor = attributes['descriptor'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)
            if 'digitalset' in attributes:
                tag_prop_buf[count].szDigitalSet = attributes['digitalset'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('digitalset', comm_mask)
            if 'engunit' in attributes:
                tag_prop_buf[count].szEngUnits = attributes['engunit'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('engunit', comm_mask)
            if 'excmaxtime' in attributes:
                tag_prop_buf[count].fExcMaxTime = attributes['excmaxtime']
                comm_mask = hyperdb.get_comm_mask('excmaxtime', comm_mask)
            if 'excdev' in attributes:
                tag_prop_buf[count].fExcDev = attributes['excdev']
                comm_mask = hyperdb.get_comm_mask('excdev', comm_mask)

            if 'tagaccess' in attributes:
                tag_prop_buf[count].nTagAccess = attributes['tagaccess']
                comm_mask = hyperdb.get_comm_mask('tagaccess', comm_mask)
            if 'tagnormalgroup' in attributes:
                tag_prop_buf[count].szTagNormalGroup = attributes['tagnormalgroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('tagnormalgroup', comm_mask)
            if 'tagownergroup' in attributes:
                tag_prop_buf[count].szTagOwnerGroup = attributes['tagownergroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('tagownergroup', comm_mask)

            if 'scan' in attributes:
                tag_prop_buf[count].bScan = attributes['scan']
                comm_mask = hyperdb.get_comm_mask('scan', comm_mask)
            if 'span' in attributes:
                tag_prop_buf[count].fSpan = attributes['span']
                comm_mask = hyperdb.get_comm_mask('span', comm_mask)
            if 'minvalue' in attributes:
                tag_prop_buf[count].fMinValue = attributes['minvalue']
                comm_mask = hyperdb.get_comm_mask('minvalue', comm_mask)
            if 'tagavaliable' in attributes:
                tag_prop_buf[count].nTagAvaliable = attributes['tagavaliable']
                comm_mask = hyperdb.get_comm_mask('tagavaliable', comm_mask)
            if 'opcstate' in attributes:
                tag_prop_buf[count].nOpcState = attributes['opcstate']
                comm_mask = hyperdb.get_comm_mask('opcstate', comm_mask)

            if 'imvstring' in attributes:
                tag_prop_buf[count].szImvString = attributes['imvstring'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('imvstring', comm_mask)
            if 'extstring1' in attributes:
                tag_prop_buf[count].szExtString1 = attributes['extstring1'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('extstring1', comm_mask)
            if 'extstring2' in attributes:
                tag_prop_buf[count].szExtString2 = attributes['extstring2'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('extstring2', comm_mask)

            # ?��???��?�???    
            if 'test1' in attributes:
                tag_prop_buf[count].szTest1 = attributes['test1'].encode('utf-8')
                tag_ext_mask = hyperdb.get_alarm_ext_mask('test1', tag_ext_mask)
            if 'test2' in attributes:
                tag_prop_buf[count].szTest2 = attributes['test2'].encode('utf-8')
                tag_ext_mask = hyperdb.get_alarm_ext_mask('test2', tag_ext_mask)
            if 'test3' in attributes:
                tag_prop_buf[count].szTest3 = attributes['test3'].encode('utf-8')
                tag_ext_mask = hyperdb.get_alarm_ext_mask('test3', tag_ext_mask)
            if 'test4' in attributes:
                tag_prop_buf[count].szTest4 = attributes['test4'].encode('utf-8')
                tag_ext_mask = hyperdb.get_alarm_ext_mask('test4', tag_ext_mask)

            if 'action1' in attributes:
                tag_prop_buf[count].szAction1 = attributes['action1'].encode('utf-8')
                tag_ext_mask = hyperdb.get_alarm_ext_mask('action1', tag_ext_mask)
            if 'action2' in attributes:
                tag_prop_buf[count].szAction2 = attributes['action2'].encode('utf-8')
                tag_ext_mask = hyperdb.get_alarm_ext_mask('action2', tag_ext_mask)
            if 'action3' in attributes:
                tag_prop_buf[count].szAction3 = attributes['action3'].encode('utf-8')
                tag_ext_mask = hyperdb.get_alarm_ext_mask('action3', tag_ext_mask)
            if 'action4' in attributes:
                tag_prop_buf[count].szAction4 = attributes['action4'].encode('utf-8')
                tag_ext_mask = hyperdb.get_alarm_ext_mask('action4', tag_ext_mask)

            if 'sourcetagname' in attributes:
                tag_prop_buf[count].szSourceTagName = attributes['sourcetagname'].encode('utf-8')
                tag_ext_mask = hyperdb.get_alarm_ext_mask('sourcetagname', tag_ext_mask)
            if 'alarmgroupid' in attributes:
                tag_prop_buf[count].nAlarmGroupID = attributes['alarmgroupid']
                tag_ext_mask = hyperdb.get_alarm_ext_mask('alarmgroupid', tag_ext_mask)
            if 'autoack' in attributes:
                tag_prop_buf[count].nAutoAck = attributes['autoack']
                tag_ext_mask = hyperdb.get_alarm_ext_mask('autoack', tag_ext_mask)
            if 'timedeadband' in attributes:
                tag_prop_buf[count].nTimeDeadBand = attributes['timedeadband']
                tag_ext_mask = hyperdb.get_alarm_ext_mask('timedeadband', tag_ext_mask)
            if 'valuedeadband' in attributes:
                tag_prop_buf[count].fValueDeadBand = attributes['valuedeadband']
                tag_ext_mask = hyperdb.get_alarm_ext_mask('valuedeadband', tag_ext_mask)

                # mask = hyperdb.HD3Mask()
            mask[count].commmask = c_int64(comm_mask)
            mask[count].extmask = c_int64(tag_ext_mask)
            # tagmask.append(mask)
            count += 1

        tagids = (number * c_uint32)()
        errorcode = (number * c_int32)()
        hyperdb.api.am3_add_tags.argtypes = [c_int32, POINTER(hyperdb.AlarmTagProp), POINTER(hyperdb.HD3Mask), c_char_p,
                                             POINTER(c_uint32), POINTER(c_int32)]
        retcode = hyperdb.api.am3_add_tags(number, tag_prop_buf, mask, None, tagids, errorcode)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return tagids

    def modify_alarm_tags_attributes(self, props):
        number = len(props)
        tag_prop_buf = (number * hyperdb.AlarmTagProp)()
        tagids = (number * c_uint32)()
        count = 0
        mask = (number * hyperdb.HD3Mask)()
        for attributes in props:
            comm_mask = 0
            ext_mask = 0
            if 'tagid' in attributes:
                tagids[count] = attributes['tagid']
            if 'tagname' in attributes:
                tagname = attributes['tagname']
                tagids[count] = self.get_tag(tagname).tagid
            if tagids[count] == '':
                continue

            if 'recaccess' in attributes:
                tag_prop_buf[count].nRecAccess = attributes['recaccess']
                comm_mask = hyperdb.get_comm_mask('recaccess', comm_mask)
            if 'recnormalgroup' in attributes:
                tag_prop_buf[count].szRecNormalGroup = attributes['recnormalgroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('recnormalgroup', comm_mask)
            if 'recownergroup' in attributes:
                tag_prop_buf[count].szRecOwnerGroup = attributes['recownergroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('recownergroup', comm_mask)

            if 'descriptor' in attributes:
                tag_prop_buf[count].szDescriptor = attributes['descriptor'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('descriptor', comm_mask)

            if 'engunit' in attributes:
                tag_prop_buf[count].szEngUnits = attributes['engunit'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('engunit', comm_mask)
            if 'excmaxtime' in attributes:
                tag_prop_buf[count].fExcMaxTime = attributes['excmaxtime']
                comm_mask = hyperdb.get_comm_mask('excmaxtime', comm_mask)
            if 'excdev' in attributes:
                tag_prop_buf[count].fExcDev = attributes['excdev']
                comm_mask = hyperdb.get_comm_mask('excdev', comm_mask)

            if 'tagaccess' in attributes:
                tag_prop_buf[count].nTagAccess = attributes['tagaccess']
                comm_mask = hyperdb.get_comm_mask('tagaccess', comm_mask)
            if 'tagnormalgroup' in attributes:
                tag_prop_buf[count].szTagNormalGroup = attributes['tagnormalgroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('tagnormalgroup', comm_mask)
            if 'tagownergroup' in attributes:
                tag_prop_buf[count].szTagOwnerGroup = attributes['tagownergroup'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('tagownergroup', comm_mask)

            if 'imvstring' in attributes:
                tag_prop_buf[count].szImvString = attributes['imvstring'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('imvstring', comm_mask)
            if 'extstring1' in attributes:
                tag_prop_buf[count].szExtString1 = attributes['extstring1'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('extstring1', comm_mask)
            if 'extstring2' in attributes:
                tag_prop_buf[count].szExtString2 = attributes['extstring2'].encode('utf-8')
                comm_mask = hyperdb.get_comm_mask('extstring2', comm_mask)

                # ?��???��?��?�???
            if 'test1' in attributes:
                tag_prop_buf[count].szTest1 = attributes['test1'].encode('utf-8')
                ext_mask = hyperdb.get_alarm_ext_mask('test1', ext_mask)
            if 'test2' in attributes:
                tag_prop_buf[count].szTest2 = attributes['test2'].encode('utf-8')
                ext_mask = hyperdb.get_alarm_ext_mask('test2', ext_mask)
            if 'test3' in attributes:
                tag_prop_buf[count].szTest3 = attributes['test3'].encode('utf-8')
                ext_mask = hyperdb.get_alarm_ext_mask('test3', ext_mask)
            if 'test4' in attributes:
                tag_prop_buf[count].szTest4 = attributes['test4'].encode('utf-8')
                ext_mask = hyperdb.get_alarm_ext_mask('test4', ext_mask)

            if 'action1' in attributes:
                tag_prop_buf[count].szAction1 = attributes['action1'].encode('utf-8')
                ext_mask = hyperdb.get_alarm_ext_mask('action1', ext_mask)
            if 'action2' in attributes:
                tag_prop_buf[count].szAction2 = attributes['action2'].encode('utf-8')
                ext_mask = hyperdb.get_alarm_ext_mask('action2', ext_mask)
            if 'action3' in attributes:
                tag_prop_buf[count].szAction3 = attributes['action3'].encode('utf-8')
                ext_mask = hyperdb.get_alarm_ext_mask('action3', ext_mask)
            if 'action4' in attributes:
                tag_prop_buf[count].szAction4 = attributes['action4'].encode('utf-8')
                ext_mask = hyperdb.get_alarm_ext_mask('action4', ext_mask)

            if 'alarmgroupid' in attributes:
                tag_prop_buf[count].nAlarmGroupID = attributes['alarmgroupid']
                ext_mask = hyperdb.get_alarm_ext_mask('alarmgroupid', ext_mask)
            if 'autoack' in attributes:
                tag_prop_buf[count].nAutoAck = attributes['autoack']
                ext_mask = hyperdb.get_alarm_ext_mask('autoack', ext_mask)
            if 'timedeadband' in attributes:
                tag_prop_buf[count].timedeadband = attributes['timedeadband']
                ext_mask = hyperdb.get_alarm_ext_mask('timedeadband', ext_mask)
            if 'valuedeadband' in attributes:
                tag_prop_buf[count].valuedeadband = attributes['valuedeadband']
                ext_mask = hyperdb.get_alarm_ext_mask('valuedeadband', ext_mask)

                # mask = hyperdb.HD3Mask()
            mask[count].commmask = c_int64(comm_mask)
            mask[count].extmask = c_int64(ext_mask)
            # tagmask.append(mask)
            count += 1
        errorcode = (number * c_int32)()
        hyperdb.api.am3_modify_tags_prop.argtypes = [c_int32, POINTER(c_uint32), POINTER(hyperdb.AlarmTagProp),
                                                     POINTER(hyperdb.HD3Mask), POINTER(c_int32)]
        retcode = hyperdb.api.am3_modify_tags_prop(number, tagids, tag_prop_buf, mask, errorcode)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        return hyperdb.hd_sucess

    def query_alarm_tags_cond(self, conds):
        number = len(conds)
        conds_buf = (number * hyperdb.HD3FilterItem)()
        condset = hyperdb.HD3FilterItemSet()

        count = 0
        for cond in conds:
            try:
                conds_buf[count].nPropItemID = hyperdb.tag_col_dict[cond[0]]
                conds_buf[count].nRelation = hyperdb.relation_dict[cond[1]]
                conds_buf[count].szValue = cond[2].encode('utf-8')
            except KeyError:
                raise hyperdb.HDError(errcode=0, errinfo='the key is not exist!')
            count = count + 1
        condset.nSize = c_int32(number)
        condset.pItem = conds_buf

        hditer = c_void_p()
        mask = hyperdb.HD3Mask()
        mask.commmask = c_int64(0XFFFFFFFFFFFFFFFF)
        mask.extmask = c_int64(0XFFFFFFFFFFFFFFFF)

        hyperdb.api.am3_query_tags_cond.argtypes = [POINTER(hyperdb.HD3FilterItemSet), POINTER(hyperdb.HD3Mask),
                                                    POINTER(c_void_p)]
        retcode = hyperdb.api.am3_query_tags_cond(byref(condset), byref(mask), byref(hditer))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)

        tag_prop = hyperdb.AlarmTagProp()
        iterate = hyperdb.Iterate(hditer, tag_prop)
        return iterate

    #    def register_client(self):
    #        retcode = hyperdb.api.am3_register_alarm_record_info(hyperdb.func)
    #        if hyperdb.hd_sucess != retcode:
    #            raise hyperdb.HDError(retcode)

    ##分页查询
    # query specify page comm tags cond
    # @param conds: 查询条件的列表，每个查询条件是一个三元组，形如[('tagname', 'like', 'test'), ('tagtype', '=', 'int8')]。
    # 通过本接口得到迭代器后通过iterate.get_next()获取点的属性
    # @param nCapacity:分页查询容量
    # @param nStartID:开始查询tag点ID
    # @return: return a iterator of tags
    def query_specify_page_comm_tags_cond(self, nCapacity, nStartID, conds):
        if (type(nCapacity) != int or type(nStartID) != int):
            raise TypeError
        number = len(conds)
        itemSet = hyperdb.HD3FilterItemSet()
        pitem = (number * hyperdb.HD3FilterItem)()
        count = 0
        for cond in conds:
            try:
                pitem[count].nPropItemID = hyperdb.tag_col_dict[cond[0]]
                pitem[count].nRelation = hyperdb.relation_dict[cond[1]]
                if cond[0] == 'tagtype':
                    pitem[count].szValue = str(hyperdb.tag_type_dict[cond[2]]).encode('utf-8')
                else:
                    pitem[count].szValue = cond[2].encode('utf-8')
            except KeyError:
                raise hyperdb.HDError(errcode=0, errinfo='the key is not exist!')
            count = count + 1

        itemSet.nSize = c_int32(number)
        itemSet.pItem = pitem
        hditer = c_void_p()
        mask = hyperdb.HD3Mask()
        mask.commmask = c_int64(0XFFFFFFFFFFFFFFFF)
        mask.extmask = c_int64(0XFFFFFFFFFFFFFFFF)
        pParam = hyperdb.HD3PageQueryTagParam()
        pParam.itemSet = itemSet
        pParam.mask = mask
        pParam.nStartID = nStartID
        pParam.nCapacity = nCapacity

        hyperdb.api.pt3_query_specify_page_tags_cond.argtypes = [POINTER(hyperdb.HD3PageQueryTagParam),
                                                                 POINTER(c_void_p)]
        retcode = hyperdb.api.pt3_query_specify_page_tags_cond(byref(pParam), byref(hditer))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)

        comm_tag_prop = hyperdb.TagProp()
        iterate = hyperdb.Iterate(hditer, comm_tag_prop)
        return iterate

    ##分页查询
    # query specify page calc tags cond
    # @param conds: 查询条件的列表，每个查询条件是一个三元组，形如[('tagname', 'like', 'test'), ('tagtype', '=', 'int8')]。
    # 通过本接口得到迭代器后通过iterate.get_next()获取点的属性
    # @param nCapacity:分页查询容量
    # @param nStartID:开始查询tag点ID
    # @return: return a iterator of tags
    def query_specify_page_calc_tags_cond(self, nCapacity, nStartID, conds):
        if (type(nCapacity) != int or type(nStartID) != int):
            raise TypeError
        number = len(conds)
        itemSet = hyperdb.HD3FilterItemSet()
        pitem = (number * hyperdb.HD3FilterItem)()
        count = 0
        for cond in conds:
            try:
                pitem[count].nPropItemID = hyperdb.tag_col_dict[cond[0]]
                pitem[count].nRelation = hyperdb.relation_dict[cond[1]]
                if cond[0] == 'tagtype':
                    pitem[count].szValue = str(hyperdb.tag_type_dict[cond[2]]).encode('utf-8')
                else:
                    pitem[count].szValue = cond[2].encode('utf-8')
            except KeyError:
                raise hyperdb.HDError(errcode=0, errinfo='the key is not exist!')
            count = count + 1

        itemSet.nSize = c_int32(number)
        itemSet.pItem = pitem
        hditer = c_void_p()
        mask = hyperdb.HD3Mask()
        mask.commmask = c_int64(0XFFFFFFFFFFFFFFFF)
        mask.extmask = c_int64(0XFFFFFFFFFFFFFFFF)
        pParam = hyperdb.HD3PageQueryTagParam()
        pParam.itemSet = itemSet
        pParam.mask = mask
        pParam.nStartID = nStartID
        pParam.nCapacity = nCapacity

        hyperdb.api.ca3_query_specify_page_tags_cond.argtypes = [POINTER(hyperdb.HD3PageQueryTagParam),
                                                                 POINTER(c_void_p)]
        retcode = hyperdb.api.ca3_query_specify_page_tags_cond(byref(pParam), byref(hditer))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)

        calc_tag_prop = hyperdb.CalcTagProp()
        iterate = hyperdb.Iterate(hditer, calc_tag_prop)
        return iterate

    ##分页查询
    # query specify page stats tags cond
    # @param conds: 查询条件的列表，每个查询条件是一个三元组，形如[('tagname', 'like', 'test'), ('tagtype', '=', 'int8')]。
    # 通过本接口得到迭代器后通过iterate.get_next()获取点的属性
    # @param nCapacity:分页查询容量
    # @param nStartID:开始查询tag点ID
    # @return: return a iterator of tags
    def query_specify_page_stats_tags_cond(self, nCapacity, nStartID, conds):
        if (type(nCapacity) != int or type(nStartID) != int):
            raise TypeError
        number = len(conds)
        itemSet = hyperdb.HD3FilterItemSet()
        pitem = (number * hyperdb.HD3FilterItem)()
        count = 0
        for cond in conds:
            try:
                pitem[count].nPropItemID = hyperdb.tag_col_dict[cond[0]]
                pitem[count].nRelation = hyperdb.relation_dict[cond[1]]
                if cond[0] == 'tagtype':
                    pitem[count].szValue = str(hyperdb.tag_type_dict[cond[2]]).encode('utf-8')
                else:
                    pitem[count].szValue = cond[2].encode('utf-8')
            except KeyError:
                raise hyperdb.HDError(errcode=0, errinfo='the key is not exist!')
            count = count + 1

        itemSet.nSize = c_int32(number)
        itemSet.pItem = pitem
        hditer = c_void_p()
        mask = hyperdb.HD3Mask()
        mask.commmask = c_int64(0XFFFFFFFFFFFFFFFF)
        mask.extmask = c_int64(0XFFFFFFFFFFFFFFFF)
        pParam = hyperdb.HD3PageQueryTagParam()
        pParam.itemSet = itemSet
        pParam.mask = mask
        pParam.nStartID = nStartID
        pParam.nCapacity = nCapacity

        hyperdb.api.st3_query_specify_page_tags_cond.argtypes = [POINTER(hyperdb.HD3PageQueryTagParam),
                                                                 POINTER(c_void_p)]
        retcode = hyperdb.api.st3_query_specify_page_tags_cond(byref(pParam), byref(hditer))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)

        stats_tag_prop = hyperdb.StatsTagProp()
        iterate = hyperdb.Iterate(hditer, stats_tag_prop)
        return iterate

    ##分页查询
    # query specify page tag3 tags cond
    # @param conds: 查询条件的列表，每个查询条件是一个三元组，形如[('tagname', 'like', 'test'), ('tagtype', '=', 'int8')]。
    # 通过本接口得到迭代器后通过iterate.get_next()获取点的属性
    # @param nTagClassMask:Tag类别掩码
    # @param nCapacity:分页查询容量
    # @param nStartID:开始查询tag点ID
    # @return: return a iterator of tags
    def query_specify_page_tag3_tags_cond(self, nTagClassMask, nCapacity, nStartID, conds):
        if (type(nCapacity) != int or type(nStartID) != int or type(nTagClassMask) != int):
            raise TypeError
        tagclassmask = c_int64(nTagClassMask)
        number = len(conds)
        itemSet = hyperdb.HD3FilterItemSet()
        pitem = (number * hyperdb.HD3FilterItem)()
        count = 0
        for cond in conds:
            try:
                pitem[count].nPropItemID = hyperdb.tag_col_dict[cond[0]]
                pitem[count].nRelation = hyperdb.relation_dict[cond[1]]
                if cond[0] == 'tagtype':
                    pitem[count].szValue = str(hyperdb.tag_type_dict[cond[2]]).encode('utf-8')
                else:
                    pitem[count].szValue = cond[2].encode('utf-8')
            except KeyError:
                raise hyperdb.HDError(errcode=0, errinfo='the key is not exist!')
            count = count + 1

        itemSet.nSize = c_int32(number)
        itemSet.pItem = pitem
        hditer = c_void_p()
        mask = hyperdb.HD3Mask()
        mask.commmask = c_int64(0XFFFFFFFFFFFFFFFF)
        mask.extmask = c_int64(0XFFFFFFFFFFFFFFFF)
        pParam = hyperdb.HD3PageQueryTagParam()
        pParam.itemSet = itemSet
        pParam.mask = mask
        pParam.nStartID = nStartID
        pParam.nCapacity = nCapacity

        hyperdb.api.tag3_query_specify_page_tags_cond.argtypes = [c_int64, POINTER(hyperdb.HD3PageQueryTagParam),
                                                                 POINTER(c_void_p)]
        retcode = hyperdb.api.tag3_query_specify_page_tags_cond(tagclassmask, byref(pParam), byref(hditer))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)

        comm_tag_prop = hyperdb.TagProp()
        iterate = hyperdb.Iterate(hditer, comm_tag_prop)
        return iterate

    ##分页查询
    # query specify page tag3 tags cond access
    # @param conds: 查询条件的列表，每个查询条件是一个三元组，形如[('tagname', 'like', 'test'), ('tagtype', '=', 'int8')]。
    # 通过本接口得到迭代器后通过iterate.get_next()获取点的属性
    # @param userName:用户名（用于权限过滤）
    # @param nTagClassMask:Tag类别掩码
    # @param nCapacity:分页查询容量
    # @param nStartID:开始查询tag点ID
    # @return: return a iterator of tags
    def query_specify_page_tag3_tags_cond_access(self, userName, nTagClassMask, nCapacity, nStartID, conds):
        if (type(nCapacity) != int or type(nStartID) != int or type(nTagClassMask) != int):
            raise TypeError
        username_buf = userName.encode('utf-8')
        tagclassmask = c_int64(nTagClassMask)
        number = len(conds)
        itemSet = hyperdb.HD3FilterItemSet()
        pitem = (number * hyperdb.HD3FilterItem)()
        count = 0
        for cond in conds:
            try:
                pitem[count].nPropItemID = hyperdb.tag_col_dict[cond[0]]
                pitem[count].nRelation = hyperdb.relation_dict[cond[1]]
                if cond[0] == 'tagtype':
                    pitem[count].szValue = str(hyperdb.tag_type_dict[cond[2]]).encode('utf-8')
                else:
                    pitem[count].szValue = cond[2].encode('utf-8')
            except KeyError:
                raise hyperdb.HDError(errcode=0, errinfo='the key is not exist!')
            count = count + 1

        itemSet.nSize = c_int32(number)
        itemSet.pItem = pitem
        hditer = c_void_p()
        mask = hyperdb.HD3Mask()
        mask.commmask = c_int64(0XFFFFFFFFFFFFFFFF)
        mask.extmask = c_int64(0XFFFFFFFFFFFFFFFF)
        pParam = hyperdb.HD3PageQueryTagParam()
        pParam.itemSet = itemSet
        pParam.mask = mask
        pParam.nStartID = nStartID
        pParam.nCapacity = nCapacity

        hyperdb.api.tag3_query_specify_page_tags_cond_access.argtypes = [c_char_p, c_int64, POINTER(hyperdb.HD3PageQueryTagParam),
                                                                 POINTER(c_void_p)]
        retcode = hyperdb.api.tag3_query_specify_page_tags_cond_access(username_buf, tagclassmask, byref(pParam), byref(hditer))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)

        comm_tag_prop = hyperdb.TagProp()
        iterate = hyperdb.Iterate(hditer, comm_tag_prop)
        return iterate

    ## 统计计算接口
    # @param nTagID:tag点ID
    # @param param:统计计算的配置参数,例如：
    #         param = {'nStatsType': hyperdb.HD3_STATS_TYPE['HD3_STATS_TYPE_TIME_NOT_IN_RANGE'],  # 统计类型
    #                  'nTimeRegionNum': 1,  # 统计时间区间个数，最小值为1，最大值为128
    #                  'pTimeRegions_left': pTimeRegions_left,  # 开始时间
    #                  'pTimeRegions_right': pTimeRegions_right,  # 结束时间
    #                  'pTimeRegions_leftclosed': True,  # 左区间是否为闭区间
    #                  'pTimeRegions_rightclosed': True,  # 右区间是否为闭区间
    #                  'pfPercentGood': 0,  # 信任度(Good的数据个数百分比大于或等于该比例时认为计算出的结果是可信的)，0-100
    #                  'nSampleType': hyperdb.HD3_STATS_SAMPLE_TYPE['HD3_STATS_SAMPLE_TYPE_RAW'],  # 采样类型: 原始记录，线性插值，前向插值。
    #                  'szFilterExpr': "",  # 数据过滤表达式: 不使用过滤表达式时，该参数可设为空字符串。
    #                  'nFilterFailMode': 0,  # 过滤失败时对采样数据的处理，0为过滤采样数据，1为不过滤采样数据。不使用过滤表达式时，该参数可设为0。
    #                  'dExtParam1': 0,  # 扩展参数一: 计数和计时的参数, 值变化的参数，区间变化的第一个参数，在区间不在区间的第一个参数。统计类型不是计时或者计数时，该参数可设为0。
    #                  'dExtParam2': 100,  # 扩展参数二: 计数和计时的参数，区间变化的第二个参数，在区间不在区间的第二个参数。统计类型不是计时或者计数时，该参数可设为0。
    #                  'nOffset': 0,  # 采样模式为插值时，第一个插值点为startTime+nOffset, 单位为秒
    #                  'nPeriod': 0,  # 采样周期:采样类型为插值时，插值点的间隔为nPeriod, 单位为秒; 采样类型不是插值时，该参数可设为0。
    #                  'bUseBadValue': False  # 是否使用bad数据。
    #                  }
    # @return: 统计计算结果
    def tag_stats_calc(self, nTagID, **param):
        if (type(nTagID) != int):
            raise TypeError
        pParam = hyperdb.HD3StRelatedParam()
        nTagID = c_uint32(nTagID)
        ret = c_double()
        pTimeRegions = hyperdb.HD3TimeRegion()

        if 'nStatsType' in param:
            pParam.nStatsType = param['nStatsType']
        else:
            pParam.nStatsType = hyperdb.HD3_STATS_TYPE['HD3_STATS_TYPE_LIMIT_MIN']
        if 'nTimeRegionNum' in param:
            pParam.nTimeRegionNum = param['nTimeRegionNum']
        else:
            pParam.nTimeRegionNum = c_int32()

        # HD3TimeRegion
        if 'pTimeRegions_left' in param:
            pTimeRegions.left = param['pTimeRegions_left']
        else:
            pTimeRegions.left = hyperdb.HD3Time()
        if 'pTimeRegions_right' in param:
            pTimeRegions.right = param['pTimeRegions_right']
        else:
            pTimeRegions.right = hyperdb.HD3Time()
        if 'pTimeRegions_leftclosed' in param:
            pTimeRegions.leftclosed = param['pTimeRegions_leftclosed']
        else:
            pTimeRegions.leftclosed = True
        if 'pTimeRegions_rightclosed' in param:
            pTimeRegions.rightclosed = param['pTimeRegions_rightclosed']
        else:
            pTimeRegions.rightclosed = True

        if 'fPercentGood' in param:
            pParam.fPercentGood = param['fPercentGood']
        else:
            pParam.fPercentGood = c_float()
        if 'nSampleType' in param:
            pParam.nSampleType = param['nSampleType']
        else:
            pParam.nSampleType = hyperdb.HD3_STATS_SAMPLE_TYPE['HD3_STATS_SAMPLE_TYPE_MIN']
        if 'szFilterExpr' in param:
            pParam.szFilterExpr = param['szFilterExpr'].encode('utf-8')
        else:
            pParam.szFilterExpr = "".encode('utf-8')
        if 'nFilterFailMode' in param:
            pParam.nFilterFailMode = param['nFilterFailMode']
        else:
            pParam.nFilterFailMode = c_int8()
        if 'dExtParam1' in param:
            pParam.dExtParam1 = c_double(param['dExtParam1'])
        else:
            pParam.dExtParam1 = c_double()
        if 'dExtParam2' in param:
            pParam.dExtParam2 = c_double(param['dExtParam2'])
        else:
            pParam.dExtParam2 = c_double()
        if 'nOffset' in param:
            pParam.nOffset = param['nOffset']
        else:
            pParam.nOffset = c_int32()
        if 'nPeriod' in param:
            pParam.nPeriod = param['nPeriod']
        else:
            pParam.nPeriod = c_int32()
        if 'bUseBadValue' in param:
            pParam.bUseBadValue = c_bool(param['bUseBadValue'])
        else:
            pParam.bUseBadValue = c_bool()

        pParam.pTimeRegions = pointer(pTimeRegions)

        hyperdb.api.st3_tag_stats_calc.argtypes = [c_uint32, POINTER(hyperdb.HD3StRelatedParam), POINTER(c_double)]
        retcode = hyperdb.api.st3_tag_stats_calc(nTagID, byref(pParam), byref(ret))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)
        return ret.value

    def tag_update_tagnames(self, oldTagNames, newTagNames):
        if len(oldTagNames) != len(newTagNames):
            raise ArgumentError
        num = len(oldTagNames)
        nTagNum = c_int32(num)
        pErrCodeArray = (num * c_int32)()

        class TagNameArray(ctypes.Structure):
            _fields_ = [("names", ctypes.c_char * (128 * 2) * num)]

        # 准备实际调用函数时的参数
        old_tag_names = TagNameArray()
        new_tag_names = TagNameArray()

        # 填充tag_names
        for i in range(num):
            tag_name = oldTagNames[i].encode('utf-8')[:128]
            ctypes.memmove(old_tag_names.names[i], tag_name, len(tag_name))
            tag_name = newTagNames[i].encode('utf-8')[:128]
            ctypes.memmove(new_tag_names.names[i], tag_name, len(tag_name))

        hyperdb.api.tag3_update_tagnames.argtypes = [c_int32, ctypes.c_char * (128 * 2) * num, ctypes.c_char * (128 * 2) * num,
                                                        POINTER(c_int32)]
        retcode = hyperdb.api.tag3_update_tagnames(nTagNum, old_tag_names.names, new_tag_names.names, pErrCodeArray)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)
        

    def tag_query_tagnames_history(self):
        hditer = c_void_p()
        hyperdb.api.tag3_query_tagnames_history.argtypes = [POINTER(c_void_p)]
        retcode = hyperdb.api.tag3_query_tagnames_history(byref(hditer))
        if hyperdb.hd_sucess != retcode:
            raise hyperdb.HDError(retcode)

        tag_name_history = hyperdb.HD3TagNameHistory()
        iterate = hyperdb.Iterate(hditer, tag_name_history)
        return iterate
    
    def tag_query_all_tags_prop(self, class_mask, prop_mask):
        if (type(class_mask) != int or type(prop_mask) != int):
            raise TypeError
        tagNum = c_int32()

        pPropArray = POINTER(hyperdb.HD3CommTagProp)()
        pErrCodeArray = c_void_p()

        hyperdb.api.tag3_query_all_tags_prop.argtypes = [c_int64, POINTER(c_int32), c_int64,
                                                            POINTER(POINTER(hyperdb.HD3CommTagProp)), c_void_p]
        retcode = hyperdb.api.tag3_query_all_tags_prop(c_int64(class_mask), byref(tagNum), c_int64(prop_mask),
                                                          byref(pPropArray), pErrCodeArray)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

        return tagNum, pPropArray
    
    def tag_query_all_tags_prop_ex(self, class_mask, collector_id):
        if (type(class_mask) != int or type(collector_id) != int):
            raise TypeError
        tagNum = c_int32()

        pPropArray = POINTER(hyperdb.TagProp)()
        pErrCodeArray = c_void_p()

        hyperdb.api.tag3_query_all_tags_prop_ex.argtypes = [c_int64, POINTER(c_int32), c_int64,
                                                            POINTER(POINTER(hyperdb.TagProp)), c_void_p]
        retcode = hyperdb.api.tag3_query_all_tags_prop_ex(c_int64(class_mask), byref(tagNum), c_int64(collector_id),
                                                          pPropArray, pErrCodeArray)
        if (hyperdb.hd_sucess != retcode):
            raise hyperdb.HDError(retcode)

        return tagNum, pPropArray
    
    def tag_free_all_tags_prop(self, pPropArray):
        hyperdb.api.tag3_free_all_tags_prop.argtypes = [POINTER(hyperdb.HD3CommTagProp)]
        hyperdb.api.tag3_free_all_tags_prop(pPropArray)
        
    def tag_free_all_tags_prop_ex(self, pPropArray):
        hyperdb.api.tag3_free_all_tags_prop_ex.argtypes = [POINTER(hyperdb.TagProp)]
        hyperdb.api.tag3_free_all_tags_prop_ex(pPropArray)

    def convert_to_commtag_list(self, pPropArray):
        commtag_prop_list = []
        for prop in pPropArray:
            commtag_prop = tag.COMMTag()
            commtag_prop.tagname = prop.szTagName.decode('utf-8')
            commtag_prop.archiving = prop.bArchiving  # state of archive
            commtag_prop.changedate = prop.nChangeDate  # the time of the Tag be modified
            commtag_prop.changer = prop.szChanger.decode('utf-8')  # the people who modified the tag  //

            commtag_prop.compdev = prop.fCompDev  # the deviate of compress
            commtag_prop.compmaxtime = prop.nCompMaxTime  # the time of producing record over 'compmax'will be archived
            commtag_prop.comptype = prop.nCompType  # the type of compress

            commtag_prop.creationdate = prop.nCreationDate  # the date of tag be created
            commtag_prop.creator = prop.szCreator.decode('utf-8')  # the tag creator

            commtag_prop.recaccess = prop.nRecAccess  # the right of accessing record
            commtag_prop.recnormalgroup = prop.szRecNormalGroup.decode('utf-8')  # the group of accessing record
            commtag_prop.recownergroup = prop.szRecOwnerGroup.decode('utf-8')  # the owner of record

            commtag_prop.descriptor = prop.szDescriptor.decode('utf-8')  # the descriptor of tag
            commtag_prop.digitalset = prop.szDigitalSet.decode('utf-8')  # digital set
            commtag_prop.engunit = prop.szEngUnits.decode('utf-8')  # engine unit
            commtag_prop.excmaxtime = prop.nExcMaxTime  # upper limit of alarm
            commtag_prop.excdev = prop.fExcDev  # lower limit of alarm

            commtag_prop.tagid = prop.nTagID  # tag id
            commtag_prop.tagtype = prop.nTagType  # the type of tag
            commtag_prop.tagaccess = prop.nTagAccess  # the right of accessing tag
            commtag_prop.tagnormalgroup = prop.szTagNormalGroup.decode('utf-8')  # tag group
            commtag_prop.tagownergroup = prop.szTagOwnerGroup.decode('utf-8')  # the group which tag belong to

            commtag_prop.scan = prop.bScan  # the state of scan
            commtag_prop.span = prop.fSpan  # the scope of the record
            commtag_prop.minvalue = prop.fMinValue  # minimize of the record
            commtag_prop.tagclass = prop.nTagClass  # a sign used to indicate tag type
            commtag_prop.tagavaliable = prop.nTagAvaliable  # 是否可用
            commtag_prop.opcstate = prop.nOpcState  # 是否发布

            commtag_prop.imvstring = prop.szImvString.decode('utf-8')
            commtag_prop.extstring1 = prop.szExtString1.decode('utf-8')
            commtag_prop.extstring2 = prop.szExtString2.decode('utf-8')

            commtag_prop.param1 = prop.nParam1  # keep parameter 1
            commtag_prop.param2 = prop.nParam2  # keep parameter 2
            commtag_prop.param3 = prop.nParam3  # keep parameter 3
            commtag_prop.param4 = prop.nParam4  # keep parameter 4
            commtag_prop.param5 = prop.nParam5  # keep parameter 5
            commtag_prop_list.append(commtag_prop)
            # FIXME: 最后一个元素会崩溃
            if(len(commtag_prop_list) > 20):
                break
        return commtag_prop_list
    
    def convert_to_pt_tag_list(self, pPropArray, num):
        pt_tag_prop_list = []
        if num < 0:
            raise hyperdb.HDError(errcode=0, errinfo='convert num < 0!')
        try:
            for i in range(num):
                prop = pPropArray[i]
                pt_tag_prop = hyperdb.TagProp()
                pt_tag_prop.tagname = prop.szTagName.decode('utf-8')
                pt_tag_prop.archiving = prop.bArchiving  # state of archive
                pt_tag_prop.changedate = prop.nChangeDate  # the time of the Tag be modified
                pt_tag_prop.changer = prop.szChanger.decode('utf-8')  # the people who modified the tag  //

                pt_tag_prop.compdev = prop.fCompDev  # the deviate of compress
                pt_tag_prop.compmaxtime = prop.nCompMaxTime  # the time of producing record over 'compmax'will be archived
                pt_tag_prop.comptype = prop.nCompType  # the type of compress

                pt_tag_prop.creationdate = prop.nCreationDate  # the date of tag be created
                pt_tag_prop.creator = prop.szCreator.decode('utf-8')  # the tag creator

                pt_tag_prop.recaccess = prop.nRecAccess  # the right of accessing record
                pt_tag_prop.recnormalgroup = prop.szRecNormalGroup.decode('utf-8')  # the group of accessing record
                pt_tag_prop.recownergroup = prop.szRecOwnerGroup.decode('utf-8')  # the owner of record

                pt_tag_prop.descriptor = prop.szDescriptor.decode('utf-8')  # the descriptor of tag
                pt_tag_prop.digitalset = prop.szDigitalSet.decode('utf-8')  # digital set
                pt_tag_prop.engunit = prop.szEngUnits.decode('utf-8')  # engine unit
                pt_tag_prop.excmaxtime = prop.nExcMaxTime  # upper limit of alarm
                pt_tag_prop.excdev = prop.fExcDev  # lower limit of alarm

                pt_tag_prop.tagid = prop.nTagID  # tag id
                pt_tag_prop.tagtype = prop.nTagType  # the type of tag
                pt_tag_prop.tagaccess = prop.nTagAccess  # the right of accessing tag
                pt_tag_prop.tagnormalgroup = prop.szTagNormalGroup.decode('utf-8')  # tag group
                pt_tag_prop.tagownergroup = prop.szTagOwnerGroup.decode('utf-8')  # the group which tag belong to

                pt_tag_prop.scan = prop.bScan  # the state of scan
                pt_tag_prop.span = prop.fSpan  # the scope of the record
                pt_tag_prop.minvalue = prop.fMinValue  # minimize of the record
                pt_tag_prop.tagclass = prop.nTagClass  # a sign used to indicate tag type
                pt_tag_prop.tagavaliable = prop.nTagAvaliable  # 是否可用
                pt_tag_prop.opcstate = prop.nOpcState  # 是否发布

                pt_tag_prop.imvstring = prop.szImvString.decode('utf-8')
                pt_tag_prop.extstring1 = prop.szExtString1.decode('utf-8')
                pt_tag_prop.extstring2 = prop.szExtString2.decode('utf-8')

                pt_tag_prop.param1 = prop.nParam1  # keep parameter 1
                pt_tag_prop.param2 = prop.nParam2  # keep parameter 2
                pt_tag_prop.param3 = prop.nParam3  # keep parameter 3
                pt_tag_prop.param4 = prop.nParam4  # keep parameter 4
                pt_tag_prop.param5 = prop.nParam5  # keep parameter 5

                pt_tag_prop.collectorname = prop.szCollectorName.decode('utf-8')
                pt_tag_prop.collectorid = prop.nCollectorID
                pt_tag_prop.devicename = prop.szDeviceName.decode('utf-8')
                pt_tag_prop.deviceid = prop.nDeviceID
                pt_tag_prop.scangroupname = prop.szScanGroupName.decode('utf-8')
                pt_tag_prop.scangroupid = prop.nScanGroupID
                pt_tag_prop.dataorder = prop.nDataOrder
                pt_tag_prop_list.append(pt_tag_prop)
        except KeyError:
            raise hyperdb.HDError(errcode=0, errinfo='convert_to_pt_tag_list crash!')
        return pt_tag_prop_list
