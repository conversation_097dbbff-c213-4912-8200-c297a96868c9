#!/usr/bin/env python
# -*- coding: gb2312 -*-

from ctypes  import *
import hyperdb 
import tag
import sys
import record
from datetime import datetime
import server
import time

## dataprovider is a operator class through which programmers can use to do all
# operators about data.
# @note: Programmers needn't create a DataProvider object. Instead, DataProvider object
# can be accessed from Server class
class DataProvider(object):
    def __init__(self, server):
        self.server = server


    def get_snapshot_by_IDs(self, nTagIDs):
        nRecNum = len(nTagIDs)
        if (nRecNum <= 0 or nRecNum > hyperdb.maxnum_query_records):
            raise hyperdb.HDParaError('the count of tag beyond the allowed scope')
        
        hdRecords = (nRecNum * hyperdb.HD3Record)()
        hdTagIDs = (nRecNum * c_uint32)()
        nErrCodes = (nRecNum * c_int32)()
        # construct hdrecord according to tag type
        for index in range(nRecNum):
            tagType = self.server.tag_mgr.get_tagType(nTagIDs[index])

            hdrecord_buf = hyperdb.HD3Record()
            hdrecord_buf.nTagType = hyperdb.tag_type_dict[tagType]
            if(tagType == 'string' or tagType == 'blob'):
                temp_buf = create_string_buffer(1000)
                hdrecord_buf.value.strBlob.pBuf = temp_buf.raw
                hdrecord_buf.value.strBlob.nLenBuf = 1000
            hdRecords[index] = hdrecord_buf
            hdTagIDs[index] = c_uint32(nTagIDs[index])
       
        hyperdb.api.sn3_query_snapshots.argtypes = [c_int32, POINTER(c_uint32), POINTER(hyperdb.HD3Record), POINTER(c_int32)]    
        retCode = hyperdb.api.sn3_query_snapshots(nRecNum, hdTagIDs, hdRecords, nErrCodes)
        if hyperdb.hd_sucess != retCode and hyperdb.EC_HD_API_QUERY_SNAPSHOTS_FAILED != retCode:
            raise hyperdb.HDError(retCode)
        rtnRecList = []
        errCodeList = []
        for record_buf in hdRecords:
            result_record = hyperdb.HD3RecordToPyRecord(record_buf)
            rtnRecList.append(result_record)  
        for errcode in nErrCodes:
            errCodeList.append(errcode)                 
        return (retCode,rtnRecList,errCodeList)
