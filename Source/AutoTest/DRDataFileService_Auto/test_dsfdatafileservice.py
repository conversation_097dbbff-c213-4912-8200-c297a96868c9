import os
import pytest
import xml.etree.ElementTree as ET

# 使用绝对导入
from datetime import datetime
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
# application_path="/home/<USER>/work/dr/"
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
config_path = os.path.join(application_path,"projects/defaultproject/config")

#缓存IO配置文件内容
io_config_cache=None

#获取数据文件地址
def get_data_path():

    data_path=''

    #读取数据存储配置文件
    tree = ET.parse(config_path+"/CurrentDataStorageConfig.ds")
    root = tree.getroot()
    #读取BaseDirectory元素
    base_directory=None
    if root.find('.//BaseDirectory') is None:
        print("缺少BaseDirectory元素")
        return data_path
    else:
        base_directory = root.find('.//BaseDirectory').text
    
    data_path=application_path+"projects/defaultproject/"+base_directory
    return data_path

#获取模块名称
def get_module_names():
    #读取IO配置文件
    tree = ET.parse(config_path+"/CurrentIoConfig.ds")
    root = tree.getroot()
    #获取模块名称
    module_names=[]
    for module in root.findall('.//Module'):
        name = module.find('Name').text
        module_names.append(name)
    return module_names

#获取信号名称
def get_signal_names():
    #读取IO配置文件
    tree = ET.parse(config_path+"/CurrentIoConfig.ds")
    root = tree.getroot()
    #获取模块名称
    signal_names=[]
    for module in root.findall('.//Module'):
        for signal in module.findall('.//Signal'):
            name = signal.find('Name').text
            signal_names.append(name)
    return signal_names

#创建DataFileService空模块IO配置
def create_io_config(config_path):

    print("生成空模块IO配置文件...")
    # 创建元素 <IOConfiguration>
    ioConfiguration = ET.Element('IOConfiguration')

    # 添加子元素 <Modules>
    modules = ET.SubElement(ioConfiguration, 'Modules')

    #写入xml文件
    xml_file=config_path+"/CurrentIoConfig.ds"
    write_xml(ioConfiguration,xml_file)
    
    print("空模块IO配置文件已生成")

#测试之前
def before_test():
    print("测试开始前执行")

    print("缓存IO配置文件内容")
    # 声明要使用全局变量
    global io_config_cache
    io_config_cache=ET.parse(config_path+"/CurrentIoConfig.ds")
        
#测试之后
def after_test():
    print("测试结束后执行")

    print("恢复IO配置文件内容")
    if io_config_cache is not None:
        #恢复XML配置文件
        xml_file=config_path+"/CurrentIoConfig.ds"
        write_xml(io_config_cache.getroot(),xml_file)

        #关闭dsfdatafileservice进程
        stop_program_by_name("dsfdatafileservice")

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

class TestDataFileService():

    @allure_setup("DSFR-1361", is_async=False)
    def test_jira_summary_1361(self):
        summary, _ = get_jira_summary("DSFR-1361")
        # assert summary == "autotest验证DataFileService是否运行"
        
        #验证
        result=is_process_running("dsfdatafileservice")
        assert True==result

    @allure_setup("DSFR-1362", is_async=False)
    def test_jira_summary_1362(self):
        summary, _ = get_jira_summary("DSFR-1362")
        # assert summary == "autotest验证DataFileService的冗余功能是否正常"

        assert True==1

    @allure_setup("DSFR-1363", is_async=False)
    def test_jira_summary_1363(self):
        summary, _ = get_jira_summary("DSFR-1363")
        # assert summary == "autotest验证DataFileService数据目录是否正确"
        
        #获取数据文件地址
        data_path=get_data_path()

        result=False
        if os.path.exists(data_path):
            result=True
        else:
            print(data_path+"路径不存在")

        #验证
        assert True==result

    @allure_setup("DSFR-1364", is_async=False)
    def test_jira_summary_1364(self):
        summary, _ = get_jira_summary("DSFR-1364")
        # assert summary == "autotest验证DataFileService是否正常生成文件"

        #获取数据文件地址
        data_path=get_data_path()

        result=False
        if os.path.exists(data_path):
            result=True
        else:
            print(data_path+"路径不存在")

        #获取当前日期
        current_date = datetime.now().date()
        print("当前日期:", current_date)
        #格式化日期为YYYYMMDD形式
        current_date = current_date.strftime('%Y%m%d')

        #获取目录下的所有文件名
        file_names = os.listdir(data_path+f"/{current_date}")

        #打印所有文件名
        for file_name in file_names:
            print(file_name)

        result=True if len(file_names) > 0 else False

        #验证
        assert True==result

    @allure_setup("DSFR-1365", is_async=False)
    def test_jira_summary_1365(self):
        summary, _ = get_jira_summary("DSFR-1365")
        # assert summary == "autotest验证DataFileService的数据文件帧数是否正确"

        #获取数据文件地址
        data_path=get_data_path()

        result=False
        if os.path.exists(data_path):
            result=True
        else:
            print(data_path+"路径不存在")

        #验证路径是否存在
        assert True==result
        
        #获取当前日期
        current_date = datetime.now().date()
        print("当前日期:", current_date)
        #格式化日期为YYYYMMDD形式
        current_date = current_date.strftime('%Y%m%d')

        #获取目录下的所有文件名
        date_path=data_path+f"/{current_date}"
        file_names = os.listdir(date_path)
        # 获取最新修改的文件
        latest_file = max(file_names, key=lambda f: os.path.getmtime(os.path.join(date_path, f)))
        for file_name in file_names:
            if file_name == latest_file:
                continue

            print() #换行
            print(file_name)

            # 以二进制模式打开文件
            file_path=data_path+f"/{current_date}/"+file_name
            with open(file_path, 'rb') as file:
                # 读取文件内容
                content = file.read()
                
                # print(content.hex())  # 输出: 文件内容的十六进制表示
                print("----------------文件头部区----------------")
                print(f"PDA文件标识符 {content[:4].decode('utf-8')}")
                print(f"钥匙标志 {content[4:8].hex()}")
                byte_string="<"+get_byte_string_by_type("DINT")
                tail_area_num=struct.unpack(byte_string, content[8:12])[0]
                # print(f"文件尾部区的起始地址 {tail_area_num}")
                # print(f"文件头部区保留位 {content[12:32].hex()}")
                
                # print(f"文件数据区: {content[32:tail_area_num].hex()}")

                # print("----------------文件尾部区----------------")
                #尝试解码内容
                try:
                    #获取文件尾部内容
                    tail_content=content[tail_area_num+1:].decode('utf-8')
                    print(tail_content)
                except (UnicodeDecodeError, IndexError) as e:
                    print(f"解码错误: {e}")
                    assert True==0

                #获取帧
                frames=None
                tail_content=content[tail_area_num+1:].decode('utf-8')
                lines = tail_content.splitlines()
                for line in lines:
                    if "frames" in line:
                        parts = line.split(':')
                        frames=int(parts[1:][0])
                        print(f"帧: {frames}")
                        break
                
                #验证
                result=True if frames == 600 else False
                assert True==result

    @allure_setup("DSFR-1366", is_async=False)
    def test_jira_summary_1366(self):
        summary, _ = get_jira_summary("DSFR-1366")
        # assert summary == "autotest验证DataFileService的数据文件模块名是否正确"

        #获取数据文件地址
        data_path=get_data_path()

        result=False
        if os.path.exists(data_path):
            result=True
        else:
            print(data_path+"路径不存在")

        #验证路径是否存在
        assert True==result
        
        #获取模块名称
        module_names=get_module_names()

        #获取当前日期
        current_date = datetime.now().date()
        print("当前日期:", current_date)
        #格式化日期为YYYYMMDD形式
        current_date = current_date.strftime('%Y%m%d')
        
        #获取目录下的所有文件名
        date_path=data_path+f"/{current_date}"
        file_names = os.listdir(date_path)
        # 获取最新修改的文件
        latest_file = max(file_names, key=lambda f: os.path.getmtime(os.path.join(date_path, f)))
        for file_name in file_names:
            if file_name == latest_file:
                continue

            print() #换行
            print(file_name)

            # 以二进制模式打开文件
            file_path=data_path+f"/{current_date}/"+file_name
            with open(file_path, 'rb') as file:
                # 读取文件内容
                content = file.read()
                
                # print(content.hex())  # 输出: 文件内容的十六进制表示
                print("----------------文件头部区----------------")
                print(f"PDA文件标识符 {content[:4].decode('utf-8')}")
                # print(f"钥匙标志 {content[4:8].hex()}")
                byte_string="<"+get_byte_string_by_type("DINT")
                tail_area_num=struct.unpack(byte_string, content[8:12])[0]
                print(f"文件尾部区的起始地址 {tail_area_num}")
                # print(f"文件头部区保留位 {content[12:32].hex()}")
                
                # print(f"文件数据区: {content[32:tail_area_num].hex()}")

                print("----------------文件尾部区----------------")
                #尝试解码内容
                try:
                    #获取文件尾部内容
                    tail_content=content[tail_area_num+1:].decode('utf-8')
                    # print(tail_content)
                except (UnicodeDecodeError, IndexError) as e:
                    print(f"解码错误: {e}")
                    assert True==0

                #读取模块名称
                lines = tail_content.splitlines()
                for line in lines:
                    if "Module_name" in line:
                        parts = line.split(':')
                        name=parts[1:][0]
                        print(f"模块名: {name}")
                        
                        #验证
                        result=True if name in module_names else False
                        assert True==result
                
    @allure_setup("DSFR-1367", is_async=False)
    def test_jira_summary_1367(self):
        summary, _ = get_jira_summary("DSFR-1367")
        # assert summary == "autotest验证DataFileService的数据文件信号名是否正确"

        #获取数据文件地址
        data_path=get_data_path()

        result=False
        if os.path.exists(data_path):
            result=True
        else:
            print(data_path+"路径不存在")

        #验证路径是否存在
        assert True==result
        
        #获取信号名称
        signal_names=get_signal_names()
        print(signal_names)

        #获取当前日期
        current_date = datetime.now().date()
        print("当前日期:", current_date)
        #格式化日期为YYYYMMDD形式
        current_date = current_date.strftime('%Y%m%d')

        #获取目录下的所有文件名
        date_path=data_path+f"/{current_date}"
        file_names = os.listdir(date_path)
        # 获取最新修改的文件
        latest_file = max(file_names, key=lambda f: os.path.getmtime(os.path.join(date_path, f)))
        for file_name in file_names:
            if file_name == latest_file:
                continue
            
            print() #换行
            print(file_name)

            #以二进制模式打开文件
            file_path=data_path+f"/{current_date}/"+file_name
            with open(file_path, 'rb') as file:
                #读取文件内容
                content = file.read()
                
                # print(content.hex())  # 输出: 文件内容的十六进制表示
                print("----------------文件头部区----------------")
                print(f"PDA文件标识符 {content[:4].decode('utf-8')}")
                # print(f"钥匙标志 {content[4:8].hex()}")
                byte_string="<"+get_byte_string_by_type("DINT")
                tail_area_num=struct.unpack(byte_string, content[8:12])[0]
                print(f"文件尾部区的起始地址 {tail_area_num}")
                # print(f"文件头部区保留位 {content[12:32].hex()}")
                
                # print(f"文件数据区: {content[32:tail_area_num].hex()}")

                print("----------------文件尾部区----------------")
                #尝试解码内容
                try:
                    #获取文件尾部内容
                    tail_content=content[tail_area_num+1:].decode('utf-8')
                    # print(tail_content)
                except (UnicodeDecodeError, IndexError) as e:
                    print(f"解码错误: {e}")
                    assert True==0

                #读取模块名称
                lines = tail_content.splitlines()
                for line in lines:
                    #排除掉模块名称
                    if not "Module_name" in line and "name" in line:
                        parts = line.split('name:')
                        name=parts[1:][0]
                        print(f"信号名: {name}")
                        
                        #验证
                        result=True if name in signal_names else False
                        assert True==result

    @allure_setup("DSFR-1368", is_async=False)
    def test_jira_summary_1368(self):
        summary, _ = get_jira_summary("DSFR-1368")
        # assert summary == "autotest验证空模块配置下DataFileService是否正常运行"
        
        #更换空模块配置
        create_io_config(config_path)

        #关闭dsfdatafileservice进程，并等待重启
        stop_program_by_name("dsfdatafileservice")
        time.sleep(5)

        #验证
        result=is_process_running("dsfdatafileservice")
        assert True==result

# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfdatafileservice.py"])