import os
import pytest
import ctypes
# 使用绝对导入
from datetime import datetime
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
# application_path="/home/<USER>/work/dr/"
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"

# 加载动态库
libDataFileReader = ctypes.CDLL(application_path + 'library/libDataFileReader.so')

# 声明参数类型和返回类型
libDataFileReader.preParseDataFile.argtypes = [
    ctypes.c_char_p,      # filePath
    ctypes.c_char_p,      # signalName
    ctypes.POINTER(ctypes.c_size_t), # dataSize
    ctypes.POINTER(ctypes.c_size_t)  # pos
]
libDataFileReader.preParseDataFile.restype = ctypes.c_long

libDataFileReader.readSignalValuesFromDataFile.argtypes = [
    ctypes.c_char_p,      # filePath
    ctypes.c_char_p,      # signalName
    ctypes.POINTER(ctypes.c_double), # data
    ctypes.c_size_t  # pos
]
libDataFileReader.readSignalValuesFromDataFile.restype = ctypes.c_long

#测试之前
def before_test():
    print("测试开始前执行")
    # 根据日志时间戳获取数据文件路径
    log_time = datetime.now().strftime("%Y%m%d")
    # 找到数据文件所在的目录
    data_file_dir = os.path.join(application_path, "projects", "defaultproject", "data", log_time)
    # 确保文件目录存在，如果不存在则失败
    if not os.path.exists(data_file_dir):
        pytest.fail(f"数据文件目录不存在: {data_file_dir}")
    # 确保至少有三个文件生成，如果没有则等待一会再次检测，如果检测10次仍然没有生成，则失败
    retries = 10
    while len(os.listdir(data_file_dir)) < 3 and retries > 0:
        print(f"数据文件数量不足: {data_file_dir}")
        time.sleep(120)  # 等待120秒后再次检测
        retries -= 1

    if retries == 0:
        pytest.fail(f"数据文件在规定时间内未生成: {data_file_dir}")

    # 获取倒数第二新的文件
    files = sorted(os.listdir(data_file_dir), key=lambda x: os.path.getmtime(os.path.join(data_file_dir, x)))
    if len(files) < 2:
        pytest.fail(f"数据文件数量不足: {data_file_dir}")
    
    # 获取倒数第二新的文件名并获取文件起始时间
    second_last_file = files[-2]
    print(f"倒数第二新的数据文件: {second_last_file}")

    # 获取文件起始时间
    second_last_file_path = os.path.join(data_file_dir, second_last_file)
    global second_last_file_start_time
    second_last_file_start_time = os.path.getmtime(second_last_file_path)
    print(f"倒数第二新的数据文件起始时间: {datetime.fromtimestamp(second_last_file_start_time)}")

    # 设置全局变量以供测试使用
    global test_file_path
    test_file_path = os.path.join(data_file_dir, second_last_file)
    print(f"使用的数据文件路径: {test_file_path}")


#测试之后
def after_test():
    print("测试结束后执行")
    # 清空所有数据文件，除了最新的一个
    log_time = datetime.now().strftime("%Y%m%d")
    data_file_dir = os.path.join(application_path, "projects", "defaultproject", "data", log_time)
    
    # 获取最新的文件名
    files = sorted(os.listdir(data_file_dir), key=lambda x: os.path.getmtime(os.path.join(data_file_dir, x)))
    if len(files) < 1:
        pytest.fail(f"数据文件数量不足: {data_file_dir}")
    
    latest_file = files[-1]
    print(f"最新的数据文件: {latest_file}")
    
    # 删除除了最新文件以外的所有文件
    for file in files[:-1]:
        file_path = os.path.join(data_file_dir, file)
        os.remove(file_path)
        print(f"删除旧数据文件: {file_path}")

    # 确保最新文件存在
    latest_file_path = os.path.join(data_file_dir, latest_file)
    if not os.path.exists(latest_file_path):
        pytest.fail(f"最新数据文件不存在: {latest_file_path}")
    print(f"保留最新数据文件: {latest_file_path}")


#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行


def find_bad_points_and_missing_count(data, data_range):
    """
    找出坏点并统计漏点总数（考虑循环递增）
    
    参数:
        data: 包含数值的列表或数组
        data_range: 数据范围上限（0到data_range循环）
        
    返回:
        bad_count: 坏点数量
        bad_points_info: 坏点详细信息列表
        missing_total: 漏点总数
    """
    bad_points_info = []
    missing_total = 0
    
    for i in range(1, len(data)):
        prev = data[i-1]
        current = data[i]
        # 如果两个点值相同，则跳过
        if prev == current:
            continue
        
        # 计算正常情况下的期望值
        if prev == data_range:
            expected = 0  # 从最大值跳到0是正常
        else:
            expected = prev + 1  # 其他情况期望递增1
        
        # 检查是否是坏点
        if current != expected:
            # 计算实际差值（考虑循环）
            if prev > current:  # 处理从最大值跳到小值的情况
                # 差值 = (data_range - prev) + current + 1
                diff = (data_range - prev) + current + 1
            else:
                diff = current - prev
            
            # 计算漏点数量（排除正常跳跃）
            if diff > 1 or (prev == data_range and current != 0):
                # 从最大值跳到非0值的情况
                if prev == data_range and current != 0:
                    # 漏点 = 从0到current的值
                    missing_count = current
                # 其他坏点情况
                else:
                    missing_count = diff - 1
                
                # 记录坏点信息
                bad_points_info.append({
                    "index": i,
                    "prev_value": prev,
                    "bad_value": current,
                    "missing_count": missing_count
                })
                
                missing_total += missing_count
    
    bad_count = len(bad_points_info)
    return bad_count, bad_points_info, missing_total


class TestDataFileService_StressTest():
    @allure_setup("DSFR-1455", is_async=False)
    def test_jira_summary_1455(self):
        summary, _ = get_jira_summary("DSFR-1455")
        # assert summary == "autotest验证DataFileService是否运行"
        
        # 获取当前路径
        file_path = test_file_path.encode('utf-8')  # 确保路径是字节串
        signal_name = b"2:0"
        data_size = ctypes.c_size_t()
        pos = ctypes.c_size_t()

        try:
            libDataFileReader.preParseDataFile(file_path, signal_name, ctypes.byref(data_size), ctypes.byref(pos))
        except Exception as e:
            print(f"预解析数据文件时发生异常: {e}")
            return
        
        print(f"数据点数：{data_size.value}")
        print(f"信号在数据区偏移的位置：{pos.value}")

        if pos == 0:
            print("信号未找到或数据文件不存在")
            return

        data = (ctypes.c_double * data_size.value)()
        
        libDataFileReader.readSignalValuesFromDataFile(file_path, signal_name, data, pos)

        # 验证data是否正确
        if data_size.value > 0:
            print(f"读取成功，数据点数：{data_size.value}")
        else:
            print("读取失败，数据点数为0")    

        # 找出坏点并统计漏点
        count, points_info, missing_total = find_bad_points_and_missing_count(data, 255)


        # 输出结果
        print(f"坏点数量: {count}")
        print(f"漏点总数: {missing_total}")
        print("\n坏点详细信息:")
        for info in points_info:
            print(f"  索引 {info['index']}: 前值={info['prev_value']}, 坏点值={info['bad_value']}, "
                f"漏点数={info['missing_count']}")
            

        # 统计最大丢点
        max_missing = max(info['missing_count'] for info in points_info) if points_info else 0
        

        # 输出最大丢点所在时间
        max_missing_info = next((info for info in points_info if info['missing_count'] == max_missing), None)
        if max_missing_info:
            print(f"最大丢点数所在时间: {datetime.fromtimestamp(second_last_file_start_time+max_missing_info['index']*10/1000)}")

        # 计算坏点率
        if data_size.value > 0:
            bad_rate = missing_total / data_size.value * 100
            print(f"丢点率: {bad_rate:.2f}%")
            print(f"最大丢点个数: {max_missing}")
            if bad_rate >= 1 or max_missing > 2:
                print("丢点率超过1%或最大丢点数超过2，测试失败")
                assert False
            else:
                print("丢点率满足条件，测试通过")
        else:
            print("数据点数为0，无法计算丢点率")
            assert False


# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfdatafileservice_stresstest.py"])