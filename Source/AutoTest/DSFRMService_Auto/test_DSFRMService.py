import os
import pytest
import requests
import json
import time

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *
from datetime import datetime, timedelta
g_local_passwd = "dsf123!@#"
# 设置iptables禁止本地访问PLC
def disable_local_access(IPAddr:str):
    proc = subprocess.Popen(['sudo', '-S', 'iptables', '-A', 'OUTPUT', '-d', IPAddr, '-j', 'DROP'],
                        stdin=subprocess.PIPE)
    proc.communicate(input=g_local_passwd.encode())     # 输入密码

# 删除iptables规则
def delete_iptables_rule(IPAddr:str):
    proc = subprocess.Popen(['sudo', '-S', 'iptables', '-D', 'OUTPUT', '-d', IPAddr, '-j', 'DROP'],
                        stdin=subprocess.PIPE)


def call_url_with_body(url, body: dict, method="POST"):
    headers = {"Content-Type": "application/json"}
    if method.upper() == "POST":
        response = requests.post(url, json=body, headers=headers)
    elif method.upper() == "GET":
        response = requests.get(url, json=body, headers=headers)
    else:
        raise ValueError("Unsupported method")
    return response.status_code, response.text

def getRMstatus(ip):
    """
    获取指定IP的节点状态
    Args:
        ip: 节点IP地址
    Returns:
        1: 节点状态为Running(活动)
        0: 节点状态为Stopped(非活动)
        -1: 请求失败或节点状态未知
    """
    url = f"http://{ip}:55020/Monitor/NodeStatus"
    body = {"NodeIp": ip}
    
    try:
        status, resp = call_url_with_body(url, body, "POST")
        
        if status != 200:
            logging.error(f"请求失败，状态码: {status}")
            return -1
            
        resp_dict = json.loads(resp.strip())
        
        # 检查响应是否包含节点状态信息
        if resp_dict and isinstance(resp_dict, list) and len(resp_dict) > 0:
            node_status = resp_dict[0].get("node_status")
            if node_status == "Running":
                return 1
            elif node_status == "Stopped":
                return 0
            else:
                logging.warning(f"未知的节点状态: {node_status}")
        else:
            logging.error("响应格式不正确或没有节点信息")
            
        return -1
    except Exception as e:
        logging.error(f"获取节点状态时出错: {str(e)}")
        return -1
def stop_redis_server():
    """
    停止监听端口6380的Redis服务器
    返回: 
        bool: 如果成功停止返回True，如果没有找到进程返回False
    """
    import subprocess
    import time
    
    try:
        # 检查是否有监听6380端口的redis-server进程
        check_cmd = "ps -ef | grep redis-server | grep 6380 | grep -v grep"
        process = subprocess.run(check_cmd, shell=True, capture_output=True, text=True)
        
        if process.stdout.strip():
            # 获取进程PID
            pid_cmd = "ps -ef | grep redis-server | grep 6380 | grep -v grep | awk '{print $2}'"
            pid = subprocess.check_output(pid_cmd, shell=True, text=True).strip()
            
            # 杀死进程
            kill_cmd = f"kill -9 {pid}"
            subprocess.run(kill_cmd, shell=True)
            
            print(f"已停止Redis服务(PID: {pid})")
            return True
        else:
            print("未找到正在运行的Redis服务")
            return False
            
    except Exception as e:
        logging.error(f"停止Redis服务时出错: {str(e)}")
        return False

# 测试用例
class TestRMService():
    #1.核心服务
    @allure_setup("DSFR-1402", is_async=False)
    def test_jira_summary1(self):
        summary, _ = get_jira_summary("DSFR-1402")
        print(f"Summary: {summary}")
        #停止进程
        url = "http://127.0.0.1:55020/Monitor/NodeControl"
        body = {"NodeIp":"127.0.0.1", "ProcessType":"System", "ProcessName":"dsfacquisitionservice", "Control":"stop"}
        method = "POST"
        status1, resp1 = call_url_with_body(url, body, method)
        time.sleep(8)  # 等待8秒钟

        # 验证主备节点状态
        masterStatus = getRMstatus("127.0.0.1")
        assert masterStatus == 0
        slaveStatus = getRMstatus("**************")
        assert slaveStatus == 1
        
        
        #启动进程
        url = "http://127.0.0.1:55020/Monitor/NodeControl"
        body = {"NodeIp":"127.0.0.1", "ProcessType":"System", "ProcessName":"dsfacquisitionservice", "Control":"run"}
        method = "POST"
        status2, resp2 = call_url_with_body(url, body, method)
        time.sleep(5)  # 等待5秒钟


        # 验证主备节点状态
        masterStatus = getRMstatus("127.0.0.1")
        assert masterStatus == 0
        slaveStatus = getRMstatus("**************")
        assert slaveStatus == 1

    # 2.主动切换
    @allure_setup("DSFR-1403", is_async=False)
    def test_jira_summary2(self):
        summary, _ = get_jira_summary("DSFR-1403")
        print(f"Summary: {summary}")

        #当前主机应该是非活动，向备机发送切换命令
        url = "http://**************:55020/Monitor/RMSwitch"
        body = {"NodeIp" : "**************"}
        method = "GET"
        status, resp = call_url_with_body(url, body, method)
        print(f"Status: {status}, Response: {resp}")
        resp_dict = json.loads(resp.strip())

        time.sleep(2)  # 等待2秒钟
        assert 200 == status

        # 验证主备节点状态
        masterStatus = getRMstatus("127.0.0.1")
        assert masterStatus == 1

        slaveStatus = getRMstatus("**************")
        assert slaveStatus == 0

    # 4.redis异常
    @allure_setup("DSFR-1405", is_async=False)
    def test_jira_summary4(self):

        summary, _ = get_jira_summary("DSFR-1404")
        print(f"Summary: {summary}")
        # 停止redis服务
        redis_stopped = stop_redis_server()
        assert redis_stopped, "无法停止Redis服务"
        print("已停止Redis服务，等待系统响应...")

        time.sleep(2)
        # 验证主备节点状态
        masterStatus = getRMstatus("127.0.0.1")
        assert masterStatus == 0
        slaveStatus = getRMstatus("**************")
        assert slaveStatus == 1

        #执行上上目录下的setup文件夹下的dsf_start.sh脚本
        script_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), '..', '..','..', 'setup', 'dsf_start.sh')
        if os.path.exists(script_path):
            print(f"正在执行脚本: {script_path}")
            os.system(f"bash {script_path}")
            time.sleep(5)
        else:
            logging.error(f"脚本文件不存在: {script_path}")
            assert False, f"脚本文件不存在: {script_path}"
        


    # 3.冗余服务关闭
    @allure_setup("DSFR-1404", is_async=False)
    def test_jira_summary3(self):
        summary, _ = get_jira_summary("DSFR-1404")
        print(f"Summary: {summary}")

        # 停止冗余服务
        url = "http://**************:55020/Monitor/NodeControl"
        body = {"NodeIp":"**************", "ProcessType":"System", "ProcessName":"dsfrmservice", "Control":"stop"}
        method = "POST"
        status1, resp1 = call_url_with_body(url, body, method)
        #冗余服务关闭后
        time.sleep(3)  # 等待3秒钟

        # 验证主备节点状态
        masterStatus = getRMstatus("127.0.0.1")
        assert masterStatus == 1
        slaveStatus = getRMstatus("**************")
        assert slaveStatus == 0
        
        
        #启动进程
        url = "http://**************:55020/Monitor/NodeControl"
        body = {"NodeIp":"**************", "ProcessType":"System", "ProcessName":"dsfrmservice", "Control":"run"}
        method = "POST"
        status2, resp2 = call_url_with_body(url, body, method)
        time.sleep(5)  # 等待5秒钟


        # 验证主备节点状态
        masterStatus = getRMstatus("127.0.0.1")
        assert masterStatus == 1
        slaveStatus = getRMstatus("**************")
        assert slaveStatus == 0

    # 5.组合验证缺陷：先关闭主机核心服务，再主动切换，再开启服务
    @allure_setup("DSFR-1462", is_async=False)
    def test_jira_summary5(self):
        summary, _ = get_jira_summary("DSFR-1462")
        print(f"Summary: {summary}")
        #停止进程
        url = "http://127.0.0.1:55020/Monitor/NodeControl"
        body = {"NodeIp":"127.0.0.1", "ProcessType":"System", "ProcessName":"dsfacquisitionservice", "Control":"stop"}
        method = "POST"
        status1, resp1 = call_url_with_body(url, body, method)
        time.sleep(8)  # 等待8秒钟

        # 验证主备节点状态
        masterStatus = getRMstatus("127.0.0.1")
        assert masterStatus == 0
        slaveStatus = getRMstatus("**************")
        assert slaveStatus == 1
        
        #当前主机应该是非活动，向备机发送切换命令
        url = "http://**************:55020/Monitor/RMSwitch"
        body = {"NodeIp" : "**************"}
        method = "GET"
        status, resp = call_url_with_body(url, body, method)
        print(f"Status: {status}, Response: {resp}")
        resp_dict = json.loads(resp.strip())

        time.sleep(2)  # 等待2秒钟
        assert 200 == status

        # 验证主备节点状态
        masterStatus = getRMstatus("127.0.0.1")
        assert masterStatus == 0

        slaveStatus = getRMstatus("**************")
        assert slaveStatus == 1
        
        #启动进程
        url = "http://127.0.0.1:55020/Monitor/NodeControl"
        body = {"NodeIp":"127.0.0.1", "ProcessType":"System", "ProcessName":"dsfacquisitionservice", "Control":"run"}
        method = "POST"
        status2, resp2 = call_url_with_body(url, body, method)
        time.sleep(3)  # 等待3秒钟

        #恢复106活动107非活动    
        #当前主机应该是非活动，向备机发送切换命令
        url = "http://**************:55020/Monitor/RMSwitch"
        body = {"NodeIp" : "**************"}
        method = "GET"
        status, resp = call_url_with_body(url, body, method)
        print(f"Status: {status}, Response: {resp}")
        resp_dict = json.loads(resp.strip())

        time.sleep(2)  # 等待2秒钟
        assert 200 == status


# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_DSFRMService.py"])