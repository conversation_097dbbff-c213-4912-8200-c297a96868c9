import logging
import pytest

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

# 配置日志记录，将日志写入文件
logging.basicConfig(level=logging.INFO, filename='test_deploy.log', filemode='w', format='%(asctime)s - %(levelname)s - %(message)s')

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
# application_path="/home/<USER>/work/dr/"
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
config_path = os.path.join(application_path,"projects/defaultproject/config")
#服务地址
service_host=b"127.0.0.1"
#端口
service_port=55100
#全局变量
deploysdk=None
dsfdeployservice=None

#重启所有进程
def restart_all_process():
    dsfpmrapi=loadDSFPMRAPILibrary(application_path)
    if not dsfpmrapi : return False

    print("重启所有进程")
    if get_program_status_by_name("dsfdataarchive") == 0:
        # 等待所有进程重启
        time.sleep(10)

#下发空配置
def write_empty_config():
    # 获取文件路径
    documents_path = os.path.join(os.path.dirname(__file__), "documents")
    file_name = "config.zip"
    file_path = os.path.join(documents_path, file_name)

    # 打开文件并读取内容
    try:
        with open(file_path, "rb") as file:
            buffer = file.read()
    except IOError as e:
        print(f"文件打开失败，详细信息：{e}")
        assert False

    file_size = len(buffer)

    # 向DSF节点下发配置
    timeout = 5000
    result = deploysdk.WriteData(service_host, service_port, buffer, file_size, timeout)
    print(f"下发配置结果：{result}")

    return result

#测试之前
def before_test():
    print("测试开始前执行")

    # 备份配置
    archive_name="config"
    if not backup_config(config_path,archive_name):
        print("备份配置失败")
        return False
    
    # 声明要使用全局变量
    global deploysdk
    global dsfdeployservice

    # 加载DeploySDK动态库
    print("加载DeploySDK动态库")
    deploysdk=loadDeploySDKLibrary(application_path)
    if not deploysdk : return False

    # 启动dsfdeployservice进程
    process_name="dsfdeployservice"
    if not is_process_running(process_name):
        print(f"启动{process_name}进程")
        command=f"./executable/{process_name}"
        dsfdeployservice=start_program(command,application_path)
        time.sleep(1)
        
#测试之后
def after_test():
    print("测试结束后执行")

    #关闭dsfdeployservice进程
    if dsfdeployservice!=None:
        print("关闭dsfdeployservice进程")
        stop_program(dsfdeployservice)

    # 恢复配置
    archive_path="config.zip"
    if not recover_config(config_path,archive_path):
        print("恢复配置失败")
        return False

    #重启所有进程
    restart_all_process()

    # 检查压缩文件是否存在
    if os.path.exists(archive_path):
        # 删除源 ZIP 文件
        os.remove(archive_path)
        print(f"压缩文件已删除: {archive_path}")
        return True
    else:
        print("压缩文件不存在")

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

class TestDeployService():

    @allure_setup("DSFR-57", is_async=False)
    def test_jira_summary_57(self):
        summary, _ = get_jira_summary("DSFR-57")
        # assert summary == "autotest验证DeployService的配置下发功能是否正常"

        # 下发空配置
        result = write_empty_config()
        assert  0 == result

        # 等待所有进程重启
        time.sleep(10)

    @allure_setup("DSFR-1438", is_async=False)
    def test_jira_summary_1438(self):
        summary, _ = get_jira_summary("DSFR-1438")
        # assert summary == "autotest验证DeployService的配置下发后redis是否已经清空"

        # # 下发空配置
        # result = write_empty_config()
        # assert  0 == result
        # time.sleep(1)

        # 获取redis中键的数量（前DSFR-57用例已经换上空配置，这里只需要直接获取数量）
        count=get_key_count_from_redis()
        assert  0 == count

    @allure_setup("DSFR-56", is_async=False)
    def test_jira_summary_56(self):
        summary, _ = get_jira_summary("DSFR-56")
        # assert summary == "autotest验证DeployService解压文件失败情况"

        # 获取文件路径
        documents_path = os.path.join(os.path.dirname(__file__), "documents")
        file_name = "invalidfile.txt"
        file_path = os.path.join(documents_path, file_name)

        # 打开文件并读取内容
        try:
            with open(file_path, "rb") as file:
                buffer = file.read()
        except IOError as e:
            print(f"文件打开失败，详细信息：{e}")
            assert False

        file_size = len(buffer)

        # 向DSF节点下发配置
        timeout = 5000
        result = deploysdk.WriteData(service_host, service_port, buffer, file_size, timeout)
        print(f"结果：{result}")

        assert  200405 == result

    @allure_setup("DSFR-68", is_async=False)
    def test_jira_summary_68(self):
        summary, _ = get_jira_summary("DSFR-68")
        # assert summary == "autotest验证连接到DeployService失败情况"

        # 获取文件路径
        documents_path = os.path.join(os.path.dirname(__file__), "documents")
        file_name = "config.zip"
        file_path = os.path.join(documents_path, file_name)

        # 打开文件并读取内容
        try:
            with open(file_path, "rb") as file:
                buffer = file.read()
        except IOError as e:
            print(f"文件打开失败，详细信息：{e}")
            assert False

        file_size = len(buffer)

        # 向DSF节点下发配置
        ip=b"***********"
        timeout = 5000
        result = deploysdk.WriteData(ip, service_port, buffer, file_size, timeout)
        print(f"结果：{result}")

        assert  200402 == result

# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfdeployservice.py"])