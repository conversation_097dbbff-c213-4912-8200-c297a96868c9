import os
import pytest

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
config_path = f"{application_path}/projects/defaultproject/config"
#全局变量
dsfpmrapi=None

#测试之前
def before_test():
    print("测试开始前执行")
    # 声明要使用全局变量
    global dsfpmrapi

    # 加载PMRAPI动态库
    print("加载PMRAPI动态库")
    dsfpmrapi=loadDSFPMRAPILibrary(application_path)
    if not dsfpmrapi : return

    # 初始化PMRAPI
    print("初始化DSFPMRAPI")
    initDSFPMRAPI(dsfpmrapi)

#测试之后
def after_test():
    print("测试结束后执行")
    # 销毁
    print("销毁PMRAPI")
    freeDSFPMRAPI(dsfpmrapi)

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

class TestDSFProcessMgr():

    @allure_setup("DSFR-1373", is_async=False)
    def test_jira_summary_1373(self):
        summary, _ = get_jira_summary("DSFR-1373")
        # assert summary == "autotest验证dsfprocessmgr能否正常启动"
        
        #验证
        result=is_process_running("dsfprocessmgr")
        assert True==result

    @allure_setup("DSFR-1374", is_async=False)
    def test_jira_summary_1374(self):
        summary, _ = get_jira_summary("DSFR-1374")
        # assert summary == "autotest验证dsfprocessmgr能否正常启动dsfdataservice"
        
        #验证
        result=is_process_running("dsfdataservice")
        assert True==result

    @allure_setup("DSFR-1375", is_async=False)
    def test_jira_summary_1375(self):
        summary, _ = get_jira_summary("DSFR-1375")
        # assert summary == "autotest验证dsfprocessmgr能否正常启动dsfacquisitions"
        
        #验证
        result=is_process_running("dsfacquisitions")
        assert True==result

    @allure_setup("DSFR-1376", is_async=False)
    def test_jira_summary_1376(self):
        summary, _ = get_jira_summary("DSFR-1376")
        # assert summary == "autotest验证dsfprocessmgr能否正常启动dsfrmservice"
        
        #验证
        result=is_process_running("dsfrmservice")
        assert True==result

    @allure_setup("DSFR-1377", is_async=False)
    def test_jira_summary_1377(self):
        summary, _ = get_jira_summary("DSFR-1377")
        # assert summary == "autotest验证dsfprocessmgr能否正常启动dsfdrvctrl"
        
        #验证
        result=is_process_running("dsfdrvctrl")
        assert True==result

    @allure_setup("DSFR-1378", is_async=False)
    def test_jira_summary_1378(self):
        summary, _ = get_jira_summary("DSFR-1378")
        # assert summary == "autotest验证dsfprocessmgr能否正常启动dsfopcuapublish"
        
        #验证
        result=is_process_running("dsfopcuapublish")
        assert True==result

    @allure_setup("DSFR-1379", is_async=False)
    def test_jira_summary_1379(self):
        summary, _ = get_jira_summary("DSFR-1379")
        # assert summary == "autotest验证dsfprocessmgr能否正常启动dsfdatafileservice"
        
        #验证
        result=is_process_running("dsfdatafileservice")
        assert True==result

    @allure_setup("DSFR-1380", is_async=False)
    def test_jira_summary_1380(self):
        summary, _ = get_jira_summary("DSFR-1380")
        # assert summary == "autotest验证dsfprocessmgr能否正常启动dsfdataarchive"
        
        #验证
        result=is_process_running("dsfdataarchive")
        assert True==result

    @allure_setup("DSFR-1381", is_async=False)
    def test_jira_summary_1381(self):
        summary, _ = get_jira_summary("DSFR-1381")
        # assert summary == "autotest验证dsfdataservice异常关闭，dsfprocessmgr能否重新拉起dsfdataservice"

        stop_program_by_name("dsfdataservice")

        time.sleep(5)
        
        #验证
        result=is_process_running("dsfdataservice")
        assert True==result

    @allure_setup("DSFR-1382", is_async=False)
    def test_jira_summary_1382(self):
        summary, _ = get_jira_summary("DSFR-1382")
        # assert summary == "autotest验证dsfacquisitionservice异常关闭，dsfprocessmgr能否重新拉起dsfacquisitions"
        
        stop_program_by_name("dsfacquisitions")

        time.sleep(5)

        #验证
        result=is_process_running("dsfacquisitions")
        assert True==result

    @allure_setup("DSFR-1383", is_async=False)
    def test_jira_summary_1383(self):
        summary, _ = get_jira_summary("DSFR-1383")
        # assert summary == "autotest验证dsfrmservice异常关闭，dsfprocessmgr能否重新拉起dsfrmservice"
        
        stop_program_by_name("dsfrmservice")

        time.sleep(5)

        #验证
        result=is_process_running("dsfrmservice")
        assert True==result

    @allure_setup("DSFR-1384", is_async=False)
    def test_jira_summary_1384(self):
        summary, _ = get_jira_summary("DSFR-1384")
        # assert summary == "autotest验证dsfdrvctrl异常关闭，dsfprocessmgr能否重新拉起dsfdrvctrl"
        
        stop_program_by_name("dsfdrvctrl")

        time.sleep(5)

        #验证
        result=is_process_running("dsfdrvctrl")
        assert True==result

    @allure_setup("DSFR-1385", is_async=False)
    def test_jira_summary_1385(self):
        summary, _ = get_jira_summary("DSFR-1385")
        # assert summary == "autotest验证dsfopcuapublish异常关闭，dsfprocessmgr能否重新拉起dsfopcuapublish"
        
        stop_program_by_name("dsfopcuapublish")

        time.sleep(5)

        #验证
        result=is_process_running("dsfopcuapublish")
        assert True==result

    @allure_setup("DSFR-1388", is_async=False)
    def test_jira_summary_1388(self):
        summary, _ = get_jira_summary("DSFR-1388")
        # assert summary == "autotest验证dsfdatafileservice异常关闭，dsfprocessmgr能否重新拉起dsfdatafileservice"
        
        stop_program_by_name("dsfdatafileservice")

        time.sleep(5)

        #验证
        result=is_process_running("dsfdatafileservice")
        assert True==result

    @allure_setup("DSFR-1389", is_async=False)
    def test_jira_summary_1389(self):
        summary, _ = get_jira_summary("DSFR-1389")
        # assert summary == "autotest验证dsfdataarchive异常关闭，dsfprocessmgr能否重新拉起dsfdataarchive"
        
        stop_program_by_name("dsfdataarchive")

        time.sleep(5)

        #验证
        result=is_process_running("dsfdataarchive")
        assert True==result

    @allure_setup("DSFR-1394", is_async=False)
    def test_jira_summary_1394(self):
        summary, _ = get_jira_summary("DSFR-1394")
        # assert summary == "autotest验证dsfprocessmgr能否正常关闭dsfdataservice"

        stopProcess(dsfpmrapi, "dsfdataservice")

        time.sleep(5)

        #验证
        result=get_program_status_by_name("dsfdataservice")
        assert False==result
        
    @allure_setup("DSFR-1395", is_async=False)
    def test_jira_summary_1395(self):
        summary, _ = get_jira_summary("DSFR-1395")
        # assert summary == "autotest验证dsfprocessmgr能否正常关闭dsfacquisitions"

        stopProcess(dsfpmrapi, "dsfacquisitions")

        time.sleep(5)

        #验证
        result=get_program_status_by_name("dsfacquisitions")
        assert False==result

    @allure_setup("DSFR-1396", is_async=False)
    def test_jira_summary_1396(self):
        summary, _ = get_jira_summary("DSFR-1396")
        # assert summary == "autotest验证dsfprocessmgr能否正常关闭dsfrmservice"

        stopProcess(dsfpmrapi, "dsfrmservice")

        time.sleep(5)

        #验证
        result=get_program_status_by_name("dsfrmservice")
        assert False==result

    @allure_setup("DSFR-1397", is_async=False)
    def test_jira_summary_1397(self):
        summary, _ = get_jira_summary("DSFR-1397")
        # assert summary == "autotest验证dsfprocessmgr能否正常关闭dsfdrvctrl"

        stopProcess(dsfpmrapi, "dsfdrvctrl")

        time.sleep(5)

        #验证
        result=get_program_status_by_name("dsfdrvctrl")
        assert False==result

    @allure_setup("DSFR-1398", is_async=False)
    def test_jira_summary_1398(self):
        summary, _ = get_jira_summary("DSFR-1398")
        # assert summary == "autotest验证dsfprocessmgr能否正常关闭dsfopcuapublish"

        stopProcess(dsfpmrapi, "dsfopcuapublish")

        time.sleep(5)

        #验证
        result=get_program_status_by_name("dsfopcuapublish")
        assert False==result

    @allure_setup("DSFR-1399", is_async=False)
    def test_jira_summary_1399(self):
        summary, _ = get_jira_summary("DSFR-1399")
        # assert summary == "autotest验证dsfprocessmgr能否正常关闭dsfdatafileservice"

        stopProcess(dsfpmrapi, "dsfdatafileservice")

        time.sleep(5)

        #验证
        result=get_program_status_by_name("dsfdatafileservice")
        assert False==result

    @allure_setup("DSFR-1400", is_async=False)
    def test_jira_summary_1400(self):
        summary, _ = get_jira_summary("DSFR-1400")
        # assert summary == "autotest验证dsfprocessmgr能否正常关闭dsfdataarchive"

        stopProcess(dsfpmrapi, "dsfdataarchive")

        time.sleep(5)

        #验证
        result=get_program_status_by_name("dsfdataarchive")
        assert False==result

# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfprocessmgr.py"])