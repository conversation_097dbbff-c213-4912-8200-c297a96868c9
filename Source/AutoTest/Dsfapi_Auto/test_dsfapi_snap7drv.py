import logging
import sys
import pytest
import subprocess
import time
import os
import math

from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
# application_path="/home/<USER>/work/dr/"
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
#驱动名称
driver_name='snap7drv'
#设备名称
device_name=f'{driver_name}_Device0'
#全局变量
dsfapi=None

#读取值
# param1 信号tag名
# param2 测试值
def check_read_value(tag_name,data_type,test_value):
    value=None
    value = read_value(dsfapi, tag_name, data_type)

    if data_type == "BOOL":
        assert bool(value) == test_value
    elif data_type == "CHAR":
        assert int(value) == test_value
    elif data_type == "SINT":
        assert int(value) == test_value
    elif data_type == "USINT":
        assert int(value) == test_value
    elif data_type == "BYTE":
        assert int(value) == test_value
    elif data_type == "INT":
        assert int(value) == test_value
    elif data_type == "UINT":
        assert int(value) == test_value
    elif data_type == "WORD":
        assert int(value) == test_value
    elif data_type == "REAL":
        assert math.isclose(float(value), test_value, rel_tol=1e-5)  # 使用相对误差比较
        # assert float(value) == test_value
    elif data_type == "LREAL":
        assert float(value) == test_value
    elif data_type == "UDINT":
        assert int(value) == test_value
    elif data_type == "DINT":
        assert int(value) == test_value
    elif data_type == "DWORD":
        assert int(value) == test_value
    elif data_type == "LINT":
        assert int(value) == test_value
    elif data_type == "LWORD":
        assert int(value) == test_value
    elif data_type == "STRING":
        # if isinstance(value, bytes):
        #     value = value.decode('utf-8')
        assert str(value) == test_value
    else:
        assert value==test_value

#测试之前
def before_test():
    print("测试开始前执行")
    # 声明要使用全局变量
    global dsfapi

    # 加载DSFAPI动态库
    print("加载DSFAPI动态库")
    dsfapi=loadDSFAPILibrary(application_path)
    if not dsfapi : return

    # 初始化DSFAPI
    print("初始化DSFAPI")
    initDSFAPI(dsfapi)

#测试之后
def after_test():
    print("测试结束后执行")
    # 销毁
    print("销毁DSFAPI")
    dsfapi.freeDRSdkContext()

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

class TestSnap7drv():

    #### DB块写入测试 ####
    @allure_setup("DSFR-909", is_async=False)
    def test_snap7_write_bool_001(self):
        write_value(dsfapi,"STD::SNAP7_READ_BOOL", "BOOL", 1)
        check_read_value("STD::SNAP7_READ_BOOL","BOOL", 1)

    @allure_setup("DSFR-910", is_async=False)
    def test_snap7_write_char_002(self):
        write_value(dsfapi,"STD::SNAP7_READ_CHAR", "CHAR", 2)
        check_read_value("STD::SNAP7_READ_CHAR", "CHAR", 2)

    @allure_setup("DSFR-911", is_async=False)
    def test_snap7_write_sint_003(self):
        write_value(dsfapi,"STD::SNAP7_READ_SINT", "SINT", 3)
        check_read_value("STD::SNAP7_READ_SINT","SINT", 3)

    @allure_setup("DSFR-912", is_async=False)
    def test_snap7_write_usint_004(self):
        write_value(dsfapi,"STD::SNAP7_READ_USINT", "USINT", 4)
        check_read_value("STD::SNAP7_READ_USINT","USINT", 4)

    @allure_setup("DSFR-913", is_async=False)
    def test_snap7_write_byte_005(self):
        write_value(dsfapi,"STD::SNAP7_READ_BYTE", "BYTE", 5)
        check_read_value("STD::SNAP7_READ_BYTE", "BYTE",5)

    @allure_setup("DSFR-914", is_async=False)
    def test_snap7_write_int_006(self):
        write_value(dsfapi,"STD::SNAP7_READ_INT", "INT", 6)
        check_read_value("STD::SNAP7_READ_INT", "INT",6)

    @allure_setup("DSFR-915", is_async=False)
    def test_snap7_write_uint_007(self):
        write_value(dsfapi,"STD::SNAP7_READ_UINT", "UINT", 7)
        check_read_value("STD::SNAP7_READ_UINT", "UINT",7)

    @allure_setup("DSFR-916", is_async=False)
    def test_snap7_write_word_008(self):
        write_value(dsfapi,"STD::SNAP7_READ_WORD", "WORD", 8)
        check_read_value("STD::SNAP7_READ_WORD","WORD", 8)

    @allure_setup("DSFR-917", is_async=False)
    def test_snap7_write_real_009(self):
        write_value(dsfapi,"STD::SNAP7_READ_REAL", "REAL", 9)
        check_read_value("STD::SNAP7_READ_REAL","REAL", 9)

    @allure_setup("DSFR-918", is_async=False)
    def test_snap7_write_udint_010(self):
        write_value(dsfapi,"STD::SNAP7_READ_UDINT", "UDINT", 10)
        check_read_value("STD::SNAP7_READ_UDINT", "UDINT", 10)

    @allure_setup("DSFR-919", is_async=False)
    def test_snap7_write_dint_011(self):
        write_value(dsfapi,"STD::SNAP7_READ_DINT", "DINT", 11)
        check_read_value("STD::SNAP7_READ_DINT", "DINT",11)

    @allure_setup("DSFR-920", is_async=False)
    def test_snap7_write_dword_012(self):
        write_value(dsfapi,"STD::SNAP7_READ_DWORD", "DWORD", 12)
        check_read_value("STD::SNAP7_READ_DWORD",  "DWORD",12)

    # 这三种类型在S7300/S7400中不支持
    # @allure_setup("DSFR-921", is_async=False)
    # def test_snap7_write_013(self):
    #     write_value(dsfapi,"STD::SNAP7_READ_LREAL", "LREAL", 13)
    #     check_read_value("STD::SNAP7_READ_LREAL", "LREAL",13)

    # @allure_setup("DSFR-922", is_async=False)
    # def test_snap7_write_014(self):
    #     write_value(dsfapi,"STD::SNAP7_READ_LINT", "LINT", 14)
    #     check_read_value("STD::SNAP7_READ_LINT", "LINT",14)
    
    # @allure_setup("DSFR-923", is_async=False)
    # def test_snap7_write_015(self):
    #     write_value(dsfapi,"STD::SNAP7_READ_LWORD", "LWORD", 15)
    #     check_read_value("STD::SNAP7_READ_LWORD", "LWORD",15)

    @allure_setup("DSFR-924", is_async=False)
    def test_snap7_write_string_016(self):
        write_value(dsfapi,"STD::SNAP7_READ_STRING", "STRING", "test_string")
        check_read_value("STD::SNAP7_READ_STRING", "STRING","test_string")

    #### DB块读取测试 ####
    @allure_setup("DSFR-925", is_async=False)
    def test_snap7_read_bool_017(self):
        check_read_value("STD::SNAP7_READ_BOOL","BOOL", 1)

    @allure_setup("DSFR-926", is_async=False)
    def test_snap7_read_char_018(self):
        check_read_value("STD::SNAP7_READ_CHAR", "CHAR",2)

    @allure_setup("DSFR-927", is_async=False)
    def test_snap7_read_sint_019(self):
        check_read_value("STD::SNAP7_READ_SINT","SINT", 3)

    @allure_setup("DSFR-928", is_async=False)
    def test_snap7_read_usint_020(self):
        check_read_value("STD::SNAP7_READ_USINT","USINT", 4)

    @allure_setup("DSFR-929", is_async=False)
    def test_snap7_read_byte_021(self):
        check_read_value("STD::SNAP7_READ_BYTE", "BYTE",5)

    @allure_setup("DSFR-930", is_async=False)
    def test_snap7_read_int_022(self):
        write_value(dsfapi,"STD::SNAP7_READ_INT", "INT", 6)
        check_read_value("STD::SNAP7_READ_INT", "INT",6)

    @allure_setup("DSFR-931", is_async=False)
    def test_snap7_read_uint_023(self):
        check_read_value("STD::SNAP7_READ_UINT", "UINT",7)

    @allure_setup("DSFR-932", is_async=False)
    def test_snap7_read_word_024(self):
        check_read_value("STD::SNAP7_READ_WORD","WORD", 8)

    @allure_setup("DSFR-933", is_async=False)
    def test_snap7_read_real_025(self):
        check_read_value("STD::SNAP7_READ_REAL","REAL", 9)

    @allure_setup("DSFR-934", is_async=False)
    def test_snap7_read_udint_026(self):
        check_read_value("STD::SNAP7_READ_UDINT", "UDINT", 10)

    @allure_setup("DSFR-935", is_async=False)
    def test_snap7_read_dint_027(self):
        check_read_value("STD::SNAP7_READ_DINT", "DINT",11)

    @allure_setup("DSFR-936", is_async=False)
    def test_snap7_read_dword_028(self):
        check_read_value("STD::SNAP7_READ_DWORD",  "DWORD",12)

    # @allure_setup("DSFR-937", is_async=False)
    # def test_snap7_read_lreal_029(self):
    #     check_read_value("STD::SNAP7_READ_LREAL", "LREAL",13)

    # @allure_setup("DSFR-938", is_async=False)
    # def test_snap7_read_lint_030(self):
    #     check_read_value("STD::SNAP7_READ_LINT", "LINT",14)
    
    # @allure_setup("DSFR-939", is_async=False)
    # def test_snap7_read_lword_031(self):
    #     check_read_value("STD::SNAP7_READ_LWORD", "LWORD",15)

    @allure_setup("DSFR-940", is_async=False)
    def test_snap7_read_string_032(self):
        check_read_value("STD::SNAP7_READ_STRING", "STRING","test_string")

     #### I/Q ####
    @allure_setup("DSFR-1036", is_async=False)
    def test_snap7_write_I_bool_033(self):
        write_value(dsfapi,"STD::SNAP7_READ_I_BOOL", "BOOL", 1)
        check_read_value("STD::SNAP7_READ_I_BOOL","BOOL", 1)

    @allure_setup("DSFR-1037", is_async=False)
    def test_snap7_write_Q_bool_034(self):
        write_value(dsfapi,"STD::SNAP7_READ_Q_BOOL", "BOOL", 1)
        check_read_value("STD::SNAP7_READ_Q_BOOL","BOOL", 1)

    @allure_setup("DSFR-1038", is_async=False)
    def test_snap7_read_I_bool_035(self):
        check_read_value("STD::SNAP7_READ_I_BOOL","BOOL", 1)

    @allure_setup("DSFR-1039", is_async=False)
    def test_snap7_read_Q_bool_036(self):
        check_read_value("STD::SNAP7_READ_Q_BOOL","BOOL", 1)

     #### M ####
    @allure_setup("DSFR-1040", is_async=False)
    def test_snap7_read_M_bool_037(self):
        write_value(dsfapi,"STD::SNAP7_READ_M_BOOL", "BOOL", 1)
        check_read_value("STD::SNAP7_READ_M_BOOL",  "BOOL",1)

    @allure_setup("DSFR-1041", is_async=False)
    def test_snap7_read_M_char_038(self):
        write_value(dsfapi,"STD::SNAP7_READ_M_CHAR", "CHAR", 9)
        check_read_value("STD::SNAP7_READ_M_CHAR",  "CHAR",9)

    @allure_setup("DSFR-1042", is_async=False)
    def test_snap7_read_M_sint_039(self):
        write_value(dsfapi,"STD::SNAP7_READ_M_SINT", "SINT", 98)
        check_read_value("STD::SNAP7_READ_M_SINT",  "SINT",98)

    @allure_setup("DSFR-1043", is_async=False)
    def test_snap7_read_M_usint_040(self):
        write_value(dsfapi,"STD::SNAP7_READ_M_USINT", "USINT", 97)
        check_read_value("STD::SNAP7_READ_M_USINT",  "USINT",97)

    @allure_setup("DSFR-1044", is_async=False)
    def test_snap7_read_M_byte_041(self):
        write_value(dsfapi,"STD::SNAP7_READ_M_BYTE", "BYTE", 96)
        check_read_value("STD::SNAP7_READ_M_BYTE",  "BYTE",96)

    @allure_setup("DSFR-1045", is_async=False)
    def test_snap7_read_M_int_042(self):
        write_value(dsfapi,"STD::SNAP7_READ_M_INT", "INT", 95)
        check_read_value("STD::SNAP7_READ_M_INT",  "INT",95)

    @allure_setup("DSFR-1046", is_async=False)
    def test_snap7_read_M_uint_043(self):
        write_value(dsfapi,"STD::SNAP7_READ_M_UINT", "UINT", 94)
        check_read_value("STD::SNAP7_READ_M_UINT",  "UINT",94)

    @allure_setup("DSFR-1047", is_async=False)
    def test_snap7_read_M_word_044(self):
        write_value(dsfapi,"STD::SNAP7_READ_M_WORD", "WORD", 93)
        check_read_value("STD::SNAP7_READ_M_WORD",  "WORD",93)

    @allure_setup("DSFR-1048", is_async=False)
    def test_snap7_read_M_real_045(self):
        write_value(dsfapi,"STD::SNAP7_READ_M_REAL", "REAL", 92)
        check_read_value("STD::SNAP7_READ_M_REAL",  "REAL",92)

    @allure_setup("DSFR-1049", is_async=False)
    def test_snap7_read_M_udint_046(self):
        write_value(dsfapi,"STD::SNAP7_READ_M_UDINT", "UDINT", 91)
        check_read_value("STD::SNAP7_READ_M_UDINT",  "UDINT",91)

    @allure_setup("DSFR-1050", is_async=False)
    def test_snap7_read_M_dint_047(self):
        write_value(dsfapi,"STD::SNAP7_READ_M_DINT", "DINT", 90)
        check_read_value("STD::SNAP7_READ_M_DINT",  "DINT",90)

    @allure_setup("DSFR-1051", is_async=False)
    def test_snap7_read_M_dword_048(self):
        write_value(dsfapi,"STD::SNAP7_READ_M_DWORD", "DWORD", 89)
        check_read_value("STD::SNAP7_READ_M_DWORD",  "DWORD",89)

    # 这四种类型M不支持
    # @allure_setup("DSFR-1052", is_async=False)
    # def test_snap7_read_M_lreal_049(self):
    #     assert False

    # @allure_setup("DSFR-1053", is_async=False)
    # def test_snap7_read_M_lint_050(self):
    #     assert False

    # @allure_setup("DSFR-1054", is_async=False)
    # def test_snap7_read_M_lword_051(self):
    #     assert False

    # @allure_setup("DSFR-1055", is_async=False)
    # def test_snap7_read_M_string_052(self):
    #     assert False

    #### 边界值 ####
    @allure_setup("DSFR-1056", is_async=False)
    def test_snap7_read_max_string_053(self):
        # 组织一个255长度全是A的字符串
        max_string = 'A' * 20
        write_value(dsfapi,"STD::SNAP7_READ_MAX_STRING", "STRING", max_string)
        check_read_value("STD::SNAP7_READ_MAX_STRING", "STRING",max_string)

    @allure_setup("DSFR-1057", is_async=False)
    def test_snap7_read_empty_string_054(self):
        write_value(dsfapi,"STD::SNAP7_READ_MIN_STRING", "STRING", "")
        check_read_value("STD::SNAP7_READ_MIN_STRING", "STRING","")

    # @allure_setup("DSFR-1058", is_async=False)
    # def test_snap7_read_max_lword_055(self):
    #     assert False

    # @allure_setup("DSFR-1059", is_async=False)
    # def test_snap7_read_min_lword_056(self):
    #     assert False

    # @allure_setup("DSFR-1060", is_async=False)
    # def test_snap7_read_max_lint_057(self):
    #     assert False

    # @allure_setup("DSFR-1061", is_async=False)
    # def test_snap7_read_min_lint_058(self):
    #     assert False

    # @allure_setup("DSFR-1062", is_async=False)
    # def test_snap7_read_max_lreal_059(self):
    #     assert False

    # @allure_setup("DSFR-1063", is_async=False)
    # def test_snap7_read_min_lreal_060(self):
    #     assert False

    @allure_setup("DSFR-1064", is_async=False)
    def test_snap7_read_max_dword_061(self):
        write_value(dsfapi,"STD::SNAP7_READ_MAX_DWORD", "DWORD", 0xFFFFFFFF)
        check_read_value("STD::SNAP7_READ_MAX_DWORD",  "DWORD",0xFFFFFFFF)

    @allure_setup("DSFR-1065", is_async=False)
    def test_snap7_read_min_dword_062(self):
        write_value(dsfapi,"STD::SNAP7_READ_MIN_DWORD", "DWORD", 0)
        check_read_value("STD::SNAP7_READ_MIN_DWORD",  "DWORD",0)

    @allure_setup("DSFR-1066", is_async=False)
    def test_snap7_read_max_dint_063(self):
        # 4字节有符号位，最大值为0x7FFFFFFF
        write_value(dsfapi,"STD::SNAP7_READ_MAX_DINT", "DINT", 0x7FFFFFFF)
        check_read_value("STD::SNAP7_READ_MAX_DINT",  "DINT",0x7FFFFFFF)

    @allure_setup("DSFR-1067", is_async=False)
    def test_snap7_read_min_dint_064(self):
        # 4字节有符号位，最小值为0x80000000
        # 注意：0x80000000在Python中是负数，表示-2147483648
        write_value(dsfapi,"STD::SNAP7_READ_MIN_DINT", "DINT", -2**31)
        check_read_value("STD::SNAP7_READ_MIN_DINT",  "DINT",-2**31)

    @allure_setup("DSFR-1068", is_async=False)
    def test_snap7_read_max_udint_065(self):
        # 4字节无符号位，最大值为0xFFFFFFFF
        write_value(dsfapi,"STD::SNAP7_READ_MAX_UDINT", "UDINT", 0xFFFFFFFF)
        check_read_value("STD::SNAP7_READ_MAX_UDINT",  "UDINT",0xFFFFFFFF)

    @allure_setup("DSFR-1069", is_async=False)
    def test_snap7_read_min_udint_066(self):
        write_value(dsfapi,"STD::SNAP7_READ_MIN_UDINT", "UDINT", 0)
        check_read_value("STD::SNAP7_READ_MIN_UDINT",  "UDINT",0)

    @allure_setup("DSFR-1070", is_async=False)
    def test_snap7_read_max_real_067(self):
        # 4字节浮点数，最大值为3.4028235e+38
        write_value(dsfapi,"STD::SNAP7_READ_MAX_REAL", "REAL", 3.4028235e+38)
        check_read_value("STD::SNAP7_READ_MAX_REAL",  "REAL",3.4028235e+38)

    @allure_setup("DSFR-1071", is_async=False)
    def test_snap7_read_min_real_068(self):
        # 4字节浮点数，最小值为-3.4028235e+38
        write_value(dsfapi,"STD::SNAP7_READ_MIN_REAL", "REAL", -3.4028235e+38)
        check_read_value("STD::SNAP7_READ_MIN_REAL",  "REAL",-3.4028235e+38)

    @allure_setup("DSFR-1072", is_async=False)
    def test_snap7_read_max_word_069(self):
        # 2字节无符号位，最大值为0xFFFF
        write_value(dsfapi,"STD::SNAP7_READ_MAX_WORD", "WORD", 0xFFFF)
        check_read_value("STD::SNAP7_READ_MAX_WORD",  "WORD",0xFFFF)

    @allure_setup("DSFR-1073", is_async=False)
    def test_snap7_read_min_word_070(self):
        write_value(dsfapi,"STD::SNAP7_READ_MIN_WORD", "WORD", 0)
        check_read_value("STD::SNAP7_READ_MIN_WORD",  "WORD",0)

    @allure_setup("DSFR-1074", is_async=False)
    def test_snap7_read_max_uint_071(self):
        write_value(dsfapi,"STD::SNAP7_READ_MAX_UINT", "UINT", 0xFFFF)
        check_read_value("STD::SNAP7_READ_MAX_UINT",  "UINT",0xFFFF)

    @allure_setup("DSFR-1075", is_async=False)
    def test_snap7_read_min_uint_072(self):
        write_value(dsfapi,"STD::SNAP7_READ_MIN_UINT", "UINT", 0)
        check_read_value("STD::SNAP7_READ_MIN_UINT",  "UINT",0)

    @allure_setup("DSFR-1076", is_async=False)
    def test_snap7_read_max_int_073(self):
        # 2字节有符号位，最大值为0xFFFF
        write_value(dsfapi,"STD::SNAP7_READ_MAX_INT", "INT", 2**15 - 1)  # 32767
        check_read_value("STD::SNAP7_READ_MAX_INT",  "INT",2**15 - 1)

    @allure_setup("DSFR-1077", is_async=False)
    def test_snap7_read_min_int_074(self):
        write_value(dsfapi,"STD::SNAP7_READ_MIN_INT", "INT", -2**15)
        check_read_value("STD::SNAP7_READ_MIN_INT",  "INT",-2**15)

    @allure_setup("DSFR-1078", is_async=False)
    def test_snap7_read_max_byte_075(self):
        write_value(dsfapi,"STD::SNAP7_READ_MAX_BYTE", "BYTE", 255)  # 32767
        check_read_value("STD::SNAP7_READ_MAX_BYTE",  "BYTE",255)

    @allure_setup("DSFR-1079", is_async=False)
    def test_snap7_read_min_byte_076(self):
        write_value(dsfapi,"STD::SNAP7_READ_MIN_BYTE", "BYTE", 0)
        check_read_value("STD::SNAP7_READ_MIN_BYTE",  "BYTE",0)

    @allure_setup("DSFR-1080", is_async=False)
    def test_snap7_read_max_usint_077(self):
        write_value(dsfapi,"STD::SNAP7_READ_MAX_USINT", "USINT", 0xFF)
        check_read_value("STD::SNAP7_READ_MAX_USINT",  "USINT",0xFF)

    @allure_setup("DSFR-1081", is_async=False)
    def test_snap7_read_min_usint_078(self):
        write_value(dsfapi,"STD::SNAP7_READ_MIN_USINT", "USINT", 0)
        check_read_value("STD::SNAP7_READ_MIN_USINT",  "USINT",0)

    @allure_setup("DSFR-1082", is_async=False)
    def test_snap7_read_max_sint_079(self):
        write_value(dsfapi,"STD::SNAP7_READ_MAX_SINT", "SINT", 2**7 - 1)  # 32767
        check_read_value("STD::SNAP7_READ_MAX_SINT",  "SINT",2**7 - 1)

    @allure_setup("DSFR-1083", is_async=False)
    def test_snap7_read_min_sint_080(self):
        write_value(dsfapi,"STD::SNAP7_READ_MIN_SINT", "SINT", -2**7)
        check_read_value("STD::SNAP7_READ_MIN_SINT",  "SINT",-2**7)

    @allure_setup("DSFR-1084", is_async=False)
    def test_snap7_read_max_char_081(self):
        write_value(dsfapi,"STD::SNAP7_READ_MAX_CHAR", "CHAR", 255)  
        check_read_value("STD::SNAP7_READ_MAX_CHAR",  "CHAR",255)

    @allure_setup("DSFR-1085", is_async=False)
    def test_snap7_read_min_char_082(self):
        write_value(dsfapi,"STD::SNAP7_READ_MIN_CHAR", "CHAR", 0)
        check_read_value("STD::SNAP7_READ_MIN_CHAR",  "CHAR",0)


    ##### 其它测试 ####
    @allure_setup("DSFR-1088", is_async=False)
    def test_snap7_read_string_with_length_083(self):
       max_string = 'A' * 20
       check_read_value("STD::SNAP7_READ_MAX_STRING", "STRING",max_string)

    @allure_setup("DSFR-1089", is_async=False)
    def test_snap7_read_bool_with_length_084(self):
        check_read_value("STD::SNAP7_READ_I_BOOL","BOOL", 1)
    
    # 冗余验证
    @allure_setup("DSFR-1090", is_async=False)
    def test_snap7_redundancy_085(self):
        assert False

    # 多连接
    @allure_setup("DSFR-1091", is_async=False)
    def test_snap7_multilink_086(self):
        assert False

    # 连接状态
    @allure_setup("DSFR-1099", is_async=False)
    def test_snap7_read_min_char_087(self):
        check_read_value("DSF_STATION_1.SYS::SNAP7DRV_DEVICE0_CONNECTSTATUS","BOOL", 1)

if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_snap7drv.py"])
  