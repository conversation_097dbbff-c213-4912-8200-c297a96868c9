import os
import pytest
import time
# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
# application_path="/home/<USER>/work/dr/"
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
#驱动名称
driver_name='CODESYS'
#设备名称
device_name=f'{driver_name}_DEVICE0'
#全局变量
dsfapi=None

#测试之前
def before_test():
    print("测试开始前执行")
    # 声明要使用全局变量
    global dsfapi

    # 加载DSFAPI动态库
    print("加载DSFAPI动态库")
    dsfapi=loadDSFAPILibrary(application_path)
    if not dsfapi : return

    # 初始化DSFAPI
    print("初始化DSFAPI")
    initDSFAPI(dsfapi)

#测试之后
def after_test():
    print("测试结束后执行")
    # 销毁
    print("销毁DSFAPI")
    dsfapi.freeDRSdkContext()

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

class TestCodesys():

    ################################################写入################################################
    # 测试先写入最大值，读取验证再写回初始值读取验证
    @allure_setup("DSFR-996", is_async=False)
    def test_jira_summary_996(self):
        summary, _ = get_jira_summary("DSFR-996")
        logging.info(f"Summary:{summary}")
        # "autotest验证Codesys驱动写入数据准确性（BOOL）"
        
        data_type='BOOL'
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value=1  # BOOL最大值就是1

        result1=write_value(dsfapi,tag_name,data_type,test_value)

        # 因为最大值就是1，所以这里写入0然后再写回1
        test_value=0
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        test_value=1
        result3=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result1
        assert True==result2
        assert True==result3
        
        
    @allure_setup("DSFR-997", is_async=False)
    def test_jira_summary_997(self):
        summary, _ = get_jira_summary("DSFR-997")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（SINT）"
        
        data_type='SINT'
        tag_name=f"STD::{driver_name}_{data_type}"
        
        test_value=127
        result1=write_value(dsfapi,tag_name,data_type,test_value)
        
        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert True==result1
        assert True==result2
        
    @allure_setup("DSFR-998", is_async=False)
    def test_jira_summary_998(self):
        summary, _ = get_jira_summary("DSFR-998")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（USINT）"
        
        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value=255
        result1=write_value(dsfapi,tag_name,data_type,test_value)
        
        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert True==result1
        assert True==result2
        
    @allure_setup("DSFR-999", is_async=False)
    def test_jira_summary_999(self):
        summary, _ = get_jira_summary("DSFR-999")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（BYTE）"
        
        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value=127
        result1=write_value(dsfapi,tag_name,data_type,test_value)
        
        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert True==result1
        assert True==result2

    @allure_setup("DSFR-1000", is_async=False)
    def test_jira_summary_1000(self):
        summary, _ = get_jira_summary("DSFR-1000")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（INT）"
        
        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value=32767
        result1=write_value(dsfapi,tag_name,data_type,test_value)
        
        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert True==result1
        assert True==result2
        
    @allure_setup("DSFR-1001", is_async=False)
    def test_jira_summary_1001(self):
        summary, _ = get_jira_summary("DSFR-1001")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（UINT）"
        
        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value=65535
        result1=write_value(dsfapi,tag_name,data_type,test_value)
        
        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert True==result1
        assert True==result2
        
    @allure_setup("DSFR-1002", is_async=False)
    def test_jira_summary_1002(self):
        summary, _ = get_jira_summary("DSFR-1002")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（WORD）"
        
        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value=65535
        result1=write_value(dsfapi,tag_name,data_type,test_value)
        
        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert True==result1
        assert True==result2
        
    @allure_setup("DSFR-1003", is_async=False)
    def test_jira_summary_1003(self):
        summary, _ = get_jira_summary("DSFR-1003")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（REAL）"
        
        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value=3.40282e+38
        result1=write_value(dsfapi,tag_name,data_type,test_value)
        
        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert True==result1
        assert True==result2
        
    @allure_setup("DSFR-1004", is_async=False)
    def test_jira_summary_1004(self):
        summary, _ = get_jira_summary("DSFR-1004")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（UDINT）"
        
        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value=4294967295
        result1=write_value(dsfapi,tag_name,data_type,test_value)
        
        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert True==result1
        assert True==result2

    @allure_setup("DSFR-1005", is_async=False)
    def test_jira_summary_1005(self):
        summary, _ = get_jira_summary("DSFR-1005")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（DINT）"
        
        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value=2147483647
        result1=write_value(dsfapi,tag_name,data_type,test_value)
        
        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert True==result1
        assert True==result2

    @allure_setup("DSFR-1006", is_async=False)
    def test_jira_summary_1006(self):
        summary, _ = get_jira_summary("DSFR-1006")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（DWORD）"
        
        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value=4294967295
        result1=write_value(dsfapi,tag_name,data_type,test_value)
        
        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert True==result1
        assert True==result2
        
    @allure_setup("DSFR-1007", is_async=False)
    def test_jira_summary_1007(self):
        summary, _ = get_jira_summary("DSFR-1007")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（LREAL）"
        
        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value=1.79769e+308
        result1=write_value(dsfapi,tag_name,data_type,test_value)

        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert True==result1
        assert True==result2

    @allure_setup("DSFR-1008", is_async=False)
    def test_jira_summary_1008(self):
        summary, _ = get_jira_summary("DSFR-1008")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（LINT）"
        
        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value=9223372036854775807
        result1=write_value(dsfapi,tag_name,data_type,test_value)
        
        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert True==result1
        assert True==result2

    @allure_setup("DSFR-1009", is_async=False)
    def test_jira_summary_1009(self):
        summary, _ = get_jira_summary("DSFR-1009")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（ULINT）"
        
        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value=18446744073709551615
        result1=write_value(dsfapi,tag_name,data_type,test_value)

        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert True==result1
        assert True==result2
    
    @allure_setup("DSFR-1010", is_async=False)
    def test_jira_summary_1010(self):
        summary, _ = get_jira_summary("DSFR-1010")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（LWORD）"
        
        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value = 18446744073709551615
        result1=write_value(dsfapi,tag_name,data_type,test_value)    
        
        test_value=1
        result2=write_value(dsfapi,tag_name,data_type,test_value)
        
        assert 1==result1
        assert True==result2
        
    @allure_setup("DSFR-1012", is_async=False)
    def test_jira_summary_1012(self):
        summary, _ = get_jira_summary("DSFR-1012")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动写入数据准确性（STRING）"
        
        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        #测试数据
        test_value = "test_string"
        #验证
        assert 1==write_value(dsfapi,tag_name,data_type,test_value)    
        




    ################################################读取################################################

    @allure_setup("DSFR-900", is_async=False)
    def test_jira_summary_900(self):
        summary, _ = get_jira_summary("DSFR-900")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（BOOL）"
        
        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=0
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=1
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
        
        
    @allure_setup("DSFR-901", is_async=False)
    def test_jira_summary_901(self):
        summary, _ = get_jira_summary("DSFR-901")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（BYTE）"
        
        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=0
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=255
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
    
    
    
    @allure_setup("DSFR-902", is_async=False)
    def test_jira_summary_902(self):
        summary, _ = get_jira_summary("DSFR-902")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（DINT）"
        
        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        # DINT MIN (-2147483648)
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=-2147483648
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        # DINT MAX (2147483647)
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=2147483647
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
        
    
    
    @allure_setup("DSFR-903", is_async=False)
    def test_jira_summary_903(self):
        summary, _ = get_jira_summary("DSFR-903")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（DWORD）"
        
        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        # DWORD MIN (0)
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=0
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        # DWORD MAX (4294967295)
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=4294967295
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max   
    
        
        
    @allure_setup("DSFR-904", is_async=False)
    def test_jira_summary_904(self):
        summary, _ = get_jira_summary("DSFR-904")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（INT）"
        
        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        # INT MIN (-32768)
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=-32768
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        # INT MAX (32767)
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=32767
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
        
        
        
    @allure_setup("DSFR-905", is_async=False)
    def test_jira_summary_905(self):
        summary, _ = get_jira_summary("DSFR-905")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（REAL）"
        
        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        # REAL MIN (转类型的时候丢失一位小数精度-3.40282e+38)
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=-3.40282e+38
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        # REAL MAX (3.40282e+38)
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=3.40282e+38
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
        
        
        
    @allure_setup("DSFR-906", is_async=False)
    def test_jira_summary_906(self):
        summary, _ = get_jira_summary("DSFR-906")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（STRING）"
        
        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1="test_string"
        time.sleep(1)
        str1=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}10"
        test_value2="0123456789"
        time.sleep(1)
        str2=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==str1
        assert str(test_value2)==str2
        
        
        
    @allure_setup("DSFR-907", is_async=False)
    def test_jira_summary_907(self):
        summary, _ = get_jira_summary("DSFR-907")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（UINT）"
        
        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=0
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=65535
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
        
        
        
    @allure_setup("DSFR-908", is_async=False)
    def test_jira_summary_908(self):
        summary, _ = get_jira_summary("DSFR-908")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（WORD）"
        
        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=0
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=65535
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
        
        
        
    @allure_setup("DSFR-988", is_async=False)
    def test_jira_summary_988(self):
        summary, _ = get_jira_summary("DSFR-988")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（SINT）"
        
        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=-128
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=127
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
    
    
    
    @allure_setup("DSFR-989", is_async=False)
    def test_jira_summary_989(self):
        summary, _ = get_jira_summary("DSFR-989")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（USINT）"
        
        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=0
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=255
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
        
        
        
    @allure_setup("DSFR-990", is_async=False)
    def test_jira_summary_990(self):
        summary, _ = get_jira_summary("DSFR-990")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（UDINT）"
        
        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=0
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=4294967295
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
        
        
        
    @allure_setup("DSFR-991", is_async=False)
    def test_jira_summary_991(self):
        summary, _ = get_jira_summary("DSFR-991")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（LREAL）"
        
        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        # LREAL MIN (-1.79769e+308)
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=-1.79769e+308
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        # LREAL MAX (1.79769e+308)
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=1.79769e+308
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
        
        
        
    @allure_setup("DSFR-992", is_async=False)
    def test_jira_summary_992(self):
        summary, _ = get_jira_summary("DSFR-992")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（LINT）"
        
        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=-9223372036854775808
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=9223372036854775807
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
        
        
        
    @allure_setup("DSFR-993", is_async=False)
    def test_jira_summary_993(self):
        summary, _ = get_jira_summary("DSFR-993")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（ULINT）"
        
        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=0
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=18446744073709551615
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
        
        
        
    @allure_setup("DSFR-994", is_async=False)
    def test_jira_summary_994(self):
        summary, _ = get_jira_summary("DSFR-994")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证Codesys驱动采集数据准确性（LWORD）"
        
        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::{driver_name}_{data_type}"
        test_value1=1
        time.sleep(1)
        mid=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MIN"
        test_value2=0
        time.sleep(1)
        min=read_value(dsfapi, tag_name, data_type)
        
        tag_name=f"STD::{driver_name}_{data_type}_MAX"
        test_value3=18446744073709551615
        time.sleep(1)
        max=read_value(dsfapi, tag_name, data_type)
        #验证
        assert str(test_value1)==mid
        assert str(test_value2)==min
        assert str(test_value3)==max
        

    
# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfapi_codesysdrv.py"])