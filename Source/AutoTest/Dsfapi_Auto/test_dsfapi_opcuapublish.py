import os
import pytest
from asyncua import Client

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
#驱动名称
driver_name='opcuadrv'
#设备名称
device_name=f'{driver_name}_Device0'
#全局变量
dsfapi=None


#测试之前
def before_test():
    print("测试开始前执行")
    # 声明要使用全局变量
    global dsfapi

    # 加载DSFAPI动态库
    print("加载DSFAPI动态库")
    dsfapi=loadDSFAPILibrary(application_path)
    if not dsfapi : return

    # 初始化DSFAPI
    print("初始化DSFAPI")
    initDSFAPI(dsfapi)

#测试之后
def after_test():
    print("测试结束后执行")
    # 销毁
    print("销毁DSFAPI")
    dsfapi.freeDRSdkContext()

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

class TestOPCUAPUBLISH():
    @allure_setup("DSFR-1315", is_async=False)
    def test_jira_summary_1315(self):
        summary, _ = get_jira_summary("DSFR-1315")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发匿名连接"

        client = Client("opc.tcp://192.168.30.101:6000")
        assert client.uaclient is not None

    # @allure_setup("DSFR-1316", is_async=False)
    # def test_jira_summary_1316(self):
    #     summary, _ = get_jira_summary("DSFR-1316")
    #     logging.info(f"Summary:{summary}")
    #     # assert summary == "autotest验证OPC UA转发带用户名和密码连接"

    #     client = Client("opc.tcp://192.168.30.101:6000")
    #     client.set_user("opcuapublish")
    #     client.set_password("123456")
    #     assert client.uaclient is not None

    # @allure_setup("DSFR-1317", is_async=False)
    # def test_jira_summary_1317(self):
    #     summary, _ = get_jira_summary("DSFR-1317")
    #     logging.info(f"Summary:{summary}")
    #     # assert summary == "autotest验证OPC UA转发配置其他端口号连接"

    #     client = Client("opc.tcp://192.168.30.101:6100")
    #     assert client.uaclient is not None

    @allure_setup("DSFR-1318", is_async=False)
    def test_jira_summary_1318(self):
        summary, _ = get_jira_summary("DSFR-1318")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发进程是否正常运行"

        process_name="dsfopcuapublish"
        assert is_process_running(process_name)

    @allure_setup("DSFR-1319", is_async=False)
    def test_jira_summary_1319(self):
        summary, _ = get_jira_summary("DSFR-1319")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（STRING-最大长度字符串）"

        #数据类型
        data_type='STRING'
        #测试数据
        test_value='1'*255

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert test_value==actual_value

    @allure_setup("DSFR-1320", is_async=False)
    def test_jira_summary_1320(self):
        summary, _ = get_jira_summary("DSFR-1320")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（STRING-默认长度字符串）"

        #数据类型
        data_type='STRING'
        #测试数据
        test_value="test opcua publish"

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert test_value==actual_value

    @allure_setup("DSFR-1321", is_async=False)
    def test_jira_summary_1321(self):
        summary, _ = get_jira_summary("DSFR-1321")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（STRING-空字符串）"

        #数据类型
        data_type='STRING'
        #测试数据
        test_value=""

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}3"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}3"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert test_value==actual_value

    @allure_setup("DSFR-1322", is_async=False)
    def test_jira_summary_1322(self):
        summary, _ = get_jira_summary("DSFR-1322")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（INT-最大值）"

        #数据类型
        data_type='INT'
        #测试数据
        test_value=2**15-1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)
        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1323", is_async=False)
    def test_jira_summary_1323(self):
        summary, _ = get_jira_summary("DSFR-1323")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（INT-最小值）"

        #数据类型
        data_type='INT'
        #测试数据
        test_value=-2**15

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1324", is_async=False)
    def test_jira_summary_1324(self):
        summary, _ = get_jira_summary("DSFR-1324")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（UINT-最大值）"

        #数据类型
        data_type='UINT'
        #测试数据
        test_value=2**16-1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1325", is_async=False)
    def test_jira_summary_1325(self):
        summary, _ = get_jira_summary("DSFR-1325")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（UINT-最小值）"

        #数据类型
        data_type='UINT'
        #测试数据
        test_value=0

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1326", is_async=False)
    def test_jira_summary_1326(self):
        summary, _ = get_jira_summary("DSFR-1326")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（REAL-最大值）"

        #数据类型
        data_type='REAL'
        #测试数据
        test_value=3.40282e+38

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1327", is_async=False)
    def test_jira_summary_1327(self):
        summary, _ = get_jira_summary("DSFR-1327")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（REAL-最小值）"

        #数据类型
        data_type='REAL'
        #测试数据
        test_value=-3.40282e+38

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1328", is_async=False)
    def test_jira_summary_1328(self):
        summary, _ = get_jira_summary("DSFR-1328")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（UDINT-最大值）"

        #数据类型
        data_type='UDINT'
        #测试数据
        test_value=2**32-1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1329", is_async=False)
    def test_jira_summary_1329(self):
        summary, _ = get_jira_summary("DSFR-1329")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（UDINT-最小值）"

        #数据类型
        data_type='UDINT'
        #测试数据
        test_value=0

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1330", is_async=False)
    def test_jira_summary_1330(self):
        summary, _ = get_jira_summary("DSFR-1330")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（DINT-最大值）"

        #数据类型
        data_type='DINT'
        #测试数据
        test_value=2**31-1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1331", is_async=False)
    def test_jira_summary_1331(self):
        summary, _ = get_jira_summary("DSFR-1331")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（DINT-最小值）"

        #数据类型
        data_type='DINT'
        #测试数据
        test_value=-2**31

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1332", is_async=False)
    def test_jira_summary_1332(self):
        summary, _ = get_jira_summary("DSFR-1332")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（BOOL-最大值）"

        #数据类型
        data_type='BOOL'
        #测试数据
        test_value=1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1333", is_async=False)
    def test_jira_summary_1333(self):
        summary, _ = get_jira_summary("DSFR-1333")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（BOOL-最小值）"

        #数据类型
        data_type='BOOL'
        #测试数据
        test_value=0

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1334", is_async=False)
    def test_jira_summary_1334(self):
        summary, _ = get_jira_summary("DSFR-1334")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（LREAL-最大值）"

        #数据类型
        data_type='LREAL'
        #测试数据
        test_value=1.79769e+308

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1335", is_async=False)
    def test_jira_summary_1335(self):
        summary, _ = get_jira_summary("DSFR-1335")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（LREAL-最小值）"

        #数据类型
        data_type='LREAL'
        #测试数据
        test_value=-1.79769e+308

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1336", is_async=False)
    def test_jira_summary_1336(self):
        summary, _ = get_jira_summary("DSFR-1336")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（SINT-最大值）"

        #数据类型
        data_type='SINT'
        #测试数据
        test_value=2**7-1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1337", is_async=False)
    def test_jira_summary_1337(self):
        summary, _ = get_jira_summary("DSFR-1337")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（SINT-最小值）"

        #数据类型
        data_type='SINT'
        #测试数据
        test_value=-2**7

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1338", is_async=False)
    def test_jira_summary_1338(self):
        summary, _ = get_jira_summary("DSFR-1338")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（USINT-最大值）"

        #数据类型
        data_type='USINT'
        #测试数据
        test_value=2**8-1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1339", is_async=False)
    def test_jira_summary_1339(self):
        summary, _ = get_jira_summary("DSFR-1339")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（USINT-最小值）"

        #数据类型
        data_type='USINT'
        #测试数据
        test_value=0

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1340", is_async=False)
    def test_jira_summary_1340(self):
        summary, _ = get_jira_summary("DSFR-1340")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（LINT-最大值）"

        #数据类型
        data_type='LINT'
        #测试数据
        test_value=2**63-1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1341", is_async=False)
    def test_jira_summary_1341(self):
        summary, _ = get_jira_summary("DSFR-1341")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（LINT-最小值）"

        #数据类型
        data_type='LINT'
        #测试数据
        test_value=-2**63

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1342", is_async=False)
    def test_jira_summary_1342(self):
        summary, _ = get_jira_summary("DSFR-1342")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（ULINT-最大值）"

        #数据类型
        data_type='ULINT'
        #测试数据
        test_value=2**64-1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1343", is_async=False)
    def test_jira_summary_1343(self):
        summary, _ = get_jira_summary("DSFR-1343")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（ULINT-最小值）"

        #数据类型
        data_type='ULINT'
        #测试数据
        test_value=0

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1344", is_async=False)
    def test_jira_summary_1344(self):
        summary, _ = get_jira_summary("DSFR-1344")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（BYTE-最大值）"

        #数据类型
        data_type='BYTE'
        #测试数据
        test_value=2**8-1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1345", is_async=False)
    def test_jira_summary_1345(self):
        summary, _ = get_jira_summary("DSFR-1345")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（BYTE-最小值）"

        #数据类型
        data_type='BYTE'
        #测试数据
        test_value=0

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1346", is_async=False)
    def test_jira_summary_1346(self):
        summary, _ = get_jira_summary("DSFR-1346")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（WORD-最大值）"

        #数据类型
        data_type='WORD'
        #测试数据
        test_value=2**16-1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1347", is_async=False)
    def test_jira_summary_1347(self):
        summary, _ = get_jira_summary("DSFR-1347")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（WORD-最小值）"

        #数据类型
        data_type='WORD'
        #测试数据
        test_value=0

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1348", is_async=False)
    def test_jira_summary_1348(self):
        summary, _ = get_jira_summary("DSFR-1348")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（DWORD-最大值）"

        #数据类型
        data_type='DWORD'
        #测试数据
        test_value=2**32-1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
    
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1349", is_async=False)
    def test_jira_summary_1349(self):
        summary, _ = get_jira_summary("DSFR-1349")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（DWORD-最小值）"

        #数据类型
        data_type='DWORD'
        #测试数据
        test_value=0

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1350", is_async=False)
    def test_jira_summary_1350(self):
        summary, _ = get_jira_summary("DSFR-1350")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（LWORD-最大值）"

        #数据类型
        data_type='LWORD'
        #测试数据
        test_value=2**64-1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1351", is_async=False)
    def test_jira_summary_1351(self):
        summary, _ = get_jira_summary("DSFR-1351")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（LWORD-最小值）"

        #数据类型
        data_type='LWORD'
        #测试数据
        test_value=0

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1352", is_async=False)
    def test_jira_summary_1352(self):
        summary, _ = get_jira_summary("DSFR-1352")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（CHAR-最大值）"

        #数据类型
        data_type='CHAR'
        #测试数据
        test_value=2**8-1

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}1"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}1"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1353", is_async=False)
    def test_jira_summary_1353(self):
        summary, _ = get_jira_summary("DSFR-1353")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA转发数据准确性（CHAR-最小值）"

        #数据类型
        data_type='CHAR'
        #测试数据
        test_value=0

        #将数据写入
        tag_name=f"STD::OPCUAPUB_{data_type}2"
        write_value(dsfapi,tag_name,data_type,test_value)

        #数据地址
        tag_name=f"STD::STD_OPCUAPUB_{data_type}2"
        
        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfapi_opcuapublish.py"])