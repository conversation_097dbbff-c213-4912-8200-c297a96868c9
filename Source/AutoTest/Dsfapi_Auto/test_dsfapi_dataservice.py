import ctypes
import os
import logging
import subprocess
import pytest
import sys
import psutil
import time
import re
import signal

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

print("exec", __file__)
config_path = os.path.dirname(__file__)
config_file = os.path.join(config_path, "dsfapi_autotest_ds.json")
print("config_file: ", config_file)
program_path = os.path.join(config_path, "../../../executable/dsfapi_autotest")
print("program_path: ", program_path)

log_path = os.path.join(config_path, ".")  # 就用相同目录
log_lines = None

def start_program(program_path, parameters, env=None):
    command = [program_path] + parameters
    process = subprocess.Popen(command, env=env)
    print(f"程序已启动，进程号: {process.pid}")
    return process


def is_process_running(process):
    """
    检查指定进程是否仍在运行
    """
    try:
        process_status = psutil.Process(process.pid)
        if process_status.status() == psutil.STATUS_ZOMBIE:
            print(f"检测到僵尸进程 {process.pid}")
            return False
        return process_status.is_running()
    except psutil.NoSuchProcess:
        return False


def read_log_content(log_file):
    """读取日志文件内容"""
    with open(log_file, "r", encoding="utf-8") as file:
        return file.readlines()


def get_log_file(log_path, process_id):
    """使用 os.listdir 查找日志"""
    for file in os.listdir(log_path):
        if file.startswith("dsfapi_") and file.endswith(f"_{process_id}.log"):
            return os.path.join(log_path, file)

    pytest.fail(f"Log file for process {process_id} not found")


def launch_and_read_log():
    env = os.environ.copy()
    env["DSF_LOG_OUT_MODE"] = "2"
    env["DSF_LOG_OUT_PATH"] = log_path
    process = start_program(program_path, [config_file,"all"] , env=env)

    # 等待进程结束并回收
    cnt = 0
    while True:
        if is_process_running(process):
            print(f"程序仍在运行，进程号: {process.pid}")
        else:
            print(f"程序已结束，进程号: {process.pid}")
            process.wait()  # 关键点：等待子进程退出，防止僵尸进程
            # dsfprocessmgnr_process.kill()
            break
        cnt += 1
        if cnt > 30:
            print(f"程序运行异常，进程号: {process.pid}")
            exit
        time.sleep(3)
    # 读取日志
    log_file = get_log_file(log_path, process.pid)
    print("log_file: ", log_file)
    global log_lines
    log_lines = read_log_content(log_file)

    

def before_test():
    print("测试开始前执行")
    launch_and_read_log()
    

#测试之后
def after_test():
    print("测试结束后执行")

@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行





class TestDataService():
    @allure_setup("DSFR-946", is_async=False)
    def test_jira_summary_write(self):
        print("test_jira_summary_write")
        # 提取包含 read success 和 read failed 的整行日志
        success_pattern = re.compile(r":TestWrite\] tag_name\[.*?\].*?read success")
        failed_pattern = re.compile(r":TestWrite\] tag_name\[.*?\].*?read failed")

        success_lines = [line.strip() for line in log_lines if success_pattern.search(line)]
        failed_lines = [line.strip() for line in log_lines if failed_pattern.search(line)]

        if failed_lines:
            print("\n❌ Read Failed Entries:")
            for line in failed_lines:
                print(line)
            pytest.fail(f"Found read failed")

        if success_lines:
            print("✅ Read Success Entries:")
            for line in success_lines:
                print(line)
        else:
            pytest.fail(f"Not found success")


    @allure_setup("DSFR-947", is_async=False)
    def test_jira_summary_read(self):
        print("test_jira_summary_read")
        # 提取包含 read success 和 read failed 的整行日志
        success_pattern = re.compile(r":TestRead\] tag_name\[.*?\].*?read success")
        failed_pattern = re.compile(r":TestRead\] tag_name\[.*?\].*?read failed")

        success_lines = [line.strip() for line in log_lines if success_pattern.search(line)]
        failed_lines = [line.strip() for line in log_lines if failed_pattern.search(line)]

        if failed_lines:
            print("\n❌ Read Failed Entries:")
            for line in failed_lines:
                print(line)
            pytest.fail(f"Found read failed")

        if success_lines:
            print("✅ Read Success Entries:")
            for line in success_lines:
                print(line)
        else:
            pytest.fail(f"Not found success")

    @allure_setup("DSFR-948", is_async=False)
    def test_jira_summary_writetext(self):
        print("test_jira_summary_writetext")
        # 提取包含 read success 和 read failed 的整行日志
        success_pattern = re.compile(r":TestWriteText\] tag_name\[.*?\].*?read success")
        failed_pattern = re.compile(r":TestWriteText\] tag_name\[.*?\].*?read failed")

        success_lines = [line.strip() for line in log_lines if success_pattern.search(line)]
        failed_lines = [line.strip() for line in log_lines if failed_pattern.search(line)]

        if failed_lines:
            print("\n❌ Read Failed Entries:")
            for line in failed_lines:
                print(line)
            pytest.fail(f"Found read failed")

        if success_lines:
            print("✅ Read Success Entries:")
            for line in success_lines:
                print(line)
        else:
            pytest.fail(f"Not found success")

    @allure_setup("DSFR-949", is_async=False)
    def test_jira_summary_regbin(self):
        print("test_jira_summary_regbin")
        # 提取包含 read success 和 read failed 的整行日志
        success_pattern = re.compile(r":TestRegisterTag\] tag_name\[.*?\].*?read success")
        failed_pattern = re.compile(r":TestRegisterTag\] tag_name\[.*?\].*?read failed")

        success_lines = [line.strip() for line in log_lines if success_pattern.search(line)]
        failed_lines = [line.strip() for line in log_lines if failed_pattern.search(line)]

        if failed_lines:
            print("\n❌ Read Failed Entries:")
            for line in failed_lines:
                print(line)
            pytest.fail(f"Found read failed")

        if success_lines:
            print("✅ Read Success Entries:")
            for line in success_lines:
                print(line)
        else:
            pytest.fail(f"Not found success")

    @allure_setup("DSFR-950", is_async=False)
    def test_jira_summary_regjson(self):
        print("test_jira_summary_regjson")
        # 提取包含 read success 和 read failed 的整行日志
        success_pattern = re.compile(r":TestRegisterTagjson\] tag_name\[.*?\].*?read success")
        failed_pattern = re.compile(r":TestRegisterTagjson\] tag_name\[.*?\].*?read failed")

        success_lines = [line.strip() for line in log_lines if success_pattern.search(line)]
        failed_lines = [line.strip() for line in log_lines if failed_pattern.search(line)]

        if failed_lines:
            print("\n❌ Read Failed Entries:")
            for line in failed_lines:
                print(line)
            pytest.fail(f"Found read failed")

        if success_lines:
            print("✅ Read Success Entries:")
            for line in success_lines:
                print(line)
        else:
            pytest.fail(f"Not found success")
    
    @allure_setup("DSFR-951", is_async=False)
    def test_jira_summary_unreg(self):
        print("test_jira_summary_unreg")
        reg_json_finish=False
        for line in log_lines:
            if False == reg_json_finish:
                match = re.search(r'DR_Unregister_Tag_Json:res\s*=\s*(\d+)', line)
                if match:
                    res_value = int(match.group(1))
                    if res_value != 0:
                        print(f"[ERROR] res != 0 in line: {line.strip()}")
                        pytest.fail(f"DR_Register_Tag_Json res not equal 0")
                    else:
                        print(f"[OK] res == 0 in line: {line.strip()}")
                        reg_json_finish=True
            
            else:
                match = re.search(r'TestRegisterTagJson received data', line)
                if match:
                    print(f"[ERROR]DR_Register_Tag_Json received data after unregister: {line.strip()}")
                    pytest.fail(f"DR_Register_Tag_Json received data after unregister")

        if False == reg_json_finish:
            print(f"[ERROR] DR_Register_Tag_Json failed")
            pytest.fail(f"DR_Register_Tag_Json failed")
        else:
            print(f"[OK] DR_Register_Tag_Json do not received data after unregister")


    @allure_setup("DSFR-954", is_async=False)
    def test_jira_summary_write_to_acq(self):
        print("test_jira_summary_write_to_acq")
        # 和场景和write相同。写值成功就会经过采集-> driver
        success_pattern = re.compile(r":TestWrite\] tag_name\[.*?\].*?read success")
        failed_pattern = re.compile(r":TestWrite\] tag_name\[.*?\].*?read failed")

        success_lines = [line.strip() for line in log_lines if success_pattern.search(line)]
        failed_lines = [line.strip() for line in log_lines if failed_pattern.search(line)]

        if failed_lines:
            print("\n❌ Read Failed Entries:")
            for line in failed_lines:
                print(line)
            pytest.fail(f"Found read failed")

        if success_lines:
            print("✅ Read Success Entries:")
            for line in success_lines:
                print(line)
        else:
            pytest.fail(f"Not found success")

    @allure_setup("DSFR-955", is_async=False)
    def test_jira_summary_rm_change(self):
        print("test_jira_summary_rm_change")
        pytest.fail(f"To be implemented")
    
    @allure_setup("DSFR-1013", is_async=False)
    def test_jira_summary_reconnect(self):
        print("test_jira_summary_reconnect")
        get_data_before=False
        get_data_after=False
        for line in log_lines:
            if False == get_data_before:
                match = re.search(r'TestRegReconnect received data: g_bConnected=0', line)
                if match:
                    print(f"[OK] before: {line.strip()}")
                    get_data_before=True
            
            elif  False == get_data_after:
                match = re.search(r'TestRegReconnect received data: g_bConnected=1', line)
                if match:
                    print(f"[OK] after: {line.strip()}")
                    get_data_after=True

        if False == get_data_before or False == get_data_after:
            print(f"[ERROR] TagRegReconnect failed")
            pytest.fail(f"TagRegReconnect failed")

# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfapi_dataservice.py"])