import os
import pytest

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
# application_path="/home/<USER>/work/dr/"
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
#驱动名称
driver_name='melsecdrv'
#设备名称
device_name=f'{driver_name}_Device0'
#全局变量
dsfapi=None

#测试之前
def before_test():
    print("测试开始前执行")
    # 声明要使用全局变量
    global dsfapi

    # 加载DSFAPI动态库
    print("加载DSFAPI动态库")
    dsfapi=loadDSFAPILibrary(application_path)
    if not dsfapi : return

    # 初始化DSFAPI
    print("初始化DSFAPI")
    initDSFAPI(dsfapi)

#测试之后
def after_test():
    print("测试结束后执行")
    # 销毁
    print("销毁DSFAPI")
    dsfapi.freeDRSdkContext()

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

class TestMelsec():

    ################################################写入################################################

    # @allure_setup("DSFR-831", is_async=False)
    # def test_jira_summary_831(self):
    #     summary, _ = get_jira_summary("DSFR-831")
    #     # assert summary == "autotest验证Melsec驱动采集数据准确性（STRING）"
        
    #     #数据类型
    #     data_type='STRING'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}1"
    #     #测试数据
    #     test_value="hello"

    #     #验证
    #     result=write_value(dsfapi,tag_name,data_type,test_value)
    #     assert True==result

    @allure_setup("DSFR-832", is_async=False)
    def test_jira_summary_832(self):
        summary, _ = get_jira_summary("DSFR-832")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（INT）"
        
        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-2**15
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**15-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-833", is_async=False)
    def test_jira_summary_833(self):
        summary, _ = get_jira_summary("DSFR-833")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（UINT）"
        
        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**16-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-834", is_async=False)
    def test_jira_summary_834(self):
        summary, _ = get_jira_summary("DSFR-834")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（REAL）"
        
        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-3.4e38
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=3.4e38
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result
        
    @allure_setup("DSFR-835", is_async=False)
    def test_jira_summary_835(self):
        summary, _ = get_jira_summary("DSFR-835")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（BOOL）"
        
        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-837", is_async=False)
    def test_jira_summary_837(self):
        summary, _ = get_jira_summary("DSFR-837")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（UDINT）"
        
        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**32-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result
        
    @allure_setup("DSFR-838", is_async=False)
    def test_jira_summary_838(self):
        summary, _ = get_jira_summary("DSFR-838")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（DINT）"
        
        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-2**31
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**31-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-839", is_async=False)
    def test_jira_summary_839(self):
        summary, _ = get_jira_summary("DSFR-839")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（LREAL）"
        
        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-1.7e308
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=1.7e308
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # @allure_setup("DSFR-840", is_async=False)
    # def test_jira_summary_840(self):
    #     summary, _ = get_jira_summary("DSFR-840")
    #     # assert summary == "autotest验证Melsec驱动采集数据准确性（SINT）"
        
    #     #数据类型
    #     data_type='SINT'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}1"

    #     #测试数据（最小值）
    #     test_value=-2**7
    #     #验证
    #     result=write_value(dsfapi,tag_name,data_type,test_value)
    #     assert True==result

    #     #测试数据（最大值）
    #     test_value=2**7-1
    #     #验证
    #     result=write_value(dsfapi,tag_name,data_type,test_value)
    #     assert True==result
        
    # @allure_setup("DSFR-841", is_async=False)
    # def test_jira_summary_841(self):
    #     summary, _ = get_jira_summary("DSFR-841")
    #     # assert summary == "autotest验证Melsec驱动采集数据准确性（USINT）"
        
    #     #数据类型
    #     data_type='USINT'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}1"
        
    #     #测试数据（最小值）
    #     test_value=0
    #     #验证
    #     result=write_value(dsfapi,tag_name,data_type,test_value)
    #     assert True==result

    #     #测试数据（最大值）
    #     test_value=2**8-1
    #     #验证
    #     result=write_value(dsfapi,tag_name,data_type,test_value)
    #     assert True==result

    @allure_setup("DSFR-842", is_async=False)
    def test_jira_summary_842(self):
        summary, _ = get_jira_summary("DSFR-842")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（LINT）"
        
        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-2**63
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**63-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-843", is_async=False)
    def test_jira_summary_843(self):
        summary, _ = get_jira_summary("DSFR-843")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（ULINT）"
        
        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**64-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # @allure_setup("DSFR-844", is_async=False)
    # def test_jira_summary_844(self):
    #     summary, _ = get_jira_summary("DSFR-844")
    #     # assert summary == "autotest验证Melsec驱动采集数据准确性（BYTE）"
        
    #     #数据类型
    #     data_type='BYTE'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}1"

    #     #测试数据（最小值）
    #     test_value=0
    #     #验证
    #     result=write_value(dsfapi,tag_name,data_type,test_value)
    #     assert True==result

    #     #测试数据（最大值）
    #     test_value=2**8-1
    #     #验证
    #     result=write_value(dsfapi,tag_name,data_type,test_value)
    #     assert True==result

    @allure_setup("DSFR-845", is_async=False)
    def test_jira_summary_845(self):
        summary, _ = get_jira_summary("DSFR-845")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（WORD）"
        
        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**16-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-846", is_async=False)
    def test_jira_summary_846(self):
        summary, _ = get_jira_summary("DSFR-846")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（DWORD）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**32-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-847", is_async=False)
    def test_jira_summary_847(self):
        summary, _ = get_jira_summary("DSFR-847")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（LWORD）"
        
        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**64-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # @allure_setup("DSFR-848", is_async=False)
    # def test_jira_summary_848(self):
    #     summary, _ = get_jira_summary("DSFR-848")
    #     # assert summary == "autotest验证Melsec驱动采集数据准确性（CHAR）"
        
    #     #数据类型
    #     data_type='CHAR'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}1"

    #     #测试数据（最小值）
    #     test_value=0
    #     #验证
    #     result=write_value(dsfapi,tag_name,data_type,test_value)
    #     assert True==result

    #     #测试数据（最大值）
    #     test_value=2**8-1
    #     #验证
    #     result=write_value(dsfapi,tag_name,data_type,test_value)
    #     assert True==result

    # ################################################读取################################################

    # @allure_setup("DSFR-741", is_async=False)
    # def test_jira_summary_741(self):
    #     summary, _ = get_jira_summary("DSFR-741")
    #     # assert summary == "autotest验证Melsec驱动采集数据准确性（STRING）"
        
    #     #数据类型
    #     data_type='STRING'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}1"
    #     #测试数据
    #     test_value="hello"

    #     #验证
    #     actual_value=read_value(dsfapi,tag_name,data_type)
    #     assert test_value==actual_value

    @allure_setup("DSFR-742", is_async=False)
    def test_jira_summary_742(self):
        summary, _ = get_jira_summary("DSFR-742")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（INT）"
        
        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**15-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-743", is_async=False)
    def test_jira_summary_743(self):
        summary, _ = get_jira_summary("DSFR-743")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（UINT）"
        
        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**16-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-744", is_async=False)
    def test_jira_summary_744(self):
        summary, _ = get_jira_summary("DSFR-744")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（REAL）"
        
        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=3.4e38

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value
        
    @allure_setup("DSFR-745", is_async=False)
    def test_jira_summary_745(self):
        summary, _ = get_jira_summary("DSFR-745")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（BOOL）"
        
        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-746", is_async=False)
    def test_jira_summary_746(self):
        summary, _ = get_jira_summary("DSFR-746")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（UDINT）"
        
        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**32-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value
        
    @allure_setup("DSFR-747", is_async=False)
    def test_jira_summary_747(self):
        summary, _ = get_jira_summary("DSFR-747")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（DINT）"
        
        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**31-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-748", is_async=False)
    def test_jira_summary_748(self):
        summary, _ = get_jira_summary("DSFR-748")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（LREAL）"
        
        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=1.7e308

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    # @allure_setup("DSFR-749", is_async=False)
    # def test_jira_summary_749(self):
    #     summary, _ = get_jira_summary("DSFR-749")
    #     # assert summary == "autotest验证Melsec驱动采集数据准确性（SINT）"
        
    #     #数据类型
    #     data_type='SINT'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}1"
    #     #测试数据
    #     test_value=2**7-1

    #     #验证
    #     expected_value=str(test_value)
    #     actual_value=read_value(dsfapi,tag_name,data_type)
    #     assert expected_value==actual_value
        
    # @allure_setup("DSFR-750", is_async=False)
    # def test_jira_summary_750(self):
    #     summary, _ = get_jira_summary("DSFR-750")
    #     # assert summary == "autotest验证Melsec驱动采集数据准确性（USINT）"
        
    #     #数据类型
    #     data_type='USINT'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}1"
    #     #测试数据
    #     test_value=2**8-1

    #     #验证
    #     expected_value=str(test_value)
    #     actual_value=read_value(dsfapi,tag_name,data_type)
    #     assert expected_value==actual_value

    @allure_setup("DSFR-751", is_async=False)
    def test_jira_summary_751(self):
        summary, _ = get_jira_summary("DSFR-751")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（LINT）"
        
        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**63-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-752", is_async=False)
    def test_jira_summary_752(self):
        summary, _ = get_jira_summary("DSFR-752")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（ULINT）"
        
        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**64-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    # @allure_setup("DSFR-753", is_async=False)
    # def test_jira_summary_753(self):
    #     summary, _ = get_jira_summary("DSFR-753")
    #     # assert summary == "autotest验证Melsec驱动采集数据准确性（BYTE）"
        
    #     #数据类型
    #     data_type='BYTE'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}1"
    #     #测试数据
    #     test_value=2**8-1

    #     #验证
    #     expected_value=str(test_value)
    #     actual_value=read_value(dsfapi,tag_name,data_type)
    #     assert expected_value==actual_value

    @allure_setup("DSFR-754", is_async=False)
    def test_jira_summary_754(self):
        summary, _ = get_jira_summary("DSFR-754")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（WORD）"
        
        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**16-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-755", is_async=False)
    def test_jira_summary_755(self):
        summary, _ = get_jira_summary("DSFR-755")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（DWORD）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**32-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-756", is_async=False)
    def test_jira_summary_756(self):
        summary, _ = get_jira_summary("DSFR-756")
        # assert summary == "autotest验证Melsec驱动采集数据准确性（LWORD）"
        
        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**64-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    # @allure_setup("DSFR-757", is_async=False)
    # def test_jira_summary_757(self):
    #     summary, _ = get_jira_summary("DSFR-757")
    #     # assert summary == "autotest验证Melsec驱动采集数据准确性（CHAR）"
        
    #     #数据类型
    #     data_type='CHAR'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}1"
    #     #测试数据
    #     test_value=2**8-1

    #     #验证
    #     expected_value=str(test_value)
    #     actual_value=read_value(dsfapi,tag_name,data_type)
    #     assert expected_value==actual_value
    
# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfapi_melsecdrv.py"])