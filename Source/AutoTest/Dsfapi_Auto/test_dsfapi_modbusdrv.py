import os
import pytest

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
# application_path="/home/<USER>/work/dr/"
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
#驱动名称
driver_name='modbusdrv'
#设备名称
device_name=f'{driver_name}_Device0'
#全局变量
dsfapi=None

#测试之前
def before_test():
    print("测试开始前执行")
    # 声明要使用全局变量
    global dsfapi

    # 加载DSFAPI动态库
    print("加载DSFAPI动态库")
    dsfapi=loadDSFAPILibrary(application_path)
    if not dsfapi : return

    # 初始化DSFAPI
    print("初始化DSFAPI")
    initDSFAPI(dsfapi)

#测试之后
def after_test():
    print("测试结束后执行")
    # 销毁
    print("销毁DSFAPI")
    dsfapi.freeDRSdkContext()

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

class TestModbus():

    ################################################写入################################################

    @allure_setup("DSFR-958", is_async=False)
    def test_jira_summary_958(self):
        summary, _ = get_jira_summary("DSFR-958")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（STRING）"
        
        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value="hello"

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-959", is_async=False)
    def test_jira_summary_959(self):
        summary, _ = get_jira_summary("DSFR-959")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（INT）"
        
        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        
        #测试数据（最小值）
        test_value=-2**15
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**15-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-960", is_async=False)
    def test_jira_summary_960(self):
        summary, _ = get_jira_summary("DSFR-960")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（UINT）"
        
        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**16-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-961", is_async=False)
    def test_jira_summary_961(self):
        summary, _ = get_jira_summary("DSFR-961")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（REAL）"
        
        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-3.4e38
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=3.4e38
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result
        
    @allure_setup("DSFR-962", is_async=False)
    def test_jira_summary_962(self):
        summary, _ = get_jira_summary("DSFR-962")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（BOOL）"
        
        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-963", is_async=False)
    def test_jira_summary_963(self):
        summary, _ = get_jira_summary("DSFR-963")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（UDINT）"
        
        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**32-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result
        
    @allure_setup("DSFR-964", is_async=False)
    def test_jira_summary_964(self):
        summary, _ = get_jira_summary("DSFR-964")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（DINT）"
        
        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-2**31
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**31-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-965", is_async=False)
    def test_jira_summary_965(self):
        summary, _ = get_jira_summary("DSFR-965")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（LREAL）"
        
        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-1.7e308
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=1.7e308
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-966", is_async=False)
    def test_jira_summary_966(self):
        summary, _ = get_jira_summary("DSFR-966")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（SINT）"
        
        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-2**7
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**7-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result
        
    @allure_setup("DSFR-967", is_async=False)
    def test_jira_summary_967(self):
        summary, _ = get_jira_summary("DSFR-967")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（USINT）"
        
        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**8-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-968", is_async=False)
    def test_jira_summary_968(self):
        summary, _ = get_jira_summary("DSFR-968")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（LINT）"
        
        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-2**63
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**63-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-969", is_async=False)
    def test_jira_summary_969(self):
        summary, _ = get_jira_summary("DSFR-969")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（ULINT）"
        
        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**64-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-970", is_async=False)
    def test_jira_summary_970(self):
        summary, _ = get_jira_summary("DSFR-970")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（BYTE）"
        
        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**8-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-971", is_async=False)
    def test_jira_summary_971(self):
        summary, _ = get_jira_summary("DSFR-971")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（WORD）"
        
        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**16-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-972", is_async=False)
    def test_jira_summary_972(self):
        summary, _ = get_jira_summary("DSFR-972")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（DWORD）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**32-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-973", is_async=False)
    def test_jira_summary_973(self):
        summary, _ = get_jira_summary("DSFR-973")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（LWORD）"
        
        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**64-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-974", is_async=False)
    def test_jira_summary_974(self):
        summary, _ = get_jira_summary("DSFR-974")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（CHAR）"
        
        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**8-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1101", is_async=False)
    def test_jira_summary_1101(self):
        summary, _ = get_jira_summary("DSFR-1101")
        # assert summary == "autotest验证Modbus输出线圈写入数据（BOOL）"
        
        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}2"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    ################################################读取################################################

    @allure_setup("DSFR-609", is_async=False)
    def test_jira_summary_609(self):
        summary, _ = get_jira_summary("DSFR-609")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（STRING）"
        
        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value="hello"

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert test_value==actual_value

    @allure_setup("DSFR-610", is_async=False)
    def test_jira_summary_610(self):
        summary, _ = get_jira_summary("DSFR-610")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（INT）"
        
        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**15-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-611", is_async=False)
    def test_jira_summary_611(self):
        summary, _ = get_jira_summary("DSFR-611")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（UINT）"
        
        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**16-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-612", is_async=False)
    def test_jira_summary_612(self):
        summary, _ = get_jira_summary("DSFR-612")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（REAL）"
        
        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=3.4e38

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value
        
    @allure_setup("DSFR-613", is_async=False)
    def test_jira_summary_613(self):
        summary, _ = get_jira_summary("DSFR-613")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（BOOL）"
        
        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-614", is_async=False)
    def test_jira_summary_614(self):
        summary, _ = get_jira_summary("DSFR-614")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（UDINT）"
        
        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**32-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value
        
    @allure_setup("DSFR-615", is_async=False)
    def test_jira_summary_615(self):
        summary, _ = get_jira_summary("DSFR-615")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（DINT）"
        
        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**31-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-616", is_async=False)
    def test_jira_summary_616(self):
        summary, _ = get_jira_summary("DSFR-616")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（LREAL）"
        
        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=1.7e308

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-617", is_async=False)
    def test_jira_summary_617(self):
        summary, _ = get_jira_summary("DSFR-617")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（SINT）"
        
        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**7-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value
        
    @allure_setup("DSFR-618", is_async=False)
    def test_jira_summary_618(self):
        summary, _ = get_jira_summary("DSFR-618")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（USINT）"
        
        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**8-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-619", is_async=False)
    def test_jira_summary_619(self):
        summary, _ = get_jira_summary("DSFR-619")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（LINT）"
        
        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**63-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-620", is_async=False)
    def test_jira_summary_620(self):
        summary, _ = get_jira_summary("DSFR-620")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（ULINT）"
        
        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**64-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-621", is_async=False)
    def test_jira_summary_621(self):
        summary, _ = get_jira_summary("DSFR-621")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（BYTE）"
        
        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**8-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-622", is_async=False)
    def test_jira_summary_622(self):
        summary, _ = get_jira_summary("DSFR-622")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（WORD）"
        
        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**16-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-623", is_async=False)
    def test_jira_summary_619(self):
        summary, _ = get_jira_summary("DSFR-623")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（DWORD）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**32-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-624", is_async=False)
    def test_jira_summary_624(self):
        summary, _ = get_jira_summary("DSFR-624")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（LWORD）"
        
        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**64-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-625", is_async=False)
    def test_jira_summary_625(self):
        summary, _ = get_jira_summary("DSFR-625")
        # assert summary == "autotest验证Modbus驱动采集数据准确性（CHAR）"
        
        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**8-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1012", is_async=False)
    def test_jira_summary_1012(self):
        summary, _ = get_jira_summary("DSFR-1012")
        # assert summary == "autotest验证Modbus输出线圈采集数据（BOOL）"
        
        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}2"
        #测试数据
        test_value=1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value
    
    @allure_setup("DSFR-1103", is_async=False)
    def test_jira_summary_1103(self):
        summary, _ = get_jira_summary("DSFR-1103")
        # assert summary == "autotest验证Modbus输入线圈采集数据（BOOL）"
        
        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}3"
        #测试数据
        test_value=1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1104", is_async=False)
    def test_jira_summary_1104(self):
        summary, _ = get_jira_summary("DSFR-1104")
        # assert summary == "autotest验证Modbus输入寄存器采集数据（INT）"
        
        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}2"
        #测试数据
        test_value=2**15-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfapi_modbusdrv.py"])