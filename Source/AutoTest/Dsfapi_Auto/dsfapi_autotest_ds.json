{"dsf_ip": "127.0.0.1", "dsf_port": 1234, "tags": [{"name": "STD::DS_TTIME", "type": "TIME", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TDATE", "type": "DATE", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TTOD", "type": "TOD", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TDT", "type": "DT", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TLTIME", "type": "LTIME", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TDINT", "type": "DINT", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TLINT", "type": "LINT", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TULINT", "type": "ULINT", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TLREAL", "type": "LREAL", "value_orig": "1.0", "value_new": "2.0"}, {"name": "STD::DS_TBYTE", "type": "BYTE", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TCHAR", "type": "CHAR", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TSINT", "type": "SINT", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TBOOL", "type": "BOOL", "value_orig": "TRUE", "value_new": "FALSE"}, {"name": "STD::DS_TREAL", "type": "REAL", "value_orig": "1.0", "value_new": "2.0"}, {"name": "STD::DS_TUINT", "type": "UINT", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TDWORD", "type": "DWORD", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TINT", "type": "INT", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TUSINT", "type": "USINT", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TUDINT", "type": "UDINT", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TLWORD", "type": "LWORD", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TWORD", "type": "WORD", "value_orig": "1", "value_new": "2"}, {"name": "STD::DS_TSTR", "type": "STRING", "value_orig": "hello world!", "value_new": "hello bao<PERSON>!"}]}