import os
import pytest

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
# application_path="/home/<USER>/work/dr/"
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
#驱动名称
driver_name='internaldrv'
#设备名称
device_name=f'{driver_name}_Device0'
#全局变量
dsfapi=None

#测试之前
def before_test():
    print("测试开始前执行")
    # 声明要使用全局变量
    global dsfapi

    # 加载DSFAPI动态库
    print("加载DSFAPI动态库")
    dsfapi=loadDSFAPILibrary(application_path)
    if not dsfapi : return

    # 初始化DSFAPI
    print("初始化DSFAPI")
    initDSFAPI(dsfapi)

#测试之后
def after_test():
    print("测试结束后执行")
    # 销毁
    print("销毁DSFAPI")
    dsfapi.freeDRSdkContext()

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

class TestInternal():

    ################################################写入################################################

    @allure_setup("DSFR-1143", is_async=False)
    def test_jira_summary_1143(self):
        summary, _ = get_jira_summary("DSFR-1143")
        # assert summary == "autotest验证Internal驱动采集数据准确性（STRING）"
        
        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value="hello"

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1144", is_async=False)
    def test_jira_summary_1144(self):
        summary, _ = get_jira_summary("DSFR-1144")
        # assert summary == "autotest验证Internal驱动采集数据准确性（INT）"
        
        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        
        #测试数据（最小值）
        test_value=-2**15
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**15-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1145", is_async=False)
    def test_jira_summary_1145(self):
        summary, _ = get_jira_summary("DSFR-1145")
        # assert summary == "autotest验证Internal驱动采集数据准确性（UINT）"
        
        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**16-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1146", is_async=False)
    def test_jira_summary_1146(self):
        summary, _ = get_jira_summary("DSFR-1146")
        # assert summary == "autotest验证Internal驱动采集数据准确性（REAL）"
        
        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-3.4e38
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=3.4e38
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result
        
    @allure_setup("DSFR-1147", is_async=False)
    def test_jira_summary_1147(self):
        summary, _ = get_jira_summary("DSFR-1147")
        # assert summary == "autotest验证Internal驱动采集数据准确性（BOOL）"
        
        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1148", is_async=False)
    def test_jira_summary_1148(self):
        summary, _ = get_jira_summary("DSFR-1148")
        # assert summary == "autotest验证Internal驱动采集数据准确性（UDINT）"
        
        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**32-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result
        
    @allure_setup("DSFR-1149", is_async=False)
    def test_jira_summary_1149(self):
        summary, _ = get_jira_summary("DSFR-1149")
        # assert summary == "autotest验证Internal驱动采集数据准确性（DINT）"
        
        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-2**31
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**31-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1150", is_async=False)
    def test_jira_summary_1150(self):
        summary, _ = get_jira_summary("DSFR-1150")
        # assert summary == "autotest验证Internal驱动采集数据准确性（LREAL）"
        
        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-1.7e308
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=1.7e308
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1151", is_async=False)
    def test_jira_summary_1151(self):
        summary, _ = get_jira_summary("DSFR-1151")
        # assert summary == "autotest验证Internal驱动采集数据准确性（SINT）"
        
        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-2**7
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**7-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result
        
    @allure_setup("DSFR-1152", is_async=False)
    def test_jira_summary_1152(self):
        summary, _ = get_jira_summary("DSFR-1152")
        # assert summary == "autotest验证Internal驱动采集数据准确性（USINT）"
        
        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**8-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1153", is_async=False)
    def test_jira_summary_1153(self):
        summary, _ = get_jira_summary("DSFR-1153")
        # assert summary == "autotest验证Internal驱动采集数据准确性（LINT）"
        
        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=-2**63
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**63-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1154", is_async=False)
    def test_jira_summary_1154(self):
        summary, _ = get_jira_summary("DSFR-1154")
        # assert summary == "autotest验证Internal驱动采集数据准确性（ULINT）"
        
        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**64-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1155", is_async=False)
    def test_jira_summary_1155(self):
        summary, _ = get_jira_summary("DSFR-1155")
        # assert summary == "autotest验证Internal驱动采集数据准确性（BYTE）"
        
        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**8-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1156", is_async=False)
    def test_jira_summary_1156(self):
        summary, _ = get_jira_summary("DSFR-1156")
        # assert summary == "autotest验证Internal驱动采集数据准确性（WORD）"
        
        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**16-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1157", is_async=False)
    def test_jira_summary_1157(self):
        summary, _ = get_jira_summary("DSFR-1157")
        # assert summary == "autotest验证Internal驱动采集数据准确性（DWORD）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**32-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1158", is_async=False)
    def test_jira_summary_1158(self):
        summary, _ = get_jira_summary("DSFR-1158")
        # assert summary == "autotest验证Internal驱动采集数据准确性（LWORD）"
        
        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**64-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1159", is_async=False)
    def test_jira_summary_1159(self):
        summary, _ = get_jira_summary("DSFR-1159")
        # assert summary == "autotest验证Internal驱动采集数据准确性（CHAR）"
        
        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"

        #测试数据（最小值）
        test_value=0
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #测试数据（最大值）
        test_value=2**8-1
        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # @allure_setup("DSFR-1302", is_async=False)
    # def test_jira_summary_1302(self):
    #     summary, _ = get_jira_summary("DSFR-1302")
    #     # assert summary == "autotest验证Internal驱动能否正常写入数据（INT数组）"

    #     #数据类型
    #     data_type='INT'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}_ARRAY1"
    #     #测试数据
    #     test_value = [1,2,3,4,5]

    #     #验证
    #     result=False
    #     if dsfapi.connectStatus():
    #         tag_name=tag_name.upper()
    #         for i in range(0,len(test_value)):
    #             # 创建字符串
    #             name=tag_name+f'[{i}]'
    #             name_byte=name.encode('utf-8')
    #             # 创建byte
    #             data_byte = None
    #             data_byte = get_byte_by_type(test_value[i],data_type.upper())
    #             # 写值
    #             error_code=dsfapi.writeValue(name_byte,data_byte,len(data_byte))
    #             # 验证写入的值
    #             isWrited=verify_write(dsfapi,name,data_type.upper(),test_value[i])
    #             # 返回结果
    #             if error_code==0 and isWrited:
    #                 print(f"信号 {name} 写入成功!")
    #                 result=True
    #             else:
    #                 print(f"信号 {name} 写入失败!")
    #     else:
    #         print("dsfdataservice未连接")

    #     assert True==result

    @allure_setup("DSFR-1303", is_async=False)
    def test_jira_summary_1303(self):
        summary, _ = get_jira_summary("DSFR-1303")
        # assert summary == "autotest验证Internal驱动能否正常写入数据（UDT-结构体）"

        #数据类型
        data_type='UDT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value = [('INT', 2**15-1), ('BOOL', 1), ('CHAR', 1), ('DINT', 2**31-1)]

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # @allure_setup("DSFR-1304", is_async=False)
    # def test_jira_summary_1304(self):
    #     summary, _ = get_jira_summary("DSFR-1304")
    #     # assert summary == "autotest验证Internal驱动能否正常写入数据（UDT-结构体数组）"

    #     #数据类型
    #     data_type='UDT'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}2"
    #     #测试数据
    #     test_value = [('INT', 2**15-1), ('BOOL', 1), ('CHAR', 1), ('DINT', 2**31-1)]

    #     #组装数据
    #     array_size=3
    #     value=[]
    #     for i in range(0,array_size):
    #         value+=test_value

    #     result=False
    #     if dsfapi.connectStatus():
    #         name=tag_name.upper()
    #         # 创建字符串
    #         name_byte=name.encode('utf-8')
    #         # 创建byte
    #         data_byte = None
    #         data_byte = get_byte_by_type(value,data_type.upper())
    #         # 写值
    #         error_code=dsfapi.writeValue(name_byte,data_byte,len(data_byte))

    #         #分割元组
    #         types = [item[0] for item in test_value]
    #         values = [item[1] for item in test_value]

    #         time.sleep(2)
    #         #通过DSFAPI验证
    #         isWrited=True
    #         for i in range(0,array_size):
    #             struct_name=name+f"[{i}]"
    #             for j in range(0,len(types)):
    #                 actual_value=read_value(dsfapi,struct_name+f".{types[j]}",types[j]) #STD::INTERNALDRV_DEVICE0_UDT2[0].INT
    #                 # 比较
    #                 print(f"比较结构体数组中第个{i+1}结构体，第{j+1}个类型{types[j]}")
    #                 expected_value=values[j]
    #                 if types[j]!='STRING':
    #                     expected_value=str(expected_value)
    #                     actual_value=str(actual_value)
    #                 if expected_value!=actual_value:
    #                     isWrited=False
    #                     break

    #         # 返回结果
    #         if error_code==0 and isWrited:
    #             print(f"信号 {name} 写入成功!")
    #             result=True
    #         else:
    #             print(f"信号 {name} 写入失败!")
    #     else:
    #         print("dsfdataservice未连接")

    #     #验证
    #     assert True==result

    @allure_setup("DSFR-1305", is_async=False)
    def test_jira_summary_1305(self):
        summary, _ = get_jira_summary("DSFR-1305")
        # assert summary == "autotest验证Internal驱动能否正常写入数据（UDT-结构体嵌套结构体数组）"

        #数据类型
        data_type='UDT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}3"
        #测试数据
        test_value=[('INT', 2**15-1), ('BOOL', 1), ('CHAR', 1), ('DINT', 2**31-1)]

        #组装数据
        array_size=3
        value=[]
        for i in range(0,array_size):
            value+=test_value

        result=False
        if dsfapi.connectStatus():
            name=tag_name.upper()
            # 创建字符串
            name_byte=name.encode('utf-8')
            # 创建byte
            data_byte = None
            data_byte = get_byte_by_type(value,data_type.upper())
            # 写值
            error_code=dsfapi.writeValue(name_byte,data_byte,len(data_byte))

            #分割元组
            types = [item[0] for item in test_value]
            values = [item[1] for item in test_value]

            time.sleep(2)
            #通过DSFAPI验证
            isWrited=True
            for i in range(0,array_size):
                struct_name=name+f".STRUCT1[{i}]"
                for j in range(0,len(types)):
                    actual_value=read_value(dsfapi,struct_name+f".{types[j]}",types[j]) #STD::INTERNALDRV_DEVICE0_UDT3.STRUCT1[0].INT
                    # 比较
                    print(f"比较结构体数组中第个{i+1}结构体，第{j+1}个类型{types[j]}")
                    expected_value=values[j]
                    if types[j]!='STRING':
                        expected_value=str(expected_value)
                        actual_value=str(actual_value)
                    if expected_value!=actual_value:
                        isWrited=False
                        break

            # 返回结果
            if error_code==0 and isWrited:
                print(f"信号 {name} 写入成功!")
                result=True
            else:
                print(f"信号 {name} 写入失败!")
        else:
            print("dsfdataservice未连接")

        #验证
        assert True==result

    # @allure_setup("DSFR-1306", is_async=False)
    # def test_jira_summary_1306(self):
    #     summary, _ = get_jira_summary("DSFR-1306")
    #     # assert summary == "autotest验证Internal驱动能否正常写入数据（UDT-结构体嵌套INT数组）"

    #     #数据类型
    #     data_type='INT'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_UDT4"
    #     #测试数据
    #     test_value=[1,2,3,4]

    #     result=False
    #     if dsfapi.connectStatus():
    #         name=tag_name.upper()
    #         type=data_type.upper()
    #         # 创建字符串
    #         name_byte=name.encode('utf-8')
    #         # 创建byte
    #         data_byte = b''
    #         for i in range(0,len(test_value)):
    #             data_byte+=get_byte_by_type(test_value[i],type)

    #         # 写值
    #         error_code=dsfapi.writeValue(name_byte,data_byte,len(data_byte))

    #         time.sleep(2)
    #         #通过DSFAPI验证
    #         isWrited=True
    #         for i in range(0,len(test_value)):
    #             arr_name=name+f".INT_ARRAY[{i}]"
    #             actual_value=read_value(dsfapi,arr_name,type) #STD::INTERNALDRV_DEVICE0_UDT4.INT_ARRAY[0]
    #             # 比较
    #             print(f"比较结构体嵌套数组中第{i+1}个元素")
    #             expected_value=test_value[i]
    #             if type!='STRING':
    #                 expected_value=str(expected_value)
    #                 actual_value=str(actual_value)
    #             if expected_value!=actual_value:
    #                 isWrited=False
    #                 break

    #         # 返回结果
    #         if error_code==0 and isWrited:
    #             print(f"信号 {name} 写入成功!")
    #             result=True
    #         else:
    #             print(f"信号 {name} 写入失败!")
    #     else:
    #         print("dsfdataservice未连接")

    #     #验证
    #     assert True==result

    ################################################读取################################################

    @allure_setup("DSFR-1160", is_async=False)
    def test_jira_summary_1160(self):
        summary, _ = get_jira_summary("DSFR-1160")
        # assert summary == "autotest验证Internal驱动采集数据准确性（STRING）"
        
        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value="hello"

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert test_value==actual_value

    @allure_setup("DSFR-1161", is_async=False)
    def test_jira_summary_1161(self):
        summary, _ = get_jira_summary("DSFR-1161")
        # assert summary == "autotest验证Internal驱动采集数据准确性（INT）"
        
        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**15-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1162", is_async=False)
    def test_jira_summary_1162(self):
        summary, _ = get_jira_summary("DSFR-1162")
        # assert summary == "autotest验证Internal驱动采集数据准确性（UINT）"
        
        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**16-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1163", is_async=False)
    def test_jira_summary_1163(self):
        summary, _ = get_jira_summary("DSFR-1163")
        # assert summary == "autotest验证Internal驱动采集数据准确性（REAL）"
        
        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=3.4e38

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value
        
    @allure_setup("DSFR-1164", is_async=False)
    def test_jira_summary_1164(self):
        summary, _ = get_jira_summary("DSFR-1164")
        # assert summary == "autotest验证Internal驱动采集数据准确性（BOOL）"
        
        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1165", is_async=False)
    def test_jira_summary_1165(self):
        summary, _ = get_jira_summary("DSFR-1165")
        # assert summary == "autotest验证Internal驱动采集数据准确性（UDINT）"
        
        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**32-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value
        
    @allure_setup("DSFR-1166", is_async=False)
    def test_jira_summary_1166(self):
        summary, _ = get_jira_summary("DSFR-1166")
        # assert summary == "autotest验证Internal驱动采集数据准确性（DINT）"
        
        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**31-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1167", is_async=False)
    def test_jira_summary_1167(self):
        summary, _ = get_jira_summary("DSFR-1167")
        # assert summary == "autotest验证Internal驱动采集数据准确性（LREAL）"
        
        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=1.7e308

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1168", is_async=False)
    def test_jira_summary_1168(self):
        summary, _ = get_jira_summary("DSFR-1168")
        # assert summary == "autotest验证Internal驱动采集数据准确性（SINT）"
        
        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**7-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value
        
    @allure_setup("DSFR-1169", is_async=False)
    def test_jira_summary_1169(self):
        summary, _ = get_jira_summary("DSFR-1169")
        # assert summary == "autotest验证Internal驱动采集数据准确性（USINT）"
        
        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**8-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1170", is_async=False)
    def test_jira_summary_1170(self):
        summary, _ = get_jira_summary("DSFR-1170")
        # assert summary == "autotest验证Internal驱动采集数据准确性（LINT）"
        
        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**63-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1171", is_async=False)
    def test_jira_summary_1171(self):
        summary, _ = get_jira_summary("DSFR-1171")
        # assert summary == "autotest验证Internal驱动采集数据准确性（ULINT）"
        
        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**64-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1172", is_async=False)
    def test_jira_summary_1172(self):
        summary, _ = get_jira_summary("DSFR-1172")
        # assert summary == "autotest验证Internal驱动采集数据准确性（BYTE）"
        
        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**8-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1173", is_async=False)
    def test_jira_summary_1173(self):
        summary, _ = get_jira_summary("DSFR-1173")
        # assert summary == "autotest验证Internal驱动采集数据准确性（WORD）"
        
        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**16-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1174", is_async=False)
    def test_jira_summary_1174(self):
        summary, _ = get_jira_summary("DSFR-1174")
        # assert summary == "autotest验证Internal驱动采集数据准确性（DWORD）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**32-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1175", is_async=False)
    def test_jira_summary_1175(self):
        summary, _ = get_jira_summary("DSFR-1175")
        # assert summary == "autotest验证Internal驱动采集数据准确性（LWORD）"
        
        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**64-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    @allure_setup("DSFR-1176", is_async=False)
    def test_jira_summary_1176(self):
        summary, _ = get_jira_summary("DSFR-1176")
        # assert summary == "autotest验证Internal驱动采集数据准确性（CHAR）"
        
        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value=2**8-1

        #验证
        expected_value=str(test_value)
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert expected_value==actual_value

    # @allure_setup("DSFR-1307", is_async=False)
    # def test_jira_summary_1307(self):
    #     summary, _ = get_jira_summary("DSFR-1307")
    #     # assert summary == "autotest验证Internal驱动采集数据准确性（INT数组）"
        
    #     #数据类型
    #     data_type='INT'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}_ARRAY1"
    #     #测试数据
    #     test_value = [1,2,3,4,5]

    #     #验证
    #     for i in range(0,len(test_value)):
    #         actual_value=read_value(dsfapi,tag_name+f"[{i}]",data_type) #STD::INTERNALDRV_DEVICE0_INT_ARRAY1[0]
    #         # 比较
    #         # print(f"比较数组中第{i+1}个")
    #         expected_value=test_value[i]
    #         if type!='STRING':
    #             expected_value=str(expected_value)
    #             actual_value=str(actual_value)
    #         assert expected_value==actual_value

    @allure_setup("DSFR-1308", is_async=False)
    def test_jira_summary_1308(self):
        summary, _ = get_jira_summary("DSFR-1308")
        # assert summary == "autotest验证Internal驱动采集数据准确性（UDT-结构体）"
        
        #数据类型
        data_type='UDT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}1"
        #测试数据
        test_value = [('INT', 2**15-1), ('BOOL', 1), ('CHAR', 1), ('DINT', 2**31-1)]
        #分割元组
        types = [item[0] for item in test_value]
        values = [item[1] for item in test_value]

        #验证
        for i in range(0,len(types)):
            actual_value=read_value(dsfapi,tag_name+f".{types[i]}",types[i]) #STD::INTERNALDRV_DEVICE0_UDT1.INT
            # 比较
            # print(f"比较结构体中第{i+1}个类型{types[i]}")
            expected_value=values[i]
            if type!='STRING':
                expected_value=str(expected_value)
                actual_value=str(actual_value)
            assert expected_value==actual_value

    # @allure_setup("DSFR-1309", is_async=False)
    # def test_jira_summary_1309(self):
    #     summary, _ = get_jira_summary("DSFR-1309")
    #     # assert summary == "autotest验证Internal驱动采集数据准确性（UDT-结构体数组）"
        
    #     #数据类型
    #     data_type='UDT'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_{data_type}2"
    #     #测试数据
    #     test_value = [('INT', 2**15-1), ('BOOL', 1), ('CHAR', 1), ('DINT', 2**31-1)]

    #     #组装数据
    #     array_size=3
    #     value=[]
    #     for i in range(0,array_size):
    #         value+=test_value

    #     #分割元组
    #     types = [item[0] for item in test_value]
    #     values = [item[1] for item in test_value]

    #     #验证
    #     for i in range(0,array_size):
    #         struct_name=tag_name+f"[{i}]"
    #         for j in range(0,len(types)):
    #             actual_value=read_value(dsfapi,struct_name+f".{types[j]}",types[j]) #STD::INTERNALDRV_DEVICE0_UDT2[0].INT
    #             # 比较
    #             print(f"比较结构体数组中第个{i+1}结构体，第{j+1}个类型{types[j]}")
    #             expected_value=values[j]
    #             if types[j]!='STRING':
    #                 expected_value=str(expected_value)
    #                 actual_value=str(actual_value)
    #             assert expected_value==actual_value

    @allure_setup("DSFR-1310", is_async=False)
    def test_jira_summary_1310(self):
        summary, _ = get_jira_summary("DSFR-1310")
        # assert summary == "autotest验证Internal驱动采集数据准确性（UDT-结构体嵌套结构体数组）"
        
        #数据类型
        data_type='UDT'
        #数据地址
        tag_name=f"STD::{device_name}_{data_type}3"
        #测试数据
        array_size=3
        test_value=[('INT', 2**15-1), ('BOOL', 1), ('CHAR', 1), ('DINT', 2**31-1)]
        #分割元组
        types = [item[0] for item in test_value]
        values = [item[1] for item in test_value]

        #验证
        for i in range(0,array_size):
            struct_name=tag_name+f".STRUCT1[{i}]"
            for j in range(0,len(types)):
                actual_value=read_value(dsfapi,struct_name+f".{types[j]}",types[j]) #STD::INTERNALDRV_DEVICE0_UDT3.STRUCT1[0].INT
                # 比较
                print(f"比较结构体数组中第个{i+1}结构体，第{j+1}个类型{types[j]}")
                expected_value=values[j]
                if types[j]!='STRING':
                    expected_value=str(expected_value)
                    actual_value=str(actual_value)
                assert expected_value==actual_value

    # @allure_setup("DSFR-1311", is_async=False)
    # def test_jira_summary_1311(self):
    #     summary, _ = get_jira_summary("DSFR-1311")
    #     # assert summary == "autotest验证Internal驱动采集数据准确性（UDT-结构体嵌套INT数组）"
        
    #     #数据类型
    #     data_type='INT'
    #     #数据地址
    #     tag_name=f"STD::{device_name}_UDT4"
    #     #测试数据
    #     test_value=[1,2,3,4]

    #     #验证
    #     type=data_type.upper()
    #     for i in range(0,len(test_value)):
    #         arr_name=tag_name+f".INT_ARRAY[{i}]"
    #         actual_value=read_value(dsfapi,arr_name,type) #STD::INTERNALDRV_DEVICE0_UDT4.INT_ARRAY[0]
    #         # 比较
    #         print(f"比较结构体嵌套数组中第{i+1}个元素")
    #         expected_value=test_value[i]
    #         if type!='STRING':
    #             expected_value=str(expected_value)
    #             actual_value=str(actual_value)
    #         assert expected_value==actual_value

# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfapi_internaldrv.py"])