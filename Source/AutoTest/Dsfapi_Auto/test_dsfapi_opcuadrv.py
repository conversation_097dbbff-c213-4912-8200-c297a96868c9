import os
import pytest
from asyncua import Client

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
#驱动名称
driver_name='opcuadrv'
#设备名称
device_name=f'{driver_name}_Device0'
#全局变量
dsfapi=None


#测试之前
def before_test():
    print("测试开始前执行")
    # 声明要使用全局变量
    global dsfapi

    # 加载DSFAPI动态库
    print("加载DSFAPI动态库")
    dsfapi=loadDSFAPILibrary(application_path)
    if not dsfapi : return

    # 初始化DSFAPI
    print("初始化DSFAPI")
    initDSFAPI(dsfapi)

#测试之后
def after_test():
    print("测试结束后执行")
    # 销毁
    print("销毁DSFAPI")
    dsfapi.freeDRSdkContext()

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

class TestOPCUA():
    @allure_setup("DSFR-1094", is_async=False)
    def test_jira_summary_1094(self):
        summary, _ = get_jira_summary("DSFR-1094")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA驱动能否正常启动"
        
        process_name="opcuadrv"
        assert is_process_running(process_name)

    @allure_setup("DSFR-1095", is_async=False)
    def test_jira_summary_1095(self):
        summary, _ = get_jira_summary("DSFR-1095")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA驱动能否匿名连接上服务端"

        client = Client("opc.tcp://192.168.100.69:49320")
        assert client.uaclient is not None
        
    @allure_setup("DSFR-1096", is_async=False)
    def test_jira_summary_1096(self):
        summary, _ = get_jira_summary("DSFR-1096")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA驱动能否使用用户名和密码连接上服务端"

        client = Client("opc.tcp://192.168.100.69:49320")
        client.set_user("opcuadrv")
        client.set_password("123456")
        assert client.uaclient is not None

    @allure_setup("DSFR-1105", is_async=False)
    def test_jira_summary_1105(self):
        summary, _ = get_jira_summary("DSFR-1105")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证OPC UA驱动设备状态点是否正确"

        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"DSF_STATION_1.SYS::ICGDRV_DEVICE0_CONNECTSTATUS"
        #测试数据
        test_value=1

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-560", is_async=False)
    def test_jira_summary_560(self):
        summary, _ = get_jira_summary("DSFR-560")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（STRING）(同步)"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value="test opcuadrv"

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert test_value==actual_value

    @allure_setup("DSFR-561", is_async=False)
    def test_jira_summary_561(self):
        summary, _ = get_jira_summary("DSFR-561")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（INT）(同步)"

        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-562", is_async=False)
    def test_jira_summary_562(self):
        summary, _ = get_jira_summary("DSFR-562")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（UINT）(同步)"

        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-563", is_async=False)
    def test_jira_summary_563(self):
        summary, _ = get_jira_summary("DSFR-563")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（REAL）(同步)"

        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-564", is_async=False)
    def test_jira_summary_564(self):
        summary, _ = get_jira_summary("DSFR-564")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（BOOL）(同步)"

        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=1

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-634", is_async=False)
    def test_jira_summary_634(self):
        summary, _ = get_jira_summary("DSFR-634")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（UDINT）(同步)"

        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-635", is_async=False)
    def test_jira_summary_635(self):
        summary, _ = get_jira_summary("DSFR-635")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（DINT）(同步)"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-636", is_async=False)
    def test_jira_summary_636(self):
        summary, _ = get_jira_summary("DSFR-636")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（LREAL）(同步)"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-637", is_async=False)
    def test_jira_summary_637(self):
        summary, _ = get_jira_summary("DSFR-637")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（SINT）(同步)"

        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-638", is_async=False)
    def test_jira_summary_638(self):
        summary, _ = get_jira_summary("DSFR-638")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（USINT）(同步)"

        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-639", is_async=False)
    def test_jira_summary_639(self):
        summary, _ = get_jira_summary("DSFR-639")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（LINT）(同步)"

        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-640", is_async=False)
    def test_jira_summary_640(self):
        summary, _ = get_jira_summary("DSFR-640")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（ULINT）(同步)"

        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-641", is_async=False)
    def test_jira_summary_641(self):
        summary, _ = get_jira_summary("DSFR-641")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（BYTE）(同步)"

        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-642", is_async=False)
    def test_jira_summary_642(self):
        summary, _ = get_jira_summary("DSFR-642")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（WORD）(同步)"

        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-643", is_async=False)
    def test_jira_summary_643(self):
        summary, _ = get_jira_summary("DSFR-643")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（DWORD）（同步）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-644", is_async=False)
    def test_jira_summary_644(self):
        summary, _ = get_jira_summary("DSFR-644")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（LWORD）(同步)"

        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-645", is_async=False)
    def test_jira_summary_645(self):
        summary, _ = get_jira_summary("DSFR-645")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（CHAR）（同步）"

        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-781", is_async=False)
    def test_jira_summary_781(self):
        summary, _ = get_jira_summary("DSFR-781")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（STRING ARRAY）(同步)"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value="test opcuadrv"

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert test_value==actual_value

    @allure_setup("DSFR-783", is_async=False)
    def test_jira_summary_783(self):
        summary, _ = get_jira_summary("DSFR-783")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（INT ARRAY）(同步)"

        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-785", is_async=False)
    def test_jira_summary_785(self):
        summary, _ = get_jira_summary("DSFR-785")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（UINT ARRAY）(同步)"

        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-787", is_async=False)
    def test_jira_summary_787(self):
        summary, _ = get_jira_summary("DSFR-787")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（BOOL ARRAY）(同步)"

        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=1
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-789", is_async=False)
    def test_jira_summary_789(self):
        summary, _ = get_jira_summary("DSFR-789")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（REAL ARRAY）(同步)"

        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-791", is_async=False)
    def test_jira_summary_791(self):
        summary, _ = get_jira_summary("DSFR-791")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（UDINT ARRAY）(同步)"

        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-793", is_async=False)
    def test_jira_summary_793(self):
        summary, _ = get_jira_summary("DSFR-793")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（DINT ARRAY）(同步)"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-795", is_async=False)
    def test_jira_summary_795(self):
        summary, _ = get_jira_summary("DSFR-795")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（LREAL ARRAY）(同步)"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-797", is_async=False)
    def test_jira_summary_797(self):
        summary, _ = get_jira_summary("DSFR-797")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（SINT ARRAY）(同步)"

        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-799", is_async=False)
    def test_jira_summary_799(self):
        summary, _ = get_jira_summary("DSFR-799")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（USINT ARRAY）(同步)"

        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-801", is_async=False)
    def test_jira_summary_801(self):
        summary, _ = get_jira_summary("DSFR-801")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（LINT ARRAY）(同步)"

        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-803", is_async=False)
    def test_jira_summary_803(self):
        summary, _ = get_jira_summary("DSFR-803")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（ULINT ARRAY）(同步)"

        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-805", is_async=False)
    def test_jira_summary_805(self):
        summary, _ = get_jira_summary("DSFR-805")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（BYTE ARRAY）(同步)"

        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-807", is_async=False)
    def test_jira_summary_807(self):
        summary, _ = get_jira_summary("DSFR-807")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（WORD ARRAY）(同步)"

        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-809", is_async=False)
    def test_jira_summary_809(self):
        summary, _ = get_jira_summary("DSFR-809")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（DWORD ARRAY）(同步)"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-811", is_async=False)
    def test_jira_summary_811(self):
        summary, _ = get_jira_summary("DSFR-811")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（LWORD ARRAY）(同步)"

        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-813", is_async=False)
    def test_jira_summary_813(self):
        summary, _ = get_jira_summary("DSFR-813")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（CHAR ARRAY）(同步)"

        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1106", is_async=False)
    def test_jira_summary_1106(self):
        summary, _ = get_jira_summary("DSFR-1106")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（STRING DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value="test opcuadrv"

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert test_value==actual_value

    @allure_setup("DSFR-1107", is_async=False)
    def test_jira_summary_1107(self):
        summary, _ = get_jira_summary("DSFR-1107")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（INT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1108", is_async=False)
    def test_jira_summary_1108(self):
        summary, _ = get_jira_summary("DSFR-1108")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（UINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1109", is_async=False)
    def test_jira_summary_1109(self):
        summary, _ = get_jira_summary("DSFR-1109")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（BOOL DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=1

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1110", is_async=False)
    def test_jira_summary_1110(self):
        summary, _ = get_jira_summary("DSFR-1110")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（REAL DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1111", is_async=False)
    def test_jira_summary_1111(self):
        summary, _ = get_jira_summary("DSFR-1111")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（UDINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1112", is_async=False)
    def test_jira_summary_1112(self):
        summary, _ = get_jira_summary("DSFR-1112")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（DINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1113", is_async=False)
    def test_jira_summary_1113(self):
        summary, _ = get_jira_summary("DSFR-1113")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（LREAL DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1114", is_async=False)
    def test_jira_summary_1114(self):
        summary, _ = get_jira_summary("DSFR-1114")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（SINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1115", is_async=False)
    def test_jira_summary_1115(self):
        summary, _ = get_jira_summary("DSFR-1115")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（USINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1116", is_async=False)
    def test_jira_summary_1116(self):
        summary, _ = get_jira_summary("DSFR-1116")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（LINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1117", is_async=False)
    def test_jira_summary_1117(self):
        summary, _ = get_jira_summary("DSFR-1117")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（ULINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1118", is_async=False)
    def test_jira_summary_1118(self):
        summary, _ = get_jira_summary("DSFR-1118")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（BYTE DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='BYTE'
        #数据类型
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1119", is_async=False)
    def test_jira_summary_1119(self):
        summary, _ = get_jira_summary("DSFR-1119")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（WORD DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1120", is_async=False)
    def test_jira_summary_1120(self):
        summary, _ = get_jira_summary("DSFR-1120")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（DWORD DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1121", is_async=False)
    def test_jira_summary_1121(self):
        summary, _ = get_jira_summary("DSFR-1121")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（LWORD DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value

    @allure_setup("DSFR-1122", is_async=False)
    def test_jira_summary_1122(self):
        summary, _ = get_jira_summary("DSFR-1122")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动采集数据准确性（CHAR DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        actual_value=read_value(dsfapi,tag_name,data_type)
        assert str(test_value)==actual_value
        
    @allure_setup("DSFR-646", is_async=False)
    def test_jira_summary_646(self):
        summary, _ = get_jira_summary("DSFR-646")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（STRING）(同步)"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value="test opcuadrv"

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-647", is_async=False)
    def test_jira_summary_647(self):
        summary, _ = get_jira_summary("DSFR-647")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（INT）(同步)"

        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-648", is_async=False)
    def test_jira_summary_648(self):
        summary, _ = get_jira_summary("DSFR-648")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UINT）(同步)"

        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-649", is_async=False)
    def test_jira_summary_649(self):
        summary, _ = get_jira_summary("DSFR-649")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（REAL）(同步)"

        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-650", is_async=False)
    def test_jira_summary_650(self):
        summary, _ = get_jira_summary("DSFR-650")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BOOL）(同步)"

        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-651", is_async=False)
    def test_jira_summary_651(self):
        summary, _ = get_jira_summary("DSFR-651")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UDINT）(同步)"

        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-652", is_async=False)
    def test_jira_summary_652(self):
        summary, _ = get_jira_summary("DSFR-652")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DINT）(同步)"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-653", is_async=False)
    def test_jira_summary_653(self):
        summary, _ = get_jira_summary("DSFR-653")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LREAL）(同步)"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-654", is_async=False)
    def test_jira_summary_654(self):
        summary, _ = get_jira_summary("DSFR-654")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（SINT）(同步)"

        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-655", is_async=False)
    def test_jira_summary_655(self):
        summary, _ = get_jira_summary("DSFR-655")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（USINT）(同步)"

        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-656", is_async=False)
    def test_jira_summary_656(self):
        summary, _ = get_jira_summary("DSFR-656")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LINT）(同步)"

        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-657", is_async=False)
    def test_jira_summary_657(self):
        summary, _ = get_jira_summary("DSFR-657")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（ULINT）(同步)"

        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-658", is_async=False)
    def test_jira_summary_658(self):
        summary, _ = get_jira_summary("DSFR-658")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BYTE）(同步)"

        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-659", is_async=False)
    def test_jira_summary_659(self):
        summary, _ = get_jira_summary("DSFR-659")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（WORD）(同步)"

        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-660", is_async=False)
    def test_jira_summary_660(self):
        summary, _ = get_jira_summary("DSFR-660")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DWORD）（同步）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-661", is_async=False)
    def test_jira_summary_661(self):
        summary, _ = get_jira_summary("DSFR-661")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LWORD）(同步)"

        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-662", is_async=False)
    def test_jira_summary_662(self):
        summary, _ = get_jira_summary("DSFR-662")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（CHAR）（同步）"

        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-782", is_async=False)
    def test_jira_summary_782(self):
        summary, _ = get_jira_summary("DSFR-782")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（STRING ARRAY）(同步)"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value="test opcuadrv"

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-784", is_async=False)
    def test_jira_summary_784(self):
        summary, _ = get_jira_summary("DSFR-784")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（INT ARRAY）(同步)"

        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-786", is_async=False)
    def test_jira_summary_786(self):
        summary, _ = get_jira_summary("DSFR-786")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UINT ARRAY）(同步)"

        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-788", is_async=False)
    def test_jira_summary_788(self):
        summary, _ = get_jira_summary("DSFR-788")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BOOL ARRAY）(同步)"

        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-790", is_async=False)
    def test_jira_summary_790(self):
        summary, _ = get_jira_summary("DSFR-790")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（REAL ARRAY）(同步)"

        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-792", is_async=False)
    def test_jira_summary_792(self):
        summary, _ = get_jira_summary("DSFR-792")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UDINT ARRAY）(同步)"

        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-794", is_async=False)
    def test_jira_summary_794(self):
        summary, _ = get_jira_summary("DSFR-794")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DINT ARRAY）(同步)"
        
        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-796", is_async=False)
    def test_jira_summary_796(self):
        summary, _ = get_jira_summary("DSFR-796")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LREAL ARRAY）(同步)"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-798", is_async=False)
    def test_jira_summary_798(self):
        summary, _ = get_jira_summary("DSFR-798")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（SINT ARRAY）(同步)"

        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-800", is_async=False)
    def test_jira_summary_800(self):
        summary, _ = get_jira_summary("DSFR-800")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（USINT ARRAY）(同步)"

        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-802", is_async=False)
    def test_jira_summary_802(self):
        summary, _ = get_jira_summary("DSFR-802")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LINT ARRAY）(同步)"

        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-804", is_async=False)
    def test_jira_summary_804(self):
        summary, _ = get_jira_summary("DSFR-804")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（ULINT ARRAY）(同步)"

        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-806", is_async=False)
    def test_jira_summary_806(self):
        summary, _ = get_jira_summary("DSFR-806")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BYTE ARRAY）(同步)"

        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-808", is_async=False)
    def test_jira_summary_808(self):
        summary, _ = get_jira_summary("DSFR-808")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（WORD ARRAY）(同步)"

        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-810", is_async=False)
    def test_jira_summary_810(self):
        summary, _ = get_jira_summary("DSFR-810")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DWORD ARRAY）(同步)"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-812", is_async=False)
    def test_jira_summary_812(self):
        summary, _ = get_jira_summary("DSFR-812")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LWORD ARRAY）(同步)"

        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-814", is_async=False)
    def test_jira_summary_814(self):
        summary, _ = get_jira_summary("DSFR-814")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（CHAR ARRAY）(同步)"

        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1123", is_async=False)
    def test_jira_summary_1123(self):
        summary, _ = get_jira_summary("DSFR-1123")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（STRING DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value="test opcuadrv"

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1124", is_async=False)
    def test_jira_summary_1124(self):
        summary, _ = get_jira_summary("DSFR-1124")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（INT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1125", is_async=False)
    def test_jira_summary_1125(self):
        summary, _ = get_jira_summary("DSFR-1125")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1126", is_async=False)
    def test_jira_summary_1126(self):
        summary, _ = get_jira_summary("DSFR-1126")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BOOL DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1127", is_async=False)
    def test_jira_summary_1127(self):
        summary, _ = get_jira_summary("DSFR-1127")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（REAL DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1128", is_async=False)
    def test_jira_summary_1128(self):
        summary, _ = get_jira_summary("DSFR-1128")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UDINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1129", is_async=False)
    def test_jira_summary_1129(self):
        summary, _ = get_jira_summary("DSFR-1129")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1130", is_async=False)
    def test_jira_summary_1130(self):
        summary, _ = get_jira_summary("DSFR-1130")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LREAL DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1131", is_async=False)
    def test_jira_summary_1131(self):
        summary, _ = get_jira_summary("DSFR-1131")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（SINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1132", is_async=False)
    def test_jira_summary_1132(self):
        summary, _ = get_jira_summary("DSFR-1132")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（USINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1133", is_async=False)
    def test_jira_summary_1133(self):
        summary, _ = get_jira_summary("DSFR-1133")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1134", is_async=False)
    def test_jira_summary_1134(self):
        summary, _ = get_jira_summary("DSFR-1134")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（ULINT DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1135", is_async=False)
    def test_jira_summary_1135(self):
        summary, _ = get_jira_summary("DSFR-1135")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BYTE DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result
    @allure_setup("DSFR-1136", is_async=False)
    def test_jira_summary_1136(self):
        summary, _ = get_jira_summary("DSFR-1136")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（WORD DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1137", is_async=False)
    def test_jira_summary_1137(self):
        summary, _ = get_jira_summary("DSFR-1137")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DWORD DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1138", is_async=False)
    def test_jira_summary_1138(self):
        summary, _ = get_jira_summary("DSFR-1138")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LWORD DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1139", is_async=False)
    def test_jira_summary_1139(self):
        summary, _ = get_jira_summary("DSFR-1139")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（CHAR DOUBLE ARRAY）(同步)"

        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2"
        #测试数据
        test_value=100

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1181", is_async=False)
    def test_jira_summary_1181(self):
        summary, _ = get_jira_summary("DSFR-1181")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（STRING-最大长度字符串）（异步）"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value='1'*255

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1182", is_async=False)
    def test_jira_summary_1182(self):
        summary, _ = get_jira_summary("DSFR-1182")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（STRING-空字符串）（异步）"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=''

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1183", is_async=False)
    def test_jira_summary_1183(self):
        summary, _ = get_jira_summary("DSFR-1183")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（INT-最大值）（异步）"

        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=2**15-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1184", is_async=False)
    def test_jira_summary_1184(self):
        summary, _ = get_jira_summary("DSFR-1184")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（INT-最小值）（异步）"

        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=-2**15

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1185", is_async=False)
    def test_jira_summary_1185(self):
        summary, _ = get_jira_summary("DSFR-1185")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UINT-最大值）（异步）"

        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=2**16-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1186", is_async=False)
    def test_jira_summary_1186(self):
        summary, _ = get_jira_summary("DSFR-1186")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UINT-最小值）（异步）"

        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1187", is_async=False)
    def test_jira_summary_1187(self):
        summary, _ = get_jira_summary("DSFR-1187")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（REAL-最大值）（异步）"

        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=3.40282e+38

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1188", is_async=False)
    def test_jira_summary_1188(self):
        summary, _ = get_jira_summary("DSFR-1188")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（REAL-最小值）（异步）"

        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=-3.40282e+38

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1189", is_async=False)
    def test_jira_summary_1189(self):
        summary, _ = get_jira_summary("DSFR-1189")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BOOL-最大值）（异步）"

        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1190", is_async=False)
    def test_jira_summary_1190(self):
        summary, _ = get_jira_summary("DSFR-1190")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BOOL-最小值）（异步）"

        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1191", is_async=False)
    def test_jira_summary_1191(self):
        summary, _ = get_jira_summary("DSFR-1191")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UDINT-最大值）（异步）"

        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=2**32-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1192", is_async=False)
    def test_jira_summary_1192(self):
        summary, _ = get_jira_summary("DSFR-1192")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UDINT-最小值）（异步）"

        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1193", is_async=False)
    def test_jira_summary_1193(self):
        summary, _ = get_jira_summary("DSFR-1193")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DINT-最大值）（异步）"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=2**31-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1194", is_async=False)
    def test_jira_summary_1194(self):
        summary, _ = get_jira_summary("DSFR-1194")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DINT-最小值）（异步）"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=-2**31

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1195", is_async=False)
    def test_jira_summary_1195(self):
        summary, _ = get_jira_summary("DSFR-1195")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LREAL-最大值）（异步）"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=1.79769e+308

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1196", is_async=False)
    def test_jira_summary_1196(self):
        summary, _ = get_jira_summary("DSFR-1196")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LREAL-最小值）（异步）"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=-1.79769e+308

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1197", is_async=False)
    def test_jira_summary_1197(self):
        summary, _ = get_jira_summary("DSFR-1197")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（SINT-最大值）（异步）"

        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=2**7-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1198", is_async=False)
    def test_jira_summary_1198(self):
        summary, _ = get_jira_summary("DSFR-1198")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（SINT-最小值）（异步）"

        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=-2**7

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1199", is_async=False)
    def test_jira_summary_1199(self):
        summary, _ = get_jira_summary("DSFR-1199")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（USINT-最大值）（异步）"

        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=2**8-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1200", is_async=False)
    def test_jira_summary_1200(self):
        summary, _ = get_jira_summary("DSFR-1200")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（USINT-最小值）（异步）"

        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1201", is_async=False)
    def test_jira_summary_1201(self):
        summary, _ = get_jira_summary("DSFR-1201")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LINT-最大值）（异步）"

        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=2**63-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1202", is_async=False)
    def test_jira_summary_1202(self):
        summary, _ = get_jira_summary("DSFR-1202")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LINT-最小值）（异步）"

        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=-2**63

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1203", is_async=False)
    def test_jira_summary_1203(self):
        summary, _ = get_jira_summary("DSFR-1203")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（ULINT-最大值）（异步）"

        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=2**64-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1204", is_async=False)
    def test_jira_summary_1204(self):
        summary, _ = get_jira_summary("DSFR-1204")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（ULINT-最小值）（异步）"

        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1205", is_async=False)
    def test_jira_summary_1205(self):
        summary, _ = get_jira_summary("DSFR-1205")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BYTE-最大值）（异步）"

        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=2**8-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1206", is_async=False)
    def test_jira_summary_1206(self):
        summary, _ = get_jira_summary("DSFR-1206")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BYTE-最小值）（异步）"

        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1207", is_async=False)
    def test_jira_summary_1207(self):
        summary, _ = get_jira_summary("DSFR-1207")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（WORD-最大值）（异步）"

        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=2**16-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1208", is_async=False)
    def test_jira_summary_1208(self):
        summary, _ = get_jira_summary("DSFR-1208")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（WORD-最小值）（异步）"

        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1209", is_async=False)
    def test_jira_summary_1209(self):
        summary, _ = get_jira_summary("DSFR-1209")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DWORD-最大值）（异步）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=2**32-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1210", is_async=False)
    def test_jira_summary_1210(self):
        summary, _ = get_jira_summary("DSFR-1210")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DWORD-最小值）（异步）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1211", is_async=False)
    def test_jira_summary_1211(self):
        summary, _ = get_jira_summary("DSFR-1211")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LWORD-最大值）（异步）"

        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=2**64-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1212", is_async=False)
    def test_jira_summary_1212(self):
        summary, _ = get_jira_summary("DSFR-1212")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LWORD-最小值）（异步）"

        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1213", is_async=False)
    def test_jira_summary_1213(self):
        summary, _ = get_jira_summary("DSFR-1213")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（CHAR-最大值）（异步）"

        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC1"
        #测试数据
        test_value=2**8-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1214", is_async=False)
    def test_jira_summary_1214(self):
        summary, _ = get_jira_summary("DSFR-1214")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（CHAR-最小值）（异步）"

        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1215", is_async=False)
    def test_jira_summary_1215(self):
        summary, _ = get_jira_summary("DSFR-1215")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（STRING ARRAY-最长字符串）（异步）"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value='1'*255

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1216", is_async=False)
    def test_jira_summary_1216(self):
        summary, _ = get_jira_summary("DSFR-1216")
        logging.info(f"Summary:{summary}")
        # assert summary == "ddsfapi验证opcua驱动写入数据准确性（STRING ARRAY-空字符串）（异步）"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=''

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1217", is_async=False)
    def test_jira_summary_1217(self):
        summary, _ = get_jira_summary("DSFR-1217")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（INT ARRAY-最大值）（异步）"

        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=2**15-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1218", is_async=False)
    def test_jira_summary_1218(self):
        summary, _ = get_jira_summary("DSFR-1218")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（INT ARRAY-最小值）（异步）"

        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=-2**15

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1219", is_async=False)
    def test_jira_summary_1219(self):
        summary, _ = get_jira_summary("DSFR-1219")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UINT ARRAY-最大值）（异步）"

        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=2**16-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1220", is_async=False)
    def test_jira_summary_1220(self):
        summary, _ = get_jira_summary("DSFR-1220")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UINT ARRAY-最小值）（异步）"

        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1221", is_async=False)
    def test_jira_summary_1221(self):
        summary, _ = get_jira_summary("DSFR-1221")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（REAL ARRAY-最大值）（异步）"

        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=3.40282e+38

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1222", is_async=False)
    def test_jira_summary_1222(self):
        summary, _ = get_jira_summary("DSFR-1222")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（REAL ARRAY-最小值）（异步）"

        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=-3.40282e+38

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1223", is_async=False)
    def test_jira_summary_1223(self):
        summary, _ = get_jira_summary("DSFR-1223")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BOOL ARRAY-最大值）（异步）"

        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1224", is_async=False)
    def test_jira_summary_1224(self):
        summary, _ = get_jira_summary("DSFR-1224")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BOOL ARRAY-最小值）（异步）"

        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1225", is_async=False)
    def test_jira_summary_1225(self):
        summary, _ = get_jira_summary("DSFR-1225")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UDINT ARRAY-最大值）（异步）"

        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=2**32-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1226", is_async=False)
    def test_jira_summary_1226(self):
        summary, _ = get_jira_summary("DSFR-1226")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UDINT ARRAY-最小值）（异步）"

        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1227", is_async=False)
    def test_jira_summary_1227(self):
        summary, _ = get_jira_summary("DSFR-1227")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DINT ARRAY-最大值）（异步）"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=2**31-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1228", is_async=False)
    def test_jira_summary_1228(self):
        summary, _ = get_jira_summary("DSFR-1228")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DINT ARRAY-最小值）（异步）"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=-2**31

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1229", is_async=False)
    def test_jira_summary_1229(self):
        summary, _ = get_jira_summary("DSFR-1229")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LREAl ARRAY-最大值）（异步）"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=1.79769e+308

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1230", is_async=False)
    def test_jira_summary_1230(self):
        summary, _ = get_jira_summary("DSFR-1230")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LREAl ARRAY-最小值）（异步）"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=-1.79769e+308

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1231", is_async=False)
    def test_jira_summary_1231(self):
        summary, _ = get_jira_summary("DSFR-1231")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（SINT ARRAY-最大值）（异步）"

        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=2**7-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1232", is_async=False)
    def test_jira_summary_1232(self):
        summary, _ = get_jira_summary("DSFR-1232")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（SINT ARRAY-最小值）（异步）"

        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=-2**7

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1233", is_async=False)
    def test_jira_summary_1233(self):
        summary, _ = get_jira_summary("DSFR-1233")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（USINT ARRAY-最大值）（异步）"

        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=2**8-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1234", is_async=False)
    def test_jira_summary_1234(self):
        summary, _ = get_jira_summary("DSFR-1234")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（USINT ARRAY-最小值）（异步）"

        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1235", is_async=False)
    def test_jira_summary_1235(self):
        summary, _ = get_jira_summary("DSFR-1235")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LINT ARRAY-最大值）（异步）"

        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=2**63-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1236", is_async=False)
    def test_jira_summary_1236(self):
        summary, _ = get_jira_summary("DSFR-1236")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LINT ARRAY-最小值）（异步）"

        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=-2**63

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1237", is_async=False)
    def test_jira_summary_1237(self):
        summary, _ = get_jira_summary("DSFR-1237")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（ULINT ARRAY-最大值）（异步）"

        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=2**64-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1238", is_async=False)
    def test_jira_summary_1238(self):
        summary, _ = get_jira_summary("DSFR-1238")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（ULINT ARRAY-最小值）（异步）"

        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1239", is_async=False)
    def test_jira_summary_1239(self):
        summary, _ = get_jira_summary("DSFR-1239")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BYTE ARRAY-最大值）（异步）"

        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=2**8-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1240", is_async=False)
    def test_jira_summary_1240(self):
        summary, _ = get_jira_summary("DSFR-1240")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BYTE ARRAY-最小值）（异步）"

        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1241", is_async=False)
    def test_jira_summary_1241(self):
        summary, _ = get_jira_summary("DSFR-1241")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（WORD ARRAY-最大值）（异步）"

        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=2**16-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1242", is_async=False)
    def test_jira_summary_1242(self):
        summary, _ = get_jira_summary("DSFR-1242")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（WORD ARRAY-最小值）（异步）"

        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1243", is_async=False)
    def test_jira_summary_1243(self):
        summary, _ = get_jira_summary("DSFR-1243")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DWORD ARRAY-最大值）（异步）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=2**32-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1244", is_async=False)
    def test_jira_summary_1244(self):
        summary, _ = get_jira_summary("DSFR-1244")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DWORD ARRAY-最小值）（异步）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1245", is_async=False)
    def test_jira_summary_1245(self):
        summary, _ = get_jira_summary("DSFR-1245")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LWORD ARRAY-最大值）（异步）"

        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=2**64-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1246", is_async=False)
    def test_jira_summary_1246(self):
        summary, _ = get_jira_summary("DSFR-1246")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LWORD ARRAY-最小值）（异步）"

        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1247", is_async=False)
    def test_jira_summary_1247(self):
        summary, _ = get_jira_summary("DSFR-1247")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（CHAR ARRAY-最大值）（异步）"

        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC1"
        #测试数据
        test_value=2**8-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1248", is_async=False)
    def test_jira_summary_1248(self):
        summary, _ = get_jira_summary("DSFR-1248")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（CHAR ARRAY-最小值）（异步）"

        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY1_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1249", is_async=False)
    def test_jira_summary_1249(self):
        summary, _ = get_jira_summary("DSFR-1249")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（STRING DOUBLE ARRAY-最长字符串）（异步）"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value='1'*255

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1250", is_async=False)
    def test_jira_summary_1250(self):
        summary, _ = get_jira_summary("DSFR-1250")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（STRING DOUBLE ARRAY-空字符串）（异步）"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=''

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1251", is_async=False)
    def test_jira_summary_1251(self):
        summary, _ = get_jira_summary("DSFR-1251")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（INT DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=2**15-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1252", is_async=False)
    def test_jira_summary_1252(self):
        summary, _ = get_jira_summary("DSFR-1252")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（INT DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='INT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=-2**15

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1253", is_async=False)
    def test_jira_summary_1253(self):
        summary, _ = get_jira_summary("DSFR-1253")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UINT DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=2**16-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1254", is_async=False)
    def test_jira_summary_1254(self):
        summary, _ = get_jira_summary("DSFR-1254")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UINT DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='UINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1255", is_async=False)
    def test_jira_summary_1255(self):
        summary, _ = get_jira_summary("DSFR-1255")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（REAL DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=3.40282e+38

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1256", is_async=False)
    def test_jira_summary_1256(self):
        summary, _ = get_jira_summary("DSFR-1256")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（REAL DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='REAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=-3.40282e+38

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1257", is_async=False)
    def test_jira_summary_1257(self):
        summary, _ = get_jira_summary("DSFR-1257")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BOOL DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1258", is_async=False)
    def test_jira_summary_1258(self):
        summary, _ = get_jira_summary("DSFR-1258")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BOOL DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='BOOL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1259", is_async=False)
    def test_jira_summary_1259(self):
        summary, _ = get_jira_summary("DSFR-1259")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UDINT DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=2**32-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1260", is_async=False)
    def test_jira_summary_1260(self):
        summary, _ = get_jira_summary("DSFR-1260")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（UDINT DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='UDINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1261", is_async=False)
    def test_jira_summary_1261(self):
        summary, _ = get_jira_summary("DSFR-1261")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DINT DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=2**31-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1262", is_async=False)
    def test_jira_summary_1262(self):
        summary, _ = get_jira_summary("DSFR-1262")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DINT DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=-2**31

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1263", is_async=False)
    def test_jira_summary_1263(self):
        summary, _ = get_jira_summary("DSFR-1263")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LREAL DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=1.79769e+308

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1264", is_async=False)
    def test_jira_summary_1264(self):
        summary, _ = get_jira_summary("DSFR-1264")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LREAL DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=-1.79769e+308

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1265", is_async=False)
    def test_jira_summary_1265(self):
        summary, _ = get_jira_summary("DSFR-1265")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（SINT DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=2**7-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1266", is_async=False)
    def test_jira_summary_1266(self):
        summary, _ = get_jira_summary("DSFR-1266")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（SINT DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='SINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=-2**7

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1267", is_async=False)
    def test_jira_summary_1267(self):
        summary, _ = get_jira_summary("DSFR-1267")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（USINT DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=2**8-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1268", is_async=False)
    def test_jira_summary_1268(self):
        summary, _ = get_jira_summary("DSFR-1268")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（USINT DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='USINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1269", is_async=False)
    def test_jira_summary_1269(self):
        summary, _ = get_jira_summary("DSFR-1269")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LINT DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=2**63-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1270", is_async=False)
    def test_jira_summary_1270(self):
        summary, _ = get_jira_summary("DSFR-1270")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LINT DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='LINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=-2**63

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1271", is_async=False)
    def test_jira_summary_1271(self):
        summary, _ = get_jira_summary("DSFR-1271")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（ULINT DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=2**64-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1272", is_async=False)
    def test_jira_summary_1272(self):
        summary, _ = get_jira_summary("DSFR-1272")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（ULINT DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='ULINT'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1273", is_async=False)
    def test_jira_summary_1273(self):
        summary, _ = get_jira_summary("DSFR-1273")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BYTE DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=2**8-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1274", is_async=False)
    def test_jira_summary_1274(self):
        summary, _ = get_jira_summary("DSFR-1274")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（BYTE DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1275", is_async=False)
    def test_jira_summary_1275(self):
        summary, _ = get_jira_summary("DSFR-1275")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（WORD DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=2**16-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1276", is_async=False)
    def test_jira_summary_1276(self):
        summary, _ = get_jira_summary("DSFR-1276")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（WORD DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='WORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1277", is_async=False)
    def test_jira_summary_1277(self):
        summary, _ = get_jira_summary("DSFR-1277")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DWORD DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=2**32-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1278", is_async=False)
    def test_jira_summary_1278(self):
        summary, _ = get_jira_summary("DSFR-1278")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（DWORD DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='DWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1279", is_async=False)
    def test_jira_summary_1279(self):
        summary, _ = get_jira_summary("DSFR-1279")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LWORD DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=2**64-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1280", is_async=False)
    def test_jira_summary_1280(self):
        summary, _ = get_jira_summary("DSFR-1280")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（LWORD DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='LWORD'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1281", is_async=False)
    def test_jira_summary_1281(self):
        summary, _ = get_jira_summary("DSFR-1281")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（CHAR DOUBLE ARRAY-最大值）（异步）"

        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC1"
        #测试数据
        test_value=2**8-1

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    @allure_setup("DSFR-1282", is_async=False)
    def test_jira_summary_1282(self):
        summary, _ = get_jira_summary("DSFR-1282")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证opcua驱动写入数据准确性（CHAR DOUBLE ARRAY-最小值）（异步）"

        #数据类型
        data_type='CHAR'
        #数据地址
        tag_name=f"STD::OPCUA_{data_type}_ARRAY2_ASYNC2"
        #测试数据
        test_value=0

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfapi_opcuadrv.py"])