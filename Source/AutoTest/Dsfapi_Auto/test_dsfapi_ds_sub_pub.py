import os
import pytest
import time
# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
# application_path="/home/<USER>/work/dr/"
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
#全局变量
dsfapi=None

#测试之前
def before_test():
    print("测试开始前执行")
    # 声明要使用全局变量
    global dsfapi

    # 加载DSFAPI动态库
    print("加载DSFAPI动态库")
    dsfapi=loadDSFAPILibrary(application_path)
    if not dsfapi : return

    # 初始化DSFAPI
    print("初始化DSFAPI")
    initDSFAPI(dsfapi)

#测试之后
def after_test():
    print("测试结束后执行")
    # 销毁
    print("销毁DSFAPI")
    dsfapi.freeDRSdkContext()

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

class TestDsDDS():
    @allure_setup("DSFR-952", is_async=False)
    def test_jira_summary_publish(self):
        logging.info("test_jira_summary_publish")
        """
        在PLC中配置了GVS_DS_DDS ,内部一个变量 DS_DDS_DINT. 
        在DSF中导入了GVS_DS_DDS中的变量DS_DDS_DINT
        在DSF同时新增一个表量表var_ds_dds,新增变量DS_DDS_DINT1 ,其地址也是DS_DDS_DINT。
        在DSF使用 DS_DDS_PUB  发布 DS_DDS_DINT1
        在PLC中订阅了  NGVS_DS_DDS_SUB, 使得PLC 有了变量DS_DDS_DINT1
        在DSF同时表量表var_ds_dds,新增变量DS_DDS_DINT1_1 ,其地址也是DS_DDS_DINT1。

        这样 DDS 发布变量 DS_DDS_DINT1 到 PLC,然后通过驱动采集上来成为 DS_DDS_DINT1_1
        通过dsfapi 查询 DS_DDS_DINT1_1 成员变量的值是否准确。
        
        """
        data_type='DINT'
        tag_name=f"STD::DS_DDS_DINT1_1"
        expect_value=1
        value=read_value(dsfapi, tag_name, data_type)
        assert str(expect_value)==value



    @allure_setup("DSFR-953", is_async=False)
    def test_jira_summary_subscribe(self):
        logging.info("test_jira_summary_subscribe")

        """
        在PLC中配置了UTD_DS_1 (三个基本类型), UTD_DS_2（嵌套包括了 UTD_DS_1成员 ,int ,real array[5]),
        在PLC用 NGVS_DS_S2 发布一个UDT_DS_2类型的变量 STD.DS2
        在DSF中 订阅了  NGVS_DS_S2_R
        
        通过dsfapi 查询  STD.DS2成员变量的值是否准确。
        """

        # 嵌套结构体内的成员
        data_type='DINT'
        tag_name=f"STD::DS2.DS_1_DATA.TDINT"
        expect_value=1
        value=read_value(dsfapi, tag_name, data_type)
        assert str(expect_value)==value

        data_type='LREAL'
        tag_name=f"STD::DS2.DS_1_DATA.TLREAL"
        expect_value=1
        value=read_value(dsfapi, tag_name, data_type)
        assert str(expect_value)==value

        data_type='STRING'
        tag_name=f"STD::DS2.DS_1_DATA.TSTRING"
        expect_value="dds publish"
        value=read_value(dsfapi, tag_name, data_type)
        assert str(expect_value)==value

         # 基本类型成员
        data_type='INT'
        tag_name=f"STD::DS2.INT_DATA"
        expect_value=1
        value=read_value(dsfapi, tag_name, data_type)
        assert str(expect_value)==value

         # 基本类型数组
        data_type='REAL'
        tag_name=f"STD::DS2.REAL_ARRAY[1]"
        expect_value=1
        value=read_value(dsfapi, tag_name, data_type)
        assert str(expect_value)==value

        data_type='REAL'
        tag_name=f"STD::DS2.REAL_ARRAY[2]"
        expect_value=2
        value=read_value(dsfapi, tag_name, data_type)
        assert str(expect_value)==value


    
# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfapi_ds_dds.py"])