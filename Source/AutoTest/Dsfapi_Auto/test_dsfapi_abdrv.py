import os
import pytest

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
#驱动名称
driver_name='AB'
#全局变量
dsfapi=None
#设备名称
#测试之前
def before_test():
    print("测试开始前执行")
    # 声明要使用全局变量
    global dsfapi

    # 加载DSFAPI动态库
    print("加载DSFAPI动态库")
    dsfapi=loadDSFAPILibrary(application_path)
    if not dsfapi : return

    # 初始化DSFAPI
    print("初始化DSFAPI")
    initDSFAPI(dsfapi)

#测试之后
def after_test():
    print("测试结束后执行")
    # 销毁
    print("销毁DSFAPI")
    dsfapi.freeDRSdkContext()

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

#每个类型读取测试测试用例有三个，读中间值（1），读最小值，读最大值
class TestAB():

    # BOOL测试
    @allure_setup("DSFR-1179", is_async=False)
    def test_jira_summary_bool(self):
        summary, _ = get_jira_summary("DSFR-1179")
        logging.info(f"Summary:{summary}")
        
        data_type='BOOL'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

        # BOOL MIN (0)
        data_type='BOOL'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # BOOL MAX (1)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=1
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    #DINT测试 
    @allure_setup("DSFR-1286", is_async=False)
    def test_jira_summary_dint(self):
        summary, _ = get_jira_summary("DSFR-1286")
        logging.info(f"Summary:{summary}")
        
        data_type='DINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # DINT MIN (-2147483648)
        data_type='DINT'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=-2147483648
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # DINT MAX (2147483647)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=2147483647
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    # LINT测试
    @allure_setup("DSFR-1288", is_async=False)
    def test_jira_summary_lint(self):
        summary, _ = get_jira_summary("DSFR-1288")
        logging.info(f"Summary:{summary}")
        
        data_type='LINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # LINT MIN (-9223372036854775808)
        data_type='LINT'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=-9223372036854775808
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # LINT MAX (9223372036854775807)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=9223372036854775807
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    # SINT测试
    @allure_setup("DSFR-1283", is_async=False)
    def test_jira_summary_sint(self):
        summary, _ = get_jira_summary("DSFR-1283")
        logging.info(f"Summary:{summary}")
        
        data_type='SINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # SINT MIN (-128)
        data_type='SINT'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=-128
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # SINT MAX (127)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=127
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

        
    # REAL测试
    @allure_setup("DSFR-1285", is_async=False)
    def test_jira_summary_real(self):
        summary, _ = get_jira_summary("DSFR-1285")
        logging.info(f"Summary:{summary}")
        
        data_type='REAL'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
        # REAL MIN (转类型的时候丢失一位小数精度-3.40282e+38)
        data_type='REAL'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=-3.40282e+38
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # REAL MAX (3.40282e+38)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=3.40282e+38
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
     
    # INT测试
    @allure_setup("DSFR-1284", is_async=False)
    def test_jira_summary_int(self):
        summary, _ = get_jira_summary("DSFR-1284")
        logging.info(f"Summary:{summary}")
        
        data_type='INT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # INT MIN (-32768)
        data_type='INT'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=-32768
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # INT MAX (32767)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=32767
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    #字符串
    @allure_setup("DSFR-1289", is_async=False)
    def test_jira_summary_str(self):
        summary, _ = get_jira_summary("DSFR-1289")
        logging.info(f"Summary:{summary}")
        
        data_type='STRING'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value="hello world!"

        assert test_value==read_value(dsfapi,tag_name,data_type)

        #STRMAX(82)
        data_type='STRING'
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value="1" * 82

        assert test_value==read_value(dsfapi,tag_name,data_type)

    
    #空字符串
    @allure_setup("DSFR-1357", is_async=False)
    def test_jira_summary_strmin(self): 
        summary, _ = get_jira_summary("DSFR-1357")
        logging.info(f"Summary:{summary}")
        
        data_type='STRING'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=""

        assert test_value==read_value(dsfapi,tag_name,data_type)



    # LINT写入测试
    @allure_setup("DSFR-1297", is_async=False)
    def test_jira_summary_writelint(self):
        summary, _ = get_jira_summary("DSFR-1297")
        logging.info(f"Summary:{summary}")
        
        data_type='LINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=9223372036854775807

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    

    # SINT写入测试
    @allure_setup("DSFR-1292", is_async=False)
    def test_jira_summary_writesint(self):
        summary, _ = get_jira_summary("DSFR-1292")
        logging.info(f"Summary:{summary}")
        
        data_type='SINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=127

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # BOOL写入测试
    @allure_setup("DSFR-1291", is_async=False)
    def test_jira_summary_writebool(self):
        summary, _ = get_jira_summary("DSFR-1291")
        logging.info(f"Summary:{summary}")
        
        data_type='BOOL'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1  # BOOL最大值就是1

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        # 因为最大值就是1，所以这里写入0然后再写回1
        test_value=0
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result
        
        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # REAL写入测试
    @allure_setup("DSFR-1294", is_async=False)
    def test_jira_summary_writereal(self):
        summary, _ = get_jira_summary("DSFR-1294")
        logging.info(f"Summary:{summary}")
        
        data_type='REAL'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=3.40282e+38

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result


    # INT写入测试
    @allure_setup("DSFR-1293", is_async=False)
    def test_jira_summary_writeint(self):
        summary, _ = get_jira_summary("DSFR-1293")
        logging.info(f"Summary:{summary}")
        
        data_type='INT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=32767

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    


    # STRING写入测试（字符串）
    @allure_setup("DSFR-1298", is_async=False)
    def test_jira_summary_writestr(self):
        summary, _ = get_jira_summary("DSFR-1298")
        logging.info(f"Summary:{summary}")

        data_type='STRING'
        tag_name=f"STD::{driver_name}{data_type}"

        #string前端默认长度是80，写不下82个字符
        test_value="1"*80
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #写回
        test_value="hello world!"
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # 空字符串写入测试
    @allure_setup("DSFR-1358", is_async=False)
    def test_jira_summary_writestrmin(self):
        summary, _ = get_jira_summary("DSFR-1358")
        logging.info(f"Summary:{summary}")
        
        data_type='STRING'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=""

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result
        #写回
        test_value="hello world!"
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result



    #系统状态点读取测试
    @allure_setup("DSFR-1360", is_async=False)
    def test_jira_summary_connectstatus(self):
        summary, _ = get_jira_summary("DSFR-1360")
        logging.info(f"Summary:{summary}")
        
        data_type='BYTE'
        tag_name=f"DSF_STATION_1.SYS::ABDRV_DEVICE0_CONNECTSTATUS"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)


# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfapi_abdrv.py"])