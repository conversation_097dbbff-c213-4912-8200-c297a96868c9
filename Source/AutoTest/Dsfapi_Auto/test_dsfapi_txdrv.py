import os
import pytest

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
# application_path="/home/<USER>/work/dr/"
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
#驱动名称
driver_name='T'
#全局变量
dsfapi=None
#设备名称
#测试之前
def before_test():
    print("测试开始前执行")
    # 声明要使用全局变量
    global dsfapi

    # 加载DSFAPI动态库
    print("加载DSFAPI动态库")
    dsfapi=loadDSFAPILibrary(application_path)
    if not dsfapi : return

    # 初始化DSFAPI
    print("初始化DSFAPI")
    initDSFAPI(dsfapi)

#测试之后
def after_test():
    print("测试结束后执行")
    # 销毁
    print("销毁DSFAPI")
    dsfapi.freeDRSdkContext()

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

#每个类型读取测试测试用例有三个，读中间值（1），读最小值，读最大值
class TestTx():
    #DINT测试 
    @allure_setup("DSFR-544", is_async=False)
    def test_jira_summary_dint(self):
        summary, _ = get_jira_summary("DSFR-544")
        logging.info(f"Summary:{summary}")
        
        data_type='DINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # DINT MIN (-2147483648)
        data_type='DINT'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=-2147483648
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # DINT MAX (2147483647)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=2147483647
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    
    
    # LINT测试
    @allure_setup("DSFR-545", is_async=False)
    def test_jira_summary_lint(self):
        summary, _ = get_jira_summary("DSFR-545")
        logging.info(f"Summary:{summary}")
        
        data_type='LINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # LINT MIN (-9223372036854775808)
        data_type='LINT'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=-9223372036854775808
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # LINT MAX (9223372036854775807)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=9223372036854775807
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    # ULINT测试
    @allure_setup("DSFR-546", is_async=False)
    def test_jira_summary_ulint(self):
        summary, _ = get_jira_summary("DSFR-546")
        logging.info(f"Summary:{summary}")
        
        data_type='ULINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # ULINT MIN (0)
        data_type='ULINT'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # ULINT MAX (18446744073709551615)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=18446744073709551615
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    
    # LREAL测试
    @allure_setup("DSFR-547", is_async=False)
    def test_jira_summary_lreal(self):
        summary, _ = get_jira_summary("DSFR-547")
        logging.info(f"Summary:{summary}")
        
        data_type='LREAL'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # LREAL MIN (-2.22507e-308)
        data_type='LREAL'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=-2.22507e-308
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # LREAL MAX (1.79769e+308)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=1.79769e+308
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    
    
    # BYTE测试
    @allure_setup("DSFR-548", is_async=False)
    def test_jira_summary_byte(self):
        summary, _ = get_jira_summary("DSFR-548")
        logging.info(f"Summary:{summary}")
        
        data_type='BYTE'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # BYTE MIN (封装的时候转为uint8，所以为0)
        data_type='BYTE'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # BYTE MAX (封装的时候转为uint8，所以为255)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=255
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    # CHAR测试
    @allure_setup("DSFR-549", is_async=False)
    def test_jira_summary_char(self):
        summary, _ = get_jira_summary("DSFR-549")
        logging.info(f"Summary:{summary}")
        
        data_type='CHAR'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=49  # '1'的ASCII码

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=255

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    
    # SINT测试
    @allure_setup("DSFR-550", is_async=False)
    def test_jira_summary_sint(self):
        summary, _ = get_jira_summary("DSFR-550")
        logging.info(f"Summary:{summary}")
        
        data_type='SINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # SINT MIN (-128)
        data_type='SINT'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=-128
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # SINT MAX (127)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=127
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    
    
    # BOOL测试
    @allure_setup("DSFR-551", is_async=False)
    def test_jira_summary_bool(self):
        summary, _ = get_jira_summary("DSFR-551")
        logging.info(f"Summary:{summary}")
        
        data_type='BOOL'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

        # BOOL MIN (0)
        data_type='BOOL'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # BOOL MAX (1)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=1
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    # REAL测试
    @allure_setup("DSFR-552", is_async=False)
    def test_jira_summary_real(self):
        summary, _ = get_jira_summary("DSFR-552")
        logging.info(f"Summary:{summary}")
        
        data_type='REAL'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
        # REAL MIN (转类型的时候丢失一位小数精度-3.40282e+38)
        data_type='REAL'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=-3.40282e+38
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # REAL MAX (3.40282e+38)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=3.40282e+38
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    # UINT测试
    @allure_setup("DSFR-553", is_async=False)
    def test_jira_summary_uint(self):
        summary, _ = get_jira_summary("DSFR-553")
        logging.info(f"Summary:{summary}")
        
        data_type='UINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # UINT MIN (0)
        data_type='UINT'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # UINT MAX (65535)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=65535
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    
    # DWORD测试
    @allure_setup("DSFR-554", is_async=False)
    def test_jira_summary_dword(self):
        summary, _ = get_jira_summary("DSFR-554")
        logging.info(f"Summary:{summary}")
        
        data_type='DWORD'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # DWORD MIN (0)
        data_type='DWORD'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # DWORD MAX (4294967295)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=4294967295
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    
    
    # INT测试
    @allure_setup("DSFR-555", is_async=False)
    def test_jira_summary_int(self):
        summary, _ = get_jira_summary("DSFR-555")
        logging.info(f"Summary:{summary}")
        
        data_type='INT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # INT MIN (-32768)
        data_type='INT'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=-32768
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # INT MAX (32767)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=32767
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    
    
    # USINT测试
    @allure_setup("DSFR-556", is_async=False)
    def test_jira_summary_usint(self):
        summary, _ = get_jira_summary("DSFR-556")
        logging.info(f"Summary:{summary}")
        
        data_type='USINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # USINT MIN (0)
        data_type='USINT'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # USINT MAX (255)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=255
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    
    
    # UDINT测试
    @allure_setup("DSFR-557", is_async=False)
    def test_jira_summary_udint(self):
        summary, _ = get_jira_summary("DSFR-557")
        logging.info(f"Summary:{summary}")
        
        data_type='UDINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # UDINT MIN (0)
        data_type='UDINT'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # UDINT MAX (4294967295)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=4294967295
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    # LWORD测试
    @allure_setup("DSFR-558", is_async=False)
    def test_jira_summary_lword(self):
        summary, _ = get_jira_summary("DSFR-558")
        logging.info(f"Summary:{summary}")
        
        data_type='LWORD'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
        # LWORD MIN (0)
        data_type='LWORD'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # LWORD MAX (18446744073709551615)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=18446744073709551615
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    
    
    # WORD测试
    @allure_setup("DSFR-559", is_async=False)
    def test_jira_summary_word(self):
        summary, _ = get_jira_summary("DSFR-559")
        logging.info(f"Summary:{summary}")
        
        data_type='WORD'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # WORD MIN (0)
        data_type='WORD'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # WORD MAX (65535)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=65535
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    
    # TIME测试 (持续时间，毫秒)
    @allure_setup("DSFR-852", is_async=False)
    def test_jira_summary_time(self):
        summary, _ = get_jira_summary("DSFR-852")
        logging.info(f"Summary:{summary}")
        
        data_type='TIME'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1  # 1毫秒

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # TIME MIN (-2147483648)
        data_type='TIME'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=-2147483648
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # TIME MAX (2147483647)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=2147483647
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    
    # LTIME测试 (长持续时间，纳秒)
    @allure_setup("DSFR-853", is_async=False)
    def test_jira_summary_ltime(self):
        summary, _ = get_jira_summary("DSFR-853")
        logging.info(f"Summary:{summary}")
        
        data_type='LTIME'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1  # 1纳秒

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # LTIME MIN (-9223372036854775808)
        data_type='LTIME'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=-9223372036854775808
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # LTIME MAX (9223372036854775807)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=9223372036854775807
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    # DATE测试 (日期)
    @allure_setup("DSFR-854", is_async=False)
    def test_jira_summary_date(self):
        summary, _ = get_jira_summary("DSFR-854")
        logging.info(f"Summary:{summary}")
        
        data_type='DATE'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1  # 1970-1-2 (基准日期+1天)

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
        # DATE MIN (0 - 1970-1-1)
        data_type='DATE'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # DATE MAX (大约49710 - 2106-02-06)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=49709
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    # TIME_OF_DAY测试 (只有时间)
    @allure_setup("DSFR-856", is_async=False)
    def test_jira_summary_tod(self):
        summary, _ = get_jira_summary("DSFR-856")
        logging.info(f"Summary:{summary}")
        
        data_type='TOD'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1  # 00:00:00.001 (午夜后1毫秒)

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
         # TOD MIN (0 - 00:00:00.000)
        data_type='TOD'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # TOD MAX (86399999 - 23:59:59.999)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=86399999
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    
    # DATE_AND_TIME测试 (日期和时间)
    @allure_setup("DSFR-860", is_async=False)
    def test_jira_summary_dt(self):
        summary, _ = get_jira_summary("DSFR-860")
        logging.info(f"Summary:{summary}")
        
        data_type='DT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1  # 1970-1-1-0:0:1 (基准日期时间+1秒)

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

        # DT MIN (0 - 1970-1-1-0:0:0)
        data_type='DT'
        tag_name=f"STD::{driver_name}{data_type}MIN"
        test_value=0
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
        
        # DT MAX (大约4294880895 - 2106-02-06-06:28:15)
        tag_name=f"STD::{driver_name}{data_type}MAX"
        test_value=4294880895
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    
    #字符串
    @allure_setup("DSFR-892", is_async=False)
    def test_jira_summary_str(self):
        summary, _ = get_jira_summary("DSFR-892")
        logging.info(f"Summary:{summary}")
        
        data_type='STRING'
        data_type_name = 'STR'
        tag_name=f"STD::{driver_name}{data_type_name}"
        test_value="hello world!"

        assert test_value==read_value(dsfapi,tag_name,data_type)

        #STRMAX(254)
        data_type='STRING'
        data_type_name = 'STR'
        tag_name=f"STD::{driver_name}{data_type_name}MAX"
        test_value="1" * 254

        assert test_value==read_value(dsfapi,tag_name,data_type)

    
    #空字符串
    @allure_setup("DSFR-893", is_async=False)
    def test_jira_summary_strmin(self): 
        summary, _ = get_jira_summary("DSFR-893")
        logging.info(f"Summary:{summary}")
        
        data_type='STRING'
        data_type_name = 'STR'
        tag_name=f"STD::{driver_name}{data_type_name}MIN"
        test_value=""

        assert test_value==read_value(dsfapi,tag_name,data_type)



#天行驱动写入测试，先写入各类型最大值，读取验证，再写入初始值1，读取验证
    # TIME写入测试
    @allure_setup("DSFR-866", is_async=False)
    def test_jira_summary_writetime(self):
        summary, _ = get_jira_summary("DSFR-866")
        logging.info(f"Summary:{summary}")
        
        #数据类型
        data_type='TIME'
        #数据地址
        tag_name=f"STD::{driver_name}{data_type}"
        #测试数据，先写入最大值
        test_value=2147483647

        #验证
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        #再写回初始值1
        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # LTIME写入测试
    @allure_setup("DSFR-867", is_async=False)
    def test_jira_summary_writeltime(self):
        summary, _ = get_jira_summary("DSFR-867")
        logging.info(f"Summary:{summary}")
        
        data_type='LTIME'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=9223372036854775807

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # DATE写入测试
    @allure_setup("DSFR-868", is_async=False)
    def test_jira_summary_writedate(self):
        summary, _ = get_jira_summary("DSFR-868")
        logging.info(f"Summary:{summary}")
        
        data_type='DATE'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=49709

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # TOD (TIME_OF_DAY)写入测试
    @allure_setup("DSFR-869", is_async=False)
    def test_jira_summary_writetod(self):
        summary, _ = get_jira_summary("DSFR-869")
        logging.info(f"Summary:{summary}")
        
        data_type='TOD'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=86399999

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # DT (DATE_AND_TIME)写入测试
    @allure_setup("DSFR-870", is_async=False)
    def test_jira_summary_writedt(self):
        summary, _ = get_jira_summary("DSFR-870")
        logging.info(f"Summary:{summary}")
        
        data_type='DT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=4294880895

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # LINT写入测试
    @allure_setup("DSFR-872", is_async=False)
    def test_jira_summary_writelint(self):
        summary, _ = get_jira_summary("DSFR-872")
        logging.info(f"Summary:{summary}")
        
        data_type='LINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=9223372036854775807

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # ULINT写入测试
    @allure_setup("DSFR-873", is_async=False)
    def test_jira_summary_writeulint(self):
        summary, _ = get_jira_summary("DSFR-873")
        logging.info(f"Summary:{summary}")
        
        data_type='ULINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=18446744073709551615

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # LREAL写入测试
    @allure_setup("DSFR-874", is_async=False)
    def test_jira_summary_writelreal(self):
        summary, _ = get_jira_summary("DSFR-874")
        logging.info(f"Summary:{summary}")
        
        data_type='LREAL'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1.79769e+308

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # BYTE写入测试
    @allure_setup("DSFR-875", is_async=False)
    def test_jira_summary_writebyte(self):
        summary, _ = get_jira_summary("DSFR-875")
        logging.info(f"Summary:{summary}")
        
        data_type='BYTE'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=255  # (封装的时候转为uint8，所以为255)

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # CHAR写入测试
    @allure_setup("DSFR-876", is_async=False)
    def test_jira_summary_writechar(self):
        summary, _ = get_jira_summary("DSFR-876")
        logging.info(f"Summary:{summary}")
        
        data_type='CHAR'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=255  

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=49
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # SINT写入测试
    @allure_setup("DSFR-877", is_async=False)
    def test_jira_summary_writesint(self):
        summary, _ = get_jira_summary("DSFR-877")
        logging.info(f"Summary:{summary}")
        
        data_type='SINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=127

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # BOOL写入测试
    @allure_setup("DSFR-879", is_async=False)
    def test_jira_summary_writebool(self):
        summary, _ = get_jira_summary("DSFR-879")
        logging.info(f"Summary:{summary}")
        
        data_type='BOOL'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=1  # BOOL最大值就是1

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        # 因为最大值就是1，所以这里写入0然后再写回1
        test_value=0
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result
        
        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # REAL写入测试
    @allure_setup("DSFR-880", is_async=False)
    def test_jira_summary_writereal(self):
        summary, _ = get_jira_summary("DSFR-880")
        logging.info(f"Summary:{summary}")
        
        data_type='REAL'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=3.40282e+38

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # UINT写入测试
    @allure_setup("DSFR-881", is_async=False)
    def test_jira_summary_writeuint(self):
        summary, _ = get_jira_summary("DSFR-881")
        logging.info(f"Summary:{summary}")
        
        data_type='UINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=65535

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # DWORD写入测试
    @allure_setup("DSFR-882", is_async=False)
    def test_jira_summary_writedword(self):
        summary, _ = get_jira_summary("DSFR-882")
        logging.info(f"Summary:{summary}")
        
        data_type='DWORD'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=4294967295

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # INT写入测试
    @allure_setup("DSFR-883", is_async=False)
    def test_jira_summary_writeint(self):
        summary, _ = get_jira_summary("DSFR-883")
        logging.info(f"Summary:{summary}")
        
        data_type='INT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=32767

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # USINT写入测试
    @allure_setup("DSFR-884", is_async=False)
    def test_jira_summary_writeusint(self):
        summary, _ = get_jira_summary("DSFR-884")
        logging.info(f"Summary:{summary}")
        
        data_type='USINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=255

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # UDINT写入测试
    @allure_setup("DSFR-885", is_async=False)
    def test_jira_summary_writeudint(self):
        summary, _ = get_jira_summary("DSFR-885")
        logging.info(f"Summary:{summary}")
        
        data_type='UDINT'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=4294967295

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # LWORD写入测试
    @allure_setup("DSFR-886", is_async=False)
    def test_jira_summary_writelword(self):
        summary, _ = get_jira_summary("DSFR-886")
        logging.info(f"Summary:{summary}")
        
        data_type='LWORD'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=18446744073709551615

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # WORD写入测试
    @allure_setup("DSFR-887", is_async=False)
    def test_jira_summary_writeword(self):
        summary, _ = get_jira_summary("DSFR-887")
        logging.info(f"Summary:{summary}")
        
        data_type='WORD'
        tag_name=f"STD::{driver_name}{data_type}"
        test_value=65535

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

        test_value=1
        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result

    # STRING写入测试（字符串）
    @allure_setup("DSFR-895", is_async=False)
    def test_jira_summary_writestr(self):
        summary, _ = get_jira_summary("DSFR-895")
        logging.info(f"Summary:{summary}")
        
        #直接判定断言失败
        assert False, "此测试用例目前预期失败"

        # data_type='STRING'
        # data_type_name='STR'
        # tag_name=f"STD::{driver_name}{data_type_name}"
        # test_value=""

        # result=write_value(dsfapi,tag_name,data_type,test_value)
        # assert True==result

        # test_value="1"*254
        # result=write_value(dsfapi,tag_name,data_type,test_value)
        # assert True==result


        # #写回
        # test_value="hello world!"
        # result=write_value(dsfapi,tag_name,data_type,test_value)
        # assert True==result

    # 空字符串写入测试
    @allure_setup("DSFR-896", is_async=False)
    def test_jira_summary_writestrmin(self):
        summary, _ = get_jira_summary("DSFR-896")
        logging.info(f"Summary:{summary}")
        
        data_type='STRING'
        data_type_name='STR'
        tag_name=f"STD::{driver_name}{data_type_name}MIN"
        test_value=""

        result=write_value(dsfapi,tag_name,data_type,test_value)
        assert True==result



    #系统状态点读取测试
    @allure_setup("DSFR-889", is_async=False)
    def test_jira_summary_connectstatus(self):
        summary, _ = get_jira_summary("DSFR-889")
        logging.info(f"Summary:{summary}")
        
        data_type='BYTE'
        tag_name=f"DSF_STATION_1.SYS::TXDRV_DEVICE0_CONNECTSTATUS"
        test_value=1

        assert str(test_value)==read_value(dsfapi,tag_name,data_type)


# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfapi_txdrv.py"])