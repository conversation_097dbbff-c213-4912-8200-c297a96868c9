import os
import pytest

# 使用绝对导入
from AutoCommon.allure_setup import *
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"
#驱动名称
driver_name='icgdrv'
#设备名称
device_name=f'{driver_name}_Device0'
#全局变量
dsfapi=None


#测试之前
def before_test():
    print("测试开始前执行")
    # 声明要使用全局变量
    global dsfapi

    # 加载DSFAPI动态库
    print("加载DSFAPI动态库")
    dsfapi=loadDSFAPILibrary(application_path)
    if not dsfapi : return

    # 初始化DSFAPI
    print("初始化DSFAPI")
    initDSFAPI(dsfapi)

#测试之后
def after_test():
    print("测试结束后执行")
    # 销毁
    print("销毁DSFAPI")
    dsfapi.freeDRSdkContext()

#测试之前和测试之后执行方法
@pytest.fixture(scope="session", autouse=True)
def my_fixture():
    before_test()  # 在测试开始前执行
    yield  # 这里是测试正在执行的地方
    after_test()  # 在测试结束后执行

class TestICG():

    @allure_setup("DSFR-663", is_async=False)
    def test_jira_summary_663(self):
        summary, _ = get_jira_summary("DSFR-663")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证ICG驱动是否启动成功"
        
        process_name="icgdrv"
        assert is_process_running(process_name)
    @allure_setup("DSFR-865", is_async=False)
    def test_jira_summary_865(self):
        summary, _ = get_jira_summary("DSFR-865")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动设备状态点是否正确"
        
        #数据类型
        data_type='BYTE'
        #数据地址
        tag_name=f"DSF_STATION_1.SYS::ICGDRV_DEVICE0_CONNECTSTATUS"
        #测试数据
        test_value=1

        #读取值并验证
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    @allure_setup("DSFR-569", is_async=False)
    def test_jira_summary_569(self):
        summary, _ = get_jira_summary("DSFR-569")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动能否正常采集到数据（STRING）"
        
        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::S_STRING1"
        #测试数据
        test_value="test icgdrv"

        #读取值并验证
        assert test_value==read_value(dsfapi,tag_name,data_type)

    @allure_setup("DSFR-575", is_async=False)
    def test_jira_summary_575(self):
        summary, _ = get_jira_summary("DSFR-575")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动能否正常采集到数据（DINT）"
        
        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::K_LONG1"
        #测试数据
        test_value=100

        #读取值并验证
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    @allure_setup("DSFR-576", is_async=False)
    def test_jira_summary_576(self):
        summary, _ = get_jira_summary("DSFR-576")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动能否正常采集到数据（LREAL）"
        
        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::K_FLOAT1"
        #测试数据
        test_value=1

        #读取值并验证
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    @allure_setup("DSFR-819", is_async=False)
    def test_jira_summary_819(self):
        summary, _ = get_jira_summary("DSFR-819")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动DINT读取4字节整形最大值"
        
        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::K_LONG2"
        #测试数据
        test_value=2**31-1

        #读取值并验证
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    @allure_setup("DSFR-820", is_async=False)
    def test_jira_summary_820(self):
        summary, _ = get_jira_summary("DSFR-820")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动DINT读取4字节整形最小值"
        
        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::K_LONG3"
        #测试数据
        test_value=-2**31

        #读取值并验证
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    @allure_setup("DSFR-821", is_async=False)
    def test_jira_summary_821(self):
        summary, _ = get_jira_summary("DSFR-821")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动STRING读取默认长度字符串（64）"
        
        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::S_STRING2"
        #测试数据
        test_value_len=64

        #读取值并验证
        assert test_value_len==len(read_value(dsfapi,tag_name,data_type))

    @allure_setup("DSFR-1019", is_async=False)
    def test_jira_summary_1019(self):
        summary, _ = get_jira_summary("DSFR-1019")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动STRING读取指定长度字符串（255）"
        
        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::S_STRING4"
        #测试数据
        test_value_len=255

        #读取值并验证
        assert test_value_len==len(read_value(dsfapi,tag_name,data_type))

    @allure_setup("DSFR-822", is_async=False)
    def test_jira_summary_822(self):
        summary, _ = get_jira_summary("DSFR-822")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动STRING读取空字符串"
        
        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::S_STRING3"
        #测试数据
        test_value_len=0

        #读取值并验证
        assert test_value_len==len(read_value(dsfapi,tag_name,data_type))

    @allure_setup("DSFR-823", is_async=False)
    def test_jira_summary_823(self):
        summary, _ = get_jira_summary("DSFR-823")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动LREAL读取8字节浮点型最大值"
        
        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::K_FLOAT2"
        #测试数据
        test_value=1.7e32

        #读取值并验证
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    @allure_setup("DSFR-824", is_async=False)
    def test_jira_summary_824(self):
        summary, _ = get_jira_summary("DSFR-824")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动LREAL读取8字节浮点型最小值"
        
        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::K_FLOAT3"
        #测试数据
        test_value=-1.7e32

        #读取值并验证
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    @allure_setup("DSFR-586", is_async=False)
    def test_jira_summary_586(self):
        summary, _ = get_jira_summary("DSFR-586")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证ICG驱动能否正常写入数据（STRING）"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::S_STRING1"
        #测试数据
        test_value='test icgdrv'

        #写入值
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证写入的值
        assert test_value==read_value(dsfapi,tag_name,data_type)

    @allure_setup("DSFR-592", is_async=False)
    def test_jira_summary_592(self):
        summary, _ = get_jira_summary("DSFR-592")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证ICG驱动能否正常写入数据（DINT）"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::K_LONG1"
        #测试数据
        test_value=100

        #写入值
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证写入的值
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)
    @allure_setup("DSFR-593", is_async=False)
    def test_jira_summary_593(self):
        summary, _ = get_jira_summary("DSFR-593")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证ICG驱动能否正常写入数据（LREAL）"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::K_FLOAT1"
        #测试数据
        test_value=1
        #写入值
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证写入的值
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    @allure_setup("DSFR-825", is_async=False)
    def test_jira_summary_825(self):
        summary, _ = get_jira_summary("DSFR-825")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动DINT写入4字节整形最大值"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::K_LONG2"
        #测试数据
        test_value=2**31-1

        #写入值
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证写入的值
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    @allure_setup("DSFR-826", is_async=False)
    def test_jira_summary_826(self):
        summary, _ = get_jira_summary("DSFR-826")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动DINT写入4字节整形最小值"

        #数据类型
        data_type='DINT'
        #数据地址
        tag_name=f"STD::K_LONG3"
        #测试数据
        test_value=-2**31

        #写入值
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证写入的值
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    @allure_setup("DSFR-827", is_async=False)
    def test_jira_summary_827(self):
        summary, _ = get_jira_summary("DSFR-827")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动STRING写入默认长度字符串（64）"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::S_STRING2"
        #测试数据
        test_value_len=64
        test_value='1'*test_value_len

        #写入值
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证写入的值
        assert test_value_len==len(read_value(dsfapi,tag_name,data_type))

    @allure_setup("DSFR-1020", is_async=False)
    def test_jira_summary_1020(self):
        summary, _ = get_jira_summary("DSFR-1020")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动STRING写入指定长度字符串（255）"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::S_STRING4"
        #测试数据
        test_value_len=255
        test_value='1'*test_value_len

        #写入值
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证写入的值
        assert test_value_len==len(read_value(dsfapi,tag_name,data_type))

    @allure_setup("DSFR-828", is_async=False)
    def test_jira_summary_828(self):
        summary, _ = get_jira_summary("DSFR-828")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动STRING写入空字符串"

        #数据类型
        data_type='STRING'
        #数据地址
        tag_name=f"STD::S_STRING3"
        #测试数据
        test_value_len=0
        test_value=''

        #写入值
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证写入的值
        assert test_value_len==len(read_value(dsfapi,tag_name,data_type))

    @allure_setup("DSFR-829", is_async=False)
    def test_jira_summary_829(self):
        summary, _ = get_jira_summary("DSFR-829")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动LREAL写入8字节浮点型最大值"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::K_FLOAT2"
        #测试数据
        test_value=1.7e32

        #写入值
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证写入的值
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    @allure_setup("DSFR-830", is_async=False)
    def test_jira_summary_830(self):
        summary, _ = get_jira_summary("DSFR-830")
        logging.info(f"Summary:{summary}")
        # assert summary == "dsfapi验证ICG驱动LREAL写入8字节浮点型最小值"

        #数据类型
        data_type='LREAL'
        #数据地址
        tag_name=f"STD::K_FLOAT3"
        #测试数据
        test_value=-1.7e32

        #写入值
        write_value(dsfapi,tag_name,data_type,test_value)

        #验证写入的值
        assert str(test_value)==read_value(dsfapi,tag_name,data_type)

    @allure_setup("DSFR-836", is_async=False)
    def test_jira_summary_836(self):
        summary, _ = get_jira_summary("DSFR-836")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证ICG驱动主机能否采集到数据"

        assert False == False

    @allure_setup("DSFR-850", is_async=False)
    def test_jira_summary_850(self):
        summary, _ = get_jira_summary("DSFR-850")
        logging.info(f"Summary:{summary}")
        # assert summary == "autotest验证ICG驱动备机能否采集到数据"

        assert False == False

    
# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_dsfapi_icgdrv.py"])