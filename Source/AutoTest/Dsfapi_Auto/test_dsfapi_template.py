import os

# 使用绝对导入
from AutoCommon.test_function_util import *

#程序路径（获取当前路径的上两级路径../../Source/AutoTest）
# application_path="/home/<USER>/work/dr/"
current_path = os.getcwd()
application_path = os.path.abspath(os.path.join(current_path, '..', '..'))+"/"

#测试写值
def test_write():
    # 加载DSFAPI动态库
    dsfapi=loadDSFAPILibrary(application_path)
    if not dsfapi : return

    # 初始化DSFAPI
    initDSFAPI(dsfapi)

    if dsfapi.connectStatus():
        # 写值
        write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_STRING1","STRING","hello")
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_INT1","INT",2**15-1)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_UINT1","UINT",2**15-1)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_REAL1","REAL",3.4e38)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_BOOL1","BOOL",1)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_UDINT1","UDINT",2**31-1)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_DINT1","DINT",2**31-1)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_LREAL1","LREAL",-1.7e308)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_SINT1","SINT",2**7-1)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_USINT1","USINT",2**7-1)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_LINT1","LINT",2**63-1)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_ULINT1","ULINT",2**63-1)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_BYTE1","BYTE",2**7-1)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_WORD1","WORD",2**15-1)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_DWORD1","DWORD",2**31-1)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_LWORD1","LWORD",2**63-1)
        # write_value(dsfapi,"STD::MODBUSDRV_DEVICE0_CHAR1","CHAR",'z')
    else:
        print("dsfdataservice未连接")

    # 销毁
    dsfapi.freeDRSdkContext()

#测试读值
def test_read():
    # 加载DSFAPI动态库
    dsfapi=loadDSFAPILibrary(application_path)
    if not dsfapi : return

    # 初始化DSFAPI
    initDSFAPI(dsfapi)

    if dsfapi.connectStatus():
        # 读值
        print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_STRING1","STRING"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_INT1","INT"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_UINT1","UINT"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_REAL1","REAL"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_BOOL1","BOOL"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_UDINT1","UDINT"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_DINT1","DINT"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_LREAL1","LREAL"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_SINT1","SINT"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_USINT1","USINT"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_LINT1","LINT"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_ULINT1","ULINT"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_BYTE1","BYTE"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_WORD1","WORD"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_DWORD1","DWORD"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_LWORD1","LWORD"))
        # print(read_value(dsfapi,"STD::MODBUSDRV_DEVICE0_CHAR1","CHAR"))
    else:
        print("dsfdataservice未连接")

    # 销毁
    dsfapi.freeDRSdkContext()

test_write()
test_read()