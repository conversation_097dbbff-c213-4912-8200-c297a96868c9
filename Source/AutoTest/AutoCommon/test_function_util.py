import subprocess
import xml.etree.ElementTree as ET
import json
import sqlite3
import psutil
import shutil
import redis
import struct
import time
import os
from ctypes import *

#数据库路径
db_path="projects/defaultproject/config/PDSCfg.db"
#Redis配置路径
redis_config_path="projects/defaultproject/config/NGVS/datadefinition.dsfngvs"
#驱动控制程序配置路径
driver_controller_config_path="projects/defaultproject/config/DrVCtrlCfg.xml"
#进程管理程序配置路径
progress_manager_config_path="projects/defaultproject/config/ProcessMgr.xml"

#Redis服务器地址
redis_host="127.0.0.1"
#Redis端口
redis_port=6380
#Redis数据库
redis_db=0

#DSF数据服务IP地址
dsfdataservice_ip="127.0.0.1"
#DSF数据服务端口
dsfdataservice_port=1234

#根据数据类型获取Byte字符串（小端）
# param1 类型
def get_byte_string_by_type(data_type):
    if data_type=="STRING":
        return 'B'
    elif data_type=="INT":
        return 'h'
    elif data_type=="UINT":
        return 'H'
    elif data_type=="REAL":
        return 'f'
    elif data_type=="BOOL":
        return 'B'
    elif data_type=="TIME":
        return 'i'
    elif data_type=="UDINT":
        return 'I'
    elif data_type=="DINT":
        return 'i'
    elif data_type=="LREAL":
        return 'd'
    elif data_type=="BLOB":
        return 'q'
    elif data_type=="SINT":
        return 'b'
    elif data_type=="USINT":
        return 'B'
    elif data_type=="LINT":
        return 'q'
    elif data_type=="ULINT":
        return 'Q'
    elif data_type=="LTIME":
        return 'q'
    elif data_type=="BYTE":
        return 'B'
    elif data_type=="WORD":
        return 'H'
    elif data_type=="DWORD":
        return 'I'
    elif data_type=="LWORD":
        return 'Q'
    elif data_type=="CHAR":
        return 'B'
    elif data_type=="UDT":
        return 'b' #自定义
    elif data_type=="DATE":
        return 'I'
    elif data_type=="TOD":
        return 'I'
    elif data_type=="DT":
        return 'I'
    else:
        return 'b'

#根据数据类型获取Byte（小端）
# param1 数据
# param2 类型
def get_byte_by_type(data,data_type):
    #设置为小端
    byte_string="<"

    if data_type=="STRING":
        byte_string+=get_byte_string_by_type(data_type)
        # 总长度
        max_length=255
        max_length_byte=struct.pack(byte_string, max_length)
        # 实际长度
        length=len(data)
        length_byte=struct.pack(byte_string, length)
        return max_length_byte+length_byte+data.encode('utf-8')
    elif data_type=="UDT":
        # 分割元组
        types = [item[0] for item in data]
        values = [item[1] for item in data]
        # 拼接
        for type in types:
            byte_string+=f"{get_byte_string_by_type(type)} "
        return struct.pack(byte_string, *values)
    else:
        byte_string+=get_byte_string_by_type(data_type)
        return struct.pack(byte_string, data)

#根据数据类型获取值（小端）
# param1 字节
# param2 类型
def get_value_by_type(byte,data_type):
    #设置为小端
    byte_string="<"

    if data_type=="STRING":
        return byte[2:].decode('utf-8')
    else:
        byte_string+=get_byte_string_by_type(data_type)
        return struct.unpack(byte_string, byte)[0]

#数据类型转换
# param1 类型
def data_type_convert(data_type):
    if data_type=="STRING":
        return 0
    elif data_type=="INT":
        return 1
    elif data_type=="UINT":
        return 2
    elif data_type=="REAL":
        return 3
    elif data_type=="BOOL":
        return 4
    elif data_type=="TIME":
        return 5
    elif data_type=="UDINT":
        return 6
    elif data_type=="DINT":
        return 7
    elif data_type=="LREAL":
        return 8
    elif data_type=="BLOB":
        return 9
    elif data_type=="SINT":
        return 10
    elif data_type=="USINT":
        return 11
    elif data_type=="LINT":
        return 12
    elif data_type=="ULINT":
        return 13
    elif data_type=="LTIME":
        return 14
    elif data_type=="BYTE":
        return 15
    elif data_type=="WORD":
        return 16
    elif data_type=="DWORD":
        return 17
    elif data_type=="LWORD":
        return 18
    elif data_type=="CHAR":
        return 19
    elif data_type=="UDT":
        return 20
    elif data_type=="DATE":
        return 21
    elif data_type=="TOD":
        return 22
    elif data_type=="DT":
        return 23
    else:
        return 20

#获取数据类型长度
# param1 类型
def get_data_type_length(data_type):
    if data_type=="STRING":
        return 255
    elif data_type=="INT":
        return 2
    elif data_type=="UINT":
        return 2
    elif data_type=="REAL":
        return 4
    elif data_type=="BOOL":
        return 1
    elif data_type=="TIME":
        return 4
    elif data_type=="UDINT":
        return 4
    elif data_type=="DINT":
        return 4
    elif data_type=="LREAL":
        return 8
    elif data_type=="BLOB":
        return 8 #未知长度，暂定为8字节
    elif data_type=="SINT":
        return 1
    elif data_type=="USINT":
        return 1
    elif data_type=="LINT":
        return 8
    elif data_type=="ULINT":
        return 8
    elif data_type=="LTIME":
        return 8
    elif data_type=="BYTE":
        return 1
    elif data_type=="WORD":
        return 2
    elif data_type=="DWORD":
        return 4
    elif data_type=="LWORD":
        return 8
    elif data_type=="CHAR":
        return 1
    elif data_type=="UDT":
        return 2 #未知长度，暂定为2字节
    elif data_type=="DATE":
        return 4
    elif data_type=="TOD":
        return 4
    elif data_type=="DT":
        return 4
    else:
        return 1

#复制文件
def copy_file(source_file, destination_file):
    try:
        shutil.copy(source_file, destination_file)
        print(f"文件 '{source_file}' 成功复制到 '{destination_file}'")
    except FileNotFoundError:
        print(f"源文件 '{source_file}' 不存在")
    except PermissionError:
        print("没有权限复制文件，请以有权限的用户运行此脚本")
    except Exception as e:
        print(f"复制文件时发生错误: {e}")

def write_xml(root_element,file_path):
    # 创建一个树对象
    tree = ET.ElementTree(root_element)

    # 将 XML 写入文件并格式化缩进
    from xml.dom import minidom
    xml_str = ET.tostring(tree.getroot(), 'utf-8')
    xml_string = minidom.parseString(xml_str).toprettyxml(indent='  ')
    with open(file_path, 'w') as xml_file:
        xml_file.write(xml_string)

#启动程序
# def start_program(program_name,command):
#     if is_process_running(program_name):
#         redis_server=start_process(command.split(' '))
#         #停止
#         stop_program(redis_server)
#         #启动
#         print(f"启动{program_name}")
#         redis_server=start_process(command.split(' '))
#     else:
#         #启动
#         print(f"启动{program_name}")
#         redis_server=start_process(command.split(' '))

#启动进程
def start_program(command,subprocess_path=None,env=None):
    process = subprocess.Popen(command, cwd=subprocess_path, env=env, stdin=subprocess.PIPE, stdout=subprocess.PIPE, stderr=subprocess.PIPE)
    print(f"程序已启动，进程号: {process.pid}")
    return process

#关闭程序
def stop_program(process):
    if process is None: return

    print(f"正在关闭程序，进程号: {process.pid}")
    process.terminate()  # 尝试优雅终止进程
    
    try:
        process.wait(timeout=5)  # 等待进程终止
    except subprocess.TimeoutExpired:
        print(f"强制杀死{process.pid}")
        process.kill()  # 如果超时，则强制杀死进程

#关闭程序
def stop_program_by_name(process_name):
    for process in psutil.process_iter(['pid', 'name']):
        try:
            # 检查进程的名称是否与指定的名称匹配
            if process_name in process.info['name']:
                print(f"正在关闭{process.info['name']}程序，进程号: {process.info['pid']}")
                process.terminate()    # 尝试优雅地终止进程
                try:
                    process.wait(timeout=5)  # 等待进程终止
                except subprocess.TimeoutExpired:
                    process.kill()  # 如果超时，则强制杀死进程
                print(f"已关闭进程: {process.info['name']}")
                # break #只关闭第一个同名的程序
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            pass

#关闭进程管理程序
def stop_program_manager(process):
    if process is None: return
    
    print(f"正在关闭程序，进程号: {process.pid}")

    try:
        command = 'echo q\n'  # 发送 q 命令到子进程
        process.stdin.write(command.encode('utf-8'))  # 写入输入
        # process.stdin.flush()  # 刷新输入缓冲区
    except subprocess.TimeoutExpired:
        stop_program(process)

#获取已经运行的程序
def get_program_by_name(process_name):
    for process in psutil.process_iter(['pid', 'name']):
        try:
            # 检查进程的名称是否与指定的名称匹配
            if process_name in process.info['name']:
                return process
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            return None
    return None

#获取已经运行的程序的状态
def get_program_status_by_name(process_name):
    for process in psutil.process_iter(['pid', 'name']):
        try:
            # 检查进程的名称是否与指定的名称匹配
            if process_name in process.info['name']:
                return process.status==psutil.STATUS_RUNNING
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            return False
    return False

#进程是否在运行
def is_process_running(process_name):

    #方法一：使用pgrep命令
    # try:
    #     # 执行 pgrep 命令并捕获输出
    #     subprocess.check_output(['pgrep', process_name])
    #     print(f"{process_name}已启动")
    #     return True
    # except subprocess.CalledProcessError:
    #     return False
    #方法二：使用psutil库
    for process in psutil.process_iter(['pid', 'name']):
        try:
            # 检查进程的名称是否与指定的名称匹配
            if process_name in process.info['name']:
                print(f"发现已启动的进程: {process.info['name']} (PID: {process.info['pid']})")
                return True
        except (psutil.NoSuchProcess, psutil.AccessDenied, psutil.ZombieProcess):
            return False
    return False

#创建驱动控制配置
def driver_controller_config(application_path,driver_name):

    # 创建元素 <SCADA>
    scada = ET.Element('SCADA')

    # 添加子元素 <name>
    name = ET.SubElement(scada, 'name')
    name.text = 'DualiCV_Test'

    # 添加 <drivers> 元素
    drivers = ET.SubElement(scada, 'drivers')

    # 添加 <driver> 元素
    driver = ET.SubElement(drivers, 'driver', name=driver_name, path=application_path+f'executable/drivers/{driver_name}/')

    #写入xml文件
    xml_file=os.path.join(application_path+driver_controller_config_path)
    write_xml(scada,xml_file)
    
    print("驱动控制配置文件已生成")

#创建进程管理配置
def process_manager_config(application_path):
    # 创建父元素 <ProcessMgr>
    process_manager = ET.Element("ProcessMgr", {
        "version": "2",
        "desc": "Platform= 1(win) 2(unix) or 3(all), ModuleName not include .exe"
    })

    # 创建子元素 <ProcessList>
    process_list = ET.SubElement(process_manager, "ProcessList")

    # 创建子元素 <Process>
    process_data = [
        {
            "ModuleName": "dsfdrvctrl",
            "DisplayName": "驱动控制",
            "Platform": "3",
            "SleepTime": "2",
            "RestartEnable": "1",
            "Type": "1",
            "Label": "drvctrl"
        },
        {
            "ModuleName": "dsfacquisitionservice",
            "DisplayName": "采集服务",
            "Platform": "3",
            "SleepTime": "2",
            "RestartEnable": "1",
            "Type": "1",
            "Label": "ac"
        },
        {
            "ModuleName": "dsfrmservice",
            "DisplayName": "冗余服务",
            "Platform": "3",
            "SleepTime": "3",
            "RestartEnable": "1",
            "Type": "1",
            "Label": "rm"
        }
    ]
    for process in process_data:
        ET.SubElement(process_list, 'Process', **process)

    #写入xml文件
    xml_file=os.path.join(application_path+progress_manager_config_path)
    write_xml(process_manager,xml_file)
    
    print("进程管理配置文件已生成")

# 创建数据库
def create_database(application_path,data_type,driver_name,device_name,data_address):

    # 连接到 SQLite 数据库
    # 如果数据库不存在，会自动创建
    db_path=os.path.join(application_path+db_path)
    connection = sqlite3.connect(db_path)

    # 创建一个游标对象
    cursor = connection.cursor()

    # 从 SQL 文件中读取 SQL 脚本
    # with open('script.sql', 'r') as sql_file:
    #     sql_script = sql_file.read()

    data_type_num=data_type_convert(data_type)

    # SQL 脚本
    sql_script = f"""
        --删除表t_pb_tags;
        DROP TABLE IF EXISTS "t_pb_tags";

        --创建表t_pb_tags;
        CREATE TABLE "t_pb_tags" (
            "fd_block_name" VARCHAR(128),
            "fd_block_desc" VARCHAR(128),
            "fd_scan_intv" INT,
            "fd_scan_mode" SMALLINT DEFAULT 1,
            "fd_access_mode" SMALLINT,
            "fd_iodrv" VARCHAR(32),
            "fd_ioaddr" VARCHAR(256),
            "fd_init_value" TEXT,
            "fd_egu_lbl" VARCHAR(15),
            "fd_egu_enable" SMALLINT,
            "fd_egu_low" FLOAT,
            "fd_raw_low" FLOAT,
            "fd_egu_span" FLOAT,
            "fd_raw_span" FLOAT,
            "fd_data_type" SMALLINT NOT NULL,
            "fd_byte_order" VARCHAR(18),
            "fd_data_type_ex" INT,
            "fd_tagid" INT NOT NULL,
            PRIMARY KEY ("fd_block_name")
        );

        INSERT INTO "t_pb_tags" ("fd_block_name", "fd_block_desc", "fd_scan_intv", "fd_scan_mode", "fd_access_mode", "fd_iodrv", "fd_ioaddr", "fd_init_value", "fd_egu_lbl", "fd_egu_enable", "fd_egu_low", "fd_raw_low", "fd_egu_span", "fd_raw_span", "fd_data_type", "fd_byte_order", "fd_data_type_ex", "fd_tagid") VALUES (
        'STD::{driver_name}_{data_type}1', 
        NULL, 
        1000, 
        1, 
        NULL, 
        '{driver_name}', 
        '{device_name}:{data_address}', 
        NULL, 
        NULL, 
        NULL, 
        NULL, 
        NULL, 
        NULL, 
        NULL, 
        {data_type_num}, 
        NULL, 
        NULL, 
        1);
    """

    # 执行 SQL 脚本
    try:
        cursor.executescript(sql_script)
        print("SQL 脚本已成功执行")
    except sqlite3.Error as e:
        print(f"执行 SQL 脚本时出错: {e}")

    # 提交事务
    connection.commit()

    # 关闭游标和连接
    cursor.close()
    connection.close()

#创建Redis配置文件
def create_redis_config(application_path,data_type,driver_name):
    # 定义 JSON 数据结构
    data = {
        "LocalObjects": [
            {
                "Length": get_data_type_length(data_type),
                "Name": f"STD::{driver_name}_{data_type}1",
                "TypeOf": data_type
            }
        ],
        "LocalVariables": [
            {
                "DBName": "",
                "DBid": 1,
                "type": data_type,
                "var_name": f"STD::{driver_name}_{data_type}1"
            }
        ],
        "UDT": [],
        "driver": [
            {
                "driver_name": driver_name,
                "path": f"./drivers/{driver_name}.xml"
            }
        ]
    }

    # 将数据写入 JSON 文件，并设置缩进
    json_file=os.path.join(application_path+redis_config_path)
    with open(json_file, 'w') as json_file:
        json.dump(data, json_file, indent=4)

    print("JSON 文件已生成")

#初始化redis
def init_redis():
    try:
        redis_client = redis.Redis(redis_host, redis_port, redis_db)
        # 检验连接是否正确
        redis_client.ping()
        print("成功连接到Redis")
        # 清空 Redis 中的数据
        redis_client.flushdb()
    except redis.ConnectionError as e:
        print(f"Redis连接失败: {e}")
        raise

#获取redis值
def get_redis_value(key):
    # 连接到 Redis
    redis_client = redis.Redis(redis_host,redis_port,redis_db)

    # 获取指定 key 的值
    value = redis_client.get(key)[:-8]  #去掉后面VTQ 8个字符（长度(2)+时间戳(4)+质量(2)）

    if value is not None:
        print(f"键 '{key}' 的值是: {value}")
    else:
        print(f"键 '{key}' 不存在")
    
    return value

#获取redis中键的数量
def get_key_count_from_redis():
    # 连接到 Redis
    redis_client = redis.Redis(redis_host,redis_port,redis_db)

    # 获取 key 的数量
    count = redis_client.dbsize()
    return count

#备份配置
# param1 配置文件目录
# param2 压缩文件名称
def backup_config(config_path,file_name):
    print("备份配置文件")
    # 检查源目录是否存在
    if not os.path.exists(config_path):
        print("配置文件目录不存在")
        return False

    # 压缩配置目录
    try:
        shutil.make_archive(file_name, 'zip', config_path)
        print(f"压缩成功，文件名为: {file_name}.zip")
        return True
    except Exception as e:
        print(f"压缩失败: {e}")
        return False
    
#恢复配置
# param1 配置文件目录
# param2 压缩文件
def recover_config(config_path,archive_path):
    # 检查压缩文件是否存在
    if not os.path.exists(archive_path):
        print("压缩文件不存在")
        return False

    print("恢复配置文件")
    # 解压缩配置文件
    try:
        shutil.unpack_archive(archive_path, config_path, 'zip')
        print(f"解压成功，文件已解压到: {config_path}")
        return True
    except Exception as e:
        print(f"解压失败: {e}")
        return False

#加载DSFAPI动态库
# param1 应用路径
def loadDSFAPILibrary(application_path):
    lib_path = os.path.join(application_path,"executable/libdsfapiinterface.so")
    try:
        dsfapi = cdll.LoadLibrary(lib_path)
        dsfapi.init.argtypes = [c_char_p, c_uint]
        dsfapi.writeValue.argtypes = [c_char_p, c_char_p, c_size_t]
        dsfapi.writeValue.restype = c_int32
        dsfapi.readValue.argtypes = [c_char_p, c_uint16, c_char_p]
        dsfapi.writeValue.restype = c_int32
        print("DLL加载成功")
        return dsfapi

    except OSError as e:
        print(f"加载DLL时出错: {e}")
        # 进一步输出更多的错误信息
        # import traceback
        # traceback.print_exc()

        #退出程序
        # sys.exit(1)
        return None

#加载DeploySDK动态库
# param1 应用路径
def loadDeploySDKLibrary(application_path):
    lib_path = os.path.join(application_path,"executable/libdsfdeploysdk.so")
    try:
        deploysdk = cdll.LoadLibrary(lib_path)
        # 定义 C++ 函数的参数和返回类型
        deploysdk.WriteData.argtypes = [
            c_char_p,       # const char* IPAddr
            c_ushort,       # unsigned short port
            c_char_p,       # const char* buf
            c_uint,         # unsigned int len
            c_int           # int timeout
        ]
        deploysdk.WriteData.restype = c_int  # 假设返回值是 int
        print("DLL加载成功")
        return deploysdk

    except OSError as e:
        print(f"加载DLL时出错: {e}")
        return None

#加载DSFPMRAPI动态库
# param1 应用路径
def loadDSFPMRAPILibrary(application_path):
    lib_path = os.path.join(application_path,"executable/libdrpmrapi.so")
    try:
        dsfpmrapi = cdll.LoadLibrary(lib_path)

        # 设置 InitEx 参数类型
        dsfpmrapi.PMRAPI_InitEx.argtypes = [c_char_p, c_uint16]
        dsfpmrapi.PMRAPI_InitEx.restype = c_int32
        
        # 设置 PMRAPI_RestartAllProcess 参数类型
        dsfpmrapi.PMRAPI_RestartAllProcess.argtypes = [c_char_p, c_long]
        dsfpmrapi.PMRAPI_InitEx.restype = c_int32

        # 设置 StopProcess 参数类型
        dsfpmrapi.PMRAPI_StopProcess.argtypes = [c_char_p, c_char_p, c_long]
        dsfpmrapi.PMRAPI_StopProcess.restype = c_int32
        
        # 设置  FiniEx 参数类型
        dsfpmrapi.PMRAPI_FiniEx.argtypes = []
        dsfpmrapi.PMRAPI_FiniEx.restype = c_int32
        print("DLL加载成功")
        return dsfpmrapi

    except OSError as e:
        print(f"加载DLL时出错: {e}")
        return None

#加载DrvctrlAPI动态库
# param1 应用路径
def loadDrvctrlAPILibrary(application_path):
    lib_path = os.path.join(application_path,"executable/libdrvctrlSDK.so")
    try:
        drvctrlapi = cdll.LoadLibrary(lib_path)
        # drvctrlapi.StartDrv.argtypes = [c_char_p, c_char_p, c_uint16, c_int16]
        # drvctrlapi.StopDrv.argtypes = [c_char_p, c_char_p, c_uint16, c_int16]
        print("DLL加载成功")
        return drvctrlapi

    except OSError as e:
        print(f"加载DLL时出错: {e}")
        return None

#初始化DSFPMRAPI
def initDSFPMRAPI(dsfpmrapi):
    ip = "127.0.0.1"
    port = 55004
    return dsfpmrapi.PMRAPI_InitEx(ip.encode('utf-8'), c_uint16(port))

#释放DSFPMRAPI
def freeDSFPMRAPI(dsfpmrapi):
    dsfpmrapi.PMRAPI_FiniEx()

#通过DSFPMRAPI停止进程
def stopProcess(dsfpmrapi, processName: str):
    ip = "127.0.0.1"
    timeout_ms = 3000
    return dsfpmrapi.PMRAPI_StopProcess(ip.encode('utf-8'), processName.encode('utf-8'), c_long(timeout_ms))

#通过DSFPMRAPI重启所有进程
def restartAllProcess(dsfpmrapi):
    ip = "127.0.0.1"
    timeout_ms = 3000
    return dsfpmrapi.PMRAPI_RestartAllProcess(ip.encode('utf-8'), c_long(timeout_ms))

#初始化DSFAPI
# param1 dsfapi动态库对象
def initDSFAPI(dsfapi):
    # 预分配内存（长度40）
    ip_str = dsfdataservice_ip.encode('utf-8')
    buffer = create_string_buffer(len(ip_str)) 
    buffer.value = ip_str
    dsfapi.init(buffer,dsfdataservice_port)

# 验证写入
# param1 dsfapi动态库对象
# param2 tag名
# param3 类型
# param4 写入值
def verify_write(dsfapi,name: str,type: str,value):
    result=False

    time.sleep(2)
    #通过Redis验证
    #...

    #通过DSFAPI验证
    actual_value=read_value(dsfapi,name,type)
    if type!='STRING':
        value=str(value)
        actual_value=str(actual_value)
    #比较
    if value==actual_value:
        result=True
    else:
        print(f"期待值：{value}，实际值：{actual_value}")

    return result

# 验证写入（结构体）
# param1 dsfapi动态库对象
# param2 tag名
# param3 写入值
def verify_write_struct(dsfapi,name: str,value):
    result=True

    time.sleep(2)
    #通过Redis验证
    #...

    #通过DSFAPI验证
    types = [item[0] for item in value]
    values = [item[1] for item in value]
    for i in range(0,len(types)):
        actual_value=read_value(dsfapi,name+f".{types[i]}",types[i])
        # 比较
        # print(f"比较结构体中第{i+1}个类型{types[i]}")
        if types[i]!='STRING':
            values[i]=str(values[i])
            actual_value=str(actual_value)
        if values[i]!=actual_value:
            print(f"期待值：{value}，实际值：{actual_value}")
            result=False
            break

    return result

# 写值
# param1 dsfapi动态库对象
# param2 tag名
# param3 类型
# param4 值
def write_value(dsfapi,name: str,type: str,value):
    result=False
    if dsfapi.connectStatus():
        name=name.upper()
        # 创建字符串
        name_byte=name.encode('utf-8')
        # 创建byte
        data_byte = None
        data_byte = get_byte_by_type(value,type.upper())
        # 写值
        error_code=dsfapi.writeValue(name_byte,data_byte,len(data_byte))
        # 验证写入的值
        isWrited=True
        if type.upper()=="UDT":
            isWrited=verify_write_struct(dsfapi,name,value)
        else:
            isWrited=verify_write(dsfapi,name,type,value)
        # 返回结果
        if error_code==0 and isWrited:
            print(f"信号 {name} 写入成功!")
            result=True
        else:
            print(f"信号 {name} 写入失败!")
    else:
        print("dsfdataservice未连接")

    return result

# 读值
# param1 dsfapi动态库对象
# param2 tag名
# param3 类型
def read_value(dsfapi,name: str,type: str):
    value=None
    if dsfapi.connectStatus():
        name=name.upper()
        # 创建字符串
        name_byte=name.encode('utf-8')
        # 创建buffer
        value_byte = create_string_buffer(b'\x00', 1024)
        # 读取
        result=dsfapi.readValue(name_byte,data_type_convert(type.upper()),value_byte)
        if result==0:
            value=value_byte.value.decode('utf-8')
        else:
            print(f"信号 {name} 读取失败!")
    else:
        print("dsfdataservice未连接")

    return value
