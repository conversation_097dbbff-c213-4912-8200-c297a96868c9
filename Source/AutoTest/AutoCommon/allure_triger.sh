#!/bin/bash

# Jenkins 服务器信息
JENKINS_URL="http://10.25.111.139:8080/"
JENKINS_USERNAME="admin"
JENKINS_API_TOKEN="1163ec86dd379cb275fe82e3b10f8c2297"
TOKEN=dsftoken

# 获取 CSRF crumb 值
CRUMB=$(curl -s -u "$JENKINS_USERNAME:$JENKINS_API_TOKEN" "$JENKINS_URL/crumbIssuer/api/xml?xpath=concat(//crumbRequestField,\":\",//crumb)")
# 提取 crumb 值
CRUMB_VALUE=$(echo "$CRUMB" | awk -F':' '{print $2}')
# 执行 Jenkins 远程构建
curl -X POST "$JENKINS_URL/job/DSF_AUTO_TEST/build?token=$TOKEN" \
  --user "$JENKINS_USERNAME:$JENKINS_API_TOKEN" \
  --header "Jenkins-Crumb: $CRUMB_VALUE"

if [ $? -eq 0 ]; then
  echo "<PERSON> job has been triggered successfully."
else
  echo "Failed to trigger <PERSON> job."
fi