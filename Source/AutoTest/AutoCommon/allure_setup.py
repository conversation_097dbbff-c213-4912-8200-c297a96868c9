import allure
import requests
from requests.auth import HTTPBasicAuth
import json
from functools import wraps
import logging
jirahost = "http://*************:8080"
jiraapi = jirahost + "/rest/api/2/issue/"
def get_jira_summary(jirakey):
    url = jiraapi + jirakey
    #用户名密码认证
    auth = HTTPBasicAuth("chenjiankun", "chenjiankun")
   
    headers = {
        "Accept": "application/json"
    }

    response = requests.request(
        "GET",
        url,
        headers=headers,
        auth=auth
    )
    # 打印响应状态码和响应内容
    print(f"Response status code: {response.status_code}")
    # print(f"Response text: {response.text}")
    result = json.loads(response.text)
    summary = iscontainedin_key = None
    try:
        summary = result["fields"]["summary"]
        iscontainedin_key = None
        for item in result["fields"]["issuelinks"]:
            if "inwardIssue" in item:
                iscontainedin_key = item["inwardIssue"]["key"]
    except Exception as e:
        print("Exception : {}".format(str(e.__str__).split(" ")[3]), ":", e,":jira key ", jirakey, ": jira api result is ", result)
    return summary, iscontainedin_key

def allure_setup(jirakey, is_async=False):
    def actual_decorator(func):
        title = story = feature = epic = iscontainedin_key = None
        title, iscontainedin_key = get_jira_summary(jirakey)
        if iscontainedin_key:
            story, iscontainedin_key = get_jira_summary(iscontainedin_key)
        if iscontainedin_key:
            feature, iscontainedin_key = get_jira_summary(iscontainedin_key)
        if iscontainedin_key:
            epic, iscontainedin_key = get_jira_summary(iscontainedin_key)

        if is_async:
            @allure.issue(jirahost + "/browse/" + jirakey, name = jirakey)
            @allure.title(title)
            @allure.story(story)
            @allure.feature(feature)
            @allure.epic(epic)
            #@wraps(func)
            async def funcwrapper(*args, **kwargs):
                return await func(*args, **kwargs)
            return funcwrapper
        else:
            @allure.issue(jirahost + "/browse/" + jirakey, name = jirakey)
            @allure.title(title)
            @allure.story(story)
            @allure.feature(feature)
            @allure.epic(epic)
           # @wraps(func)
            def funcwrapper(*args, **kwargs):
                return func(*args, **kwargs)
            return funcwrapper

    return actual_decorator
