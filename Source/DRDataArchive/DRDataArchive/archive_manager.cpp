

#include "archive_manager.h"
#include "archive_db_ihdb.h"
#include "common/CVLog.h"
#include "common/cvcomm.hxx"
#include <filesystem>
#include <fstream>
// #include <openssl/md5.h>
#include <regex>
#include <set>

extern CCVLog g_DataArchiveLog;
namespace fs = std::filesystem;
const int32 BATCH_CNT = 5000;

bool CheckFilesEqual(const std::string &file1, const std::string &file2)
{
    std::ifstream f1(file1, std::ios::binary);
    std::ifstream f2(file2, std::ios::binary);

    return std::equal(std::istreambuf_iterator<char>(f1), std::istreambuf_iterator<char>(),
                      std::istreambuf_iterator<char>(f2));
}
CArchiveManager::CArchiveManager(const int32 nHisTagNumber) : m_nHisTagNumber(nHisTagNumber)
{
}

CArchiveManager::~CArchiveManager()
{
    Shutdown();
}

void CArchiveManager::GetRmStatusThreadCallback()
{
    pthread_setname_np(pthread_self(), "rm_status");
    while (!m_bStop.load())
    {
        long nRmStatus = 0;
        auto nRes = GetRMStatus(&nRmStatus);
        if (nRes != 0)
        {
            CV_ERROR(g_DataArchiveLog, nRes, "GetRMStatus failed");
        }
        else if (nRmStatus != m_nRmStatus)
        {
            CV_INFO(g_DataArchiveLog, "m_nRmStatus changed : %ld -> %ld", m_nRmStatus, nRmStatus);
            m_nRmStatus = nRmStatus;
        }

        // Asynchronous execution once
        // I am too lazy to start another thread, so I just leave it here to execute
        if (m_bBackupFileFlag.load())
        {
            BackupConfigFile();
            m_bBackupFileFlag.store(false);
        }

        std::this_thread::sleep_for(std::chrono::milliseconds(300));
    }
}

void CArchiveManager::BackupConfigFile()
{
    CV_INFO(g_DataArchiveLog, "BackupConfigFile start,m_nOldFileFlag=%d!", m_nOldFileFlag.load());
    if (1 == m_nOldFileFlag.load())
        return; // the same with the old file

    try
    {
        std::string timestamp = dsfapi::CurrentTimestampMicro(1);

        std::string backupFilePath = m_strConfigFileNameOld;
        backupFilePath.insert(backupFilePath.find_last_of('.'), "_" + timestamp);

        if (fs::exists(m_strConfigFileNameOld))
        {
            fs::rename(m_strConfigFileNameOld, backupFilePath);
            CV_INFO(g_DataArchiveLog, "Renamed old file to: %s", backupFilePath.c_str());
        }

        fs::path newFile(m_strConfigFileName);
        fs::path oldFileDir = fs::path(m_strConfigFileNameOld).parent_path();
        fs::path targetPath = oldFileDir / newFile.filename();

        if (!fs::exists(oldFileDir))
        {
            fs::create_directories(oldFileDir);
        }

        fs::copy(newFile, targetPath, fs::copy_options::overwrite_existing);
        CV_INFO(g_DataArchiveLog, "Copied new file to: %s", targetPath.c_str());

        //  clean up old file
        const int32 maxFiles = 30; // Not a lot, right~~
        const std::regex backupRegex(R"(dsf_archive_tags_(\d{14})\.json)");
        std::vector<std::pair<std::string, fs::path>> backupFiles;

        for (const auto &entry : fs::directory_iterator(oldFileDir))
        {
            if (!fs::is_regular_file(entry.path()))
                continue;

            std::smatch match;
            std::string filename = entry.path().filename().string();
            if (std::regex_match(filename, match, backupRegex))
            {
                backupFiles.emplace_back(match[1].str(), entry.path());
            }
        }

        std::sort(backupFiles.begin(), backupFiles.end(),
                  [](const auto &a, const auto &b) { return a.first > b.first; });

        while (backupFiles.size() > maxFiles)
        {
            fs::remove(backupFiles.back().second);
            CV_INFO(g_DataArchiveLog, "Deleted old backup: %s", backupFiles.back().second.c_str());
            backupFiles.pop_back();
        }
    }
    catch (const fs::filesystem_error &e)
    {
        CV_INFO(g_DataArchiveLog, "Filesystem error: %s", e.what());
    }
}

int32 CArchiveManager::Init()
{
    int32 nRet = 0;
    m_pDBConnectInfo = std::make_shared<DBConnectInfo>();
    m_pDBLoginInfo = std::make_shared<DBLoginInfo>();
    m_pGetRmStatusThread.reset(new std::thread(&CArchiveManager::GetRmStatusThreadCallback, this));
    // 0 get config
    const char *pCVCfgPath = CCVComm::GetInstance().GetCVProjCfgPath();
    const char *pDataPath = CCVComm::GetInstance().GetProjectDataPath();
    if (NULL == pCVCfgPath || NULL == pDataPath)
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_PUB_COMMON_ERROR, "GetCVProjCfgPath  or GetProjectDataPath failed");
        return EC_DSF_PUB_COMMON_ERROR;
    }
    m_strConfigFileName = std::string(pCVCfgPath) + std::string("/") + std::string("dsf_archive_tags.json");
    m_strConfigFileNameOld =
        std::string(pDataPath) + std::string("/ihdb_archive/") + std::string("dsf_archive_tags.json");
    // load config
    try
    {
        nRet = LoadConfig();
        if (nRet != 0)
        {
            if (nRet == EC_DSF_ARCH_OPEN_NOT_EXISTS)
            { // not finish process when config file not found
                CV_WARN(g_DataArchiveLog, nRet, "LoadConfig file not exist");
                return 0; // do not continue
            }
            else
            {
                CV_ERROR(g_DataArchiveLog, nRet, "LoadConfig failed");
                return nRet;
            }
        }

        nRet = LoadConfigOld();
        if (nRet != 0)
        {
            if (nRet == EC_DSF_ARCH_OPEN_NOT_EXISTS)
            {
                CV_WARN(g_DataArchiveLog, nRet, "LoadConfigOld file not exist");
            }
            else
            {
                CV_ERROR(g_DataArchiveLog, nRet, "LoadConfigOld failed");
                return nRet;
            }
        }
    }
    catch (const std::exception &e)
    {
        CV_ERROR(g_DataArchiveLog, -1, "LoadConfig failed,exception:%s", e.what());
        return -1;
    }
    catch (...)
    {
        CV_ERROR(g_DataArchiveLog, -1, "LoadConfig failed,unknow exception");
        return -1;
    }

    // init dsfapi
    m_pContext = DrsdkInit();
    if (nullptr == m_pContext)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "DrsdkInit failed");
        return -1;
    }

    // connect  db
    m_pArchiveDb = std::make_unique<CArchiveDBIhdb>();
    m_pArchiveDb->SetRmStatusCallback(std::bind(&CArchiveManager::RmStatus, this));
    m_pArchiveDb->SetBackupFileCallback([this]() { m_bBackupFileFlag.store(true); });
    m_pArchiveDb->SetTagInfoComp(&m_mapTagInfoComp, &m_mapTagInfoCompOld);
    // retry connect and login
    std::atomic<bool> bFirstConnect = {true};
    while (true)
    {
        if (!bFirstConnect.exchange(false))
        {
            m_pArchiveDb->Disconnect();
            std::this_thread::sleep_for(std::chrono::seconds(3));
        }
        nRet = m_pArchiveDb->Connect(m_pDBConnectInfo);
        if (nRet != 0)
        {
            CV_ERROR(g_DataArchiveLog, nRet, "m_pArchiveDb->Connect failed");
            // return nRet;
            continue;
        }
        nRet = m_pArchiveDb->Login(m_pDBLoginInfo);
        if (nRet != 0)
        {
            CV_ERROR(g_DataArchiveLog, nRet, "m_pArchiveDb->Login failed");
            // return nRet;
            continue;
        }
        // all success
        break;
    }
    m_bInit.store(true);
    return 0;
}
int32 CArchiveManager::Start()
{
    if (!m_bInit.load())
    {
        CV_ERROR(g_DataArchiveLog, -1, "init not success!");
        return 0; // not
    }
    // register tag groups to dsfapi
    CV_INFO(g_DataArchiveLog, "RegisterAllToDrsdk start!,size=%d", m_mapTagInfoComp.size());
    int32 nRet = RegisterAllToDrsdk();
    if (nRet != 0)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "RegisterAllToDrsdk failed");
        return nRet;
    }
    return 0;
}

int32 CArchiveManager::Shutdown()
{
    m_bStop.store(true);

    dsfapi::DR_Uninit(m_pContext);
    dsfapi::Free_DR_Sdk_Context(&m_pContext);

    if (nullptr != m_pGetRmStatusThread && m_pGetRmStatusThread->joinable())
    {
        m_pGetRmStatusThread->join();
        m_pGetRmStatusThread.reset();
    }

    if (nullptr != m_pArchiveDb)
    {
        m_pArchiveDb->Disconnect();
    }
    return 0;
}

dsfapi::DRSdkContext *CArchiveManager::DrsdkInit()
{

    uint32 nThreadPoolNum = 1;
    // 不同时间间隔，需要注册不同的批次
    // 相同时间间隔的点数过多，每个注册批次限定点数是 BATCH_CNT
    for (auto &tPair : m_mapIntervalTagInfo)
    {
        nThreadPoolNum += tPair.second.size() / BATCH_CNT;
    }
    // 不能无限多. 限定最多10个线程
    nThreadPoolNum = nThreadPoolNum < 10 ? nThreadPoolNum : 10;
    // 1.should init first
    dsfapi::DRSdkConnectParam tConnectParam;
    strncpy(tConnectParam.szServerIp, m_pDBConnectInfo->strDsfAddress.c_str(), sizeof(tConnectParam.szServerIp) - 1);
    tConnectParam.nServerPort = m_pDBConnectInfo->nDsfPort;
    tConnectParam.nConnectTimeoutMs = 1000;

    dsfapi::DRSdkOption tOption;
    tOption.nThreadPoolNum = nThreadPoolNum; // thread nums for run  register callback
    tOption.nRequestWaitTimeoutMs = 1000;

    dsfapi::DRSdkContext *pContext = nullptr;
    pContext = dsfapi::DR_Init(tConnectParam, tOption);
    if (nullptr == pContext || pContext->errorcode != 0)
    {
        CV_ERROR(g_DataArchiveLog, -1, "DR_Init failed! errorcode");
        dsfapi::Free_DR_Sdk_Context(&pContext);
        return nullptr;
    }
    return pContext;
}

int32 CArchiveManager::LoadConfig()
{
    if (!std::filesystem::exists(m_strConfigFileName) || 0 == std::filesystem::file_size(m_strConfigFileName))
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_OPEN_NOT_EXISTS, "File is emptyr or not exist: %s",
                 m_strConfigFileName.c_str());
        return EC_DSF_ARCH_OPEN_NOT_EXISTS;
    }

    std::ifstream configFile(m_strConfigFileName);
    if (!configFile.is_open())
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "Failed to open file: %s", m_strConfigFileName.c_str());
        return EC_DSF_ARCH_COMMON_ERROR;
    }
    json jConfigArray;
    configFile >> jConfigArray;
    configFile.close();

    if (jConfigArray.empty())
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "config file array is empty!: %s",
                 m_strConfigFileName.c_str());
        return EC_DSF_ARCH_OPEN_NOT_EXISTS; // as not exists;
    }
    if (!jConfigArray.is_array())
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "config file incorrect!: %s", m_strConfigFileName.c_str());
        return EC_DSF_ARCH_COMMON_ERROR;
    }

    for (auto &jConfig : jConfigArray)
    {
        int32 nRet = EC_DSF_ARCH_SUCCESS;
        nRet = LoadDBConfig(jConfig);
        if (EC_DSF_ARCH_SUCCESS != nRet)
        {
            CV_ERROR(g_DataArchiveLog, nRet, "LoadDBConfig failed");
            return nRet;
        }

        nRet = LoadTagsConfig(jConfig);
        if (EC_DSF_ARCH_SUCCESS != nRet)
        {
            CV_ERROR(g_DataArchiveLog, nRet, "LoadTagsConfig failed ,file[%s]", m_strConfigFileName);
            return nRet;
        }
        CV_INFO(g_DataArchiveLog, "LoadConfig success");
        break; // only load one config
    }
    return EC_DSF_ARCH_SUCCESS;
}

int32 CArchiveManager::LoadConfigOld()
{
    if (!std::filesystem::exists(m_strConfigFileNameOld) || 0 == std::filesystem::file_size(m_strConfigFileName))
    {
        m_nOldFileFlag = 0;
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_OPEN_NOT_EXISTS, "File  is empty or not exist: %s",
                 m_strConfigFileNameOld.c_str());
        return EC_DSF_ARCH_OPEN_NOT_EXISTS;
    }

    if (CheckFilesEqual(m_strConfigFileName, m_strConfigFileNameOld))
    {
        m_nOldFileFlag = 1;
        CV_INFO(g_DataArchiveLog, "old File is the same with new file");
        return EC_DSF_ARCH_SUCCESS;
    }
    m_nOldFileFlag = 2;
    std::ifstream configFile(m_strConfigFileNameOld);
    if (!configFile.is_open())
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "Failed to open file: %s", m_strConfigFileNameOld.c_str());
        return EC_DSF_ARCH_COMMON_ERROR;
    }
    json jConfigArray;
    configFile >> jConfigArray;
    configFile.close();

    if (jConfigArray.empty())
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "config file array is empty!: %s",
                 m_strConfigFileNameOld.c_str());
        return EC_DSF_ARCH_OPEN_NOT_EXISTS; // as not exists;
    }
    if (!jConfigArray.is_array())
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "config file incorrect!: %s",
                 m_strConfigFileNameOld.c_str());
        // return EC_DSF_ARCH_COMMON_ERROR;
        m_nOldFileFlag = 0; // Just pretend it doesn't exist
    }

    for (auto &jConfig : jConfigArray)
    {
        int32 nRet = EC_DSF_ARCH_SUCCESS;

        nRet = LoadTagsConfigOld(jConfig);
        if (EC_DSF_ARCH_SUCCESS != nRet)
        {
            CV_ERROR(g_DataArchiveLog, nRet, "LoadTagsConfigOld failed");
            return nRet;
        }
        CV_INFO(g_DataArchiveLog, "LoadTagsConfigOld success");
        break; // only load one config
    }
    return EC_DSF_ARCH_SUCCESS;
}
int32 CArchiveManager::LoadDBConfig(const json &jConfig)
{

    if (jConfig.contains("name"))
    {
        CV_INFO(g_DataArchiveLog, "name: %s", jConfig["name"].get<std::string>().c_str());
    }

    // dsf station
    if (!jConfig.contains("dsf_address") || !jConfig.contains("dsf_port"))
    {
        CV_WARN(g_DataArchiveLog, EC_DSF_ARCH_CONFIG_ERROR, "dsf connect info not complete.");
        m_pDBConnectInfo->strDsfAddress = "127.0.0.1";
        m_pDBConnectInfo->nDsfPort = 1234;
    }
    else
    {
        m_pDBConnectInfo->strDsfAddress = jConfig["dsf_address"];
        m_pDBConnectInfo->nDsfPort = jConfig["dsf_port"];
    }

    // db info
    if (!jConfig.contains("address") || !jConfig.contains("port") || !jConfig.contains("timeout"))
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "address,port or timeout not found");
        return EC_DSF_ARCH_COMMON_ERROR;
    }
    else
    {
        m_pDBConnectInfo->strAddress = jConfig["address"];
        m_pDBConnectInfo->nPort = jConfig["port"];
        m_pDBConnectInfo->nTimeoutSec = jConfig["timeout"];
    }

    if (jConfig.contains("bak_address"))
    {
        m_pDBConnectInfo->strBakAddress = jConfig["bak_address"];
    }

    if (jConfig.contains("bak_port"))
    {
        m_pDBConnectInfo->nBakPort = jConfig["bak_port"];
    }

    if (!jConfig.contains("username") || !jConfig.contains("password"))
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "username or username not found");
        return EC_DSF_ARCH_COMMON_ERROR;
    }
    else
    {
        m_pDBLoginInfo->strUserName = jConfig["username"];
        m_pDBLoginInfo->strPassword = jConfig["password"];
    }
    return EC_DSF_ARCH_SUCCESS;
}

int32 CArchiveManager::CheckSupportType(const std::string &strType)
{

    static std::set<std::string> SUPPORT_TYPE = {"BOOL", "BYTE", "SINT", "USINT", "CHAR",  "INT",   "UINT",
                                                 "WORD", "TIME", "DINT", "REAL",  "LREAL", "STRING"};
    return SUPPORT_TYPE.count(strType) > 0;
}

int32 CArchiveManager::LoadTagsConfig(const json &jConfig)
{
    m_mapIntervalTagInfo.clear();
    m_mapTagInfo.clear();
    m_mapTagInfoComp.clear();

    if (!jConfig.contains("tags"))
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "tags not found");
        return EC_DSF_ARCH_COMMON_ERROR;
    }
    auto jConfigArray = jConfig["tags"];
    if (!jConfigArray.is_array())
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "config file incorrect,tags is note array or  empty!: %s",
                 m_strConfigFileName.c_str());
        return EC_DSF_ARCH_COMMON_ERROR;
    }

    uint32 nTagNum = 0;
    ArchiveTagInfoPtr pTagInfo;
    for (const auto &tag : jConfigArray)
    {
        if (m_mapTagInfo.size() >= m_nHisTagNumber)
        {
            CV_WARN(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR,
                    "tag number is too large, m_mapTagInfo.size[%d],m_nHisTagNumber[%d]", m_mapTagInfo.size(),
                    m_nHisTagNumber);
            return EC_DSF_ARCH_SUCCESS;
        }

        ++nTagNum;
        if (!tag.contains("tagname") || !tag.contains("type") || !tag.contains("archive_tagname") ||
            !tag.contains("annotation") || !tag.contains("compress_deviation") || !tag.contains("compress_max_time") ||
            !tag.contains("interval") || !tag.contains("after_changes"))
        {
            CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "tag config illegal, num %d", nTagNum);
            return EC_DSF_ARCH_COMMON_ERROR;
        }

        pTagInfo = std::make_shared<ArchiveTagInfo>();
        pTagInfo->strTagname = tag["tagname"];
        pTagInfo->strType = tag["type"];
        pTagInfo->strArchiveTagname = tag["archive_tagname"];
        pTagInfo->strAnnotation = tag["annotation"];
        pTagInfo->fCompDev = tag["compress_deviation"];
        pTagInfo->nCompMaxTime = tag["compress_max_time"];
        pTagInfo->nIntervalMs = tag["interval"];
        pTagInfo->after_changes = tag["after_changes"];

        if (!CheckSupportType(pTagInfo->strType))
        {
            CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "tag pTagInfo->strType[%s] not support, num %d",
                     pTagInfo->strType.c_str(), nTagNum);
            continue;
        }

        if (0 == pTagInfo->nIntervalMs)
        {
            CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "tag interval is 0, num %d", nTagNum);
            return EC_DSF_ARCH_COMMON_ERROR;
        }

        auto it = m_mapIntervalTagInfo.find(pTagInfo->nIntervalMs);
        if (it == m_mapIntervalTagInfo.end())
        {
            ArchiveTagInfoPtrVec vecTagInfo;
            m_mapIntervalTagInfo.emplace(pTagInfo->nIntervalMs, vecTagInfo);
        }
        m_mapIntervalTagInfo[pTagInfo->nIntervalMs].emplace_back(pTagInfo);
        m_mapTagInfo.emplace(std::make_pair(pTagInfo->strTagname, pTagInfo));
        m_mapTagInfoComp.emplace(std::make_pair(pTagInfo->strTagname, pTagInfo));
    }
    CV_INFO(g_DataArchiveLog, "LoadTagsConfig success, m_mapIntervalTagInfo.size = %d", m_mapIntervalTagInfo.size());
    CV_INFO(g_DataArchiveLog, "LoadTagsConfig success, m_mapTagInfo.size = %d", m_mapTagInfo.size());
    if (m_mapIntervalTagInfo.size() > 10)
    {
        CV_WARN(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR,
                "m_mapIntervalTagInfo.size more than 10,will be limited in dsfapi thread_pool");
    }

    return EC_DSF_ARCH_SUCCESS;
}

int32 CArchiveManager::LoadTagsConfigOld(const json &jConfig)
{
    m_mapTagInfoCompOld.clear();
    if (!jConfig.contains("tags"))
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "tags not found");
        return EC_DSF_ARCH_COMMON_ERROR;
    }

    auto jConfigArray = jConfig["tags"];
    if (!jConfigArray.is_array())
    {
        CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "config file incorrect,tags is note array or  empty!: %s",
                 m_strConfigFileName.c_str());
        return EC_DSF_ARCH_COMMON_ERROR;
    }

    uint32 nTagNum = 0;
    ArchiveTagInfoPtr pTagInfo;
    for (const auto &tag : jConfigArray)
    {
        ++nTagNum;
        if (!tag.contains("tagname") || !tag.contains("type") || !tag.contains("archive_tagname") ||
            !tag.contains("annotation") || !tag.contains("compress_deviation") || !tag.contains("compress_max_time") ||
            !tag.contains("interval") || !tag.contains("after_changes"))
        {
            CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "tag config illegal, num %d", nTagNum);
            return EC_DSF_ARCH_COMMON_ERROR;
        }

        pTagInfo = std::make_shared<ArchiveTagInfo>();
        pTagInfo->strTagname = tag["tagname"];
        pTagInfo->strType = tag["type"];
        pTagInfo->strArchiveTagname = tag["archive_tagname"];
        pTagInfo->strAnnotation = tag["annotation"];
        pTagInfo->fCompDev = tag["compress_deviation"];
        pTagInfo->nCompMaxTime = tag["compress_max_time"];
        pTagInfo->nIntervalMs = tag["interval"];
        pTagInfo->after_changes = tag["after_changes"];

        if (!CheckSupportType(pTagInfo->strType))
        {
            CV_ERROR(g_DataArchiveLog, EC_DSF_ARCH_COMMON_ERROR, "tag pTagInfo->strType[%s] not support, num %d",
                     pTagInfo->strType.c_str(), nTagNum);
            continue;
        }

        m_mapTagInfoCompOld.emplace(std::make_pair(pTagInfo->strTagname, pTagInfo));
    }

    CV_INFO(g_DataArchiveLog, "LoadTagsConfigOld success, m_mapTagInfoCompOld.size = %d", m_mapTagInfoCompOld.size());

    return EC_DSF_ARCH_SUCCESS;
}

int32 CArchiveManager::RegisterAllToDrsdk()
{
    bool bRetryFlag = true;
    while (bRetryFlag && !m_bStop.load())
    {
        bRetryFlag = false;
        for (auto &item : m_mapIntervalTagInfo)
        {
            if (m_mapRegFlag.find(item.first) != m_mapRegFlag.end())
            {
                if (m_mapRegFlag[item.first] == true)
                {
                    CV_WARN(g_DataArchiveLog, -1, "interval:%d has been registered", item.first);
                    continue;
                }
            }
            int nRet = RegisterTagsToDrsdk(item.first, item.second);
            if (nRet != 0)
            {
                bRetryFlag = true;
                CV_ERROR(g_DataArchiveLog, nRet, "RegisterTagsToDrsdk failed, interval:%d", item.first);
            }
            m_mapRegFlag[item.first] = (0 == nRet) ? true : false;
        }

        std::this_thread::sleep_for(std::chrono::seconds(2));
    }

    return 0;
}

int32 CArchiveManager::RegisterTagsToDrsdk(const int32 nInterval, const ArchiveTagInfoPtrVec &vecTagInfo)
{
    const int32 nTagSize = vecTagInfo.size();

    const char *pTagName[nTagSize] = {0};
    int i = 0;
    for (auto &item : vecTagInfo)
    {
        pTagName[i++] = item->strTagname.c_str();
    }

    auto func = [this](const int32 nBatchId, dsfapi::TagRecord *pTagRecord, int32 *pErrorCode, const int32 nTagCount) {
        CV_INFO(g_DataArchiveLog, "callback called. count=%d, batch_id=%d,m_bStop=%s", nTagCount, nBatchId,
                m_bStop.load() ? "true" : "false")
        if (m_bStop.load())
        {
            return;
        }
        if (m_nRmStatus != RM_STATUS_ACTIVE)
        {
            CV_WARN(g_DataArchiveLog, -1,
                    "callback will return. INACTIVE or UNAVALIBLE... count=%d, batch_id=%d, m_nRmStatus=%d", nTagCount,
                    nBatchId, m_nRmStatus)
            return;
        }
        SaveTagValues(nBatchId, pTagRecord, pErrorCode, nTagCount);
        // Must be released
        dsfapi::Free_Int32_Ptr(&pErrorCode);
        dsfapi::Free_Tag_Record(&pTagRecord);
    };
    int32 nPos = 0;
    for (int i = 1; i <= nTagSize; ++i) // begin from 1
    {
        if (0 == i % BATCH_CNT)
        {
            int32 nBatchId = 0;
            int32 nRet = dsfapi::DR_Register_Tag(m_pContext, (const char **)(pTagName + nPos), BATCH_CNT, nInterval,
                                                 func, 0, &nBatchId);
            if (nRet != 0)
            {
                CV_ERROR(g_DataArchiveLog, nRet, "DR_Register_Tag failed, nRet:%d", nRet);
                return nRet;
            }
            else
            {
                CV_INFO(g_DataArchiveLog, "DR_Register_Tag success, batch_id =%d, cnt=%d", nBatchId, BATCH_CNT);
            }
            nPos = i;
        }
    }
    auto nLeft = nTagSize - nPos;
    if (nLeft > 0)
    {
        int32 nBatchId = 0;
        int32 nRet =
            dsfapi::DR_Register_Tag(m_pContext, (const char **)(pTagName + nPos), nLeft, nInterval, func, 0, &nBatchId);
        if (nRet != 0)
        {
            CV_ERROR(g_DataArchiveLog, nRet, "DR_Register_Tag failed, nRet:%d", nRet);
            return nRet;
        }
        else
        {
            CV_INFO(g_DataArchiveLog, "DR_Register_Tag success, batch_id =%d, cnt=%d", nBatchId, nLeft);
        }
    }
    return 0;
}

void CArchiveManager::SaveTagValues(const int32 nBatchId, dsfapi::TagRecord *pTagRecord, int32 *pErrorCode,
                                    const int32 nTagCount)
{
    ArchiveTagInfoPtrVec vecTagInfo;
    std::vector<dsfapi::TagValue *> vecTagValue;
    for (int i = 0; i < nTagCount; i++)
    {
        if (0 != pErrorCode[i])
        {
            CV_DEBUG(g_DataArchiveLog, "pErrorCode !=0. errorCode:%d, tagname:%s,tagvalue:%s", pErrorCode[i],
                     pTagRecord[i].tTagName.Buffer(), pTagRecord[i].tTagValue.Buffer());
            continue;
        }

        std::string strTagName(pTagRecord[i].tTagName.Buffer(), pTagRecord[i].tTagName.Length());
        if (m_mapTagInfo.find(strTagName) != m_mapTagInfo.end())
        {
            vecTagInfo.push_back(m_mapTagInfo[strTagName]);
            vecTagValue.push_back(&pTagRecord[i].tTagValue);
        }
        else
        {
            CV_WARN(g_DataArchiveLog, -1, "strTagName is not found. tagname:%s ", strTagName.c_str());
            continue;
        }
    }
    int nSaveCount = vecTagInfo.size();
    if (nSaveCount > 0)
    {
        m_pArchiveDb->SaveSnapshots(nBatchId, vecTagInfo, (std::vector<void *> &)vecTagValue);
    }
}
