/**
 * Filename        archive_db_ihdb.cpp
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description     define the interface of iHybirDB archive database
 *
 * Author          wuzheqiang
 * Version         11/06/2024    wuzheqiang    Initial Version
 **************************************************************/

#include "archive_db_ihdb.h"
#include "common/CVLog.h"
#include "common/TypeCast.h"
#include "dsfapi.h"
#include <cmath>
#include <unordered_map>
#include <unordered_set>
#include <vector>

extern CCVLog g_DataArchiveLog; // log

const uint16 DEFAULT_RECORD_QUALITY = 192;
#define MAX_QUERY_ITEM_NUM 80000

struct FlagGuard
{
    std::atomic<bool> &flag;
    FlagGuard(std::atomic<bool> &flag) : flag(flag)
    {
        flag.store(true);
    }
    ~FlagGuard()
    {
        flag.store(false);
    }
};

struct TypeTransfer
{
    HD3_TAG_TYPE type = HD3_TAG_TYPE_MIN;           // true size
    HD3_TAG_TYPE hd_expand_type = HD3_TAG_TYPE_MIN; // ihd expand size

    TypeTransfer(HD3_TAG_TYPE _type) : type(_type), hd_expand_type(_type)
    {
    }
    TypeTransfer(HD3_TAG_TYPE _type, HD3_TAG_TYPE _hd_expand_type) : type(_type), hd_expand_type(_hd_expand_type)
    {
    }
};

std::unordered_map<std::string, TypeTransfer> gMapHD3types = {
    // The unsigned type( and word) is not supported. Expand length
    // 1byte
    {"BOOL", {HD3_TAG_TYPE_DIGITAL}},
    {"SINT", {HD3_TAG_TYPE_INT8}},
    {"CHAR", {HD3_TAG_TYPE_INT8}},                     //
    {"BYTE", {HD3_TAG_TYPE_INT8, HD3_TAG_TYPE_INT16}}, //
    {"USINT", {HD3_TAG_TYPE_INT8, HD3_TAG_TYPE_INT16}},

    // 2bytes
    {"INT", {HD3_TAG_TYPE_INT16}},
    {"UINT", {HD3_TAG_TYPE_INT16, HD3_TAG_TYPE_INT32}},
    {"WORD", {HD3_TAG_TYPE_INT16, HD3_TAG_TYPE_INT32}}, //
    // 4bytes
    {"TIME", {HD3_TAG_TYPE_INT32}}, //
    {"DINT", {HD3_TAG_TYPE_INT32}},
    // {"UDINT", {HD3_TAG_TYPE_INT32, HD3_TAG_TYPE_FLOAT64}},
    // {"DWORD", {HD3_TAG_TYPE_INT32, HD3_TAG_TYPE_FLOAT64}}, //
    // // 8bytes
    // {"LTIME", {HD3_TAG_TYPE_FLOAT64}},
    // {"LINT", {HD3_TAG_TYPE_FLOAT64}},  //
    // {"ULINT", {HD3_TAG_TYPE_FLOAT64}}, //
    // {"LWORD", {HD3_TAG_TYPE_FLOAT64}}, //
    // float
    {"REAL", {HD3_TAG_TYPE_FLOAT32}},
    {"LREAL", {HD3_TAG_TYPE_FLOAT64}},
    // others
    {"STRING", {HD3_TAG_TYPE_STRING}}, //

};

using HandlerFunc = std::function<void(dsfapi::TagValue *, HD3Record &)>;
static const std::unordered_map<std::string, HandlerFunc> gTypeHandlers = {
    {"BOOL", [](dsfapi::TagValue *value,
                HD3Record &record) { record.value.nDigital = *reinterpret_cast<int8 *>(value->Buffer()); }},
    {"CHAR", [](dsfapi::TagValue *value,
                HD3Record &record) { record.value.nInt8 = *reinterpret_cast<int8 *>(value->Buffer()); }},
    {"SINT", [](dsfapi::TagValue *value,
                HD3Record &record) { record.value.nInt8 = *reinterpret_cast<int8 *>(value->Buffer()); }},
    {"BYTE",
     [](dsfapi::TagValue *value, HD3Record &record) {
         record.value.nInt16 = static_cast<int16>(*reinterpret_cast<uint8 *>(value->Buffer())); // expand
     }},
    {"USINT",
     [](dsfapi::TagValue *value, HD3Record &record) {
         record.value.nInt16 = static_cast<int16>(*reinterpret_cast<uint8 *>(value->Buffer())); // expand
     }},
    {"INT", [](dsfapi::TagValue *value,
               HD3Record &record) { record.value.nInt16 = *reinterpret_cast<int16 *>(value->Buffer()); }},
    {"UINT",
     [](dsfapi::TagValue *value, HD3Record &record) {
         record.value.nInt32 = static_cast<int32>(*reinterpret_cast<uint16 *>(value->Buffer())); // expand
     }},
    {"WORD",
     [](dsfapi::TagValue *value, HD3Record &record) {
         record.value.nInt32 = static_cast<int32>(*reinterpret_cast<uint16 *>(value->Buffer())); // expand
     }},
    {"TIME", [](dsfapi::TagValue *value,
                HD3Record &record) { record.value.nInt32 = *reinterpret_cast<int32 *>(value->Buffer()); }},
    {"DINT", [](dsfapi::TagValue *value,
                HD3Record &record) { record.value.nInt32 = *reinterpret_cast<int32 *>(value->Buffer()); }},
    {"REAL", [](dsfapi::TagValue *value,
                HD3Record &record) { record.value.fFloat32 = *reinterpret_cast<float32 *>(value->Buffer()); }},
    {"LREAL", [](dsfapi::TagValue *value,
                 HD3Record &record) { record.value.fFloat64 = *reinterpret_cast<float64 *>(value->Buffer()); }},
    {"STRING",
     [](dsfapi::TagValue *value, HD3Record &record) {
         record.value.strBlob.pBuf = new char[value->Length() + 1];
         cvcommon::Safe_StrNCopy(record.value.strBlob.pBuf, value->Buffer(), value->Length() + 1);
         record.value.strBlob.nLenBuf = strlen(record.value.strBlob.pBuf) + 1;
     }},
};

int32 CArchiveDBIhdb::Connect(const DBConnectInfoPtr &tConnectInfoPtr)
{
    int32 nRet = EC_DSF_ARCH_SUCCESS;
    HD3Connection conn;
    memset(&conn, 0, sizeof(conn));

    strncpy(conn.szAddress, tConnectInfoPtr->strAddress.c_str(), sizeof(conn.szAddress) - 1);
    conn.nPort = tConnectInfoPtr->nPort;
    if (!tConnectInfoPtr->strBakAddress.empty() && 0 != tConnectInfoPtr->nBakPort)
    {
        strncpy(conn.szBakAddress, tConnectInfoPtr->strBakAddress.c_str(), sizeof(conn.szBakAddress) - 1);
        conn.nBakPort = tConnectInfoPtr->nBakPort;
    }
    conn.nTimeout = tConnectInfoPtr->nTimeoutSec;

    nRet = nt3_connect(&conn);
    if (nRet != EC_DSF_ARCH_SUCCESS)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "connect to server failed, error code[%d]!", nRet);
        return nRet;
    }

    CV_INFO(g_DataArchiveLog, "connect to server successful!");
    return nRet;
}

int32 CArchiveDBIhdb::Disconnect()
{
    m_bStop.store(true);
    m_CheckCv.notify_all();

    int32 nRet = nt3_disconnect();
    if (nRet != EC_DSF_ARCH_SUCCESS)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "disconnect to server failed, error code [%d]!", nRet);
        return nRet;
    }

    CV_INFO(g_DataArchiveLog, "disconnect to server successful!");
    return nRet;
}

int32 CArchiveDBIhdb::Login(const DBLoginInfoPtr &tLoginInfoPtr)
{
    m_tLoginInfoPtr = tLoginInfoPtr;
    int32 nRet = sc3_login(m_tLoginInfoPtr->strUserName.c_str(), m_tLoginInfoPtr->strPassword.c_str());
    if (nRet != EC_DSF_ARCH_SUCCESS)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "login failed, error code[%d]!", nRet);
        return nRet;
    }

    // 设置一次请求的缓存大小，对使用ut3_get_item_step的接口有效，设置大小为8万
    ut3_set_request_cache_capacity(MAX_QUERY_ITEM_NUM);
    // 优化存数据的性能的
    ut3_set_api_option(HD3M_API_OPTION_DISABLE_SAVE_SNAPSHOT_CHECK_TAG_TYPE |
                       HD3M_API_OPTION_DISABLE_SAVE_SNAPSHOT_REC_SORT);
    // 系统优化：设置Query超时时间为30秒，查询完成后改回5秒
    ut3_set_request_timeout(30);
    // 设置字符集
    ut3_set_charset(HD3_UNICODE);

    {
        static std::once_flag CheckFlag;
        std::call_once(CheckFlag, [this]() {
            m_CheckThread.reset(new std::thread(&CArchiveDBIhdb::CheckTagThreadCallback, this));
            m_CheckThread->detach();
        });

        std::this_thread::sleep_for(std::chrono::seconds(1));
        m_CheckCv.notify_one(); // exec CheckTagInfo
    }

    CV_INFO(g_DataArchiveLog, "login successful!");
    return nRet;
}

int32 CArchiveDBIhdb::GetDbTags_bak(std::map<std::string, HD3CommTagProp> &mapHD3CommTagProp)
{
    int nRet = EC_DSF_ARCH_SUCCESS;
    int nTotalTagNum = 0;
    HD3FilterItemSet filterSet;
    HD3FilterItem filterItem[2];
    memset(filterItem, 0x0, sizeof(HD3FilterItem) * 2);
    filterItem[0].nItemID = HD3_TAG_COL_COMM_PROP_PARAM4;
    filterItem[0].nRelation = HD3_RELATION_EQUAL;
    strcpy(filterItem[0].szValue, "166");
    // filterItem[0].nItemID = HD3_TAG_COL_COMM_PROP_TAG_NAME;
    // filterItem[0].nRelation = HD3_RELATION_LIKE;
    // strcpy(filterItem[0].szValue, "STD.*");
    filterItem[1].nItemID = HD3_TAG_COL_COMM_PROP_CREATOR;
    filterItem[1].nRelation = HD3_RELATION_EQUAL;
    strncpy(filterItem[1].szValue, m_tLoginInfoPtr->strUserName.c_str(), sizeof(filterItem[1].szValue) - 1);

    filterSet.nSize = 2;
    filterSet.pItem = filterItem;

    // nRet = tag3_query_tag_num_cond(HD3_TAG_CLASS_BASIC, &filterSet, &nTotalTagNum);
    // if (nRet != RD_SUCCESS)
    // {
    //     CV_INFO(g_DataArchiveLog, "CheckAllTags tag3_query_tag_num_cond, error code [%d]!", nRet);
    //     return nRet;
    // }
    // CV_INFO(g_DataArchiveLog, "CheckAllTags tag3_query_tag_num_cond, nTotalTagNum [%d]!", nTotalTagNum);

    // query tags base props  by condition
    HD3HANDLE hIter = NULL;
    nRet = tag3_query_tags_cond(HD3M_ALL, &filterSet, HD3M_ALL, &hIter);
    if (nRet != RD_SUCCESS)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "tag_query_tags_cond, error code [%d]!", nRet);
        return nRet;
    }

    int32 nTagNum = 0;
    // HD3CommTagProp *pHD3CommTagProp = new HD3CommTagProp[nTotalTagNum];
    auto display = [](HD3CommTagProp &tHD3CommTagProp) {
        CV_DEBUG(g_DataArchiveLog,
                 "szTagName[%s],szDescriptor[%s],nTagType[%d],nCompType[%d],fCompDev[%f],nCompMaxTime[%d],nParam1[%u],"
                 "nParam4[%u]!",
                 tHD3CommTagProp.szTagName, tHD3CommTagProp.szDescriptor, tHD3CommTagProp.nTagType,
                 tHD3CommTagProp.nCompType, tHD3CommTagProp.fCompDev, tHD3CommTagProp.nCompMaxTime,
                 tHD3CommTagProp.nParam1, tHD3CommTagProp.nParam4);
    };
    while (true)
    {
        HD3CommTagProp tHD3CommTagProp;
        nRet = ut3_get_item_step(hIter, &tHD3CommTagProp);
        if (RD_SUCCESS == nRet)
        {
            nTagNum++;
            display(tHD3CommTagProp);
            mapHD3CommTagProp.emplace(std::make_pair(tHD3CommTagProp.szTagName, tHD3CommTagProp));
        }
        else if (EC_HD_API_QUERY_END == nRet)
        {
            CV_INFO(g_DataArchiveLog, "query tag complete!");
            nRet = RD_SUCCESS;
            break;
        }
        else
        {
            CV_INFO(g_DataArchiveLog, "query tag failed, error code [%d]!", nRet);
            break;
        }
    }

    CV_INFO(g_DataArchiveLog, "you have queried %d tags totally!", nTagNum);
    ut3_free_handle(hIter);

    return nRet;
}

int32 CArchiveDBIhdb::CheckTagInfo()
{
    int nRet = EC_DSF_ARCH_SUCCESS;
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);
    CV_INFO(g_DataArchiveLog, "CheckTagInfo begin");

    // RAII
    FlagGuard guard(m_CheckFlag);

    if (m_pRmStatusCallback && RM_STATUS_ACTIVE != m_pRmStatusCallback())
    {
        CV_WARN(g_DataArchiveLog, -1, "RmStatus is not active, ignore delete/update/add!");
        return EC_DSF_ARCH_SUCCESS;
    }

    if (nullptr == m_mapTagInfoCompPtr)
    {
        CV_WARN(g_DataArchiveLog, -1, "m_mapTagInfoCompPtr is null or empty!");
        return EC_DSF_ARCH_NULLPTR;
    }
    // 1.query db data by old config, compare with new config ,and delete/update
    CV_INFO(g_DataArchiveLog, "CheckTagInfo old");
    std::vector<HD3CommTagProp> vecHD3CommTagPropOld;
    vecHD3CommTagPropOld.clear();
    static std::atomic_flag flag = ATOMIC_FLAG_INIT;
    if (!flag.test_and_set())
    {
        // The old configuration will only be queried once,
        // and in the future, it will be considered as only new configuration
        nRet = GetDbTags(*m_mapTagInfoCompOldPtr, vecHD3CommTagPropOld);
        if (nRet != EC_DSF_ARCH_SUCCESS)
        {
            CV_ERROR(g_DataArchiveLog, nRet, "GetDbTags failed, error code[%d]!", nRet);
            return nRet;
        }
    }

    std::vector<uint32> vecDelTagId;
    vecDelTagId.clear();
    nRet = CompTags(*m_mapTagInfoCompPtr, vecHD3CommTagPropOld, vecDelTagId);
    if (nRet != EC_DSF_ARCH_SUCCESS)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "CompTags failed, error code[%d]!", nRet);
        return nRet;
    }
    // delete old tag id
    nRet = DeleteTags(vecDelTagId);
    if (nRet != EC_DSF_ARCH_SUCCESS)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "DeleteTags failed, error code[%d]!", nRet);
        return nRet;
    }
    // update oldtag attr
    nRet = UpdateTags(*m_mapTagInfoCompPtr);
    if (nRet != EC_DSF_ARCH_SUCCESS)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "UpdateTags failed, error code[%d]!", nRet);
        return nRet;
    }

    // 2.The added points this time may already exist or may need to be modified
    CV_INFO(g_DataArchiveLog, "CheckTagInfo new");
    std::map<std::string, ArchiveTagInfoPtr> mapTagInfoComp;
    mapTagInfoComp.clear();
    for (auto &item : *m_mapTagInfoCompPtr)
    {
        if (item.second->nTagID == 0 || (DB_OPER_TYPE_ADD & item.second->nOperType))
        {
            mapTagInfoComp.insert(item);
        }
    }
    std::vector<HD3CommTagProp> vecHD3CommTagProp;
    vecHD3CommTagProp.clear();
    nRet = GetDbTags(mapTagInfoComp, vecHD3CommTagProp);
    if (nRet != EC_DSF_ARCH_SUCCESS)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "GetDbTags failed, error code[%d]!", nRet);
        return nRet;
    }
    vecDelTagId.clear();
    nRet = CompTags(mapTagInfoComp, vecHD3CommTagProp, vecDelTagId);
    if (nRet != EC_DSF_ARCH_SUCCESS)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "CompTags failed, error code[%d]!", nRet);
        return nRet;
    }
    // for rebuild .. delete old
    nRet = DeleteTags(vecDelTagId);
    if (nRet != EC_DSF_ARCH_SUCCESS)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "DeleteTags failed, error code[%d]!", nRet);
        return nRet;
    }

    // update oldtag attr
    nRet = UpdateTags(mapTagInfoComp);
    if (nRet != EC_DSF_ARCH_SUCCESS)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "UpdateTags failed, error code[%d]!", nRet);
        return nRet;
    }

    nRet = AddTags(mapTagInfoComp);
    if (nRet != EC_DSF_ARCH_SUCCESS)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "AddTags failed, error code[%d]!", nRet);
        return nRet;
    }

    if (!m_mapTagInfoCompPtr->empty())
    {
        ArchiveTagInfoPtr MaxTagIDTagInfoPtr = nullptr;
        auto iter = m_mapTagInfoCompPtr->begin();
        MaxTagIDTagInfoPtr = iter->second;
        for (; iter != m_mapTagInfoCompPtr->end(); ++iter)
        {
            if (iter->second->nTagID > MaxTagIDTagInfoPtr->nTagID)
            {
                MaxTagIDTagInfoPtr = iter->second;
            }
        }
        if (nullptr != MaxTagIDTagInfoPtr)
        {
            std::lock_guard<std::mutex> lock(m_MaxTagIDMtx);
            m_MaxTagIDTagInfoPtr = MaxTagIDTagInfoPtr;
            CV_INFO(g_DataArchiveLog, "m_nMaxTagID=%u ,name=%s", m_MaxTagIDTagInfoPtr->nTagID,
                    m_MaxTagIDTagInfoPtr->strArchiveTagname.c_str());
        }
    }

    static std::atomic_flag backupFlag = ATOMIC_FLAG_INIT;
    if (!backupFlag.test_and_set()) // only once
    {
        if (nullptr != m_pBackupFileCallback)
        {
            m_pBackupFileCallback();
        }
    }
}

int32 CArchiveDBIhdb::GetDbTags(std::map<std::string, ArchiveTagInfoPtr> &mapTagInfoCompPtr,
                                std::vector<HD3CommTagProp> &vecHD3CommTagProp)
{
    // mapTagInfoCompPtr is map and order by key (name)
    // vecHD3CommTagProp are inserted in order and also sorted
    vecHD3CommTagProp.clear();
    int nRet = EC_DSF_ARCH_SUCCESS;
    int32 nSize = mapTagInfoCompPtr.size();
    if (nSize <= 0)
    {
        return EC_DSF_ARCH_SUCCESS;
    }

    // 1.get id by names
    std::shared_ptr<uint32> pTagIDs(new uint32[nSize]);
    std::shared_ptr<int32> pErrCodes(new int32[nSize]);
    // char(*pTagNames)[256] = new char[nSize][HD3_LEN_TAG_NAME * HD3_BASE];
    using TagNameRow = char[HD3_LEN_TAG_NAME * HD3_BASE];
    std::shared_ptr<TagNameRow[]> pTagNames(new TagNameRow[nSize], [](TagNameRow *ptr) { delete[] ptr; });

    memset(pTagIDs.get(), 0, nSize * sizeof(uint32));
    memset(pErrCodes.get(), 0, nSize * sizeof(int32));
    memset(pTagNames.get(), 0, nSize * sizeof(TagNameRow));

    int i = 0;
    for (auto &item : mapTagInfoCompPtr)
    {
        auto &pTagInfo = item.second;
        cvcommon::Safe_StrNCopy(pTagNames[i++], pTagInfo->strArchiveTagname.c_str(), HD3_LEN_TAG_NAME * HD3_BASE);
    }

    nRet = tag3_query_ids_by_names(nSize, pTagNames.get(), pTagIDs.get(), pErrCodes.get());
    if (RD_SUCCESS == nRet || EC_HD3_API_BATCH_REQUEST == nRet)
    {
        for (int32 j = 0; j < nSize; ++j)
        {
            if (0 != pErrCodes.get()[j] || pTagIDs.get()[j] == 0)
            {
                CV_WARN(g_DataArchiveLog, pErrCodes.get()[j],
                        "[FAIL]query tags j = %d, id = %u, error_code = %d, tagname=%s", j, pTagIDs.get()[j],
                        pErrCodes.get()[j], pTagNames[j]);
                continue;
            }
            else
            {
                CV_INFO(g_DataArchiveLog, "[SUCC]query tags j = %d, id = %u, error_code = %d, tagname=%s", j,
                        pTagIDs.get()[j], pErrCodes.get()[j], pTagNames[j]);
                HD3CommTagProp tHD3CommTagProp;
                tHD3CommTagProp.nTagID = pTagIDs.get()[j];
                vecHD3CommTagProp.emplace_back(tHD3CommTagProp);
            }
        }
        nRet = RD_SUCCESS;
        CV_INFO(g_DataArchiveLog, "query tags success! nRet = %d", nRet);
    }
    else
    {
        CV_ERROR(g_DataArchiveLog, nRet, "query tags failed! nRet = %d", nRet);
    }

    // 2.get tagprop by ids
    auto display = [](const char *sRes, HD3CommTagProp &tHD3CommTagProp, const int64 nErrCode) {
        CV_INFO(g_DataArchiveLog,
                "query[%s],szTagName[%s],nTagID[%u],szDescriptor[%s],nTagType[%d],nCompType[%d],fCompDev[%f],"
                "nCompMaxTime[%d],nParam4[%d],nErrCode[%ld]",
                sRes, tHD3CommTagProp.szTagName, tHD3CommTagProp.nTagID, tHD3CommTagProp.szDescriptor,
                tHD3CommTagProp.nTagType, tHD3CommTagProp.nCompType, tHD3CommTagProp.fCompDev,
                tHD3CommTagProp.nCompMaxTime, tHD3CommTagProp.nParam4, nErrCode);
    };
    nSize = vecHD3CommTagProp.size();
    if (nSize <= 0)
    {
        return nRet;
    }
    pErrCodes.reset(new int32[nSize]);
    memset(pErrCodes.get(), 0, nSize * sizeof(int32));
    nRet = tag3_query_tags_common_prop(HD3M_ALL, nSize, HD3M_ALL, vecHD3CommTagProp.data(), pErrCodes.get());
    if (nRet == RD_SUCCESS || EC_HD3_API_BATCH_REQUEST == nRet)
    {
        for (int32 j = 0; j < nSize; ++j)
        {
            if (0 != pErrCodes.get()[j])
            {
                display("FAIL", vecHD3CommTagProp[j], pErrCodes.get()[j]);
                continue;
            }
            else
            {
                display("SUCC", vecHD3CommTagProp[j], pErrCodes.get()[j]);
            }
        }
        nRet = RD_SUCCESS;
        CV_INFO(g_DataArchiveLog, "query tags success! nRet = %d", nRet);
    }
    else
    {
        CV_ERROR(g_DataArchiveLog, nRet, "tag_query_tags_basic_prop failed, error code [%d]!\n", nRet);
        return nRet;
    }
    return nRet;
}

int32 CArchiveDBIhdb::CompTags(std::map<std::string, ArchiveTagInfoPtr> &mapTagInfoComp,
                               std::vector<HD3CommTagProp> &vecHD3CommTagProp, std::vector<uint32> &vecDelTagId)
{
    auto iter_conf = mapTagInfoComp.begin();
    auto iter_db = vecHD3CommTagProp.begin();
    while (iter_conf != mapTagInfoComp.end() && iter_db != vecHD3CommTagProp.end())
    {
        auto conf_tag = iter_conf->second;
        conf_tag->nTagType = GetHD3TagType(conf_tag->strType, true);
        if (conf_tag->strArchiveTagname == std::string(iter_db->szTagName))
        {
            if (conf_tag->nTagType != iter_db->nTagType)
            {
                CV_INFO(g_DataArchiveLog, "[%s] will rebuild!", iter_conf->first.c_str());
                conf_tag->nOperType = DB_OPER_TYPE_REBUILD; // DB_OPER_TYPE_ADD
                vecDelTagId.push_back(iter_db->nTagID);     // delete first and add later
            }
            else if (conf_tag->fCompDev != iter_db->fCompDev || conf_tag->nCompMaxTime != iter_db->nCompMaxTime)
            {
                CV_INFO(g_DataArchiveLog, "[%s] will update!", iter_conf->first.c_str());
                conf_tag->nOperType = DB_OPER_TYPE_UPDATE;
                conf_tag->nTagID = iter_db->nTagID;
            }
            else
            {
                CV_INFO(g_DataArchiveLog, "[%s]  not change!", iter_conf->first.c_str());
                conf_tag->nOperType = DB_OPER_TYPE_NONE;
                conf_tag->nTagID = iter_db->nTagID;
            }
            ++iter_conf;
            ++iter_db;
        }
        else if (conf_tag->strArchiveTagname < std::string(iter_db->szTagName))
        {
            CV_INFO(g_DataArchiveLog, "[%s] will added!", iter_conf->first.c_str());
            conf_tag->nOperType = DB_OPER_TYPE_ADD;
            ++iter_conf;
        }
        else
        {
            CV_INFO(g_DataArchiveLog, "[%s] will deleted!", iter_db->szTagName);
            vecDelTagId.push_back(iter_db->nTagID);
            ++iter_db;
        }
    }

    while (iter_conf != mapTagInfoComp.end())
    {
        CV_INFO(g_DataArchiveLog, "[%s] will added!", iter_conf->first.c_str());
        iter_conf->second->nTagType = GetHD3TagType(iter_conf->second->strType, true);
        iter_conf->second->nOperType = DB_OPER_TYPE_ADD;
        ++iter_conf;
    }

    while (iter_db != vecHD3CommTagProp.end())
    {
        CV_INFO(g_DataArchiveLog, "[%s] will deleted!", iter_db->szTagName);
        vecDelTagId.push_back(iter_db->nTagID);
        ++iter_db;
    }
    return EC_DSF_ARCH_SUCCESS;
}
int32 CArchiveDBIhdb::DeleteTags(const std::vector<uint32> &vecDelTagId)
{
    CV_INFO(g_DataArchiveLog, "delete tag begin,size= %d", vecDelTagId.size());
    const int32 SIZE = vecDelTagId.size();
    if (SIZE <= 0)
    {
        return EC_DSF_ARCH_SUCCESS;
    }
    constexpr int32 BATCH_COUNT = 500;
    std::shared_ptr<uint32> pTagIDArray(new uint32[BATCH_COUNT]);
    std::shared_ptr<int32> pErrCodeArray(new int32[BATCH_COUNT]);
    int32 nBatch = (SIZE + BATCH_COUNT - 1) / BATCH_COUNT;
    for (int i = 0; i < nBatch; ++i)
    {
        int32 nPos = i * BATCH_COUNT;
        int32 nSize = std::min(BATCH_COUNT, SIZE - nPos);
        memset(pTagIDArray.get(), 0, sizeof(uint32) * BATCH_COUNT);
        memset(pErrCodeArray.get(), 0, sizeof(int32) * BATCH_COUNT);
        std::copy(vecDelTagId.begin() + nPos, vecDelTagId.begin() + nPos + nSize, pTagIDArray.get());
        int32 nRet = tag3_delete_tags(nSize, pTagIDArray.get(), pErrCodeArray.get());
        if (nRet != RD_SUCCESS)
        {
            CV_ERROR(g_DataArchiveLog, nRet, "delete tag failed, error code [%d]!\n", nRet);
            return nRet;
        }

        CV_INFO(g_DataArchiveLog, "delete tag end, count= %d!", nSize);
    }
    return EC_DSF_ARCH_SUCCESS;
}

int32 CArchiveDBIhdb::UpdateTags(std::map<std::string, ArchiveTagInfoPtr> &mapTagInfoComp)
{
    CV_INFO(g_DataArchiveLog, "modify tag begin");
    constexpr int32 BATCH_COUNT = 500;
    int32 nRet = 0;
    std::shared_ptr<uint32> pTagIDs(new uint32[BATCH_COUNT]);
    std::shared_ptr<int32> pErrCodes(new int32[BATCH_COUNT]);
    std::shared_ptr<HD3Mask> pHD3Mask(new HD3Mask[BATCH_COUNT]);
    std::shared_ptr<HD3PtTagProp> pHD3PtTagProps(new HD3PtTagProp[BATCH_COUNT]);

    memset(pTagIDs.get(), 0x0, sizeof(uint32) * BATCH_COUNT);
    memset(pErrCodes.get(), 0x0, sizeof(int32) * BATCH_COUNT);
    memset(pHD3Mask.get(), 0x0, sizeof(HD3Mask) * BATCH_COUNT);
    memset(pHD3PtTagProps.get(), 0x0, sizeof(HD3PtTagProp) * BATCH_COUNT);
    int32 nPos = 0;
    for (auto &item : mapTagInfoComp)
    {
        auto &pTagInfo = item.second;
        if (DB_OPER_TYPE_UPDATE != pTagInfo->nOperType)
        {
            continue;
        }

        pTagIDs.get()[nPos] = pTagInfo->nTagID;
        cvcommon::Safe_StrNCopy(pHD3PtTagProps.get()[nPos].szTagName, pTagInfo->strArchiveTagname.c_str(),
                                sizeof(pHD3PtTagProps.get()[nPos].szTagName));
        cvcommon::Safe_StrNCopy(pHD3PtTagProps.get()[nPos].szDescriptor, pTagInfo->strAnnotation.c_str(),
                                sizeof(pHD3PtTagProps.get()[nPos].szDescriptor));

        if (HD3_TAG_TYPE_DIGITAL == pTagInfo->nTagType)
        {
            cvcommon::Safe_StrNCopy(pHD3PtTagProps.get()[nPos].szDigitalSet, "DEFAULT",
                                    sizeof(pHD3PtTagProps.get()[nPos].szDigitalSet));
        }
        pHD3PtTagProps.get()[nPos].nTagType = static_cast<HD3_TAG_TYPE>(pTagInfo->nTagType);
        pHD3PtTagProps.get()[nPos].fCompDev = pTagInfo->fCompDev;
        pHD3PtTagProps.get()[nPos].nCompMaxTime = pTagInfo->nCompMaxTime;
        pHD3PtTagProps.get()[nPos].nCompType = HD3_COMP_TYPE_NO;
        if (std::fabs(pTagInfo->fCompDev) > std::numeric_limits<float>::epsilon())
        {
            pHD3PtTagProps.get()[nPos].nCompType = HD3_COMP_TYPE_SDT;
        }
        pHD3PtTagProps.get()[nPos].nParam4 = 166; // HD3M_COMM_PROP_PARAM4
        pHD3Mask.get()[nPos].nCommMask =
            HD3M_COMM_PROP_TAG_NAME | HD3M_COMM_PROP_DESCRIPTOR | HD3M_COMM_PROP_DIGITAL_SET | HD3M_COMM_PROP_TAG_TYPE |
            HD3M_COMM_PROP_COMP_TYPE | HD3M_COMM_PROP_COMP_DEV | HD3M_COMM_PROP_COMP_MAX_TIME | HD3M_COMM_PROP_PARAM4;
        pHD3Mask.get()[nPos].nExtMask = 0;

        // fetch add and judge the batch count
        if (++nPos >= BATCH_COUNT)
        {
            nRet = pt3_modify_tags_prop(nPos, pTagIDs.get(), pHD3PtTagProps.get(), pHD3Mask.get(), pErrCodes.get());
            if (nRet != RD_SUCCESS)
            {
                CV_ERROR(g_DataArchiveLog, nRet, "modify tags failed, error code [%d]!", nRet);
                return nRet;
            }
            CV_INFO(g_DataArchiveLog, "modify tag end, count= %d!", nPos);

            memset(pTagIDs.get(), 0x0, sizeof(uint32) * BATCH_COUNT);
            memset(pErrCodes.get(), 0x0, sizeof(int32) * BATCH_COUNT);
            memset(pHD3Mask.get(), 0x0, sizeof(HD3Mask) * BATCH_COUNT);
            memset(pHD3PtTagProps.get(), 0x0, sizeof(HD3PtTagProp) * BATCH_COUNT);
            nPos = 0;
        }
    }

    if (nPos > 0)
    {
        nRet = pt3_modify_tags_prop(nPos, pTagIDs.get(), pHD3PtTagProps.get(), pHD3Mask.get(), pErrCodes.get());
        if (nRet != RD_SUCCESS)
        {
            CV_ERROR(g_DataArchiveLog, nRet, "modify tags failed, error code [%d]!", nRet);
            return nRet;
        }
        CV_INFO(g_DataArchiveLog, "modify tag end, count= %d!", nPos);
    }
    return EC_DSF_ARCH_SUCCESS;
}

int32 CArchiveDBIhdb::AddTags(std::map<std::string, ArchiveTagInfoPtr> &mapTagInfoComp)
{
    CV_INFO(g_DataArchiveLog, "add tag begin");
    constexpr int32 BATCH_COUNT = 500;
    int32 nRet = 0;
    std::vector<std::string> vecTagNames;
    vecTagNames.resize(BATCH_COUNT);

    std::shared_ptr<uint32> pTagIDs(new uint32[BATCH_COUNT]);
    std::shared_ptr<int32> pErrCodes(new int32[BATCH_COUNT]);
    std::shared_ptr<HD3Mask> pHD3Mask(new HD3Mask[BATCH_COUNT]);
    std::shared_ptr<HD3PtTagProp> pHD3PtTagProps(new HD3PtTagProp[BATCH_COUNT]);

    vecTagNames.clear();
    memset(pTagIDs.get(), 0x0, sizeof(uint32) * BATCH_COUNT);
    memset(pErrCodes.get(), 0x0, sizeof(int32) * BATCH_COUNT);
    memset(pHD3Mask.get(), 0x0, sizeof(HD3Mask) * BATCH_COUNT);
    memset(pHD3PtTagProps.get(), 0x0, sizeof(HD3PtTagProp) * BATCH_COUNT);
    int32 nPos = 0;
    for (auto &item : mapTagInfoComp)
    {
        auto &pTagInfo = item.second;
        if (0 == (DB_OPER_TYPE_ADD & pTagInfo->nOperType))
        {
            continue;
        }

        vecTagNames[nPos] = pTagInfo->strTagname;
        cvcommon::Safe_StrNCopy(pHD3PtTagProps.get()[nPos].szTagName, pTagInfo->strArchiveTagname.c_str(),
                                sizeof(pHD3PtTagProps.get()[nPos].szTagName));
        cvcommon::Safe_StrNCopy(pHD3PtTagProps.get()[nPos].szDescriptor, pTagInfo->strAnnotation.c_str(),
                                sizeof(pHD3PtTagProps.get()[nPos].szDescriptor));
        if (HD3_TAG_TYPE_DIGITAL == pTagInfo->nTagType)
        {
            cvcommon::Safe_StrNCopy(pHD3PtTagProps.get()[nPos].szDigitalSet, "DEFAULT",
                                    sizeof(pHD3PtTagProps.get()[nPos].szDigitalSet));
        }
        pHD3PtTagProps.get()[nPos].nTagType = static_cast<HD3_TAG_TYPE>(pTagInfo->nTagType);
        pHD3PtTagProps.get()[nPos].fCompDev = pTagInfo->fCompDev;
        pHD3PtTagProps.get()[nPos].nCompMaxTime = pTagInfo->nCompMaxTime;
        pHD3PtTagProps.get()[nPos].nCompType = HD3_COMP_TYPE_NO;
        if (std::fabs(pTagInfo->fCompDev) > std::numeric_limits<float>::epsilon())
        {
            pHD3PtTagProps.get()[nPos].nCompType = HD3_COMP_TYPE_SDT;
        }
        pHD3PtTagProps.get()[nPos].nParam4 = 166; // HD3M_COMM_PROP_PARAM4
        pHD3Mask.get()[nPos].nCommMask =
            HD3M_COMM_PROP_TAG_NAME | HD3M_COMM_PROP_DESCRIPTOR | HD3M_COMM_PROP_DIGITAL_SET | HD3M_COMM_PROP_TAG_TYPE |
            HD3M_COMM_PROP_COMP_TYPE | HD3M_COMM_PROP_COMP_DEV | HD3M_COMM_PROP_COMP_MAX_TIME | HD3M_COMM_PROP_PARAM4;
        pHD3Mask.get()[nPos].nExtMask = 0;

        // fetch add and judge the batch count
        if (++nPos >= BATCH_COUNT)
        {
            nRet = pt3_add_tags(nPos, pHD3PtTagProps.get(), pHD3Mask.get(), "", pTagIDs.get(), pErrCodes.get());
            if (RD_SUCCESS == nRet || EC_HD3_API_BATCH_REQUEST == nRet)
            {
                for (int32 j = 0; j < nPos; ++j)
                {
                    if (0 != pErrCodes.get()[j])
                    {
                        CV_WARN(g_DataArchiveLog, j, "Tagname[%s] added failed, err code[%d], name[%s]",
                                vecTagNames[j].c_str(), pErrCodes.get()[j], pHD3PtTagProps.get()[j].szTagName)
                        continue;
                    }
                    mapTagInfoComp[vecTagNames[j]]->nTagID = pTagIDs.get()[j];
                    CV_INFO(g_DataArchiveLog, "Tagname[%s] added success, id[%d], name[%s]", vecTagNames[j].c_str(),
                            pTagIDs.get()[j], pHD3PtTagProps.get()[j].szTagName);
                }
                nRet = RD_SUCCESS;
                CV_INFO(g_DataArchiveLog, "add tag end, count= %d!", nPos);
            }
            else
            {
                CV_ERROR(g_DataArchiveLog, nRet, "add tags failed! nRet = %d", nRet);
                return nRet;
            }

            vecTagNames.clear();
            memset(pTagIDs.get(), 0x0, sizeof(uint32) * BATCH_COUNT);
            memset(pErrCodes.get(), 0x0, sizeof(int32) * BATCH_COUNT);
            memset(pHD3Mask.get(), 0x0, sizeof(HD3Mask) * BATCH_COUNT);
            memset(pHD3PtTagProps.get(), 0x0, sizeof(HD3PtTagProp) * BATCH_COUNT);
            nPos = 0;
        }
    }

    if (nPos > 0)
    {
        nRet = pt3_add_tags(nPos, pHD3PtTagProps.get(), pHD3Mask.get(), "", pTagIDs.get(), pErrCodes.get());
        if (RD_SUCCESS == nRet || EC_HD3_API_BATCH_REQUEST == nRet)
        {
            for (int32 j = 0; j < nPos; ++j)
            {
                if (0 != pErrCodes.get()[j])
                {
                    CV_WARN(g_DataArchiveLog, j, "Tagname[%s] added failed, err code[%d], name[%s]",
                            vecTagNames[j].c_str(), pErrCodes.get()[j], pHD3PtTagProps.get()[j].szTagName)
                    continue;
                }
                mapTagInfoComp[vecTagNames[j]]->nTagID = pTagIDs.get()[j];
                CV_INFO(g_DataArchiveLog, "Tagname[%s] added success, id[%d], name[%s]", vecTagNames[j].c_str(),
                        pTagIDs.get()[j], pHD3PtTagProps.get()[j].szTagName);
            }
            nRet = RD_SUCCESS;
            CV_INFO(g_DataArchiveLog, "add tag end, count= %d!", nPos);
        }
        else
        {
            CV_ERROR(g_DataArchiveLog, nRet, "add tags failed! nRet = %d", nRet);
            return nRet;
        }
    }
    return EC_DSF_ARCH_SUCCESS;
}

int32 CArchiveDBIhdb::SaveSnapshots(const int32 nBatchId, const ArchiveTagInfoPtrVec &vecTagInfo,
                                    const std::vector<void *> &vecData)
{
    int32 nRet = EC_DSF_ARCH_SUCCESS;
    const int32 nSize = vecTagInfo.size();
    if (nSize <= 0)
    {
        CV_INFO(g_DataArchiveLog, "size of vecTagInfo is 0!");
        return EC_DSF_ARCH_SUCCESS;
    }
    if (m_CheckFlag.load())
    {
        CV_INFO(g_DataArchiveLog, "CheckTagInfo is running!");
        return EC_DSF_ARCH_COMMON_ERROR;
    }
    if (!m_bSyncDone.load())
    {
        // checking  or  (checking finished and sync is not done)
        if (m_bSyncChecking.load() || !IsIHDAddTagSyncDone())
        {
            CV_INFO(g_DataArchiveLog, "IHDAddTagSync is not done!");
            return EC_DSF_ARCH_COMMON_ERROR;
        }
    }

    /*
    If there are multiple databases, then thread_local static variable is problematic.
    Unless it is ensured that different databases are on different threads: if batch count is less 10,there will be
    different thread.
    */
    thread_local static timeval last_tv = {0, 0};
    thread_local static CIhdbCache ihdbCache;

    std::lock_guard<std::mutex> lock(ihdbCache.m_mtx); // Multi batch use  one thread

    if (!ihdbCache.IsEnough(nSize))
    {
        CV_INFO(g_DataArchiveLog, "SaveSnapshots11111111111111,ihdbCache.nBatchId[%d],m_nBatchCnt[%d]", nBatchId,
                ihdbCache.m_nBatchCnt.load());
        nRet = BatchSaveSnapshots(ihdbCache);
        if (nRet != EC_DSF_ARCH_SUCCESS)
        {
            CV_ERROR(g_DataArchiveLog, nRet, "BatchSaveSnapshots failed! nRet = %d", nRet);
            return nRet;
        }
        CV_INFO(g_DataArchiveLog, "SaveSnapshot2222222222222222");
    }

    struct timeval tv;
    gettimeofday(&tv, nullptr);

    CV_DEBUG(g_DataArchiveLog,
             "m_nBatchCnt[%d],ihdbCache.m_nBatchCnt[%d],tv.tv_sec[%d],tv.tv_usec[%d],last_tv.tv_sec[%d], "
             "last_tv.tv_usec[%d] ",
             nBatchId, ihdbCache.m_nBatchCnt.load(), tv.tv_sec, tv.tv_usec, last_tv.tv_sec, last_tv.tv_usec);
    // Save is blocked, so there will be some accumulation.
    // Causing two consecutive processing within one millisecond adjust 1 ms
    if ((last_tv.tv_sec > tv.tv_sec) || (last_tv.tv_sec == tv.tv_sec && last_tv.tv_usec / 1000 >= tv.tv_usec / 1000))
    {
        tv = last_tv;
        tv.tv_usec += 1000;
        if (tv.tv_usec >= 1000000)
        {
            tv.tv_sec++;
            tv.tv_usec = 0;
        }
    }
    last_tv = tv;

    for (int32 i = 0; i < nSize; i++)
    {
        auto &pTagInfo = vecTagInfo[i];
        dsfapi::TagValue *pTagValue = (dsfapi::TagValue *)vecData[i];

        if (0 == pTagInfo->nTagID)
        {
            CV_WARN(g_DataArchiveLog, -1, "tag %s not match tagid!", pTagInfo->strTagname.c_str());
            m_CheckCv.notify_one();
            continue; // not need save
        }

        auto pos = ihdbCache.m_nBatchCnt.fetch_add(1); // fetch_add return old value
        ihdbCache.pTagIDArray[pos] = pTagInfo->nTagID;
        ihdbCache.pRecArray[pos].nTagType = static_cast<HD3_TAG_TYPE>(pTagInfo->nTagType); // ihdb type

        ihdbCache.pRecArray[pos].nSec = tv.tv_sec;
        ihdbCache.pRecArray[pos].nMsec = tv.tv_usec / 1000;
        ihdbCache.pRecArray[pos].nQuality = DEFAULT_RECORD_QUALITY;

        auto iterHandler = gTypeHandlers.find(pTagInfo->strType);
        if (iterHandler != gTypeHandlers.end())
        {
            iterHandler->second(pTagValue, ihdbCache.pRecArray[pos]);
        }
        else
        {
            CV_WARN(g_DataArchiveLog, -1, "unknown tag type %s, myber not support", pTagInfo->strType.c_str());
        }
    }

    if (!ihdbCache.ShouldSave())
    {
        CV_DEBUG(g_DataArchiveLog, "ShouldSave false,skip!");
        return EC_DSF_ARCH_SUCCESS;
    }
    CV_INFO(g_DataArchiveLog, "SaveSnapshots555555555555555555,ihdbCache.nBatchId[%d],m_nBatchCnt[%d]", nBatchId,
            ihdbCache.m_nBatchCnt.load());
    nRet = BatchSaveSnapshots(ihdbCache);
    if (nRet != EC_DSF_ARCH_SUCCESS)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "BatchSaveSnapshots failed! nRet = %d", nRet);
        return nRet;
    }
    CV_INFO(g_DataArchiveLog, "SaveSnapshot666666666666666666");
    return nRet;
}

int32 CArchiveDBIhdb::BatchSaveSnapshots(CIhdbCache &ihdbCache)
{
    static std::unordered_set<uint32> setIgnoreEC = {EC_RD_SEQ_CACHE_NO_FREE_FILE, EC_RD_NET_CONNECT_TIMEOUT,
                                                     EC_RD_NET_SEND_TIMEOUT,       EC_RD_NET_RECV_TIMEOUT,
                                                     EC_RD_NET_NOT_CONNECT,        EC_HD_API_CONNECT_FAILED};
    int32 nRet =
        sn3_save_snapshots(ihdbCache.m_nBatchCnt, ihdbCache.pTagIDArray, ihdbCache.pRecArray, ihdbCache.pErrCodeArray);
    if (nRet == EC_DSF_ARCH_SUCCESS || nRet == EC_HD_API_SAVE_SNAPSHOTS_FAILED)
    {
        for (int j = 0; j < ihdbCache.m_nBatchCnt; ++j)
        {
            if (0 != ihdbCache.pErrCodeArray[j])
            {
                CV_WARN(g_DataArchiveLog, ihdbCache.pErrCodeArray[j],
                        "sn3_save_snapshots  j = %d, id = %u, error_code = %d,  ihdbCache.pRecArray.nMsec=%u", j,
                        ihdbCache.pTagIDArray[j], ihdbCache.pErrCodeArray[j], ihdbCache.pRecArray[j].nMsec);
                if (ihdbCache.pErrCodeArray[j] == EC_RD_TAG_INDEX_TAG_DELETE ||
                    ihdbCache.pErrCodeArray[j] == EC_RD_TAG_INDEX_TAG_ID_GREATER_THAN_MAXID)
                { // tag deleted
                    m_CheckCv.notify_one();
                }
            }
        }
        nRet = EC_DSF_ARCH_SUCCESS;
    }
    else if (setIgnoreEC.find(nRet) != setIgnoreEC.end())
    {
        // some error caused by network,disk, ignore it
        CV_WARN(g_DataArchiveLog, nRet, "sn3_save_snapshots failed,ignore!, nRet = %d", nRet);
    }
    else
    {
        CV_ERROR(g_DataArchiveLog, nRet, "sn3_save_snapshots failed, nRet = %d", nRet);
        m_CheckCv.notify_one();
    }
    // reset :release buffer, reset counter
    ihdbCache.Reset();
    return nRet;
}
HD3_TAG_TYPE CArchiveDBIhdb::GetHD3TagType(const std::string &strType, bool bHdFlag) const
{
    auto iter = gMapHD3types.find(strType);
    if (iter != gMapHD3types.end())
    {
        if (bHdFlag)
            return iter->second.hd_expand_type;
        else
            return iter->second.type;
    }
    else
    {
        CV_WARN(g_DataArchiveLog, -1, "unknown type %s", strType.c_str());
        return HD3_TAG_TYPE_BLOB;
    }
}

void CArchiveDBIhdb::CheckTagThreadCallback()
{
    CV_INFO(g_DataArchiveLog, "CheckTagThreadCallback begin!");
    pthread_setname_np(pthread_self(), "check_tag");
    static auto LastCheckTime = std::chrono::system_clock::now() - std::chrono::seconds(60);
    while (!m_bStop.load())
    {
        std::unique_lock<std::mutex> lock(m_CheckMtx);
        m_CheckCv.wait(lock);
        if (m_bStop.load())
            break;
        if (std::chrono::system_clock::now() - LastCheckTime >= std::chrono::seconds(60)) // interval 60s
        {
            auto nRet = CheckTagInfo();
            if (nRet != 0)
            {
                CV_ERROR(g_DataArchiveLog, nRet, " CheckTagInfo failed");
            }
            LastCheckTime = std::chrono::system_clock::now();
        }
        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
    CV_INFO(g_DataArchiveLog, "CheckThreadCallback end!");
}

bool CArchiveDBIhdb::IsIHDAddTagSyncDone()
{
    // RAII
    FlagGuard guard(m_bSyncChecking);

    if (m_bSyncDone.load())
        return true;
    if (!m_MaxTagIDTagInfoPtr)
    {
        m_CheckCv.notify_one();
        CV_ERROR(g_DataArchiveLog, -1, "m_MaxTagIDTagInfoPtr  is nullptr");
        return false;
    }

    uint32 nTagMaxID = 0;
    HD3Record sn;
    {
        std::lock_guard<std::mutex> lock(m_MaxTagIDMtx);
        nTagMaxID = m_MaxTagIDTagInfoPtr->nTagID;
        sn.nTagType = static_cast<HD3_TAG_TYPE>(m_MaxTagIDTagInfoPtr->nTagType);
    }

    if (0 == nTagMaxID)
    {
        CV_DEBUG(g_DataArchiveLog, "nTagMaxID = 0");
        return false;
    }

    int32 nRet = 0;
    if (HD3_TAG_TYPE_STRING == sn.nTagType || HD3_TAG_TYPE_BLOB == sn.nTagType)
    {
        sn.value.strBlob.nLenBuf = LENGTH_VAR_TYPE_VALUE;
        sn.value.strBlob.pBuf = new char[LENGTH_VAR_TYPE_VALUE];
    }
    nRet = sn3_query_snapshot(nTagMaxID, &sn);
    CV_INFO(g_DataArchiveLog, "sn3_query_snapshot nTagMaxID %d TYPE %d, nRet %d", nTagMaxID, (int32)sn.nTagType, nRet);
    if (HD3_TAG_TYPE_STRING == sn.nTagType || HD3_TAG_TYPE_BLOB == sn.nTagType)
    {
        delete[] sn.value.strBlob.pBuf;
    }
    if (RD_SUCCESS == nRet || EC_RD_DATA_QUERY_SNAPSHOT_NO_EXIST == nRet)
    {
        m_bSyncDone.store(true);
        return true;
    }
    return false;
}