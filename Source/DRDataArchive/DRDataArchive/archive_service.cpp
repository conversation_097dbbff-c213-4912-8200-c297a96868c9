#include "archive_service.h"
#include "common/CVLog.h"

CCVLog g_DataArchiveLog;
const char *szServerName = "dsfdataarchive";

long CArchiveService::Init(int argc, char *args[])
{
    std::string strHisTagNumber = "";
    int32 nHisTagNumber = 100; //default value
    long ret = GetExetendedLicInfo("iHDHisTagNumber", strHisTagNumber);
    if (ret == 0)
    {
        nHisTagNumber = std::stoi(strHisTagNumber);
    }
    CV_INFO(g_DataArchiveLog, "strHisTagNumber=%s, HisTagNumber:%d", strHisTagNumber.c_str(), nHisTagNumber);

    m_pArchiveManager = new CArchiveManager(nHisTagNumber);
    int32 nRet = m_pArchiveManager->Init();
    if (nRet != 0)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "m_pArchiveManager->Init() failed, ret = %d", nRet);
    }
    return nRet;
}

long CArchiveService::Start()
{
    int32 nRet = m_pArchiveManager->Start();
    if (nRet != 0)
    {
        CV_ERROR(g_DataArchiveLog, nRet, "m_pArchiveManager->Start() failed, ret = %d", nRet);
    }
    return ICV_SUCCESS;
}

void CArchiveService::PrintHelpScreen()
{
    printf("+===================================================================+\n");
    printf("|                     <<Welcome to dsfdataarchive>>                    |\n");
    printf("|  You can entering the following commands to configure the service    |\n");
    printf("|  q/Q:Quit                                                            |\n");
    printf("|  Others:Print tips                                                   |\n");
    printf("+======================================================================+\n");
}

long CArchiveService::Fini()
{
    m_pArchiveManager->Shutdown();
    g_DataArchiveLog.StopLogThread();
    return ICV_SUCCESS;
}

void CArchiveService::SetLogFileName()
{
    g_DataArchiveLog.SetLogFileNameThread(szServerName);
}

CServiceBase *g_pServiceHandler = new CArchiveService();