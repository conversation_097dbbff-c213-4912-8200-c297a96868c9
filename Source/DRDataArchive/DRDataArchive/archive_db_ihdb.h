/**
 * Filename        archive_db_ihdb.h
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description     declare the interface of iHybirDB archive database
 *
 * Author          wuzheqiang
 * Version         11/06/2024    wuzheqiang    Initial Version
 **************************************************************/

#ifndef __DR_ARCHIVE_DB_IHDB_H__
#define __DR_ARCHIVE_DB_IHDB_H__
#include "RMAPI.h"
#include "archive_define.h"
#include "ihd3/error_code.h"
#include "ihd3/hdKingAPI.h"
#include "string.h"
#include <array>
#include <atomic>
#include <condition_variable>
#include <functional>
#include <memory>
#include <mutex>
#include <thread>
#include <unordered_map>
#include <vector>

using RmStatusCallback = std::function<long(void)>;
using BackupFileCallback = std::function<void()>;

struct TIHDBExtendTagInfo
{
    HD3_TAG_TYPE nTagType = HD3_TAG_TYPE_MIN;
    uint32 nTagID = 0;
};

class CIhdbCache
{
  public:
    CIhdbCache()
    {
        pTagIDArray = new uint32[m_nBatchMaxSize];
        pRecArray = new HD3Record[m_nBatchMaxSize];
        pErrCodeArray = new int32[m_nBatchMaxSize];
        memset(pTagIDArray, 0, sizeof(uint32) * m_nBatchMaxSize);
        memset(pRecArray, 0, sizeof(HD3Record) * m_nBatchMaxSize);
        memset(pErrCodeArray, 0, sizeof(int32) * m_nBatchMaxSize);
    }
    CIhdbCache(const CIhdbCache &) = delete;
    CIhdbCache &operator=(const CIhdbCache &) = delete;
    ~CIhdbCache()
    {
        if (pTagIDArray != nullptr)
        {
            delete[] pTagIDArray;
            pTagIDArray = nullptr;
        }
        if (pRecArray != nullptr)
        {
            for (int32 i = 0; i < m_nBatchMaxSize; i++)
            {
                if (pRecArray[i].value.strBlob.pBuf != nullptr)
                {
                    delete[] pRecArray[i].value.strBlob.pBuf;
                    pRecArray[i].value.strBlob.pBuf = nullptr;
                }
            }
            delete[] pRecArray;
            pRecArray = nullptr;
        }
        if (pErrCodeArray != nullptr)
        {
            delete[] pErrCodeArray;
            pErrCodeArray = nullptr;
        }
    }

    void Reset()
    {
        // The length of the blob is inconsistent and cannot be reused
        for (int32 i = 0; i < m_nBatchCnt; i++)
        {
            if (pRecArray[i].value.strBlob.pBuf != nullptr)
            {
                delete[] pRecArray[i].value.strBlob.pBuf;
                pRecArray[i].value.strBlob.pBuf = nullptr;
            }
        }

        m_nBatchCnt.store(0);
        m_nLastSaveSecond = std::chrono::steady_clock::now();
    }

    bool IsEnough(const int nSzie) const
    {
        return m_nBatchCnt + nSzie <= m_nBatchMaxSize;
    }
    bool ShouldSave() const
    {
        if (m_nBatchCnt.load() >= m_nBatchSize)
        {
            return true;
        }

        auto now = std::chrono::steady_clock::now();
        auto duration = std::chrono::duration_cast<std::chrono::milliseconds>(now - m_nLastSaveSecond).count();
        if (duration >= m_nBatchInterval)
        {
            return true;
        }

        return false;
    }

  public:
    int32 m_nBatchInterval = 1000; // ms
    std::chrono::steady_clock::time_point m_nLastSaveSecond = std::chrono::steady_clock::now();

    int32 m_nBatchSize = 5000;
    int32 m_nBatchMaxSize = 10000;
    std::atomic<int32> m_nBatchCnt = {0};

    uint32 *pTagIDArray = nullptr;
    HD3Record *pRecArray = nullptr;
    int32 *pErrCodeArray = nullptr;

    std::mutex m_mtx;
};

class CArchiveDBIhdb
{
  public:
    CArchiveDBIhdb() = default;
    virtual ~CArchiveDBIhdb()
    {
        Disconnect();
    }
    int32 Connect(const DBConnectInfoPtr &tConnectInfoPtr);
    int32 Disconnect();
    int32 Login(const DBLoginInfoPtr &tLoginInfoPtr);
    int32 SaveSnapshots(const int32 nBatchId, const ArchiveTagInfoPtrVec &vecTagInfo,
                        const std::vector<void *> &vecData);
    void SetRmStatusCallback(RmStatusCallback pRmStatusCallback)
    {
        m_pRmStatusCallback = pRmStatusCallback;
    }

    void SetBackupFileCallback(BackupFileCallback pBackupFileCallback)
    {
        m_pBackupFileCallback = pBackupFileCallback;
    }

    void SetTagInfoComp(std::map<std::string, ArchiveTagInfoPtr> *pNew, std::map<std::string, ArchiveTagInfoPtr> *pOld)
    {
        m_mapTagInfoCompPtr = pNew;
        m_mapTagInfoCompOldPtr = pOld;
    }

  private:
    HD3_TAG_TYPE GetHD3TagType(const std::string &strType, bool bHdFlag = false) const;
    int32 CheckTagInfo();
    int32 GetDbTags(std::map<std::string, ArchiveTagInfoPtr> &mapTagInfoCompPtr,
                    std::vector<HD3CommTagProp> &vecHD3CommTagProp);
    int32 GetDbTags_bak(std::map<std::string, HD3CommTagProp> &mapHD3CommTagProp);
    int32 CompTags(std::map<std::string, ArchiveTagInfoPtr> &mapTagInfoComp,
                   std::vector<HD3CommTagProp> &vecHD3CommTagProp, std::vector<uint32> &vecDelTagId);
    int32 DeleteTags(const std::vector<uint32> &vecDelTagId);
    int32 UpdateTags(std::map<std::string, ArchiveTagInfoPtr> &mapTagInfoComp);
    int32 AddTags(std::map<std::string, ArchiveTagInfoPtr> &mapTagInfoComp);
    int32 BatchSaveSnapshots(CIhdbCache &ihdbCache);
    void CheckTagThreadCallback();
    bool IsIHDAddTagSyncDone();

  public:
    std::atomic<bool> m_bStop = {false};
    DBLoginInfoPtr m_tLoginInfoPtr = nullptr;
    std::mutex m_CheckMtx;
    std::condition_variable m_CheckCv;
    std::unique_ptr<std::thread> m_CheckThread = nullptr;
    RmStatusCallback m_pRmStatusCallback = nullptr;
    BackupFileCallback m_pBackupFileCallback = nullptr;
    std::map<std::string, ArchiveTagInfoPtr> *m_mapTagInfoCompPtr = nullptr;
    std::map<std::string, ArchiveTagInfoPtr> *m_mapTagInfoCompOldPtr = nullptr;

    std::atomic<bool> m_CheckFlag{false};
    std::mutex m_MaxTagIDMtx;
    ArchiveTagInfoPtr m_MaxTagIDTagInfoPtr = nullptr;
    std::atomic<bool> m_bSyncChecking{false}; // for multi-thread
    std::atomic<bool> m_bSyncDone{false};
};

#endif // __DR_ARCHIVE_DB_IHDB_H__
