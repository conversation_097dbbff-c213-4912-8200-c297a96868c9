

#ifndef __DR_ARCHIVE_MANAGER_H__
#define __DR_ARCHIVE_MANAGER_H__
#include "archive_db_ihdb.h"
#include "archive_define.h"
#include "data_types.h"
#include "dsfapi.h"
#include "nlohmann/json.hpp"
#include <atomic>

using json = nlohmann::json;

class CArchiveManager
{
  public:
    CArchiveManager(const int nHisTagNumber);
    ~CArchiveManager();
    int32 Init();
    int32 Start();
    int32 Shutdown();

  private:
    dsfapi::DRSdkContext *DrsdkInit();
    int32 LoadConfig();
    int32 LoadConfigOld();
    int32 LoadDBConfig(const json &jConfig);
    int32 LoadTagsConfig(const json &jConfig);
    int32 LoadTagsConfigOld(const json &jConfig);
    int32 RegisterAllToDrsdk();
    int32 RegisterTagsToDrsdk(const int32 nInterval, const ArchiveTagInfoPtrVec &vecTagInfo);
    int32 CheckSupportType(const std::string &strType);
    void SaveTagValues(const int32 nBatchId, dsfapi::TagRecord *pTagRecord, int32 *pErrorCode, const int32 nTagCount);
    void GetRmStatusThreadCallback();
    void BackupConfigFile();
    long RmStatus() const
    {
        return m_nRmStatus;
    }

  private:
    int32 m_nHisTagNumber = 100;
    std::atomic<bool> m_bInit = {false};
    std::atomic<bool> m_bStop = {false};
    std::string m_strConfigFileName = "";
    std::string m_strConfigFileNameOld = "";
    std::atomic<int> m_nOldFileFlag = 0;                    // 0: not exist, 1: the same, 2: different
    dsfapi::DRSdkContext *m_pContext = nullptr;             // dsfapi handler
    std::unique_ptr<CArchiveDBIhdb> m_pArchiveDb = nullptr; // db handler

    std::shared_ptr<DBConnectInfo> m_pDBConnectInfo = nullptr;
    std::shared_ptr<DBLoginInfo> m_pDBLoginInfo = nullptr;
    std::map<std::string, ArchiveTagInfoPtr> m_mapTagInfoComp;       // use to compare old data, ordered by tagname
    std::map<std::string, ArchiveTagInfoPtr> m_mapTagInfoCompOld;    // use to compare old data, ordered by tagname
    std::unordered_map<std::string, ArchiveTagInfoPtr> m_mapTagInfo; // use to get tag info ,faster than map
    std::unordered_map<int32, ArchiveTagInfoPtrVec> m_mapIntervalTagInfo;
    std::unordered_map<int32, bool> m_mapRegFlag;

    long m_nRmStatus = RM_STATUS_UNAVALIBLE;                     // 0: inactive, 1: active, 2: unavaliabe
    std::unique_ptr<std::thread> m_pGetRmStatusThread = nullptr; // thread to get rm status
    std::atomic<bool> m_bBackupFileFlag = {false};               // last rm status
};
#endif // __DR_ARCHIVE_MANAGER_H__