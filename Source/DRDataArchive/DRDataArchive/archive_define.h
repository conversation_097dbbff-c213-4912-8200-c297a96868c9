/**
 * Filename        archive_define.h
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description     declare some define for archive
 *
 * Author          wuzheqiang
 * Version         11/06/2024    wuzheqiang    Initial Version
 **************************************************************/

#ifndef __DR_ARCHIVE_DEF_H__
#define __DR_ARCHIVE_DEF_H__
#include "data_types.h"
#include "errcode/error_code.h"
#include <atomic>
#include <map>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

// connect info for archive
struct DBConnectInfo
{
    // dsf info
    std::string strDsfAddress = "";
    uint16 nDsfPort = 0;
    // db info
    std::string strAddress = "";
    uint16 nPort = 0;
    std::string strBakAddress = "";
    uint16 nBakPort = 0;
    int32 nTimeoutSec = 1; // connect timeout, unit: s
};
using DBConnectInfoPtr = std::shared_ptr<DBConnectInfo>;

// connect info for archive
struct DBLoginInfo
{
    std::string strUserName = "";
    std::string strPassword = "";
};
using DBLoginInfoPtr = std::shared_ptr<DBLoginInfo>;

constexpr uint16 DB_OPER_TYPE_NONE = 0x0;
constexpr uint16 DB_OPER_TYPE_ADD = 0x01;
constexpr uint16 DB_OPER_TYPE_DEL = 0x02;
constexpr uint16 DB_OPER_TYPE_UPDATE = 0x04;
constexpr uint16 DB_OPER_TYPE_REBUILD = DB_OPER_TYPE_DEL | DB_OPER_TYPE_ADD;

// tag info for archive
struct ArchiveTagInfo
{
    // config data info
    std::string strTagname = "";
    std::string strType = ""; // such as "UINT","REAL"
    std::string strArchiveTagname = "";
    std::string strAnnotation = "";
    float32 fCompDev = 0.0;
    int32 nCompMaxTime = 0; // seconds

    // config deal info
    int32 nIntervalMs = 1000; // unit: ms
    bool after_changes = false;

    // expand info
    uint32 nTagID = 0;  // The ID in the database can be obtained from query or add interfaces
    int32 nTagType = 0; // The type in the database can be obtained from query or add interfaces
    uint16 nOperType = DB_OPER_TYPE_ADD;
};
using ArchiveTagInfoPtr = std::shared_ptr<ArchiveTagInfo>;
using ArchiveTagInfoPtrVec = std::vector<ArchiveTagInfoPtr>;

#endif //__DR_ARCHIVE_DEF_H__