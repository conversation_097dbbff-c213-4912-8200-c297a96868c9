

#ifndef __DR_ARCHIVE_SERVICE_H__
#define __DR_ARCHIVE_SERVICE_H__
#include "archive_manager.h"
#include "common/ServiceBase.h"

class CArchiveService : public CServiceBase
{
  public:
    CArchiveService() : CServiceBase("dsfdataarchive", true, "iHDArchiveService")
    {
        ExitWhenInitFailed(false);
    }
    virtual long Init(int argc, char *args[]);
    virtual long Start();
    virtual void PrintStartUpScreen()
    {
        PrintHelpScreen();
    }
    virtual void PrintHelpScreen();
    virtual long Fini();
    virtual void SetLogFileName();
    bool ProcessCmd(char c)
    {
        return 0;
    }

  private:
    CArchiveManager *m_pArchiveManager = nullptr;
};

#endif // __DR_ARCHIVE_SERVICE_H__
