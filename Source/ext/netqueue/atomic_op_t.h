#ifndef _CV_COMM_ATOMIC_OP_H_
#define _CV_COMM_ATOMIC_OP_H_

#include <boost/thread/locks.hpp>

namespace cvcomm
{

	template <class LOCK, typename TYPE>
	class Atomic_Op_T
	{
	public:

		//typedef typename TYPE arg_type;

		// = Initialization methods.

		/// Initialize @c value_ to 0.
		Atomic_Op_T ()
		{
			//value_ = 0;
		}

		/// Initialize @c value_ to c.
		Atomic_Op_T (TYPE c)
		{
			value_ = c;
		}

		// = Accessors.

		/// Atomically pre-increment @c value_.
		TYPE operator++ (void)
		{
			boost::lock_guard<LOCK> guard(mutex_);
			return ++value_ ;
		}

		/// Atomically pre-decrement @c value_.
		TYPE operator-- (void)
		{
			boost::lock_guard<LOCK> guard(mutex_);
			return --value_;
		}

		/// Atomically compare @c value_ with rhs.
		bool operator== (TYPE rhs) const
		{
			boost::lock_guard<LOCK> guard(mutex_);
			return value_ == rhs;
		}

		/// Atomically compare @c value_ with rhs.
		bool operator!= (TYPE rhs) const
		{
			boost::lock_guard<LOCK> guard(mutex_);
			return value_ != rhs;
		}

		/// Atomically assign rhs to @c value_.
		Atomic_Op_T<LOCK, TYPE> &operator= (TYPE rhs)
		{
			boost::lock_guard<LOCK> guard(mutex_);
			value_ = rhs;
			return *this;
		}

		/// Atomically assign <rhs> to @c value_.
		Atomic_Op_T<LOCK, TYPE> &operator= (
			Atomic_Op_T<LOCK, TYPE> const & rhs)
		{
			boost::lock_guard<LOCK> guard(mutex_);
			value_ = rhs.value_;
			return *this;
		}

		/// Explicitly return @c value_.
		TYPE value (void) 
		{
			boost::lock_guard<LOCK> guard(mutex_);
			return value_;
		}

	private:
		/// Type of synchronization mechanism.
		LOCK mutex_;

		/// Current object decorated by the atomic op.
		TYPE value_;
	};

}//namespace cvcomm

#endif//CVNDK_ATOMIC_OP_H_
