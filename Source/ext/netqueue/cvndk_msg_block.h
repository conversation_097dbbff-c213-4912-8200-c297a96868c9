#ifndef _CVNDK_MSG_BLOCK_H_
#define _CVNDK_MSG_BLOCK_H_

#include "CVNDKDef.h"
#include "common/NetQueue.h"

CVNDK_NAMESPACE_BEGIN

class Output_Message
{
public:
	Output_Message(size_t payload_size);
	~Output_Message();
	const char* data()
	{
		return buffer_;
	}
	size_t length();
	void release();

	void transform_byte_order();

	NDK_Cmn_Header* header_;
	QueueAddress* local_addr_;
	QueueAddress* remote_addr_;
	char* payload_;

	const char* package_data()
	{
		return buffer_ + package_length_ * current_package_ ;
	}


	// set package length for split
	void set_package_length(size_t package_len, PFN_SendCallBack pfn, void* callback_param);

	// return length of current package
	size_t package_length();
	
	// move to next package, return false if no package left;
	bool next_package();

	void package_call_back();

private:
	char* buffer_;
	size_t total_length_;
	size_t payload_length_;
	size_t package_length_;// split messages into small packages to send
	size_t total_package_;
	size_t current_package_;
	PFN_SendCallBack call_back_;
	void *callback_param_;
};

class Input_Message
{
public:
	Input_Message(size_t total_length);

	void transform_byte_order();

	QueueAddress* local_addr_;
	QueueAddress* remote_addr_;
	char* payload_;
	size_t payload_length_;
	size_t total_length_;
	char* data()
	{
		return buffer_;
	}
	void release()
	{
		if (buffer_)
		{
			free(buffer_);
			buffer_ = NULL;
		}
		delete this;
	}
	size_t recv_quitflag;
private:
	char* buffer_;
};

CVNDK_NAMESPACE_END

#endif//_CVNDK_MSG_BLOCK_H_
