cmake_minimum_required(VERSION 3.10)
############FOR_MODIFIY_BEGIN#######################
#Setting Project Name
PROJECT (drnetqueue)

INCLUDE($ENV{DRDIR}CMakeCommon)



#Setting Source Files
SET(SRCS ${SRCS} CVNDK.cpp GlobalObjectManager.cpp QueueHandler.cpp SockHandler.cpp cvndk_msg_block.cpp EventMapManager.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME drnetqueue)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS ACE intl iconv)

IF(${CMAKE_SYSTEM_NAME} MATCHES Windows)
	SET (LINK_LIBS  ${LINK_LIBS} exceptionreport)
ELSE(${CMAKE_SYSTEM_NAME} MATCHES Windows)		
	IF(${CMAKE_SYSTEM_NAME} MATCHES HP-UX)
		 SET (CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -lipv6 ")
		 SET (CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -lipv6 ")
		 SET(CMAKE_CXX_COMPILER "aCC")
		 SET(CMAKE_C_COMPILER "cc")
	ENDIF(${CMAKE_SYSTEM_NAME} MATCHES HP-UX)

    SET (LINK_LIBS  ${LINK_LIBS} pthread)
	SET(LINK_LIBS  ${LINK_LIBS} boost_thread boost_system)
ENDIF(${CMAKE_SYSTEM_NAME} MATCHES Windows)

INCLUDE($ENV{DRDIR}CMakeCommonLib)
