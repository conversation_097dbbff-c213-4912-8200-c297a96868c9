def postinstall(env, status):
    import os
    import os.path
    import sys
    import shutil
    import stat

    def cvcopy(src, dst):
        """Copy data and mode bits ("cp src dst").

        The destination may be a directory.

        """
        #rename the old file
        newname=''
        if os.path.exists(dst) == True:
            os.chmod(dst, stat.S_IWRITE | stat.S_IREAD)
            old_name = dst.split('.')[0]
            newname = old_name + '_bak.' + dst.split('.')[1]
            os.rename(dst, newname)

        #copy file
        shutil.copy(src, dst)

        #remove the old file
        if newname != '':
            os.remove(newname)


    def cvcopy2(src, dst):
        """Copy data and all stat info ("cp -p src dst").

        The destination may be a directory.

        """

        #rename the old file
        newname=''
        if os.path.exists(dst) == True:
            os.chmod(dst, stat.S_IWRITE | stat.S_IREAD)
            old_name = dst.split('.')[0]
            newname = old_name + '_bak.' + dst.split('.')[1]
            os.rename(dst, newname)

        #copy file
        shutil.copy2(src, dst)

        #remove the old file
        if newname != '':
            os.remove(newname)

    def cvrmtree(path, ignore_errors=False, onerror=None):
        """Recursively delete a directory tree.
        """
        shutil.rmtree(path, ignore_errors, onerror)

    def cvcopytree(src, dst, symlinks=False, ignore=None):
        shutil.copytree(src, dst, symlinks, ignore)
            
    try:    
        #print 'target root:' + env.target_root
        cvcopy(env.target_root + os.sep + 'Executable/netqueue.dll', env.target_root + os.sep + 'Executable/cvndk.dll')
        #cvcopy(env.target_root + os.sep + 'library/netqueue.lib', env.target_root + os.sep + 'library/cvndk.lib')        
    except Exception,e:
        print e;
