#ifndef _RDA_EVENT_MAP_MANAGER_H_
#define _RDA_EVENT_MAP_MANAGER_H_

#include "HashMap.h"
#include <ace/Basic_Types.h>
#include <ace/Event.h>
#include "atomic_op_t.h"
#include <boost/thread/mutex.hpp>
//#include "common/CommHelper.h"
//////////////////////////////////////////////////////////////////////////
// CEventMapManager Class Def
//////////////////////////////////////////////////////////////////////////
CVNDK_NAMESPACE_BEGIN
class CEventMapManager
{
public:
	const static ACE_UINT32 INVALID_EVENT_ID = 0;
	CEventMapManager();
	// Register Event
	ACE_UINT32 RegisterEvent(ACE_UINT32 nType);

	// UnRegister Event
	long UnRegisterEvent(ACE_UINT32 nEventID);
	
	// Wait Event and Delete Event
	long WaitEventDelete(ACE_UINT32 nEventID, ACE_Time_Value *tvSleep, void **pArg);
	
	// Signal Event
	long SignalEvent(ACE_UINT32 nEventID, ACE_UINT32 nType, void *arg);

protected:
	struct MapItem 
	{
		ACE_UINT32 nEventID;	// Event ID
		ACE_UINT32 nType;		// Event Type
		void* arg;				// Argument
		ACE_Event *pEvent;		// Event Pointer
		MapItem(ACE_UINT32 nType)
		{
			this->nType = nType;
			arg = NULL;
			pEvent = new ACE_Event();
		}

		~MapItem()
		{
			if(pEvent)
			{
				delete pEvent;
				pEvent = NULL;
			}
		}
	};
private:
	//ACE_Atomic_Op<ACE_Thread_Mutex, ACE_UINT32> m_nEventIDGen;	// Event ID Generator
	cvcomm::Atomic_Op_T<boost::mutex, ACE_UINT32> m_nEventIDGen;
	ACE_RW_Thread_Mutex m_mutex;								// RW Thread Mutex
	Hash_Map<ACE_UINT32, MapItem*> m_mapEvent;					// Event Map
};

CVNDK_NAMESPACE_END
#endif//#ifndef _RDA_EVENT_MAP_MANAGER_H_
