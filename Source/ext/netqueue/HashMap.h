/**************************************************************
 *  Filename:    HashMap.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description:  HashMap.h
 *
 *  @author:     lijingjing
 *  @version     05/14/2008  lijingjing  Initial Version
**************************************************************/

#ifndef _HASH_MAP_H_
#define _HASH_MAP_H_

#include <ace/Hash_Map_Manager.h>
#include <ace/ACE.h>
#include "CVNDKDef.h"

#define APP_NAME_LEN 128

CVNDK_NAMESPACE_BEGIN


struct AppName{
	char szName[APP_NAME_LEN];
};


template<class EXT_ID, class INT_ID>
class Hash_Map :
      public ACE_Hash_Map_Manager_Ex<EXT_ID, INT_ID,
      ACE_Hash<EXT_ID>, ACE_Equal_To<EXT_ID>, ACE_RW_Mutex>
{};

inline bool operator==(const IP_Port& ip_port1, const IP_Port& ip_port2)
{
	return (ip_port1.IP == ip_port2.IP && ip_port1.nPort == ip_port2.nPort);
};

inline bool operator==(const AppName& appName0, const AppName& appName1)
{
	return strcmp(appName0.szName, appName1.szName) == 0;
}

CVNDK_NAMESPACE_END


ACE_BEGIN_VERSIONED_NAMESPACE_DECL

/**
 *  Specialize the hash functor. 
 */

using common::cvndk::IP_Port;
using common::cvndk::AppName;

template<>
class ACE_Hash<IP_Port>
{
public:
  u_long operator() (const IP_Port kt) const
  {
	  return (kt.IP + kt.nPort);
  }
};

template<>
class ACE_Hash<AppName>
{
public:
	u_long operator() (const AppName appName) const
	{
		return ACE::hash_pjw(appName.szName);
	}
};

/**
 *  Specialize the equality functor. 
 */
template<>
class ACE_Equal_To<IP_Port>
{
public:
  int operator() (const IP_Port& val1,
                  const IP_Port& val2) const
  {
    return (val1 == val2);
  }
};

template<>
class ACE_Equal_To<AppName>
{
public:
	int operator() (const AppName& val1,
		const AppName& val2) const
	{
		return (val1 == val2);
	}
};

ACE_END_VERSIONED_NAMESPACE_DECL

#endif//_HASH_MAP_H_
