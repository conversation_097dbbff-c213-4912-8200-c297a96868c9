cmake_minimum_required(VERSION 3.10)

############FOR_MODIFIY_BEGIN#######################
#Setting Project Name
PROJECT (tinyxml_static)

INCLUDE($ENV{DRDIR}CMakeCommon)
INCLUDE_DIRECTORIES(../tinyxml/include)

SET(CMAKE_CXX_FLAGS_RELWITHDEBINFO  "/MT /O2 /Ob2 /D NDEBUG")
#SET(CMAKE_CXX_FLAGS_RELEASE   "${CMAKE_CXX_FLAGS_RELEASE} /NDEBUG")
#SET(CMAKE_C_FLAGS_RELEASE   "${CMAKE_C_FLAGS_RELEASE} /NDEBUG")
#Setting Source Files
SET(SRCS ${SRCS} ../tinyxml/tinystr.cpp ../tinyxml/tinyxml.cpp ../tinyxml/tinyxmlerror.cpp ../tinyxml/tinyxmlparser.cpp)

SET(CMAKE_CXX_FLAGS_RELEASE "${CMAKE_CXX_FLAGS_RELEASE} /MT")
SET(CMAKE_C_FLAGS_RELEASE "${CMAKE_C_FLAGS_RELEASE} /MT")
#Setting Target Name (executable file name | library name)
SET(TARGET_NAME tinyxml_static)
#Setting library type used when build a library
SET(LIB_TYPE STATIC)

SET(LINK_LIBS )
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)
IF(MSVC)
	if( CMAKE_SIZEOF_VOID_P EQUAL 8 )
		set_target_properties(${TARGET_NAME} PROPERTIES STATIC_LIBRARY_FLAGS "/machine:x64")
	endif( CMAKE_SIZEOF_VOID_P EQUAL 8 )	
ENDIF(MSVC)
