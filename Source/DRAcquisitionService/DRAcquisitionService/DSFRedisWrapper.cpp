#include "DSFRedisWrapper.h"
#include "common/cvcomm.hxx"
#include "common/LogHelper.h"
#include <iostream>
#include "ace/Default_Constants.h"

constexpr char REDIS_DEFAULT_UNIX_SOCKNAME[] = "redis.sock";
extern CCVLog g_asLog;

DSFRedisWrapper::DSFRedisWrapper(const std::string &host, int port)
    : m_isConnected(false),
      m_context(nullptr)
{
    // m_context = redisConnect(host.c_str(), port);
    // ���ó�ʱʱ��
    struct timeval timeout = {1, 0};
    m_context = redisConnectWithTimeout(host.c_str(), port, timeout);
    if (m_context == nullptr || m_context->err)
    {
        if (m_context)
        {
            CV_ERROR(g_asLog, -1, "Error: %s", m_context->errstr);
        }
        else
        {
            CV_ERROR(g_asLog, -1, "Can't allocate redis context");
        }
        m_isConnected = false;
    }
    else
    {
        m_isConnected = true;
    }

    auto printTime = []() {
    };

    struct timeval tv = {1, 0}; // 1秒超时
    redisSetTimeout(m_context, tv);
    
    // m_timer = new Timer(handleTimeout, std::chrono::seconds(2));
    // m_timerThread = new std::thread(&Timer::start, m_timer, this);
    m_timer = nullptr;
    m_timerThread = nullptr;
}

DSFRedisWrapper::DSFRedisWrapper(const std::string &unixSockPath)
    : m_isConnected(false),
      m_context(nullptr)
{
    struct timeval timeout = {3, 0};
    m_context = redisConnectUnixWithTimeout(unixSockPath.c_str(), timeout);
    if (m_context == nullptr || m_context->err)
    {
        if (m_context)
        {
            CV_ERROR(g_asLog, -1, "Error: %s", m_context->errstr);
        }
        else
        {
            CV_ERROR(g_asLog, -1, "Can't allocate redis context");
        }
        m_isConnected = false;
    }
    else
    {
        m_isConnected = true;
    }

    // m_timer = new Timer(handleTimeout, std::chrono::seconds(2));
    // m_timerThread = new std::thread(&Timer::start, m_timer, this);
    m_timer = nullptr;
    m_timerThread = nullptr;
}

DSFRedisWrapper::DSFRedisWrapper()
    : m_isConnected(false),
      m_context(nullptr)
{
    // 默认使用unix套接字连接
    const char *szRedisPath = CVComm.GetDsfRedisPath();
    if (NULL == szRedisPath)
    {
        return;
    }

    m_strRedisSockPath = szRedisPath;
    m_strRedisSockPath += ACE_DIRECTORY_SEPARATOR_STR;
    m_strRedisSockPath += REDIS_DEFAULT_UNIX_SOCKNAME;

    struct timeval timeout = {3, 0};
    m_context = redisConnectUnixWithTimeout(m_strRedisSockPath.c_str(), timeout);
    if (m_context == nullptr || m_context->err)
    {
        if (m_context)
        {
            CV_ERROR(g_asLog, -1, "Error: %s", m_context->errstr);
        }
        else
        {
            CV_ERROR(g_asLog, -1, "Can't allocate redis context");
        }
        m_isConnected = false;
    }
    else
    {
        m_isConnected = true;
    }

    // m_timer = new Timer(handleTimeout, std::chrono::seconds(2));
    // m_timerThread = new std::thread(&Timer::start, m_timer, this);
    m_timer = nullptr;
    m_timerThread = nullptr;
}

// std::shared_ptr<DSFRedisWrapper> DSFRedisWrapper::getInstance(const std::string &host, int port)
// {
//     static std::shared_ptr<DSFRedisWrapper> instance(new DSFRedisWrapper(host, port));
//     return instance;
// }

void DSFRedisWrapper::handleTimeout(void *arg)
{
    DSFRedisWrapper *p = (DSFRedisWrapper *)arg;

    if (p->isConnected())
        return;

    p->reconnect();

    // std::cout << "Current time: " << std::chrono::system_clock::now().time_since_epoch().count() << std::endl;
}

bool DSFRedisWrapper::reconnect()
{
    if (nullptr == m_context)
        return false;

    if (redisReconnect(m_context) != REDIS_OK)
    {
        return false;
    }
    m_isConnected = true;
    return true;
}

DSFRedisWrapper::~DSFRedisWrapper()
{
    if (m_context)
    {
        redisFree(m_context);
    }

    if (m_timer)
        delete m_timer;

    if (m_timerThread)
    {
        m_timerThread->detach();
        delete m_timerThread;
    }
}

void DSFRedisWrapper::setValue(const std::string &key, const unsigned char *value, int value_len)
{
    std::lock_guard<std::mutex> lock(m_mtx);
    if (!m_isConnected)
    {
        if (!reconnect()) {
            CV_ERROR(g_asLog, -1, "Redis not connected ,Failed to execute command:  %s", m_context->errstr);
            return;
        }
    }

    redisReply *reply = static_cast<redisReply *>(redisCommand(m_context, "SET %s %b", key.c_str(), value, value_len));
    if (reply == nullptr)
    {
        CV_ERROR(g_asLog, -1, "Failed to execute command:  %s", m_context->errstr);
        m_isConnected = false; // TODO:��������?д��ʧ�ܾ�����Ϊ���ӶϿ�����������ģ��������?������
        return;
    }
    freeReplyObject(reply);
    return;
}

// TODO: test this function
// void DSFRedisWrapper::setValueByPipelining(const std::string &key, const unsigned char* value,int value_len)
// {
//     if(!m_isConnected)
//     {
//         CV_ERROR(g_asLog, -1, "Redis not connected ,Failed to execute command:  %s", m_context->errstr);
//         return;
//     }
//     redisAppendCommand(m_context, "SET %s %b", key.c_str(), value, value_len);
//     return;
// }

bool DSFRedisWrapper::setValueByPipelining(const std::vector<RedisData> &data)
{
    std::lock_guard<std::mutex> lock(m_mtx);
    if (!m_isConnected)
    {
        if (!reconnect()) {
            CV_ERROR(g_asLog, -1, "Redis not connected ,Failed to execute command:  %s", m_context->errstr);
            return false;
        }
    }

    bool bSuccess = true;

    for (const auto &entry : data)
    {
        // 计算需要的总缓冲区大小：原始值 + 时间戳(秒+毫秒) + 质量
        size_t totalSize = entry.len + sizeof(int32_t) + sizeof(int16_t) + sizeof(int16_t);
        
        // 分配新的缓冲区
        char* totalBuffer = new char[totalSize];
        
        // 按VTQ顺序复制数据
        // 1. 复制原始值 (V)
        memcpy(totalBuffer, entry.buf, entry.len);
        
        // 2. 复制时间戳秒部分 (T-sec)
        memcpy(totalBuffer + entry.len, &entry.sec, sizeof(int32_t));
        
        // 3. 复制时间戳毫秒部分 (T-ms)
        memcpy(totalBuffer + entry.len + sizeof(int32_t), &entry.ms, sizeof(int16_t));
        
        // 4. 复制质量信息 (Q)
        memcpy(totalBuffer + entry.len + sizeof(int32_t) + sizeof(int16_t), &entry.quality, sizeof(int16_t));

        // 将整个缓冲区写入Redis
        int ret = redisAppendCommand(m_context, "SET %b %b", 
                                     entry.str.c_str(), entry.str.length(), 
                                     totalBuffer, totalSize);

        // 释放临时缓冲区
        delete[] totalBuffer;
        //old
        // int ret = redisAppendCommand(m_context, "SET %b %b", entry.str.c_str(), entry.str.length(), entry.buf, entry.len);

        if (ret != REDIS_OK)
        {
            CV_INFO(g_asLog, "Failed to append command for key[%s]", entry.str.c_str());
            bSuccess = false;
        }
    }

    for (const auto &entry : data)
    {
        redisReply *reply = nullptr;

        if (m_context == nullptr)
        {
            CV_ERROR(g_asLog, -1, "Redis context is null");
            bSuccess = false;
            continue;
        }

        if (redisGetReply(m_context, (void **)&reply) == REDIS_OK && reply != nullptr)
        {
            if(!(reply->type == REDIS_REPLY_STATUS && strcmp(reply->str, "OK") == 0))
            {
                CV_INFO(g_asLog, "Failed to execute SET command for key[%s], error[%s]", entry.str.c_str(), (reply->str ? reply->str : "NULL"));
                bSuccess = false;
            }

            freeReplyObject(reply);
        }
        else
        {
            CV_INFO(g_asLog, "Error receiving reply from Redis for key:[%s]", entry.str.c_str());
            m_isConnected = false;
            bSuccess = false;
        }
    }

    return bSuccess; // 返回最终的执行结果
}

bool DSFRedisWrapper::deleteValue(const std::string &key)
{
    std::lock_guard<std::mutex> lock(m_mtx);
    if (!m_isConnected)
    {
        if (!reconnect()) {
            CV_ERROR(g_asLog, -1, "Redis not connected ,Failed to execute command:  %s", m_context->errstr);
            return false;
        }
    }

    bool bSuccess = true;

    redisReply *reply = static_cast<redisReply *>(redisCommand(m_context, "DEL %b", key.c_str(), key.length()));
    if (reply == nullptr)
    {
        CV_ERROR(g_asLog, -1, "Failed to execute command: %s", m_context->errstr);
        m_isConnected = false;
        return false;
    }

    if (!(reply->type == REDIS_REPLY_INTEGER))
    {
        CV_ERROR(g_asLog, -1, "Failed to execute DEL command for key[%s]", key.c_str());
        bSuccess = false;
    }
    freeReplyObject(reply);

    return bSuccess; // 返回最终的执行结果
}
