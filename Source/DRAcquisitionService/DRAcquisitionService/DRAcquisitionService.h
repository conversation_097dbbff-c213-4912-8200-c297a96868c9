/*  Filename:    DRAcquisitionService.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Declare DRAcquisitionService
 *
 *  @author:     huangcan
 *  @version:    10/02/2024	huangcan	Initial Version
 **************************************************************/

#ifndef DRACQUISITIONSERVICE_H
#define DRACQUISITIONSERVICE_H
#include "common/ServiceBase.h"
#include "NetWrapper.h"
#include "SaveDataHandler.h"
#include "DSFRedisWrapper.h"
#include "CtrlDataHandler.h"
#include "DSFTimer.h"

class DRAcquisitionService : public CServiceBase
{
public:
    /**
     * @brief          Acquisition Service Constructor
     * @version        2024/10/02	huangcan	Initial Version
     */
	DRAcquisitionService();

    /**
     * @brief          Acquisition Init
     * @param [in]     argc  
     * @param [out]    args  
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual long Init(int argc, char* args[]);

    /**
     * @brief          Acquisition refresh
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual void Refresh();

    /**
     * @brief          Acquisition Start
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual long Start();

    /**
     * @brief          PrintStartUpScreen
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual void PrintStartUpScreen();

    /**
     * @brief          PrintHelpScreen
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual void PrintHelpScreen();

    /**
     * @brief          release resource
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual long Fini();

    /**
     * @brief          cmd process
     * @param [in]     c  cmd message
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual bool ProcessCmd(char c);

    /**
     * @brief          set log file name
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual void SetLogFileName();

    /**
     * @brief          other things
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual void Misc();
private:
    // static void handleTimeout(void* arg);

    //data prase and save
	CSaveDataHandler* m_dataHandler; 
    //data recieve
	CNetWrapper* m_netWrapper;
    //control data recieve
    CtrlDataHandler* m_ctrlHandler;
    ServiceProvider m_serviceProvider;
    INGVSAgentManager *m_NGVSManager;

    // int32_t m_testValue = 0;

    // Timer* m_timer;
    // std::thread* m_timerThread;
};

#endif // DRACQUISITIONSERVICE_H