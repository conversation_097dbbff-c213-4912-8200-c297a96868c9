#include "CtrlDataHandler.h"
#include "common/LogHelper.h"
#include <iostream>
#include "drds_ctrl_cmd.h"
#include "sqlite/sqlite3.h"
#include <algorithm>
#include "../../DRDataService/DRDataService/drds_ngvs_config.h"
#include "../../DRDataService/DRDataService/drds_data_attr.h"
extern CCVLog g_asLog;


CtrlDataHandler::CtrlDataHandler(CNetWrapper* netWrapper)
: 
m_netWrapper(netWrapper)
{
    m_pConfigParser = CConfigParser::getInstance();
}

int CtrlDataHandler::callback(void *pObj, int argc, char **argv, char **azColName){

	CtrlDataHandler* pCtrlDataHandler = static_cast<CtrlDataHandler*>(pObj);
	if(pCtrlDataHandler == nullptr)
	{
		return 0;
	}
    int32 tag_id = 0;
    std::string driver_name = "";
    uint8 tag_type = 0;

	int i;
	for(i=0; i<argc; i++){
		// printf("%s = %s\n", azColName[i], argv[i] ? argv[i] : "NULL");
        std::string columnName = azColName[i];
        if(columnName == "fd_tagid")
        {
            if(argv[i]==nullptr)
                continue;
            tag_id = std::stoi(argv[i]);
        }
        else  if(columnName == "fd_iodrv")
        {
            if(argv[i]==nullptr)
                continue;
            driver_name = argv[i];
            std::transform(driver_name.begin(), driver_name.end(), driver_name.begin(), ::tolower);
        }
        else if (columnName == "fd_data_type")
        {
            if(argv[i]==nullptr)
                continue;
            tag_type = std::stoi(argv[i]);
        }
        
	}
	// printf("\n");

    if(pCtrlDataHandler->m_tagIdToDriverMap.find(tag_id) == pCtrlDataHandler->m_tagIdToDriverMap.end())
    {
        pCtrlDataHandler->m_tagIdToDriverMap[tag_id] = driver_name;
        pCtrlDataHandler->m_tagIdToTagType[tag_id] = tag_type;
    }
    
    return 0;
}

void CtrlDataHandler::readSqliteDB(std::string strCfgFile)
{
    sqlite3 *db;
	char *zErrMsg = 0;
	int rc;
	char *sql;

	std::string m_strdbFile = strCfgFile + "/PDSCfg.db";

	/* Open database */
	rc = sqlite3_open(m_strdbFile.c_str(), &db);
	if( rc ){
		fprintf(stderr, "Can't open database: %s\n", sqlite3_errmsg(db));
		exit(0);
	}else{
		// fprintf(stderr, "Opened database successfully\n");
	}

	/* Create SQL statement */
	sql = "SELECT * from t_pb_tags";

	/* Execute SQL statement */
	rc = sqlite3_exec(db, sql, callback, (void*)this, &zErrMsg);
	if( rc != SQLITE_OK ){
		fprintf(stderr, "SQL error: %s\n", zErrMsg);
		sqlite3_free(zErrMsg);
	}else{
		// fprintf(stdout, "Operation done successfully\n");
	}

    // sql = "SELECT * from t_pb_di";

	// /* Execute SQL statement */
	// rc = sqlite3_exec(db, sql, callback, (void*)this, &zErrMsg);
	// if( rc != SQLITE_OK ){
	// 	fprintf(stderr, "SQL error: %s\n", zErrMsg);
	// 	sqlite3_free(zErrMsg);
	// }else{
	// 	// fprintf(stdout, "Operation done successfully\n");
	// }

    // sql = "SELECT * from t_pb_tx";

	// /* Execute SQL statement */
	// rc = sqlite3_exec(db, sql, callback, (void*)this, &zErrMsg);
	// if( rc != SQLITE_OK ){
	// 	fprintf(stderr, "SQL error: %s\n", zErrMsg);
	// 	sqlite3_free(zErrMsg);
	// }else{
	// 	// fprintf(stdout, "Operation done successfully\n");
	// }


	sqlite3_close(db);
}


void CtrlDataHandler::initialize()
{
    std::string strCfgFile = CVComm.GetCVProjCfgPath();
    CV_INFO(g_asLog, "Loading  ngvs config file: %s",strCfgFile.c_str());

	readSqliteDB(strCfgFile);

    // DRDSNgvsParse::getInstance().init(strCfgFile);
    // DRDSNgvsParse::getInstance().init("/home/<USER>/dr/Repo/copy_force/projects/defaultproject/config");

    const unordered_map<string, LocalVaribale> &GlobeLocalVaribales = DRDSNgvsParse::getInstance().GetGetLocalVariable();

    DRDSDataAttr &dataAttr = DRDSDataAttr::getInstance();

    // unordered_map<string, LocalVaribale> copy_variables = GlobeLocalVaribales;

    std::vector<std::string> temp;
    int p = 0;
    int h = 0;
    for(auto iter = GlobeLocalVaribales.begin(); iter != GlobeLocalVaribales.end(); ++iter)
    {
        if(m_tagIdToDriverMap.find(iter->second.tagId)==m_tagIdToDriverMap.end())
        {
            continue;
        }

        // if(DRDSNgvsParse::getInstance().GetGlobeNGVSVariableByname(iter->first)==nullptr)
        // {
        //     continue;
        // }
        // if(iter->first.find("STD_G_IMGPERCENTAGE")!=string::npos)
        // {
        //     p++;
        // }

        // if(iter->first.find("STD_IF_HMI")!=string::npos)
        // {
        //     h++;
        // }

        // if(iter->first == "STD_IF_HMI.COLORTABLE_471")
        // {
        //     std::cout<<"find it"<<std::endl;
        // }

        TagInfoForCtrl tagInfo;
        tagInfo.m_nDataType = m_tagIdToTagType[iter->second.tagId];
        // tagInfo.m_nLenBuf = DRDSNgvsParse::getInstance().GetGlobeNGVSVariableByname(iter->first)->Length;
        // tagInfo.m_nLenBuf = GetNGVSDataTypeSize(iter->second.TypeOf);
        tagInfo.m_nTagID = iter->second.tagId;
        tagInfo.m_strDriverName = m_tagIdToDriverMap[iter->second.tagId];

        ObjectAttr *attr = nullptr;
        attr = dataAttr.GetAttrByName(iter->first);
        if(nullptr != attr)
            tagInfo.m_nLenBuf = attr->nLen;
        else
            tagInfo.m_nLenBuf = GetNGVSDataTypeSize(iter->second.TypeOf);

        m_tagInfoMap.insert(make_pair(iter->first, tagInfo));
        temp.push_back(iter->first);
    }

    // std::cout<<"p:"<<p<<" h:"<<h << " total: "<<GlobeLocalVaribales.size()<< " get total: "<<m_tagInfoMap.size()<<std::endl;
}

int CtrlDataHandler::GetNGVSDataTypeSize(const std::string &typeOf)
{
    if (typeOf == "BOOL")
    {
        return 1;
    }
    else if (typeOf == "BYTE")
    {
        return sizeof(char);
    }
    else if (typeOf == "WORD")
    {
        return sizeof(uint16_t);
    }
    else if (typeOf == "DWORD")
    {
        return sizeof(uint32_t);
    }
    else if (typeOf == "LWORD")
    {
        return sizeof(uint64_t);
    }
    else if (typeOf == "SINT")
    {
        return sizeof(int8_t);
    }
    else if (typeOf == "USINT")
    {
        return sizeof(uint8_t);
    }
    else if (typeOf == "INT")
    {
        return sizeof(int16_t);
    }
    else if (typeOf == "UINT")
    {
        return sizeof(uint16_t);
    }
    else if (typeOf == "DINT")
    {
        return sizeof(int32_t);
    }
    else if (typeOf == "UDINT")
    {
        return sizeof(uint32_t);
    }
    else if (typeOf == "LINT")
    {
        return sizeof(int64_t);
    }
    else if (typeOf == "ULINT")
    {
        return sizeof(uint64_t);
    }
    else if (typeOf == "REAL")
    {
        return sizeof(float);
    }
    else if (typeOf == "LREAL")
    {
        return sizeof(double);
    }
    else if (typeOf == "STRING")
    {
        return 250;
    }
    else if (typeOf == "CHAR")
    {
        return sizeof(char);;
    }
    else
    {
        return 0;
    }
}


/**
 * @brief Unpack buffer with key-value pairs.
 * @param sBuf [tag_name_length + tag_name + value_length + value]
 * @param nLen tag list + 四字节对齐
 * @return
 */
static std::unordered_map<std::string, std::string> UnPackBufferWithKeyValue(const char *sBuf, const size_t nLen) 
{
    std::unordered_map<std::string, std::string> mapRes;

    size_t nLeft = nLen;
    uint16_t nNameLen = 0;
    uint16_t nValueLen = 0;

    while (nLeft > 4) {
        // 1. 获取tag 名称长度
        if (nLeft < sizeof(uint16_t)) {
            std::cout << "Buffer length too small for name length."<<std::endl;
            break;
        }
        memcpy(&nNameLen, sBuf + (nLen - nLeft), sizeof(nNameLen));
        nLeft -= sizeof(nNameLen);

        // 2. 获取tag 名称
        if (nNameLen > nLeft) {
            std::cout << "Name length exceeds remaining buffer size."<<std::endl;
            break;
        }
        std::string name(sBuf + (nLen - nLeft), nNameLen);
        nLeft -= nNameLen;

        // 3. 获取value 长度
        if (nLeft < sizeof(uint16_t)) {
            std::cout << "Buffer length too small for value length."<<std::endl;
            break;
        }
        memcpy(&nValueLen, sBuf + (nLen - nLeft), sizeof(nValueLen));
        nLeft -= sizeof(nValueLen);

        // 4. 获取value
        if (nValueLen > nLeft) {
            std::cout << "Value length exceeds remaining buffer size."<<std::endl;
            break;
        }
        std::string value(sBuf + (nLen - nLeft), nValueLen);
        nLeft -= nValueLen;

        mapRes[name] = value;
    }

    return mapRes;
}

void CtrlDataHandler::RecvData(string ngvsName, unsigned char *buf, uint32_t length)
{
    if (m_netWrapper != nullptr)
    {
        std::unordered_map<std::string, std::string> valueMap = UnPackBufferWithKeyValue((const char *)buf, length);

        // for (auto iter = valueMap.begin(); iter != valueMap.end(); ++iter)
        // {
        //     std::cout << iter->first << std::endl;

        //     for (int i = 0; i < iter->second.size(); i++)
        //     {
        //         printf("%02x ", iter->second[i]);
        //     }

        //     printf("\n");
        // }

        for (auto iter = valueMap.begin(); iter != valueMap.end(); ++iter)
        {
            auto variableIt = m_tagInfoMap.find(iter->first);

            if (variableIt != m_tagInfoMap.end())
            {
                char szBuffer[ICV_BLOBVALUE_MAXLEN]{};
	            unsigned int nHexBufferLen = ICV_BLOBVALUE_MAXLEN;
                cvcommon::HexDumpBuf((unsigned char*)iter->second.c_str(), iter->second.length(), szBuffer, &nHexBufferLen);
                CV_INFO(g_asLog, "CtrlCmd: Devive[%s] Tag[%s] Write[%s] WriteLen[%d].", variableIt->second.m_strDriverName.c_str(), iter->first.c_str(), szBuffer, iter->second.length());

                TagInfoForCtrl tempVariable = variableIt->second;
                TProtoDriverAPICTRLMsg msg;

                msg.m_nDataType = tempVariable.m_nDataType;
                msg.m_nLenBuf = tempVariable.m_nLenBuf;
                msg.m_nTagID = tempVariable.m_nTagID;
                ACE_Time_Value start = ACE_OS::gettimeofday();
                msg.m_nTimeOutSec = start.sec() + 1;
                msg.m_nTimeOutMs = start.usec() / 1000;

                if (variableIt->second.m_nDataType == 0)
                {
                    // DataService下发控制命令字符串类型包含前两个表示长度的字节，但内容字符串不保证以'\0'结束，不保证控制命令长度，因此需要在这里做校验
                    // 此处控制命令校验如下： 1. 如果PDSCfg.db中配置了#长度，则将下发控制命令补全至配置长度，不足部分以'\0'填充;
                    //                      2. 如果PDSCfg.db中没有配置#长度，则下发用户写入的字符串，以'\0'结束
                    if(iter->second.size() < 2)
                    {
                        CV_ERROR(g_asLog, -1, "CtrlCmd: String cmd length less then 2 Bytes. Invalid tag name: [%s].", iter->first.c_str());
                        continue;
                    }

                    int nActualLen = static_cast<unsigned char>(iter->second[1]);   // 控制命令字符串的实际长度
                    nActualLen = std::min(nActualLen, static_cast<int>(iter->second.size() - 2));
                    int nCfgStrLen = -1;
                    PointInfo info;
                    if (m_pConfigParser->GetPointInfo(tempVariable.m_nTagID, info))
                        nCfgStrLen = info.ioaddrLen;

                    char szTemp[AC_STRING_MAXLEN] = {0};
                    memcpy(szTemp, iter->second.c_str() + 2, nActualLen);

                    if (nCfgStrLen > 0) // 如果PDSCfg.db中配置了#长度
                    {
                        if (nActualLen > nCfgStrLen)
                        {
                            CV_ERROR(g_asLog, -1, "CtrlCmd: String cmd length not match. Invalid tag name: [%s], Received length: [%d], Defined length: [%d].",
                                     iter->first.c_str(), iter->second.size(), nCfgStrLen);
                            continue;
                        }

                        msg.m_nLenBuf = nCfgStrLen;
                    }
                    else // 如果PDSCfg.db中没有配置#长度，则直接使用实际长度
                    {
                        if (0 == nActualLen)    // 如果下发空字符串，则补齐'\0'
                            nActualLen++;

                        if (szTemp[nActualLen - 1] != '\0') // 不以'\0'结尾，则补齐'\0'
                            nActualLen++;

                        msg.m_nLenBuf = nActualLen;
                    }

                    msg.m_pBuf = szTemp;

                    long ret = m_netWrapper->SendCtrlToDriver(tempVariable.m_strDriverName.c_str(), msg); // get driver type
                    if (ret != ICV_SUCCESS)
                    {
                        CV_ERROR(g_asLog, -1, "CtrlCmd: SendCtrlToDriver failed. Invalid tag name: [%s], driver name: [%s], length: [%d].", iter->first.c_str(), tempVariable.m_strDriverName.c_str(), msg.m_nLenBuf);
                        continue;
                    }
                    else
                    {
                        CV_INFO(g_asLog, "CtrlCmd: SendCtrlToDriver success. Tag name: [%s], driver name: [%s], length: [%d].", iter->first.c_str(), tempVariable.m_strDriverName.c_str(), msg.m_nLenBuf);
                    }
                    // std::cout << "send to driver " << tempVariable.m_strDriverName << " 有效长度: " << h2 << std::endl;
                    // m_netWrapper->SendCtrlToDriver("tdrv",msg);//get driver type
                }
                else
                {
                    if(iter->second.size() != tempVariable.m_nLenBuf)
                    {
                        CV_ERROR(g_asLog, -1, "CtrlCmd: cmd length not match. Invalid tag name: [%s], Received length: [%d], Defined length: [%d].",iter->first.c_str(), iter->second.size(), tempVariable.m_nLenBuf);
                        continue;
                    }

                    char *temp = new char[tempVariable.m_nLenBuf];
                    memcpy(temp, iter->second.c_str(), iter->second.size()); // debug it

                    PointInfo info;
                    if (m_pConfigParser->GetPointInfo(tempVariable.m_nTagID, info))
                    {
                        if (info.byteOrder.length() == tempVariable.m_nLenBuf)
                        {
                            std::string str = info.byteOrder;
                            for (int i = 0; i < tempVariable.m_nLenBuf; ++i)
                            {
                                char c = str.at(i);
                                memcpy(temp + i, iter->second.c_str() + c - 49, 1);
                            }
                        }
                    }

                    msg.m_pBuf = temp;
                    long ret = m_netWrapper->SendCtrlToDriver(tempVariable.m_strDriverName.c_str(), msg); // get driver type
                    if (ret != ICV_SUCCESS)
                    {
                        CV_ERROR(g_asLog, -1, "CtrlCmd: SendCtrlToDriver failed. Invalid tag name: [%s], driver name: [%s], length: [%d].", iter->first.c_str(), tempVariable.m_strDriverName.c_str(), tempVariable.m_nLenBuf);
                        continue;
                    }
                    else
                    {
                        CV_INFO(g_asLog, "CtrlCmd: SendCtrlToDriver success. Tag name: [%s], driver name: [%s], length: [%d].", iter->first.c_str(), tempVariable.m_strDriverName.c_str(), tempVariable.m_nLenBuf);
                    }
                    // std::cout << "send to driver " << tempVariable.m_strDriverName << "successed length: " << msg.m_nLenBuf << std::endl;
                    // m_netWrapper->SendCtrlToDriver("tdrv",msg);//get driver type
                    delete[] temp;
                }
            }
            else
            {
                CV_ERROR(g_asLog, -1, "CtrlCmd: invaild tag name: %s.", iter->first.c_str());
            }
        }
    }
}
