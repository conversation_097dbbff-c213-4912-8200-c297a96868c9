#include "NetWrapper.h"
#include "common/LogHelper.h"
#include "errcode/error_code.h"

extern CCVLog g_asLog;

#define SAVE_DATA_QUEUE_MAX_SIZE 100000

CNetWrapper::CNetWrapper()
{
	m_hLocalQueRecvData = NULL;
	m_hLocalQueRegDriver = NULL;
	m_bExit = false;
	m_nThreadID = -1;
}

CNetWrapper::~CNetWrapper()
{

}

long CNetWrapper::InitializeNetwork()
{
	m_hLocalQueRecvData = NULL;
	m_hLocalQueRegDriver = NULL;

	// adopting syncronous sending mode
	long nRet = CVNDK_Init(SYNC_SEND);
	CV_CHECK_FAIL_LOG_RETURN_ERR(g_asLog, nRet, nRet, "CVNDK_Init(SYNC_SEND");

	m_hLocalQueRecvData = CVNDK_RegLocalQueue(DRVAPI_SERVER_DATA_QUEUE_ID);
	CV_CHECK_NULL_LOG_RETURN_ERR(g_asLog, m_hLocalQueRecvData, -1, "CVNDK_RegLocalQueue");

	m_hLocalQueRegDriver = CVNDK_RegLocalQueue(DRVAPI_SERVER_CTRL_QUEUE_ID);
	CV_CHECK_NULL_LOG_RETURN_ERR(g_asLog, m_hLocalQueRegDriver, -1, "CVNDK_RegLocalQueue");

	unsigned short usPort = (unsigned short)CVComm.GetServicePort("DRAcquisitionService", ICV_DRACQUISITIONSERVICE_DEFAULT_TCPPORT);
	CV_INFO(g_asLog, "DRAcquisitionService GetServicePort %d", usPort);
	CV_CHECK_TRUE_LOG_RETURN_ERR(g_asLog, 0 == usPort, EC_ICV_PDB_PORTCFGERROR, "GetServicePort");

	nRet = CVNDK_Listen(usPort);
	CV_CHECK_FAIL_LOG_RETURN_ERR(g_asLog, nRet, nRet, "CVNDK_Listen port %d", usPort);
	CV_INFO(g_asLog, "DRAcquisitionService CVNDK_Listen port %d", usPort);
	CV_INFO(g_asLog, "InitializeNetwork done");
	return ICV_SUCCESS;
}

long CNetWrapper::UnInitializeNetwork()
{
	long nRet = CVNDK_ReleaseQueue(m_hLocalQueRecvData);
	CV_CHECK_FAIL_LOG(g_asLog, nRet, nRet, "CVNDK_ReleaseQueue");

	nRet = CVNDK_ReleaseQueue(m_hLocalQueRegDriver);
	CV_CHECK_FAIL_LOG(g_asLog, nRet, nRet, "CVNDK_ReleaseQueue");

	nRet = CVNDK_Finalize();
	CV_CHECK_FAIL_LOG(g_asLog, nRet, nRet, "CVNDK_Finalize");
	CV_INFO(g_asLog, "UnInitializeNetwork done");
	return nRet;
}

long CNetWrapper::SendCtrlToDriver(const char* szDriverName, const TProtoDriverAPICTRLMsg& ctrlMsg)
{
	long lRet = 0;
	std::map<std::string, HQUEUE>::iterator it = m_mapDriverName2CliQueue.find(szDriverName);
	if (m_mapDriverName2CliQueue.end() == it)
	{
		return EC_ICV_PDB_DRIVER_NOT_REGISTER;
	}
	char* pBuf = NULL;
	int32 nLenBuf = 0;
	proto_driverapi_ctrl_pack(ctrlMsg, &pBuf, &nLenBuf);
	lRet = CVNDK_Send(it->second, m_hLocalQueRegDriver, pBuf, nLenBuf);
	proto_driverapi_free(pBuf);
	return lRet;
}

bool CNetWrapper::IsDriverRegister(const char* szDriverName)
{
	return m_mapDriverName2CliQueue.find(szDriverName) != m_mapDriverName2CliQueue.end();
}

// driver register 
void* CNetWrapper::ThreadRegDirver(void* pArg)
{
	CNetWrapper* pNet = (CNetWrapper*)pArg;
	long nRet = ICV_SUCCESS;
	HQUEUE hSrcQueue = NULL;
	void* szRecvBuf = NULL;
	long lRecvBytes = 0;
	std::string strDriverName;

	while(!pNet->m_bExit)
	{
		hSrcQueue = NULL;
		szRecvBuf = NULL;
		lRecvBytes = 0;

		nRet = CVNDK_Recv(pNet->m_hLocalQueRegDriver, &hSrcQueue, &szRecvBuf, lRecvBytes);	
		CV_CHECK_FAIL_LOG_CONTINUE(g_asLog, nRet, nRet, "CVNDK_Recv");
		CV_DEBUG(g_asLog, "CVNDK_Recv m_hLocalQueRegDriver, RetCode: %d lRecvBytes %d",  nRet, lRecvBytes);
		
		// unpack driver register message
		nRet = proto_driverapi_register_unpack((char*)szRecvBuf, lRecvBytes, &strDriverName);
		CVNDK_Free(szRecvBuf);

		if (nRet != ICV_SUCCESS)
		{
			CVNDK_ReleaseQueue(hSrcQueue);
			CV_ERROR(g_asLog, nRet, "proto_driverapi_register_unpack Failed, RetCode: %d lRecvBytes %d",  nRet, lRecvBytes);
			continue;
		}
		
		// get driver name
		transform(strDriverName.begin(), strDriverName.end(), strDriverName.begin(), (int(*)(int))tolower);

		// get ip
		char szIP[30]={'\0'};
		CVNDK_GetQueueIPStr(hSrcQueue, szIP);

		// get port
		unsigned short nPort = 0;
		CVNDK_GetQueuePort(hSrcQueue, nPort);

		CV_INFO(g_asLog, "driver %s register ip %s : %d",  strDriverName.c_str(), szIP, (int)nPort);

		std::map<std::string, HQUEUE>::iterator it = pNet->m_mapDriverName2CliQueue.find(strDriverName);
		if (it!=pNet->m_mapDriverName2CliQueue.end())
		{
			CV_INFO(g_asLog, "driver %s has register, clear old register data",  strDriverName.c_str() );
			CVNDK_ReleaseQueue(it->second);
			pNet->m_mapDriverName2CliQueue.erase(it);
		}

		// insert new driver
		pNet->m_mapDriverName2CliQueue.insert(std::pair<std::string, HQUEUE>(strDriverName, hSrcQueue));
		continue;
	}
	return NULL;
}

int CNetWrapper::svc(void)
{
	long nRet = ICV_SUCCESS;
	HQUEUE hSrcQueue = NULL;
	void* szRecvBuf = NULL;
	long lRecvBytes = 0;

	while(!m_bExit)
	{
		hSrcQueue = NULL;
		szRecvBuf = NULL;
		lRecvBytes = 0;

		nRet = CVNDK_Recv(m_hLocalQueRecvData, &hSrcQueue, &szRecvBuf, lRecvBytes);		
		CV_CHECK_FAIL_LOG_CONTINUE(g_asLog, nRet, nRet, "CVNDK_Recv m_hLocalQueRecvData");

		if (ICV_SUCCESS == nRet)
		{
			CV_DEBUG(g_asLog, "CVNDK_Recv m_hLocalQueRecvData, RetCode: %d lRecvBytes %d",  nRet, lRecvBytes);
			CV_CHECK_TRUE_LOG_CONTINUE(g_asLog, lRecvBytes < sizeof(TProtoDriverAPIHeader),  -1, "invalid cmd len %d", lRecvBytes);
			
			//get header
			TProtoDriverAPIHeader header;
			memcpy(&header, szRecvBuf, sizeof(header));

			switch (header.m_nCmdType)
			{
				case DRIVERAPI_CMD_QUERY_RY_STATUS:
					CV_DEBUG(g_asLog,"rm feature is removed,so ignore it");
					break;

				case DRIVERAPI_CMD_SAVE_DATA:
				case DRIVERAPI_CMD_SET_BLOCKQUALITY:
				case DRIVERAPI_CMD_SAVE_DRV_STATUS:
				case DRIVERAPI_CMD_SAVE_DEV_STATUS:
					{
						ACE_Message_Block*  pMsg = new ACE_Message_Block(lRecvBytes);
						pMsg->copy((char*)(szRecvBuf), lRecvBytes);

						// unsigned char* byteBuffer = (unsigned char*)szRecvBuf;
						// for (size_t i = 0; i < lRecvBytes; ++i) {
						// 	printf("%02X ", byteBuffer[i]);
						// }
						// printf("\n");
						m_dataQueue.enqueue(pMsg);
						if (m_dataQueue.size() > 10000)
						{
							CV_ERROR(g_asLog, -1, "DRIVERAPI_CMD_SAVE_DATA queue size %d", m_dataQueue.size());
						}
					}
					break;
				default:
					CV_ERROR(g_asLog, -1, "invalid cmd type %d from driver", header.m_nCmdType);
					break;
			}

			CVNDK_ReleaseQueue(hSrcQueue);
			CVNDK_Free(szRecvBuf);
		}
	}

	CV_INFO(g_asLog, "NetWrapper recv thread return!");
	return nRet;
}

long CNetWrapper::Activate()
{
	// new thread
	ACE_Thread::spawn((ACE_THR_FUNC)(ThreadRegDirver), this, THR_JOINABLE | THR_NEW_LWP,  &m_nThreadID, &m_hThreadHandle);

	// active ace thread
	this->activate();
	return ICV_SUCCESS;
}

void CNetWrapper::ShutDown()
{
	//shutdown thread
	m_bExit = true;
	CVNDK_Recv_Quit(m_hLocalQueRegDriver);
	ACE_Thread::join(m_hThreadHandle);
	CVNDK_Recv_Quit(m_hLocalQueRecvData);

	// wait ace thread
	this->wait();
}

