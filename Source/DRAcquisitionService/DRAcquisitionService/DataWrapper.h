#ifndef _DATAWRAPPER_H_
#define _DATAWRAPPER_H_

#include "ace/Message_Block.h"
#include "ace/Singleton.h"
#include "ace/Task.h"
#include "common/SimpleQueue.h"
#include "proto/proto_driverapi_pdb.h"
#include "DSFRedisWrapper.h"
#include <condition_variable>
#include <mutex>
#include <queue>
#include <set>
#include <string>
#include <unordered_map>
#include <vector>
#include "SaveDataHandler.h"
#include "ConfigParser.h"

typedef struct {
    std::string objName = "";
    int         len = 0;
    char*       buf = nullptr;
    int32_t sec;       // 时间戳秒部分
    int16_t ms;        // 时间戳毫秒部分
    int16_t quality;   // 质量字段
}LocalObjectData;

typedef struct{
    std::string tagName = "";
    std::string objName = "";
    int         tagId = -1;
    int         len = 0;
    char*       buf = nullptr;
}LocalVariableData;


class CDataWrapper : public ACE_Task_Base
{
friend class ACE_Singleton<CDataWrapper, ACE_Thread_Mutex>;

public:
	CDataWrapper();
	virtual ~CDataWrapper();

public:
	bool Initialize();
	virtual int svc(void);
	void ShutDown();
    // bool SetData(const std::vector<TProtoIDVTQ> &msg);
    bool SetData(ACE_Message_Block *pMsg);
    bool SetByteOrderMap(const std::unordered_map<int, TagByteOrder> &map);
    bool SetBadDevName(const std::string &devName);

public:
    std::unordered_map<int, std::string> m_idToDevNameMap;

private:
    bool GetObjectName(const std::string &varName, std::string &objName);
    bool WriteToRedis(const std::set<std::string>& modifiedObj);

private:
    std::unordered_map<std::string, LocalObjectData> m_localObjects;
    std::unordered_map<int, LocalVariableData> m_localVars;
    std::unordered_map<std::string, std::set<std::string>> m_devToObjNameMap;
    std::set<std::string> m_modifiedObj;
    std::set<std::string> m_badDevNameSet;

    bool m_bStopped;
    // std::mutex m_dataMutex;
    std::mutex m_condMutex;
    std::condition_variable m_condVariable;
    std::queue<ACE_Message_Block*> m_dataQueue;

    std::shared_ptr<DSFRedisWrapper> m_redis;

    std::unordered_map<int, TagByteOrder> m_idToByteOrder;
};

typedef ACE_Singleton<CDataWrapper, ACE_Thread_Mutex> CDataWrapperSingleton;
#define DR_DATAWRAPPER CDataWrapperSingleton::instance()

#endif // End of #ifndef _DATAWRAPPER_H_
