#include <iostream>
#include "PMRAPI.h" // 包含 PMRAPI 接口的头文件
#include "errcode/error_code.h"
#include <vector>
using namespace std;

int main(int argc, char* argv[]) {
    const char* agentIP = "127.0.0.1"; // 代理的 IP 地址
    // std::vector<ProcessInfo> processInfoList;
    //55004 是port.xml里指定的DRProcessMgr的端口号
    long lRet = PMRAPI_InitEx(agentIP, 55004);
    if (lRet == ICV_SUCCESS) {
        //停止全部进程
        lRet = PMRAPI_QUIT(agentIP);
        sleep(3);
        if(lRet == ICV_SUCCESS){
            std::cout << "Successfully stopped all the process" << std::endl;
        } else {
            std::cout << "Failed to stop all the process, error code: " << lRet << std::endl;
        }
    } else {
        std::cout << "Failed to connect to the remote processmgr" << std::endl;
    }

    return 0;
}