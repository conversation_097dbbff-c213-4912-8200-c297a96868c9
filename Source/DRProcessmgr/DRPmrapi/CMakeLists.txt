cmake_minimum_required(VERSION 2.6)
############FOR_MODIFIY_BEGIN#######################
# Setting Project Name
PROJECT(drpmrapi)

INCLUDE($ENV{DRDIR}CMakeCommon)

# Setting Source Files
SET(SRCS ${SRCS} PMRAPI.cpp)

# Setting Target Name (library name)
SET(TARGET_NAME drpmrapi)
# Setting library type used when build a library
# STATIC/SHARED
SET(LIB_TYPE SHARED)

SET(LINK_LIBS ACE drnetqueue drcomm drlog drlogimpl driobuffer intl)
IF(HPUX)
SET(LINK_LIBS ${LINK_LIBS} pthread)
ENDIF(HPUX)

############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommon)

# 添加生成库的指令
ADD_LIBRARY(${TARGET_NAME} ${LIB_TYPE} ${SRCS})
TARGET_LINK_LIBRARIES(${TARGET_NAME} ${LINK_LIBS})

# 添加生成可执行文件的指令
ADD_EXECUTABLE(dsfpmrapi_quit main.cpp)
TARGET_LINK_LIBRARIES(dsfpmrapi_quit ${TARGET_NAME} ${LINK_LIBS})