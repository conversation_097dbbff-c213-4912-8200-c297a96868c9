#ifndef CFGFILEHELPER_HXX
#define CFGFILEHELPER_HXX

#include "ace/OS_NS_strings.h"
#include <ace/Default_Constants.h>
#include "../DRPmrapi/ProcMngrDef.h"
#include <string>
#include <list>

typedef std::list<PROJECT_CONFIG> ProjCfgList;
typedef ProjCfgList::iterator ProjCfgListIter;
typedef std::list<std::string> ProjPathList;
typedef ProjPathList::iterator ProjPathListIter;

class CCfgFileHelper
{
public:
	long GetAllProjectCfg(ProjCfgList &listProjectCfg);
	long GetActiveProject(std::string &strActiveProject);
	long GetDefProjSavePath(std::string &strProjectPath);
	long UpdateActiveProject(const std::string &strActiveProject);
	long AddProject(const PROJECT_CONFIG &projCfg);
	long RemoveProject(const std::string &strProjectPath);
	long ModifyProject(const std::string &strProjectName, const PROJECT_CONFIG &projCfg);
	long GetProjectCfgByPath(const std::string &strProjectPath, PROJECT_CONFIG &projCfg);
	long UpdateProjectCfgFile(const PROJECT_CONFIG &projCfg);
	long UpdateProjectsFile(const PROJECT_CONFIG &projCfg, bool bRemove);
protected:
	long GetAllProjectPath(ProjPathList &listProjectPath);
	long GetTemplateProjectPath(std::string &strTemplatePath);
private:
	ProjCfgList m_listProjectCfg;
	std::string m_strActiveProject;
	ProjPathList m_listProjectPath;
	std::string m_strTemplatePath;
};

#endif
