#ifndef CMDHANDLER_HXX
#define CMDHANDLER_HXX

#include "common/CVNDK.h"
//#include "../backuprestoreproject/include/common/BackResCallBackDef.h"
#include "../backuprestoreproject/BackResCallBackDef.h"
#include "CfgFileHelper.h"
#include "ace/DLL.h"

typedef long (*PFN_ExtractProject)( const char* szSourceFile, const char* szDestFolder,PFN_BackResProgress pfnProgress);
typedef long (*PFN_ZipProject)(const char* szSrcDir, const char* szDestName, bool bZipHtdData, bool bZipAlmData, PFN_BackResProgress pfnProgress);

class CProjectHandler
{
public:
	CProjectHandler();
	~CProjectHandler();
	void HandleGetProjectTemplates(HQUEUE hLocalQueue, HQUEUE hRemoteQueue, void *pBuffer, long lBufferLen);
	void HandleGetActiveProject(HQUEUE hLocalQueue, HQUEUE hRemoteQueue, void *pBuffer, long lBufferLen);
	void HandleGetProjectPath(HQUEUE hLocalQueue, HQUEUE hRemoteQueue, void *pBuffer, long lBufferLen);
	void HandleGetAllProjects(HQUEUE hLocalQueue, HQUEUE hRemoteQueue, void *pBuffer, long lBufferLen);
	void HandleCreateProject(HQUEUE hLocalQueue, HQUEUE hRemoteQueue, void *pBuffer, long lBufferLen);
	void HandleRemoveProject(HQUEUE hLocalQueue, HQUEUE hRemoteQueue, void *pBuffer, long lBufferLen);
	void HandleModifyProject(HQUEUE hLocalQueue, HQUEUE hRemoteQueue, void *pBuffer, long lBufferLen);
	void HandleStartProject(HQUEUE hLocalQueue, HQUEUE hRemoteQueue, void *pBuffer, long lBufferLen);
	void HandleStopProject(HQUEUE hLocalQueue, HQUEUE hRemoteQueue, void *pBuffer, long lBufferLen);
	void HandleBackUpProject(HQUEUE hLocalQueue, HQUEUE hRemoteQueue, void *pBuffer, long lBufferLen);
	void HandleRestoreProject(HQUEUE hLocalQueue, HQUEUE hRemoteQueue, void *pBuffer, long lBufferLen);
protected:
	long LoadBackupRestoreDll();
private:
	CCfgFileHelper m_cfgFileHelper;
	PFN_ExtractProject m_fnExtractProject;
	PFN_ZipProject m_fnZipProject;
	ACE_DLL m_objBackupRestoreDll;
};
#endif
