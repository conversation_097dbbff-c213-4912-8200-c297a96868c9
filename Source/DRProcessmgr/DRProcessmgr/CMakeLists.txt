cmake_minimum_required(VERSION 2.6)

PROJECT (DRDrocessmgr)

INCLUDE($ENV{DRDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
#Setting Source Files
SET(SRCS ${SRCS} DRProcess.cpp ProcessMngr.cpp RequestHandler.cpp main.cpp CfgFileHelper.cpp ProjectHandler.cpp ProjectHandlerThread.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME dsfprocessmgr)
#Setting library type used when build a library
#SET(LIB_TYPE STATIC)

SET(LINK_LIBS ACE drnetqueue drlog drlogimpl drcomm tinyxml shmqueue driobuffer drrmapi)


INCLUDE($ENV{DRDIR}CMakeCommonExec)
