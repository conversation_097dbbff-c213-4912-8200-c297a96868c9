cmake_minimum_required(VERSION 2.6)
############FOR_MODIFIY_BEGIN#######################
#Setting Project Name
PROJECT (backuprestoreproject)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} BackupRestoreProject.cpp BackupRestoreImpl.cpp )

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME backuprestoreproject)
#Setting library type used when build a library
#SET(LIB_TYPE STATIC)

SET(LIB_TYPE SHARED)

SET(LINK_LIBS ACE drcomm drlog drlogimpl)

IF(${CMAKE_SYSTEM_NAME} MATCHES "Windows")
	SET(LINK_LIBS ${LINK_LIBS} zlibwapi)
ELSE(${CMAKE_SYSTEM_NAME} MATCHES Windows)
	SET(LINK_LIBS ${LINK_LIBS} z)
ENDIF(${CMAKE_SYSTEM_NAME} MATCHES "Windows")

#define ZLIB_WINAPI to use zlib
#ADD_DEFINITIONS(-DZLIB_WINAPI)

#IF(HPUX)
#SET(LINK_LIBS ${LINK_LIBS} pthread )
#ENDIF(HPUX)

############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)

