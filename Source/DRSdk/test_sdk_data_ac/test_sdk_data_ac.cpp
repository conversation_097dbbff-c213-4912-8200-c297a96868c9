#include "dsfapi.h"
#include "drsdk_inner.h"
#include <atomic>
#include <chrono>
#include <csignal>
#include <iostream>
#include <thread>
#include <string_view>
#include <algorithm>
#include <numeric>

std::atomic<bool> g_bStop(false);

void signalHandler(int signal)
{
    if (signal == SIGINT)
    {
        std::cout << "\nCaught Ctrl+C (SIGINT). Exiting loop..." << std::endl;
        g_bStop = true;
    }
}

void TestRead(dsfapi::DRSdkContext *pContext)
{
    // 正确输入1:单点查询
    {
        static int NUM = 1;
        char *pTagName[] = {"STD.TEST_BOOL1"};
        dsfapi::TagValue *pTagValue = nullptr;
        int32 *pErrorCode = nullptr;
        int32 nTagValueCount = 0;
        auto res = dsfapi::DR_Read(pContext, (const char **)pTagName, NUM, &pTagValue, &pErrorCode, &nTagValueCount);
        LOG_INFO << "DR_Read:res = " << res << std::endl;
        if (0 == res)
        {
            for (int i = 0; i < nTagValueCount; i++)
            {
                LOG_INFO << "DR_Read: errorCode:" << pErrorCode[i] << ", len:" << pTagValue[i].Length()
                         << ", buffer:" << pTagValue[i].Buffer() << std::endl;
            }
        }
        // Must be released
        dsfapi::Free_Int32_Ptr(&pErrorCode);
        dsfapi::Free_Tag_Value(&pTagValue);
    }
}
void TestWrite(dsfapi::DRSdkContext *pContext)
{
    static int NUM = 3;
    char *pTagName[] = {"wzq.name", "wzq.age", "wzq.weight"};
    dsfapi::TagValue tTagValue[NUM];

    std::string sValue = "wzq";
    tTagValue[0].ReSet(sValue.data(), sValue.size());

    char buffer[32] = {};
    sprintf(buffer, "%d", 100);
    tTagValue[1].ReSet(buffer, strlen(buffer));

    sprintf(buffer, "%f", 1.23);
    tTagValue[2].ReSet(buffer, strlen(buffer));

    auto res = dsfapi::DR_Write(pContext, (const char **)pTagName, tTagValue, NUM);
    LOG_INFO << "dr_write:res = " << res << std::endl;
}

void TestSave(dsfapi::DRSdkContext *pContext)
{
    static int NUM = 3;
    char *pTagName[] = {"wzq.save1", "wzq.save2", "wzq.save3"};
    dsfapi::TagValue tTagValue[NUM];
    for (int i = 0; i < NUM; ++i)
    {
        std::string sValue = "wzq.value" + std::to_string(i);
        tTagValue[i].ReSet(sValue.data(), sValue.size());
    }
    auto res = dsfapi::Dr_Save(pContext, (const char **)pTagName, tTagValue, NUM);
    LOG_INFO << "dr_save:res = " << res << std::endl;
}

void TestReadTagtypeInfo(dsfapi::DRSdkContext *pContext)
{

    const char *pObjectName = "wzq_object1";
    dsfapi::TagtypeInfo *pTagtypeInfo = nullptr;
    int nTagtypeInfoCount = 0;
    auto res = dsfapi::DR_Get_Tagtype_Info(pContext, pObjectName, &pTagtypeInfo, &nTagtypeInfoCount);
    LOG_INFO << "dr_get_tagtype_info:res = " << res << std::endl;
    if (0 == res)
    {
        for (int i = 0; i < nTagtypeInfoCount; i++)
        {
            LOG_INFO << "dr_get_tagtype_info. tagname:" << pTagtypeInfo[i].ToString() << std::endl;
        }
    }

    // Must be released
    dsfapi::Free_Tagtype_Info(&pTagtypeInfo);
}

void TestReadTagValue(dsfapi::DRSdkContext *pContext)
{

    const char *pObjectName = "wzq_object1";
    dsfapi::TagRecord *pTagRecord = nullptr;
    int nTagRecordCount = 0;
    auto res = dsfapi::DR_Get_Tag_Value(pContext, pObjectName, &pTagRecord, &nTagRecordCount);
    LOG_INFO << "dr_get_tag_value:res = " << res << std::endl;
    if (0 == res)
    {
        for (int i = 0; i < nTagRecordCount; i++)
        {
            LOG_INFO << "dr_get_tag_value. tagname:" << pTagRecord[i].tTagName.Buffer()
                     << ",tagvalue:" << pTagRecord[i].tTagValue.Buffer() << std::endl;
        }
    }
    // Must be released
    dsfapi::Free_Tag_Record(&pTagRecord);
}

dsfapi::DRSdkContext *TestDrsdkInit()
{
    // 1.shoud init first
    dsfapi::DRSdkConnectParam tConnectParam;
    strncpy(tConnectParam.szServerIp, "127.0.0.1", sizeof(tConnectParam.szServerIp) - 1);
    tConnectParam.nServerPort = 1234;
    tConnectParam.nConnectTimeoutMs = 1000;

    dsfapi::DRSdkOption tOption;
    tOption.nThreadPoolNum = 10; // thread nums for run  register callback
    tOption.nRequestWaitTimeoutMs = 10000;

    dsfapi::DRSdkContext *pContext = nullptr;
    pContext = dsfapi::DR_Init(tConnectParam, tOption);
    if (nullptr == pContext || pContext->errorcode != EC_DSF_SDK_SUCCESS)
    {
        LOG_INFO << "dr_sdk_init failed!";
        dsfapi::Free_DR_Sdk_Context(&pContext);
        return nullptr;
    }
    return pContext;
}

void TestReadInt32FromRedis(dsfapi::DRSdkContext *pContext)
{
    static int NUM = 1;
    char *pTagName[] = {"STD.TEST_DINT"};
    dsfapi::TagValue *pTagValue = nullptr;
    dsfapi::TagtypeInfo *pTagtypeInfo = nullptr;
    int nTagtypeInfoCount = 0;
    auto resAtr = dsfapi::DR_Get_Tagtype_Info(pContext, pTagName[0], &pTagtypeInfo, &nTagtypeInfoCount);
    LOG_INFO << "dr_get_tagtype_info:res = " << resAtr << std::endl;
    if (0 == resAtr)
    {
        for (int i = 0; i < nTagtypeInfoCount; i++)
        {
            LOG_INFO << "dr_get_tagtype_info. tagname:" << pTagtypeInfo[i].ToString() << std::endl;
        }
        if (pTagtypeInfo[0].nType == dsfapi::SDK_ATTR_TYPE_INT32)
        {
            int32 *pErrorCode = nullptr;
            int32 nTagValueCount = 0;
            auto res = dsfapi::DR_Read(pContext, (const char **)pTagName, NUM, &pTagValue, &pErrorCode, &nTagValueCount);
            LOG_INFO << "dr_read:res = " << res << std::endl;
            if (0 == res)
            {
                for (int i = 0; i < nTagValueCount; i++)
                {
                    // 将二进制字符串转换为 int32 整数
                    int32_t value = *reinterpret_cast<const int32_t *>(pTagValue[i].Buffer());

                    // 使用 stringstream 转换整数为十六进制字符串
                    std::stringstream ss;
                    ss << "0x" << std::hex << std::uppercase << value; // std::hex 设置为十六进制格式，std::uppercase 使字母大写

                    // 打印日志
                    LOG_INFO << "dr_read_data: vecTagName: " << pTagName[i] << ", value: " << ss.str() << std::endl;
                }
            }
        }
    }
    // Must be released
    dsfapi::Free_Tagtype_Info(&pTagtypeInfo);
    // Must be released
    dsfapi::Free_Tag_Value(&pTagValue);
}
void TestReadInt16FromRedis(dsfapi::DRSdkContext *pContext)
{
    static int NUM = 1;
    char *pTagName[] = {"STD.TEST_INT"};
    dsfapi::TagValue *pTagValue = nullptr;
    dsfapi::TagtypeInfo *pTagtypeInfo = nullptr;
    int nTagtypeInfoCount = 0;
    auto resAtr = dsfapi::DR_Get_Tagtype_Info(pContext, pTagName[0], &pTagtypeInfo, &nTagtypeInfoCount);
    LOG_INFO << "dr_get_tagtype_info:res = " << resAtr << std::endl;
    if (0 == resAtr)
    {
        for (int i = 0; i < nTagtypeInfoCount; i++)
        {
            LOG_INFO << "dr_get_tagtype_info. tagname:" << pTagtypeInfo[i].ToString() << std::endl;
        }
        if (pTagtypeInfo[0].nType == dsfapi::SDK_ATTR_TYPE_INT16)
        {
            int32 *pErrorCode = nullptr;
            int32 nTagValueCount = 0;
            auto res = dsfapi::DR_Read(pContext, (const char **)pTagName, NUM, &pTagValue, &pErrorCode, &nTagValueCount);
            LOG_INFO << "dr_read:res = " << res << std::endl;
            if (0 == res)
            {
                for (int i = 0; i < nTagValueCount; i++)
                {
                    // 将二进制字符串转换为 int32 整数
                    int16_t value = *reinterpret_cast<const int16_t *>(pTagValue[i].Buffer());

                    // 使用 stringstream 转换整数为十六进制字符串
                    std::stringstream ss;
                    ss << "0x" << std::hex << std::uppercase << value; // std::hex 设置为十六进制格式，std::uppercase 使字母大写

                    // 打印日志
                    LOG_INFO << "dr_read_data: vecTagName: " << pTagName[i] << ", value: " << ss.str() << std::endl;
                }
            }
        }
    }
    // Must be released
    dsfapi::Free_Tagtype_Info(&pTagtypeInfo);
    // Must be released
    dsfapi::Free_Tag_Value(&pTagValue);
}
void TestReadBoolFromRedis(dsfapi::DRSdkContext *pContext)
{
    static int NUM = 1;
    char *pTagName[] = {"STD.TEST_BOOL"};
    dsfapi::TagValue *pTagValue = nullptr;
    dsfapi::TagtypeInfo *pTagtypeInfo = nullptr;
    int nTagtypeInfoCount = 0;
    auto resAtr = dsfapi::DR_Get_Tagtype_Info(pContext, pTagName[0], &pTagtypeInfo, &nTagtypeInfoCount);
    LOG_INFO << "dr_get_tagtype_info:res = " << resAtr << std::endl;
    if (0 == resAtr)
    {
        for (int i = 0; i < nTagtypeInfoCount; i++)
        {
            LOG_INFO << "dr_get_tagtype_info. tagname:" << pTagtypeInfo[i].ToString() << std::endl;
        }
        if (pTagtypeInfo[0].nType == dsfapi::SDK_ATTR_TYPE_BOOL)
        {
            int32 *pErrorCode = nullptr;
            int32 nTagValueCount = 0;
            auto res = dsfapi::DR_Read(pContext, (const char **)pTagName, NUM, &pTagValue, &pErrorCode, &nTagValueCount);
            LOG_INFO << "dr_read:res = " << res << std::endl;
            if (0 == res)
            {
                for (int i = 0; i < nTagValueCount; i++)
                {
                    // 将char指针转换回bool指针，然后解引用它来获取bool值
                    bool value = *reinterpret_cast<bool *>(pTagValue[i].Buffer());
                    // 打印日志
                    LOG_INFO << "dr_read_data: vecTagName: " << pTagName[i] << ", value: " << std::boolalpha << value << std::endl;
                }
            }
        }
    }
    // Must be released
    dsfapi::Free_Tagtype_Info(&pTagtypeInfo);
    // Must be released
    dsfapi::Free_Tag_Value(&pTagValue);
}

void TestReadCharFromRedis(dsfapi::DRSdkContext *pContext)
{
    static int NUM = 1;
    char *pTagName[] = {"STD.TEST_CHAR"};
    dsfapi::TagValue *pTagValue = nullptr;
    dsfapi::TagtypeInfo *pTagtypeInfo = nullptr;
    int nTagtypeInfoCount = 0;
    auto resAtr = dsfapi::DR_Get_Tagtype_Info(pContext, pTagName[0], &pTagtypeInfo, &nTagtypeInfoCount);
    LOG_INFO << "dr_get_tagtype_info:res = " << resAtr << std::endl;
    if (0 == resAtr)
    {
        for (int i = 0; i < nTagtypeInfoCount; i++)
        {
            LOG_INFO << "dr_get_tagtype_info. tagname:" << pTagtypeInfo[i].ToString() << std::endl;
        }
        if (pTagtypeInfo[0].nType == dsfapi::SDK_ATTR_TYPE_INT8)
        {
            int32 *pErrorCode = nullptr;
            int32 nTagValueCount = 0;
            auto res = dsfapi::DR_Read(pContext, (const char **)pTagName, NUM, &pTagValue, &pErrorCode, &nTagValueCount);
            LOG_INFO << "dr_read:res = " << res << std::endl;
            if (0 == res)
            {
                for (int i = 0; i < nTagValueCount; i++)
                {
                    // 将二进制字符串转换为 char 字符
                    char value = *reinterpret_cast<const char *>(pTagValue[i].Buffer());

                    // 直接打印字符
                    LOG_INFO << "dr_read_data: vecTagName: " << pTagName[i] << ", value: '" << value << "'" << std::endl;
                }
            }
        }
    }
    // Must be released
    dsfapi::Free_Tagtype_Info(&pTagtypeInfo);
    // Must be released
    dsfapi::Free_Tag_Value(&pTagValue);
}

void TestReadStringFromRedis(dsfapi::DRSdkContext *pContext)
{
    static int NUM = 1;
    char *pTagName[] = {"STD.TEST_STRING"};
    dsfapi::TagValue *pTagValue = nullptr;
    dsfapi::TagtypeInfo *pTagtypeInfo = nullptr;
    int nTagtypeInfoCount = 0;
    auto resAtr = dsfapi::DR_Get_Tagtype_Info(pContext, pTagName[0], &pTagtypeInfo, &nTagtypeInfoCount);
    LOG_INFO << "dr_get_tagtype_info:res = " << resAtr << std::endl;
    if (0 == resAtr)
    {
        for (int i = 0; i < nTagtypeInfoCount; i++)
        {
            LOG_INFO << "dr_get_tagtype_info. tagname:" << pTagtypeInfo[i].ToString() << std::endl;
        }
        if (pTagtypeInfo[0].nType == dsfapi::SDK_ATTR_TYPE_STRING)
        {
            int32 *pErrorCode = nullptr;
            int32 nTagValueCount = 0;
            auto res = dsfapi::DR_Read(pContext, (const char **)pTagName, NUM, &pTagValue, &pErrorCode, &nTagValueCount);
            LOG_INFO << "dr_read:res = " << res << std::endl;
            if (0 == res)
            {
                for (int i = 0; i < nTagValueCount; i++)
                {
                    std::string value(pTagValue[i].Buffer(), pTagValue[i].nLength);
                    LOG_INFO << "dr_read_data: vecTagName: " << pTagName[i] << ", value: \"" << value << "\"" << std::endl;
                }
            }
        }
    }
    // Must be released
    dsfapi::Free_Tagtype_Info(&pTagtypeInfo);
    // Must be released
    dsfapi::Free_Tag_Value(&pTagValue);
}
void generateData(char *pTagName[])
{
    // 定义数据类型数组
    const char *types[] = {
        "BOOL", "BYTE", "WORD", "DWORD", "LWORD",
        "SINT", "USINT", "INT", "UINT", "DINT",
        "UDINT", "LINT", "ULINT", "REAL", "LREAL",
        "CHAR", "TIME", "LTIME"};
    const int TYPES_COUNT = 18;

    // 定义常量
    const int BOOL_COUNT_PER_FILE = 200;
    const int BYTE_COUNT_PER_FILE = 200;
    const int RECORDS_PER_TYPE = 1; // 每种类型除了BOOL和BYTE，每个文件生成1个记录
    const int UDT_COUNT = 500;      // UDT点的数量
    const int MODEL_COUNT = 500;    // 嵌套点的数量
    const int TOTAL_FILES = 10;     // 总共生成的文件数
    int currentUDTIndex = 1;        // 用于UDT的索引
    int index = 0;                  // 用于跟踪pTagName数组的当前索引

    // 生成BOOL和BYTE类型的点名，每种类型每文件200个，共10个文件
    for (int fileIndex = 1; fileIndex <= TOTAL_FILES; fileIndex++)
    {
        for (int i = 0; i < BOOL_COUNT_PER_FILE; i++)
        {
            std::sprintf(pTagName[index++], "STD.TEST_BOOL%d", i + 1 + (BOOL_COUNT_PER_FILE * (fileIndex - 1)));
        }
        for (int i = 0; i < BYTE_COUNT_PER_FILE; i++)
        {
            std::sprintf(pTagName[index++], "STD.TEST_BYTE%d", i + 1 + (BYTE_COUNT_PER_FILE * (fileIndex - 1)));
        }
    }

    // 生成剩余类型的点名
    for (int fileIndex = 1; fileIndex <= TOTAL_FILES; fileIndex++)
    {
        for (int i = 2; i < TYPES_COUNT; i++)
        { // Skip BOOL and BYTE
            std::sprintf(pTagName[index++], "STD.TEST_%s%d", types[i], fileIndex);
        }
    }

    // 生成UDT类型的点名
    for (int j = 0; j < UDT_COUNT; j++)
    {
        std::sprintf(pTagName[index++], "STD.TEST_UDT%d", currentUDTIndex++);
    }

    // //生成嵌套类型的点名
    // for (int k = 0; k < MODEL_COUNT; k++)
    // {
    //     std::sprintf(pTagName[index++], "STD.TEST_MODEL%d", currentUDTIndex++);
    // }
}

// 分割tagNames到三个部分的函数
void splitTagNames(char *tagNames[], char *part1[], char *part2[], char *part3[])
{

    const int MAX_NAME_LENGTH = 256;
    const int TOTAL_POINTS = 4160 + 500; // 总点数
    const int PART1_SIZE = 2080;         // 第一部分的大小
    const int PART2_SIZE = 2080;         // 第二部分的大小
    const int PART3_SIZE = 500;          // 第三部分的大小

    // 复制前2080个元素到part1
    for (int i = 0; i < PART1_SIZE; ++i)
    {
        part1[i] = tagNames[i];
    }

    // 复制接下来的2080个元素到part2
    for (int i = 0; i < PART2_SIZE; ++i)
    {
        part2[i] = tagNames[i + PART1_SIZE];
    }

    // 复制剩下的500个元素到part3
    for (int i = 0; i < PART3_SIZE; ++i)
    {
        part3[i] = tagNames[i + PART1_SIZE + PART2_SIZE];
    }
}
void copyTagNames(char *tagNames[], char *tagCopy[], int start, int end, int totalSize)
{
    // 复制 tagNames 的所有元素到 tagCopy
    for (int i = 0; i < totalSize; ++i)
    {
        strcpy(tagCopy[i], tagNames[i]);
    }

    // 复制指定 start 到 end 部分的元素到 tagCopy 的末端
    int copyIndex = totalSize;
    for (int i = start; i < end; ++i)
    {
        strcpy(tagCopy[copyIndex], tagNames[i]);
        ++copyIndex;
    }
}

void initializeTagValues(dsfapi::TagValue tTagValue[])
{
    // 定义数据类型到字节数的映射
    std::unordered_map<std::string, int> typeToSize = {
        {"BOOL", 1}, {"BYTE", 1}, {"WORD", 2}, {"DWORD", 4}, {"LWORD", 8}, {"SINT", 1}, {"USINT", 1}, {"INT", 2}, {"UINT", 2}, {"DINT", 4}, {"UDINT", 4}, {"LINT", 8}, {"ULINT", 8}, {"REAL", 4}, {"LREAL", 8}, {"CHAR", 1}, {"TIME", 4}, {"LTIME", 8}};

    // 初始化数组
    uint8_t boolValue = 1;
    uint8_t byteValue = 1;
    uint16_t wordValue = 1;
    uint32_t dwordValue = 1;
    uint64_t lwordValue = 1;
    uint8_t sintValue = 1;
    uint8_t usintValue = 1;
    uint16_t intValue = 1;
    uint16_t uintValue = 1;
    uint32_t dintValue = 1;
    uint32_t udintValue = 1;
    uint64_t lintValue = 1;
    uint64_t ulintValue = 1;
    float realValue = 1.0f;
    double lrealValue = 1.0;
    char charValue = '1';
    uint32_t timeValue = 1;
    uint64_t ltimeValue = 1;

    int index = 0;

    // 1-1000 BOOL
    for (int i = 0; i < 1000; ++i)
    {
        tTagValue[index++].ReSet((const char *)&boolValue, typeToSize["BOOL"]);
    }

    // 1-1000 BYTE
    for (int i = 0; i < 1000; ++i)
    {
        tTagValue[index++].ReSet((const char *)&byteValue, typeToSize["BYTE"]);
    }

    // 16种数据类型，每种5组
    for (int i = 0; i < 5; ++i)
    {
        tTagValue[index++].ReSet((const char *)&wordValue, typeToSize["WORD"]);
        tTagValue[index++].ReSet((const char *)&dwordValue, typeToSize["DWORD"]);
        tTagValue[index++].ReSet((const char *)&lwordValue, typeToSize["LWORD"]);
        tTagValue[index++].ReSet((const char *)&sintValue, typeToSize["SINT"]);
        tTagValue[index++].ReSet((const char *)&usintValue, typeToSize["USINT"]);
        tTagValue[index++].ReSet((const char *)&intValue, typeToSize["INT"]);
        tTagValue[index++].ReSet((const char *)&uintValue, typeToSize["UINT"]);
        tTagValue[index++].ReSet((const char *)&dintValue, typeToSize["DINT"]);
        tTagValue[index++].ReSet((const char *)&udintValue, typeToSize["UDINT"]);
        tTagValue[index++].ReSet((const char *)&lintValue, typeToSize["LINT"]);
        tTagValue[index++].ReSet((const char *)&ulintValue, typeToSize["ULINT"]);
        tTagValue[index++].ReSet((const char *)&realValue, typeToSize["REAL"]);
        tTagValue[index++].ReSet((const char *)&lrealValue, typeToSize["LREAL"]);
        tTagValue[index++].ReSet((const char *)&charValue, typeToSize["CHAR"]);
        tTagValue[index++].ReSet((const char *)&timeValue, typeToSize["TIME"]);
        tTagValue[index++].ReSet((const char *)&ltimeValue, typeToSize["LTIME"]);
    }
}

void Test2080BatchSpeed(dsfapi::DRSdkContext *pContext)
{
    static int NUMbatch1 = 2080;
    static int NUMbatch2 = 2080;
    static int NUMbatch3 = 500;
    const int TOTAL_POINTS = 4160 + 500; // 总点数
    char *tagNames[TOTAL_POINTS];        // 字符指针数组

    for (int i = 0; i < TOTAL_POINTS; ++i)
    {
        tagNames[i] = new char[256]; // 为每个字符串动态分配内存
    }

    // 将字符指针数组传递给函数
    // 生成字符串数据
    generateData(tagNames);

    // 创建三个新的数组来存储分割后的字符串
    char *part1[NUMbatch1];
    char *part2[NUMbatch2];
    char *part3[NUMbatch3];

    // 调用函数分割tagNames到三个部分
    splitTagNames(tagNames, part1, part2, part3);

    dsfapi::TagValue *pTagValue = nullptr;
    int nTagtypeInfoCount = 0;
    int32 *pErrorCode = nullptr;
    int32 nTagValueCount = 0;
    // auto start = std::chrono::high_resolution_clock::now();
    // auto res1 = dsfapi::DR_Read(pContext, (const char **)part1, NUMbatch1, &pTagValue, &pErrorCode, &nTagValueCount);
    // LOG_INFO << "dr_read:res1 = " << res1 << std::endl;
    // auto res2 = dsfapi::DR_Read(pContext, (const char **)part2, NUMbatch2, &pTagValue, &pErrorCode, &nTagValueCount);
    // LOG_INFO << "dr_read:res2 = " << res2 << std::endl;
    // auto res3 = dsfapi::DR_Read(pContext, (const char **)part3, NUMbatch3, &pTagValue, &pErrorCode, &nTagValueCount);
    // LOG_INFO << "dr_read:res3 = " << res3 << std::endl;
    // auto res4 = dsfapi::DR_Read(pContext, (const char **)part3, NUMbatch3, &pTagValue, &pErrorCode, &nTagValueCount);
    // LOG_INFO << "dr_read:res4 = " << res4 << std::endl;
    // auto end = std::chrono::high_resolution_clock::now();
    // auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    // PrintTimeByNs(start);
    // PrintTimeByNs(end);
    // std::cout << duration << std::endl;
    const int NUM_ITERATIONS = 10;
    long durations[NUM_ITERATIONS];

    for (int i = 0; i < NUM_ITERATIONS; ++i)
    {
        auto start = std::chrono::high_resolution_clock::now();
        auto res1 = dsfapi::DR_Read(pContext, (const char **)part1, NUMbatch1, &pTagValue, &pErrorCode, &nTagValueCount);
        LOG_INFO << "dr_read:res1 = " << res1 << std::endl;
        auto res2 = dsfapi::DR_Read(pContext, (const char **)part2, NUMbatch2, &pTagValue, &pErrorCode, &nTagValueCount);
        LOG_INFO << "dr_read:res2 = " << res2 << std::endl;
        auto res3 = dsfapi::DR_Read(pContext, (const char **)part3, NUMbatch3, &pTagValue, &pErrorCode, &nTagValueCount);
        LOG_INFO << "dr_read:res3 = " << res3 << std::endl;
        auto res4 = dsfapi::DR_Read(pContext, (const char **)part3, NUMbatch3, &pTagValue, &pErrorCode, &nTagValueCount);
        LOG_INFO << "dr_read:res4 = " << res4 << std::endl;
        auto end = std::chrono::high_resolution_clock::now();
        durations[i] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    }

    long maxDuration = *std::max_element(durations, durations + NUM_ITERATIONS);
    long minDuration = *std::min_element(durations, durations + NUM_ITERATIONS);
    long sumDuration = std::accumulate(durations, durations + NUM_ITERATIONS, 0L);
    long avgDuration = sumDuration / NUM_ITERATIONS;

    std::cout << "Max duration: " << maxDuration << " microseconds" << std::endl;
    std::cout << "Min duration: " << minDuration << " microseconds" << std::endl;
    std::cout << "Avg duration: " << avgDuration << " microseconds" << std::endl;

    // if (0 == res)
    // {
    //     for (int i = 0; i < nTagValueCount; i++)
    //     {
    //         std::string value(pTagValue[i].Buffer(), pTagValue[i].nLength);
    //         LOG_INFO << "dr_read_data: vecTagName: " << tagNames[i] << ", value: \"" << value << "\"" << std::endl;
    //     }
    // }
    // 函数使用完后，释放内存
    for (int i = 0; i < TOTAL_POINTS; ++i)
    {
        delete[] tagNames[i];
    }

    // Must be released
    dsfapi::Free_Tag_Value(&pTagValue);
}

void Test5160BatchSpeed(dsfapi::DRSdkContext *pContext)
{
    const int TOTAL_POINTS = 4160 + 500 + 500; // 总点数
    char *tagNames[4660];                      // 字符指针数组

    for (int i = 0; i < 4660; ++i)
    {
        tagNames[i] = new char[256]; // 为每个字符串动态分配内存
    }

    // 将字符指针数组传递给函数
    // 生成字符串数据
    generateData(tagNames);
    char *tagCopy[TOTAL_POINTS];
    for (int i = 0; i < TOTAL_POINTS; ++i)
    {
        tagCopy[i] = new char[256]; // 为每个字符串动态分配内存
    }
    copyTagNames(tagNames, tagCopy, 4160, 4660, 4660);
    dsfapi::TagValue *pTagValue = nullptr;
    int nTagtypeInfoCount = 0;
    int32 *pErrorCode = nullptr;
    int32 nTagValueCount = 0;
    const int NUM_ITERATIONS = 10;
    long durations[NUM_ITERATIONS];
    for (int i = 0; i < 1000; i++)
    {
        std::cout << tagCopy[i + 4160] << std::endl;
    }
    for (int i = 0; i < NUM_ITERATIONS; ++i)
    {
        auto start = std::chrono::high_resolution_clock::now();
        auto res1 = dsfapi::DR_Read(pContext, (const char **)tagCopy, TOTAL_POINTS, &pTagValue, &pErrorCode, &nTagValueCount);
        LOG_INFO << "dr_read:res1 = " << res1 << std::endl;
        auto end = std::chrono::high_resolution_clock::now();
        durations[i] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    }

    long maxDuration = *std::max_element(durations, durations + NUM_ITERATIONS);
    long minDuration = *std::min_element(durations, durations + NUM_ITERATIONS);
    long sumDuration = std::accumulate(durations, durations + NUM_ITERATIONS, 0L);
    long avgDuration = sumDuration / NUM_ITERATIONS;

    std::cout << "Max duration: " << maxDuration << " microseconds" << std::endl;
    std::cout << "Min duration: " << minDuration << " microseconds" << std::endl;
    std::cout << "Avg duration: " << avgDuration << " microseconds" << std::endl;

    // 函数使用完后，释放内存
    for (int i = 0; i < 4660; ++i)
    {
        delete[] tagNames[i];
    }
    // 函数使用完后，释放内存
    for (int i = 0; i < TOTAL_POINTS; ++i)
    {
        delete[] tagCopy[i];
    }

    // Must be released
    dsfapi::Free_Tag_Value(&pTagValue);
}

void TestOneByOneSpeed(dsfapi::DRSdkContext *pContext)
{
    const int TOTAL_POINTS = 4160 * 2;     // 总点数
    char *tagNames[] = {"STD.TEST_BOOL1"}; // 字符指针数组

    dsfapi::TagValue *pTagValue = nullptr;
    int nTagtypeInfoCount = 0;
    int32 *pErrorCode = nullptr;
    int32 nTagValueCount = 0;
    const int NUM_ITERATIONS = 10;
    long durations[NUM_ITERATIONS];

    for (int i = 0; i < NUM_ITERATIONS; ++i)
    {
        auto start = std::chrono::high_resolution_clock::now();
        for (int j = 0; j < TOTAL_POINTS; j++)
        {
            auto res1 = dsfapi::DR_Read(pContext, (const char **)tagNames, 1, &pTagValue, &pErrorCode, &nTagValueCount);
        }
        // LOG_INFO << "dr_read:res1 = " << res1 << std::endl;
        auto end = std::chrono::high_resolution_clock::now();
        durations[i] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    }

    long maxDuration = *std::max_element(durations, durations + NUM_ITERATIONS);
    long minDuration = *std::min_element(durations, durations + NUM_ITERATIONS);
    long sumDuration = std::accumulate(durations, durations + NUM_ITERATIONS, 0L);
    long avgDuration = sumDuration / NUM_ITERATIONS;

    std::cout << "Max duration: " << maxDuration << " microseconds" << std::endl;
    std::cout << "Min duration: " << minDuration << " microseconds" << std::endl;
    std::cout << "Avg duration: " << avgDuration << " microseconds" << std::endl;

    // Must be released
    dsfapi::Free_Tag_Value(&pTagValue);
}

void TestOneBatchSpeed(dsfapi::DRSdkContext *pContext)
{
    char *tagNames1[] = {"STD.TEST_BOOL1"};
    char *tagNames2[] = {"STD.TEST_UDT1"};

    dsfapi::TagValue *pTagValue = nullptr;
    int nTagtypeInfoCount = 0;
    int32 *pErrorCode = nullptr;
    int32 nTagValueCount = 0;
    const int NUM_ITERATIONS = 10;
    long durations[NUM_ITERATIONS];

    for (int i = 0; i < NUM_ITERATIONS; ++i)
    {
        auto start = std::chrono::high_resolution_clock::now();
        for (int j = 0; j < 4160; j++)
        {
            auto res1 = dsfapi::DR_Read(pContext, (const char **)tagNames1, 1, &pTagValue, &pErrorCode, &nTagValueCount);
        }
        for (int k = 0; k < 1000; k++)
        {
            auto res2 = dsfapi::DR_Read(pContext, (const char **)tagNames2, 1, &pTagValue, &pErrorCode, &nTagValueCount);
        }
        // LOG_INFO << "dr_read:res1 = " << res1 << std::endl;
        auto end = std::chrono::high_resolution_clock::now();
        durations[i] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    }

    long maxDuration = *std::max_element(durations, durations + NUM_ITERATIONS);
    long minDuration = *std::min_element(durations, durations + NUM_ITERATIONS);
    long sumDuration = std::accumulate(durations, durations + NUM_ITERATIONS, 0L);
    long avgDuration = sumDuration / NUM_ITERATIONS;

    std::cout << "Max duration: " << maxDuration << " microseconds" << std::endl;
    std::cout << "Min duration: " << minDuration << " microseconds" << std::endl;
    std::cout << "Avg duration: " << avgDuration << " microseconds" << std::endl;

    // Must be released
    dsfapi::Free_Tag_Value(&pTagValue);
}

void Test1wSingleSpeed(dsfapi::DRSdkContext *pContext)
{
    static int NUMbatch1 = 2080;
    static int NUMbatch2 = 2080;
    static int NUMbatch3 = 500;
    const int TOTAL_POINTS = 4160 + 500; // 总点数
    char *tagNames[TOTAL_POINTS];        // 字符指针数组

    for (int i = 0; i < TOTAL_POINTS; ++i)
    {
        tagNames[i] = new char[256]; // 为每个字符串动态分配内存
    }

    // 将字符指针数组传递给函数
    // 生成字符串数据
    generateData(tagNames);

    // 创建三个新的数组来存储分割后的字符串
    char *part1[NUMbatch1];
    char *part2[NUMbatch2];
    char *part3[NUMbatch3];

    // 调用函数分割tagNames到三个部分
    splitTagNames(tagNames, part1, part2, part3);

    dsfapi::TagValue *pTagValue = nullptr;
    int nTagtypeInfoCount = 0;
    int32 *pErrorCode = nullptr;
    int32 nTagValueCount = 0;
    const int NUM_ITERATIONS = 10;
    long durations[NUM_ITERATIONS];

    for (int i = 0; i < NUM_ITERATIONS; ++i)
    {
        auto start = std::chrono::high_resolution_clock::now();
        auto res1 = dsfapi::DR_Read(pContext, (const char **)part1, NUMbatch1, &pTagValue, &pErrorCode, &nTagValueCount);
        LOG_INFO << "dr_read:res1 = " << res1 << std::endl;
        auto res2 = dsfapi::DR_Read(pContext, (const char **)part2, NUMbatch2, &pTagValue, &pErrorCode, &nTagValueCount);
        LOG_INFO << "dr_read:res2 = " << res2 << std::endl;
        auto res3 = dsfapi::DR_Read(pContext, (const char **)part1, NUMbatch1, &pTagValue, &pErrorCode, &nTagValueCount);
        LOG_INFO << "dr_read:res3 = " << res3 << std::endl;
        auto res4 = dsfapi::DR_Read(pContext, (const char **)part2, NUMbatch2, &pTagValue, &pErrorCode, &nTagValueCount);
        LOG_INFO << "dr_read:res4 = " << res4 << std::endl;
        auto end = std::chrono::high_resolution_clock::now();
        durations[i] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    }

    long maxDuration = *std::max_element(durations, durations + NUM_ITERATIONS);
    long minDuration = *std::min_element(durations, durations + NUM_ITERATIONS);
    long sumDuration = std::accumulate(durations, durations + NUM_ITERATIONS, 0L);
    long avgDuration = sumDuration / NUM_ITERATIONS;

    std::cout << "Max duration: " << maxDuration << " microseconds" << std::endl;
    std::cout << "Min duration: " << minDuration << " microseconds" << std::endl;
    std::cout << "Avg duration: " << avgDuration << " microseconds" << std::endl;

    // if (0 == res)
    // {
    //     for (int i = 0; i < nTagValueCount; i++)
    //     {
    //         std::string value(pTagValue[i].Buffer(), pTagValue[i].nLength);
    //         LOG_INFO << "dr_read_data: vecTagName: " << tagNames[i] << ", value: \"" << value << "\"" << std::endl;
    //     }
    // }
    // 函数使用完后，释放内存
    for (int i = 0; i < TOTAL_POINTS; ++i)
    {
        delete[] tagNames[i];
    }

    // Must be released
    dsfapi::Free_Tag_Value(&pTagValue);
}

void TestWrite1wSingle(dsfapi::DRSdkContext *pContext)
{
    static int NUMbatch1 = 2080;
    static int NUMbatch2 = 2080;
    static int NUMbatch3 = 500;
    const int TOTAL_POINTS = 4160 + 500; // 总点数
    char *tagNames[TOTAL_POINTS];        // 字符指针数组

    for (int i = 0; i < TOTAL_POINTS; ++i)
    {
        tagNames[i] = new char[256]; // 为每个字符串动态分配内存
    }

    // 将字符指针数组传递给函数
    // 生成字符串数据
    generateData(tagNames);

    // 创建三个新的数组来存储分割后的字符串
    char *part1[NUMbatch1];
    char *part2[NUMbatch2];
    char *part3[NUMbatch3];

    splitTagNames(tagNames, part1, part2, part3);
    dsfapi::TagValue tTagValue[NUMbatch1];

    initializeTagValues(tTagValue);

    const int NUM_ITERATIONS = 10;
    long durations[NUM_ITERATIONS];

    for (int i = 0; i < NUM_ITERATIONS; ++i)
    {
        auto start = std::chrono::high_resolution_clock::now();
        for (int i = 0; i < 4; i++)
        {
            auto res = dsfapi::DR_Write(pContext, (const char **)part1, tTagValue, NUMbatch1);
            LOG_INFO << "DR_Write:res = " << res << std::endl;
        }
        auto end = std::chrono::high_resolution_clock::now();
        durations[i] = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
    }

    for (int i = 0; i < TOTAL_POINTS; ++i)
    {
        delete[] tagNames[i];
    }

    long maxDuration = *std::max_element(durations, durations + NUM_ITERATIONS);
    long minDuration = *std::min_element(durations, durations + NUM_ITERATIONS);
    long sumDuration = std::accumulate(durations, durations + NUM_ITERATIONS, 0L);
    long avgDuration = sumDuration / NUM_ITERATIONS;

    std::cout << "Max duration: " << maxDuration << " microseconds" << std::endl;
    std::cout << "Min duration: " << minDuration << " microseconds" << std::endl;
    std::cout << "Avg duration: " << avgDuration << " microseconds" << std::endl;
}

void TestRegister1wTag(dsfapi::DRSdkContext *pContext)
{

    const int TOTAL_POINTS = 4660*2; // 总点数
    char *tagNames[4660];                      // 字符指针数组

    for (int i = 0; i < 4660; ++i)
    {
        tagNames[i] = new char[256]; // 为每个字符串动态分配内存
    }

    // 将字符指针数组传递给函数
    // 生成字符串数据
    generateData(tagNames);
    char *tagCopy[TOTAL_POINTS];
    for (int i = 0; i < TOTAL_POINTS; ++i)
    {
        tagCopy[i] = new char[256]; // 为每个字符串动态分配内存
    }
    copyTagNames(tagNames, tagCopy, 0, 4660, 4660);
    
    auto func1 = [](const int32 nBatchId, dsfapi::TagRecord *pTagRecord, int32 *pErrorCode, const int32 nTagCount)
    {
        LOG_INFO << "fun_1. count=" << nTagCount << ", batch_id=" << nBatchId << std::endl;
        // for (int i = 0; i < nTagCount; i++)
        // {
        //     LOG_INFO << "fun_1. errorCode:" << pErrorCode[i] << ", tagname:" << pTagRecord[i].tTagName.Buffer()
        //              << ",tagvalue:" << pTagRecord[i].tTagValue.Buffer() << std::endl;
        // }
        // Must be released
        dsfapi::Free_Int32_Ptr(&pErrorCode);
        dsfapi::Free_Tag_Record(&pTagRecord);
    };

    int32 res = 0;
    int32 nBatchId1 = 0;
    res = dsfapi::DR_Register_Tag(pContext, (const char **)tagCopy, TOTAL_POINTS, 100, func1, 0, &nBatchId1);
    LOG_INFO << "dr_async_reg_tag 4660 :res = " << res << ", batch_id=" << nBatchId1 << std::endl;
    for (int i = 0; i < 4660; ++i)
    {
        delete[] tagNames[i];
    }
    for(int i = 0;i < TOTAL_POINTS;++i){
        delete[] tagCopy[i];
    }

}

int main()
{
    dsfapi::DRSdkContext *pContext = nullptr;
    pContext = TestDrsdkInit();
    if (nullptr == pContext)
    {
        return -1;
    }
    // TestReadInt32FromRedis(pContext);
    // TestReadInt16FromRedis(pContext);
    // TestReadBoolFromRedis(pContext);
    // TestReadCharFromRedis(pContext);
    // TestReadStringFromRedis(pContext);
    // while (true)
    // { // loop test
    //     static int cnt1 = 0;
    //     static int cnt2 = 0;
    //     if (++cnt1 % 2 == 0)
    //     {
    //         cnt1 = 0;
    //         TestRead(pContext);
    //         // TestControlData();
    //         // TestSaveData();
    //     }

    //     if (++cnt2 % 10 == 0)
    //     {
    //         cnt2 = 0;
    //         // TestReadObjectAttr();
    //         // TestReadObjectAttrData();
    //     }
    //     std::this_thread::sleep_for(std::chrono::milliseconds(100));
    // }
    // Test2080BatchSpeed(pContext);
    // Test5160BatchSpeed(pContext);
    // TestOneByOneSpeed(pContext);
    // TestOneBatchSpeed(pContext);
    // Test1wSingleSpeed(pContext);
     TestWrite1wSingle(pContext);

    //TestRegister1wTag(pContext);

    std::this_thread::sleep_for(std::chrono::seconds(20));

    // 3. should uninit
    dsfapi::DR_Uninit(pContext);
    // 4. should release
    dsfapi::Free_DR_Sdk_Context(&pContext);

    return 0;
}
