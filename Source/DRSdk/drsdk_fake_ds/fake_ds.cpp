/**
 * Filename        fake_ds.cpp
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description     simulate DataService
 *
 * Author          wuz<PERSON><PERSON><PERSON>
 * Version         09/20/2024	wuzheqiang	Initial Version
 **************************************************************/

#include "drsdk_common.h" // just for include some headers
#include <boost/asio.hpp>
#include <boost/bind/bind.hpp>
#include <chrono>
#include <iostream>
#include <memory>
#include <mutex>
#include <nlohmann/json.hpp>
#include <set>
#include <thread>
#include <unordered_map>
#include <vector>

using boost::asio::ip::tcp;
using dsfapi::DataHeader;

using json = nlohmann::json;
struct RegInfo
{
    std::vector<dsfapi::TagValue> vecTagName;
    uint16 nInterval;
    uint8 nRegType;
};
using MapRegInfo = std::unordered_map<int32, RegInfo>;

constexpr int32 TIMER_INTERVAL = 10;
int32 HEARTBEAT_TIMER_INTERVAL = 30;
class ClientSession : public std::enable_shared_from_this<ClientSession>
{
  public:
    ClientSession(boost::asio::io_service &io_service)
        : socket_(io_service), timer_(io_service), heartbeat_timer_(io_service) // 保持 io_service 参数
    {
    }

    tcp::socket &socket()
    {
        return socket_;
    }

    void Start()
    {
        ReadMessage();
        StartPushTimer();
        StartPushHeartBeat();
    }

    void SendMessage(char *sBuf, const size_t nLen)
    {
        auto self(shared_from_this());
        boost::asio::async_write(
            socket_, boost::asio::buffer(sBuf, nLen),
            [this, self](const boost::system::error_code &error, std::size_t /*bytes_transferred*/) {
                if (error)
                {
                    LOG_ERR << "Failed to send message: " << error.message() << std::endl;
                    // Handle disconnection here
                }
            });
    }

  private:
    void StartPushHeartBeat()
    {
        static uint32 times = 0;
        static int32 nSeq = 0;
        rm_status_ = 1;
        // rm_status_ = (times++ / HEARTBEAT_TIMER_INTERVAL) % 2;
        json jHeartBeat;
        jHeartBeat["rm_status"] = rm_status_;
        jHeartBeat["timestamp_ms"] = dsfapi::NowMillisecond();
        std::string strContent = jHeartBeat.dump();

        DataHeader tHead;
        tHead.nType = dsfapi::SDK_HEARTBEAT;
        tHead.nLen = strContent.size();
        tHead.nSeq = ++nSeq;

        char sBuf[1024] = {0};
        int32 nBufLen = sizeof(sBuf);
        memcpy(sBuf, &tHead, sizeof(tHead));
        memcpy(sBuf + sizeof(tHead), strContent.c_str(), tHead.nLen);
        nBufLen = sizeof(DataHeader) + tHead.nLen;
        LOG_INFO << "send heartbeat:  strContent = " << strContent << ", nBufLen = " << nBufLen << std::endl;
        SendMessage(sBuf, nBufLen);

        heartbeat_timer_.expires_from_now(boost::posix_time::milliseconds(1000)); // 使用 posix_time
        heartbeat_timer_.async_wait([this](const boost::system::error_code &error) {
            if (!error)
            {
                StartPushHeartBeat(); // Restart timer
            }
        });
    }

    void StartPushTimer()
    {
        timer_.expires_from_now(boost::posix_time::milliseconds(TIMER_INTERVAL)); // 使用 posix_time
        timer_.async_wait([this](const boost::system::error_code &error) {
            static uint32 times = 0;
            if (!error)
            {
                ++times;

                std::lock_guard<std::mutex> lock(batch_data_mutex_);
                for (auto &item : batch_data_)
                {
                    auto &batch_id = item.first;
                    auto &reg_info = item.second;
                    auto &interval = reg_info.nInterval;
                    if ((times * TIMER_INTERVAL) % interval < TIMER_INTERVAL)
                    {
                        if (0 == rm_status_)
                        {
                            LOG_DEBUG << "rm_status_ is 0, ignore it" << std::endl;
                            break;
                        }
                        char sBuf[dsfapi::RECV_BUFFER_MAX_LENGTH] = {0};
                        int32 nBufLen = sizeof(sBuf);
                        static int32 nSeq = 0;
                        std::string strContent = "";
                        int error_code = 0;
                        if (dsfapi::SDK_REG_TAG == item.second.nRegType)
                        {
                            for (auto &tTagValue : reg_info.vecTagName)
                            {
                                std::string strTagName(tTagValue.Buffer(), tTagValue.Length());
                                // construct msg content
                                // ++error_code;
                                std::string strTemp = "";
                                char szTemp[256] = {0};
                                if (std::string::npos != strTagName.find("INT"))
                                {
                                    nTemp_++;
                                    memcpy(szTemp, &nTemp_, sizeof(nTemp_));
                                    strTemp.assign(szTemp, sizeof(nTemp_));
                                }
                                else if (std::string::npos != strTagName.find("REAL"))
                                {

                                    fTemp_ += 1.001;
                                    memcpy(szTemp, &fTemp_, sizeof(fTemp_));
                                    strTemp.assign(szTemp, sizeof(fTemp_));
                                }
                                else if (std::string::npos != strTagName.find("STRING"))
                                {
                                    strTemp = sTemp_;
                                }
                                else
                                {
                                    strTemp = dsfapi::CurrentTimestampMicro();
                                }

                                dsfapi::PackItem(&strContent, strTagName.c_str(), strTagName.size());
                                dsfapi::PackItem(&strContent, strTemp.c_str(), strTemp.size());
                                dsfapi::PackItem(&strContent, reinterpret_cast<char *>(&error_code),
                                                 sizeof(error_code));
                            }
                            DataHeader tHead;
                            tHead.nType = dsfapi::SDK_REG_TAG_DATA;
                            tHead.nLen = strContent.size();
                            tHead.nSeq = ++nSeq;
                            tHead.nReserve1 = batch_id;
                            memcpy(sBuf, &tHead, sizeof(tHead));
                            memcpy(sBuf + sizeof(tHead), strContent.c_str(), tHead.nLen);
                            nBufLen = sizeof(DataHeader) + tHead.nLen;
                            LOG_INFO << "send data.batch_id:" << batch_id << ", strContent = " << strContent
                                     << ", nBufLen = " << nBufLen << std::endl;
                            SendMessage(sBuf, nBufLen);
                        }
                        else if (dsfapi::SDK_REG_OBJ_JSON == item.second.nRegType)
                        {
                            json jResult = nlohmann::json::array();
                            for (auto &tTagValue : reg_info.vecTagName)
                            {
                                std::string strTagName(tTagValue.Buffer(), tTagValue.Length());
                                json jModel;
                                jModel["obj_name"] = strTagName;
                                jModel["error_code"] = 0;
                                jModel["value"] = {"10", "200", "3.14", "abcd"};
                                jResult.push_back(jModel);
                            }

                            //  serialize to string
                            std::string strContent = jResult.dump();
                            DataHeader tHead;
                            tHead.nType = dsfapi::SDK_REG_OBJ_JSON_DATA;
                            tHead.nLen = strContent.size();
                            tHead.nSeq = ++nSeq;
                            tHead.nReserve1 = batch_id;
                            memcpy(sBuf, &tHead, sizeof(tHead));
                            memcpy(sBuf + sizeof(tHead), strContent.c_str(), tHead.nLen);
                            nBufLen = sizeof(DataHeader) + tHead.nLen;
                            LOG_INFO << "send data.batch_id:" << batch_id << ", strContent = " << strContent
                                     << ", nBufLen = " << nBufLen << std::endl;
                            SendMessage(sBuf, nBufLen);
                        }
                        else
                        {
                            LOG_ERR << "reg_type error" << (int32)(item.second.nRegType) << std::endl;
                        }
                    }
                }

                StartPushTimer(); // Restart timer
            }
        });
    }

    void ReadMessage()
    {
        auto self(shared_from_this());
        memset(data_, 0, dsfapi::BUFFER_MAX_LENGTH);
        socket_.async_read_some(boost::asio::buffer(data_, dsfapi::BUFFER_MAX_LENGTH),
                                [this, self](const boost::system::error_code &error, std::size_t bytes_transferred) {
                                    if (!error)
                                    {
                                        LOG_INFO << "recv data. bytes_transferred = " << bytes_transferred << std::endl;
                                        DealRequest(data_, bytes_transferred);
                                        // Continue reading
                                        ReadMessage();
                                    }
                                    else
                                    {
                                        socket_.close();
                                        // LOG_ERR << "Failed to read message: " << error.message() << std::endl;
                                        LOG_ERR << "Failed to read message: " << "error.message() not useable";

                                        // Handle disconnection here
                                    }
                                });
    }

    void DealRequest(char *sBuf, const size_t nLen)
    {
        int32 nRet = 0;
        int32 nBufLen = nLen;
        DataHeader *pHead = (DataHeader *)sBuf;
        LOG_INFO << pHead->ToString() << std::endl;
        const char *pContent = sBuf + sizeof(DataHeader);
        std::vector<dsfapi::TagValue> vecContent;
        if (pHead->nLen > 0)
        {
            auto bRes = dsfapi::UnPackBuffer(pContent, pHead->nLen, &vecContent);
            if (!bRes || 0 == vecContent.size())
            {
                LOG_ERR << "UnPackBuffer failed, bRes=" << bRes << " vecContent.size()=" << vecContent.size()
                        << std::endl;
                return;
            }
        }
        switch (pHead->nType)
        {
        case dsfapi::SDK_READ_DATA: {
            LOG_INFO << "SDK_READ_DATA readed data: " << std::string(pContent, pHead->nLen) << std::endl;
            // query the value
            std::string strValue = "";
            int32 error_code = 0;
            for (int i = 0; i < vecContent.size(); i++)
            {
                std::string strTagName(vecContent[i].Buffer(), vecContent[i].Length());
                std::string strTemp = "";
                char szTemp[256] = {0};
                if (std::string::npos != strTagName.find("INT"))
                {
                    nTemp_++;
                    memcpy(szTemp, &nTemp_, sizeof(nTemp_));
                    strTemp.assign(szTemp, sizeof(nTemp_));
                }
                else if (std::string::npos != strTagName.find("REAL"))
                {

                    fTemp_ += 1.001;
                    memcpy(szTemp, &fTemp_, sizeof(fTemp_));
                    strTemp.assign(szTemp, sizeof(fTemp_));
                }
                else
                {
                    strTemp = dsfapi::CurrentTimestampMicro();
                }
                dsfapi::PackItem(&strValue, strTemp.c_str(), strTemp.size());
                dsfapi::PackItem(&strValue, reinterpret_cast<char *>(&error_code), sizeof(error_code));
            }

            memcpy(sBuf + sizeof(DataHeader), strValue.c_str(), strValue.size());
            pHead->nLen = strValue.size();
            pHead->nFlag = 0; // 0: success, 1: failed
            nBufLen = sizeof(DataHeader) + pHead->nLen;
            LOG_INFO << "SDK_READ_DATA send length:" << pHead->nLen << ", data:" << strValue << std::endl;
            SendMessage(sBuf, nBufLen);

            break;
        }
        case dsfapi::SDK_CONTROL_DATA:
        case dsfapi::SDK_WRITE_TEXT_DATA:
        case dsfapi::SDK_DUMP_DATA: {
            for (int i = 0; i < vecContent.size(); i += 2)
            {
                LOG_INFO << "SDK_CONTROL_DATA/SDK_DUMP_DATA tagname:" << vecContent[i].Buffer() << " value:";
                std::ostringstream oss;
                for (size_t pos = 0; pos < vecContent[i + 1].Length(); pos++)
                {
                    oss << std::hex << std::setw(2) << std::setfill('0')
                        << static_cast<int>(static_cast<unsigned char>(vecContent[i + 1].Buffer()[pos]));
                }
                LOG_INFO << oss.str();

                if (nullptr != strstr(vecContent[i].Buffer(), "INT"))
                {
                    memcpy(&nTemp_, vecContent[i + 1].Buffer(), sizeof(nTemp_));
                }
                else if (nullptr != strstr(vecContent[i].Buffer(), "REAL"))
                {
                    memcpy(&fTemp_, vecContent[i + 1].Buffer(), sizeof(fTemp_));
                    LOG_INFO << "REAL" << fTemp_;
                }
                else if (nullptr != strstr(vecContent[i].Buffer(), "STRING"))
                {
                    sTemp_ = std::string(vecContent[i + 1].Buffer(), vecContent[i + 1].Length());
                    LOG_INFO << "StRING" << sTemp_;
                }
            }
            // send the value
            pHead->nLen = 0;  // have not content data
            pHead->nFlag = 0; // 0: success, 1: failed
            nBufLen = sizeof(DataHeader);
            SendMessage(sBuf, nBufLen);

            break;
        }
        case dsfapi::SDK_REG_TAG:
        case dsfapi::SDK_REG_OBJ_JSON: {
            uint16 &nBatchId = pHead->nReserve1;
            uint16 &nInterval = pHead->nReserve2;

            LOG_INFO << "SDK_REG_TAG/SDK_REG_OBJ_JSON batch_id:" << nBatchId << ", interval:" << nInterval
                     << ", tag size:" << vecContent.size() << ",pHead->nType" << (uint32)(pHead->nType) << std::endl;
            RegInfo tRegInfo{std::move(vecContent), nInterval, pHead->nType};
            {
                std::lock_guard<std::mutex> lock(batch_data_mutex_);
                batch_data_.emplace(nBatchId, std::move(tRegInfo));
            }

            // send the value
            pHead->nLen = 0;  // have not content data
            pHead->nFlag = 0; // 0: success, 1: failed
            nBufLen = sizeof(DataHeader);
            SendMessage(sBuf, nBufLen);
            break;
        }
        case dsfapi::SDK_UNREG_TAG: {
            uint16 &nBatchId = pHead->nReserve1;
            LOG_INFO << "SDK_UNREG_TAG batch_id:" << nBatchId << std::endl;
            RegInfo tRegInfo{std::move(vecContent), nBatchId};
            {
                std::lock_guard<std::mutex> lock(batch_data_mutex_);
                batch_data_.erase(nBatchId);
            }

            // send the value
            pHead->nLen = 0;  // have not content data
            pHead->nFlag = 0; // 0: success, 1: failed
            nBufLen = sizeof(DataHeader);
            SendMessage(sBuf, nBufLen);
            break;
        }
        case dsfapi::SDK_READ_ATTR: {
            std::string strObjectName(vecContent[0].Buffer(), vecContent[0].Length());
            LOG_INFO << "SDK_READ_ATTR readed data: " << strObjectName << std::endl;
            // query the value
            std::string strValue = "";
            dsfapi::TagtypeInfo objAttr[10];
            for (int i = 0; i < 10; i++)
            {
                std::string strName = strObjectName + std::string(".tag") + std::to_string(i);
                strncpy(objAttr[i].szName, strName.c_str(), strName.size());
                strncpy(objAttr[i].szAliasName, strName.c_str(), strName.size());
                objAttr[i].nType = dsfapi::SDKAttrType::SDK_ATTR_TYPE_INT8;
                objAttr[i].nLen = 10 + i;
            }

            memcpy(sBuf + sizeof(DataHeader), objAttr, sizeof(dsfapi::TagtypeInfo) * 10);
            pHead->nLen = sizeof(dsfapi::TagtypeInfo) * 10;
            pHead->nFlag = 0; // 0: success, 1: failed
            nBufLen = sizeof(DataHeader) + pHead->nLen;
            LOG_INFO << "SDK_READ_DATA send length:" << pHead->nLen << ", data:" << strValue << std::endl;
            SendMessage(sBuf, nBufLen);

            break;
        }
        case dsfapi::SDK_READ_ATTR_DATA: {
            std::string strObjectName(vecContent[0].Buffer(), vecContent[0].Length());
            LOG_INFO << "SDK_READ_ATTR_DATA readed data: " << strObjectName << std::endl;
            // query the value
            std::string strValue = "";
            for (int i = 0; i < 10; i++)
            {
                std::string strTempName = strObjectName + std::string(".tag") + std::to_string(i);
                std::string strTempValue = dsfapi::CurrentTimestampMicro();
                dsfapi::PackItem(&strValue, strTempName.c_str(), strTempName.size());
                dsfapi::PackItem(&strValue, strTempValue.c_str(), strTempValue.size());
            }

            memcpy(sBuf + sizeof(DataHeader), strValue.c_str(), strValue.size());
            pHead->nLen = strValue.size();
            pHead->nFlag = 0; // 0: success, 1: failed
            nBufLen = sizeof(DataHeader) + pHead->nLen;
            LOG_INFO << "SDK_READ_ATTR_DATA send length:" << pHead->nLen << ", data:" << strValue << std::endl;
            SendMessage(sBuf, nBufLen);

            break;
        }
        case dsfapi::SDK_READ_MODEL_JSON: {
            std::string strObjectName(vecContent[0].Buffer(), vecContent[0].Length());
            LOG_INFO << "SDK_READ_MODEL_JSON readed data: " << strObjectName << std::endl;

            // query the value
            json jModel;
            jModel["MemberName"] = "UDTVAR1";
            jModel["MemberType"] = "DINT";
            jModel["Offset"] = 0;
            jModel["Length"] = 4;
            //  serialize to string
            std::string strValue = jModel.dump();

            memcpy(sBuf + sizeof(DataHeader), strValue.c_str(), strValue.size());
            pHead->nLen = strValue.size();
            pHead->nFlag = 0; // 0: success, 1: failed
            nBufLen = sizeof(DataHeader) + pHead->nLen;
            LOG_INFO << "SDK_READ_MODEL_JSON send length:" << pHead->nLen << ", data:" << strValue << std::endl;
            SendMessage(sBuf, nBufLen);

            break;
        }
        case dsfapi::SDK_HEARTBEAT: {
            LOG_INFO << "received HEARTBEAT";
            break;
        }
        default:
            LOG_INFO << "Not supported pHead->nType: " << static_cast<u_int32_t>(pHead->nType) << std::endl;
            break;
        }
    }

    tcp::socket socket_;
    char data_[dsfapi::BUFFER_MAX_LENGTH]; // Buffer for async read operations
    std::map<uint16, RegInfo> batch_data_;
    std::mutex batch_data_mutex_;
    boost::asio::deadline_timer timer_;
    boost::asio::deadline_timer heartbeat_timer_;
    int rm_status_ = 0; // 0: not registered, 1: registered

    float fTemp_ = 100.0f;
    int16 nTemp_ = 100;
    std::string sTemp_ = "abcd";
};

class DataService
{
  public:
    DataService(boost::asio::io_service &io_service, const tcp::endpoint &endpoint)
        : acceptor_(io_service, endpoint), io_service_(io_service)
    {
        StartAccept();
    }

    void StartAccept()
    {
        auto new_session = std::make_shared<ClientSession>(io_service_);
        acceptor_.async_accept(new_session->socket(), [this, new_session](const boost::system::error_code &error) {
            if (!error)
            {
                LOG_INFO << "New client connected" << std::endl;
                new_session->Start();
            }
            StartAccept(); // Accept next connection
        });
    }

  private:
    tcp::acceptor acceptor_;
    boost::asio::io_service &io_service_;
};

bool g_bStop = false;
void signalHandler(int signal)
{
    if (signal == SIGINT)
    {
        std::cout << "\nCaught Ctrl+C (SIGINT). Exiting loop..." << std::endl;
        g_bStop = true;
    }
}

int main(int args, char *argv[])
{
    uint16 nPort = 1234;

    if (args > 1)
    {
        nPort = std::stoi(argv[1]);
    }
    if (args > 2)
    {
        HEARTBEAT_TIMER_INTERVAL = std::stoi(argv[2]);
    }

    std::signal(SIGINT, signalHandler);
    try
    {
        boost::asio::io_service io_service;

        tcp::endpoint endpoint(tcp::v4(), nPort);
        DataService server(io_service, endpoint);

        // Run the io_service to process asynchronous events
        std::thread io_thread([&io_service]() { io_service.run(); });

        while (1)
        {
            if (g_bStop)
            {
                io_service.stop();
                break;
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        }
        io_thread.join();
    }
    catch (std::exception &e)
    {
        LOG_ERR << "Exception: " << e.what() << std::endl;
    }

    return 0;
}
