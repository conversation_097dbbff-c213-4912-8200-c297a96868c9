/**
 * Filename        fake_ds.cpp
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description     simulate DataService
 *
 * Author          wuz<PERSON>qiang
 * Version         09/20/2024	wuzheqiang	Initial Version
 **************************************************************/

#include "drsdk_common.h"
#include <boost/asio.hpp>
#include <boost/bind/bind.hpp>
#include <chrono>
#include <iostream>
#include <memory>
#include <nlohmann/json.hpp>
#include <set>
#include <thread>
#include <unordered_map>
#include <vector>

using boost::asio::ip::tcp;
using dsfapi::DataHeader;

using json = nlohmann::json;
struct RegInfo
{
    std::vector<dsfapi::TagValue> vecTagName;
    uint16 nInterval;
};
using MapRegInfo = std::unordered_map<int32, RegInfo>;

constexpr int32 TIMER_INTERVAL = 10;
class ClientSession : public std::enable_shared_from_this<ClientSession>
{
  public:
    ClientSession(boost::asio::io_context &io_context) : socket_(io_context), timer_(io_context)
    {
    }

    tcp::socket &socket()
    {
        return socket_;
    }

    void Start()
    {
        ReadMessage();
        StartPushTimer();
    }

    void SendMessage(char *sBuf, const size_t nLen)
    {
        auto self(shared_from_this());
        boost::asio::async_write(
            socket_, boost::asio::buffer(sBuf, nLen),
            [this, self](const boost::system::error_code &error, std::size_t /*bytes_transferred*/) {
                if (error)
                {
                    LOG_ERR << "Failed to send message: " << error.message() << std::endl;
                    // Handle disconnection here
                }
            });
    }

  private:
    void StartPushTimer()
    {
        timer_.expires_after(std::chrono::milliseconds(TIMER_INTERVAL)); // Set push interval
        timer_.async_wait([this](const boost::system::error_code &error) {
            static uint32 times = 0;
            if (!error)
            {
                ++times;
                for (auto &item : batch_data_)
                {
                    auto &batch_id = item.first;
                    auto &reg_info = item.second;
                    auto &interval = reg_info.nInterval;
                    if ((times * TIMER_INTERVAL) % interval < TIMER_INTERVAL)
                    {
                        char sBuf[1024] = {0};
                        int32 nBufLen = sizeof(sBuf);
                        static int32 nSeq = 0;
                        std::string strContent = "";
                        int error_code = 0;
                        for (auto &tTagValue : reg_info.vecTagName)
                        {
                            std::string strTagName(tTagValue.Buffer(), tTagValue.Length());
                            // construct msg content
                            // ++error_code;
                            std::string strTemp = "";
                            char szTemp[256] = {0};
                            if (std::string::npos != strTagName.find("int32"))
                            {
                                static int32 nTemp = 100;
                                nTemp++;
                                memcpy(szTemp, &nTemp, sizeof(nTemp));
                                strTemp.assign(szTemp, sizeof(nTemp));
                            }
                            else if (std::string::npos != strTagName.find("double"))
                            {
                                static double dTemp = 100.0;
                                dTemp += 1.001;
                                memcpy(szTemp, &dTemp, sizeof(dTemp));
                                strTemp.assign(szTemp, sizeof(dTemp));
                            }
                            else
                            {
                                strTemp = dsfapi::CurrentTimestampMicro();
                            }

                            dsfapi::PackItem(&strContent, strTagName.c_str(), strTagName.size());
                            dsfapi::PackItem(&strContent, strTemp.c_str(), strTemp.size());
                            dsfapi::PackItem(&strContent, reinterpret_cast<char *>(&error_code), sizeof(error_code));
                        }
                        DataHeader tHead;
                        tHead.nType = dsfapi::SDK_REG_TAG_DATA;
                        tHead.nLen = strContent.size();
                        tHead.nSeq = ++nSeq;
                        tHead.nReserve1 = batch_id;
                        memcpy(sBuf, &tHead, sizeof(tHead));
                        memcpy(sBuf + sizeof(tHead), strContent.c_str(), tHead.nLen);
                        nBufLen = sizeof(DataHeader) + tHead.nLen;
                        LOG_INFO << "send data.batch_id:" << batch_id << ", strContent = " << strContent
                                 << ", nBufLen = " << nBufLen << std::endl;
                        SendMessage(sBuf, nBufLen);
                    }
                }

                StartPushTimer(); // Restart timer
            }
        });
    }
    void ReadMessage()
    {
        auto self(shared_from_this());
        memset(data_, 0, dsfapi::BUFFER_DEF_LENGTH);
        socket_.async_read_some(boost::asio::buffer(data_, dsfapi::BUFFER_DEF_LENGTH),
                                [this, self](const boost::system::error_code &error, std::size_t bytes_transferred) {
                                    if (!error)
                                    {
                                        LOG_INFO << "recv data. bytes_transferred = " << bytes_transferred << std::endl;
                                        DealRequest(data_, bytes_transferred);
                                        // Continue reading
                                        ReadMessage();
                                    }
                                    else
                                    {
                                        LOG_ERR << "Failed to read message: " << error.message() << std::endl;
                                        socket_.close();
                                        // Handle disconnection here
                                    }
                                });
    }

    void DealRequest(char *sBuf, const size_t nLen)
    {
        int32 nRet = 0;
        int32 nBufLen = nLen;
        DataHeader *pHead = (DataHeader *)sBuf;
        LOG_INFO << pHead->ToString() << std::endl;
        const char *pContent = sBuf + sizeof(DataHeader);
        std::vector<dsfapi::TagValue> vecContent;
        auto bRes = dsfapi::UnPackBuffer(pContent, pHead->nLen, &vecContent);
        if (!bRes || 0 == vecContent.size())
        {
            LOG_ERR << "UnPackBuffer failed, bRes=" << bRes << " vecContent.size()=" << vecContent.size() << std::endl;
            return;
        }

        switch (pHead->nType)
        {
        case dsfapi::SDK_READ_DATA: {
            LOG_INFO << "SDK_READ_DATA readed data: " << std::string(pContent, pHead->nLen) << std::endl;
            // query the value
            std::string strValue = "";
            int32 error_code = 0;
            for (int i = 0; i < vecContent.size(); i++)
            {
                std::string strTemp = dsfapi::CurrentTimestampMicro();
                dsfapi::PackItem(&strValue, strTemp.c_str(), strTemp.size());
                dsfapi::PackItem(&strValue, reinterpret_cast<char *>(&error_code), sizeof(error_code));
            }

            memcpy(sBuf + sizeof(DataHeader), strValue.c_str(), strValue.size());
            pHead->nLen = strValue.size();
            pHead->nFlag = 0; // 0: success, 1: failed
            nBufLen = sizeof(DataHeader) + pHead->nLen;
            LOG_INFO << "SDK_READ_DATA send length:" << pHead->nLen << ", data:" << strValue << std::endl;
            SendMessage(sBuf, nBufLen);

            break;
        }
        case dsfapi::SDK_CONTROL_DATA:
        case dsfapi::SDK_DUMP_DATA: {
            for (int i = 0; i < vecContent.size(); i += 2)
            {
                LOG_INFO << "SDK_CONTROL_DATA/SDK_DUMP_DATA tagname:" << vecContent[i].Buffer() << " value:";
                for (size_t pos = 0; pos < vecContent[i + 1].Length(); pos++)
                {
                    std::cout << std::hex << std::setw(2) << std::setfill('0')
                              << static_cast<int>(static_cast<unsigned char>(vecContent[i + 1].Buffer()[pos]));
                }
                std::cout << std::endl;
            }
            // send the value
            pHead->nLen = 0;  // have not content data
            pHead->nFlag = 0; // 0: success, 1: failed
            nBufLen = sizeof(DataHeader);
            SendMessage(sBuf, nBufLen);

            break;
        }
        case dsfapi::SDK_REG_TAG: {
            uint16 &nBatchId = pHead->nReserve1;
            uint16 &nInterval = pHead->nReserve2;
            LOG_INFO << "SDK_REG_TAG batch_id:" << nBatchId << ", interval:" << nInterval
                     << ", tag size:" << vecContent.size() << std::endl;
            RegInfo tRegInfo{std::move(vecContent), nInterval};
            batch_data_.emplace(nBatchId, std::move(tRegInfo));

            // send the value
            pHead->nLen = 0;  // have not content data
            pHead->nFlag = 0; // 0: success, 1: failed
            nBufLen = sizeof(DataHeader);
            SendMessage(sBuf, nBufLen);
            break;
        }
        case dsfapi::SDK_READ_ATTR: {
            std::string strObjectName(vecContent[0].Buffer(), vecContent[0].Length());
            LOG_INFO << "SDK_READ_ATTR readed data: " << strObjectName << std::endl;
            // query the value
            std::string strValue = "";
            dsfapi::TagtypeInfo objAttr[10];
            for (int i = 0; i < 10; i++)
            {
                std::string strName = strObjectName + std::string(".tag") + std::to_string(i);
                strncpy(objAttr[i].szName, strName.c_str(), strName.size());
                strncpy(objAttr[i].szAliasName, strName.c_str(), strName.size());
                objAttr[i].nType = dsfapi::SDKAttrType::SDK_ATTR_TYPE_INT8;
                objAttr[i].nLen = 10 + i;
            }

            memcpy(sBuf + sizeof(DataHeader), objAttr, sizeof(dsfapi::TagtypeInfo) * 10);
            pHead->nLen = sizeof(dsfapi::TagtypeInfo) * 10;
            pHead->nFlag = 0; // 0: success, 1: failed
            nBufLen = sizeof(DataHeader) + pHead->nLen;
            LOG_INFO << "SDK_READ_DATA send length:" << pHead->nLen << ", data:" << strValue << std::endl;
            SendMessage(sBuf, nBufLen);

            break;
        }
        case dsfapi::SDK_READ_ATTR_DATA: {
            std::string strObjectName(vecContent[0].Buffer(), vecContent[0].Length());
            LOG_INFO << "SDK_READ_ATTR_DATA readed data: " << strObjectName << std::endl;
            // query the value
            std::string strValue = "";
            for (int i = 0; i < 10; i++)
            {
                std::string strTempName = strObjectName + std::string(".tag") + std::to_string(i);
                std::string strTempValue = dsfapi::CurrentTimestampMicro();
                dsfapi::PackItem(&strValue, strTempName.c_str(), strTempName.size());
                dsfapi::PackItem(&strValue, strTempValue.c_str(), strTempValue.size());
            }

            memcpy(sBuf + sizeof(DataHeader), strValue.c_str(), strValue.size());
            pHead->nLen = strValue.size();
            pHead->nFlag = 0; // 0: success, 1: failed
            nBufLen = sizeof(DataHeader) + pHead->nLen;
            LOG_INFO << "SDK_READ_ATTR_DATA send length:" << pHead->nLen << ", data:" << strValue << std::endl;
            SendMessage(sBuf, nBufLen);

            break;
        }
        case dsfapi::SDK_READ_MODEL_JSON: {
            std::string strObjectName(vecContent[0].Buffer(), vecContent[0].Length());
            LOG_INFO << "SDK_READ_MODEL_JSON readed data: " << strObjectName << std::endl;

            // query the value
            json jModel;
            jModel["MemberName"] = "UDTVAR1";
            jModel["MemberType"] = "DINT";
            jModel["Offset"] = 0;
            jModel["Length"] = 4;
            //  serialize to string
            std::string strValue = jModel.dump();

            memcpy(sBuf + sizeof(DataHeader), strValue.c_str(), strValue.size());
            pHead->nLen = strValue.size();
            pHead->nFlag = 0; // 0: success, 1: failed
            nBufLen = sizeof(DataHeader) + pHead->nLen;
            LOG_INFO << "SDK_READ_MODEL_JSON send length:" << pHead->nLen << ", data:" << strValue << std::endl;
            SendMessage(sBuf, nBufLen);

            break;
        }
        default:
            LOG_INFO << "Not supported pHead->nType: " << static_cast<u_int32_t>(pHead->nType) << std::endl;
            break;
        }
    }

    tcp::socket socket_;
    char data_[dsfapi::RECV_BUFFER_MAX_LENGTH]; // Buffer for async read operations
    std::map<uint16, RegInfo> batch_data_;
    boost::asio::steady_timer timer_;
};

class DataService
{
  public:
    DataService(boost::asio::io_context &io_context, const tcp::endpoint &endpoint)
        : acceptor_(io_context, endpoint), io_context_(io_context)
    {
        StartAccept();
    }

    void StartAccept()
    {
        auto new_session = std::make_shared<ClientSession>(io_context_);
        acceptor_.async_accept(new_session->socket(), [this, new_session](const boost::system::error_code &error) {
            if (!error)
            {
                LOG_INFO << "New client connected" << std::endl;
                new_session->Start();
            }
            StartAccept(); // Accept next connection
        });
    }

  private:
    tcp::acceptor acceptor_;
    boost::asio::io_context &io_context_;
};

int main()
{
    try
    {
        boost::asio::io_context io_context;

        tcp::endpoint endpoint(tcp::v4(), 1234);
        DataService server(io_context, endpoint);

        // Run the io_context to process asynchronous events
        io_context.run();
    }
    catch (std::exception &e)
    {
        LOG_ERR << "Exception: " << e.what() << std::endl;
    }

    return 0;
}
