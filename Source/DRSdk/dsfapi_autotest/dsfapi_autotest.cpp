#include "DRDataService/common.h"
#include "drsdk_inner.h"
#include "dsfapi.h"
#include "nlohmann/json.hpp"
#include "sqlite/sqlite3.h"
#include <atomic>
#include <chrono>
#include <cmath>
#include <csignal>
#include <filesystem>
#include <iostream>
#include <thread>
#include <vector>

namespace fs = std::filesystem;
using json = nlohmann::json;

std::atomic<bool> g_bStop(false);
std::atomic<bool> g_bReduntancy(false);
std::atomic<bool> g_bConnected(false);

struct TagInfo
{
    std::string name = "";
    std::string type = "";
    std::string value_orig = "";
    std::string value_new = "";
};
using TagInfoPtr = std::shared_ptr<TagInfo>;

struct Config
{
    std::string dsf_ip = "";
    uint16 dsf_port = 0;
    std::string dsf_ip_bak = "";
    uint16 dsf_port_bak = 0;

    std::vector<std::string> oper_type = {};
    std::vector<TagInfoPtr> tags;
    std::unordered_map<std::string, TagInfoPtr> tag_map;
};
using ConfigPtr = std::unique_ptr<Config>;
ConfigPtr g_config(new Config());

using GetHandlerFunc = std::function<std::string(dsfapi::TagValue *)>;
static const std::unordered_map<std::string, GetHandlerFunc> gTypeGetHandlers = {

    {"BOOL",
     [](dsfapi::TagValue *value) {
         bool res = *reinterpret_cast<bool *>(value->Buffer());
         return std::string(res ? "TRUE" : "FALSE");
     }},
    {"CHAR",
     [](dsfapi::TagValue *value) {
         char res = *reinterpret_cast<char *>(value->Buffer());
         return std::string(1, res);
     }},
    {"SINT",
     [](dsfapi::TagValue *value) {
         int8 res = *reinterpret_cast<int8 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"USINT",
     [](dsfapi::TagValue *value) {
         uint8 res = *reinterpret_cast<uint8 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"INT",
     [](dsfapi::TagValue *value) {
         int16 res = *reinterpret_cast<int16 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"UINT",
     [](dsfapi::TagValue *value) {
         uint16 res = *reinterpret_cast<uint16 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"DINT",
     [](dsfapi::TagValue *value) {
         int32 res = *reinterpret_cast<int32 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"TIME",
     [](dsfapi::TagValue *value) {
         int32 res = *reinterpret_cast<int32 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"DATE",
     [](dsfapi::TagValue *value) {
         uint32 res = *reinterpret_cast<uint32 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"DT",
     [](dsfapi::TagValue *value) {
         uint32 res = *reinterpret_cast<uint32 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"TOD",
     [](dsfapi::TagValue *value) {
         uint32 res = *reinterpret_cast<uint32 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"UDINT",
     [](dsfapi::TagValue *value) {
         uint32 res = *reinterpret_cast<uint32 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"LINT",
     [](dsfapi::TagValue *value) {
         int64 res = *reinterpret_cast<int64 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"LTIME",
     [](dsfapi::TagValue *value) {
         int64 res = *reinterpret_cast<int64 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"LDATE",
     [](dsfapi::TagValue *value) {
         uint64 res = *reinterpret_cast<uint64 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"LDT",
     [](dsfapi::TagValue *value) {
         uint64 res = *reinterpret_cast<uint64 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"LTOD",
     [](dsfapi::TagValue *value) {
         uint64 res = *reinterpret_cast<uint64 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"ULINT",
     [](dsfapi::TagValue *value) {
         uint64 res = *reinterpret_cast<uint64 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"REAL",
     [](dsfapi::TagValue *value) {
         float32 res = *reinterpret_cast<float32 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"LREAL",
     [](dsfapi::TagValue *value) {
         float64 res = *reinterpret_cast<float64 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"BYTE",
     [](dsfapi::TagValue *value) {
         uint8 res = *reinterpret_cast<uint8 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"WORD",
     [](dsfapi::TagValue *value) {
         uint16 res = *reinterpret_cast<uint16 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"DWORD",
     [](dsfapi::TagValue *value) {
         uint32 res = *reinterpret_cast<uint32 *>(value->Buffer());
         return std::to_string(res);
     }},
    {"LWORD",
     [](dsfapi::TagValue *value) {
         uint64 res = *reinterpret_cast<uint64 *>(value->Buffer());
         return std::to_string(res);
     }},
    // {"STRING", [](dsfapi::TagValue *value) { return std::string(value->Buffer() + 2, value->Length() - 2); }},
    {"STRING", [](dsfapi::TagValue *value) { return std::string(value->Buffer() + 2, value->Buffer()[1]); }},

};

using SetHandlerFunc = std::function<void(dsfapi::TagValue *, const std::string &)>;
static const std::unordered_map<std::string, SetHandlerFunc> gTypeSetHandlers = {
    {"BOOL",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         bool res = (strValue == "TRUE" || strValue == "true" || strValue == "1");
         pTagValue->ReSet((const char *)&res, sizeof(bool));
     }},
    {"CHAR",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         char res = !strValue.empty() ? strValue[0] : '\0';
         pTagValue->ReSet((const char *)&res, sizeof(char));
     }},
    {"SINT",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         int8 res = static_cast<int8>(std::stoi(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(int8));
     }},
    {"USINT",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint8 res = static_cast<uint8>(std::stoul(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint8));
     }},
    {"INT",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         int16 res = static_cast<int16>(std::stoi(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(int16));
     }},
    {"UINT",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint16 res = static_cast<uint16>(std::stoul(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint16));
     }},
    {"DINT",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         int32 res = static_cast<int32>(std::stol(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(int32));
     }},
    {"TIME",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         int32 res = static_cast<int32>(std::stol(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(int32));
     }},
    {"UDINT",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint32 res = static_cast<uint32>(std::stoul(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint32));
     }},
    {"DATE",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint32 res = static_cast<uint32>(std::stoul(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint32));
     }},
    {"DT",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint32 res = static_cast<uint32>(std::stoul(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint32));
     }},
    {"TOD",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint32 res = static_cast<uint32>(std::stoul(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint32));
     }},
    {"LINT",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         int64 res = static_cast<int64>(std::stoll(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(int64));
     }},
    {"LTIME",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         int64 res = static_cast<int64>(std::stoll(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(int64));
     }},
    {"ULINT",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint64 res = static_cast<uint64>(std::stoull(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint64));
     }},
    {"LDATE",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint64 res = static_cast<uint64>(std::stoull(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint64));
     }},
    {"LDT",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint64 res = static_cast<uint64>(std::stoull(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint64));
     }},
    {"LTOD",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint64 res = static_cast<uint64>(std::stoull(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint64));
     }},
    {"REAL",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         float32 res = std::stof(strValue);
         pTagValue->ReSet((const char *)&res, sizeof(float32));
     }},
    {"LREAL",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         float64 res = std::stod(strValue);
         pTagValue->ReSet((const char *)&res, sizeof(float64));
     }},
    {"BYTE",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint8 res = static_cast<uint8>(std::stoul(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint8));
     }},
    {"WORD",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint16 res = static_cast<uint16>(std::stoul(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint16));
     }},
    {"DWORD",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint32 res = static_cast<uint32>(std::stoul(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint32));
     }},
    {"LWORD",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         uint64 res = static_cast<uint64>(std::stoull(strValue));
         pTagValue->ReSet((const char *)&res, sizeof(uint64));
     }},
    {"STRING",
     [](dsfapi::TagValue *pTagValue, const std::string &strValue) {
         // 若有PLC格式要求（如前两个字节是长度），可根据具体协议调整
         std::string buffer(2, '\0'); // 预留2字节长度位
         buffer[0] = static_cast<char>(strValue.length() + 2);
         buffer[1] = static_cast<char>(strValue.length());
         buffer.append(strValue);
         pTagValue->ReSet(buffer.data(), buffer.size());
     }},
};

void TestRead(dsfapi::DRSdkContext *pContext)
{
    const int nSize = g_config->tags.size();
    if (nSize <= 0)
    {
        LOG_INFO << "tags size =0";
        return;
    }

    const char *pTagName[nSize] = {0};
    for (int i = 0; i < nSize; i++)
    {
        pTagName[i] = (char *)g_config->tags[i]->name.c_str();
        LOG_INFO << "DR_Read:" << i << "->tag_name = " << pTagName[i] << ",type:" << g_config->tags[i]->type;
    }
    dsfapi::TagValue *pTagValue = nullptr;
    int32 *pErrorCode = nullptr;
    int32 nTagValueCount = 0;
    auto res = dsfapi::DR_Read(pContext, (const char **)pTagName, nSize, &pTagValue, &pErrorCode, &nTagValueCount);
    LOG_INFO << "DR_Read:res = " << res;
    if (0 == res)
    {
        for (int i = 0; i < nTagValueCount; i++)
        {
            const auto &tagname = g_config->tags[i]->name;
            if (0 != pErrorCode[i])
            {
                LOG_ERR << " tag_name[" << tagname << "] read failed,  errorCode:" << pErrorCode[i];
                continue;
            }

            auto iterHandler = gTypeGetHandlers.find(g_config->tags[i]->type);
            if (iterHandler != gTypeGetHandlers.end())
            {
                const std::string &expect_value = g_config->tags[i]->value_orig;  // 期望的值
                const std::string tag_value = iterHandler->second(&pTagValue[i]); // 读到的值
                LOG_INFO << " tag_name[" << tagname << "] tag_value:" << tag_value << ", expect_value:" << expect_value;
                if (expect_value == tag_value)
                {
                    LOG_INFO << " tag_name[" << tagname << "] read success, value: " << expect_value
                             << " == " << tag_value;
                }
                else
                {
                    LOG_ERR << " tag_name[" << tagname << "] read failed, value: " << expect_value
                            << " != " << tag_value;
                }
            }
            else
            {
                LOG_ERR << " tag_name[" << tagname << "] read failed, Type not supported, young man!"
                        << g_config->tags[i]->type;
            }
        }
    }
    // Must be released
    dsfapi::Free_Int32_Ptr(&pErrorCode);
    dsfapi::Free_Tag_Value(&pTagValue);
}

void TestWrite(dsfapi::DRSdkContext *pContext, bool write_origin = true)
{
    const int nSize = g_config->tags.size();
    if (nSize <= 0)
    {
        LOG_INFO << "tags size =0";
        return;
    }

    std::shared_ptr<dsfapi::TagValue> pTagValues(new dsfapi::TagValue[nSize],
                                                 [](dsfapi::TagValue *p) { dsfapi::Free_Tag_Value(&p); });
    const char *pTagName[nSize] = {0};
    for (int i = 0; i < nSize; i++)
    {
        pTagName[i] = (char *)g_config->tags[i]->name.c_str();
        std::string *pValue = nullptr;
        if (write_origin)
        {
            pValue = &g_config->tags[i]->value_orig;
        }
        else
        {
            pValue = &g_config->tags[i]->value_new;
        }
        const auto &tag_value = *pValue;
        LOG_INFO << "DR_Write:" << i << "->tag_name = " << pTagName[i] << ",type:" << g_config->tags[i]->type
                 << ",value:" << tag_value;

        auto iterHandler = gTypeSetHandlers.find(g_config->tags[i]->type);
        if (iterHandler != gTypeSetHandlers.end())
        {
            iterHandler->second(pTagValues.get() + i, tag_value);
        }
        else
        {
            LOG_ERR << "Type not supported, young man!" << g_config->tags[i]->type;
        }
    }
    auto res = dsfapi::DR_Write(pContext, (const char **)pTagName, pTagValues.get(), nSize);
    LOG_INFO << "DR_Write:res = " << res;

    std::this_thread::sleep_for(std::chrono::seconds(2));
    dsfapi::TagValue *pTagValue = nullptr;
    int32 *pErrorCode = nullptr;
    int32 nTagValueCount = 0;
    res = dsfapi::DR_Read(pContext, (const char **)pTagName, nSize, &pTagValue, &pErrorCode, &nTagValueCount);
    LOG_INFO << "DR_Read:res = " << res;
    if (0 == res)
    {
        for (int i = 0; i < nTagValueCount; i++)
        {
            auto &tagname = g_config->tags[i]->name;
            if (0 != pErrorCode[i])
            {
                LOG_ERR << " tag_name[" << tagname << "] read failed,  errorCode:" << pErrorCode[i];
                continue;
            }

            auto iterHandler = gTypeGetHandlers.find(g_config->tags[i]->type);
            if (iterHandler != gTypeGetHandlers.end())
            {
                const std::string &expect_value =
                    write_origin ? g_config->tags[i]->value_orig : g_config->tags[i]->value_new; // 期望的值
                const std::string tag_value = iterHandler->second(&pTagValue[i]);                // 读到的值
                LOG_INFO << " tag_name[" << tagname << "] tag_value:" << tag_value << ", expect_value:" << expect_value;
                if (expect_value == tag_value)
                {
                    LOG_INFO << " tag_name[" << tagname << "] read success, value: " << expect_value
                             << " == " << tag_value;
                }
                else
                {
                    LOG_ERR << " tag_name[" << tagname << "] read failed, value: " << expect_value
                            << " != " << tag_value;
                }
            }
            else
            {
                LOG_ERR << " tag_name[" << tagname << "] read failed, Type not supported, young man!"
                        << g_config->tags[i]->type;
            }
        }
    }
    // Must be released
    dsfapi::Free_Int32_Ptr(&pErrorCode);
    dsfapi::Free_Tag_Value(&pTagValue);
}

void TestWriteText(dsfapi::DRSdkContext *pContext, bool write_origin = true)
{
    const int nSize = g_config->tags.size();
    if (nSize <= 0)
    {
        LOG_INFO << "tags size =0";
        return;
    }

    std::string strCharHex = "";
    std::string strStringHex = "";
    std::string strRealHex = "";
    std::string strLRealHex = "";
    std::shared_ptr<char *> pTagValueTexts(new char *[nSize]);
    const char *pTagName[nSize] = {0};
    for (int i = 0; i < nSize; i++)
    {
        pTagName[i] = (char *)g_config->tags[i]->name.c_str();
        std::string *pValue = nullptr;
        if (write_origin)
        {
            pValue = &g_config->tags[i]->value_orig;
        }
        else
        {
            pValue = &g_config->tags[i]->value_new;
        }

        if ("CHAR" == g_config->tags[i]->type)
        {
            strCharHex = StrToHexString(*pValue);
            pValue = &strCharHex;
        }
        else if ("STRING" == g_config->tags[i]->type)
        {
            strStringHex = StrToHexString(*pValue);
            pValue = &strStringHex;
        }
        else if ("REAL" == g_config->tags[i]->type)
        {
            strRealHex = FloatHexConverter::FloatToHex(std::stof(*pValue));
            pValue = &strRealHex;
        }
        else if ("LREAL" == g_config->tags[i]->type)
        {
            strLRealHex = FloatHexConverter::DoubleToHex(std::stod(*pValue));
            pValue = &strLRealHex;
        }

        const auto &tag_value = *pValue;
        LOG_INFO << "TestWriteText:" << i << "->tag_name = " << pTagName[i] << ",type:" << g_config->tags[i]->type
                 << ",value:" << tag_value;
        pTagValueTexts.get()[i] = (char *)tag_value.c_str();
    }
    auto res = dsfapi::DR_Write_Text(pContext, (const char **)pTagName, (const char **)pTagValueTexts.get(), nSize);
    LOG_INFO << "TestWriteText:res = " << res;

    std::this_thread::sleep_for(std::chrono::seconds(2));
    dsfapi::TagValue *pTagValue = nullptr;
    int32 *pErrorCode = nullptr;
    int32 nTagValueCount = 0;
    res = dsfapi::DR_Read(pContext, (const char **)pTagName, nSize, &pTagValue, &pErrorCode, &nTagValueCount);
    LOG_INFO << "DR_Read:res = " << res;
    if (0 == res)
    {
        for (int i = 0; i < nTagValueCount; i++)
        {
            auto &tagname = g_config->tags[i]->name;
            if (0 != pErrorCode[i])
            {
                LOG_ERR << " tag_name[" << tagname << "] read failed,  errorCode:" << pErrorCode[i];
                continue;
            }

            auto iterHandler = gTypeGetHandlers.find(g_config->tags[i]->type);
            if (iterHandler != gTypeGetHandlers.end())
            {
                const std::string &expect_value =
                    write_origin ? g_config->tags[i]->value_orig : g_config->tags[i]->value_new; // 期望的值
                const std::string tag_value = iterHandler->second(&pTagValue[i]);                // 读到的值
                LOG_INFO << " tag_name[" << tagname << "] tag_value:" << tag_value << ", expect_value:" << expect_value;
                if (expect_value == tag_value)
                {
                    LOG_INFO << " tag_name[" << tagname << "] read success, value: " << expect_value
                             << " == " << tag_value;
                }
                else
                {
                    LOG_ERR << " tag_name[" << tagname << "] read failed, value: " << expect_value
                            << " != " << tag_value;
                }
            }
            else
            {
                LOG_ERR << " tag_name[" << tagname << "] read failed, Type not supported, young man!"
                        << g_config->tags[i]->type;
            }
        }
    }
    // Must be released
    dsfapi::Free_Int32_Ptr(&pErrorCode);
    dsfapi::Free_Tag_Value(&pTagValue);
}

void TestRegisterTag(dsfapi::DRSdkContext *pContext)
{
    auto func1 = [](const int32 nBatchId, dsfapi::TagRecord *pTagRecord, int32 *pErrorCode, const int32 nTagCount) {
        LOG_INFO << "TestRegisterTag1. count=" << nTagCount << ", batch_id=" << nBatchId;
        for (int i = 0; i < nTagCount; ++i)
        {
            auto tagname = std::string(pTagRecord[i].tTagName.Buffer(), pTagRecord[i].tTagName.Length());
            if (0 != pErrorCode[i])
            {
                LOG_ERR << ":TestRegisterTag] tag_name[" << tagname << "] read failed,  errorCode:" << pErrorCode[i];
                continue;
            }

            auto iter = g_config->tag_map.find(tagname);
            if (iter == g_config->tag_map.end())
            {
                LOG_ERR << ":TestRegisterTag] tag_name[" << tagname << "] read failed,  cannot find tag_name in config";
                continue;
            }

            const auto &tag = iter->second;
            auto iterHandler = gTypeGetHandlers.find(tag->type);
            if (iterHandler != gTypeGetHandlers.end())
            {
                const std::string &expect_value = tag->value_orig;                           // 期望的值
                const std::string tag_value = iterHandler->second(&pTagRecord[i].tTagValue); // 读到的值
                LOG_INFO << ":TestRegisterTag] tag_name[" << tagname << "] tag_value:" << tag_value
                         << ", expect_value:" << expect_value;
                if (expect_value == tag_value)
                {
                    LOG_INFO << ":TestRegisterTag] tag_name[" << tagname << "] read success, value: " << expect_value
                             << " == " << tag_value;
                }
                else
                {
                    LOG_ERR << ":TestRegisterTag] tag_name[" << tagname << "] read failed, value: " << expect_value
                            << " != " << tag_value;
                }
            }
            else
            {
                LOG_ERR << ":TestRegisterTag] tag_name[" << tagname << "] read failed, Type not supported, young man!"
                        << tag->type;
            }
        }

        // Must be released
        dsfapi::Free_Int32_Ptr(&pErrorCode);
        dsfapi::Free_Tag_Record(&pTagRecord);
    };

    int32 res = 0;
    const int nSize = g_config->tags.size();
    if (nSize <= 0)
    {
        LOG_INFO << "tags size =0";
        return;
    }

    const char *pTagName[nSize] = {0};
    for (int i = 0; i < nSize; i++)
    {
        pTagName[i] = (char *)g_config->tags[i]->name.c_str();
        LOG_INFO << "DR_Register:" << i << "->tag_name = " << pTagName[i] << ",type:" << g_config->tags[i]->type;
    }
    int32 nBatchId1 = 0;
    res = dsfapi::DR_Register_Tag(pContext, (const char **)pTagName, nSize, 1000, func1, 0, &nBatchId1);
    LOG_INFO << "DR_Register_Tag 1 :res = " << res << ", batch_id=" << nBatchId1;

    // unregister : assuming we want to unregistern BatchId2 after 5s.
    std::this_thread::sleep_for(std::chrono::seconds(5));
    if (pContext && pContext->ConnectStatus())
    {
        int32 res = dsfapi::DR_Unregister_Tag(pContext, nBatchId1);
        LOG_INFO << "DR_Unregister_Tag:res = " << res << ", batch_id=" << nBatchId1;
    }
}
void TestRegisterTagJson(dsfapi::DRSdkContext *pContext)
{
    auto func1 = [](const int32 nBatchId, char *strJson, const int32 nLen) {
        LOG_INFO << "TestRegisterTagJson received data. batch_id=" << nBatchId
                 << ", json:" << std::string(strJson, nLen) << std::endl;
        try
        {
            // 把 JSON 字符串转换成 json 对象
            std::string jsonStr(strJson, nLen);
            json jsonArr = json::parse(jsonStr);

            if (jsonArr.size() != g_config->tags.size())
            {
                LOG_ERR << ":TestRegisterTagjson] tag_name[] read failed, jsonArr.size = " << jsonArr.size();
            }

            // 遍历数组中的每个对象，进行校验
            for (const auto &obj : jsonArr)
            {
                const std::string tagname = obj["obj_name"];
                const int error_code = obj["error_code"];
                // 这里是你自己定义的校验逻辑
                if (error_code != 0)
                {
                    LOG_ERR << ":TestRegisterTagjson] tag_name[" << tagname
                            << "] read failed,  errorCode:" << error_code;
                    continue;
                }

                const std::string value = obj["value"].at(0); // value 是数组，取第一个元素
                auto iter = g_config->tag_map.find(tagname);
                if (iter == g_config->tag_map.end())
                {
                    LOG_ERR << ":TestRegisterTagjson] tag_name[" << tagname
                            << "] read failed,  cannot find tag_name in config";
                    continue;
                }

                const auto &tag = iter->second;
                auto iterHandler = gTypeGetHandlers.find(tag->type);
                if (iterHandler != gTypeGetHandlers.end())
                {
                    const std::string &expect_value = tag->value_orig; // 期望的值
                    // const std::string &tag_value = value;              // 读到的值
                    std::string tag_value = value; // 读到的值
                    if (tag->type == "BOOL")
                    {
                        std::transform(tag_value.begin(), tag_value.end(), tag_value.begin(), ::toupper);
                    }
                    else if (tag->type == "CHAR" || tag->type == "STRING")
                    {
                        HexStringToStr(value, tag_value);
                    }
                    else if (tag->type == "REAL")
                    {
                        float f = FloatHexConverter::HexToFloat(value);
                        tag_value = std::to_string(f); // 转成默认的精度
                    }
                    else if (tag->type == "LREAL")
                    {
                        double d = FloatHexConverter::HexToDouble(value);
                        tag_value = std::to_string(d); // 转成默认的精度
                    }

                    LOG_INFO << ":TestRegisterTagjson] tag_name[" << tagname << "] tag_value:" << tag_value
                             << ", expect_value:" << expect_value;
                    if (expect_value == tag_value)
                    {
                        LOG_INFO << ":TestRegisterTagjson] tag_name[" << tagname
                                 << "] read success, value: " << expect_value << " == " << tag_value;
                    }
                    else
                    {
                        LOG_ERR << ":TestRegisterTagjson] tag_name[" << tagname
                                << "] read failed, value: " << expect_value << " != " << tag_value;
                    }
                }
                else
                {
                    LOG_ERR << ":TestRegisterTagjson] tag_name[" << tagname
                            << "] read failed, Type not supported, young man!" << tag->type;
                }
            }
        }
        catch (const std::exception &ex)
        {
            LOG_ERR << ":TestRegisterTagjson] tag_name[] read failed, JSON parse or validation error: " << ex.what();
        }

        // Must be released
        dsfapi::Free_Str_Ptr(&strJson);
    };

    int32 res = 0;
    const int nSize = g_config->tags.size();
    if (nSize <= 0)
    {
        LOG_INFO << "tags size =0";
        return;
    }

    const char *pTagName[nSize] = {0};
    for (int i = 0; i < nSize; i++)
    {
        pTagName[i] = (char *)g_config->tags[i]->name.c_str();
        LOG_INFO << "DR_Register_Tag_Json:" << i << "->tag_name = " << pTagName[i]
                 << ",type:" << g_config->tags[i]->type;
    }
    int32 nBatchId1 = 0;
    res = dsfapi::DR_Register_Tag_Json(pContext, (const char **)pTagName, nSize, 1000, func1, 0, &nBatchId1);
    LOG_INFO << "DR_Register_Tag_Json 1 :res = " << res << ", batch_id=" << nBatchId1 << std::endl;

    std::this_thread::sleep_for(std::chrono::seconds(5));
    if (pContext && pContext->ConnectStatus())
    {
        int32 res = dsfapi::DR_Unregister_Tag(pContext, nBatchId1);
        LOG_INFO << "DR_Unregister_Tag_Json:res = " << res << ", batch_id=" << nBatchId1;
    }
}

void TestRegReconnect(dsfapi::DRSdkContext *pContext)
{
    g_bConnected.store(false);
    auto func1 = [](const int32 nBatchId, char *strJson, const int32 nLen) {
        LOG_INFO << "TestRegReconnect received data: g_bConnected=" << g_bConnected << ", batch_id=" << nBatchId
                 << ", json:" << std::string(strJson, nLen) << std::endl;

        // Must be released
        dsfapi::Free_Str_Ptr(&strJson);
    };

    int32 res = 0;
    int nSize = g_config->tags.size();
    if (nSize <= 0)
    {
        LOG_INFO << "tags size =0";
        return;
    }

    nSize = 1;
    const char *pTagName[nSize] = {0};
    for (int i = 0; i < nSize; i++)
    {
        pTagName[i] = (char *)g_config->tags[i]->name.c_str();
        LOG_INFO << "TestRegReconnect:" << i << "->tag_name = " << pTagName[i] << ",type:" << g_config->tags[i]->type;
    }
    int32 nBatchId1 = 0;
    res = dsfapi::DR_Register_Tag_Json(pContext, (const char **)pTagName, nSize, 1000, func1, 0, &nBatchId1);
    LOG_INFO << "TestRegReconnect:res = " << res << ", batch_id=" << nBatchId1 << std::endl;

    std::this_thread::sleep_for(std::chrono::seconds(3));
    const char *cmd = "ps -ef | grep dsfdataservice | grep -v grep | awk '{print $2}' | xargs kill -9";
    int ret = system(cmd);
    if (ret != 0)
    {
        LOG_INFO << "Failed to kill process, return code: " << ret;
    }
    g_bConnected.store(true);

    std::this_thread::sleep_for(std::chrono::seconds(5));
    if (pContext && pContext->ConnectStatus())
    {
        int32 res = dsfapi::DR_Unregister_Tag(pContext, nBatchId1);
        LOG_INFO << "TestRegReconnect unregister_tag:res = " << res << ", batch_id=" << nBatchId1;
    }
}

dsfapi::DRSdkContext *TestDrsdkInit()
{
    // 1.should init first
    dsfapi::DRSdkConnectParam tConnectParam;
    strncpy(tConnectParam.szServerIp, g_config->dsf_ip.c_str(), sizeof(tConnectParam.szServerIp) - 1);
    tConnectParam.nServerPort = g_config->dsf_port;
    if (g_bReduntancy.load())
    {
        strncpy(tConnectParam.szServerIpBak, g_config->dsf_ip_bak.c_str(), sizeof(tConnectParam.szServerIpBak) - 1);
        tConnectParam.nServerPortBak = g_config->dsf_port_bak;
    }
    tConnectParam.nHeartbeatTimeoutMs = 3000;
    tConnectParam.nConnectTimeoutMs = 1000;

    dsfapi::DRSdkOption tOption;
    tOption.nThreadPoolNum = 10; // thread nums for run  register callback
    tOption.nRequestWaitTimeoutMs = 1000;

    dsfapi::DRSdkContext *pContext = nullptr;
    pContext = dsfapi::DR_Init(tConnectParam, tOption);
    if (nullptr == pContext || pContext->errorcode != 0)
    {
        LOG_ERR << "DR_Init failed! errorcode =" << -1;
        dsfapi::Free_DR_Sdk_Context(&pContext);
        return nullptr;
    }
    return pContext;
}

int Run()
{
    auto fut = std::async(std::launch::async, []() {
        int CNT = 100;
        while (!g_bStop.load() && --CNT > 0)
        {
            std::this_thread::sleep_for(std::chrono::seconds(1));
        }
        g_bStop.store(true);
    });

    // 1. should init first
    dsfapi::DRSdkContext *pContext = nullptr;
    pContext = TestDrsdkInit();
    if (nullptr == pContext)
    {
        return -1;
    }
    while (!g_bStop && !pContext->ConnectStatus())
    {
        LOG_ERR << "dsfapi connect_status is false!";
        std::this_thread::sleep_for(std::chrono::milliseconds(pContext->tDRSdkConnectParam.nConnectTimeoutMs));
        continue;
    }
    // 2.run
    if (!g_bStop.load())
    {
        for (const auto &oper_type : g_config->oper_type)
        {
            if ("read" == oper_type)
            {
                TestRead(pContext);
            }
            else if ("write" == oper_type)
            {
                TestWrite(pContext, false);

                std::this_thread::sleep_for(std::chrono::seconds(1));
                LOG_INFO << "resume origin value----------------------------------";
                TestWrite(pContext, true);
            }
            else if ("writetext" == oper_type)
            {
                TestWriteText(pContext, false);

                std::this_thread::sleep_for(std::chrono::seconds(1));
                LOG_INFO << "resume origin value----------------------------------";
                TestWriteText(pContext, true);
            }
            else if ("regbin" == oper_type)
            {
                TestRegisterTag(pContext);
            }
            else if ("regjson" == oper_type)
            {
                TestRegisterTagJson(pContext);
            }
            else if ("reconnect" == oper_type)
            {
                TestRegReconnect(pContext);
            }
            else
            {
                LOG_ERR << "oper_type is not supported!";
                continue;
            }
        }

        g_bStop.store(true);
    }

    fut.wait();
    // 3. should uninit
    dsfapi::DR_Uninit(pContext);
    // 4. should release
    dsfapi::Free_DR_Sdk_Context(&pContext);
    return 0;
}

int LoadConfig(const std::string &config_file)
{
    std::ifstream configFile(config_file);
    if (!configFile.is_open())
    {
        LOG_ERR << "Failed to open file:" << config_file.c_str();
        return -1;
    }
    json jConfig;
    configFile >> jConfig;
    configFile.close();

    if (!jConfig.contains("dsf_ip") || !jConfig.contains("dsf_port"))
    {
        LOG_ERR << "dsf_ip or dsf_port info not complete.";
        g_config->dsf_ip = "127.0.0.1";
        g_config->dsf_port = 1234;
    }
    else
    {
        g_config->dsf_ip = jConfig["dsf_ip"];
        g_config->dsf_port = jConfig["dsf_port"];
    }

    if (!jConfig.contains("dsf_ip_bak") || !jConfig.contains("dsf_port_bak"))
    {
        LOG_INFO << "dsf_ip_bak or dsf_port_bak info not complete.";
    }
    else
    {
        g_bReduntancy.store(true);
        g_config->dsf_ip_bak = jConfig["dsf_ip_bak"];
        g_config->dsf_port_bak = jConfig["dsf_port_bak"];
    }

    if (!jConfig.contains("tags"))
    {
        LOG_ERR << "tags info not complete.";
        return -1;
    }
    auto jConfigArray = jConfig["tags"];
    if (!jConfigArray.is_array() || jConfigArray.empty())
    {
        LOG_ERR << "tags config file incorrect!" << config_file.c_str();
        return -1;
    }

    for (auto &jTag : jConfigArray)
    {
        if (!jTag.contains("name") || !jTag.contains("type"))
        {
            LOG_ERR << "name or type info not complete.";
            return -1;
        }
        TagInfoPtr tag_info(new TagInfo);
        tag_info->name = jTag["name"];
        tag_info->type = jTag["type"];
        if (jTag.contains("value_orig"))
            tag_info->value_orig = jTag["value_orig"];
        if (jTag.contains("value_new"))
            tag_info->value_new = jTag["value_new"];

        if (tag_info->type == "REAL")
        {
            tag_info->value_orig = std::to_string(std::stof(tag_info->value_orig)); // 转成默认的精度
            tag_info->value_new = std::to_string(std::stof(tag_info->value_new));   // 转成默认的精度
        }
        else if (tag_info->type == "LREAL")
        {
            tag_info->value_orig = std::to_string(std::stod(tag_info->value_orig)); // 转成默认的精度
            tag_info->value_new = std::to_string(std::stod(tag_info->value_new));   // 转成默认的精度
        }

        g_config->tags.emplace_back(tag_info);
        g_config->tag_map[tag_info->name] = tag_info;
    }

    return 0;
}

void SignalHandler(int signal)
{
    if (signal == SIGINT)
    {
        std::cout << "\nCaught Ctrl+C (SIGINT). Exiting loop...";
        g_bStop = true;
    }
}
int main(int args, char *argv[])
{
    std::signal(SIGINT, SignalHandler);

    if (args != 3)
    {
        LOG_INFO << "Usage: " << argv[0] << " <config_file>  oper_type";
        LOG_INFO << "oper_type  in [read, write, writetext, regbin, regjson]";
        LOG_INFO << "e.g";
        LOG_INFO << "\t" << argv[0] << " read.json  read";
        LOG_INFO << "\t" << argv[0] << " read.json  all";
        LOG_INFO << "bye~";
        std::this_thread::sleep_for(std::chrono::seconds(1));
        return -1;
    }

    {
        g_config->oper_type.clear();
        if (strcmp(argv[2], "all") == 0)
        {
            g_config->oper_type.emplace_back("write"); // 写放在最前面，使得plc数据源是设定的值
            g_config->oper_type.emplace_back("read");
            g_config->oper_type.emplace_back("writetext");
            g_config->oper_type.emplace_back("regbin");
            g_config->oper_type.emplace_back("regjson");
            g_config->oper_type.emplace_back("reconnect");
        }
        else
        {
            g_config->oper_type.emplace_back(argv[2]);
        }
    }

    const std::string config_file = argv[1];
    int ret = LoadConfig(config_file);
    if (ret != 0)
    {
        LOG_ERR << "LoadConfig failed!";
        std::this_thread::sleep_for(std::chrono::seconds(1));
        return -1;
    }

    ret = Run();
    if (ret != 0)
    {
        LOG_ERR << "Run failed!";
        std::this_thread::sleep_for(std::chrono::seconds(1));
        return -1;
    }

    return 0;
}