#include "drsdk_inner.h"
#include "dsfapi.h"
#include "sqlite/sqlite3.h"
#include <atomic>
#include <chrono>
#include <cmath>
#include <csignal>
#include <iostream>
#include <thread>

std::atomic<bool> g_bStop(false);
std::atomic<bool> g_bReduntancy(false);

std::vector<std::string> tagList;

int callback(void *pObj, int argc, char **argv, char **azColName)
{
    int i;
    for (i = 0; i < argc; i++)
    {
        std::string columnName = azColName[i];
        if (columnName == "fd_block_name")
        {
            if (argv[i] == nullptr)
                continue;
            tagList.push_back(argv[i]);
        }
    }
    return 0;
}

void readSqliteDB()
{
    sqlite3 *db;
    char *zErrMsg = 0;
    int rc;
    char *sql;

    std::string m_strdbFile = "/home/<USER>/code/dr/projects/defaultproject/config/PDSCfg.db";

    tagList.clear();

    /* Open database */
    rc = sqlite3_open(m_strdbFile.c_str(), &db);
    if (rc)
    {
        fprintf(stderr, "Can't open database: %s\n", sqlite3_errmsg(db));
        exit(0);
    }
    else
    {
        // fprintf(stderr, "Opened database successfully\n");
    }

    /* Create SQL statement */
    sql = "SELECT * from t_pb_tags";

    /* Execute SQL statement */
    rc = sqlite3_exec(db, sql, callback, NULL, &zErrMsg);
    if (rc != SQLITE_OK)
    {
        fprintf(stderr, "SQL error: %s\n", zErrMsg);
        sqlite3_free(zErrMsg);
    }
    else
    {
        // fprintf(stdout, "Operation done successfully\n");
    }

    sqlite3_close(db);
}

void ConsumeCPU(int cnt = 100)
{
    for (int i = 10000; i < 10000 + cnt; i++)
    {
        auto res = std::sqrt(i);
    }
}

void signalHandler(int signal)
{
    if (signal == SIGINT)
    {
        std::cout << "\nCaught Ctrl+C (SIGINT). Exiting loop..." << std::endl;
        g_bStop = true;
    }
}

void TestRead(dsfapi::DRSdkContext *pContext)
{
    static int NUM = 4;
    char *pTagName[] = {"STD::TINT", "STD::TREAL", "STD::DS_DDS_DINT1_1", "STD::DS2.DS_1_DATA.TDINT"};
    dsfapi::TagValue *pTagValue = nullptr;
    int32 *pErrorCode = nullptr;
    int32 nTagValueCount = 0;
    auto res = dsfapi::DR_Read(pContext, (const char **)pTagName, NUM, &pTagValue, &pErrorCode, &nTagValueCount);
    LOG_INFO << "DR_Read:res = " << res << std::endl;
    if (0 == res)
    {

        for (int i = 0; i < nTagValueCount; i++)
        {
            LOG_INFO << "DR_Read: pTagName:" << pTagName[i] << ", errorCode:" << pErrorCode[i]
                     << ", len:" << pTagValue[i].Length();
            unsigned char *pBuffer = (unsigned char *)pTagValue[i].Buffer();
            std::ostringstream oss;
            for (int j = 0; j < pTagValue[i].Length(); ++j)
            {
                oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<uint32>(pBuffer[j]);
            }
            LOG_INFO << oss.str();
        }
    }
    // Must be released
    dsfapi::Free_Int32_Ptr(&pErrorCode);
    dsfapi::Free_Tag_Value(&pTagValue);
}
// #include <algorithm>
// #include <numeric> // For std::accumulate

// void TestRead_loop(dsfapi::DRSdkContext *pContext)
// {
//     static int NUM = 2;
//     char *pTagName[] = {"STD.TEST_UDT492", "STD.TEST_UDT491"};
//     dsfapi::TagValue *pTagValue = nullptr;
//     int32 *pErrorCode = nullptr;
//     int32 nTagValueCount = 0;
//     std::vector<int32> vecTimes;
//     for (int i = 0; i < 10000; i++)
//     {
//         auto start = std::chrono::system_clock::now();
//         auto res = dsfapi::DR_Read(pContext, (const char **)pTagName, NUM, &pTagValue, &pErrorCode, &nTagValueCount);
//         // release resource
//         auto end = std::chrono::system_clock::now();
//         auto duration = std::chrono::duration_cast<std::chrono::microseconds>(end - start).count();
//         LOG_INFO << "duration:" << duration << std::endl;
//         vecTimes.emplace_back(duration);
//     }

//     LOG_INFO << "max:" << *(std::max_element(vecTimes.begin(), vecTimes.end())) << std::endl;
//     LOG_INFO << "min:" << *(std::min_element(vecTimes.begin(), vecTimes.end())) << std::endl;
//     LOG_INFO << "avg:" << std::accumulate(vecTimes.begin(), vecTimes.end(), 0) / vecTimes.size() << std::endl;

//     // Must be released
//     dsfapi::Free_Int32_Ptr(&pErrorCode);
//     dsfapi::Free_Tag_Value(&pTagValue);
// }

void TestWrite(dsfapi::DRSdkContext *pContext)
{
    static int NUM = 4;
    char *pTagName[] = {"STD::ICE_LREAL_RES", "STD::ICE_LINT_RES", "STD::ICE_BOOL_RES", "STD::ICE_STRING_RES"};
    dsfapi::TagValue tTagValue[NUM];

    {
        double nValue1 = 66.66;
        tTagValue[0].ReSet((const char *)&nValue1, sizeof(nValue1));
    }
    {
        int64 nValue1 = 1234;
        tTagValue[1].ReSet((const char *)&nValue1, sizeof(nValue1));
    }
    {
        bool nValue1 = true;
        tTagValue[2].ReSet((const char *)&nValue1, sizeof(nValue1));
    }
    {
        char nValue1[32] = {};
        nValue1[0] = 30;
        nValue1[1] = 8;
        strcpy((char *)(&nValue1[2]), "baosight");

        tTagValue[3].ReSet((const char *)nValue1, sizeof(nValue1));
    }

    auto res = dsfapi::DR_Write(pContext, (const char **)pTagName, tTagValue, NUM);
    LOG_INFO << "DR_Write:res = " << res << std::endl;
}

void TestWriteText(dsfapi::DRSdkContext *pContext)
{
    // static int NUM = 4;
    // char *pTagName[] = {"STD::ICE_LREAL_RES", "STD::ICE_LINT_RES", "STD::ICE_BOOL_RES", "STD::ICE_STRING_RES"};
    // char *pTagValue[] = {"3.14", "1234", "TRUE", "baosight"};
    static int NUM = 2;
    char *pTagName[] = {"STD::DS_TLREAL", "STD::DS_TREAL"};
    char *pTagValue[] = {"3ff0000000000000", "3f800000"};
    // static char c = 'a';
    // char *pTagValue[1] = {&c};

    auto res = dsfapi::DR_Write_Text(pContext, (const char **)pTagName, (const char **)pTagValue, NUM);
    LOG_INFO << "DR_Write_Text:res = " << res << std::endl;
}

void TestSave(dsfapi::DRSdkContext *pContext)
{
    static int NUM = 3;
    char *pTagName[] = {"wzq.save1", "wzq.save2", "wzq.save3"};
    dsfapi::TagValue tTagValue[NUM];
    for (int i = 0; i < NUM; ++i)
    {
        std::string sValue = "wzq.value" + std::to_string(i);
        tTagValue[i].ReSet(sValue.data(), sValue.size());
    }
    auto res = dsfapi::Dr_Save(pContext, (const char **)pTagName, tTagValue, NUM);
    LOG_INFO << "Dr_Save:res = " << res << std::endl;
}

void TestRegisterTag_db(dsfapi::DRSdkContext *pContext)
{
    readSqliteDB();
    LOG_INFO << "tagList :size = " << tagList.size();
    auto func1 = [](const int32 nBatchId, dsfapi::TagRecord *pTagRecord, int32 *pErrorCode, const int32 nTagCount) {
        LOG_INFO << "fun_1. count=" << nTagCount << ", batch_id=" << nBatchId << std::endl;
        for (int i = 0; i < nTagCount; i++)
        {
            // LOG_INFO << "fun_1. errorCode:" << pErrorCode[i] << ", tagname:" << pTagRecord[i].tTagName.Buffer()
            //  << ",tagvalue:" << pTagRecord[i].tTagValue.Buffer() << std::endl;
            // short value = *reinterpret_cast<short *>(pTagRecord[i].tTagValue.Buffer());
            // LOG_INFO << "TestRegisterTag.fun_1. value:" << value << std::endl;
        }

        // Must be released
        dsfapi::Free_Int32_Ptr(&pErrorCode);
        dsfapi::Free_Tag_Record(&pTagRecord);
    };

    int32 res = 0;

    char **pName = new char *[tagList.size() + 1];
    for (int i = 0; i < tagList.size(); ++i)
    {
        pName[i] = const_cast<char *>(tagList[i].c_str());

        if (i % 3000 == 2999)
        {
            int32 nBatchId = 0;
            res = dsfapi::DR_Register_Tag(pContext, (const char **)(pName + i - 2999), 3000, 200, func1, 0, &nBatchId);
            LOG_INFO << "dr_async_reg_tag :res = " << res << ", batch_id=" << nBatchId << std::endl;
        }
    }
}
void TestRegisterTag(dsfapi::DRSdkContext *pContext)
{
    auto func1 = [](const int32 nBatchId, dsfapi::TagRecord *pTagRecord, int32 *pErrorCode, const int32 nTagCount) {
        LOG_INFO << "fun_1. count=" << nTagCount << ", batch_id=" << nBatchId << std::endl;
        for (int i = 0; i < nTagCount; i++)
        {
            LOG_INFO << "fun_1. errorCode:" << pErrorCode[i] << ", tagname:" << pTagRecord[i].tTagName.Buffer()
                     << ", value_len:" << pTagRecord[i].tTagValue.Length() << std::endl;
            unsigned char *pBuffer = (unsigned char *)pTagRecord[i].tTagValue.Buffer();
            std::ostringstream oss;
            for (int j = 0; j < pTagRecord[i].tTagValue.Length(); ++j)
            {
                oss << std::hex << std::setw(2) << std::setfill('0') << static_cast<uint32>(pBuffer[j]);
            }
            LOG_INFO << oss.str();
        }

        // Must be released
        dsfapi::Free_Int32_Ptr(&pErrorCode);
        dsfapi::Free_Tag_Record(&pTagRecord);
    };

    // auto func2 = [](const int32 nBatchId, dsfapi::TagRecord *pTagRecord, int32 *pErrorCode, const int32 nTagCount) {
    //     LOG_INFO << "fun_2. count=" << nTagCount << ", batch_id=" << nBatchId << std::endl;
    //     // for (int i = 0; i < nTagCount; i++)
    //     // {
    //     //     LOG_INFO << "fun_2. errorCode:" << pErrorCode[i] << ", tagname:" << pTagRecord[i].tTagName.Buffer()
    //     //              << ",tagvalue:" << pTagRecord[i].tTagValue.Buffer() << std::endl;
    //     // }

    //     // Must be released
    //     dsfapi::Free_Int32_Ptr(&pErrorCode);
    //     dsfapi::Free_Tag_Record(&pTagRecord);
    // };
    int32 res = 0;
    char *pTagName_1[] = {"STD::DS_TINT", "STD::DS_TREAL", "STD::DS_TBOOL", "STD::DS_TSTR"};
    int32 nBatchId1 = 0;
    res = dsfapi::DR_Register_Tag(pContext, (const char **)pTagName_1, 4, 1000, func1, 0, &nBatchId1);
    LOG_INFO << "dr_async_reg_tag 1 :res = " << res << ", batch_id=" << nBatchId1 << std::endl;

    // char *pTagName_2[] = {"STD.TEST_INT", "STD.TEST_UDT_22", "STD.TEST_REAL"};
    // int32 nBatchId2 = 0;
    // res = dsfapi::DR_Register_Tag(pContext, (const char **)pTagName_2, 3, 1200, func2, 1, &nBatchId2);
    // LOG_INFO << "dr_async_reg_tag 2 :res = " << res << ", batch_id=" << nBatchId2 << std::endl;

    // unregister : assuming we want to unregistern BatchId2 after 5s.
    // std::thread([nBatchId2, pContext]() {
    //     std::this_thread::sleep_for(std::chrono::seconds(10));
    //     if (pContext && pContext->ConnectStatus())
    //     {
    //         int32 res = dsfapi::DR_Unregister_Tag(pContext, nBatchId2);
    //         LOG_INFO << "DR_Unregister_Tag  :res = " << res << ", batch_id=" << nBatchId2 << std::endl;
    //     }
    // }).detach();

    // // 批量测试
    // for (int p = 0; p < 6; ++p)
    // {
    //     {
    //         int32 res = 0;
    //         std::string strTagName[500];
    //         char *pTagName_1[500];
    //         for (int i = 0; i < 500; ++i)
    //         {
    //             strTagName[i] = "STD::DS_PERF_INT[" + std::to_string(i + 1) + "]";
    //             pTagName_1[i] = const_cast<char *>(strTagName[i].c_str());
    //         }
    //         int32 nBatchId1 = 0;
    //         res = dsfapi::DR_Register_Tag(pContext, (const char **)pTagName_1, 500, 20, func1, 0, &nBatchId1);
    //         LOG_INFO << "dr_async_reg_tag 1 :res = " << res << ", batch_id=" << nBatchId1 << std::endl;
    //     }
    //     {
    //         int32 res = 0;
    //         std::string strTagName[500];
    //         char *pTagName_1[500];
    //         for (int i = 0; i < 500; ++i)
    //         {
    //             strTagName[i] = "STD::DS_PERF_DINT[" + std::to_string(i + 1) + "]";
    //             pTagName_1[i] = const_cast<char *>(strTagName[i].c_str());
    //         }
    //         int32 nBatchId1 = 0;
    //         res = dsfapi::DR_Register_Tag(pContext, (const char **)pTagName_1, 500, 40, func1, 0, &nBatchId1);
    //         LOG_INFO << "dr_async_reg_tag 1 :res = " << res << ", batch_id=" << nBatchId1 << std::endl;
    //     }
    //     {
    //         int32 res = 0;
    //         std::string strTagName[500];
    //         char *pTagName_1[500];
    //         for (int i = 0; i < 500; ++i)
    //         {
    //             strTagName[i] = "STD::DS_PERF_LINT[" + std::to_string(i + 1) + "]";
    //             pTagName_1[i] = const_cast<char *>(strTagName[i].c_str());
    //         }
    //         int32 nBatchId1 = 0;
    //         res = dsfapi::DR_Register_Tag(pContext, (const char **)pTagName_1, 500, 30, func1, 0, &nBatchId1);
    //         LOG_INFO << "dr_async_reg_tag 1 :res = " << res << ", batch_id=" << nBatchId1 << std::endl;
    //     }
    // }
}

void TestRegisterTagJson(dsfapi::DRSdkContext *pContext)
{
    auto func1 = [](const int32 nBatchId, char *strJson, const int32 nLen) {
        LOG_INFO << "json_fun_1. batch_id=" << nBatchId << ", json:" << strJson << std::endl;

        ConsumeCPU();

        // Must be released
        dsfapi::Free_Str_Ptr(&strJson);
    };
    // auto func2 = [](const int32 nBatchId, char *strJson, const int32 nLen) {
    //     LOG_INFO << "json_fun_2. batch_id=" << nBatchId << ", json:" << strJson << std::endl;

    //     ConsumeCPU(200);

    //     // Must be released
    //     dsfapi::Free_Str_Ptr(&strJson);
    // };

    int32 res = 0;

    char *pTagName_1[] = {"STD::TINT", "STD::TREAL", "STD::TBOOL"};
    int32 nBatchId1 = 0;
    res = dsfapi::DR_Register_Tag_Json(pContext, (const char **)pTagName_1, 3, 2000, func1, 0, &nBatchId1);
    LOG_INFO << "dr_async_reg_tag 1 :res = " << res << ", batch_id=" << nBatchId1 << std::endl;

    // char *pTagName_2[] = {"STD.TEST_INT", "STD.TEST_UDT_22", "STD.TEST_REAL"};
    // int32 nBatchId2 = 0;
    // res = dsfapi::DR_Register_Tag_Json(pContext, (const char **)pTagName_2, 3, 100, func2, 1, &nBatchId2);
    // LOG_INFO << "dr_async_reg_tag 2 :res = " << res << ", batch_id=" << nBatchId2 << std::endl;

    /// unregister : assuming we want to unregistern BatchId2 after 5s.
    // std::thread([nBatchId2, pContext]() {
    //     std::this_thread::sleep_for(std::chrono::seconds(5));
    //     if (pContext && pContext->ConnectStatus())
    //     {
    //         int32 res = dsfapi::DR_Unregister_Tag(pContext, nBatchId2);
    //         LOG_INFO << "DR_Unregister_Tag  :res = " << res << ", batch_id=" << nBatchId2 << std::endl;
    //     }
    // }).detach();
}

void TestReadTagtypeInfo(dsfapi::DRSdkContext *pContext)
{
    const char *pObjectName = "wzq_object1";
    dsfapi::TagtypeInfo *pTagtypeInfo = nullptr;
    int nTagtypeInfoCount = 0;
    auto res = dsfapi::DR_Get_Tagtype_Info(pContext, pObjectName, &pTagtypeInfo, &nTagtypeInfoCount);
    LOG_INFO << "DR_Get_Tagtype_Info:res = " << res << std::endl;
    if (0 == res)
    {
        for (int i = 0; i < nTagtypeInfoCount; i++)
        {
            LOG_INFO << "DR_Get_Tagtype_Info. tagname:" << pTagtypeInfo[i].ToString() << std::endl;
        }
    }

    // Must be released
    dsfapi::Free_Tagtype_Info(&pTagtypeInfo);
}

void TestReadTagValue(dsfapi::DRSdkContext *pContext)
{
    const char *pObjectName = "wzq_object1";
    dsfapi::TagRecord *pTagRecord = nullptr;
    int nTagRecordCount = 0;
    auto res = dsfapi::DR_Get_Tag_Value(pContext, pObjectName, &pTagRecord, &nTagRecordCount);
    LOG_INFO << "DR_Get_Tag_Value:res = " << res << std::endl;
    if (0 == res)
    {
        for (int i = 0; i < nTagRecordCount; i++)
        {
            LOG_INFO << "DR_Get_Tag_Value. tagname:" << pTagRecord[i].tTagName.Buffer()
                     << ",tagvalue:" << pTagRecord[i].tTagValue.Buffer() << std::endl;
        }
    }
    // Must be released
    dsfapi::Free_Tag_Record(&pTagRecord);
}

void TestModelJson(dsfapi::DRSdkContext *pContext)
{
    const char *pObjectName = "wzq_object1";
    char *sModeJsonString = nullptr;
    int nLength = 0;

    auto res = dsfapi::DR_Get_Model_Json(pContext, pObjectName, &sModeJsonString, &nLength);
    LOG_INFO << "DR_Get_Model_Json:res = " << res << ", length = " << nLength << std::endl;
    if (0 == res)
    {
        LOG_INFO << "DR_Get_Model_Json. json string:" << sModeJsonString << std::endl;
    }
    // Must be released
    dsfapi::Free_Str_Ptr(&sModeJsonString);
}

dsfapi::DRSdkContext *TestDrsdkInit()
{
    // 1.should init first
    dsfapi::DRSdkConnectParam tConnectParam;
    strncpy(tConnectParam.szServerIp, "127.0.0.1", sizeof(tConnectParam.szServerIp) - 1);
    tConnectParam.nServerPort = 1234;
    if (g_bReduntancy.load())
    {
        strncpy(tConnectParam.szServerIpBak, "127.0.0.1", sizeof(tConnectParam.szServerIpBak) - 1);
        tConnectParam.nServerPortBak = 2345;
    }
    tConnectParam.nHeartbeatTimeoutMs = 3000;
    tConnectParam.nConnectTimeoutMs = 1000;

    dsfapi::DRSdkOption tOption;
    tOption.nThreadPoolNum = 10; // thread nums for run  register callback
    tOption.nRequestWaitTimeoutMs = 1000;

    dsfapi::DRSdkContext *pContext = nullptr;
    pContext = dsfapi::DR_Init(tConnectParam, tOption);
    if (nullptr == pContext || pContext->errorcode != 0)
    {
        LOG_ERR << "DR_Init failed! errorcode =" << -1 << std::endl;
        dsfapi::Free_DR_Sdk_Context(&pContext);
        return nullptr;
    }
    return pContext;
}

int test()
{
    // 1. should init first
    dsfapi::DRSdkContext *pContext = nullptr;
    pContext = TestDrsdkInit();
    if (nullptr == pContext)
    {
        return -1;
    }

    // 2. using
    // TestRead_loop(pContext);

    while (!g_bStop && !pContext->ConnectStatus())
    {
        LOG_ERR << "dsfapi connect_status is false!" << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(pContext->tDRSdkConnectParam.nConnectTimeoutMs));
        continue;
    }
    TestRegisterTag(pContext);
    // TestRegisterTagJson(pContext);

    // int CNT = 30;
    // while (!g_bStop && --CNT > 0)
    while (!g_bStop)
    { // loop test
        static int cnt1 = 0;
        static int cnt2 = 0;

        // check connect status.
        if (!pContext->ConnectStatus())
        {
            LOG_ERR << "dsfapi connect_status is false!" << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(pContext->tDRSdkConnectParam.nConnectTimeoutMs));
            continue;
        }
        if (++cnt1 % 10 == 0)
        {
            cnt1 = 0;
            // TestRead(pContext);
            // TestWrite(pContext);
            // TestWriteText(pContext);
            // TestSave(pContext);
            // TestModelJson(pContext);
        }
        if (++cnt2 % 5 == 0)
        {
            cnt2 = 0;
            // TestReadTagtypeInfo(pContext);
            // TestReadTagValue(pContext);
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(200));
    }
    // 3. should uninit
    dsfapi::DR_Uninit(pContext);
    // 4. should release
    dsfapi::Free_DR_Sdk_Context(&pContext);
    return 0;
}

void test_loop(int num)
{
    for (int i = 0; i < num; i++)
    {
        test();
    }
}

int test_thread()
{
    // 1. should init first
    dsfapi::DRSdkContext *pContext = nullptr;
    pContext = TestDrsdkInit();
    if (nullptr == pContext)
    {
        return -1;
    }
    while (!g_bStop && !pContext->ConnectStatus())
    {
        LOG_ERR << "dsfapi connect_status is false!" << std::endl;
        std::this_thread::sleep_for(std::chrono::milliseconds(pContext->tDRSdkConnectParam.nConnectTimeoutMs));
        continue;
    }

    // 2. using
    std::vector<std::thread> vecThread;
    vecThread.emplace_back(std::thread(TestRegisterTag, pContext));
    vecThread.emplace_back([pContext]() {
        int CNT = 50;
        while (!g_bStop && --CNT > 0)
        { // loop test
            static int cnt1 = 0;
            if (++cnt1 % 2 == 0)
            {
                cnt1 = 0;
                TestRead(pContext);
                TestWrite(pContext);
                TestSave(pContext);
                TestModelJson(pContext);
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(1200));
        }
    });
    vecThread.emplace_back([pContext]() {
        int CNT = 50;
        while (!g_bStop && --CNT > 0)
        { // loop test
            static int cnt1 = 0;
            if (++cnt1 % 5 == 0)
            {
                cnt1 = 0;
                TestReadTagtypeInfo(pContext);
                TestReadTagValue(pContext);
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(1200));
        }
    });

    for (auto &t : vecThread)
    {
        t.join();
    }
    // 3. should uninit
    dsfapi::DR_Uninit(pContext);
    // 4. should release
    dsfapi::Free_DR_Sdk_Context(&pContext);

    return 0;
}

int main(int args, char *argv[])
{
    std::signal(SIGINT, signalHandler);
    if (args > 1)
    {
        g_bReduntancy.store(true);
    }

    test_loop(1);

    // test_thread();

    return 0;
}