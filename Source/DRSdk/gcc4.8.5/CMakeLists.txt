# 设置最小的 CMake 版本
cmake_minimum_required(VERSION 3.10)
set(CMAKE_CXX_STANDARD 11)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 项目名称
project(DRSDK)

# 设置项目的路径
set(DR_PATH ${CMAKE_SOURCE_DIR}/../../..)
message(STATUS "DR_PATH: ${DR_PATH}")

# 设置编译输出目录
set(CMAKE_BINARY_DIR "${CMAKE_SOURCE_DIR}")
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}")
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}")
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY "${CMAKE_BINARY_DIR}")
message(STATUS "CMAKE_BINARY_DIR: ${CMAKE_BINARY_DIR}")

# 设置 Boost 路径（如有需要，可修改为实际路径）
set(DR_INCLUDE_DIR ${DR_PATH}/include)
set(DR_LIBRARY_DIR ${DR_PATH}/library)
include_directories(${DR_INCLUDE_DIR})
link_directories(${DR_LIBRARY_DIR})

# 包含头文件路径
include_directories(${CMAKE_SOURCE_DIR}/../dsfapi)

# 自定义安装目录
set(INSTALL_LIB_DIR ${CMAKE_SOURCE_DIR}/lib)
set(INSTALL_BIN_DIR ${CMAKE_SOURCE_DIR}/bin)
set(INSTALL_INCLUDE_DIR ${CMAKE_SOURCE_DIR}/include)

# 生成 libdrsdk.so 动态库
add_library(dsfapi SHARED
    ../dsfapi/dsfapi.cpp
    ../dsfapi/drsdk_inner.cpp
    ../dsfapi/drsdk_log.cpp
    ../dsfapi/drsdk_manager.cpp
    ../dsfapi/drsdk_transport_socket.cpp
)

# 链接库
set_target_properties(dsfapi PROPERTIES
    LIBRARY_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}
)
target_link_libraries(dsfapi pthread)

# 创建 drsdk_app 可执行文件
add_executable(drsdk_app
    ../drsdk_app/drsdk_app.cpp
)

# 链接库和依赖
set_target_properties(drsdk_app PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}
)
target_link_libraries(drsdk_app dsfapi pthread boost_system boost_thread sqlite3)

# 创建 drsdk_fake_ds 可执行文件
add_executable(drsdk_fake_ds
    ../dsfapi/drsdk_log.cpp
    ../drsdk_fake_ds/fake_ds.cpp
)

set_target_properties(drsdk_fake_ds PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}
)
target_link_libraries(drsdk_fake_ds pthread boost_system boost_thread)

########################################

# 在编译后拷贝文件（例如 libdrsdk.so, drsdk_app 和 drsdk_fake_ds 到指定目录）
add_custom_command(TARGET dsfapi POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory ${INSTALL_LIB_DIR}
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:dsfapi> ${INSTALL_LIB_DIR}
)

add_custom_command(TARGET drsdk_app POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory ${INSTALL_BIN_DIR}
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:drsdk_app> ${INSTALL_BIN_DIR}
)

add_custom_command(TARGET drsdk_fake_ds POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E make_directory ${INSTALL_BIN_DIR}
    COMMAND ${CMAKE_COMMAND} -E copy $<TARGET_FILE:drsdk_fake_ds> ${INSTALL_BIN_DIR}
)

# 自定义目标：将头文件拷贝到指定目录
add_custom_target(copy_drsdk_headers ALL
    COMMAND ${CMAKE_COMMAND} -E make_directory ${INSTALL_INCLUDE_DIR}
    COMMAND ${CMAKE_COMMAND} -E copy_if_different ${CMAKE_SOURCE_DIR}/../dsfapi/*.h ${INSTALL_INCLUDE_DIR}
)

add_custom_target(copy_type_headers ALL
    COMMAND ${CMAKE_COMMAND} -E make_directory ${INSTALL_INCLUDE_DIR}
    COMMAND ${CMAKE_COMMAND} -E copy_if_different ${DR_INCLUDE_DIR}/data_types.h ${INSTALL_INCLUDE_DIR}
)

add_custom_target(copy_errcode_headers ALL
    COMMAND ${CMAKE_COMMAND} -E make_directory ${INSTALL_INCLUDE_DIR}/errcode
    COMMAND ${CMAKE_COMMAND} -E copy_if_different ${DR_INCLUDE_DIR}/errcode/error_code.h ${INSTALL_INCLUDE_DIR}/errcode/
)

# 在构建完成后执行 tar 命令，打包 include, bin 和 lib 目录
add_custom_target(finish ALL
    COMMENT "finish...."
)
add_dependencies(finish dsfapi drsdk_app drsdk_fake_ds copy_drsdk_headers copy_type_headers copy_errcode_headers)

add_custom_command(TARGET finish POST_BUILD
    # COMMAND ${CMAKE_COMMAND} -E tar "zcvf" ${CMAKE_SOURCE_DIR}/drsdk_rh7.8_gcc4.8.5.tar.gz ${CMAKE_SOURCE_DIR}/include ${CMAKE_SOURCE_DIR}/bin ${CMAKE_SOURCE_DIR}/lib
    COMMAND ${CMAKE_COMMAND} -E chdir ${CMAKE_SOURCE_DIR} tar "zcvf" drsdk_rh7.8_gcc4.8.5.tar.gz include bin lib
)
