/**
 * Filename        drsdk_log.cpp
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description     The logging component used internally in dsfapi
 *
 * Author          wuzheqiang
 * Version         12/19/2024    wuzheqiang    Initial Version
 **************************************************************/

#include "drsdk_log.h"
#include "errcode/error_code.h"

namespace dsfapi
{
Logger::~Logger()
{
    Stop();
}
void Logger::Stop()
{
    m_bStopFlag = true;
    m_CacheQueueCV.notify_all();
    if (m_OutputThread != nullptr && m_OutputThread->joinable())
    {
        m_OutputThread->join();
        m_OutputThread = nullptr;
    }
    if (m_LogStream.is_open())
    {
        m_LogStream.close();
    }
    m_bInitFlag.store(false);
}

int32 Logger::Init(const LogLevel eLogLevel, const OutputMethod eOutPutMethod, const std::string &strLogPath)
{
    m_bInitFlag.store(true);
    m_bStopFlag.store(false);
    m_LogLevel = eLogLevel;
    m_OutputMethod = eOutPutMethod;
    m_strLogFile = strLogPath;
    if (m_strLogFile.empty())
    {
        // m_strLogFile = std::getenv("HOME");
        char cwd[PATH_MAX];
        if (getcwd(cwd, sizeof(cwd)) != nullptr)
        {
            m_strLogFile = std::string(cwd);
        }
        else
        {
            m_strLogFile = ".";
        }

        const char *szEnvValue = std::getenv("DSF_LOG_OUT_PATH");
        if (szEnvValue != nullptr)
        {
            m_strLogFile = std::string(szEnvValue);
        }
    }

    {
        const char *szEnvValue = std::getenv("DSF_LOG_OUT_LEVEL");
        if (szEnvValue != nullptr)
        {
            auto nEnvValue = std::stoi(szEnvValue);
            if (nEnvValue >= LogLevel::DEBUG || nEnvValue <= LogLevel::ERROR)
            {
                m_LogLevel = static_cast<LogLevel>(nEnvValue);
            }
        }
    }
    {
        const char *szEnvValue = std::getenv("DSF_LOG_OUT_MODE");
        if (szEnvValue != nullptr)
        {
            auto nEnvValue = std::stoi(szEnvValue);
            if (nEnvValue >= OutputMethod::NONE || nEnvValue <= OutputMethod::BOTH)
            {
                m_OutputMethod = static_cast<OutputMethod>(nEnvValue);
            }
        }
    }

    if ((m_OutputMethod & OutputMethod::FILE) && !m_strLogFile.empty())
    {
        auto pid = getpid();
        m_strLogFile = m_strLogFile + std::string("/dsfapi_") + CurrentTimestampMicro(1) + "_" + std::to_string(pid) +
                       std::string(".log");
        m_LogStream.open(m_strLogFile, std::ios::out | std::ios::trunc);
        if (!m_LogStream.is_open())
        {
            std::cerr << "Failed to open log file: " << m_strLogFile << std::endl;
            return EC_DSF_SDK_OPEN_FILE_FAILED;
        }
    }
    m_OutputThread.reset(new std::thread(&Logger::LogWorker, this));
}

void Logger::LogWorker()
{
    pthread_setname_np(pthread_self(), "log_worker");
    while (!m_bStopFlag)
    {
        std::unique_lock<std::mutex> lock(m_CacheQueueMutex);
        m_CacheQueueCV.wait(lock, [this]() { return !m_CacheQueue.empty() || m_bStopFlag; });

        while (!m_CacheQueue.empty())
        {
            std::shared_ptr<LogMessage> pLogMessage = m_CacheQueue.front();
            m_CacheQueue.pop();
            pLogMessage->message;
            if (m_OutputMethod & OutputMethod::TERMINAL)
            {
                if (pLogMessage->level >= ERROR)
                {
                    std::cout << RED << pLogMessage->message << RESET << std::endl;
                }
                else
                {
                    std::cout << pLogMessage->message << std::endl;
                }
            }

            if ((m_OutputMethod & OutputMethod::FILE) && m_LogStream.is_open())
            {
                m_LogStream << pLogMessage->message << std::endl;
            }
        }
    }
}

LogLevel Logger::GetLogLevel() const
{
    return m_LogLevel;
}

void Logger::EnqueueMsg(const std::shared_ptr<LogMessage> &pLogMessage)
{
    if (!m_bInitFlag)
    {
        Init(LogLevel::INFO, OutputMethod::TERMINAL, ""); // in order to start the worker thread
    }

    std::lock_guard<std::mutex> lock(m_CacheQueueMutex);
    if (m_CacheQueue.size() > 128)
    {
        std::cerr << "EnqueueMsg failed ,m_CacheQueue size > 128" << std::endl;
        return;
    }
    m_CacheQueue.push(pLogMessage);
    m_CacheQueueCV.notify_all();
}

} // namespace dsfapi
