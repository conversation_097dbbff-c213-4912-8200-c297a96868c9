/*  Filename:    drsdk_manager.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Define DRSdkManager, It is a singleton, managing the processing of data
 *
 *  @author:     wuzheqiang
 *  @version:    09/10/2024	wuzheqiang	Initial Version
 **************************************************************/

#include "drsdk_manager.h"
#include "drsdk_transport_socket.h"
namespace dsfapi
{
std::atomic<uint16> DRSdkManager::m_nBatchId = {1};
constexpr uint16 MIN_INTERVAL_TIME = 10; // ms

class FuncTrace
{
  public:
    std::string func_name_;
    FuncTrace(const char *szFuncName)
    {
        func_name_ = std::string(szFuncName);
        LOG_INFO << func_name_ << " start !!!!";
    }
    ~FuncTrace()
    {
        LOG_INFO << func_name_ << " end !!!!";
    }
};

DRSdkManager::DRSdkManager()
{
    LOG_INFO << "DRSdkManager [this=" << (void *)this << "]";
}

DRSdkManager::~DRSdkManager()
{
    LOG_INFO << "~DRSdkManager [this=" << (void *)this << "]";
    Uninit();
    Logger::GetInstance().Stop();
}

int32 DRSdkManager::Init(const DRSdkConnectParam &tDRSdkConnectParam, const DRSdkOption &tDRSdkOption)
{
    int32 nRet = 0;
    LOG_INFO << " DRSdkManager::Init()  DRSdkConnectParam:" << tDRSdkConnectParam.ToString();
    LOG_INFO << " DRSdkManager::Init()  DRSdkOption:" << tDRSdkOption.ToString();
    m_tDRSdkConnectParam = tDRSdkConnectParam;
    m_tDRSdkOption = tDRSdkOption;

    m_pTransportMain.reset(new DrSdkTransportSocket);
    LOG_INFO << "[this=" << (void *)this << "], [m_pTransportMain=" << (void *)m_pTransportMain.get() << "] ";
    nRet = m_pTransportMain->Init(m_tDRSdkConnectParam);
    if (0 != nRet)
    {
        LOG_ERR << "m_pTransportMain->Init() failed";
        //   return nRet;
    }
    m_pTransportMain->SetConnectedCallback(std::bind(&DRSdkManager::DrAutomaticRegister, this, std::placeholders::_1));

    if (strlen(m_tDRSdkConnectParam.szServerIpBak) > 0 && m_tDRSdkConnectParam.nServerPortBak > 0)
    {
        m_bMultilink.store(true);
        m_pTransportBak.reset(new DrSdkTransportSocket);
        LOG_INFO << "[this=" << (void *)this << "], [m_pTransportBak=" << (void *)m_pTransportMain.get() << "] ";
        nRet = m_pTransportBak->Init(m_tDRSdkConnectParam, false);
        if (0 != nRet)
        {
            LOG_ERR << "m_pTransportBak->Init() failed";
            //   return nRet;
        }
        m_pTransportBak->SetConnectedCallback(
            std::bind(&DRSdkManager::DrAutomaticRegister, this, std::placeholders::_1));
    }

    m_pSendBuffer = std::make_shared<std::vector<char>>();
    m_nSendBufferLen = SEND_BUFFER_MAX_LENGTH;
    m_pSendBuffer->reserve(m_nSendBufferLen);
    if (!m_pSendBuffer)
    {
        LOG_ERR << "m_pSendBuffer is null";
        return EC_DSF_SDK_MALLOC_FAILED;
    }

    m_pRecvThread.reset(
        new std::thread(&DRSdkManager::RecvThreadCallback, this, m_pTransportMain, std::string("[main]")));
    pthread_t thread = m_pRecvThread->native_handle();
    pthread_setname_np(thread, "recv");

    if (m_bMultilink)
    {
        m_pRecvThreadBak.reset(
            new std::thread(&DRSdkManager::RecvThreadCallback, this, m_pTransportBak, std::string("[bak]")));
        pthread_t thread = m_pRecvThreadBak->native_handle();
        pthread_setname_np(thread, "recv_bak");
    }

    // m_pThreadPool = std::make_unique<ThreadPool>(m_tDRSdkOption.nThreadPoolNum);
    m_pThreadPool.reset(new ThreadPool(m_tDRSdkOption.nThreadPoolNum));

    return EC_DSF_SDK_SUCCESS;
}

int32 DRSdkManager::Uninit()
{
    m_bStop.store(true);
    if (nullptr != m_pTransportMain)
    {
        m_pTransportMain->Stop();
    }
    if (nullptr != m_pTransportBak)
    {
        m_pTransportBak->Stop();
    }
    {
        std::lock_guard<std::mutex> lg(m_RequestMtx);
        m_mapRequest.clear();
    }
    if (m_pRecvThread && m_pRecvThread->joinable())
    {
        m_pRecvThread->join();
        m_pRecvThread = nullptr;
    }
    if (m_pRecvThreadBak && m_pRecvThreadBak->joinable())
    {
        m_pRecvThreadBak->join();
        m_pRecvThreadBak = nullptr;
    }

    LOG_INFO << "Uninit [this=" << (void *)this << "]";

    return EC_DSF_SDK_SUCCESS;
}

bool DRSdkManager::DrConnectStatus()
{
    if (nullptr != m_pTransport)
    {
        return true; // if m_pTransport is not null, it means that the connection server is selected
    }
    else
    {
        // return false; 
        // if m_pTransport is null, it means that the connection server has not been selected
        return EC_DSF_SDK_SUCCESS == CheckTransportAvailable();
    }
}

int32 DRSdkManager::DrWrite(const char *pTagName[], const TagValue *const pTagValue, const int32 nTagCount)
{
    FuncTrace ft(__FUNCTION__);
    std::string strContent = "";
    if (0 == nTagCount)
    {
        LOG_ERR << "params nTagCount == 0:" << nTagCount;
        return EC_DSF_SDK_TAGS_COUNT_ZERO;
    }
    if (nullptr == pTagName || nullptr == pTagValue)
    {
        LOG_ERR << "pTagName is null or pTagValue is nullptr";
        return EC_DSF_SDK_PARAMS_NULLPTR;
    }

    for (int32 i = 0; i < nTagCount; ++i)
    {
        if (nullptr == pTagName[i] || pTagValue[i].Buffer() == nullptr)
        {
            LOG_ERR << "pTagName[i] is null or pTagValue[i].Buffer() is null, i = " << i;
            return EC_DSF_SDK_INCORRECT_INPUT_PARAMS;
        }
        if (!PackItem(&strContent, pTagName[i], strlen(pTagName[i])))
        {
            LOG_ERR << "PackItem failed  pTagName= " << pTagName[i];
            return EC_DSF_SDK_PACKITEM_NAME_FAILED;
        }
        if (!PackItem(&strContent, pTagValue[i].Buffer(), pTagValue[i].Length(), false))
        {
            LOG_ERR << "PackItem failed i = " << i << ",pTagName= " << pTagName[i];
            return EC_DSF_SDK_PACKITEM_VALUE_FAILED;
        }
    }
    DataHeader tHead;
    tHead.nType = SDK_CONTROL_DATA;
    tHead.nLen = strContent.size();
    tHead.nSeq = m_nHeadSeq.fetch_add(1);

    int32 nRet = WriteToSendBuffer(tHead, strContent, strContent.size());
    if (0 != nRet)
    {
        LOG_ERR << "WriteToSendBuffer failed. nRet = " << nRet;
        return nRet;
    }
    strContent.clear();
    nRet = AsyncGetResult(tHead, &strContent);
    if (0 != nRet)
    {
        LOG_ERR << "AsyncGetResult failed. nRet = " << nRet;
        return nRet;
    }
    DataHeader *pHead = reinterpret_cast<DataHeader *>(const_cast<char *>(strContent.c_str()));
    if (pHead->nFlag != 0)
    {
        LOG_ERR << "pHead->nFlag = " << static_cast<int32>(pHead->nFlag);
        return static_cast<int32>(pHead->nFlag);
    }
    return EC_DSF_SDK_SUCCESS;
}

int32 DRSdkManager::DrWriteText(const char *pTagName[], const char *pTagValue[], const int32 nTagCount)
{
    FuncTrace ft(__FUNCTION__);
    std::string strContent = "";
    if (0 == nTagCount)
    {
        LOG_ERR << "params nTagCount == 0:" << nTagCount;
        return EC_DSF_SDK_TAGS_COUNT_ZERO;
    }
    if (nullptr == pTagName || nullptr == pTagValue)
    {
        LOG_ERR << "pTagName is null or pTagValue is nullptr";
        return EC_DSF_SDK_PARAMS_NULLPTR;
    }

    for (int32 i = 0; i < nTagCount; ++i)
    {
        if (nullptr == pTagName[i] || nullptr == pTagValue[i] || strlen(pTagValue[i]) > 256)
        {
            LOG_ERR << "pTagName[i]/pTagValue[i] is null or pTagValue[i] length more than 256, i = " << i;
            return EC_DSF_SDK_INCORRECT_INPUT_PARAMS;
        }
        if (!PackItem(&strContent, pTagName[i], strlen(pTagName[i])))
        {
            LOG_ERR << "PackItem failed  pTagName= " << pTagName[i];
            return EC_DSF_SDK_PACKITEM_NAME_FAILED;
        }
        if (!PackItem(&strContent, pTagValue[i], strlen(pTagValue[i]), false))
        {
            LOG_ERR << "PackItem failed i = " << i << ",pTagName= " << pTagName[i];
            return EC_DSF_SDK_PACKITEM_VALUE_FAILED;
        }
    }
    DataHeader tHead;
    tHead.nType = SDK_WRITE_TEXT_DATA;
    tHead.nLen = strContent.size();
    tHead.nSeq = m_nHeadSeq.fetch_add(1);

    int32 nRet = WriteToSendBuffer(tHead, strContent, strContent.size());
    if (0 != nRet)
    {
        LOG_ERR << "WriteToSendBuffer failed. nRet = " << nRet;
        return nRet;
    }
    strContent.clear();
    nRet = AsyncGetResult(tHead, &strContent);
    if (0 != nRet)
    {
        LOG_ERR << "AsyncGetResult failed. nRet = " << nRet;
        return nRet;
    }
    DataHeader *pHead = reinterpret_cast<DataHeader *>(const_cast<char *>(strContent.c_str()));
    if (pHead->nFlag != 0)
    {
        LOG_ERR << "pHead->nFlag = " << static_cast<int32>(pHead->nFlag);
        return static_cast<int32>(pHead->nFlag);
    }
    return EC_DSF_SDK_SUCCESS;
}

int32 DRSdkManager::DrSave(const char *pTagName[], const TagValue *const pTagValue, const int32 nTagCount)
{
    FuncTrace ft(__FUNCTION__);
    std::string strContent = "";
    if (0 == nTagCount)
    {
        LOG_ERR << "params nTagCount == 0:" << nTagCount;
        return EC_DSF_SDK_TAGS_COUNT_ZERO;
    }
    if (nullptr == pTagName || nullptr == pTagValue)
    {
        LOG_ERR << "pTagName is null or pTagValue is nullptr";
        return EC_DSF_SDK_PARAMS_NULLPTR;
    }

    for (int i = 0; i < nTagCount; ++i)
    {
        if (nullptr == pTagName[i] || pTagValue[i].Buffer() == nullptr)
        {
            LOG_ERR << "pTagName[i] is null or pTagValue[i].Buffer() is null, i = " << i;
            return EC_DSF_SDK_INCORRECT_INPUT_PARAMS;
        }
        if (!PackItem(&strContent, pTagName[i], strlen(pTagName[i])))
        {
            LOG_ERR << "PackItem failed  pTagName= " << pTagName[i];
            return EC_DSF_SDK_PACKITEM_NAME_FAILED;
        }
        if (!PackItem(&strContent, pTagValue[i].Buffer(), pTagValue[i].Length(), false))
        {
            LOG_ERR << "PackItem failed i = " << i << ",pTagName= " << pTagName[i];
            return EC_DSF_SDK_PACKITEM_VALUE_FAILED;
        }
    }
    DataHeader tHead;
    tHead.nType = SDK_DUMP_DATA;
    tHead.nLen = strContent.size();
    tHead.nSeq = m_nHeadSeq.fetch_add(1);

    int32 nRet = WriteToSendBuffer(tHead, strContent, strContent.size());
    if (0 != nRet)
    {
        LOG_ERR << "WriteToSendBuffer failed. nRet = " << nRet;
        return nRet;
    }
    strContent.clear();
    nRet = AsyncGetResult(tHead, &strContent);
    if (0 != nRet)
    {
        LOG_ERR << "AsyncGetResult failed. nRet = " << nRet;
        return nRet;
    }
    DataHeader *pHead = reinterpret_cast<DataHeader *>(const_cast<char *>(strContent.c_str()));
    if (pHead->nFlag != 0)
    {
        LOG_ERR << "pHead->nFlag = " << static_cast<int32>(pHead->nFlag);
        return static_cast<int32>(pHead->nFlag);
    }
    return EC_DSF_SDK_SUCCESS;
}

int32 DRSdkManager::DrRead(const char *pTagName[], const int32 nTagNameCount, TagValue **pTagValue, int32 **pErrorCode,
                           int32 *pTagValueCount)
{
    FuncTrace ft(__FUNCTION__);
    if (0 == nTagNameCount)
    {
        LOG_ERR << "params nTagCount == 0:" << nTagNameCount;
        return EC_DSF_SDK_TAGS_COUNT_ZERO;
    }
    if (nullptr == pTagName || nullptr == pTagValue || nullptr == pTagValueCount)
    {
        LOG_ERR << "pTagName is nullptr or pTagValueCount is nullptr";
        return EC_DSF_SDK_PARAMS_NULLPTR;
    }
    std::string strContent = "";

    for (int i = 0; i < nTagNameCount; ++i)
    {
        if (nullptr == pTagName[i])
        {
            LOG_ERR << "pTagName[i] is nullptr, i = " << i;
            return EC_DSF_SDK_INCORRECT_INPUT_PARAMS;
        }
        if (!PackItem(&strContent, pTagName[i], strlen(pTagName[i])))
        {
            LOG_ERR << "PackItem failed  pTagName= " << pTagName[i];
            return EC_DSF_SDK_PACKITEM_NAME_FAILED;
        }
    }

    DataHeader tHead;
    tHead.nType = SDK_READ_DATA;
    tHead.nLen = strContent.size();
    tHead.nSeq = m_nHeadSeq.fetch_add(1);

    int32 nRet = WriteToSendBuffer(tHead, strContent, strContent.size());
    if (0 != nRet)
    {
        LOG_ERR << "WriteToSendBuffer failed. nRet = " << nRet;
        return nRet;
    }
    strContent.clear();
    nRet = AsyncGetResult(tHead, &strContent);
    if (0 != nRet)
    {
        LOG_ERR << "AsyncGetResult failed. nRet = " << nRet;
        return nRet;
    }
    DataHeader *pHead = reinterpret_cast<DataHeader *>(const_cast<char *>(strContent.c_str()));
    if (pHead->nFlag != 0)
    {
        LOG_ERR << "pHead->nFlag = " << static_cast<int32>(pHead->nFlag);
        return static_cast<int32>(pHead->nFlag);
    }

    std::vector<dsfapi::TagValue> vecValue;
    auto bRes = dsfapi::UnPackBuffer((char *)(pHead + 1), pHead->nLen, &vecValue);
    if (!bRes || 0 == vecValue.size() || 0 != vecValue.size() % 2)
    {
        LOG_ERR << "UnPackBuffer failed,bRes=" << bRes << ",  vecValue.size()=" << vecValue.size();
        return EC_DSF_SDK_COMMON_ERROR;
    }

    int32 nCount = vecValue.size() / 2;
    *pTagValue = new TagValue[nCount];
    *pErrorCode = new int32[nCount];
    // TODO  pErrorCode
    for (int i = 0; i < vecValue.size(); i += 2)
    {
        (*pTagValue)[i / 2] = std::move(vecValue[i]);
        (*pErrorCode)[i / 2] = *reinterpret_cast<int32 *>(vecValue[i + 1].Buffer());
    }

    *pTagValueCount = nCount;
    LOG_INFO << " DrRead nTagNameCount:" << nTagNameCount << ", pTagValueCount:" << *pTagValueCount;
    return nTagNameCount == *pTagValueCount ? EC_DSF_SDK_SUCCESS : EC_DSF_SDK_COMMON_ERROR;
}

int32 DRSdkManager::DrRegisterTag(const char *pTagName[], const int32 nTagCount, const uint16 nIntervalMs,
                                  const Callback &pCallBack, const uint8 nSendFlag, int32 *pBatchId)
{
    FuncTrace ft(__FUNCTION__);
    if (0 == nTagCount)
    {
        LOG_ERR << "params nTagCount == 0:" << nTagCount;
        return EC_DSF_SDK_TAGS_COUNT_ZERO;
    }
    if (nullptr == pTagName)
    {
        LOG_ERR << "pTagName is nullptr";
        return EC_DSF_SDK_PARAMS_NULLPTR;
    }
    if (nIntervalMs < MIN_INTERVAL_TIME)
    {
        LOG_ERR << "params nIntervalMs <  MIN_INTERVAL_TIME(10):" << nIntervalMs;
        return EC_DSF_SDK_INTERVAL_TOO_SMALL;
    }

    uint16 nBatchId = m_nBatchId.fetch_add(1);
    std::string strContent = "";
    for (int i = 0; i < nTagCount; ++i)
    {
        if (nullptr == pTagName[i])
        {
            LOG_ERR << "pTagName[i] is null, i = " << i;
            return EC_DSF_SDK_INCORRECT_INPUT_PARAMS;
        }
        if (!PackItem(&strContent, pTagName[i], strlen(pTagName[i])))
        {
            LOG_ERR << "PackItem failed  pTagName= " << pTagName[i];
            return EC_DSF_SDK_PACKITEM_NAME_FAILED;
        }
    }
    RegInfo regInfo{strContent, nIntervalMs, nSendFlag, pCallBack};

    DataHeader tHead;
    tHead.nType = SDK_REG_TAG;
    tHead.nLen = strContent.size();
    tHead.nSeq = m_nHeadSeq.fetch_add(1);
    tHead.nFlagReserve1 = nSendFlag;
    tHead.nReserve1 = nBatchId;
    tHead.nReserve2 = nIntervalMs;
    int32 nRet = WriteToSendBuffer(tHead, strContent, strContent.size());
    if (0 != nRet)
    {
        LOG_ERR << "WriteToSendBuffer failed. nRet = " << nRet;
        return nRet;
    }
    LOG_INFO << "DrRegisterTag send tHead: " << tHead.ToString() << ", content: " << strContent;

    strContent.clear();
    nRet = AsyncGetResult(tHead, &strContent);
    if (0 != nRet)
    {
        LOG_ERR << "AsyncGetResult failed. nRet = " << nRet;
        return nRet;
    }
    DataHeader *pHead = reinterpret_cast<DataHeader *>(const_cast<char *>(strContent.c_str()));
    if (pHead->nFlag != 0)
    {
        LOG_ERR << "pHead->nFlag = " << static_cast<int32>(pHead->nFlag);
        return static_cast<int32>(pHead->nFlag);
    }

    {
        std::lock_guard<std::mutex> lock(m_CallbacksMtx);
        m_mapRegInfos.emplace(static_cast<int32>(nBatchId), std::move(regInfo));
    }

    if (m_bMultilink.load())
    {
        LOG_INFO << "SyncRegister, nBatchId:" << nBatchId;
        SyncRegister(nBatchId);
    }
    *pBatchId = static_cast<int32>(nBatchId); // output batchId
    return EC_DSF_SDK_SUCCESS;
}

int32 DRSdkManager::DrRegisterTagJson(const char *pTagName[], const int32 nTagCount, const uint16 nIntervalMs,
                                      const JsonCallback &pJsonCallBack, const uint8 nSendFlag, int32 *pBatchId)
{
    FuncTrace ft(__FUNCTION__);
    if (0 == nTagCount)
    {
        LOG_ERR << "params nTagCount == 0:" << nTagCount;
        return EC_DSF_SDK_TAGS_COUNT_ZERO;
    }
    if (nullptr == pTagName)
    {
        LOG_ERR << "pTagName is nullptr";
        return EC_DSF_SDK_PARAMS_NULLPTR;
    }
    if (nIntervalMs < MIN_INTERVAL_TIME)
    {
        LOG_ERR << "params nIntervalMs <  MIN_INTERVAL_TIME(10):" << nIntervalMs;
        return EC_DSF_SDK_INTERVAL_TOO_SMALL;
    }

    uint16 nBatchId = m_nBatchId.fetch_add(1);
    std::string strContent = "";
    for (int i = 0; i < nTagCount; ++i)
    {
        if (nullptr == pTagName[i])
        {
            LOG_ERR << "pTagName[i] is null, i = " << i;
            return EC_DSF_SDK_INCORRECT_INPUT_PARAMS;
        }
        if (!PackItem(&strContent, pTagName[i], strlen(pTagName[i])))
        {
            LOG_ERR << "PackItem failed  pTagName= " << pTagName[i];
            return EC_DSF_SDK_PACKITEM_NAME_FAILED;
        }
    }
    RegInfo regInfo{strContent, nIntervalMs, nSendFlag, pJsonCallBack};

    DataHeader tHead;
    tHead.nType = SDK_REG_OBJ_JSON;
    tHead.nLen = strContent.size();
    tHead.nSeq = m_nHeadSeq.fetch_add(1);
    tHead.nFlagReserve1 = nSendFlag;
    tHead.nReserve1 = nBatchId;
    tHead.nReserve2 = nIntervalMs;
    int32 nRet = WriteToSendBuffer(tHead, strContent, strContent.size());
    if (0 != nRet)
    {
        LOG_ERR << "WriteToSendBuffer failed. nRet = " << nRet;
        return nRet;
    }
    LOG_INFO << "DrRegisterTag send tHead: " << tHead.ToString() << ", content: " << strContent;

    strContent.clear();
    nRet = AsyncGetResult(tHead, &strContent);
    if (0 != nRet)
    {
        LOG_ERR << "AsyncGetResult failed. nRet = " << nRet;
        return nRet;
    }
    DataHeader *pHead = reinterpret_cast<DataHeader *>(const_cast<char *>(strContent.c_str()));
    if (pHead->nFlag != 0)
    {
        LOG_ERR << "pHead->nFlag = " << static_cast<int32>(pHead->nFlag);
        return static_cast<int32>(pHead->nFlag);
    }

    {
        std::lock_guard<std::mutex> lock(m_CallbacksMtx);
        m_mapRegInfos.emplace(static_cast<int32>(nBatchId), std::move(regInfo));
    }
    if (m_bMultilink.load())
    {
        LOG_INFO << "SyncRegister, nBatchId:" << nBatchId;
        SyncRegister(nBatchId);
    }
    *pBatchId = static_cast<int32>(nBatchId); // output batchId
    return EC_DSF_SDK_SUCCESS;
}

int32 DRSdkManager::DrUnregisterTag(const int32 nBatchId)
{
    FuncTrace ft(__FUNCTION__);
    if (0 == nBatchId)
    {
        LOG_ERR << "nBatchId is 0.";
        return EC_DSF_SDK_INCORRECT_INPUT_PARAMS;
    }
    // should check nbatchid is exists in  m_mapRegInfos???

    std::string strContent = "";
    DataHeader tHead;
    tHead.nType = SDK_UNREG_TAG;
    tHead.nLen = 0;
    tHead.nSeq = m_nHeadSeq.fetch_add(1);
    tHead.nReserve1 = static_cast<uint16>(nBatchId);
    tHead.nReserve2 = 0;
    int32 nRet = WriteToSendBuffer(tHead, strContent, strContent.size());
    if (0 != nRet)
    {
        LOG_ERR << "WriteToSendBuffer failed. nRet = " << nRet;
        return nRet;
    }
    LOG_INFO << "DrUnregisterTag send tHead: " << tHead.ToString() << ", content: " << strContent;

    strContent.clear();
    nRet = AsyncGetResult(tHead, &strContent);
    if (0 != nRet)
    {
        LOG_ERR << "AsyncGetResult failed. nRet = " << nRet;
        return nRet;
    }
    DataHeader *pHead = reinterpret_cast<DataHeader *>(const_cast<char *>(strContent.c_str()));
    if (pHead->nFlag != 0)
    {
        LOG_ERR << "pHead->nFlag = " << static_cast<int32>(pHead->nFlag);
        return static_cast<int32>(pHead->nFlag);
    }

    {
        std::lock_guard<std::mutex> lock(m_CallbacksMtx);
        if (m_mapRegInfos.find(nBatchId) != m_mapRegInfos.end())
        {
            m_mapRegInfos.erase(nBatchId);
        }
    }
    if (m_bMultilink.load())
    {
        LOG_INFO << "SyncUnregister, nBatchId:" << nBatchId;
        SyncUnregister(nBatchId);
    }
    return EC_DSF_SDK_SUCCESS;
}
int32 DRSdkManager::DrAutoUnregisterTag(std::shared_ptr<DrSdkTransport> pTransport, const uint16 nBatchId)
{
    FuncTrace ft(__FUNCTION__);
    if (0 == nBatchId)
    {
        LOG_ERR << "nBatchId is 0.";
        return EC_DSF_SDK_INCORRECT_INPUT_PARAMS;
    }

    std::string strContent = "";
    DataHeader tHead;
    tHead.nType = SDK_UNREG_TAG;
    tHead.nLen = 0;
    tHead.nSeq = m_nHeadSeq.fetch_add(1);
    tHead.nReserve1 = static_cast<uint16>(nBatchId);
    tHead.nReserve2 = 0;
    int32 nRet = WriteToSendBufferForTransport(pTransport, tHead, strContent, strContent.size());
    if (0 != nRet)
    {
        LOG_ERR << "WriteToSendBufferForTransport failed. nRet = " << nRet;
        return nRet;
    }
    LOG_INFO << "send tHead: " << tHead.ToString() << ", content: " << strContent;

    strContent.clear();
    nRet = AsyncGetResult(tHead, &strContent);
    if (0 != nRet)
    {
        LOG_ERR << "AsyncGetResult failed. nRet = " << nRet;
        return nRet;
    }
    DataHeader *pHead = reinterpret_cast<DataHeader *>(const_cast<char *>(strContent.c_str()));
    if (pHead->nFlag != 0)
    {
        LOG_ERR << "pHead->nFlag = " << static_cast<int32>(pHead->nFlag);
        return static_cast<int32>(pHead->nFlag);
    }

    return EC_DSF_SDK_SUCCESS;
}
int32 DRSdkManager::DrAutoRegisterTag(std::shared_ptr<DrSdkTransport> pTransport, const uint16 nBatchId,
                                      const RegInfo &tRegInfo)
{
    FuncTrace ft(__FUNCTION__);
    DataHeader tHead;
    tHead.nType = tRegInfo.nRegType;
    tHead.nLen = tRegInfo.strRegContent.size();
    tHead.nSeq = m_nHeadSeq.fetch_add(1);
    tHead.nFlagReserve1 = tRegInfo.nSendFlag;
    tHead.nReserve1 = nBatchId;
    tHead.nReserve2 = tRegInfo.nInterval;

    int32 nRet =
        WriteToSendBufferForTransport(pTransport, tHead, tRegInfo.strRegContent, tRegInfo.strRegContent.size());
    if (0 != nRet)
    {
        LOG_ERR << "WriteToSendBufferForTransport failed. nRet = " << nRet;
        return nRet;
    }
    LOG_INFO << "send tHead: " << tHead.ToString() << ", content: " << tRegInfo.strRegContent;

    std::string strContent = "";
    nRet = AsyncGetResult(tHead, &strContent);
    if (0 != nRet)
    {
        LOG_ERR << "AsyncGetResult failed. nRet = " << nRet;
        return nRet;
    }
    DataHeader *pHead = reinterpret_cast<DataHeader *>(const_cast<char *>(strContent.c_str()));
    if (pHead->nFlag != 0)
    {
        LOG_ERR << "pHead->nFlag = " << static_cast<int32>(pHead->nFlag);
        return static_cast<int32>(pHead->nFlag);
    }
    return EC_DSF_SDK_SUCCESS;
}

void DRSdkManager::DrAutomaticRegister(std::shared_ptr<DrSdkTransport> pTransport)
{
    FuncTrace ft(__FUNCTION__);
    // Cannot block, otherwise it will affect io_service
    std::thread([this, pTransport]() {
        std::lock_guard<std::mutex> lock(m_CallbacksMtx);
        for (auto &item : m_mapRegInfos)
        {
            DrAutoRegisterTag(pTransport, item.first, item.second);
        }
    }).detach();
}

void DRSdkManager::SyncRegister(const uint16 nBatchId)
{
    // Cannot block, otherwise it will affect io_service
    std::thread([this, nBatchId]() {
        std::shared_ptr<DrSdkTransport> pTransport = nullptr;

        if (CS_MAIN == m_ConnServerType)
        {
            pTransport = m_pTransportBak; // the other
        }
        else if (CS_BAK == m_ConnServerType)
        {
            pTransport = m_pTransportMain;
        }
        if (nullptr == pTransport)
            return;
        // std::lock_guard<std::mutex> lock(m_CallbacksMtx);
        auto iter = m_mapRegInfos.find(nBatchId);
        if (iter != m_mapRegInfos.end())
        {
            DrAutoRegisterTag(pTransport, iter->first, iter->second);
        }
    }).detach();
}

void DRSdkManager::SyncUnregister(const uint16 nBatchId)
{
    // Cannot block, otherwise it will affect io_service
    std::thread([this, nBatchId]() {
        std::shared_ptr<DrSdkTransport> pTransport = nullptr;

        if (CS_MAIN == m_ConnServerType)
        {
            pTransport = m_pTransportBak; // the other
        }
        else if (CS_BAK == m_ConnServerType)
        {
            pTransport = m_pTransportMain;
        }
        if (nullptr == pTransport)
            return;

        DrAutoUnregisterTag(pTransport, nBatchId);
    }).detach();
}

int32 DRSdkManager::DrGetTagtypeInfo(const char *const pObjectName, TagtypeInfo **pTagtypeInfo,
                                     int32 *pTagtypeInfoCount)
{
    FuncTrace ft(__FUNCTION__);
    if (nullptr == pObjectName || nullptr == pTagtypeInfo || nullptr == pTagtypeInfoCount)
    {
        LOG_ERR << "pObjectName is nullptr.";
        return EC_DSF_SDK_PARAMS_NULLPTR;
    }

    std::string strContent = "";
    if (!PackItem(&strContent, pObjectName, strlen(pObjectName)))
    {
        LOG_ERR << "PackItem failed  pObjectName= " << pObjectName;
        return EC_DSF_SDK_PACKITEM_NAME_FAILED;
    }
    DataHeader tHead;
    tHead.nType = SDK_READ_ATTR;
    tHead.nLen = strContent.size();
    tHead.nSeq = m_nHeadSeq.fetch_add(1);

    int32 nRet = WriteToSendBuffer(tHead, strContent, strContent.size());
    if (0 != nRet)
    {
        LOG_ERR << "WriteToSendBuffer failed. nRet = " << nRet;
        return nRet;
    }
    strContent.clear();
    nRet = AsyncGetResult(tHead, &strContent);
    if (0 != nRet)
    {
        LOG_ERR << "AsyncGetResult failed. nRet = " << nRet;
        return nRet;
    }
    DataHeader *pHead = reinterpret_cast<DataHeader *>(const_cast<char *>(strContent.c_str()));
    if (pHead->nFlag != 0)
    {
        LOG_ERR << "pHead->nFlag = " << static_cast<int32>(pHead->nFlag);
        return static_cast<int32>(pHead->nFlag);
    }
    if (0 == pHead->nLen || 0 != pHead->nLen % sizeof(TagtypeInfo))
    {
        LOG_ERR << "illegal pHead->nLen = " << static_cast<int32>(pHead->nLen);
        return EC_DSF_SDK_COMMON_ERROR;
    }

    auto nAttrSize = pHead->nLen / sizeof(TagtypeInfo);
    *pTagtypeInfo = new TagtypeInfo[nAttrSize];
    *pTagtypeInfoCount = nAttrSize;

    for (int i = 0; i < nAttrSize; ++i)
    {
        memcpy(&((*pTagtypeInfo)[i]), strContent.c_str() + sizeof(DataHeader) + i * sizeof(TagtypeInfo),
               sizeof(TagtypeInfo));
    }

    LOG_INFO << pObjectName << " Attr Size :" << nAttrSize;
    return EC_DSF_SDK_SUCCESS;
}

int32 DRSdkManager::DrGetTagtypeValue(const char *const pObjectName, TagRecord **pTagRecord, int32 *pTagRecordCount)
{
    FuncTrace ft(__FUNCTION__);
    if (nullptr == pObjectName || nullptr == pTagRecord || nullptr == pTagRecordCount)
    {
        LOG_ERR << "pObjectName is nullptr or pTagRecord is nullptr or pTagRecordCount is nullptr .";
        return EC_DSF_SDK_PARAMS_NULLPTR;
    }

    std::string strContent = "";
    if (!PackItem(&strContent, pObjectName, strlen(pObjectName)))
    {
        LOG_ERR << "PackItem failed  pObjectName= " << pObjectName;
        return EC_DSF_SDK_PACKITEM_NAME_FAILED;
    }
    DataHeader tHead;
    tHead.nType = SDK_READ_ATTR_DATA;
    tHead.nLen = strContent.size();
    tHead.nSeq = m_nHeadSeq.fetch_add(1);

    int32 nRet = WriteToSendBuffer(tHead, strContent, strContent.size());
    if (0 != nRet)
    {
        LOG_ERR << "WriteToSendBuffer failed. nRet = " << nRet;
        return nRet;
    }
    strContent.clear();
    nRet = AsyncGetResult(tHead, &strContent);
    if (0 != nRet)
    {
        LOG_ERR << "AsyncGetResult failed. nRet = " << nRet;
        return nRet;
    }
    DataHeader *pHead = reinterpret_cast<DataHeader *>(const_cast<char *>(strContent.c_str()));
    if (pHead->nFlag != 0)
    {
        LOG_ERR << "pHead->nFlag = " << static_cast<int32>(pHead->nFlag);
        return static_cast<int32>(pHead->nFlag);
    }
    std::vector<dsfapi::TagValue> vecAttrs;
    auto bRes = dsfapi::UnPackBuffer(reinterpret_cast<char *>(pHead + 1), pHead->nLen, &vecAttrs);
    if (!bRes || 0 != vecAttrs.size() % 2)
    {
        LOG_ERR << "UnPackBuffer failed,bRes=" << bRes << ",  vecValue.size()=" << vecAttrs.size();
        return EC_DSF_SDK_COMMON_ERROR;
    }

    auto nAttrSize = vecAttrs.size();
    LOG_INFO << pObjectName << " Attr Size :" << nAttrSize;
    *pTagRecord = new TagRecord[nAttrSize / 2];
    *pTagRecordCount = nAttrSize / 2;
    for (int i = 0; i < nAttrSize; i += 2)
    {
        (*pTagRecord)[i / 2].tTagName = std::move(vecAttrs[i]);
        (*pTagRecord)[i / 2].tTagValue = std::move(vecAttrs[i + 1]);
    }
    return EC_DSF_SDK_SUCCESS;
}

int32 DRSdkManager::DrGetModelJson(const char *const pObjectName, char **sModeJsonString, int32 *nLength)
{
    FuncTrace ft(__FUNCTION__);
    if (nullptr == pObjectName || nullptr == sModeJsonString || nullptr == nLength)
    {
        LOG_ERR << "pObjectName is nullptr or sModeJsonString is nullptr or nLength is nullptr .";
        return EC_DSF_SDK_PARAMS_NULLPTR;
    }

    std::string strContent = "";
    if (!PackItem(&strContent, pObjectName, strlen(pObjectName)))
    {
        LOG_ERR << "PackItem failed  pObjectName= " << pObjectName;
        return EC_DSF_SDK_PACKITEM_NAME_FAILED;
    }
    DataHeader tHead;
    tHead.nType = SDK_READ_MODEL_JSON;
    tHead.nLen = strContent.size();
    tHead.nSeq = m_nHeadSeq.fetch_add(1);

    int32 nRet = WriteToSendBuffer(tHead, strContent, strContent.size());
    if (0 != nRet)
    {
        LOG_ERR << "WriteToSendBuffer failed. nRet = " << nRet;
        return nRet;
    }
    strContent.clear();
    nRet = AsyncGetResult(tHead, &strContent);
    if (0 != nRet)
    {
        LOG_ERR << "AsyncGetResult failed. nRet = " << nRet;
        return nRet;
    }
    DataHeader *pHead = reinterpret_cast<DataHeader *>(const_cast<char *>(strContent.c_str()));
    if (pHead->nFlag != 0)
    {
        LOG_ERR << "pHead->nFlag = " << static_cast<int32>(pHead->nFlag);
        return static_cast<int32>(pHead->nFlag);
    }

    *nLength = pHead->nLen;
    *sModeJsonString = new char[pHead->nLen];
    memcpy(*sModeJsonString, strContent.c_str() + sizeof(DataHeader), pHead->nLen);
    return EC_DSF_SDK_SUCCESS;
}

int32 DRSdkManager::WriteToSendBuffer(const DataHeader &tHead, const std::string &strContent, const size_t nLen)
{
    int32 nRet = 0;
    nRet = CheckTransportAvailable();
    if (EC_DSF_SDK_SUCCESS != nRet)
    {
        LOG_ERR << "[this=" << (void *)this << "]CheckTransportAvailable check failed, nRet = " << nRet;
        return nRet;
    }
    return WriteToSendBufferForTransport(m_pTransport, tHead, strContent, nLen);
}

int32 DRSdkManager::WriteToSendBufferForTransport(std::shared_ptr<DrSdkTransport> pTransport, const DataHeader &tHead,
                                                  const std::string &strContent, const size_t nLen)
{
    // use pTransport to send data
    int32 nRet = 0;
    if (nullptr == pTransport)
    {
        LOG_ERR << "pTransport is nullptr";
        return EC_DSF_SDK_NULLPTR;
    }

    int32 nLenBuf = sizeof(tHead) + nLen;
    SharedPromise call_promise = std::make_shared<Promise>();
    SharedFuture f(call_promise->get_future());
    m_mapRequest[tHead.nSeq] = std::make_tuple(call_promise, f, NowSecond());

    if (nLenBuf > m_nSendBufferLen)
    {
        LOG_INFO << "send buffer nLenBuf=" << nLenBuf << ", is more than m_nSendBufferLen=" << m_nSendBufferLen;
        if (nLenBuf > BUFFER_MAX_LENGTH)
        { // Maybe it's misplaced
            LOG_ERR << "send buffer nLenBuf=" << nLenBuf << ", is more than BUFFER_MAX_LENGTH=" << BUFFER_MAX_LENGTH;
            return EC_DSF_SDK_COMMON_ERROR;
        }

        m_nSendBufferLen = nLenBuf;
        m_pSendBuffer->reserve(m_nSendBufferLen);
    }

    std::lock_guard<std::mutex> lock(m_SendBufferMtx);
    memcpy(m_pSendBuffer->data(), &tHead, sizeof(tHead));
    memcpy(m_pSendBuffer->data() + sizeof(tHead), strContent.c_str(), nLen);
    nRet = pTransport->SendData(m_pSendBuffer->data(), nLenBuf);
    if (0 != nRet)
    {
        LOG_ERR << "send data failed. length = " << nLenBuf << ", nRet = " << nRet;
        m_mapRequest.erase(tHead.nSeq);
        return nRet;
    }
    LOG_INFO << "[this=" << (void *)this << "][pTransport=" << (void *)pTransport.get() << "]"
             << pTransport->GetServiceName() << "send data success. length = " << nLenBuf
             << ", tHead = " << tHead.ToString();

    return EC_DSF_SDK_SUCCESS;
}

int32 DRSdkManager::AsyncGetResult(const DataHeader &tHead, std::string *pRes)
{
    auto &tuple = m_mapRequest[tHead.nSeq];
    auto &future = std::get<1>(tuple);
    auto status = future.wait_for(std::chrono::milliseconds(m_tDRSdkOption.nRequestWaitTimeoutMs));

    int32 nRet = EC_DSF_SDK_SUCCESS;
    if (status != std::future_status::ready)
    {
        LOG_ERR << "time_out, tHead: " << tHead.ToString();
        nRet = EC_DSF_SDK_TIMEOUT;
    }
    else
    {
        if (m_bStop)
        {
            LOG_ERR << "DRSdkManager is stop, m_mapRequest have released";
            return EC_DSF_SDK_TIMEOUT;
        }
        else
        {
            *pRes = future.get();
        }
    }
    {
        std::lock_guard<std::mutex> lg(m_RequestMtx);
        m_mapRequest.erase(tHead.nSeq);
    }

    return nRet;
};

void DRSdkManager::RecvThreadCallback(const std::shared_ptr<DrSdkTransport> &pTransport,
                                      const std::string &sServiceName)
{
    std::shared_ptr<char> pRecvBuffer = nullptr;
    if (pTransport == nullptr)
    {
        LOG_ERR << sServiceName << "pTransport is nullptr";
        return;
    }
    while (!m_bStop)
    {
        int32 buffer_len = RECV_BUFFER_MAX_LENGTH;
        int32 nRet = 0;

        nRet = CheckTransportAvailable();
        if (EC_DSF_SDK_SUCCESS != nRet)
        {
            LOG_ERR << sServiceName << "CheckTransportAvailable check failed, nRet = " << nRet;
            std::this_thread::sleep_for(std::chrono::milliseconds(m_tDRSdkConnectParam.nConnectTimeoutMs));
            continue;
        }

        nRet = pTransport->RecvData(pRecvBuffer, buffer_len);
        if (0 != nRet)
        {
            LOG_ERR << sServiceName << "pTransport RecvData failed. nRet = " << nRet;
            continue;
        }
        DataHeader *pHead = (DataHeader *)pRecvBuffer.get();
        LOG_INFO << "[" << (void *)pTransport.get() << "]" << sServiceName
                 << "pTransport recv data. length = " << buffer_len << ", pHead:" << pHead->ToString();
        switch (pHead->nType)
        {
        case SDK_CONTROL_DATA:
        case SDK_DUMP_DATA:
        case SDK_READ_DATA:
        case SDK_REG_TAG:
        case SDK_REG_OBJ_JSON:
        case SDK_READ_ATTR:
        case SDK_READ_ATTR_DATA:
        case SDK_READ_MODEL_JSON:
        case SDK_UNREG_TAG:
        case SDK_WRITE_TEXT_DATA: {
            if (pTransport != m_pTransport && CannotTransportTypeWhenUnavailable(pHead))
            {
                LOG_ERR << sServiceName << "unavailable , ignore it";
                break;
            }
            std::lock_guard<std::mutex> lg(m_RequestMtx);
            if (m_mapRequest.find(pHead->nSeq) != m_mapRequest.end())
            {
                auto &tuple = m_mapRequest[pHead->nSeq];
                auto &promise = std::get<0>(tuple);
                promise->set_value(std::string(pRecvBuffer.get(), sizeof(DataHeader) + pHead->nLen));
            }
            else
            {
                LOG_ERR << sServiceName << "recved msg time_out ,nSeq = " << pHead->nSeq;
            }
        }
        break;
        case SDK_REG_TAG_DATA: {
            if (pTransport != m_pTransport)
            {
                LOG_ERR << sServiceName << "unavailable , ignore it";
                break;
            }
            const int32 FIELD_NUM = 3;
            if (0 != pHead->nFlag)
            {
                LOG_ERR << sServiceName << "SDK_REG_TAG_DATA, nFlag = " << static_cast<int32>(pHead->nFlag);
                break;
            }
            auto nBatchId = pHead->nReserve1;
            if (m_mapRegInfos.find(nBatchId) == m_mapRegInfos.end())
            {
                LOG_ERR << sServiceName << "SDK_REG_TAG_DATA, batch id not found. nBatchId = " << nBatchId;
                break;
            }
            std::string strContent = std::string(pRecvBuffer.get() + sizeof(DataHeader), pHead->nLen);
            std::vector<dsfapi::TagValue> vecContent;
            auto bRes = dsfapi::UnPackBuffer(reinterpret_cast<char *>(pHead + 1), pHead->nLen, &vecContent);
            if (!bRes || 0 == vecContent.size() || vecContent.size() % FIELD_NUM != 0)
            {
                LOG_ERR << sServiceName << "SDK_REG_TAG_DATA, content size error,bRes=" << bRes
                        << ", size() = " << vecContent.size();
                break;
            }
            auto nCount = vecContent.size() / FIELD_NUM;

            // add task to thread pool and  async execute
            if (!m_pThreadPool->IsFull(nBatchId))
            {
                TagRecord *pTagRecord = new TagRecord[nCount];
                // TODO
                int32 *pErrorCode = new int32[nCount];
                for (int32 i = 0; i < vecContent.size(); i += FIELD_NUM)
                {

                    pTagRecord[i / FIELD_NUM].tTagName = std::move(vecContent[i]);
                    pTagRecord[i / FIELD_NUM].tTagValue = std::move(vecContent[i + 1]);
                    pErrorCode[i / FIELD_NUM] = *reinterpret_cast<int32 *>(vecContent[i + 2].Buffer());
                }

                auto func = m_mapRegInfos[nBatchId].pCallback;
                m_pThreadPool->Enqueue(nBatchId, func, nBatchId, pTagRecord, pErrorCode, nCount);
            }
            else
            {
                LOG_ERR << sServiceName << "threadPool is full, data will lost. nBatchId = " << nBatchId;
            }

            break;
        }
        case SDK_REG_OBJ_JSON_DATA: {
            if (pTransport != m_pTransport)
            {
                LOG_ERR << "unavailable , ignore it";
                break;
            }
            if (0 != pHead->nFlag)
            {
                LOG_ERR << sServiceName << "SDK_REG_OBJ_JSON_DATA, nFlag = " << static_cast<int32>(pHead->nFlag);
                break;
            }
            auto nBatchId = pHead->nReserve1;
            if (m_mapRegInfos.find(nBatchId) == m_mapRegInfos.end())
            {
                LOG_ERR << sServiceName << "SDK_REG_TAG_DATA, batch id not found. nBatchId = " << nBatchId;
                break;
            }

            // add task to thread pool and  async execute
            if (!m_pThreadPool->IsFull(nBatchId))
            {
                char *strJson = new char[pHead->nLen];
                memcpy(strJson, pRecvBuffer.get() + sizeof(DataHeader), pHead->nLen);

                auto func = m_mapRegInfos[nBatchId].pJsonCallback;
                m_pThreadPool->Enqueue(nBatchId, func, nBatchId, strJson, pHead->nLen);
            }
            else
            {
                LOG_ERR << sServiceName << "threadPool is full, data will lost. nBatchId = " << nBatchId;
            }

            break;
        }
        case SDK_HEARTBEAT: {
            // do nothing
            break;
        }
        default: {
            LOG_ERR << sServiceName << "pHead->nType not support. nType = " << static_cast<int32>(pHead->nType);
            break;
        }
        }
    }
}

int32 DRSdkManager::CheckTransportAvailable()
{
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx);
    if (nullptr == m_pTransport)
    {
        if (m_pTransportMain->GetAvailable())
        {
            LOG_INFO << "rmcheck:select main server";
            m_pTransport = m_pTransportMain;
            m_ConnServerType.store(CS_MAIN);
            return EC_DSF_SDK_SUCCESS;
        }
        else if (m_bMultilink && m_pTransportBak->GetAvailable())
        {
            LOG_INFO << "rmcheck:select bak server";
            m_pTransport = m_pTransportBak;
            m_ConnServerType.store(CS_BAK);
            return EC_DSF_SDK_SUCCESS;
        }
        // else fail
    }
    else
    {
        if (CS_MAIN == m_ConnServerType)
        {
            if (m_pTransportMain->GetAvailable())
            {
                return EC_DSF_SDK_SUCCESS; // no change,continue
            }
            else if (m_bMultilink && m_pTransportBak->GetAvailable())
            {
                LOG_INFO << "rmcheck:switch to bak server";
                m_pTransport = m_pTransportBak;
                m_ConnServerType.store(CS_BAK);
                return EC_DSF_SDK_SUCCESS;
            }
        }
        else
        {
            if (m_bMultilink && m_pTransportBak->GetAvailable())
            {
                return EC_DSF_SDK_SUCCESS; // no change,continue
            }
            else if (m_pTransportMain->GetAvailable())
            {
                LOG_INFO << "rmcheck:switch to main server";
                m_pTransport = m_pTransportMain;
                m_ConnServerType.store(CS_MAIN);
                return EC_DSF_SDK_SUCCESS;
            }
        }
    }
    LOG_ERR << "rmcheck:not found active server";
    m_pTransport = nullptr;
    m_ConnServerType.store(CS_UNKNOWN);
    return EC_DSF_SDK_CONNECT_FAILED;
}

} // namespace dsfapi