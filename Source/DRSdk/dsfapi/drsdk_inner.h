/*  Filename:    drsdk_inner.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Declare dsfapi inner API. for unit test
 *
 *  @author:     wuzheqiang
 *  @version:    10/08/2024	wuzheqiang	Initial Version
 **************************************************************/

#ifndef DRSDK_INNER_H
#define DRSDK_INNER_H

#include <string>

#include <functional>
#include <map>
#include <vector>

#include "dsfapi.h" // for DRSdkContext define
#include "drsdk_manager.h"
#ifdef _WIN32
#ifdef DRSDK_EXPORTS
#define DRSDK_API __declspec(dllexport)
#else
#define DRSDK_API __declspec(dllimport)
#endif
#else
#define DRSDK_API
#endif

namespace dsfapi
{
/**
 * @brief		save data to storage. dsfapi--> dataservice-->redis ,do not push to  dds
 *              [[only for unit test]]
 * @param [in]	pContext   dsfapi context handle
 * @param [in]	pTagName   tag names array
 * @param [in]	pTagValue  tag value array. should fill buffer and length
 * @param [in]	nTagCount  the count of tag
 * @return		Successfully returned 0, other returned error codes
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
DRSDK_API int32 Dr_Save(const DRSdkContext *pContext, const char **pTagName, const TagValue *const pTagValue,
                        const int32 nTagCount);

} // namespace dsfapi

#endif // DRSDK_INNER_H
