/*  Filename:    dsfapi.cpp
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Declare dsfapi API
 *
 *  @author:     wuzheqiang
 *  @version:    09/10/2024	wuzheqiang	Initial Version
 **************************************************************/
#include "dsfapi.h"
#include <iomanip>
#include <sstream>
#include <vector>

namespace dsfapi
{
std::mutex g_mutex;
#define CHECK_CONTEXT_NULLPTR(pContext)                                                                                \
    if (nullptr == pContext || nullptr == pContext->pDRSdkManager)                                                     \
    {                                                                                                                  \
        return EC_DSF_SDK_NULLPTR;                                                                                     \
    }

void Free_DR_Sdk_Context(DRSdkContext **pContextAddr)
{
    DRSdkContext *pContext = *pContextAddr;
    if (nullptr != pContext)
    {
        if (nullptr != pContext->pDRSdkManager)
        {
            delete pContext->pDRSdkManager;
            pContext->pDRSdkManager = nullptr;
        }
        delete pContext;
        pContext = nullptr;
        *pContextAddr = nullptr;
    }
}

void Free_Tag_Value(TagValue **ppTagValue)
{
    TagValue *pTagValue = *ppTagValue;
    if (nullptr != pTagValue)
    {
        delete[] pTagValue;
        pTagValue = nullptr;
    }
}

void Free_Tagtype_Info(TagtypeInfo **ppTagtypeInfo)
{
    TagtypeInfo *pTagtypeInfo = *ppTagtypeInfo;
    if (nullptr != pTagtypeInfo)
    {
        delete[] pTagtypeInfo;
        pTagtypeInfo = nullptr;
    }
}

void Free_Tag_Record(TagRecord **ppTagRecord)
{
    TagRecord *pTagRecord = *ppTagRecord;
    if (nullptr != pTagRecord)
    {
        delete[] pTagRecord;
        pTagRecord = nullptr;
    }
}

void Free_Str_Ptr(char **ppStr)
{
    char *pStr = *ppStr;
    if (nullptr != pStr)
    {
        delete[] pStr;
        pStr = nullptr;
    }
}

void Free_Int32_Ptr(int32 **ppData)
{
    int32 *pData = *ppData;
    if (nullptr != pData)
    {
        delete[] pData;
        pData = nullptr;
    }
}

DRSDK_API DRSdkContext *DR_Init(const DRSdkConnectParam &tDRSdkConnectParam, const DRSdkOption &tDRSdkOption)
{
    std::lock_guard<std::mutex> lock(g_mutex);
    auto *pManager = new DRSdkManager();
    if (nullptr == pManager)
    {
        return nullptr;
    }
    auto *pContext = new DRSdkContext();
    pContext->pDRSdkManager = pManager;
    pContext->tDRSdkConnectParam = tDRSdkConnectParam;
    pContext->tDRSdkOption = tDRSdkOption;

    int32 iRet = pManager->Init(tDRSdkConnectParam, tDRSdkOption);
    if (iRet != EC_DSF_SDK_SUCCESS)
    {
        pContext->errorcode = iRet;
    }

    return pContext;
}

DRSDK_API int32 DR_Uninit(const DRSdkContext *pContext)
{
    CHECK_CONTEXT_NULLPTR(pContext);

    std::lock_guard<std::mutex> lock(g_mutex);
    int32 nRet = pContext->pDRSdkManager->Uninit();
    // wait thread exit
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    return nRet;
}

DRSDK_API int32 DR_Write(const DRSdkContext *pContext, const char **pTagName, const TagValue *const pTagValue,
                         const int32 nTagCount)
{
    CHECK_CONTEXT_NULLPTR(pContext);
    return pContext->pDRSdkManager->DrWrite(pTagName, pTagValue, nTagCount);
}

DRSDK_API int32 DR_Write_Text(const DRSdkContext *pContext, const char *pTagName[], const char *pTagValue[],
                              const int32 nTagCount)
{
    CHECK_CONTEXT_NULLPTR(pContext);
    return pContext->pDRSdkManager->DrWriteText(pTagName, pTagValue, nTagCount);
}

DRSDK_API int32 DR_Read(const DRSdkContext *pContext, const char **pTagName, const int32 nTagNameCount,
                        TagValue **pTagValue, int32 **pErrorCode, int32 *pTagValueCount)
{
    CHECK_CONTEXT_NULLPTR(pContext);
    return pContext->pDRSdkManager->DrRead(pTagName, nTagNameCount, pTagValue, pErrorCode, pTagValueCount);
}

DRSDK_API int32 DR_Register_Tag(const DRSdkContext *pContext, const char **pTagName, const int32 nTagCount,
                                const uint16 nIntervalMs, const Callback &pCallBack, const uint8 nSendFlag,
                                int32 *pBatchId)
{
    CHECK_CONTEXT_NULLPTR(pContext);
    return pContext->pDRSdkManager->DrRegisterTag(pTagName, nTagCount, nIntervalMs, pCallBack, nSendFlag, pBatchId);
}

DRSDK_API int32 DR_Register_Tag_Json(const DRSdkContext *pContext, const char **pTagName, const int32 nTagCount,
                                     const uint16 nIntervalMs, const JsonCallback &pJsonCallBack, const uint8 nSendFlag,
                                     int32 *pBatchId)
{
    CHECK_CONTEXT_NULLPTR(pContext);
    return pContext->pDRSdkManager->DrRegisterTagJson(pTagName, nTagCount, nIntervalMs, pJsonCallBack, nSendFlag,
                                                      pBatchId);
}

DRSDK_API int32 DR_Unregister_Tag(const DRSdkContext *pContext, const int32 nBatchId)
{
    CHECK_CONTEXT_NULLPTR(pContext);
    return pContext->pDRSdkManager->DrUnregisterTag(nBatchId);
}

DRSDK_API int32 DR_Get_Tagtype_Info(const DRSdkContext *pContext, const char *const pObjectName,
                                    TagtypeInfo **pTagtypeInfo, int32 *pTagtypeInfoCount)
{
    CHECK_CONTEXT_NULLPTR(pContext);
    return pContext->pDRSdkManager->DrGetTagtypeInfo(pObjectName, pTagtypeInfo, pTagtypeInfoCount);
}

DRSDK_API int32 DR_Get_Tag_Value(const DRSdkContext *pContext, const char *const pObjectName, TagRecord **pTagRecord,
                                 int32 *pTagRecordCount)
{
    CHECK_CONTEXT_NULLPTR(pContext);
    return pContext->pDRSdkManager->DrGetTagtypeValue(pObjectName, pTagRecord, pTagRecordCount);
}

DRSDK_API int32 DR_Get_Model_Json(const DRSdkContext *pContext, const char *const pObjectName, char **sModeJsonString,
                                  int32 *nLength)
{
    CHECK_CONTEXT_NULLPTR(pContext);
    return pContext->pDRSdkManager->DrGetModelJson(pObjectName, sModeJsonString, nLength);
}
} // namespace dsfapi
