/*  Filename:    drsdk_common.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Define  common  structure, function and macro
 *
 *  @author:     wuzheqiang
 *	@version     09/10/2024	wuzheqiang	Initial Version
 **************************************************************/
#ifndef DRSDK_STRUCT_DEF_H
#define DRSDK_STRUCT_DEF_H

#include <functional>
#include <iostream>
#include <map>
#include <sstream>
#include <string.h>
#include <string>
#include <vector>

namespace dsfapi
{

typedef struct
{
    char szServerIp[32] = "127.0.0.1"; // DSF station ip
    uint16 nServerPort = 1234;         // DSF station port
    char szServerIpBak[32] = "";       // DSF station ip bak, for redundancy
    uint16 nServerPortBak = 0;         // DSF station port bak, for redundancy
    uint16 nConnectTimeoutMs = 1000;   // the timeout for connect DSF server. unit ms
    uint16 nHeartbeatTimeoutMs = 3000; // time limit for not receiving heartbeat. unit ms

    // for log display
    std::string ToString() const
    {
        std::stringstream ss;
        ss << "ip:" << szServerIp << ", port:" << nServerPort << ", ip_bak:" << szServerIpBak
           << ", port_bak:" << nServerPortBak << ", connect timeout:" << nConnectTimeoutMs
           << ", heartbeat timeout:" << nHeartbeatTimeoutMs;
        return ss.str();
    }
} DRSdkConnectParam;
typedef struct
{
    uint32 nThreadPoolNum = 10; // thread nums for run  register callback
    uint16 nRequestWaitTimeoutMs =
        1000; // The timeout for waiting for a response after sending a request to DSF. unit ms

    // for log display
    std::string ToString() const
    {
        std::stringstream ss;
        ss << "thread pool num:" << nThreadPoolNum << ", timeout:" << nRequestWaitTimeoutMs;
        return ss.str();
    }
} DRSdkOption;

enum SDKAttrType
{
    SDK_ATTR_TYPE_unknown = 0,
    SDK_ATTR_TYPE_INT8,
    SDK_ATTR_TYPE_INT16,
    SDK_ATTR_TYPE_INT32,
    SDK_ATTR_TYPE_INT64,
    SDK_ATTR_TYPE_UINT8 = 5,
    SDK_ATTR_TYPE_UINT16,
    SDK_ATTR_TYPE_UINT32,
    SDK_ATTR_TYPE_UINT64,
    SDK_ATTR_TYPE_FLOAT,
    SDK_ATTR_TYPE_DOUBLE = 10,
    SDK_ATTR_TYPE_STRING,
    SDK_ATTR_TYPE_BOOL,
    SDK_ATTR_TYPE_STRUCT,
    SDK_ATTR_TYPE_TIME,
    SDK_ATTR_TYPE_DATE = 15,
    SDK_ATTR_TYPE_TIME_OF_DAY,
    SDK_ATTR_TYPE_DATE_AND_TIME,
    SDK_ATTR_TYPE_LTIME,
    SDK_ATTR_TYPE_LDATE,
    SDK_ATTR_TYPE_LTIME_OF_DAY = 20,
    SDK_ATTR_TYPE_LDATE_AND_TIME,
    SDK_ATTR_TYPE_ARRAY,
    SDK_ATTR_TYPE_CHAR,
    SDK_ATTR_TYPE_max,
};

#pragma pack(push, 4) // set pack mode
typedef struct
{
    char szName[256] = {0};      // attr name
    char szAliasName[256] = {0}; // attr alias name
    SDKAttrType nType = SDK_ATTR_TYPE_unknown;
    uint16_t offset;
    uint16 nLen = 0; // attr data len
    SDKAttrType nArrayType = SDK_ATTR_TYPE_unknown;
    uint16_t nArrayRangeMin = 0;
    uint16_t nArrayRangeMax = 0;

    // for log display
    std::string ToString() const
    {
        std::stringstream ss;
        ss << "name:" << szName << ", alias:" << szAliasName << ", type:" << (int32_t)nType << ", len:" << nLen
           << ", offset: " << offset;
        return ss.str();
    }
} TagtypeInfo;
#pragma pack(pop) // resume to default
struct TagValue
{
    size_t nLength = 0;
    char *pBuffer = nullptr;

    TagValue() = default;
    TagValue(const char *str, const size_t len)
    {
        ReSet(str, len);
    }
    ~TagValue()
    {
        // std::cout << "~TagValue:" << nLength << std::endl;
        Clear();
    }
    // forbidden copy
    TagValue(const TagValue &other) = delete;
    TagValue &operator=(const TagValue &other) = delete;
    // allow move
    TagValue(TagValue &&other) noexcept : nLength(other.nLength), pBuffer(other.pBuffer)
    {
        other.pBuffer = nullptr;
        other.nLength = 0;
    }
    TagValue &operator=(TagValue &&other) noexcept
    {
        if (&other != this)
        {
            Clear();
            nLength = other.nLength;
            pBuffer = other.pBuffer;
            other.pBuffer = nullptr;
            other.nLength = 0;
        }
        return *this;
    }

    void ReSet(const char *str, const size_t len)
    {
        if (str == nullptr || len == 0)
        {
            Clear();
            return;
        }
        Clear();
        nLength = len;
        pBuffer = new char[nLength + 1];
        memcpy(pBuffer, str, nLength);
        pBuffer[nLength] = '\0';
    }

    void Clear()
    {
        if (pBuffer != nullptr)
        {
            delete[] pBuffer;
            pBuffer = nullptr;
        }
        nLength = 0;
    }

    char *Buffer() const
    {
        return pBuffer;
    }
    size_t Length() const
    {
        return nLength;
    }

    void Display() const
    {
        std::cout << "nLength: " << nLength << ", data: " << (pBuffer ? pBuffer : "null") << std::endl;
    }
};
// typedef TagValue TagName;
using TagName = TagValue;
typedef struct
{
    TagName tTagName;
    TagValue tTagValue;
} TagRecord;
// should free TagRecord
using Callback =
    std::function<void(const int32 nBatchId, TagRecord *pTagRecord, int32 *pErrorCode, const int32 nTagCount)>;
using JsonCallback = std::function<void(const int32 nBatchId, char *strJson, const int32 nLen)>;

} // namespace dsfapi
#endif // DRSDK_STRUCT_DEF_H