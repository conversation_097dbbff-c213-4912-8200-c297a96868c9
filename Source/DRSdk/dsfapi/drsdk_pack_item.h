/**
 * Filename        drsdk_pack_item.h
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description     pack and unpack
 *
 * Author          wuzheqiang
 * Version         12/19/2024    wuzheqiang    Initial Version
 **************************************************************/

#ifndef DRSDK_PACK_ITEM_H
#define DRSDK_PACK_ITEM_H

#include "data_types.h"
#include "drsdk_log.h"
#include "drsdk_struct_def.h"
#include <chrono>
#include <ctime>
#include <iomanip>
#include <string>
#include <vector>

namespace dsfapi
{

/**
 * @brief         PackItem str into  strRes
 * @param [out]   strRes  the result string
 * @param [in]    pStr    the item to pack
 * @param [in]    nLen    the nLen of pStr
 * @param [in]    bCheck  check nLen or not
 * @version       2024/12/19    wuzheqiang    Initial Version
 */
inline bool PackItem(std::string *strRes, const char *pStr, const uint16 nLen, bool bCheck = true)
{
    if (nullptr == strRes || nullptr == pStr || (bCheck && 0 == nLen))
    {
        LOG_ERR << "PackString param error. strRes is nullptr:" << (nullptr == strRes)
                << ", pStr is nullptr:" << (nullptr == pStr) << ", bCheck:" << bCheck << ", nLen:" << nLen;
        return false;
    }

    strRes->append((char *)&nLen, sizeof(nLen));
    strRes->append(pStr, nLen);
    return true;
}

/**
 * @brief        unpack buffer to  vector
 * @param [in]   sBuf    buffer
 * @param [in]   nLen    the nLen of buffer
 * @param [out]  vecRes  result vector
 * @version      2024/12/19    wuzheqiang    Initial Version
 */
inline bool UnPackBuffer(const char *sBuf, const size_t nLen, std::vector<TagValue> *vecRes)
{
    if (nLen < sizeof(uint16))
    {
        LOG_ERR << "UnPackBuffer buf len too small, nLen=" << nLen << std::endl;
        return false;
    }

    size_t nLeft = nLen;
    while (nLeft > 0)
    {
        // 1.length of item
        if (nLeft < sizeof(uint16))
        {
            LOG_ERR << "UnPackBuffer nLeft < sizeof(uint16) , nLeft=" << nLeft << std::endl;
            return false;
        }

        uint16 nItemLen = 0;
        memcpy(&nItemLen, sBuf + (nLen - nLeft), sizeof(nItemLen));
        nLeft -= sizeof(nItemLen);

        // 2.content of item
        if (nItemLen > nLeft)
        {
            LOG_ERR << "UnPackBuffer nItemLen > nLeft, nItemLen=" << nItemLen << ", nLeft=" << nLeft << std::endl;
            return false;
        }

        // TagValue tItem(sBuf + (nLen - nLeft), nItemLen);
        // vecRes->emplace_back(std::move(tItem));
        vecRes->emplace_back(sBuf + (nLen - nLeft), static_cast<size_t>(nItemLen));
        nLeft -= nItemLen;
    }

    return true;
}
} // namespace dsfapi
#endif // DRSDK_PACK_ITEM_H
