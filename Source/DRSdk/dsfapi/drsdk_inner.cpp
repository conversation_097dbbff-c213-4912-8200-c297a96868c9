/*  Filename:    drsdk_inner.cpp
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Declare dsfapi API. for unit test
 *
 *  @author:     wuzheqiang
 *  @version:    09/10/2024	wuzheqiang	Initial Version
 **************************************************************/
#include "drsdk_inner.h"
#include <iomanip>
#include <sstream>
#include <vector>

namespace dsfapi
{

DRSDK_API int32 Dr_Save(const DRSdkContext *pContext, const char **pTagName, const TagValue *const pTagValue,
                        const int32 nTagCount)
{
    if (nullptr == pContext || nullptr == pContext->pDRSdkManager)
    {
        return EC_DSF_SDK_NULLPTR;
    }
    return pContext->pDRSdkManager->DrSave(pTagName, pTagValue, nTagCount);
}

} // namespace dsfapi
