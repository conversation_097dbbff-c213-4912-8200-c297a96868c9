/*  Filename:    dsfapi.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Declare dsfapi API
 *
 *  @author:     wuzheqiang
 *  @version:    09/10/2024	wuzheqiang	Initial Version
 **************************************************************/

#ifndef DRSDK_H
#define DRSDK_H

#include "drsdk_manager.h"

#ifdef _WIN32
#ifdef DRSDK_EXPORTS
#define DRSDK_API __declspec(dllexport)
#else
#define DRSDK_API __declspec(dllimport)
#endif
#else
#define DRSDK_API
#endif

namespace dsfapi
{
/*
instructions:
1. Returned error_code defined in [drsdk_common.h]
2. The struct info used is defined in the [drsdk_struct_def.h]
*/

/*
    context for connection to dsf station
    dsfapi api should be called with this context
*/
struct DRSdkContext
{

    DRSdkConnectParam tDRSdkConnectParam;  // connect param ,such as ip,port,timeout
    DRSdkOption tDRSdkOption;              // connect param ,such as thread pool size , request timeout
    int32 errorcode = EC_DSF_SDK_SUCCESS;  // the error code of last operation
    DRSdkManager *pDRSdkManager = nullptr; // the handle of dsfapi manager

    /**
     * @brief          get connection status
     * @return         true if connected else false
     * @version        2024/10/17	wuzheqiang	Initial Version
     */
    bool ConnectStatus()
    {
        if (nullptr == pDRSdkManager)
        {
            return false;
        }
        return pDRSdkManager->DrConnectStatus();
    }
};

/**
 * @brief          init dsfapi: Construct a DRSdkContext ; connect to server, alloc memery and create threads
 * @param [in]     tDRSdkConnectParam  contains server ip, server port, connect timeout
 * @param [in]     tDRSdkOption  contains thread pool size, request timeout
 * @return         DRSdkContext* if success, else nullptr.
 *                 DRSdkContext is the handle of dsfapi, should be used in all  dsfapi APIs
 *                 DRSdkContext shoud be released by Free_DR_Sdk_Context at last
 * @version        2024/09/25	wuzheqiang	Initial Version
 */
DRSDK_API DRSdkContext *DR_Init(const DRSdkConnectParam &tDRSdkConnectParam, const DRSdkOption &tDRSdkOption);

/**
 * @brief          uninit dsfapi: disconnect from server and join threads
 * @param [in]	   pContext   dsfapi context handle
 * @return         Successfully returned 0, other returned error codes
 * @version        2024/10/09	wuzheqiang	Initial Version
 */
DRSDK_API int32 DR_Uninit(const DRSdkContext *pContext);

/**
 * @brief		write control commands to dsf station
 * @param [in]	pContext   dsfapi context handle
 * @param [in]	pTagName   tag names array
 * @param [in]	pTagValue  tag value array. should fill buffer and length
 * @param [in]	nTagCount  the count of tag
 * @return		Successfully returned 0, other returned error codes
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
DRSDK_API int32 DR_Write(const DRSdkContext *pContext, const char **pTagName, const TagValue *const pTagValue,
                         const int32 nTagCount);

/**
 * @brief		write control commands to dsf station
 * @param [in]	pContext   dsfapi context handle
 * @param [in]	pTagName   tag names array
 * @param [in]	pTagValue  tag value array. text value of tag
 * @param [in]	nTagCount  the count of tag
 * @return		Successfully returned 0, other returned error codes
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
DRSDK_API int32 DR_Write_Text(const DRSdkContext *pContext, const char *pTagName[], const char *pTagValue[],
                              const int32 nTagCount);

/**
 * @brief		read tag values from dsf station
 * @param [in]	pContext    dsfapi context handle
 * @param [in]	pTagName    tag names array
 * @param [in]	nTagNameCount   the cout of tag
 * @param [out]	pTagValue   tag value array.
 *                          !!! [should release by Free_Tag_Value]
 * @param [out] pErrorCode  the error code for each tag value
 *                          !!! [should release by Free_Int32_Ptr]
 * @param [out]	pTagValueCount  the count of tag value
 * @return		Successfully returned 0, other returned error codes
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
DRSDK_API int32 DR_Read(const DRSdkContext *pContext, const char **pTagName, const int32 nTagNameCount,
                        TagValue **pTagValue, int32 **pErrorCode, int32 *pTagValueCount);

/**
 * @brief		register tag for async reading
 * @param [in]	pContext   dsfapi context handle
 * @param [in]	pTagName   tag names array
 * @param [in]	nTagCount  the count of tag
 * @param [in]	nIntervalMs Interval for querying data, unit:ms.
 *                          interval value should in   {x|10<=x<65535}
 * @param [in]	pCallBack   callback function ptr ,defined drsdk_struct_def.h
 *        std::function<void(const int32 nBatchId, TagRecord *pTagRecord, int32 *pErrorCode, const int32 nTagCount)>;
 *        !!! [The callback must  release the TagRecord(by Free_Tag_Record) and  pErrorCode(by Free_Int32_Ptr) at last]
 * @param [in]  nSendFlag 0: direct push, 1: push after changed
 * @param [out]	pBatchId    batch ID after successful registration
 * @return		Successfully returned 0, other returned error codes
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
DRSDK_API int32 DR_Register_Tag(const DRSdkContext *pContext, const char **pTagName, const int32 nTagCount,
                                const uint16 nIntervalMs, const Callback &pCallBack, const uint8 nSendFlag,
                                int32 *pBatchId);
/**
 * @brief		register tag for async reading
 * @param [in]	pContext   dsfapi context handle
 * @param [in]	pTagName   tag names array
 * @param [in]	nTagCount  the count of tag
 * @param [in]	nIntervalMs Interval for querying data, unit:ms.
 *                          interval value should in   {x|10<=x<65535}
 * @param [in]	pJsonCallBack   callback function ptr ,defined drsdk_struct_def.h
 *        std::function<void(const int32 nBatchId, char *strJson, const int32 nLen)>;
 *        !!! [The callback must  release the strJson(by Free_Str_Ptr) at last]
 * @param [in]  nSendFlag 0: direct push, 1: push after changed
 * @param [out]	pBatchId    batch ID after successful registration
 * @return		Successfully returned 0, other returned error codes
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
DRSDK_API int32 DR_Register_Tag_Json(const DRSdkContext *pContext, const char **pTagName, const int32 nTagCount,
                                     const uint16 nIntervalMs, const JsonCallback &pJsonCallBack, const uint8 nSendFlag,
                                     int32 *pBatchId);
/**
 * @brief       unregister BatchId async reading
 * @param [in]  pContext  dsfapi context handle
 * @param [in]  nBatchId  batch ID return by DR_Register_Tag
 * @return      Successfully returned 0, other returned error codes
 * @version     2024/11/11    wuzheqiang    Initial Version
 */
DRSDK_API int32 DR_Unregister_Tag(const DRSdkContext *pContext, const int32 nBatchId);

/**
 * @brief		read tagtype info from dsf station
 * @param [in]	pContext    dsfapi handle
 * @param [in]	pObjectName object names array
 * @param [out]	pTagtypeInfo object tagtype array
 *                           !!! [should release by Free_Tagtype_Info]
 * @param [out]	pTagtypeInfoCount  the count of tagtype
 * @return		Successfully returned 0, other returned error codes
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
DRSDK_API int32 DR_Get_Tagtype_Info(const DRSdkContext *pContext, const char *const pObjectName,
                                    TagtypeInfo **pTagtypeInfo, int32 *pTagtypeInfoCount);

/**
 * @brief		read tag record from dsf station
 * @param [in]	pContext    dsfapi handle
 * @param [in]	pObjectName object names array
 * @param [out]	TagRecord   object tag record array
 *                          !!! [should release by Free_Tag_Record]
 * @param [out]	pTagRecordCount  the count of tag record
 * @return		Successfully returned 0, other returned error codes
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
DRSDK_API int32 DR_Get_Tag_Value(const DRSdkContext *pContext, const char *const pObjectName, TagRecord **TagRecord,
                                 int32 *pTagRecordCount);

/**
 * @brief       read model of the object and return its JSON string
 * @param [in]	pContext    dsfapi handle
 * @param [in]	pObjectName object names array
 * @param [out]	sModeJsonString   json string of the object model
 *                          !!! [should release by Free_Str_Ptr]
 * @param [out]	length     the count of sModeJsonString
 * @return
 * @version        2024/10/19	wuzheqiang	Initial Version
 */
DRSDK_API int32 DR_Get_Model_Json(const DRSdkContext *pContext, const char *const pObjectName, char **sModeJsonString,
                                  int32 *nLength);

/**
 * @brief          this function should must be called to disconnect from dsf and release resources.
 * @version        2024/10/15	wuzheqiang	Initial Version
 */
void Free_DR_Sdk_Context(DRSdkContext **pContextAddr);

/**
 * @brief          this function should must be called to  release TagValue.
 * @version        2024/10/15	wuzheqiang	Initial Version
 */
void Free_Tag_Value(TagValue **pTagValue);
/**
 * @brief          this function should must be called to  release TagtypeInfo.
 * @version        2024/10/15	wuzheqiang	Initial Version
 */
void Free_Tagtype_Info(TagtypeInfo **pTagtypeInfo);
/**
 * @brief          this function should must be called to  release TagRecord .
 * @version        2024/10/15	wuzheqiang	Initial Version
 */
void Free_Tag_Record(TagRecord **pTagRecord);

/**
 * @brief          this function should must be called to  release char* .
 * @version        2024/10/15	wuzheqiang	Initial Version
 */
void Free_Str_Ptr(char **ppStr);

/**
 * @brief          this function should must be called to  release int32* .
 * @version        2024/10/15	wuzheqiang	Initial Version
 */
void Free_Int32_Ptr(int32 **ppStr);

} // namespace dsfapi

#endif // DRSDK_H
