/**
 * Filename        drsdk_transport_socket.h
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description     Define DrSdkTransport, as client use boost::asio to communicate with DataService.
 *
 * Author          wuzheqiang
 * Version         09/20/2024	wuzheqiang	Initial Version
 **************************************************************/

#ifndef DRSDK_TRANSPORT_H
#define DRSDK_TRANSPORT_H
#include "drsdk_common.h"

namespace dsfapi
{

class DrSdkTransport
{
  public:
    DrSdkTransport() = default;
    virtual ~DrSdkTransport() = default;
    /**
     * @brief		init DrSdkTransport: conenct to DataService
     * @param [in]  tDRSdkConnectParam     contains server ip, server port, connect timeout
     * @return		Successfully returned 0, other returned error codes
     * @version		09/10/2024	wuzheqiang	Initial Version
     */
    virtual int32 Init(const DRSdkConnectParam &tDRSdkConnectParam, const bool bIsMain = true) = 0;

    /**
     * @brief		Send data to DataService
     * @param [in]	pSendBuf  buffer to send
     * @param [in]	nLenBuf   buffer length
     * @return		Successfully returned 0, other returned error codes
     * @version		09/10/2024	wuzheqiang	Initial Version
     */
    virtual int32 SendData(const char *pSendBuf, int32 nLenBuf) = 0;

    /**
     * @brief		Recv data to DataService
     * @param [in]	pSendBuf  buffer to recv
     * @param [in]	nLenBuf   received length
     * @return		Successfully returned 0, other returned error codes
     * @version		09/10/2024	wuzheqiang	Initial Version
     */
    virtual int32 RecvData(std::shared_ptr<char> &pRecvBuf, int32 &pBufLen) = 0;

    /**
     * @brief          get connection status
     * @return         true if connected else false
     * @version        2024/10/09	wuzheqiang	Initial Version
     */
    virtual bool GetConnectStatus() const = 0;

    /**
     * @brief          stop read thread and close connection
     * @version        2024/10/18	wuzheqiang	Initial Version
     */
    virtual void Stop() = 0;

    /**
     * @brief          set connected callback, for automatic register async read callback
     * @version        2024/10/09	wuzheqiang	Initial Version
     */
    virtual void SetConnectedCallback(
        const std::function<void(std::shared_ptr<DrSdkTransport>)> &pConnectedCallback) = 0;

    virtual SDKRMStatus GetRmStatus() const = 0;
    virtual bool GetAvailable() const = 0;
    virtual const std::string &GetServiceName() const = 0;
};
} // namespace dsfapi
#endif