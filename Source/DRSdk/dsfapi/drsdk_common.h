/*  Filename:    drsdk_common.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Define  common  structure, function and macro
 *
 *  @author:     wuz<PERSON>qiang
 *	@version     09/10/2024	wuzheqiang	Initial Version
 **************************************************************/
#ifndef DRSDK_COMMON_H
#define DRSDK_COMMON_H

#include "data_types.h"
#include "drsdk_log.h"
#include "drsdk_pack_item.h"
#include "drsdk_struct_def.h"
#include "errcode/error_code.h"
#include <chrono>
#include <ctime>
#include <iostream>
#include <memory>
#include <stdint.h>
#include <string.h>
#include <string>
#include <thread>
#include <vector>

namespace dsfapi
{
constexpr uint32 BUFFER_DEF_LENGTH = 1024;
constexpr uint32 SEND_BUFFER_MAX_LENGTH = BUFFER_DEF_LENGTH * 64;
constexpr uint32 RECV_BUFFER_MAX_LENGTH = BUFFER_DEF_LENGTH * 64;
constexpr uint32 BUFFER_MAX_LENGTH = 1024 * 1024 * 2; // 2 mb

/*get cur second time_stamp*/
inline uint32 NowSecond()
{
    using namespace std::chrono;
    auto now = system_clock::now();

    auto seconds_since_epoch = duration_cast<seconds>(now.time_since_epoch()).count();

    return static_cast<uint32>(seconds_since_epoch);
}
inline uint64 NowMillisecond()
{
    using namespace std::chrono;
    auto now = system_clock::now();

    auto milliseconds_since_epoch = duration_cast<milliseconds>(now.time_since_epoch()).count();

    return static_cast<uint64>(milliseconds_since_epoch);
}

enum SDKRMStatus : uint8
{
    SDKRM_STATUS_INACTIVE = 0,
    SDKRM_STATUS_ACTIVE = 1,
    SDKRM_STATUS_UNAVALIBLE = 2,
};

enum SDKConnServerType : uint8
{
    CS_UNKNOWN = 0,
    CS_MAIN = 1,
    CS_BAK = 2,
};

enum SDKReqType : uint8
{
    SDK_UNKNOWN = 0,
    SDK_CONTROL_DATA = 1,       // write binary data
    SDK_DUMP_DATA = 2,          // write binary data ,just for save
    SDK_REG_TAG = 3,            // reg tag and return binary data
    SDK_REG_TAG_DATA = 4,       // reg tag and return binary data
    SDK_READ_DATA = 5,          // sync read data
    SDK_READ_ATTR = 6,          // read model attribute
    SDK_READ_ATTR_DATA = 7,     // read model value
    SDK_READ_MODEL_JSON = 8,    // read model value, return json str
    SDK_UNREG_TAG = 9,          // unregister tag
    SDK_REG_OBJ_JSON = 10,      // reg obj and return json
    SDK_REG_OBJ_JSON_DATA = 11, // reg obj and return json
    SDK_WRITE_TEXT_DATA = 12,   // write text data
    // reserved

    SDK_HEARTBEAT = 100, // heartbeat
    SDK_MAX = 255        // max value
};

#pragma pack(push, 4) // set pack mode
struct DataHeader
{
    uint8 nType = 0;          // request/response type. use enum SDKReqType
    uint8 nFlag = 0;          // result flag. 0 success, 1 fail
    uint8 nFlagReserve1 = 0;  // reserve. special purpose
    uint8 nFlagReserve2 = 0;  // reserve. special purpose
    uint16 nReserve1 = 0;     // reserve. special purpose
    uint16 nReserve2 = 0;     // reserve. special purpose
    uint32 nLen = 0;          // content data len
    uint32 nSeq = 0;          // for async request
    uint32 nLongReserve1 = 0; // reserve. special purpose
    uint32 nLongReserve2 = 0; // reserve. special purpose

    // for log display
    std::string ToString() const
    {
        std::stringstream ss;
        ss << "head_size:" << sizeof(DataHeader) << ", nType:" << (int32)nType << ", nFlag:" << (int32)nFlag
           << ", nFlagReserve1:" << (int32)nFlagReserve1 << ", nFlagReserve2:" << (int32)nFlagReserve2
           << ", nReserve1:" << (int32)nReserve1 << ", nReserve2:" << (int32)nReserve2 << ", nLen:" << (int32)nLen
           << ", nSeq:" << nSeq << ", nLongReserve1:" << nLongReserve1 << ", nLongReserve2:" << nLongReserve2;
        return ss.str();
    }
};
#pragma pack(pop) // resume to default

inline bool CanTransportTypeWhenUnavailable(const DataHeader *pHead)
{
    return (SDK_REG_TAG == pHead->nType || SDK_UNREG_TAG == pHead->nType || SDK_REG_OBJ_JSON == pHead->nType ||
            SDK_HEARTBEAT == pHead->nType);
}

inline bool CannotTransportTypeWhenUnavailable(const DataHeader *pHead)
{
    return false == CanTransportTypeWhenUnavailable(pHead);
}
} // namespace dsfapi
#endif