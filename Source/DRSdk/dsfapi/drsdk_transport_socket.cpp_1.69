/**
 * Filename        drsdk_transport_socket.cpp
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description     Define DrSdkTransportSocket, use share memory to communicate with DataService.
 *
 * Author          wuzheqiang
 * Version         09/20/2024	wuzheqiang	Initial Version
 **************************************************************/

#include "drsdk_transport_socket.h"
#include "error_code.h"
#include <boost/bind/bind.hpp>
namespace dsfapi
{

// Constructor
DrSdkTransportSocket::DrSdkTransportSocket()
    : m_pSocket(std::make_unique<boost::asio::ip::tcp::socket>(m_IoContext)), m_tResolver(m_IoContext),
      m_ReconnectTimer(m_IoContext)
{
    m_bStop.store(false);
    m_bConnected.store(false);
    m_RecvBufHead.reset(new char[sizeof(DataHeader)]);
    m_RecvBufBody.reset(new char[RECV_BUFFER_MAX_LENGTH]);
}

// Destructor
DrSdkTransportSocket::~DrSdkTransportSocket()
{
    Stop();
}

int32 DrSdkTransportSocket::Init(const DRSdkConnectParam &tDRSdkConnectParam)
{
    m_tDRSdkConnectParam = tDRSdkConnectParam;
    StartConnect(); // Start asynchronous connection

    // Start the io_context run loop, which will handle all async operations
    std::thread ioThread([this]() {
        m_IoContext.run(); // This will run in a separate thread
    });
    pthread_setname_np(ioThread.native_handle(), "io_context");
    ioThread.detach();

    // Timeout range: 500ms ~ 2000ms
    auto nTimeoutMs = m_tDRSdkConnectParam.nConnectTimeoutMs;
    auto nRealTimeoutMs = (nTimeoutMs < 500) ? 500 : (nTimeoutMs > 2000) ? 2000 : nTimeoutMs;
    std::this_thread::sleep_for(std::chrono::milliseconds(nRealTimeoutMs)); // Wait for the connection to complete
    return m_bConnected ? EC_DSF_SDK_SUCCESS : EC_DSF_SDK_CONNECT_FAILED;
}

int32 DrSdkTransportSocket::Close()
{
    if (m_bConnected)
    {
        boost::system::error_code ec;
        m_pSocket->shutdown(boost::asio::ip::tcp::socket::shutdown_both, ec);
        m_pSocket->close(ec);
        m_bConnected = false;
    }

    return EC_DSF_SDK_SUCCESS;
}

void DrSdkTransportSocket::StartConnect()
{
    boost::asio::ip::tcp::resolver::results_type endpoints =
        m_tResolver.resolve(m_tDRSdkConnectParam.szServerIp, std::to_string(m_tDRSdkConnectParam.nServerPort));
    boost::asio::async_connect(
        *m_pSocket, endpoints,
        boost::bind(&DrSdkTransportSocket::HandleConnect, this, boost::asio::placeholders::error));
}

void DrSdkTransportSocket::HandleConnect(const boost::system::error_code &error)
{
    if (!error)
    {
        LOG_INFO << "Connected to server!" << std::endl;
        m_bConnected = true;
        // set  TCP_NODELAY before connect
        m_pSocket->set_option(boost::asio::ip::tcp::no_delay(true));
        StartRead(); // Start asynchronous read after successful connect

        if (nullptr != m_pConnectedCallback)
        {
            m_pConnectedCallback();
        }
    }
    else
    {
        LOG_ERR << "Failed to connect: " << error.message() << std::endl;
        StartReconnect(); // Start reconnection process if connection fails
    }
}

void DrSdkTransportSocket::StartRead()
{
    // m_pSocket->async_read_some(boost::asio::buffer(m_RecvBufHead),
    //                            boost::bind(&DrSdkTransportSocket::HandleRead, this, boost::asio::placeholders::error,
    //                                        boost::asio::placeholders::bytes_transferred));
    if (m_bStop)
    {
        LOG_INFO << "Stop read head" << std::endl;
        return;
    }
    boost::asio::async_read(*m_pSocket, boost::asio::buffer(m_RecvBufHead.get(), sizeof(DataHeader)),
                            boost::asio::transfer_exactly(sizeof(DataHeader)), // Read exactly the size of DataHeader
                            boost::bind(&DrSdkTransportSocket::HandleReadHead, this, boost::asio::placeholders::error,
                                        boost::asio::placeholders::bytes_transferred));
}

void DrSdkTransportSocket::HandleReadHead(const boost::system::error_code &error, size_t bytes_transferred)
{
    if (!error)
    {
        // LOG_INFO << "HandleReadHead Received data, bytes_transferred:" << bytes_transferred << std::endl;
        // Process the received data
        auto pHead = reinterpret_cast<DataHeader *>(m_RecvBufHead.get());
        LOG_INFO << "HandleReadHead Received data, head:" << pHead->ToString() << std::endl;
        if (pHead->nLen > 0)
        {
            if (m_bStop)
            {
                LOG_INFO << "Stop read body" << std::endl;
                return;
            }
            if (pHead->nLen >= RECV_BUFFER_MAX_LENGTH)
            {
                LOG_ERR << "recv buffer length is less than RECV_BUFFER_MAX_LENGTH, nLen=" << pHead->nLen << std::endl;
                // abnormal ,shoud reconnect ??
                // StartReconnect();
            }

            boost::asio::async_read(*m_pSocket, boost::asio::buffer(m_RecvBufBody.get(), pHead->nLen),
                                    boost::asio::transfer_exactly(pHead->nLen), // Read exactly the size of msg body
                                    boost::bind(&DrSdkTransportSocket::HandleReadBody, this,
                                                boost::asio::placeholders::error,
                                                boost::asio::placeholders::bytes_transferred));
        }
        else
        {
            int32 nMsgLen = sizeof(DataHeader);
            std::shared_ptr<char[]> pMsgBuf(new char[nMsgLen]);
            memcpy(pMsgBuf.get(), m_RecvBufHead.get(), sizeof(DataHeader));
            DataTuple tData = std::make_tuple(pMsgBuf, nMsgLen);
            Enqueue(tData);
            // Continue reading more data asynchronously
            StartRead();
        }
    }
    else
    {
        LOG_ERR << "Read error: " << error.message() << std::endl;
        StartReconnect(); // If read fails, trigger reconnection
    }
}

void DrSdkTransportSocket::HandleReadBody(const boost::system::error_code &error, size_t bytes_transferred)
{
    if (!error)
    {
        LOG_INFO << "HandleReadBody Received data, bytes_transferred:" << bytes_transferred << std::endl;

        int32 nMsgLen = sizeof(DataHeader) + bytes_transferred;
        std::shared_ptr<char[]> pMsgBuf(new char[nMsgLen]);
        memcpy(pMsgBuf.get(), m_RecvBufHead.get(), sizeof(DataHeader));
        memcpy(pMsgBuf.get() + sizeof(DataHeader), m_RecvBufBody.get(), bytes_transferred);
        DataTuple tData = std::make_tuple(pMsgBuf, nMsgLen);
        Enqueue(tData);
        // Continue reading more data asynchronously
        StartRead();
    }
    else
    {
        LOG_ERR << "Read error: " << error.message() << std::endl;
        StartReconnect(); // If read fails, trigger reconnection
    }
}

bool DrSdkTransportSocket::Enqueue(DataTuple &tData)
{
    std::lock_guard<std::mutex> lock(m_Mutex);
    m_SendQueue.emplace(std::move(tData));
    m_Cv.notify_one(); // Notify one waiting thread
    return true;
}

bool DrSdkTransportSocket::Dequeue(DataTuple &tData)
{
    std::unique_lock<std::mutex> lock(m_Mutex);
    m_Cv.wait(lock, [this] { return m_bStop || !m_SendQueue.empty(); }); // Wait until the queue is not empty
    if (this->m_bStop)
    {
        return false; // If the stop flag is set�� return false
    }
    tData = m_SendQueue.front();
    m_SendQueue.pop();
    return true;
}

int32 DrSdkTransportSocket::SendData(const char *pSendBuf, int32 nLenBuf)
{
    if (!m_bConnected || m_bStop)
    {
        LOG_ERR << "Not connected or m_bStop, can't send data" << std::endl;
        return EC_DSF_SDK_COMMON_ERROR;
    }

    boost::asio::async_write(*m_pSocket, boost::asio::buffer(pSendBuf, nLenBuf),
                             boost::bind(&DrSdkTransportSocket::HandleSend, this, boost::asio::placeholders::error,
                                         boost::asio::placeholders::bytes_transferred));

    return EC_DSF_SDK_SUCCESS;
}

void DrSdkTransportSocket::HandleSend(const boost::system::error_code &error, size_t bytes_transferred)
{
    if (!error)
    {
        LOG_INFO << "Sent data successfully, bytes_transferred:" << bytes_transferred << std::endl;
    }
    else
    {
        LOG_ERR << "Send error: " << error.message() << std::endl;
        StartReconnect(); // If send fails, trigger reconnection
    }
}

int32 DrSdkTransportSocket::RecvData(char *pRecvBuf, int32 &pBufLen)
{
    DataTuple tData;
    if (!Dequeue(tData))
    {
        LOG_ERR << "RecvData Dequeue failed" << std::endl;
        return EC_DSF_SDK_COMMON_ERROR;
    }

    auto pRecvData = std::get<0>(tData);
    auto nRecvLen = std::get<1>(tData);
    std::memcpy(pRecvBuf, pRecvData.get(), nRecvLen);
    pBufLen = nRecvLen;

    return EC_DSF_SDK_SUCCESS;
}

void DrSdkTransportSocket::StartReconnect()
{
    if (m_bStop)
    {
        return; // If the socket is stopped, do not attempt to reconnect
    }
    LOG_ERR << "Attempting to reconnect in 2 seconds..." << std::endl;

    // Wait for 5 seconds before attempting to reconnect
    m_ReconnectTimer.expires_after(std::chrono::seconds(2));
    m_ReconnectTimer.async_wait(
        boost::bind(&DrSdkTransportSocket::HandleReconnect, this, boost::asio::placeholders::error));
}

void DrSdkTransportSocket::HandleReconnect(const boost::system::error_code &error)
{
    if (!error)
    {
        LOG_ERR << "Reconnecting..." << std::endl;
        Close();                                                                 // Close existing socket if necessary
        m_pSocket = std::make_unique<boost::asio::ip::tcp::socket>(m_IoContext); // Create a new socket
        StartConnect();                                                          // Try to reconnect
    }
}
} // namespace dsfapi