/**
 * Filename        drsdk_transport_socket.cpp
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description     Define DrSdkTransportSocket, use share memory to communicate with DataService.
 *
 * Author          wuzheqiang
 * Version         09/20/2024	wuzheqiang	Initial Version
 *************************************************************/

#include "drsdk_transport_socket.h"
#include <boost/bind/bind.hpp>
#include <nlohmann/json.hpp>
#include <thread>
namespace dsfapi
{
using json = nlohmann::json;
// Constructor
DrSdkTransportSocket::DrSdkTransportSocket()
    : m_pSocket(new boost::asio::ip::tcp::socket(m_IoService)), m_tResolver(m_IoService), m_HeartBeatTimer(m_IoService),
      m_TimeoutTimer(m_IoService), m_ReconnectTimer(m_IoService) // 初始化 timer
{
    m_bStop.store(false);
    m_bConnected.store(false);
    m_RecvBufHead.reset(new char[sizeof(DataHeader)]);
    // m_RecvBufBody.reset(new char[RECV_BUFFER_MAX_LENGTH]);
    m_RecvBufBodyLen = RECV_BUFFER_MAX_LENGTH;
    m_RecvBufBody.reserve(m_RecvBufBodyLen);
}

// Destructor
DrSdkTransportSocket::~DrSdkTransportSocket()
{
    this->Stop();
    m_RecvBufBody.clear();
    m_RecvBufBody.shrink_to_fit();
}

int32 DrSdkTransportSocket::Init(const DRSdkConnectParam &tDRSdkConnectParam, const bool bIsMain)
{
    m_tDRSdkConnectParam = tDRSdkConnectParam;
    m_sServerIp = std::string(m_tDRSdkConnectParam.szServerIp);
    m_sServerPort = std::to_string(m_tDRSdkConnectParam.nServerPort);
    if (!bIsMain)
    {
        m_sServiceName = "bak";
        m_sServerIp = std::string(m_tDRSdkConnectParam.szServerIpBak);
        m_sServerPort = std::to_string(m_tDRSdkConnectParam.nServerPortBak);
    }

    StartConnect(); // Start asynchronous connection

    // Start the io_context run loop, which will handle all async operations
    std::thread ioThread([this]() {
        m_IoService.run(); // This will run in a separate thread
    });
    pthread_setname_np(ioThread.native_handle(), std::string("io_srv_" + m_sServiceName).c_str());
    ioThread.detach();

    std::ostringstream oss;
    oss << static_cast<const void *>(this);
    m_sServiceName = "[" + oss.str() + "-" + m_sServiceName + "]";

    // Timeout range: 500ms ~ 2000ms
    auto nTimeoutMs = m_tDRSdkConnectParam.nConnectTimeoutMs;
    auto nRealTimeoutMs = (nTimeoutMs < 500) ? 500 : (nTimeoutMs > 2000) ? 2000 : nTimeoutMs;
    std::this_thread::sleep_for(std::chrono::milliseconds(nRealTimeoutMs)); // Wait for the connection to complete

    boost::system::error_code ec;
    CheckHeartBeatTimeOut(ec); // Start the heartbeat timer

    return m_bConnected ? EC_DSF_SDK_SUCCESS : EC_DSF_SDK_CONNECT_FAILED;
}

int32 DrSdkTransportSocket::Close()
{
    if (m_bConnected)
    {
        boost::system::error_code ec;
        m_pSocket->shutdown(boost::asio::ip::tcp::socket::shutdown_both, ec);
        m_pSocket->close(ec);
        m_bConnected = false;
    }

    return EC_DSF_SDK_SUCCESS;
}

void DrSdkTransportSocket::StartConnect()
{
    // Resolve the endpoint
    boost::asio::ip::tcp::resolver::query query(m_sServerIp, m_sServerPort);
    boost::asio::ip::tcp::resolver::iterator endpoints = m_tResolver.resolve(query);
    LOG_INFO << m_sServiceName << "m_sServerIp:" << m_sServerIp << ", m_sServerPort:" << m_sServerPort;

    m_TimeoutTimer.expires_from_now(
        boost::posix_time::milliseconds(m_tDRSdkConnectParam.nConnectTimeoutMs)); // 1 second
    m_TimeoutTimer.async_wait([this](const boost::system::error_code &ec) {
        if (!ec)
        {
            LOG_ERR << "Connect timed out.";
            m_pSocket->close(); // 取消当前连接,防止阻塞
        }
    });

    boost::asio::async_connect(
        *m_pSocket, endpoints,
        boost::bind(&DrSdkTransportSocket::HandleConnect, this, boost::asio::placeholders::error));
}

void DrSdkTransportSocket::HandleConnect(const boost::system::error_code error)
{
    m_TimeoutTimer.cancel(); // 成功或失败都取消定时器
    if (!error)
    {
        LOG_INFO << m_sServiceName << "Connected to server!";
        m_bConnected = true;
        // set  TCP_NODELAY before connect
        m_pSocket->set_option(boost::asio::ip::tcp::no_delay(true));
        StartRead(); // Start asynchronous read after successful connect

        if (nullptr != m_pConnectedCallback)
        {
            m_pConnectedCallback(this->getSharedFromThis());
        }
    }
    else
    {
        LOG_ERR << m_sServiceName << "Failed to connect: " << "error_message_cannot_used";
        StartReconnect(); // Start reconnection process if connection fails
    }
}

void DrSdkTransportSocket::StartRead()
{
    // m_pSocket->async_read_some(boost::asio::buffer(m_RecvBufHead),
    //                            boost::bind(&DrSdkTransportSocket::HandleRead, this, boost::asio::placeholders::error,
    //                                        boost::asio::placeholders::bytes_transferred));
    if (m_bStop)
    {
        LOG_INFO << "Stop read head" << std::endl;
        return;
    }
    boost::asio::async_read(*m_pSocket, boost::asio::buffer(m_RecvBufHead.get(), sizeof(DataHeader)),
                            boost::asio::transfer_exactly(sizeof(DataHeader)), // Read exactly the size of DataHeader
                            boost::bind(&DrSdkTransportSocket::HandleReadHead, this, boost::asio::placeholders::error,
                                        boost::asio::placeholders::bytes_transferred));
}

void DrSdkTransportSocket::HandleReadHead(const boost::system::error_code &error, size_t bytes_transferred)
{
    if (!error)
    {
        // LOG_INFO << "HandleReadHead Received data, bytes_transferred:" << bytes_transferred << std::endl;
        // Process the received data
        auto *pHead = reinterpret_cast<DataHeader *>(m_RecvBufHead.get());
        LOG_INFO << m_sServiceName << "Received data, head:" << pHead->ToString();
        if (pHead->nLen > 0)
        {
            if (m_bStop)
            {
                LOG_INFO << m_sServiceName << "Stop read body" << std::endl;
                return;
            }
            if (pHead->nLen > m_RecvBufBodyLen)
            {
                LOG_ERR << m_sServiceName << "recv buffer length m_RecvBufBodyLen=" << m_RecvBufBodyLen
                        << " is less than pHead->nLen=" << pHead->nLen << std::endl;
                if (pHead->nLen > BUFFER_MAX_LENGTH)
                {
                    // Maybe it's misplaced
                    LOG_ERR << m_sServiceName << "recv buffer length pHead->nLen=" << pHead->nLen
                            << " is too large, max length is " << BUFFER_MAX_LENGTH << std::endl;
                    // abnormal ,shoud reconnect ??
                    // StartReconnect();
                }
                else
                {
                    m_RecvBufBodyLen = pHead->nLen;
                    m_RecvBufBody.reserve(m_RecvBufBodyLen);
                }
            }

            boost::asio::async_read(*m_pSocket, boost::asio::buffer(m_RecvBufBody.data(), pHead->nLen),
                                    boost::asio::transfer_exactly(pHead->nLen), // Read exactly the size of msg body
                                    boost::bind(&DrSdkTransportSocket::HandleReadBody, this,
                                                boost::asio::placeholders::error,
                                                boost::asio::placeholders::bytes_transferred));
        }
        else
        {
            int32 nMsgLen = sizeof(DataHeader);
            std::shared_ptr<char> pMsgBuf(new char[nMsgLen]);
            memcpy(pMsgBuf.get(), m_RecvBufHead.get(), sizeof(DataHeader));
            DataTuple tData = std::make_tuple(pMsgBuf, nMsgLen);
            Enqueue(tData);
            // Continue reading more data asynchronously
            StartRead();
        }
    }
    else
    {
        LOG_ERR << m_sServiceName << "Read error: " << "error_message_cannot_used";
        StartReconnect(); // If read fails, trigger reconnection
    }
}

void DrSdkTransportSocket::HandleReadBody(const boost::system::error_code &error, size_t bytes_transferred)
{
    if (!error)
    {
        LOG_INFO << m_sServiceName << "Received data, bytes_transferred:" << bytes_transferred;
        auto *pHead = reinterpret_cast<DataHeader *>(m_RecvBufHead.get());
        if (m_bAvailable || CanTransportTypeWhenUnavailable(pHead))
        {
            int32 nMsgLen = sizeof(DataHeader) + bytes_transferred;
            std::shared_ptr<char> pMsgBuf(new char[nMsgLen]);
            memcpy(pMsgBuf.get(), m_RecvBufHead.get(), sizeof(DataHeader));
            memcpy(pMsgBuf.get() + sizeof(DataHeader), m_RecvBufBody.data(), bytes_transferred);
            DataTuple tData = std::make_tuple(pMsgBuf, nMsgLen);

            if (SDK_HEARTBEAT == pHead->nType)
            {
                DealHeartBeat(tData);
            }
            else
            {
                Enqueue(tData);
            }
        }
        else
        {
            LOG_ERR << m_sServiceName << "transport is unavailable, ignore." << pHead->ToString();
        }
        // Continue reading more data asynchronously
        StartRead();
    }
    else
    {
        LOG_ERR << "Read error: " << "error_message_cannot_used" << std::endl;
        StartReconnect(); // If read fails, trigger reconnection
    }
}

bool DrSdkTransportSocket::Enqueue(DataTuple &tData)
{
    if (m_bStop)
    {
        return false; // If the stop flag is set, return false
    }
    std::lock_guard<std::mutex> lock(m_Mutex);
    m_SendQueue.emplace(std::move(tData));
    m_Cv.notify_one(); // Notify one waiting thread
    return true;
}

bool DrSdkTransportSocket::Dequeue(DataTuple &tData)
{
    std::unique_lock<std::mutex> lock(m_Mutex);
    // Wait until the queue is not empty
    m_Cv.wait(lock, [this] { return m_bStop || !m_SendQueue.empty(); });
    if (m_bStop)
    {
        return false; // If the stop flag is set, return false
    }
    tData = m_SendQueue.front();
    m_SendQueue.pop();
    return true;
}

int32 DrSdkTransportSocket::SendData(const char *pSendBuf, int32 nLenBuf)
{
    // Once connected, send
    if (!m_bConnected || m_bStop)
    {
        LOG_ERR << m_sServiceName << "Not connected or m_bStop, can't send data" << std::endl;
        return EC_DSF_SDK_COMMON_ERROR;
    }
    // sync
    //  boost::asio::write(*m_pSocket, boost::asio::buffer(pSendBuf, nLenBuf));

    // async
    boost::asio::async_write(*m_pSocket, boost::asio::buffer(pSendBuf, nLenBuf),
                             boost::bind(&DrSdkTransportSocket::HandleSend, this, boost::asio::placeholders::error,
                                         boost::asio::placeholders::bytes_transferred));

    return EC_DSF_SDK_SUCCESS;
}

void DrSdkTransportSocket::HandleSend(const boost::system::error_code &error, size_t bytes_transferred)
{
    if (!error)
    {
        LOG_INFO << m_sServiceName << "Sent data successfully, bytes_transferred:" << bytes_transferred << std::endl;
    }
    else
    {
        LOG_ERR << m_sServiceName << "Send error: " << "error_message_cannot_used" << std::endl;
        StartReconnect(); // If send fails, trigger reconnection
    }
}

int32 DrSdkTransportSocket::RecvData(std::shared_ptr<char> &pRecvBuf, int32 &pBufLen)
{
    DataTuple tData;
    if (!Dequeue(tData))
    {
        LOG_ERR << m_sServiceName << "RecvData Dequeue failed" << std::endl;
        return EC_DSF_SDK_COMMON_ERROR;
    }

    auto &pRecvData = std::get<0>(tData);
    auto nRecvLen = std::get<1>(tData);
    // std::memcpy(pRecvBuf, pRecvData.get(), nRecvLen);
    pRecvBuf = pRecvData;
    pBufLen = nRecvLen;

    return EC_DSF_SDK_SUCCESS;
}

void DrSdkTransportSocket::StartReconnect()
{
    if (m_bStop)
    {
        return; // If the socket is stopped, do not attempt to reconnect
    }
    m_bAvailable.store(false); // Set the connection can't be used
    m_Cv.notify_all();         // Notify all waiting threads, Dequeue will return false

    // Wait for 5 seconds before attempting to reconnect
    m_ReconnectTimer.expires_from_now(boost::posix_time::milliseconds(1000)); // 1 second
    m_ReconnectTimer.async_wait(
        boost::bind(&DrSdkTransportSocket::HandleReconnect, this, boost::asio::placeholders::error));
}

void DrSdkTransportSocket::HandleReconnect(const boost::system::error_code &error)
{
    if (!error)
    {
        LOG_ERR << m_sServiceName << "Reconnecting...,m_bConnected=" << m_bConnected.load();
        Close(); // Close existing socket if necessary
        // m_pSocket = std::make_unique<boost::asio::ip::tcp::socket>(m_IoService); // Create a new socket
        m_pSocket.reset(new boost::asio::ip::tcp::socket(m_IoService));
        StartConnect(); // Try to reconnect
    }
}

void DrSdkTransportSocket::Stop()
{
    m_bStop.store(true);
    m_Cv.notify_all();
    Close();
    m_IoService.stop(); // Stop the io_service
}

void DrSdkTransportSocket::SetConnectedCallback(
    const std::function<void(std::shared_ptr<DrSdkTransport>)> &pConnectedCallback)
{
    m_pConnectedCallback = pConnectedCallback;
}

bool DrSdkTransportSocket::GetConnectStatus() const
{
    return m_bConnected;
}

void DrSdkTransportSocket::DealHeartBeat(const DataTuple &tData)
{
    std::shared_ptr<char> pSendBuf(new char[sizeof(DataHeader)]);

    auto &pRecvData = std::get<0>(tData);
    auto nRecvLen = std::get<1>(tData);
    DataHeader *pHead = (DataHeader *)pRecvData.get();
    char *pContent = (char *)pRecvData.get() + sizeof(DataHeader);
    std::string strContent(pContent, pHead->nLen);

    LOG_INFO << m_sServiceName << " DealHeartBeat recv content: " << strContent;
    bool bJsonCheck = true;

    m_nLastHeartBeatTimeMs = std::chrono::steady_clock::now();
    try
    {
        json parsedJson = json::parse(strContent);
        if (parsedJson.contains("rm_status"))
        {
            uint8 rm_status = parsedJson["rm_status"];
            m_RmStatus = static_cast<SDKRMStatus>(rm_status);
            m_bAvailable.store(SDKRMStatus::SDKRM_STATUS_ACTIVE == m_RmStatus);
        }
        else
        {
            LOG_ERR << "rm_status not exist";
            bool bJsonCheck = false;
        }
    }
    catch (const nlohmann::json::parse_error &e)
    {
        LOG_ERR << "JSON parse error: " << e.what();
        bool bJsonCheck = false;
    }

    if (!bJsonCheck)
    {
        pHead->nFlag = 1;
    }
    auto nCuntTimeMs = NowMillisecond();
    pHead->nLen = 0;
    pHead->nLongReserve1 = nCuntTimeMs / 1000;
    pHead->nLongReserve2 = nCuntTimeMs % 1000;

    memcpy(pSendBuf.get(), pHead, sizeof(DataHeader));
    int32 nRet = SendData(pSendBuf.get(), sizeof(DataHeader));
    if (0 != nRet)
    {
        LOG_ERR << "DealHeartBeat send data failed. length = " << sizeof(DataHeader) << ", nRet = " << nRet
                << std::endl;
    }
}

void DrSdkTransportSocket::CheckHeartBeatTimeOut(const boost::system::error_code &error)
{
    if (m_bStop)
    {
        return; // If the socket is stopped, do not check
    }

    auto nDiff = std::chrono::steady_clock::now() - m_nLastHeartBeatTimeMs;
    if (nDiff > std::chrono::milliseconds(m_tDRSdkConnectParam.nHeartbeatTimeoutMs))
    {
        // Set the connection can't be used
        if (m_bAvailable.exchange(false))
        {
            // connection timeout ,close socket
            Close();
            LOG_ERR << m_sServiceName << "Heartbeat timeout, m_bAvailable set false ,and close socket";
        }
    }
    if (!m_bAvailable.load())
    {
        m_Cv.notify_one(); // Notify one waiting thread, Dequeue will return false
    }

    m_HeartBeatTimer.expires_from_now(boost::posix_time::milliseconds(500));
    m_HeartBeatTimer.async_wait(
        boost::bind(&DrSdkTransportSocket::CheckHeartBeatTimeOut, this, boost::asio::placeholders::error));
}

} // namespace dsfapi
