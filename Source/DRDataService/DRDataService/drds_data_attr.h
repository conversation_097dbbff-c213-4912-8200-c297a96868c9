#pragma once

#include <vector>
#include <string>
#include <unordered_map>
#include "data_type.h"
#include "drds_ngvs_config.h"

class DRDSDataAttr
{
public:
    ~DRDSDataAttr();
    inline static DRDSDataAttr &getInstance()
    {
        static DRDSDataAttr instance;
        return instance;
    }
    void Init(const uint32_t IONumber);

    ObjectAttr* GetAttrByName(std::string name);
    std::vector<ObjectAttr>* GetAttrElementByName(std::string name);

private:
    std::unordered_map<std::string, ObjectAttr> dataAttrMap;
    std::unordered_map<std::string, std::vector<ObjectAttr>> dataAttrMapVec;
private:
    DRDSDataAttr();

private:
    uint32_t m_IONumber;

};