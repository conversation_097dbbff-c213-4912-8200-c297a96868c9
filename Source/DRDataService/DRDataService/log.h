#pragma once
#include "logger.h"
#include <chrono>
#include <iostream>
#include <sstream>
#include <sys/syscall.h>
#include <unistd.h>

inline void logInit(std::string logPath)
{
  Logger::Instance().setFlushOnLevel(Logger::info);
  Logger::Instance().Init(logPath, Logger::both, Logger::info, 60, 10); 
}



inline void logUninit()
{
  Logger::Instance().Uinit();
}

#define DSF_LOG(level)   LOG(level) 
#define DSF_LOG_TIME(level, interval_ms)   LOG_TIME(level, interval_ms)