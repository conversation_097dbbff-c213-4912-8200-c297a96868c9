#pragma once
#include <string>

#include "config.h"

class DRDSConfig {
 private:
  struct DRDSConfigData {
    uint16_t dsfApiPort;
    std::string dsfRedisUnixSockPath;
    uint16_t dsfRedisPort;
    uint8_t isRedisConectType;
    uint32_t threadPoolNum;
    uint32_t dsfRmStatusGetInterval;
    uint32_t dsfRmStatus;
  };

 public:
  static DRDSConfig& getInstance();
  void init(std::string configPath);
  void Uinit();
  ~DRDSConfig();

 public:
  uint16_t getDsfApiPort() const;
  uint16_t getDsfRedisPort() const;
  uint32_t getThreadPoolNum() const;
  uint32_t getDsfRmStatusGetInterval() const;
  uint8_t getIsRedisConectType() const;
  std::string getDsfRedisUnixSockPath() const;
  uint32_t getDsfRmstatus() const;

 private:
  Config* m_config = nullptr;
  DRDSConfigData m_configData;
  DRDSConfig();
  void ConfigDataInit();
};
