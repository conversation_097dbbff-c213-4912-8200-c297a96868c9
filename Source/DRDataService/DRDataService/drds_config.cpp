#include "drds_config.h"
#

DRDSConfig::DRDSConfig()
{


}

DRDSConfig::~DRDSConfig()
{
    if (m_config)
    {
        delete m_config;
        m_config = nullptr;
    }
}

DRDSConfig& DRDSConfig::getInstance()
{
    static DRDSConfig instance;
    return instance;
}

void DRDSConfig::init(std::string configPath)
{
    m_config = new Config(configPath);
    m_config->load();
    ConfigDataInit();
}

void DRDSConfig::ConfigDataInit()
{
    m_configData.dsfApiPort = m_config->getInt("dsf_api_port", 1234);
    m_configData.dsfRedisPort = m_config->getInt("dsf_redis_port", 6380);
    m_configData.threadPoolNum = m_config->getInt("thread_pool_num", 32);  
    m_configData.dsfRmStatusGetInterval = m_config->getInt("dsf_rm_status_get_interval", 100);
    m_configData.isRedisConectType = m_config->getInt("redis_conect_type", 0);
    m_configData.dsfRedisUnixSockPath = m_config->getString("dsf_redis_unix_sock_path", "/tmp/redis.sock");
    m_configData.dsfRmStatus = m_config->getInt("RmStatus", 0xff);
}

uint16_t DRDSConfig::getDsfApiPort() const
{
    return m_configData.dsfApiPort;
}

uint16_t DRDSConfig::getDsfRedisPort() const
{
    return m_configData.dsfRedisPort;
}

uint32_t DRDSConfig::getThreadPoolNum() const
{
    return m_configData.threadPoolNum;
}

uint32_t DRDSConfig::getDsfRmStatusGetInterval() const
{
    return m_configData.dsfRmStatusGetInterval;
}

uint8_t DRDSConfig::getIsRedisConectType() const
{
    return m_configData.isRedisConectType;
}

std::string DRDSConfig::getDsfRedisUnixSockPath() const
{
    return m_configData.dsfRedisUnixSockPath;
}

uint32_t DRDSConfig::getDsfRmstatus() const
{
    return m_configData.dsfRmStatus;
}
