cmake_minimum_required(VERSION 3.10)
############FOR_MODIFIY_BEGIN#######################
PROJECT (DSFDataService)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)


INCLUDE($ENV{DRDIR}CMakeCommon)


set(DSF_BUILD_TYPE "default" CACHE STRING "Custom build type")

# ?? DSF_BUILD_TYPE ??????
if(DSF_BUILD_TYPE STREQUAL "debug")
    message("Building with DSF_DEBUG enabled")
    add_compile_definitions(DSF_DEBUG)
elseif(DSF_BUILD_TYPE STREQUAL "release")
    message("Building in release mode")
else()
    message("Building with default settings")
endif()

file(GLOB DDS_SHM_HELLOWORLD_EXAMPLE_SOURCES_CPP "${CMAKE_CURRENT_SOURCE_DIR}/*.cpp")
message(STATUS "Collected CPP sources: ${DDS_SHM_HELLOWORLD_EXAMPLE_SOURCES_CPP}")
set(ALL_SOURCES  ${DDS_SHM_HELLOWORLD_EXAMPLE_SOURCES_CPP})
set(MAIN_SOURCES ${ALL_SOURCES})
set(TEST_SOURCES ${ALL_SOURCES})

SET(SRCS ${SRCS} ${MAIN_SOURCES})

SET(TARGET_NAME dsfdataservice)

SET(LINK_LIBS 
            log
            pthread
            DSF 
            fastrtps
            fastcdr
            tinyxml2
            hiredis
            drcomm 
            drnetqueue 
            servicebase 
            shmqueue  licverify License
            drrmapi
            z)

# ############FOR_MODIFIY_END#########################

INCLUDE($ENV{DRDIR}CMakeCommonExec)
