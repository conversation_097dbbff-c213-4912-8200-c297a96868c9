#include "DRDeployApi.h"
#include <mutex>
#include "common/NetQueue.h"
#include "errcode/error_code.h"
#include <iostream>

#ifdef _WIN32
#include <winsock2.h>
#include <iphlpapi.h>
#include <ws2tcpip.h>
#pragma comment(lib, "ws2_32.lib")
#pragma comment(lib, "iphlpapi.lib")
#else
#include <ifaddrs.h>
#include <netinet/in.h>
#include <arpa/inet.h>
#include <cstring>
#endif

typedef enum
{
    DSF_CMD_UNKNOW,
    DSF_CMD_GET_CONFIG_FILE,
    DSF_CMD_GET_IP_LIST,
    /*.....add*/
} DSF_CMD_TYPE_E;


#pragma pack(4)
typedef struct
{
    uint16_t uType;
    uint32_t result;
} DataHead;


#define DSF_DRDEPLOY_DEFAULT_NODEQUEUEID 2001

std::mutex g_mutex;

static void init() __attribute__((constructor));
static void uninit() __attribute__((destructor));

static void init()
{
    long lRet = CVNDK_Init(SYNC_SEND);
}

static void uninit()
{
    long lRet = CVNDK_Finalize();
}

int WriteData(const char *IPAddr, unsigned short port, const char *buf, unsigned int len, int timeout)
{
    std::lock_guard<std::mutex> guard(g_mutex);

    HQUEUE hRemoteQueue = CVNDK_RegRemoteQueue(DSF_DRDEPLOY_DEFAULT_NODEQUEUEID, IPAddr, port);
    if (NULL == hRemoteQueue)
        return EC_DSF_DP_API_PARSE_IPANDPORT;

    long lRet = CVNDK_Connect(hRemoteQueue);
    if (lRet != ICV_SUCCESS)
    {
        return EC_DSF_DP_API_CONNECTION;
    }

    HQUEUE hLocalQue = CVNDK_RegLocalQueue(2002);
    if (!hLocalQue)
    {
        return EC_DSF_DP_API_SEND_DATA;
    }

    HQUEUE hCliQue = 0;
    DataHead ReqDataHeader{};
    ReqDataHeader.uType = DSF_CMD_GET_CONFIG_FILE;
    char *pszResponse = NULL;
    long lRcvLen = 0;
    uint32_t totalSize = sizeof(DataHead) + len;
    char *buffer = new char[totalSize]; 

    memcpy(buffer, &ReqDataHeader, sizeof(DataHead));
    memcpy(buffer + sizeof(DataHead), buf, len);

    lRet = CVNDK_Send(hRemoteQueue, hLocalQue, buffer, totalSize);
    if (lRet != ICV_SUCCESS)
    {
        return EC_DSF_DP_API_SEND_DATA;
    }
    delete[] buffer;
    lRet = CVNDK_Recv(hLocalQue, &hCliQue, (void **)&pszResponse, lRcvLen, timeout);
    if (lRet != ICV_SUCCESS)
    {
        CVNDK_Free((void *&)pszResponse);
        return EC_DSF_DP_API_GET_STATUS;
    }

    DataHead AckDataHeader{};
    AckDataHeader = *(DataHead *)pszResponse;
    int status = AckDataHeader.result;
    if (0 != status)
        status = EC_DSF_DP_API_UNZIPFILE;

    lRet = CVNDK_DisConnect(hRemoteQueue);

    CVNDK_Free((void *&)pszResponse);
    CVNDK_ReleaseQueue(hRemoteQueue);
    CVNDK_ReleaseQueue(hLocalQue);

    return status;
}


bool getDsfIPAddresses(const char *IPAddr, unsigned short port, std::vector<std::string> &ipAddrList, int timeout)
{
    HQUEUE hRemoteQueue = CVNDK_RegRemoteQueue(DSF_DRDEPLOY_DEFAULT_NODEQUEUEID, IPAddr, port);
    if (NULL == hRemoteQueue)
        return EC_DSF_DP_API_PARSE_IPANDPORT;

    long lRet = CVNDK_Connect(hRemoteQueue);
    if (lRet != ICV_SUCCESS)
    {
        std::cerr << "CVNDK_Connect err: " << std::endl;
        return false;
    }

    HQUEUE hLocalQue = CVNDK_RegLocalQueue(1);
    if (!hLocalQue)
    {
        return false;
    }

    HQUEUE hCliQue = 0;
    DataHead ReqDataHeader{};
    long lRcvLen = 0;
    ReqDataHeader.uType = DSF_CMD_GET_IP_LIST;
    lRet = CVNDK_Send(hRemoteQueue, hLocalQue, &ReqDataHeader, sizeof(ReqDataHeader));
    if (lRet != ICV_SUCCESS)
    {
        std::cerr << "CVNDK_Send err: " << std::endl;
        return false;
    }

    DataHead AckDataHeader{};
    char *pszResponse = NULL;
    lRet = CVNDK_Recv(hLocalQue, &hCliQue, (void **)&pszResponse, lRcvLen, timeout);
    if (lRet != ICV_SUCCESS)
    {
        std::cerr << "CVNDK_Recv err: " << std::endl;
        CVNDK_Free((void *&)pszResponse);
        return false;
    }
    AckDataHeader = *(DataHead *)pszResponse;
    char *pData = pszResponse + sizeof(AckDataHeader);

    size_t offset = 0; 

    while (offset + sizeof(uint32_t) <= lRcvLen - sizeof(AckDataHeader))
    {
        uint32_t ipLength = 0;
        memcpy(&ipLength, pData + offset, sizeof(uint32_t));
        offset += sizeof(uint32_t);

        if (offset + ipLength > lRcvLen - sizeof(AckDataHeader))
        {
            std::cerr << "Invalid IP length: " << ipLength << std::endl;
            break; 
        }

        std::string ip(pData + offset, ipLength);
        offset += ipLength;
        ipAddrList.push_back(ip);
    }

    lRet = CVNDK_DisConnect(hRemoteQueue);

    CVNDK_Free((void *&)pszResponse);
    CVNDK_ReleaseQueue(hRemoteQueue);
    CVNDK_ReleaseQueue(hLocalQue);

    return AckDataHeader.result;
}


// int main(int argc, char *argv[])
// {
//     std::cout << "++++: " << argv[0] << std::endl;
//     std::vector<std::string> ipAddresses;
//     if (getDsfIPAddresses("127.0.0.1", 60100, ipAddresses, 3000))
//     {
//         std::cout << "IP Addresses:" << std::endl;
//         for (const auto &ip : ipAddresses)
//         {
//             std::cout << "  " << ip << std::endl;
//         }
//     }
//     else
//     {
//         std::cerr << "Failed to retrieve IP addresses." << std::endl;
//     }

//     int iret = WriteData("127.0.0.1", 60100, "hello world", 12, 3000);
//     if (iret != 0)
//     {
//         std::cerr << "Failed to write data." << std::endl;
//     }
//     return 0;
// }
