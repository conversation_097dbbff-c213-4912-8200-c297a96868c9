#ifndef _DRDEPLOY_API_H_
#define _DRDEPLOY_API_H_

#include <vector>
#include <string>

#ifdef _WIN32
    #ifdef DRDEPLOY_EXPORTS
        #define DR_API extern "C" __declspec(dllexport)
    #else
        #define DR_API extern "C" __declspec(dllimport)
    #endif  // End of #ifdef DRDEPLOY_EXPORTS
#else
    #define DR_API extern "C"
#endif // End of #ifdef _WIN32


// #define DSF_ERR_DEPLOY_IPANDPORT_PARSE     1       // 输入IP地址或者端口号解析失败
// #define DSF_ERR_DEPLOY_CONNECTION          2       // 连接到DSF节点失败
// #define DSF_ERR_DEPLOY_SEND_DATA           3       // 发送数据失败
// #define DSF_ERR_DEPLOY_GET_STATUS          4       // 获取DSF节点返回信息失败
// #define DSF_ERR_DEPLOY_UNZIPFILE           5       // 部署节点解压文件失败

/**
 *  向DSF节点发送数据
 *
 *  @param  -[in]  char* IPAddr: DSF节点的IP地址
 *  @param  -[in]  unsigned short port: DSF节点的端口号
 *  @param  -[in]  const char* buf: 要发送的数据
 *  @param  -[in]  int len: 发送数据的长度
 *  @param  -[in]  int timeout: ms, 设置连接与数据交互的超时时间，默认无超时时间
 * 
 *  @return 0表示发送成功
 */
DR_API int WriteData(const char* IPAddr, unsigned short port, const char* buf, unsigned int len, int timeout = -1);



/**
 * @brief Get the Dsf I P Addresses object
 * @param  IPAddr       ip address
 * @param  port         port
 * @param  ipAddrList    out result list
 * @param  timeout      wait time
 * @return true         success
 * @return false        failed
 */
DR_API  bool getDsfIPAddresses(const char *IPAddr, unsigned short port, std::vector<std::string> &ipAddrList, int timeout);

#endif // End of #ifndef _DRDEPLOY_API_H_



