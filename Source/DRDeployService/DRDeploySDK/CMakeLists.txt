cmake_minimum_required(VERSION 3.10)
############FOR_MODIFIY_BEGIN#######################
PROJECT (DRDeploySDK)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} DRDeployApi.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME dsfdeploysdk)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS ACE drnetqueue intl)
# IF(UNIX)
# 	IF(HPUX)
# 		SET(LINK_LIBS ${LINK_LIBS} pthread)
# 	ENDIF(HPUX)
	
# 	IF(CMAKE_SYSTEM MATCHES "SunOS.*")
# 		SET(LINK_LIBS ${LINK_LIBS} socket)
# 	ENDIF(CMAKE_SYSTEM MATCHES "SunOS.*")
# ENDIF(UNIX)
# if( CMAKE_SIZEOF_VOID_P EQUAL 4 )
# 	IF(CMAKE_SYSTEM MATCHES "Linux")
# 		SET(LINK_LIBS ${LINK_LIBS} SentinelKeys32)
# 	ENDIF(CMAKE_SYSTEM MATCHES "Linux")
# endif( CMAKE_SIZEOF_VOID_P EQUAL 4 )

# ############FOR_MODIFIY_END#########################

INCLUDE($ENV{DRDIR}CMakeCommonLib)

# IF(MSVC)
# 	if( CMAKE_SIZEOF_VOID_P EQUAL 8 )
# 		set_target_properties(${TARGET_NAME} PROPERTIES STATIC_LIBRARY_FLAGS "/machine:x64")
# 	endif( CMAKE_SIZEOF_VOID_P EQUAL 8 )	
# ENDIF(MSVC)