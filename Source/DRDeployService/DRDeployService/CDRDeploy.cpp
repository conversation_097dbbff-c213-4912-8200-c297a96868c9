#include "CDRDeploy.h"
#include "CmdProcessTask.h"
#include "RecFileLink.h"
#include "gettext/libintl.h"

#define _(STRING) gettext(STRING)

CCVLog g_DSFDeployLog;


CDRDeploy::CDRDeploy() : CServiceBase("dsfdeployservice", false, "")
{
    ExitWhenInitFailed(false);
}

long CDRDeploy::Init( int argc, char* args[] )
{
	return RECFILE_LINK->InitializeConn();
}

void CDRDeploy::Refresh()
{
}

long CDRDeploy::Start()
{
	CMD_PROCESS_TASK->StartTask();
	return 0;
}

void CDRDeploy::PrintStartUpScreen()
{
	PrintHelpScreen();
}

void CDRDeploy::PrintHelpScreen()
{
	printf("\n");
	printf("+=================================================================+\n");
	printf(_("|                <<Welcome to dsfdeployservcie! >>                |\n"));
	printf(_("|  You can configure this service by entering following commands  |\n"));
	printf(_("|  q/Q:Quit                                                       |\n"));
	printf(_("|  Others:Print tips                                              |\n"));
	printf("+=================================================================+\n");
}

long CDRDeploy::Fini()
{
	CMD_PROCESS_TASK->StopTask();
	CMD_PROCESS_TASK->wait();

	RECFILE_LINK->UnInitializeConn();

    g_DSFDeployLog.StopLogThread();
	return ICV_SUCCESS;
}

bool CDRDeploy::ProcessCmd(char c)
{
	return 0;
}

void CDRDeploy::Misc()
{
}

void CDRDeploy::SetLogFileName()
{
	g_DSFDeployLog.SetLogFileNameThread("DSFDeployService");
}

CServiceBase* g_pServiceHandler = new CDRDeploy();
