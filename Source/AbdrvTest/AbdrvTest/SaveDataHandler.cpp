/*  Filename:    SaveDataHandler.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Declare CSaveDataHandler
 *
 *  @author:     huangcan
 *  @version:    10/08/2024	huangcan	Initial Version
 **************************************************************/

#include "SaveDataHandler.h"
#include "common/LogHelper.h"
#include "NetWrapper.h"
#include "processdb/CastHelper.h"
#include <iostream>
#include <sstream>
#include <set>
extern CCVLog g_asLog;

CSaveDataHandler::CSaveDataHandler(CNetWrapper* netWrapper)
: m_netWrapper(netWrapper)
{
	m_bExit = false;
	// m_redisObject = DSFRedisWrapper::getInstance("127.0.0.1",6379);
	m_redisObject = DSFRedisWrapper::getInstance("127.0.0.1",6380);
}

CSaveDataHandler::~CSaveDataHandler()
{

}

int CSaveDataHandler::svc( void )
{
	long nRet = ICV_SUCCESS;
	ACE_Time_Value tv_wait;
	tv_wait.set_msec(1000);
	size_t lastRecTime = time(NULL);

	while(!m_bExit)
	{
		ACE_Message_Block* pMsg = NULL;
		ACE_Time_Value tv_abs = ACE_OS::gettimeofday() + tv_wait;
		nRet = m_netWrapper->m_dataQueue.dequeue(pMsg, &tv_abs);
		if (nRet != ICV_SUCCESS || NULL == pMsg)
		{
			continue;
		}
		
		CV_CHECK_TRUE_LOG_CONTINUE(g_asLog, pMsg->length() < sizeof(TProtoDriverAPIHeader),  -1, "invalid cmd len %d", pMsg->length());
		
		TProtoDriverAPIHeader header;
		memcpy(&header, pMsg->rd_ptr(), sizeof(header));

		switch (header.m_nCmdType)
		{
			case DRIVERAPI_CMD_SAVE_DATA:
				{
					std::vector<TProtoIDVTQ> v_msg;
					nRet = proto_driverapi_save_data_unpack(pMsg->rd_ptr() + sizeof(header), pMsg->length() - sizeof(header), &v_msg);
					if (nRet != ICV_SUCCESS)
					{
						CV_ERROR(g_asLog, nRet, "proto_driverapi_save_data_unpack failed lRet = %d len %d",nRet, pMsg->length());
						pMsg->release();
						continue;
					}

					for(std::vector<TProtoIDVTQ>::size_type i = 0; i < v_msg.size(); i ++)
					{
						double dValue = 0;
						CastBuffer2POD_f(v_msg[i].m_nDataType, v_msg[i].m_pBuf, dValue);
						std::cout<<"id:"<<v_msg[i].m_nTagID<<" type:"<<static_cast<int>(v_msg[i].m_nDataType)<<" dValue:"<<dValue<<std::endl;


						unsigned char* byteBuffer = (unsigned char*)v_msg[i].m_pBuf;
						for (size_t i = 0; i < v_msg[i].m_nLenBuf; ++i) {
							printf("%02X ", byteBuffer[i]);
						}
						printf("\n");

						if(m_idToObjMap.find(v_msg[i].m_nTagID)==m_idToObjMap.end())
						{
							CV_ERROR(g_asLog, -1, "invalid tag id %d", v_msg[i].m_nTagID);
							continue;
						}
						else
						{
							for(size_t j = 0; j < m_idToObjMap[v_msg[i].m_nTagID].size(); j++)
							{
								std::string objName = m_idToObjMap[v_msg[i].m_nTagID][j];
								// unsigned char data1[1024]={0};
								// m_value++;
								// memcpy(reinterpret_cast<void*>(data1),reinterpret_cast<void*>(&m_value),2);
								// m_redisObject->setValue(objName, reinterpret_cast<unsigned char*>(data1) ,v_msg[i].m_nLenBuf);
								unsigned char* m_data = 
									m_objMap[objName]->setValue(v_msg[i].m_nTagID,v_msg[i].m_pBuf,v_msg[i].m_nLenBuf);
								
								if(m_data!=nullptr)
								{
									m_redisObject->setValue(objName, m_data ,v_msg[i].m_nLenBuf);
									// std::cout<<"set value to redis success"<<std::endl;
								}
							}
						}
					}	
				}
				break;
			case DRIVERAPI_CMD_SET_BLOCKQUALITY:
				{
					CV_DEBUG(g_asLog, "recv %d bytes from driver for block quality, I ignore it", pMsg->length());
				}
				break;
			case DRIVERAPI_CMD_SAVE_DRV_STATUS:
				{
					CV_DEBUG(g_asLog, "recv %d bytes from driver for drv status, I ignore it", pMsg->length());
				}
				break;
			case DRIVERAPI_CMD_SAVE_DEV_STATUS:
				{
					CV_DEBUG(g_asLog, "recv %d bytes from driver for dev status, I ignore it", pMsg->length());
				}
				break;
			default:
				CV_ERROR(g_asLog, -1, "invalid cmd type %d", header.m_nCmdType);
				break;
		}	
		pMsg->release();
	}
	CV_INFO(g_asLog, "thread CSaveDataHandler stop");
	return 0;
}

bool CSaveDataHandler::Initialize()
{
	std::string m_strCfgFile = CVComm.GetCVProjCfgPath();
    CV_INFO(g_asLog, "Loading  ngvs config file: %s",m_strCfgFile.c_str());

	// m_objectHandler = new DSFObjectHandler();

	// if(!g_config_reader.loadConfig(m_objectHandler))
	// {
	// 	return false;
	// }


	DRDSNgvsParse::getInstance().init(m_strCfgFile);

    //init object struct
    const unordered_map<string, LocalVaribale> GlobeLocalVaribales = DRDSNgvsParse::getInstance().GetGetLocalVariable();

    std::set<std::string> strObjectSet;
    std::map<std::string,int> variableNameToIDMap;

    for(auto& item : GlobeLocalVaribales)
    {
        std::stringstream ss(item.second.Name);
        std::string strSplitItem;
        std::vector<std::string> strNames;
        while (std::getline(ss, strSplitItem, '.')) 
        {
            strSplitItem.erase(0, strSplitItem.find_first_not_of(' '));
            strSplitItem.erase(strSplitItem.find_last_not_of(' ') + 1);
            if (!strSplitItem.empty()) 
            {
                strNames.push_back(strSplitItem);
            }
        }
        std::string strObjName = strNames[0] + "." + strNames[1];
        //save all 2td level object for sending to redis
        strObjectSet.insert(strObjName);

        if(m_idToObjMap.find(item.second.tagId)==m_idToObjMap.end())
        {
            m_idToObjMap[item.second.tagId] = std::vector<std::string>();
            m_idToObjMap[item.second.tagId].push_back(item.second.Name);
        }
        else
        {
            m_idToObjMap[item.second.tagId].push_back(item.second.Name);
        }


        if(variableNameToIDMap.find(item.second.Name)==variableNameToIDMap.end())
        {
            variableNameToIDMap[item.second.Name] = item.second.tagId;
        }
    }

    for(auto& strObj : strObjectSet)
    {
        DSFSimpleObject* pObj = new DSFSimpleObject();
        pObj->name = strObj;

        std::vector<ObjectAttr> attrs;
        DRDSNgvsParse::getInstance().GetDataAttrforLocalObject(strObj,attrs);

        pObj->attrs = attrs;

		pObj->variableNameToIDMap = variableNameToIDMap;

		m_objMap.insert(std::pair<std::string,DSFSimpleObject*>(strObj,pObj));
    }


	return true;
}


long CSaveDataHandler::Activate()
{
	
	this->activate();
	return ICV_SUCCESS;
}

void CSaveDataHandler::ShutDown()
{
	m_bExit = true;
	this->wait();
}
