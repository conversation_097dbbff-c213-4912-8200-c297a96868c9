#include <iostream>
#include <sstream>
#include "ace/Time_Value.h"
#include "NetWrapper.h"
#include "processdb/CastHelper.h"
#include "common/CVLog.h"

CCVLog g_asLog;

int main()
{
    bool write_stop = false;
    CNetWrapper *pNetWrapper = new CNetWrapper();
    pNetWrapper->InitializeNetwork();
    pNetWrapper->Activate();

    std::cout<<"===============start writing !!!============="<<std::endl;
    while(!write_stop){
        ACE_Message_Block *pMsg = NULL;
        ACE_Time_Value tv_wait;
        ACE_Time_Value tv_abs = ACE_OS::gettimeofday() + tv_wait;
        int nRet = pNetWrapper->m_dataQueue.dequeue(pMsg, &tv_abs);
        if (nRet != ICV_SUCCESS || NULL == pMsg){
            continue;
        }
        if(pNetWrapper!=nullptr){
            TProtoDriverAPICTRLMsg msg;
            int8_t newValue = 3;
            msg.m_nDataType = 10;
            msg.m_nLenBuf = sizeof(newValue);
            msg.m_nTagID = 28;
            ACE_Time_Value start = ACE_OS::gettimeofday();
            msg.m_nTimeOutSec = start.sec()+1;
            msg.m_nTimeOutMs = start.usec()/1000;
            msg.m_pBuf = new char[msg.m_nLenBuf];
            memcpy(msg.m_pBuf, &newValue, msg.m_nLenBuf);
            int res = pNetWrapper->SendCtrlToDriver("abdrv",msg);//get driver type
            std::cout<<"writing res:" << res << std::endl;
            write_stop = true;
        }
        pMsg->release();
    }
    
    bool running = true;
    int save_data_count = 0; // 添加计数器
    while (1)
    {
        ACE_Message_Block *pMsg = NULL;
        ACE_Time_Value tv_wait;
        ACE_Time_Value tv_abs = ACE_OS::gettimeofday() + tv_wait;
        int nRet = pNetWrapper->m_dataQueue.dequeue(pMsg, &tv_abs);
        if (nRet != ICV_SUCCESS || NULL == pMsg)
        {
            continue;
        }

        TProtoDriverAPIHeader header;
        memcpy(&header, pMsg->rd_ptr(), sizeof(header));

        switch (header.m_nCmdType)
        {
        case DRIVERAPI_CMD_SAVE_DATA:
        {
            std::vector<TProtoIDVTQ> v_msg;
            nRet = proto_driverapi_save_data_unpack(pMsg->rd_ptr() + sizeof(header), pMsg->length() - sizeof(header), &v_msg);
            if (nRet != ICV_SUCCESS)
            {
                std::cout<< "proto_driverapi_save_data_unpack failed lRet = "<< nRet << "len " << pMsg->length();
                pMsg->release();
                continue;
            }

            for (std::vector<TProtoIDVTQ>::size_type i = 0; i < v_msg.size(); i++)
            {
                double dValue = 0;
                CastBuffer2POD_f(v_msg[i].m_nDataType, v_msg[i].m_pBuf, dValue);
                std::cout << "id:" << v_msg[i].m_nTagID << " type:" << static_cast<int>(v_msg[i].m_nDataType) << " dValue:" << dValue << std::endl;

                unsigned char *byteBuffer = (unsigned char *)v_msg[i].m_pBuf;
                for (size_t i = 0; i < v_msg[i].m_nLenBuf; ++i)
                {
                    printf("%02X ", byteBuffer[i]);
                }
                printf("\n");
            }

            // save_data_count++; // 增加计数器
            // if (save_data_count >= 5) // 检查计数器是否达到5次
            // {
            //     running = false;
            // }
        }
        break;
        default:
            // CV_ERROR(g_asLog, -1, "invalid cmd type %d", header.m_nCmdType);
            break;
        }
        pMsg->release();
    }

    delete pNetWrapper;
    return 0;
}
