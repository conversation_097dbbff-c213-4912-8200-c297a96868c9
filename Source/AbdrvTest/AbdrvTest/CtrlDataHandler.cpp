#include "CtrlDataHandler.h"
#include "common/LogHelper.h"
#include <iostream>
#include "drds_ctrl_cmd.h"
#include "sqlite/sqlite3.h"
extern CCVLog g_asLog;
CCVLog g_DSFDataServicesLog;

CtrlDataHandler::CtrlDataHandler(CNetWrapper* netWrapper)
: 
m_netWrapper(netWrapper)
{

}

int CtrlDataHandler::callback(void *pObj, int argc, char **argv, char **azColName){

	CtrlDataHandler* pCtrlDataHandler = static_cast<CtrlDataHandler*>(pObj);
	if(pCtrlDataHandler == nullptr)
	{
		return 0;
	}
    int32 tag_id = 0;
    std::string driver_name = "";

	int i;
	for(i=0; i<argc; i++){
		printf("%s = %s\n", azColName[i], argv[i] ? argv[i] : "NULL");
        std::string columnName = azColName[i];
        if(columnName == "fd_tagid")
        {
            tag_id = std::stoi(argv[i]);
        }
        else  if(columnName == "fd_iodrv")
        {
            driver_name = argv[i];
        }
	}
	printf("\n");

    if(pCtrlDataHandler->m_tagIdToDriverMap.find(tag_id) == pCtrlDataHandler->m_tagIdToDriverMap.end())
    {
        pCtrlDataHandler->m_tagIdToDriverMap[tag_id] = driver_name;
    }
    
    return 0;
}

void CtrlDataHandler::readSqliteDB(std::string strCfgFile)
{
    sqlite3 *db;
	char *zErrMsg = 0;
	int rc;
	char *sql;

	std::string m_strdbFile = strCfgFile + "/PDSCfg.db";

	/* Open database */
	rc = sqlite3_open(m_strdbFile.c_str(), &db);
	if( rc ){
		fprintf(stderr, "Can't open database: %s\n", sqlite3_errmsg(db));
		exit(0);
	}else{
		fprintf(stderr, "Opened database successfully\n");
	}

	/* Create SQL statement */
	sql = "SELECT * from t_pb_ai";

	/* Execute SQL statement */
	rc = sqlite3_exec(db, sql, callback, (void*)this, &zErrMsg);
	if( rc != SQLITE_OK ){
		fprintf(stderr, "SQL error: %s\n", zErrMsg);
		sqlite3_free(zErrMsg);
	}else{
		fprintf(stdout, "Operation done successfully\n");
	}
	sqlite3_close(db);
}


//TODO: debug it
void CtrlDataHandler::initialize()
{
    std::string strCfgFile = CVComm.GetCVProjCfgPath();
    CV_INFO(g_asLog, "Loading  ngvs config file: %s",strCfgFile.c_str());

	readSqliteDB(strCfgFile);

    DRDSNgvsParse::getInstance().init(strCfgFile);
    // DRDSNgvsParse::getInstance().init("/home/<USER>/dr/Repo/copy_force/projects/defaultproject/config");

    const unordered_map<string, LocalVaribale> GlobeLocalVaribales = DRDSNgvsParse::getInstance().GetGetLocalVariable();

    for(auto iter = GlobeLocalVaribales.begin(); iter != GlobeLocalVaribales.end(); ++iter)
    {
        if(m_tagIdToDriverMap.find(iter->second.tagId)==m_tagIdToDriverMap.end())
        {
            continue;
        }

        if(DRDSNgvsParse::getInstance().GetGlobeNGVSVariableByname(iter->first)==nullptr)
        {
            continue;
        }
        TagInfoForCtrl tagInfo;
        tagInfo.m_nDataType = GetNGVSDataTypeSize(iter->second.TypeOf);
        tagInfo.m_nLenBuf = DRDSNgvsParse::getInstance().GetGlobeNGVSVariableByname(iter->first)->Length;
        tagInfo.m_nTagID = iter->second.tagId;
        tagInfo.m_strDriverName = m_tagIdToDriverMap[iter->second.tagId];
        m_tagInfoMap.insert(make_pair(iter->first, tagInfo));
    }
}

int CtrlDataHandler::GetNGVSDataTypeSize(const std::string &typeOf)
{
    if (typeOf == "BOOL")
    {
        return 1;
    }
    else if (typeOf == "BYTE")
    {
        return sizeof(char);
    }
    else if (typeOf == "WORD")
    {
        return sizeof(uint16_t);
    }
    else if (typeOf == "DWORD")
    {
        return sizeof(uint32_t);
    }
    else if (typeOf == "LWORD")
    {
        return sizeof(uint64_t);
    }
    else if (typeOf == "SINT")
    {
        return sizeof(int8_t);
    }
    else if (typeOf == "USINT")
    {
        return sizeof(uint8_t);
    }
    else if (typeOf == "INT")
    {
        return sizeof(int16_t);
    }
    else if (typeOf == "UINT")
    {
        return sizeof(uint16_t);
    }
    else if (typeOf == "DINT")
    {
        return sizeof(int32_t);
    }
    else if (typeOf == "UDINT")
    {
        return sizeof(uint32_t);
    }
    else if (typeOf == "LINT")
    {
        return sizeof(int64_t);
    }
    else if (typeOf == "ULINT")
    {
        return sizeof(uint64_t);
    }
    else if (typeOf == "REAL")
    {
        return sizeof(float);
    }
    else if (typeOf == "LREAL")
    {
        return sizeof(double);
    }
    else if (typeOf == "STRING")
    {
        return 250;
    }
    else if (typeOf == "CHAR")
    {
        return sizeof(char);;
    }
    else
    {
        return 0;
    }
}

void CtrlDataHandler::RecvData(string ngvsName, unsigned char *buf, uint32_t length)
{
    std::cout<<" recv ctrl message from dataService"<<std::endl;
    if(m_netWrapper!=nullptr)
    {
        std::string data((const char*)buf,length);
        std::unordered_map<std::string, std::string> valueMap = DataPacket::UnPackBufferWithKeyValue((const char*)buf,length);

        std::cout << "ctrl des :" << std::endl;
        for(auto iter = valueMap.begin(); iter != valueMap.end(); ++iter)
        {
            std::cout << iter->first << std::endl;
        }
        
        for(auto iter = valueMap.begin(); iter != valueMap.end(); ++iter)
        {
            auto variableIt = m_tagInfoMap.find(iter->first);

            if(variableIt != m_tagInfoMap.end())
            {
                if(variableIt->second.m_nDataType == 0)
                {
                    continue;
                }
                TagInfoForCtrl tempVariable = variableIt->second;

                TProtoDriverAPICTRLMsg msg;
                msg.m_nDataType = tempVariable.m_nDataType;
                msg.m_nLenBuf = tempVariable.m_nLenBuf;
                msg.m_nTagID = tempVariable.m_nTagID;
                ACE_Time_Value start = ACE_OS::gettimeofday();
                msg.m_nTimeOutSec = start.sec()+1;
                msg.m_nTimeOutMs = start.usec()/1000;
                char * temp = new char[tempVariable.m_nLenBuf];
                memcpy(temp,iter->second.c_str(),iter->second.size());//debug it
                msg.m_pBuf = temp;
                m_netWrapper->SendCtrlToDriver(tempVariable.m_strDriverName.c_str(),msg);//get driver type
                // m_netWrapper->SendCtrlToDriver("tdrv",msg);//get driver type
                delete[] temp;
            }
        }
    }
}
