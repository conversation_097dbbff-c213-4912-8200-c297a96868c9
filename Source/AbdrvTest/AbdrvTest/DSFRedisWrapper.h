#ifndef DSF_REDIS_WRAPPER_H
#define DSF_REDIS_WRAPPER_H

#include <hiredis/hiredis.h>
#include <memory>
#include <string.h>
#include <mutex>
#include <string>
#include <vector>
#include "DSFTimer.h"

//TODO: interface remove redis feature, use pimp
//TODO: USE redis pool instead of single connection
class DSFRedisWrapper
{
public:
    /**
     * @brief          Get DSFRedisWrapper Instance
     * @return
     * @version        2024/10/09	huangcan	Initial Version
     */
    static std::shared_ptr<DSFRedisWrapper> getInstance(const std::string &host, int port);

    /**
     * @brief          DSFRedisWrapper destructor
     * @return
     * @version        2024/10/09	huangcan	Initial Version
     */
    ~DSFRedisWrapper();
   
    /**
     * @brief          Set value to redis
     * @param [in]     key  redis key
     * @param [in]     value  redis value
     * @param [in]     value_len  redis value length
     * @return
     * @version        2024/10/09	huangcan	Initial Version
     */
    void setValue(const std::string &key, const unsigned char* value,int value_len);

    /**
     * @brief          Set value to redis by pipelining
     * @param [in]     key  redis key
     * @param [in]     value  redis value
     * @param [in]     value_len  redis value length
     * @return
     * @version        2024/10/09	huangcan	Initial Version
     */
    void setValueByPipelining (const std::string &key, const unsigned char* value,int value_len);

    bool isConnected(){return m_isConnected;}

    bool reconnect();

private:
    
    DSFRedisWrapper(const std::string &host, int port);
    static void handleTimeout(void* arg);

private:
    static std::shared_ptr<DSFRedisWrapper> instance;
    std::mutex m_mtx;
    redisContext *m_context;
    bool m_isConnected;
    Timer* m_timer;
    std::thread* m_timerThread;
};

#endif