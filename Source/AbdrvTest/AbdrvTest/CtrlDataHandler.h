#ifndef _CTRL_DATA_HANDLER_H_
#define _CTRL_DATA_HANDLER_H_

#include "INGVSAgentManager.h"
#include "NetWrapper.h"
#include "drds_ngvs_config.h"


struct TagInfoForCtrl
{
public:
	int32 m_nTagID;
	uint8 m_nDataType;
	int32 m_nLenBuf;
    std::string m_strDriverName;
};


class CtrlDataHandler : public RecvDataCallback
{

public:
    void RecvData(string ngvsName, unsigned char *buf, uint32_t length) override;
    CtrlDataHandler(CNetWrapper* netWrapper);
    void initialize();
    std::map<string, TagInfoForCtrl> m_tagInfoMap;

private:
    int GetNGVSDataTypeSize(const std::string &typeOf);
    static int callback(void *pObj, int argc, char **argv, char **azColName);
    void readSqliteDB(std::string strCfgFile);

private:
    CNetWrapper* m_netWrapper;
    const unordered_map<string, LocalVaribale> m_GlobeLocalVaribales;
    std::map<uint32,std::string> m_tagIdToDriverMap;
};

#endif
