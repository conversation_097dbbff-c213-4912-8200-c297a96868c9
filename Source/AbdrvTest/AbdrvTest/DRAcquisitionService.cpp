#include "DRAcquisitionService.h"

CCVLog g_asLog;

DRAcquisitionService::DRAcquisitionService() 
: 
CServiceBase ("DRAcquisitionService", false, "")
{
}

long DRAcquisitionService::Init( int argc, char* args[] )
{
	long nErr = ICV_SUCCESS;

	m_netWrapper = new CNetWrapper();
	CV_INFO(g_asLog,"m_netWrapper Initialize");
	nErr = m_netWrapper->InitializeNetwork();
	CHECK_ERROR_AND_RETURN_FAIL(nErr);
	m_netWrapper->Activate();
	CV_INFO(g_asLog,"m_netWrapper done");

	m_dataHandler = new CSaveDataHandler(m_netWrapper);
	CV_INFO(g_asLog,"m_dataHandler Initialize");
	if(!m_dataHandler->Initialize())
	{
		CV_ERROR(g_asLog,-1,"m_dataHandler Initialize failed");
		return -1;
	}
	m_dataHandler->Activate();
	CV_INFO(g_asLog,"m_dataHandler done");

	m_ctrlHandler = new CtrlDataHandler(m_netWrapper);

	m_ctrlHandler->initialize();

	// list<string> sendNGVS;
    // list<string> recvNGVS;
	map<string,string> sendNGVS;
    map<string,string> recvNGVS;

	m_NGVSManager = m_serviceProvider.CreateManager();
	std::string m_strCfgFile = CVComm.GetCVProjCfgPath();
    CV_INFO(g_asLog, "Loading  ngvs config file: %s",m_strCfgFile.c_str());
	m_strCfgFile += "/ctrlcmd";
    m_NGVSManager->Initial(m_strCfgFile, sendNGVS, recvNGVS);
 
    m_NGVSManager->RegisterAll(m_ctrlHandler);
    m_NGVSManager->StartAll();

	// m_timer = new Timer(handleTimeout, std::chrono::seconds(3));
    // m_timerThread = new std::thread(&Timer::start, m_timer,this);

	return nErr;
}

void DRAcquisitionService::Refresh()
{
	return;
}

long DRAcquisitionService::Start()
{
	return ICV_SUCCESS;
}

void DRAcquisitionService::SetLogFileName()
{
	g_asLog.SetLogFileNameThread("DRAcquisitionService");
}

void DRAcquisitionService::PrintStartUpScreen()
{
	PrintHelpScreen();
}

void DRAcquisitionService::PrintHelpScreen()
{
	printf("+====================================================================+\n");
	printf("|                     <<Welcome to DRAcquisitionService>>					   |\n");
	printf("|  You can entering the following commands to configure the service  |\n");
	printf("|  q/Q:Quit														   |\n");
	printf("|  Others:Print tips												   |\n");
	printf("+====================================================================+\n");
}

long DRAcquisitionService::Fini()
{
	m_serviceProvider.DestroyManager(m_NGVSManager);
	CV_INFO(g_asLog,"m_netWrapper->ShutDown()");
	m_netWrapper->ShutDown();
	CV_INFO(g_asLog,"m_netWrapper->ShutDown() done");

	CV_INFO(g_asLog,"m_netWrapper->UnInitializeNetwork()");
	m_netWrapper->UnInitializeNetwork();	
	CV_INFO(g_asLog,"m_netWrapper->UnInitializeNetwork() done");

	CV_INFO(g_asLog,"m_dataHandler->ShutDown()");
	m_dataHandler->ShutDown();	
	CV_INFO(g_asLog,"m_dataHandler->ShutDown() done");

	if(m_ctrlHandler)
		delete m_ctrlHandler;
	m_ctrlHandler = nullptr;

	SAFE_DELETE(m_netWrapper);
	g_asLog.StopLogThread();
	return ICV_SUCCESS;
}

bool DRAcquisitionService::ProcessCmd( char c )
{
	return false;
}

void DRAcquisitionService::Misc()
{

}

// #include <iostream>

// void DRAcquisitionService::handleTimeout(void* arg)
// {
//     DRAcquisitionService* p = (DRAcquisitionService*)arg;
    
// 	std::cout<<"drac-----"<<std::endl;
//     TProtoDriverAPICTRLMsg msg;
// 	msg.m_nDataType = 12;
// 	msg.m_nLenBuf = 4;
// 	msg.m_nTagID = 1000000;
// 	ACE_Time_Value start = ACE_OS::gettimeofday();
// 	msg.m_nTimeOutSec = start.sec()+1;
// 	msg.m_nTimeOutMs = start.usec()/1000;
// 	p->m_testValue++;
// 	// p->m_testValue = 99;
// 	char * temp = new char[4];
// 	memcpy(temp,&(p->m_testValue),4);
// 	msg.m_pBuf = temp;
// 	p->m_netWrapper->SendCtrlToDriver("tdrv",msg);
// 	delete[] temp;

//     std::cout << "value: "<<p->m_testValue<<" Send Ctrl Simulate. Current time: " << std::chrono::system_clock::now().time_since_epoch().count() << std::endl;
// }


CServiceBase* g_pServiceHandler = new DRAcquisitionService();