/**************************************************************
 *  Filename:    
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: 
 *
 *  @author:     ya<PERSON><PERSON>
 *  @version     09/22/2018  yangqi  Initial Version
**************************************************************/

#if !defined(PDB_SAVE_DATA_HANDER_H_INCLUDED_)
#define PDB_SAVE_DATA_HANDER_H_INCLUDED_

#include "ace/Null_Mutex.h"
#include "ace/Singleton.h"
#include "ace/Task.h"
#include "ace/Message_Block.h"

#include "common/CVNDK.h"
#include "common/SimpleQueue.h"
#include "proto/proto_driverapi_pdb.h"

#include "NetWrapper.h"
#include "DSFRedisWrapper.h"
#include "DSFObject.h"


class CSaveDataHandler : public ACE_Task_Base
{

friend class ACE_Singleton<CSaveDataHandler, ACE_Thread_Mutex>;

public:
	CSaveDataHandler(CNetWrapper* netWrapper);
	virtual ~CSaveDataHandler();

public:

     /**
     * @brief          DataHandler Init
     * @return
     * @version        2024/10/10	huangcan	Initial Version
     */
	bool Initialize();

	/**
     * @brief          ace task function
     * @return
     * @version        2024/10/10	huangcan	Initial Version
     */
	virtual int svc(void);

	/**
     * @brief          active ace task
     * @return
     * @version        2024/10/10	huangcan	Initial Version
     */
	long Activate();

	/**
     * @brief          shutdown ace task
     * @return
     * @version        2024/10/10	huangcan	Initial Version
     */
	void ShutDown();

private:

	bool m_bExit;
     std::shared_ptr<DSFRedisWrapper> m_redisObject;
	// DSFObjectHandler* m_objectHandler;
	CNetWrapper* m_netWrapper;
     std::map<int,std::vector<std::string>> m_idToObjMap;//���ڿ����ҵ���Ӧ�ı���
     std::map<std::string,DSFSimpleObject*> m_objMap;
     unsigned short m_value = 1;
};

#endif