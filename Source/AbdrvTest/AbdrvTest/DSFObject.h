#pragma once
#include <string>
#include <map>
#include "drds_ngvs_config.h"
#include <algorithm>


struct DSFSimpleObject
{
    std::string name;
    unsigned char m_data[1024] = {0};
    std::vector<ObjectAttr> attrs;
    std::map<std::string,int> variableNameToIDMap;
    unsigned char* setValue(int id,char* value,int value_length)
    {
        for(auto& attr:attrs)
        {
            if(variableNameToIDMap[attr.sName] == id)
            {
                if(std::memcmp(value, m_data+attr.offset, value_length) != 0)
                {
                    memcpy(m_data+attr.offset,value,value_length);
                    return m_data;
                }
                else 
                {
                    if(std::equal(static_cast<const char*>(value), static_cast<const char*>(value) + value_length, reinterpret_cast<const char*>(std::memset(new char[value_length], 0, value_length))))
                    {
                        return m_data;
                    }
                }
            }
        }
        return nullptr;
    }
};