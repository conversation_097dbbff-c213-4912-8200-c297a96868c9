#ifndef DSFTIMER_H
#define DSFTIMER_H

#include <chrono>
#include <thread>
#include <functional>

class Timer {
public:
    Timer(std::function<void(void*)> callback, std::chrono::milliseconds duration)
        : m_callback(callback), m_duration(duration) 
        {

        }

    void start(void* argc) {
        while (true) {
            std::this_thread::sleep_for(m_duration);
            m_callback(argc);
        }
    }

private:
    std::function<void(void*)> m_callback;
    std::chrono::milliseconds m_duration;
};

#endif