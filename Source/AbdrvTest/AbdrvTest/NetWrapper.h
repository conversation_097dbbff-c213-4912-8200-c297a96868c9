/*  Filename:    NetWrapper.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Declare CNetWrapper
 *
 *  @author:     huangcan
 *  @version:    10/08/2024	huangcan	Initial Version
 **************************************************************/

#ifndef CNETWRAPPER_H
#define CNETWRAPPER_H

#include "ace/Null_Mutex.h"
#include "ace/Singleton.h"
#include "ace/Task.h"
#include "ace/Message_Block.h"

#include "common/CVNDK.h"
#include "common/SimpleQueue.h"
#include "proto/proto_driverapi_pdb.h"

#include <map>
#include <string>


class CNetWrapper : public ACE_Task_Base
{
friend class ACE_Singleton<CNetWrapper, ACE_Thread_Mutex>;
public:
	CNetWrapper();
	virtual ~CNetWrapper();

public:
	CSimpleThreadQueue<ACE_Message_Block*> m_dataQueue;

public:
    /**
     * @brief          NetWrapper Init
     * @return
     * @version        2024/10/08	huangcan	Initial Version
     */
	long InitializeNetwork();

	/**
     * @brief          NetWrapper unInit
     * @return
     * @version        2024/10/08	huangcan	Initial Version
     */
	long UnInitializeNetwork();

	/**
     * @brief          send control to driver
     * @param [in]     szDriverName  
     * @param [in]     ctrlMsg  
     * @return
     * @version        2024/10/08	huangcan	Initial Version
     */
	long SendCtrlToDriver(const char* szDriverName, const TProtoDriverAPICTRLMsg& ctrlMsg);

	/**
     * @brief          check driver status
     * @param [in]     szDriverName  
     * @return
     * @version        2024/10/08	huangcan	Initial Version
     */
	bool IsDriverRegister(const char* szDriverName);

	/**
     * @brief          registe driver to thread
     * @param [in]     pArg  thread args
     * @return
     * @version        2024/10/08	huangcan	Initial Version
     */
	static void* ThreadRegDirver(void* pArg);

	/**
     * @brief          ace task
     * @return
     * @version        2024/10/08	huangcan	Initial Version
     */
	virtual int svc(void);

	/**
     * @brief          ace task Activate
     * @return
     * @version        2024/10/08	huangcan	Initial Version
     */
	long Activate();

	/**
     * @brief          ace task ShutDown
     * @return
     * @version        2024/10/08	huangcan	Initial Version
     */
	void ShutDown();

private:
	std::map<std::string, HQUEUE> m_mapDriverName2CliQueue;
	bool m_bExit;
	HQUEUE m_hLocalQueRecvData;
	HQUEUE m_hLocalQueRegDriver;	
	ACE_thread_t m_nThreadID;
	ACE_hthread_t m_hThreadHandle;
};

typedef ACE_Singleton<CNetWrapper, ACE_Thread_Mutex> CNetWrapperSingleton;

#endif