

#ifndef __DR_OPCUA_PUBLISH_SERVICE_H__
#define __DR_OPCUA_PUBLISH_SERVICE_H__
#include "common/ServiceBase.h"
#include "opcua_publish_manager.h"

class COpcuaPublishService : public CServiceBase
{
  public:
    COpcuaPublishService() : CServiceBase("dsfopcuapublish", true, "OPCUAPublishService")
    {
        ExitWhenInitFailed(false);
    }
    ~COpcuaPublishService()
    {
        if (nullptr != m_pOpcuaPublishManager)
        {
            delete m_pOpcuaPublishManager;
            m_pOpcuaPublishManager = nullptr;
        }
    }
    virtual long Init(int argc, char *args[]);
    virtual long Start();
    virtual void PrintStartUpScreen()
    {
        PrintHelpScreen();
    }
    virtual void PrintHelpScreen();
    virtual long Fini();
    virtual void SetLogFileName();
    bool ProcessCmd(char c)
    {
        return 0;
    }

  private:
    COpcuaPublishManager *m_pOpcuaPublishManager = nullptr;
};

#endif // __DR_OPCUA_PUBLISH_SERVICE_H__
