#ifndef __OPUCA_PUBLISH_DEF_H__
#define __OPUCA_PUBLISH_DEF_H__

#include "data_types.h"
#include "errcode/error_code.h"
#include <atomic>
#include <iostream>
#include <memory>
#include <string>
#include <unordered_map>
#include <vector>

struct OpcuaUserInfo
{
    std::string strOpcuaUserName = "";
    std::string strOpcuaPassword = "";
};

struct OpcuaConnectInfo
{
    std::string strDsfAddress = "";
    uint16 nDsfPort = 0;
    std::string strOpcuaAddress = "";
    uint16 nOpcuaPort = 0;
    bool bAnonymous = false;

    std::vector<OpcuaUserInfo> vecOpcuaUserInfo = {};
};
using OpcuaConnectInfoPtr = std::shared_ptr<OpcuaConnectInfo>;

struct OpcuaTagInfo
{
    std::string strTagName = "";
    std::string strPubTagName = "";
    std::string strType = "";
    size_t nLength = 0;
    int32 nIntervalMs = 0; // the interval updated from DS, unit: ms
    int16 nUaType = -1;
    // additional
    static std::atomic<uint32> nIdBase; // initialize at opcua_publish_manager.cpp
    uint32 nId = 0;
    uint16 nAccesslevel = 0; // 0x01: read only, 0x02: write only, 0x03: read/write

    OpcuaTagInfo()
    {
        nId = nIdBase.fetch_add(1);
    }
};
using OpcuaTagInfoPtr = std::shared_ptr<OpcuaTagInfo>;
using OpcuaTagInfoPtrVec = std::vector<OpcuaTagInfoPtr>;

#endif //  __OPUCA_PUBLISH_DEF_H__