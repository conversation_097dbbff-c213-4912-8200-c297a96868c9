

#ifndef __DR_OPCUA_SERVER_H__
#define __DR_OPCUA_SERVER_H__
#include "opcua/open62541.h"
#include "opcua_publish_def.h"
bool GetUaType(OpcuaTagInfoPtr &pTagInfo);

class COpcuaServer
{
  public:
    static COpcuaServer *GetInstance()
    {
        static COpcuaServer instance;
        return &instance;
    }
    ~COpcuaServer();
    int32 Init(OpcuaConnectInfoPtr &pConnectInfo);
    int32 AddDataSourceVariable(const OpcuaTagInfoPtr &pTagInfo);
    int32 Run();
    void Finish();

  private:
    int32 UpdateVariable(const OpcuaTagInfoPtr &pTagInfo, const void *pValue, const int32 nLen, UA_Variant *pVariant);

    static UA_StatusCode ReadTagValue(UA_Server *server, const UA_NodeId *sessionId, void *sessionContext,
                                      const UA_NodeId *nodeId, void *nodeContext, UA_Boolean sourceTimeStamp,
                                      const UA_NumericRange *range, UA_DataValue *dataValue);

    static UA_StatusCode WriteTagValue(UA_Server *server, const UA_NodeId *sessionId, void *sessionContext,
                                       const UA_NodeId *nodeId, void *nodeContext, const UA_NumericRange *range,
                                       const UA_DataValue *data);

    COpcuaServer() = default;

  private:
    UA_Server *m_pServer = nullptr;
    UA_Boolean m_bRuning = true;
    OpcuaConnectInfoPtr m_pOpcuaConnectInfo = nullptr; // opcua server info
    UA_UsernamePasswordLogin *m_Logins;                // opcua login info
    size_t m_nLoginCnt = 0;
};

#endif // __DR_OPCUA_SERVER_H__
