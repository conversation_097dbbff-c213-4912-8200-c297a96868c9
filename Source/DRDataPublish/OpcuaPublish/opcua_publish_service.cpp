

#include "opcua_publish_service.h"
#include "common/CVLog.h"

CCVLog g_OpcuaServerLog;
const char *szServerName = "dsfopcuapublish";

long COpcuaPublishService::Init(int argc, char *args[])
{
    std::string strTagNumber = "";
    int32 nOPCUAPubTagNumber = 100; // default value
    long ret = GetExetendedLicInfo("OPCUAPubTagNumber", strTagNumber);
    if (ret == 0)
    {
        nOPCUAPubTagNumber = std::stoi(strTagNumber);
    }
    CV_INFO(g_OpcuaServerLog, "strTagNumber=%s, HisTagNumber:%d", strTagNumber.c_str(), nOPCUAPubTagNumber);

    m_pOpcuaPublishManager = new COpcuaPublishManager(nOPCUAPubTagNumber);
    int32 nRet = m_pOpcuaPublishManager->Init();
    if (nRet != 0)
    {
        CV_ERROR(g_OpcuaServerLog, nRet, "Failed to Init publish manager, ret = %d", nRet);
    }
    return nRet;
}

long COpcuaPublishService::Start()
{
    int32 nRet = m_pOpcuaPublishManager->Run();
    if (nRet != 0)
    {
        CV_ERROR(g_OpcuaServerLog, nRet, "Failed to Run publish manager, ret = %d", nRet);
    }
    return ICV_SUCCESS;
}

void COpcuaPublishService::PrintHelpScreen()
{
    printf("+===================================================================+\n");
    printf("|                     <<Welcome to OpcuaPublish>>                      |\n");
    printf("|  You can entering the following commands to configure the service    |\n");
    printf("|  q/Q:Quit                                                            |\n");
    printf("|  Others:Print tips                                                   |\n");
    printf("+======================================================================+\n");
}

long COpcuaPublishService::Fini()
{
    m_pOpcuaPublishManager->Shutdown();
    g_OpcuaServerLog.StopLogThread();
    return ICV_SUCCESS;
}

void COpcuaPublishService::SetLogFileName()
{
    g_OpcuaServerLog.SetLogFileNameThread(szServerName);
}

CServiceBase *g_pServiceHandler = new COpcuaPublishService();