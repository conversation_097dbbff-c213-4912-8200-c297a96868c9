cmake_minimum_required(VERSION 2.6)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)
############FOR_MODIFIY_BEGIN#######################
#Setting Project Name
PROJECT (OpcuaPublish)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting The Target Description Shown in the File Properties Dialog
SET(TARGET_DESCRIPTION "OpcuaPublish")
SET(TARGET_VERSION 1.0.0)

#Setting Source Files
file(GLOB DR_OPCUA_SERVER_CPP ${CMAKE_CURRENT_SOURCE_DIR}/*.cpp)
Set(SRCS ${SRCS} ${DR_OPCUA_SERVER_CPP})

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME dsfopcuapublish)
#Setting library type used when build a library
SET(LIB_TYPE STATIC)


include_directories(${CMAKE_SOURCE_DIR}/DRSdk/dsfapi)
# INCLUDE_DIRECTORIES(${CMAKE_SOURCE_DIR}/../include/open62541pp)
SET(LINK_LIBS ACE drlog drcomm drrmapi servicebase shmqueue licverify License open62541 dsfapi  pthread)


IF(UNIX)
	IF(HPUX)
        	SET(LINK_LIBS ${LINK_LIBS} pthread)
	ENDIF(HPUX)
	
	IF(CMAKE_SYSTEM MATCHES "SunOS.*")
		SET(LINK_LIBS ${LINK_LIBS} socket)
	ENDIF(CMAKE_SYSTEM MATCHES "SunOS.*")
	IF(CMAKE_SYSTEM MATCHES "Linux")
		SET(LINK_LIBS ${LINK_LIBS} dl boost_thread boost_system)
	ENDIF(CMAKE_SYSTEM MATCHES "Linux")
ELSE(UNIX)
	SET(LINK_LIBS ${LINK_LIBS} exceptionreport dbghelp)
ENDIF(UNIX)

#windows下bool定义和sszie_t定于与BOOST库和ACE库冲突，因此关闭open62541.h中的相关开关
IF(${CMAKE_SYSTEM_NAME} MATCHES Windows)
	option(_SSIZE_T_DEFINED "ssize defined" ON)
	option(__bool_true_false_are_defined "bool defined" ON)
	if(_SSIZE_T_DEFINED)
		add_definitions(-D_SSIZE_T_DEFINED)
    endif()
	if(__bool_true_false_are_defined)
		add_definitions(-D__bool_true_false_are_defined)
	endif()
ENDIF(${CMAKE_SYSTEM_NAME} MATCHES Windows)
#Setting Executable and Library Output Path

#Setting Include Directorys

#Setting Link Directorys
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonExec)