/**
 * Filename        opcua_data_cache.h
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description     cache tagvalue from dsfapi
 *
 * Author          wuzheqiang
 * Version         01/17/2025    wuzheqiang    Initial Version
 **************************************************************/

#ifndef __DR_OPCUA_DATA_CAHCE_H__
#define __DR_OPCUA_DATA_CAHCE_H__

#include "dsfapi.h"
#include "opcua_publish_def.h"
#include <queue>
#include <shared_mutex>

struct WriteData
{
    std::string strPubTagName = "";
    dsfapi::TagValue tagValue;
};

class OpcuaDataCache
{
  public:
    ~OpcuaDataCache()
    {
        m_CacheData.clear();
    }
    static OpcuaDataCache *GetInstance()
    {
        static OpcuaDataCache instance;
        return &instance;
    }

    uint32_t FastSampledStringHash(const std::string &str, const uint32_t base = 101)
    {
        // return std::hash<std::string>{}(str)%base; //20 times slower than below

        size_t len = str.size();
        if (len == 0)
            return 0;
        uint32_t hash = len; // default hash
        size_t step = len / 4;
        step = (step == 0 ? 1 : step);
        for (size_t i = 0; i < 4; ++i)
        {
            size_t idx = i * step;
            if (idx >= len)
                idx = len - 1;
            hash = (hash * 31) + static_cast<unsigned char>(str[idx]);
        }
        // last one
        hash = (hash * 31) + static_cast<unsigned char>(str[len - 1]);
        return hash % base;
    }
    const dsfapi::TagValue *GetCacheData(const std::string &strName)
    {
        std::shared_lock<std::shared_mutex> lock(m_CacheDataMutex[FastSampledStringHash(strName)]);
        if (m_CacheData.find(strName) == m_CacheData.end())
        {
            return nullptr;
        }
        return &m_CacheData[strName];
    }

    void CacheData(const std::string &strName, dsfapi::TagValue &&tagValue)
    {
        std::unique_lock<std::shared_mutex> lock(m_CacheDataMutex[FastSampledStringHash(strName)]);
        m_CacheData[strName] = std::move(tagValue);
    }

    bool EnqueueWriteData(std::shared_ptr<WriteData> pWriteData)
    {
        std::lock_guard<std::mutex> lock(m_WriteDataMutex);
        if (m_WriteDataQueue.size() > 100)
        {
            return false;
        }
        // When adding DataSourceVariable, WriteTagValue will be triggered
        // There is no need to handle write operations at this time.
        // So, it is determined whether dsfapi has already been registered and obtained data
        if (m_CacheData.size() > 0)
        {
            m_WriteDataQueue.push(pWriteData);
        }
        return true;
    }

    std::shared_ptr<WriteData> DequeueWriteData()
    {
        std::lock_guard<std::mutex> lock(m_WriteDataMutex);
        if (m_WriteDataQueue.empty())
        {
            return nullptr;
        }
        auto pWriteData = m_WriteDataQueue.front();
        m_WriteDataQueue.pop();
        return pWriteData;
    }

  private:
    OpcuaDataCache()
    {
        m_CacheData.clear();
    }

  private:
    std::unordered_map<uint32, std::shared_mutex> m_CacheDataMutex;
    std::unordered_map<std::string, dsfapi::TagValue> m_CacheData;

    std::mutex m_WriteDataMutex;
    std::queue<std::shared_ptr<WriteData>> m_WriteDataQueue;
};

#endif //__DR_OPCUA_DATA_CAHCE_H__