/**
 * Filename        DrOpcuaServer.cpp
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description
 *
 * Author          wuzheqiang
 * Version         10/23/2024    wuzheqiang    Initial Version
 **************************************************************/

#include "opcua_server.h"
#include "common/CVLog.h"
#include "opcua_data_cache.h"

extern CCVLog g_OpcuaServerLog;
std::unordered_map<std::string, uint16_t> gMapUatypes = {
    // 1byte
    {"BOOL", UA_TYPES_BOOLEAN},
    {"BYTE", UA_TYPES_BYTE}, //
    {"SINT", UA_TYPES_SBYTE},
    {"USINT", UA_TYPES_BYTE},
    {"CHAR", UA_TYPES_SBYTE}, //
    // 2bytes
    {"INT", UA_TYPES_INT16},
    {"UINT", UA_TYPES_UINT16},
    {"WORD", UA_TYPES_UINT16}, //
    // 4bytes
    {"TIME", UA_TYPES_INT32}, //
    {"DINT", UA_TYPES_INT32},
    {"UDINT", UA_TYPES_UINT32},
    {"DWORD", UA_TYPES_UINT32}, //
    // 8bytes
    {"LTIME", UA_TYPES_INT64}, //
    {"LINT", UA_TYPES_INT64},
    {"ULINT", UA_TYPES_UINT64},
    {"LWORD", UA_TYPES_UINT64}, //
    {"REAL", UA_TYPES_FLOAT},
    {"LREAL", UA_TYPES_DOUBLE}, //
    // others
    {"STRING", UA_TYPES_STRING},
    {"TIME", UA_TYPES_UINT64},  //
    {"LTIME", UA_TYPES_UINT64}, //
    {"DATE_AND_TIME", UA_TYPES_DATETIME}};

bool GetUaType(OpcuaTagInfoPtr &pTagInfo)
{
    if (gMapUatypes.find(pTagInfo->strType) == gMapUatypes.end())
    {
        return false;
    }

    pTagInfo->nUaType = gMapUatypes[pTagInfo->strType];
    return true;
}
/////////////////////////////////////////////////////
COpcuaServer::~COpcuaServer()
{
    for (int i = 0; i < m_nLoginCnt; i++)
    {
        UA_String_clear(&m_Logins[i].username);
        UA_String_clear(&m_Logins[i].password);
    }
    delete[] m_Logins;
    m_Logins = nullptr;

    Finish();
}

int32 COpcuaServer::Init(OpcuaConnectInfoPtr &pConnectInfo)
{
    if (nullptr == pConnectInfo)
    {
        CV_ERROR(g_OpcuaServerLog, EC_DSF_PUB_NULLPTR, "m_pServer Failed !");
        return EC_DSF_PUB_NULLPTR;
    }

    m_pOpcuaConnectInfo = pConnectInfo;
    m_pServer = UA_Server_new();
    if (nullptr == m_pServer)
    {
        CV_ERROR(g_OpcuaServerLog, EC_DSF_PUB_NULLPTR, "m_pServer Failed !");
        return EC_DSF_PUB_NULLPTR;
    }

    UA_ServerConfig *config = UA_Server_getConfig(m_pServer);
    UA_ServerConfig_setMinimal(config, m_pOpcuaConnectInfo->nOpcuaPort, NULL);
    // UA_String customIP = UA_STRING_ALLOC(const_cast<char *>(m_pOpcuaConnectInfo->strOpcuaAddress.c_str()));

    // default is "0.0.0.0"
    // UA_String customIP = UA_STRING_ALLOC("0.0.0.0");
    // config->customHostname = customIP;

    /* Disable anonymous logins, enable two user/password logins */
    if (!m_pOpcuaConnectInfo->bAnonymous)
    {

        m_nLoginCnt = pConnectInfo->vecOpcuaUserInfo.size();
        m_Logins = new UA_UsernamePasswordLogin[m_nLoginCnt];
        for (int i = 0; i < m_nLoginCnt; i++)
        {
            m_Logins[i] = {UA_STRING_ALLOC(pConnectInfo->vecOpcuaUserInfo[i].strOpcuaUserName.data()),
                           UA_STRING_ALLOC(pConnectInfo->vecOpcuaUserInfo[i].strOpcuaPassword.data())};
        }

        config->accessControl.clear(&config->accessControl);
        UA_StatusCode retval = UA_AccessControl_default(
            config, false, NULL, &config->securityPolicies[config->securityPoliciesSize - 1].policyUri, m_nLoginCnt,
            m_Logins);
        if (retval != UA_STATUSCODE_GOOD)
        {
            CV_ERROR(g_OpcuaServerLog, EC_DSF_PUB_NULLPTR, "m_pServer UA_AccessControl_default  Failed , ret=%d!",
                     retval);
            return EC_DSF_PUB_COMMON_ERROR;
        }
    }

    return 0;
}

UA_StatusCode COpcuaServer::ReadTagValue(UA_Server *server, const UA_NodeId *sessionId, void *sessionContext,
                                         const UA_NodeId *nodeId, void *nodeContext, UA_Boolean sourceTimeStamp,
                                         const UA_NumericRange *range, UA_DataValue *dataValue)
{
    static COpcuaServer *instance = COpcuaServer::GetInstance();

    OpcuaTagInfoPtr pTagInfo = *static_cast<OpcuaTagInfoPtr *>(nodeContext);
    CV_DEBUG(g_OpcuaServerLog, "ReadTagValue name=%s", pTagInfo->strPubTagName.c_str());
    auto *pTagValue = OpcuaDataCache::GetInstance()->GetCacheData(pTagInfo->strPubTagName);
    if (pTagValue)
    {
        instance->UpdateVariable(pTagInfo, pTagValue->Buffer(), pTagValue->Length(), &(dataValue->value));
        dataValue->hasValue = true;
    }
    else
    {
        dataValue->hasValue = false;
    }

    if (sourceTimeStamp)
    {
        dataValue->sourceTimestamp = UA_DateTime_now();
        dataValue->hasSourceTimestamp = true;
    }

    return UA_STATUSCODE_GOOD;
}

UA_StatusCode COpcuaServer::WriteTagValue(UA_Server *server, const UA_NodeId *sessionId, void *sessionContext,
                                          const UA_NodeId *nodeId, void *nodeContext, const UA_NumericRange *range,
                                          const UA_DataValue *data)
{
    OpcuaTagInfoPtr pTagInfo = *static_cast<OpcuaTagInfoPtr *>(nodeContext);
    CV_INFO(g_OpcuaServerLog, "WriteTagValue name=%s", pTagInfo->strPubTagName.c_str());

    std::shared_ptr<WriteData> pWriteData = std::make_shared<WriteData>();
    pWriteData->strPubTagName = pTagInfo->strPubTagName;

    if (data && data->hasValue && data->value.type == &UA_TYPES[UA_TYPES_STRING] && data->value.data != nullptr)
    {
        UA_String uaStr = *static_cast<UA_String *>(data->value.data);
        std::string strValue(reinterpret_cast<char *>(uaStr.data), uaStr.length);
        pWriteData->tagValue.ReSet(strValue.data(), strValue.length());
    }
    else
    {
        pWriteData->tagValue.ReSet(reinterpret_cast<const char *>(data->value.data), pTagInfo->nLength);
    }

    if (!OpcuaDataCache::GetInstance()->EnqueueWriteData(pWriteData))
    {
        CV_WARN(g_OpcuaServerLog, -1, "EnqueueWriteData failed");
        return UA_STATUSCODE_BAD;
    }
    return UA_STATUSCODE_GOOD;
}

int32 COpcuaServer::AddDataSourceVariable(const OpcuaTagInfoPtr &pTagInfo)
{
    UA_VariableAttributes attr = UA_VariableAttributes_default;
    attr.displayName = UA_LOCALIZEDTEXT("en-US", const_cast<char *>(pTagInfo->strPubTagName.data()));
    attr.description = UA_LOCALIZEDTEXT("en-US", const_cast<char *>(pTagInfo->strPubTagName.data()));
    attr.accessLevel = pTagInfo->nAccesslevel;
    attr.dataType = UA_TYPES[pTagInfo->nUaType].typeId;
    attr.valueRank = UA_VALUERANK_SCALAR;

    UA_NodeId uaNodeID = UA_NODEID_STRING(1, const_cast<char *>(pTagInfo->strPubTagName.data()));
    UA_QualifiedName uaQualifiedName = UA_QUALIFIEDNAME(1, const_cast<char *>(pTagInfo->strPubTagName.data()));
    UA_NodeId uaParentNodeId = UA_NODEID_NUMERIC(0, UA_NS0ID_OBJECTSFOLDER);
    UA_NodeId uaParentReferenceNodeId = UA_NODEID_NUMERIC(0, UA_NS0ID_ORGANIZES);
    UA_NodeId uaVariableTypeNodeId = UA_NODEID_NUMERIC(0, UA_NS0ID_BASEDATAVARIABLETYPE);

    UA_DataSource uaDataSource;
    uaDataSource.read = ReadTagValue;
    uaDataSource.write = WriteTagValue;
    UA_StatusCode uaStatus = UA_Server_addDataSourceVariableNode(
        m_pServer, uaNodeID, uaParentNodeId, uaParentReferenceNodeId, uaQualifiedName, uaVariableTypeNodeId, attr,
        uaDataSource, (void *)&pTagInfo, NULL);
    if (uaStatus != UA_STATUSCODE_GOOD)
    {
        CV_ERROR(g_OpcuaServerLog, EC_DSF_PUB_COMMON_ERROR, "Error adding datasource variable: %s",
                 UA_StatusCode_name(uaStatus));
        return -1;
    }
    return 0;
}

int32 COpcuaServer::UpdateVariable(const OpcuaTagInfoPtr &pTagInfo, const void *pValue, const int32 nLen,
                                   UA_Variant *pVariant)
{
    UA_StatusCode status = UA_STATUSCODE_GOOD;
    // 获取节点 ID

    const auto &tTagInfo = *pTagInfo;
    UA_NodeId nodeId = UA_NODEID_STRING(1, const_cast<char *>(tTagInfo.strTagName.data()));

    switch (tTagInfo.nUaType)
    {
    case UA_TYPES_BOOLEAN: {
        UA_Boolean uaBoolean = *static_cast<const UA_Boolean *>(pValue);
        UA_Variant_setScalarCopy(pVariant, &uaBoolean, &UA_TYPES[tTagInfo.nUaType]);
        break;
    }
    case UA_TYPES_SBYTE: {
        UA_SByte uaSByte = *static_cast<const UA_SByte *>(pValue);
        UA_Variant_setScalarCopy(pVariant, &uaSByte, &UA_TYPES[tTagInfo.nUaType]);
        break;
    }
    case UA_TYPES_BYTE: {
        UA_Byte uaByte = *static_cast<const UA_Byte *>(pValue);
        UA_Variant_setScalarCopy(pVariant, &uaByte, &UA_TYPES[tTagInfo.nUaType]);
        break;
    }
    case UA_TYPES_INT16: {
        UA_Int16 uaInt16 = *static_cast<const UA_Int16 *>(pValue);
        UA_Variant_setScalarCopy(pVariant, &uaInt16, &UA_TYPES[tTagInfo.nUaType]);
        break;
    }
    case UA_TYPES_UINT16: {
        UA_UInt16 uaUInt16 = *static_cast<const UA_UInt16 *>(pValue);
        UA_Variant_setScalarCopy(pVariant, &uaUInt16, &UA_TYPES[tTagInfo.nUaType]);
        break;
    }
    case UA_TYPES_INT32: {
        UA_Int32 uaInt32 = *static_cast<const UA_Int32 *>(pValue);
        UA_Variant_setScalarCopy(pVariant, &uaInt32, &UA_TYPES[tTagInfo.nUaType]);
        break;
    }
    case UA_TYPES_UINT32: {
        UA_UInt32 uaUInt32 = *static_cast<const UA_UInt32 *>(pValue);
        UA_Variant_setScalarCopy(pVariant, &uaUInt32, &UA_TYPES[tTagInfo.nUaType]);
        break;
    }
    case UA_TYPES_INT64: {
        UA_Int64 uaInt64 = *static_cast<const UA_Int64 *>(pValue);
        UA_Variant_setScalarCopy(pVariant, &uaInt64, &UA_TYPES[tTagInfo.nUaType]);
        break;
    }
    case UA_TYPES_UINT64: {
        UA_UInt64 uaUInt64 = *static_cast<const UA_UInt64 *>(pValue);
        UA_Variant_setScalarCopy(pVariant, &uaUInt64, &UA_TYPES[tTagInfo.nUaType]);
        break;
    }
    case UA_TYPES_FLOAT: {
        UA_Float uaFloat = *static_cast<const UA_Float *>(pValue);
        UA_Variant_setScalarCopy(pVariant, &uaFloat, &UA_TYPES[tTagInfo.nUaType]);
        break;
    }
    case UA_TYPES_DOUBLE: {
        UA_Double uaDouble = *static_cast<const UA_Double *>(pValue);
        UA_Variant_setScalarCopy(pVariant, &uaDouble, &UA_TYPES[tTagInfo.nUaType]);
        break;
    }
    case UA_TYPES_STRING: {
        const char *stringValue = static_cast<const char *>(pValue) + 2;
        UA_String uaString = UA_STRING_ALLOC(stringValue);
        UA_Variant_setScalarCopy(pVariant, &uaString, &UA_TYPES[tTagInfo.nUaType]);
        UA_String_clear(&uaString);
        break;
    }
    case UA_TYPES_DATETIME: {
        UA_DateTime uaDateTime = *static_cast<const UA_DateTime *>(pValue);
        UA_Variant_setScalarCopy(pVariant, &uaDateTime, &UA_TYPES[tTagInfo.nUaType]);
        break;
    }
    default:
        return UA_STATUSCODE_BADTYPEMISMATCH; // 返回不支持的类型
    }
    return status;
}

int32 COpcuaServer::Run()
{
    UA_StatusCode retval = UA_Server_run(m_pServer, &m_bRuning);
    return retval == UA_STATUSCODE_GOOD ? EXIT_SUCCESS : EXIT_FAILURE;
}

void COpcuaServer::Finish()
{
    m_bRuning = false;
    if (nullptr != m_pServer)
    {
        UA_Server_delete(m_pServer);
        m_pServer = nullptr;
    }
}