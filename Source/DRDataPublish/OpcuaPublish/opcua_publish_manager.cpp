
#include "opcua_publish_manager.h"
#include "common/CVLog.h"
#include "common/cvcomm.hxx"
#include <filesystem>
#include <fstream>

extern CCVLog g_OpcuaServerLog;

constexpr int32 kWriteDataMaxCnt = 1000;
constexpr int32 BATCH_CNT = 5000;
std::atomic<uint32> OpcuaTagInfo::nIdBase = {1000000};

COpcuaPublishManager::COpcuaPublishManager(const int32 nOPCUAPubTagNumber) : m_nOPCUAPubTagNumber(nOPCUAPubTagNumber)
{
    m_pOpcuaConnectInfo = std::make_shared<OpcuaConnectInfo>();
}

COpcuaPublishManager::~COpcuaPublishManager()
{
    Shutdown();
}

int32 COpcuaPublishManager::Init()
{
    int32 nRet = 0;
    // 0 get config
    const char *pCVCfgPath = CCVComm::GetInstance().GetCVProjCfgPath();
    if (NULL == pCVCfgPath)
    {
        CV_ERROR(g_OpcuaServerLog, EC_DSF_PUB_COMMON_ERROR, "GetCVProjCfgPath failed");
        return EC_DSF_PUB_COMMON_ERROR;
    }

    m_strConfigFileName = std::string(pCVCfgPath) + std::string("/") + std::string("dsf_opcua_tags.json");
    if (!std::filesystem::exists(m_strConfigFileName) || (0 == std::filesystem::file_size(m_strConfigFileName)))
    { // not finish the process
        CV_ERROR(g_OpcuaServerLog, EC_DSF_PUB_CONFIG_ERROR, "dsf_opcua_tags.json is emptry or  not exist: %s",
                 m_strConfigFileName.c_str());
        std::cout << "dsf_opcua_tags.json not exist:" << m_strConfigFileName << std::endl;
        return 0;
    }

    try
    {
        // load config
        nRet = LoadConfig();
        if (nRet != 0)
        {
            CV_ERROR(g_OpcuaServerLog, nRet, "LoadConfig failed");
            return nRet;
        }
    }
    catch (const std::exception &e)
    {
        CV_ERROR(g_OpcuaServerLog, -1, "LoadConfig failed,exception:%s", e.what());
        return -1;
    }
    catch (...)
    {
        CV_ERROR(g_OpcuaServerLog, -1, "LoadConfig failed,unknow exception");
        return -1;
    }

    nRet = AddedInnerTags();
    if (nRet != 0)
    {
        CV_ERROR(g_OpcuaServerLog, nRet, "AddedInnerTags failed");
        return nRet;
    }

    // init dsfapi
    m_pContext = DrsdkInit();
    if (nullptr == m_pContext)
    {
        CV_ERROR(g_OpcuaServerLog, EC_DSF_PUB_COMMON_ERROR, "DrsdkInit failed");
        return EC_DSF_PUB_COMMON_ERROR;
    }

    // init opcua server
    m_pOpcuaServer = COpcuaServer::GetInstance();
    nRet = m_pOpcuaServer->Init(m_pOpcuaConnectInfo);
    if (nRet != 0)
    {
        CV_ERROR(g_OpcuaServerLog, nRet, "m_pOpcuaServer->Init failed");
        return nRet;
    }

    // opcua add variable
    nRet = RegisterTagGroupsToUaServer();
    if (nRet != 0)
    {
        CV_ERROR(g_OpcuaServerLog, nRet, "RegisterTagGroupsToUaServer failed");
        return nRet;
    }

    m_bInit.store(true);
    return 0;
}
int32 COpcuaPublishManager::Run()
{
    if (!m_bInit.load())
    {
        CV_ERROR(g_OpcuaServerLog, -1, "init not success!");
        return 0; // not
    }
    // run opcua server
    m_pOpcuaRunThread.reset(new std::thread([this]() {
        try
        {
            m_bOpcuaRuning.store(true);
            int nRet = m_pOpcuaServer->Run();
            if (nRet != 0)
            {
                CV_ERROR(g_OpcuaServerLog, nRet, "m_pOpcuaServer->Run failed");
                return nRet;
            }
            m_bOpcuaRuning.store(false);
            CV_WARN(g_OpcuaServerLog, nRet, "m_pOpcuaServer->Run finished");
        }
        catch (const std::exception &e)
        {
            CV_ERROR(g_OpcuaServerLog, -1, "m_pOpcuaServer->Run exception: %s", e.what());
            printf("!!!m_pOpcuaServer->Run exception: %s\n", e.what());
        }
        catch (...)
        {
            CV_ERROR(g_OpcuaServerLog, -1, "m_pOpcuaServer->Run exception");
            printf("!!!m_pOpcuaServer->Run exception\n");
        }
    }));
    // for inner tag
    m_pInnerTagThread.reset(new std::thread([this]() { DealInnerTagData(); }));
    // for write data to dsfapi
    m_pWriteDataThread.reset(new std::thread([this]() { DealOpcuaWriteData(); }));
    // register tag groups to dsfapi
    int32 nRet = RegisterTagGroupsToDrsdk();
    if (nRet != 0)
    {
        CV_ERROR(g_OpcuaServerLog, nRet, "RegisterTags failed");
        return nRet;
    }
    return 0;
}

int32 COpcuaPublishManager::Shutdown()
{
    m_bStop.store(true);
    if (nullptr != m_pOpcuaServer)
    {
        m_pOpcuaServer->Finish();
    }

    if (nullptr != m_pOpcuaRunThread)
    {
        m_pOpcuaRunThread->join();
        m_pOpcuaRunThread = nullptr;
    }
    if (nullptr != m_pWriteDataThread)
    {
        m_pWriteDataThread->join();
        m_pWriteDataThread = nullptr;
    }
    if (nullptr != m_pInnerTagThread)
    {
        m_pInnerTagThread->join();
        m_pInnerTagThread = nullptr;
    }

    dsfapi::DR_Uninit(m_pContext);
    dsfapi::Free_DR_Sdk_Context(&m_pContext);
    return 0;
}

dsfapi::DRSdkContext *COpcuaPublishManager::DrsdkInit()
{
    // 1.should init first
    dsfapi::DRSdkConnectParam tConnectParam;
    strncpy(tConnectParam.szServerIp, m_pOpcuaConnectInfo->strDsfAddress.c_str(), sizeof(tConnectParam.szServerIp) - 1);
    tConnectParam.nServerPort = m_pOpcuaConnectInfo->nDsfPort;
    tConnectParam.nConnectTimeoutMs = 1000;

    dsfapi::DRSdkOption tOption;
    tOption.nThreadPoolNum = 10; // thread nums for run  register callback
    tOption.nRequestWaitTimeoutMs = 1000;

    dsfapi::DRSdkContext *pContext = nullptr;
    pContext = dsfapi::DR_Init(tConnectParam, tOption);
    if (nullptr == pContext || pContext->errorcode != 0)
    {
        CV_ERROR(g_OpcuaServerLog, -1, "DR_Init failed! errorcode");
        dsfapi::Free_DR_Sdk_Context(&pContext);
        return nullptr;
    }
    return pContext;
}

int32 COpcuaPublishManager::LoadConfig()
{
    std::ifstream configFile(m_strConfigFileName);
    if (!configFile.is_open())
    {
        CV_ERROR(g_OpcuaServerLog, EC_DSF_PUB_CONFIG_ERROR, "Failed to open file: %s", m_strConfigFileName.c_str());
        return EC_DSF_PUB_CONFIG_ERROR;
    }
    json jConfig;
    configFile >> jConfig;
    configFile.close();

    if (jConfig.contains("description"))
    {
        CV_INFO(g_OpcuaServerLog, "description: %s", jConfig["description"].get<std::string>().c_str());
    }

    int32 nRet = 0;
    nRet = LoadConnnectConfig(jConfig);
    if (nRet != 0)
    {
        CV_ERROR(g_OpcuaServerLog, nRet, "LoadConnnectConfig failed");
        return nRet;
    }

    nRet = LoadTagsConfig(jConfig);
    if (nRet != 0)
    {
        CV_ERROR(g_OpcuaServerLog, nRet, "LoadTagsConfig failed");
        return nRet;
    }

    return 0;
}

int32 COpcuaPublishManager::LoadConnnectConfig(const json &jConfig)
{
    // dsf station
    if (!jConfig.contains("dsf_address") || !jConfig.contains("dsf_port"))
    {
        CV_WARN(g_OpcuaServerLog, EC_DSF_PUB_CONFIG_ERROR, "dsf connect info use default.");
        m_pOpcuaConnectInfo->strDsfAddress = "127.0.0.1";
        m_pOpcuaConnectInfo->nDsfPort = 1234;
    }
    else
    {
        m_pOpcuaConnectInfo->strDsfAddress = jConfig["dsf_address"];
        m_pOpcuaConnectInfo->nDsfPort = jConfig["dsf_port"];
    }

    // opcua server
    if (!jConfig.contains("opcua_port"))
    {
        CV_ERROR(g_OpcuaServerLog, EC_DSF_PUB_CONFIG_ERROR, "opcua connect opcua_port not complete.");
        return EC_DSF_PUB_CONFIG_ERROR;
    }
    else
    {
        m_pOpcuaConnectInfo->nOpcuaPort = jConfig["opcua_port"];
    }

    // anonymous
    if (!jConfig.contains("anonymous"))
    {
        CV_ERROR(g_OpcuaServerLog, EC_DSF_PUB_CONFIG_ERROR, "opcua connect anonymous not complete.");
        return EC_DSF_PUB_CONFIG_ERROR;
    }
    else
    {
        m_pOpcuaConnectInfo->bAnonymous = jConfig["anonymous"];
    }

    // opcua login
    if (!m_pOpcuaConnectInfo->bAnonymous)
    {
        if (jConfig.contains("opcua_login") && jConfig["opcua_login"].is_array())
        {
            for (const auto &login : jConfig["opcua_login"])
            {
                if (login.contains("opcua_user") && login.contains("opcua_password"))
                {
                    OpcuaUserInfo userInfo;
                    userInfo.strOpcuaUserName = login["opcua_user"].get<std::string>();
                    userInfo.strOpcuaPassword = login["opcua_password"].get<std::string>();

                    m_pOpcuaConnectInfo->vecOpcuaUserInfo.emplace_back(std::move(userInfo));
                }
                else
                {
                    CV_ERROR(g_OpcuaServerLog, EC_DSF_PUB_CONFIG_ERROR, "opcua login info not complete.");
                    return EC_DSF_PUB_CONFIG_ERROR;
                }
            }
        }
        else
        {
            CV_ERROR(g_OpcuaServerLog, EC_DSF_PUB_CONFIG_ERROR, "opcua login array missing or incorrect.");
            return EC_DSF_PUB_CONFIG_ERROR;
        }
    }
    return 0;
}

int32 COpcuaPublishManager::LoadTagsConfig(const json &jConfig)
{
    int32 nTagNum = 0;
    for (const auto &tag : jConfig["tags"])
    {
        if (m_mapTagInfo.size() >= m_nOPCUAPubTagNumber)
        {
            CV_WARN(g_OpcuaServerLog, EC_DSF_ARCH_COMMON_ERROR,
                    "tag number is too large, m_mapTagInfo.size[%d],m_nOPCUAPubTagNumber[%d]", m_mapTagInfo.size(),
                    m_nOPCUAPubTagNumber);
            return EC_DSF_ARCH_SUCCESS;
        }

        ++nTagNum;
        if (!tag.contains("tagname") || !tag.contains("pub_tagname") || !tag.contains("type") ||
            !tag.contains("length") || !tag.contains("interval"))
        {
            CV_ERROR(g_OpcuaServerLog, EC_DSF_PUB_CONFIG_ERROR, "tag info not complete, nTagNum:%d", nTagNum);
            return EC_DSF_PUB_CONFIG_ERROR;
        }

        auto pOpcuaTagInfo = std::make_shared<OpcuaTagInfo>();
        pOpcuaTagInfo->strTagName = tag["tagname"];
        pOpcuaTagInfo->strPubTagName = tag["pub_tagname"];
        pOpcuaTagInfo->strType = tag["type"];
        pOpcuaTagInfo->nLength = tag["length"];
        pOpcuaTagInfo->nIntervalMs = tag["interval"];
        pOpcuaTagInfo->nAccesslevel = UA_ACCESSLEVELMASK_READ | UA_ACCESSLEVELMASK_WRITE;
        if (!GetUaType(pOpcuaTagInfo))
        {
            CV_WARN(g_OpcuaServerLog, EC_DSF_PUB_CONFIG_ERROR, "GetUaType pOpcuaTagInfo->strType [%s] not support.\n",
                    pOpcuaTagInfo->strType.c_str());
            continue;
        }

        auto it = m_mapIntervalTagInfo.find(pOpcuaTagInfo->nIntervalMs);
        if (it == m_mapIntervalTagInfo.end())
        {
            OpcuaTagInfoPtrVec vecTagInfo;
            m_mapIntervalTagInfo.emplace(pOpcuaTagInfo->nIntervalMs, vecTagInfo);
        }
        m_mapIntervalTagInfo[pOpcuaTagInfo->nIntervalMs].emplace_back(pOpcuaTagInfo);

        if (m_mapTagInfo.count(pOpcuaTagInfo->strTagName) > 0)
        {
            CV_WARN(g_OpcuaServerLog, EC_DSF_PUB_CONFIG_ERROR, "tag name [%s] is duplicate.",
                    pOpcuaTagInfo->strTagName.c_str());
        }
        m_mapTagInfo.emplace(std::make_pair(pOpcuaTagInfo->strTagName, pOpcuaTagInfo));

        if (m_mapTagInfoPub.count(pOpcuaTagInfo->strPubTagName) > 0)
        {
            CV_WARN(g_OpcuaServerLog, EC_DSF_PUB_CONFIG_ERROR, "tag pub name [%s] is duplicate.",
                    pOpcuaTagInfo->strPubTagName.c_str());
        }
        m_mapTagInfoPub.emplace(std::make_pair(pOpcuaTagInfo->strPubTagName, pOpcuaTagInfo));
    }

    CV_INFO(g_OpcuaServerLog, "LoadConfig success, tag size= %d", m_mapTagInfo.size());
    return 0;
}

int COpcuaPublishManager::AddedInnerTags()
{
    auto add_tags = [this](const std::string &strPubTagName, const std::string &strType, const int32 nLength,
                           const int32 nIntervalMs) {
        auto pOpcuaTagInfo = std::make_shared<OpcuaTagInfo>();
        pOpcuaTagInfo->strTagName = strPubTagName;
        pOpcuaTagInfo->strPubTagName = strPubTagName;
        pOpcuaTagInfo->strType = strType;
        pOpcuaTagInfo->nLength = nLength;
        pOpcuaTagInfo->nIntervalMs = nIntervalMs;
        pOpcuaTagInfo->nAccesslevel = UA_ACCESSLEVELMASK_READ;
        GetUaType(pOpcuaTagInfo);
        m_vecInnerTagInfo.emplace_back(pOpcuaTagInfo);
        if (m_mapTagInfo.count(pOpcuaTagInfo->strTagName) > 0)
        {
            CV_WARN(g_OpcuaServerLog, EC_DSF_PUB_CONFIG_ERROR, "tag name [%s] is duplicate.",
                    pOpcuaTagInfo->strTagName.c_str());
        }
        m_mapTagInfo.emplace(std::make_pair(pOpcuaTagInfo->strTagName, pOpcuaTagInfo));

        if (m_mapTagInfoPub.count(pOpcuaTagInfo->strPubTagName) > 0)
        {
            CV_WARN(g_OpcuaServerLog, EC_DSF_PUB_CONFIG_ERROR, "tag pub name [%s] is duplicate.",
                    pOpcuaTagInfo->strPubTagName.c_str());
        }
        m_mapTagInfoPub.emplace(std::make_pair(pOpcuaTagInfo->strPubTagName, pOpcuaTagInfo));
    };
    // add rm status
    add_tags("dsf.opcua_publish.inner.rm_status", "DINT", sizeof(int32), 1000);
    add_tags("dsf.opcua_publish.inner.current_time", "DATE_AND_TIME", sizeof(UA_DateTime), 1000);
    return 0;
}

int32 COpcuaPublishManager::RegisterTagGroupsToDrsdk()
{
    bool bRetryFlag = true;
    while (bRetryFlag && !m_bStop.load())
    {
        bRetryFlag = false;
        for (auto &item : m_mapIntervalTagInfo)
        {
            if (m_mapRegFlag.find(item.first) != m_mapRegFlag.end())
            {
                if (m_mapRegFlag[item.first] == true)
                {
                    CV_WARN(g_OpcuaServerLog, -1, "interval:%d has been registered", item.first);
                    continue;
                }
            }
            int nRet = RegisterTagsToDrsdk(item.first, item.second);
            if (nRet != 0)
            {
                bRetryFlag = true;
                CV_ERROR(g_OpcuaServerLog, nRet, "RegisterTagsToDrsdk failed, interval:%d", item.first);
            }
            m_mapRegFlag[item.first] = (0 == nRet) ? true : false;
        }

        std::this_thread::sleep_for(std::chrono::seconds(2));
    }

    return 0;
}

int32 COpcuaPublishManager::RegisterTagsToDrsdk(const int32 nInterval, const OpcuaTagInfoPtrVec &pTags)
{
    const int32 nTagSize = pTags.size();

    const char *pTagName[nTagSize] = {0};

    int i = 0;
    for (auto &item : pTags)
    {
        pTagName[i++] = item->strTagName.data();
    }

    auto func = [this](const int32 nBatchId, dsfapi::TagRecord *pTagRecord, int32 *pErrorCode, const int32 nTagCount) {
        CV_INFO(g_OpcuaServerLog, "callback called. count=%d, batch_id=%d,m_bOpcuaRun=%s", nTagCount, nBatchId,
                m_bOpcuaRuning.load() ? "true" : "false")
        if (!m_bOpcuaRuning.load())
        {
            return;
        }

        int32 nInterval = 0;
        if (m_mapBatch.find(nBatchId) != m_mapBatch.end())
        {
            nInterval = m_mapBatch[nBatchId];
        }
        else
        {
            CV_ERROR(g_OpcuaServerLog, -1, "batch_id not found. batch_id=%d", nBatchId)
            return;
        }

        int32 nErrCount = 0;
        for (int i = 0; i < nTagCount; i++)
        {
            if (0 != pErrorCode[i])
            {
                CV_DEBUG(g_OpcuaServerLog, "pErrorCode !=0. errorCode:%d, tagname:%s,tagvalue:%s", pErrorCode[i],
                         pTagRecord[i].tTagName.Buffer(), pTagRecord[i].tTagValue.Buffer());
                nErrCount++;
                continue;
            }

            std::string strTagName(pTagRecord[i].tTagName.Buffer(), pTagRecord[i].tTagName.Length());
            if (m_mapTagInfo.find(strTagName) != m_mapTagInfo.end())
            {
                auto &pTagInfo = m_mapTagInfo[strTagName];
                OpcuaDataCache::GetInstance()->CacheData(pTagInfo->strPubTagName, std::move(pTagRecord[i].tTagValue));
            }
            else
            {
                CV_WARN(g_OpcuaServerLog, -1, "strTagName is not found. tagname:%s,nInterval:%s", strTagName.c_str(),
                        nInterval);
                continue;
            }
        }
        if (nErrCount > 0)
        {
            CV_WARN(g_OpcuaServerLog, -1,
                    "the number of points with error codes is %d, Open debug to view detailed information", nErrCount);
        }
        // Must be released
        dsfapi::Free_Int32_Ptr(&pErrorCode);
        dsfapi::Free_Tag_Record(&pTagRecord);
    };

    int32 nPos = 0;
    for (int i = 1; i <= nTagSize; ++i) // begin from 1
    {
        if (0 == i % BATCH_CNT)
        {
            int32 nBatchId = 0;
            int32 nRet = dsfapi::DR_Register_Tag(m_pContext, (const char **)(pTagName + nPos), BATCH_CNT, nInterval,
                                                 func, 0, &nBatchId);
            if (nRet != 0)
            {
                CV_ERROR(g_OpcuaServerLog, nRet, "DR_Register_Tag failed, nRet:%d", nRet);
                return nRet;
            }
            else
            {
                CV_INFO(g_OpcuaServerLog, "DR_Register_Tag success, batch_id =%d, cnt=%d", nBatchId, BATCH_CNT);
                m_mapBatch[nBatchId] = nInterval;
            }
            nPos = i;
        }
    }
    auto nLeft = nTagSize - nPos;
    if (nLeft > 0)
    {
        int32 nBatchId = 0;
        int32 nRet =
            dsfapi::DR_Register_Tag(m_pContext, (const char **)(pTagName + nPos), nLeft, nInterval, func, 0, &nBatchId);
        if (nRet != 0)
        {
            CV_ERROR(g_OpcuaServerLog, nRet, "DR_Register_Tag failed, nRet:%d", nRet);
            return nRet;
        }
        else
        {
            CV_INFO(g_OpcuaServerLog, "DR_Register_Tag success, batch_id =%d, cnt=%d", nBatchId, nLeft);
            m_mapBatch[nBatchId] = nInterval;
        }
    }
    return 0;
}

int COpcuaPublishManager::DealInnerTagData()
{
    /*
    The inner tag is added in the AddedEnerTags ;
    In this function, assign values periodically.
    */
    static uint32 nCnt = 0;

    while (!m_bStop)
    {
        if (nCnt % 10 == 0)
        {
            long nRmStatus = 0;
            auto nRes = GetRMStatus(&nRmStatus);
            if (nRes != 0)
            {
                CV_ERROR(g_OpcuaServerLog, nRes, "GetRMStatus failed");
            }
            else if (nRmStatus != m_nRmStatus)
            {
                CV_INFO(g_OpcuaServerLog, "m_nRmStatus changed : %ld -> %ld", m_nRmStatus, nRmStatus);
                m_nRmStatus = nRmStatus;
            }
        }

        if (nCnt % 10 == 1)
        {
            std::string strTagName("dsf.opcua_publish.inner.rm_status");
            if (m_mapTagInfo.find(strTagName) != m_mapTagInfo.end())
            {
                auto &pTagInfo = m_mapTagInfo[strTagName];
                OpcuaDataCache::GetInstance()->CacheData(pTagInfo->strPubTagName,
                                                         {(const char *)&m_nRmStatus, sizeof(m_nRmStatus)});
            }
            else
            {
                CV_WARN(g_OpcuaServerLog, -1, "inner strTagName is not found. tagname:%s", strTagName.c_str());
            }
        }

        if (nCnt % 10 == 2)
        {
            std::string strTagName("dsf.opcua_publish.inner.current_time");
            if (m_mapTagInfo.find(strTagName) != m_mapTagInfo.end())
            {
                auto &pTagInfo = m_mapTagInfo[strTagName];
                UA_DateTime now = UA_DateTime_now();
                OpcuaDataCache::GetInstance()->CacheData(pTagInfo->strPubTagName, {(const char *)&now, sizeof(now)});
            }
            else
            {
                CV_WARN(g_OpcuaServerLog, -1, "inner strTagName is not found. tagname:%s", strTagName.c_str());
            }
        }

        ++nCnt;
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
}

int COpcuaPublishManager::DealOpcuaWriteData()
{
    std::vector<std::shared_ptr<WriteData>> vecWriteData;
    while (!m_bStop)
    {
        vecWriteData.clear();
        while (true)
        {
            auto pWriteData = OpcuaDataCache::GetInstance()->DequeueWriteData();
            if (nullptr == pWriteData)
            {
                break;
            }
            vecWriteData.emplace_back(pWriteData);
            if (vecWriteData.size() >= kWriteDataMaxCnt)
            {
                break;
            }
        }
        if (vecWriteData.empty())
        {
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
            continue;
        }
        // sync write and get reponse
        int nRet = WriteDataToDrsdk(vecWriteData);
        if (nRet != 0)
        {
            CV_ERROR(g_OpcuaServerLog, nRet, "WriteDataToDrsdk failed, nRet:%d", nRet);
            // return nRet;
        }
    }
}

int COpcuaPublishManager::WriteDataToDrsdk(std::vector<std::shared_ptr<WriteData>> &vecWriteData)
{
    const auto nSize = vecWriteData.size();
    if (0 == nSize)
    {
        return 0;
    }

    char **pTagName = new char *[nSize];
    dsfapi::TagValue *pTagValue = new dsfapi::TagValue[nSize];

    int32 cnt = 0;
    for (size_t i = 0; i < nSize; ++i)
    {
        auto &pWriteData = vecWriteData[i];
        auto iter = m_mapTagInfoPub.find(pWriteData->strPubTagName);
        if (iter == m_mapTagInfoPub.end())
        {
            CV_WARN(g_OpcuaServerLog, -1, "WriteDataToDrsdk tagid not found. tagid:%s",
                    pWriteData->strPubTagName.c_str());
            continue;
        }
        auto &strTagName = iter->second->strTagName;
        pTagName[cnt] = const_cast<char *>(strTagName.data());
        if ("STRING" == iter->second->strType)
        {
            // added to bytes
            std::string strTagValue(iter->second->nLength + 3, 0);
            strTagValue[0] = static_cast<uint8>(iter->second->nLength);
            strTagValue[1] = static_cast<uint8>(std::min(iter->second->nLength, pWriteData->tagValue.Length()));
            memcpy(strTagValue.data() + 2, pWriteData->tagValue.Buffer(), (size_t)strTagValue[1]);
            pWriteData->tagValue.ReSet(strTagValue.data(), strTagValue.length());
        }

        pTagValue[cnt] = std::move(pWriteData->tagValue);
        cnt++;
    }

    auto res = dsfapi::DR_Write(m_pContext, (const char **)pTagName, pTagValue, cnt);
    LOG_INFO << "DR_Write:res = " << res << std::endl;

    if (nullptr == pTagName)
    {
        delete[] pTagName;
        pTagName = nullptr;
    }
    if (nullptr == pTagValue)
    {
        delete[] pTagValue;
        pTagValue = nullptr;
    }
}

int32 COpcuaPublishManager::RegisterTagGroupsToUaServer()
{
    int nRet = 0;
    for (auto &item : m_mapIntervalTagInfo)
    {
        auto &tTags = item.second;
        nRet = RegisterTagsToUaServer(tTags);

        if (nRet != 0)
        {
            CV_ERROR(g_OpcuaServerLog, nRet, "RegisterTagsToUaServer failed, interval_id: %d, nRet:%d", item.first,
                     nRet);
            return nRet;
        }
    }

    // inner tag value
    nRet = RegisterTagsToUaServer(m_vecInnerTagInfo);
    if (nRet != 0)
    {
        CV_ERROR(g_OpcuaServerLog, nRet, "RegisterTagsToUaServer  inner tag failed,   nRet:%d", nRet);
        return nRet;
    }

    return 0;
}

int32 COpcuaPublishManager::RegisterTagsToUaServer(const OpcuaTagInfoPtrVec &pTags)
{
    int nRet = 0;
    for (const auto &pTagInfo : pTags)
    {

        // nRet = m_pOpcuaServer->AddVariable(pTagInfo);
        nRet = m_pOpcuaServer->AddDataSourceVariable(pTagInfo);
        if (nRet != 0)
        {
            CV_ERROR(g_OpcuaServerLog, -1, "AddVariable failed, tag_name: %s, nRet:%d", pTagInfo->strTagName.data(),
                     nRet);
            return nRet;
        }
    }
    return 0;
}