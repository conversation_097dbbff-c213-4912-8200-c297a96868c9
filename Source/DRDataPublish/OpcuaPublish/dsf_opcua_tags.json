{"opcua_port": 4668, "anonymous": true, "opcua_login": [{"opcua_user": "dsf", "opcua_password": "dsf"}, {"opcua_user": "admin", "opcua_password": "admin"}], "tags": [{"tagname": "STD::TEST_LWORD", "pub_tagname": "STD.TEST_LWORD", "interval": 1000, "type": "LWORD", "length": 8}, {"tagname": "STD::TEST_LINT", "pub_tagname": "STD.TEST_LINT", "interval": 1000, "type": "LINT", "length": 8}, {"tagname": "STD::TEST_ULINT", "pub_tagname": "STD.TEST_ULINT", "interval": 1000, "type": "ULINT", "length": 8}, {"tagname": "STD::TEST_LREAL", "pub_tagname": "STD.TEST_LREAL", "interval": 1000, "type": "LREAL", "length": 8}, {"tagname": "STD::TEST_LTIME", "pub_tagname": "STD.TEST_LTIME", "interval": 1000, "type": "LTIME", "length": 8}, {"tagname": "STD::TEST_DWORD", "pub_tagname": "STD.TEST_DWORD", "interval": 1000, "type": "DWORD", "length": 4}, {"tagname": "STD::TEST_DINT", "pub_tagname": "STD.TEST_DINT", "interval": 1000, "type": "DINT", "length": 4}, {"tagname": "STD::TEST_UDINT", "pub_tagname": "STD.TEST_UDINT", "interval": 1000, "type": "UDINT", "length": 4}, {"tagname": "STD::TEST_REAL", "pub_tagname": "STD.TEST_REAL", "interval": 1000, "type": "REAL", "length": 4}, {"tagname": "STD::TEST_TIME", "pub_tagname": "STD.TEST_TIME", "interval": 1000, "type": "TIME", "length": 4}, {"tagname": "STD::TEST_WORD", "pub_tagname": "STD.TEST_WORD", "interval": 1000, "type": "WORD", "length": 2}, {"tagname": "STD::TEST_INT", "pub_tagname": "STD.TEST_INT", "interval": 1000, "type": "INT", "length": 2}, {"tagname": "STD::TEST_UINT", "pub_tagname": "STD.TEST_UINT", "interval": 1000, "type": "UINT", "length": 2}, {"tagname": "STD::TEST_BOOL", "pub_tagname": "STD.TEST_BOOL", "interval": 300, "type": "BOOL", "length": 1}, {"tagname": "STD::TEST_BYTE", "pub_tagname": "STD.TEST_BYTE", "interval": 300, "type": "BYTE", "length": 1}, {"tagname": "STD::TEST_SINT", "pub_tagname": "STD.TEST_SINT", "interval": 300, "type": "SINT", "length": 1}, {"tagname": "STD::TEST_USINT", "pub_tagname": "STD.TEST_USINT", "interval": 500, "type": "USINT", "length": 1}, {"tagname": "STD::TEST_CHAR", "pub_tagname": "STD.TEST_CHAR", "interval": 500, "type": "CHAR", "length": 1}]}