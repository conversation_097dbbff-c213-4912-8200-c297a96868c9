

#ifndef __PUBLISH_MANAGER_H__
#define __PUBLISH_MANAGER_H__
#include "RMAPI.h"
#include "data_types.h"
#include "dsfapi.h"
#include "nlohmann/json.hpp"
#include "opcua_data_cache.h"
#include "opcua_publish_def.h"
#include "opcua_server.h"

#include <atomic>

using json = nlohmann::json;

class COpcuaPublishManager
{
  public:
    COpcuaPublishManager(const int nOPCUAPubTagNumber);
    ~COpcuaPublishManager();
    int32 Init();
    int32 Run();
    int32 Shutdown();

#ifdef UNIT_TEST
  public:
#else
  private:
#endif
    dsfapi::DRSdkContext *DrsdkInit();
    int32 LoadConfig();
    int32 LoadConnnectConfig(const json &jConfig);
    int32 LoadTagsConfig(const json &jConfig);
    int32 AddedInnerTags();
    int32 RegisterTagGroupsToDrsdk();
    int32 RegisterTagsToDrsdk(const int32 nInterval, const OpcuaTagInfoPtrVec &pTags);
    int32 DealInnerTagData();
    int32 DealOpcuaWriteData();
    int32 WriteDataToDrsdk(std::vector<std::shared_ptr<WriteData>> &vecWriteData);

    int32 RegisterTagGroupsToUaServer();
    int32 RegisterTagsToUaServer(const OpcuaTagInfoPtrVec &pTags);

  private:
    int32 m_nOPCUAPubTagNumber = 100;
    std::atomic<bool> m_bStop = {false};
    std::atomic<bool> m_bInit = {false};
    std::atomic<bool> m_bOpcuaRuning = {false};
    std::string m_strConfigFileName = "";
    dsfapi::DRSdkContext *m_pContext = nullptr;
    COpcuaServer *m_pOpcuaServer = nullptr;

    OpcuaConnectInfoPtr m_pOpcuaConnectInfo = nullptr;                  // opcua server info
    std::unordered_map<std::string, OpcuaTagInfoPtr> m_mapTagInfo;      // key: tag name
    std::unordered_map<std::string, OpcuaTagInfoPtr> m_mapTagInfoPub;   // key: tag pub name
    std::unordered_map<int32, OpcuaTagInfoPtrVec> m_mapIntervalTagInfo; // key: interval
    OpcuaTagInfoPtrVec m_vecInnerTagInfo;
    std::unordered_map<int32, bool> m_mapRegFlag;
    std::unordered_map<int32, int32> m_mapBatch;

    std::unique_ptr<std::thread> m_pOpcuaRunThread = nullptr;
    std::unique_ptr<std::thread> m_pWriteDataThread = nullptr;
    std::unique_ptr<std::thread> m_pInnerTagThread = nullptr;

    long m_nRmStatus = RM_STATUS_UNAVALIBLE;
};
#endif // __PUBLISH_MANAGER_H__