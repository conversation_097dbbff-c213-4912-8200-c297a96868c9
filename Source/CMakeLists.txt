cmake_minimum_required(VERSION 3.10)
PROJECT(DR)
# SUBDIRS(common ext pdb drivers DRAcquisitionService DRDeployService)
# SUBDIRS(common ext pdb drivers DRAcquisitionService DRDeployService DRSdk DRDataPublish DRDataService simulateDrv)
# SUBDIRS(common ext Utility-backup pdb drivers DRAcquisitionService DRDeployService DRDataService DRSdk)

INCLUDE(cmake/prebuild.cmake)
prebuild()

add_subdirectory(common)
add_subdirectory(ext)
add_subdirectory(pdb)
add_subdirectory(drivers)
add_subdirectory(DRAcquisitionService)
add_subdirectory(DRDeployService)
add_subdirectory(DRSdk)
add_subdirectory(DRDataPublish)
add_subdirectory(DRDataService)
add_subdirectory(DRDataFileService)
add_subdirectory(DRProcessmgr)
add_subdirectory(DRSourceMonitor)
add_subdirectory(DRDataArchive)
add_subdirectory(DSFMonitor)
add_subdirectory(DSFEvent)

# if(${CMAKE_SYSTEM_NAME} MATCHES "Linux")
#     # 检查发行版特定标识文件
#     if(EXISTS "/etc/redhat-release")
#         message(STATUS "####Building on Red Hat")
#         # Red Hat-specific subdirectories
#         add_subdirectory(DRDataArchive)

#     elseif(EXISTS "/etc/lsb-release")
#         file(READ "/etc/lsb-release" LSB_CONTENT)
#         if(LSB_CONTENT MATCHES "Ubuntu")
#             message(STATUS "#####Building on Ubuntu")
#             # Ubuntu-specific subdirectories

#         endif()
#     else()
#         message(STATUS "Unknown Linux distribution, building with default settings")
#         # Default subdirectories for unknown Linux

#     endif()
# else()
#     message(STATUS "Unknown platform: ${CMAKE_SYSTEM_NAME}")
# endif()
