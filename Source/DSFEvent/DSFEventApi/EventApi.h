#ifndef SHM_API_HPP
#define SHM_API_HPP

#include <string>

#ifdef _WIN32
#ifdef SHM_API_EXPORTS
#define SHM_API extern "C" __declspec(dllexport)
#else
#define SHM_API extern "C" __declspec(dllimport)
#endif
#else
#define SHM_API extern "C"
#endif

#define EVENT_LOG(format, ...) write_to_shared_memory(format, __VA_ARGS__)

SHM_API int write_to_shared_memory(const char *format, ...);

#endif // SHM_API_HPP
