#include "DSFEventService.h"
#include "EventDBHandler.h"
#include "common/CVLog.h"

#define EVTSERVERNAME "dsfeventservice"

CCVLog g_CVLogEventServer;
using namespace std;

CServiceBase *g_pServiceHandler = new CDSFEventService();

CDSFEventService::CDSFEventService() : CServiceBase(EVTSERVERNAME, false, "")
{
	ExitWhenInitFailed(false);
}

CDSFEventService::~CDSFEventService()
{
}

void CDSFEventService::SetLogFileName()
{
	g_CVLogEventServer.SetLogFileNameThread(EVTSERVERNAME);
}

long CDSFEventService::Init(int argc, char* args[])
{
	m_pEventDBHandler = new CEventDBHandler();
	m_pEventDBHandler->Initialize();
}

long CDSFEventService::Start()
{
	m_pEventDBHandler->activate(THR_NEW_LWP | THR_JOINABLE, 1);
}

long CDSFEventService::Fini()
{
	delete m_pEventDBHandler;
	return ICV_SUCCESS;
}

void CDSFEventService::PrintStartUpScreen()
{
	std::cout << "+========================================+"<< std::endl;
	std::cout << ("|      <<DSF Event service running>>     |")<< std::endl;
	std::cout << ("|                                        |")<< std::endl;
	std::cout << ("|        q/Q:exit                        |")<< std::endl;
	std::cout << ("|        other key: display tips         |")<< std::endl; 
	std::cout << "+========================================+"<< std::endl;
}

bool CDSFEventService::ProcessCmd( char c )
{
	return false;
}
