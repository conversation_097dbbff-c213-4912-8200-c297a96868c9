#ifndef _DSF_EVENTSERVICE_H_
#define _DSF_EVENTSERVICE_H_

#include "common/ServiceBase.h"
#include "EventDBHandler.h"

class CDSFEventService : public CServiceBase
{
public:
	CDSFEventService();
	virtual ~CDSFEventService();

	virtual long Init(int argc, char *args[]);
	virtual long Start();
	virtual long Fini();

	virtual void PrintStartUpScreen();
	virtual void SetLogFileName();

	virtual bool ProcessCmd(char c);

private:
	CEventDBHandler *m_pEventDBHandler;
};

#endif // _DSF_EVENTSERVICE_H_
