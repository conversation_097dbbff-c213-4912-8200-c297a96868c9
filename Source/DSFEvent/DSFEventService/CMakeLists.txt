cmake_minimum_required(VERSION 3.10)
############FOR_MODIFIY_BEGIN#######################
PROJECT (DSFEventService)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} DSFEventService.cpp EventDBHandler.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME dsfeventservice)
#Setting library type used when build a library
# SET(LIB_TYPE STATIC)

SET(LINK_LIBS ACE drlogimpl drcomm cppsqlite sqlite3 intl servicebase shmqueue iconv boost_filesystem boost_system pthread licverify License)
IF(UNIX)
	IF(CMAKE_SYSTEM MATCHES "Linux")
		SET(LINK_LIBS ${LINK_LIBS} protobuf)
		if( CMAKE_SIZEOF_VOID_P EQUAL 4 )
			SET(LINK_LIBS ${LINK_LIBS} SentinelKeys32)
		endif( CMAKE_SIZEOF_VOID_P EQUAL 4 )
	ENDIF(CMAKE_SYSTEM MATCHES "Linux")

	IF(CMAKE_SYSTEM MATCHES "SunOS.*")
		SET(LINK_LIBS ${LINK_LIBS}  socket)
	ENDIF(CMAKE_SYSTEM MATCHES "SunOS.*")
	IF(HPUX)
		SET(LINK_LIBS ${LINK_LIBS} pthread)
	ENDIF(HPUX)
ENDIF(UNIX)

############FOR_MODIFIY_END#########################

INCLUDE($ENV{DRDIR}CMakeCommonExec)
IF(MSVC)
	if( CMAKE_SIZEOF_VOID_P EQUAL 8 )
		set_target_properties(${TARGET_NAME} PROPERTIES STATIC_LIBRARY_FLAGS "/machine:x64")
	endif( CMAKE_SIZEOF_VOID_P EQUAL 8 )
	SET_TARGET_PROPERTIES(${TARGET_NAME} PROPERTIES LINK_FLAGS "/ignore:4099")
ENDIF(MSVC)
IF(CMAKE_HOST_UNIX)
        include(CheckCXXCompilerFlag)
        CHECK_CXX_COMPILER_FLAG("-std=c++11" COMPILER_SUPPORTS_CXX11)
        if(COMPILER_SUPPORTS_CXX11)
                set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -std=c++11") # set C++ 11
        endif(COMPILER_SUPPORTS_CXX11)
ENDIF(CMAKE_HOST_UNIX)