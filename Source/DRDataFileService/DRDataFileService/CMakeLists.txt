cmake_minimum_required(VERSION 3.10)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

PROJECT (DRDataFileService)

INCLUDE($ENV{DRDIR}CMakeCommon)

SET(SRCS ${SRCS} 
DRDataFileService.cpp
DataReceiver.cpp
Task.cpp
TaskManager.cpp
ConfigReader.cpp)
SET(TARGET_NAME dsfdatafileservice)

include_directories(${CMAKE_SOURCE_DIR}/DRSdk/dsfapi)

SET(LINK_LIBS 
ACE drlog drlogimpl drrmapi drcomm tinyxml2 drnetqueue servicebase shmqueue  licverify License dsfapi DataFileRecorder)

INCLUDE($ENV{DRDIR}CMakeCommonExec)
