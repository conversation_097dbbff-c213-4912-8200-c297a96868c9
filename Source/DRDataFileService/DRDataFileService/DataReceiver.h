#ifndef DATARECEIVER_H
#define DATARECEIVER_H
#include "dsfapi.h"
#include "ConfigReader.h"
#include <unordered_map>
#include <shared_mutex>
#include <set>
// 接收dsfapi数据，写入快照
class DataReceiver
{
public:
    DataReceiver();
    ~DataReceiver();

    bool Init(int32 nHisTagNumber, FileInfo fileInfo, std::vector<ModuleInfo> vecModuleInfo, std::map<std::string, std::set<std::string>> mapTagsName, std::map<std::string, FDAADataType> mapTagsType);
    bool Start();
    bool Stop();

    std::map<std::string, double> &GetSignalDataSnapshot(int moduleNo)
    {
        std::lock_guard<std::mutex> lock(*m_mapCacheDataMutex[moduleNo]);
        return m_mapSignalDataSnapshot[moduleNo];
    }

    // std::unordered_map<std::string, bool> &GetSignalStatus()
    // {
    //     return m_mapSignalStatus;
    // }

private:
    //dsf api接口对象
    dsfapi::DRSdkContext *m_pContext = nullptr;

    // 模块，存储信息
    FileInfo m_fileInfo;
    std::vector<ModuleInfo> m_vecModuleInfo;
    std::map<std::string, std::set<std::string>> m_mapTagsName;
    std::map<std::string, FDAADataType> m_mapTagsType;

    // 信号数据快照
    std::map<int, std::map<std::string, double>> m_mapSignalDataSnapshot;

    std::unordered_map<uint32, std::shared_ptr<std::mutex>> m_mapCacheDataMutex;


    // 信号状态
    // std::unordered_map<std::string, bool> m_mapSignalStatus;

private:
    bool RegisterTag(ModuleInfo &module);
};

#endif