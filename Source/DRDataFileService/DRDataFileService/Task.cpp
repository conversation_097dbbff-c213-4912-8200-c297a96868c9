#include "Task.h"
#include <iostream>
#include <boost/asio.hpp>
#include <memory>
#include <vector>
#include <unordered_map>
#include <string>
#include <chrono>
#include "common/LogHelper.h" //for log
#include "common/cvcomm.hxx"  //for cvcomm
extern CCVLog g_dfsLog;

Task::Task(boost::asio::io_service& io,ModuleInfo moduleInfo,DataFileRecorder* dfRecorder,std::shared_ptr<DataReceiver> dfManager)
: 
m_deadlineTimer(io), 
m_interval(boost::posix_time::millisec(moduleInfo.iTimeBase)),
m_moduleNum(moduleInfo.iModuleNo),
m_pDataFileRecorder(dfRecorder),
m_stopped(false),
m_dfManager(dfManager),
m_timebase(moduleInfo.iTimeBase)
{

    for(const auto& signal : moduleInfo.vecSignalInfo) 
    {
        m_mapSignalData[signal.strSignalNo] = std::vector<double>(); // 初始化缓存数据为一个空的vector
        m_mapSignalDataSnapshot[signal.strSignalNo] = 0; // 初始化快照数据
    }

    std::thread([this]() { do_work(); }).detach();
}

void Task::do_work() {

    std::unique_lock<std::mutex> lock(m_mtx);
    while (!m_stopped)
    {
        m_cv.wait(lock, [&]{ return m_ready; }); // 等待 ready 为 true
    
        m_receiveCount++;
        m_mapSignalDataSnapshot = m_dfManager->GetSignalDataSnapshot(m_moduleNum);
        // m_mapSignalStatus = m_dfManager->GetSignalStatus();
        // 从快照拷贝数据到缓冲区
        for(const auto& item : m_mapSignalDataSnapshot) {
            // if(m_mapSignalStatus[item.first]) 
            // {
            // if(m_moduleNum==15)
            // {
            //     CV_INFO(g_dfsLog, "--------------moduleNo:%d--------signal addr:%s  value:%f------------------",m_moduleNum,item.first.c_str(),item.second);
            // }
            m_mapSignalData[item.first].push_back(item.second); // 将快照数据添加到缓存中
            // }
        }

        // 统计接收次数来判断是否可以写入文件
        if( m_receiveCount >= m_writeInterval / m_timebase) {
            m_receiveCount = 0; // 重置接收次数

            // 将缓存数据写入文件
            if(m_pDataFileRecorder != nullptr) {
                // CV_INFO(g_dfsLog, "WriteData called, thread ID: %ld,module No:%d, write count:%d,", std::this_thread::get_id(),m_moduleNum,m_writeInterval / m_timebase);
                // if(m_moduleNum==15)
                // {
                    // CV_INFO(g_dfsLog, "---------------module %d-------start------------------",m_moduleNum);
                
                    // //遍历m_mapSignalData第一个元素的所有值
                    // for(auto iter = m_mapSignalData.begin(); iter != m_mapSignalData.end(); iter++)
                    // {
                    //     CV_INFO(g_dfsLog, "signal addr:%s, count:%d",iter->first.c_str(),iter->second.size());
                    //     for(auto siter = iter->second.begin();siter!=iter->second.end();siter++)
                    //     {
                    //         CV_INFO(g_dfsLog, "module:%d, value:%f", m_moduleNum, *siter);
                    //     }
                    // }
                    // CV_INFO(g_dfsLog, "--------------------end---------------------");
                // }

                m_pDataFileRecorder->WriteData(m_mapSignalData);
                m_mapSignalData.clear(); // 清空缓存数据
            } else {
                CV_ERROR(g_dfsLog, -1, "DataFileRecorder is not initialized.");
            }
        }

        m_ready = false; // 重置 ready 状态
    }
}

void Task::start() {
    m_stopped = false;
    schedule();
}

void Task::stop() {
    m_stopped = true;
    m_deadlineTimer.cancel();
}


void Task::schedule() {
    if (m_stopped) return;
    m_deadlineTimer.expires_from_now(m_interval);
    auto self = shared_from_this();
    m_deadlineTimer.async_wait([this, self](const boost::system::error_code& ec) {
        if (!ec && !m_stopped) {
            {
                std::lock_guard<std::mutex> lock(m_mtx);
                m_ready = true; // 设置条件
            }
            m_cv.notify_one();
            schedule();
        }
    });
}