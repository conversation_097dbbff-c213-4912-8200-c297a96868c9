#ifndef DRDATAFILESERVICE_TASK_H
#define DRDATAFILESERVICE_TASK_H
#include <boost/asio.hpp>
#include <chrono>
#include "DataFileRecorder.h"
#include "DataReceiver.h"
#include <mutex>
#include <condition_variable>

/**
 * 1. 初始化定时任务：周期为模块周期
 * 2. 定周期从快照获取模块的最新值，也就是重采样
 * 3. 将重采样的数据写入文件中（简单处理就是一边重采样，一边写入。优化处理就是所有任务每过一段时间写入一次。）
 */
class Task : public std::enable_shared_from_this<Task> {
public:
    Task(boost::asio::io_service& io,ModuleInfo moduleInfo,DataFileRecorder* dfRecorder,std::shared_ptr<DataReceiver> dfManager);

    void start();

    void stop();

private:
    void schedule();

    void do_work();

    //快照
    std::map<std::string, double> m_mapSignalDataSnapshot;
    // std::unordered_map<std::string, bool> m_mapSignalStatus;
    //模块号
    int m_moduleNum;
    //模块周期,单位毫秒
    int m_timebase;
    //写入周期
    int m_writeInterval=1000; // 默认每1000毫秒写入一次
    //接收次数
    int m_receiveCount = 0;

    //缓存的数据
    std::map<std::string, std::vector<double>> m_mapSignalData;
    DataFileRecorder* m_pDataFileRecorder;
    std::shared_ptr<DataReceiver> m_dfManager;

    boost::asio::deadline_timer m_deadlineTimer;
    boost::posix_time::milliseconds m_interval;
    //线程停止标志
    bool m_stopped;

    //线程同步
    std::mutex m_mtx;
    std::condition_variable m_cv;
    bool m_ready = false;

};
#endif // DRDATAFILESERVICE_TASK_H