#include "ConfigReader.h"
#include "common/LogHelper.h" //for log
#include "common/cvcomm.hxx"  //for cvcomm
extern CCVLog g_dfsLog;
#include "tinyxml2.h"

bool ConfigReader::ReadDSConfigFile(const std::string &configFilePath, FileInfo &fileInfo)
{
    tinyxml2::XMLDocument doc;
    if (doc.LoadFile(configFilePath.c_str()) != tinyxml2::XML_SUCCESS)
    {
        CV_ERROR(g_dfsLog, -1, "Failed to load config file: %s", configFilePath.c_str());
        return false;
    }

    tinyxml2::XMLElement *rootElement = doc.FirstChildElement("DataStorageConfigurations");
    if (!rootElement)
    {
        CV_ERROR(g_dfsLog, -1, "Failed to find <DataStorageConfigurations> element in config file.");
        return false;
    }

    tinyxml2::XMLElement *dsElement = rootElement->FirstChildElement("DataStorage");
    if (!dsElement)
    {
        CV_ERROR(g_dfsLog, -1, "Failed to find <DataStorage> element in config file.");
        return false;
    }

    tinyxml2::XMLElement *filesElement = dsElement->FirstChildElement("Files");
    if (!filesElement)
    {
        CV_ERROR(g_dfsLog, -1, "Failed to find <Files> element in config file.");
        return false;
    }

    const char *strFileNamePrefix = filesElement->FirstChildElement("BaseFileName")->GetText();
    const char *strFileBasePath = filesElement->FirstChildElement("BaseDirectory")->GetText();
    const char *iFileMaxSerialNumberStr = filesElement->FirstChildElement("MaximumFileNumber")->GetText();
    const char *iFileRecordTimeStr = filesElement->FirstChildElement("MaxRecordingTime")->GetText();

    if (strFileNamePrefix && strFileBasePath && iFileRecordTimeStr && iFileMaxSerialNumberStr)
    {
        fileInfo.strFileNamePrefix = strFileNamePrefix;
        fileInfo.strFileBasePath = strFileBasePath;
        fileInfo.iFileMaxSerialNumber = std::stoul(iFileMaxSerialNumberStr);
        fileInfo.iFileRecordTime = std::stoul(iFileRecordTimeStr);
        return true;
    }
    else
    {
        CV_ERROR(g_dfsLog, -1, "Failed to read all required elements from config file.");
        return false;
    }
}

bool ConfigReader::ReadIOConfigFile(const std::string &configFilePath, std::vector<ModuleInfo> &modules, std::map<std::string, FDAADataType> &mapTagsType, std::map<std::string, std::set<std::string>> &mapTagsName)
{
    tinyxml2::XMLDocument doc;
    if (doc.LoadFile(configFilePath.c_str()) != tinyxml2::XML_SUCCESS)
    {
        CV_ERROR(g_dfsLog, -1, "Failed to load config file:%s", configFilePath.c_str());
        return false;
    }

    tinyxml2::XMLElement *ioConfigElement = doc.FirstChildElement("IOConfiguration");
    if (!ioConfigElement)
    {
        CV_ERROR(g_dfsLog, -1, "Failed to find <IOConfiguration> element in config file.");
        return false;
    }

    tinyxml2::XMLElement *modulesElement = ioConfigElement->FirstChildElement("Modules");
    if (!modulesElement)
    {
        CV_ERROR(g_dfsLog, -1, "Failed to find <Modules> element in config file.");
        return false;
    }

    for (tinyxml2::XMLElement *moduleElement = modulesElement->FirstChildElement("Module"); moduleElement != nullptr; moduleElement = moduleElement->NextSiblingElement("Module"))
    {
        ModuleInfo module;
        if (std::stoi(moduleElement->FirstChildElement("Enabled")->GetText()) != 1)
            continue;
        module.iModuleNo = std::stoi(moduleElement->FirstChildElement("ModuleNo")->GetText());
        module.strModuleName = moduleElement->FirstChildElement("Name")->GetText();
        module.iTimeBase = std::stoi(moduleElement->FirstChildElement("Timebase")->GetText());

        // 解析模拟信号
        tinyxml2::XMLElement *analogElement = moduleElement->FirstChildElement("Analog");
        if (analogElement)
        {
            for (tinyxml2::XMLElement *signalElement = analogElement->FirstChildElement("Signal"); signalElement != nullptr; signalElement = signalElement->NextSiblingElement("Signal"))
            {
                if (std::stoi(signalElement->FirstChildElement("Active")->GetText()) != 1)
                    continue;
                SignalInfo signal;
                signal.strSignalNo = signalElement->FirstChildElement("No")->GetText();
                signal.strSignalName = signalElement->FirstChildElement("Name")->GetText();
                mapTagsName[signal.strSignalName].insert(signal.strSignalNo);
                mapTagsType[signal.strSignalName] = static_cast<FDAADataType>(std::stoi(signalElement->FirstChildElement("DataType")->GetText()));
                tinyxml2::XMLElement *unitElement = signalElement->FirstChildElement("Unit");
                if (unitElement && unitElement->GetText())
                {
                    signal.strSignalUnit = unitElement->GetText();
                }
                else
                {
                    signal.strSignalUnit = "";
                }
                module.vecSignalInfo.push_back(signal);
            }
        }

        // 解析数字信号
        tinyxml2::XMLElement *digitalElement = moduleElement->FirstChildElement("Digital");
        if (digitalElement)
        {
            for (tinyxml2::XMLElement *signalElement = digitalElement->FirstChildElement("Signal"); signalElement != nullptr; signalElement = signalElement->NextSiblingElement("Signal"))
            {
                if (std::stoi(signalElement->FirstChildElement("Active")->GetText()) != 1)
                    continue;
                SignalInfo signal;
                signal.strSignalNo = signalElement->FirstChildElement("No")->GetText();
                signal.strSignalName = signalElement->FirstChildElement("Name")->GetText();
                mapTagsName[signal.strSignalName].insert(signal.strSignalNo);
                mapTagsType[signal.strSignalName] = static_cast<FDAADataType>(std::stoi(signalElement->FirstChildElement("DataType")->GetText()));
                tinyxml2::XMLElement *unitElement = signalElement->FirstChildElement("Unit");
                if (unitElement && unitElement->GetText())
                {
                    signal.strSignalUnit = unitElement->GetText();
                }
                else
                {
                    signal.strSignalUnit = "";
                }
                module.vecSignalInfo.push_back(signal);
            }
        }

        modules.push_back(module);
    }

    if (modules.empty())
    {
        CV_ERROR(g_dfsLog, -1, "No module found in config file.");
        return false;
    }

    CV_INFO(g_dfsLog, "Loaded %d modules from config file.", modules.size());

    return true;
}