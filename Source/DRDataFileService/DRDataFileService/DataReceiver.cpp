#include "DataReceiver.h"
#include "common/LogHelper.h"
extern CCVLog g_dfsLog;
DataReceiver::DataReceiver()
{
}

DataReceiver::~DataReceiver()
{
    if (nullptr != m_pContext)
    {
        dsfapi::DR_Uninit(m_pContext);
        dsfapi::Free_DR_Sdk_Context(&m_pContext);
        m_pContext = nullptr;
    }
}

//注册信号到dsfapi
bool DataReceiver::RegisterTag(ModuleInfo &module)
{
    uint32_t nTagCount = 0;
    std::vector<std::string> signalList;
    for (auto &signal : module.vecSignalInfo)
    {
        nTagCount++;
        signalList.push_back(signal.strSignalName);
    }

    char **pTagNameList = new char *[nTagCount];
    for (uint32_t i = 0; i < signalList.size(); i++)
    {
        pTagNameList[i] = new char[signalList[i].size() + 1];
        strcpy(pTagNameList[i], signalList[i].c_str());
    }

    m_mapSignalDataSnapshot[module.iModuleNo].clear();
    for (auto &signal : module.vecSignalInfo)
    {
        m_mapSignalDataSnapshot[module.iModuleNo][signal.strSignalNo] = 0; // 初始化快照数据
    }

    auto func = [&](const int32 nBatchId, dsfapi::TagRecord *pTagRecord, int32 *pErrorCode, const int32 nTagCount)
    {
        std::lock_guard<std::mutex> lock(*m_mapCacheDataMutex[module.iModuleNo]);
        //准备接收值的容器
        bool bValue = true;
        uint8_t u8Value = 0;
        uint16_t u16Value = 0;
        uint32_t u32Value = 0;
        int16_t i16Value = 0;
        int32_t i32Value = 0;
        float f32Value = 0;

        for (int i = 0; i < nTagCount; i++)
        {
            std::string strTagFDAAAddress;
            std::string prefix;
            //查找以模块号开头的信号地址
            if (m_mapTagsType[pTagRecord[i].tTagName.Buffer()]==FDAA_DATA_TYPE_BOOL)
            {
                prefix = std::to_string(module.iModuleNo) + ".";
            }
            else
            {
                prefix = std::to_string(module.iModuleNo) +":";
            }

            for (auto it = m_mapTagsName[pTagRecord[i].tTagName.Buffer()].begin(); it != m_mapTagsName[pTagRecord[i].tTagName.Buffer()].end(); ++it) {
                if (it->compare(0, prefix.size(), prefix) == 0) {
                    strTagFDAAAddress=*it;
                }
            }
            
            if (pErrorCode[i] == 0)
            {
                // m_mapSignalStatus[m_mapTagsName[pTagRecord[i].tTagName.Buffer()]] = true; // 标记信号有效
                switch (m_mapTagsType[pTagRecord[i].tTagName.Buffer()])
                {
                case FDAA_DATA_TYPE_BOOL:
                    bValue = *reinterpret_cast<bool *>(pTagRecord[i].tTagValue.Buffer());
                    m_mapSignalDataSnapshot[module.iModuleNo][strTagFDAAAddress] = bValue;
                    CV_TRACE(g_dfsLog, "++++++++++++++++++++++++++ tag: %s, value: %d", pTagRecord[i].tTagName.Buffer(), bValue);
                    break;
                case FDAA_DATA_TYPE_BYTE:
                    u8Value = *reinterpret_cast<uint8_t *>(pTagRecord[i].tTagValue.Buffer());
                    m_mapSignalDataSnapshot[module.iModuleNo][strTagFDAAAddress] = u8Value;
                    CV_TRACE(g_dfsLog, "++++++++++++++++++++++++++ tag: %s, value: %d", pTagRecord[i].tTagName.Buffer(), u8Value);
                    break;
                case FDAA_DATA_TYPE_WORD:
                    u16Value = *reinterpret_cast<uint16_t *>(pTagRecord[i].tTagValue.Buffer());
                    m_mapSignalDataSnapshot[module.iModuleNo][strTagFDAAAddress]=u16Value;
                    CV_TRACE(g_dfsLog, "++++++++++++++++++++++++++ tag: %s, value: %d", pTagRecord[i].tTagName.Buffer(), u16Value);
                    break;
                case FDAA_DATA_TYPE_DWORD:
                    u32Value = *reinterpret_cast<uint32_t *>(pTagRecord[i].tTagValue.Buffer());
                    m_mapSignalDataSnapshot[module.iModuleNo][strTagFDAAAddress]=u32Value;
                    CV_TRACE(g_dfsLog, "++++++++++++++++++++++++++ tag: %s, value: %d", pTagRecord[i].tTagName.Buffer(), u32Value);
                    break;
                case FDAA_DATA_TYPE_INT:
                    i16Value = *reinterpret_cast<int16_t *>(pTagRecord[i].tTagValue.Buffer());
                    m_mapSignalDataSnapshot[module.iModuleNo][strTagFDAAAddress]=i16Value;
                    CV_TRACE(g_dfsLog, "++++++++++++++++++++++++++ tag: %s, value: %d", pTagRecord[i].tTagName.Buffer(), i16Value);
                    break;
                case FDAA_DATA_TYPE_DINT:
                    i32Value = *reinterpret_cast<int32_t *>(pTagRecord[i].tTagValue.Buffer());
                    m_mapSignalDataSnapshot[module.iModuleNo][strTagFDAAAddress]=i32Value;
                    CV_TRACE(g_dfsLog, "++++++++++++++++++++++++++ tag: %s, value: %d", pTagRecord[i].tTagName.Buffer(), i32Value);
                    break;
                case FDAA_DATA_TYPE_REAL:
                    f32Value = *reinterpret_cast<float *>(pTagRecord[i].tTagValue.Buffer());
                    m_mapSignalDataSnapshot[module.iModuleNo][strTagFDAAAddress]=f32Value;
                    CV_TRACE(g_dfsLog, "++++++++++++++++++++++++++ tag: %s, value: %f", pTagRecord[i].tTagName.Buffer(), f32Value);
                    break;
                default:
                    break;
                }
            }
            else
            {
                //m_mapSignalStatus[m_mapTagsName[pTagRecord[i].tTagName.Buffer()]] = false; // 标记信号无效
                CV_ERROR(g_dfsLog,-1, "ERROR tag: %s, value: %s", pTagRecord[i].tTagName.Buffer(), pTagRecord[i].tTagValue.Buffer());
            }
        }

        // LOG_INFO << "++++++++++++++++++++++++++ Write Module "<<module.iModuleNo<<" Module Name : "<<module.strModuleName << std::endl;
        // CV_INFO(g_dfsLog, "++++++++++++++++++++++++++ Write Module %d Module Name : %s",module.iModuleNo,module.strModuleName.c_str());
       
        dsfapi::Free_Int32_Ptr(&pErrorCode);
        dsfapi::Free_Tag_Record(&pTagRecord);
    };

    int32 nRet = 0;
    int32 nBatchId = 0;
    nRet = dsfapi::DR_Register_Tag(m_pContext, (const char **)pTagNameList, nTagCount, module.iTimeBase, func, 0, &nBatchId);
    if (nRet != 0)
    {
        CV_ERROR(g_dfsLog, nRet, "DR_Register_Tag failed, nRet:%d", nRet);
        return nRet==0;
    }
    else
    {
        CV_INFO(g_dfsLog, "DR_Register_Tag success, batch_id =%d", nBatchId);
    }

    return nRet==0;
}

bool DataReceiver::Init(int32 nHisTagNumber,FileInfo fileInfo,std::vector<ModuleInfo> vecModuleInfo,std::map<std::string, std::set<std::string>> mapTagsName,std::map<std::string, FDAADataType> mapTagsType)
{
    m_vecModuleInfo = vecModuleInfo;
    m_fileInfo = fileInfo;
    m_mapTagsType = mapTagsType;
    m_mapTagsName = mapTagsName;


    // 统计点数，判断是否可以运行
    int totalItems = 0;
    for(auto &module : m_vecModuleInfo)
    {
        totalItems+=module.vecSignalInfo.size();
    }

    if(totalItems > nHisTagNumber)
    {
        CV_ERROR(g_dfsLog,-1,"tag number is too large, config tag size: %d License Tag Number:%d", totalItems, nHisTagNumber);
        return false;
    }

    dsfapi::DRSdkConnectParam tConnectParam;
    // strncpy(tConnectParam.szServerIp, "127.0.0.1", sizeof(tConnectParam.szServerIp) - 1);
    // tConnectParam.nServerPort = 1234;
    // tConnectParam.nConnectTimeoutMs = 1000;

    dsfapi::DRSdkOption tOption;
    // tOption.nThreadPoolNum = 10;
    // tOption.nRequestWaitTimeoutMs = 1000;
    CV_INFO(g_dfsLog,"DR_Init Connect Param : %s, SDK Option : %s",tConnectParam.ToString().c_str(), tOption.ToString().c_str());


    m_pContext = dsfapi::DR_Init(tConnectParam, tOption);
    if (nullptr == m_pContext || m_pContext->errorcode != 0)
    {
        dsfapi::Free_DR_Sdk_Context(&m_pContext);
        return false;
    }

    for (auto &module : m_vecModuleInfo)
    {
        m_mapCacheDataMutex.emplace(module.iModuleNo, std::make_shared<std::mutex>());
    }

    return true;
}

/*
    如果点注册失败，则一直重试，重试间隔10秒
*/
bool DataReceiver::Start()
{
    //等待时长
    const int32 nWaitTime = 10;
    //因为模块频率可能不同，所以这里简单的按模块来分别注册
    bool bRetryFlag = true;
    while(bRetryFlag)
    {
        bRetryFlag = false;
        for (auto &module : m_vecModuleInfo)
        {
            if(!RegisterTag(module))
            {
                bRetryFlag = true;
                CV_ERROR(g_dfsLog,-1,"RegisterTag failed, retry after %d seconds",nWaitTime);
                std::this_thread::sleep_for(std::chrono::seconds(nWaitTime));
                break;
            }
        }

    }

    CV_INFO(g_dfsLog,"Start DSFDataFileManager success")

    return true;
}

bool DataReceiver::Stop()
{
    // 先停止数据源，否则会导致回调函数写入空对象
    if (nullptr != m_pContext)
    {
        dsfapi::DR_Uninit(m_pContext);
        dsfapi::Free_DR_Sdk_Context(&m_pContext);
    }
    m_pContext = nullptr;

}