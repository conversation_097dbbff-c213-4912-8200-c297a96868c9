#ifndef TASKMANAGER_H
#define TASKMANAGER_H
#include <boost/asio.hpp>
#include "Task.h"
#include "DataReceiver.h"

class TaskManager {
public:
    TaskManager(boost::asio::io_service& io, std::shared_ptr<DataReceiver> dfManager);
    void add_task(ModuleInfo moduleInfo, DataFileRecorder* dfRecorder);
    void start_all();
    void stop_all();

private:
    boost::asio::io_service& m_ioService;
    std::unordered_map<int, std::shared_ptr<Task>> m_tasks;
    std::shared_ptr<DataReceiver> m_dfManager;
};

#endif // TASKMANAGER_H