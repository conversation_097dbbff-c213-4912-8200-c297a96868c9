#ifndef DRDataFileService_H
#define DRDataFileService_H
#include "common/ServiceBase.h"
#include "DataReceiver.h"
#include "TaskManager.h"
#include "RMAPI.h"
#include <set>
class DRDataFileService : public CServiceBase
{
public:
    /**
     * @brief          Acquisition Service Constructor
     * @version        2024/10/02	huangcan	Initial Version
     */
	DRDataFileService();

    /**
     * @brief          Acquisition Init
     * @param [in]     argc  
     * @param [out]    args  
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual long Init(int argc, char* args[]);

    /**
     * @brief          Acquisition refresh
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual void Refresh();

    /**
     * @brief          Acquisition Start
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual long Start();

    /**
     * @brief          PrintStartUpScreen
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual void PrintStartUpScreen();

    /**
     * @brief          PrintHelpScreen
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual void PrintHelpScreen();

    /**
     * @brief          release resource
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual long Fini();

    /**
     * @brief          cmd process
     * @param [in]     c  cmd message
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual bool ProcessCmd(char c);

    /**
     * @brief          set log file name
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual void SetLogFileName();

    /**
     * @brief          other things
     * @return
     * @version        2024/10/02	huangcan	Initial Version
     */
	virtual void Misc();
private:
    std::atomic<bool> m_bStop = {false};
    void GetRmStatusThreadCallback();

    std::shared_ptr<DataReceiver> m_pDataReceiver;
    std::shared_ptr<TaskManager> m_pTaskManager;
    std::shared_ptr<boost::asio::io_service> m_pIoService;
    DataFileRecorder* m_pDataFileRecorder;


    //配置信息
    FileInfo m_fileInfo;
    std::vector<ModuleInfo> m_vecModuleInfo;
    std::map<std::string, FDAADataType> m_mapTagsType;
    //dsf信号名称:fdaa信号地址
    std::map<std::string, std::set<std::string>> m_mapTagsName;

    //心跳检测
    long m_nRmStatus = RM_STATUS_UNAVALIBLE;                     // 0: inactive, 1: active, 2: unavaliabe
    std::unique_ptr<std::thread> m_pGetRmStatusThread = nullptr; // thread to get rm status
    std::atomic<bool> m_bBackupFileFlag = {false};               // last rm status
};

#endif