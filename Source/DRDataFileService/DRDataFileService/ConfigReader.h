#ifndef CONFIG_READER_H
#define CONFIG_READER_H

#include "DataFileRecorder.h"
#include <set>

enum FDAADataType
{
    FDAA_DATA_TYPE_BOOL = 0,
    FDAA_DATA_TYPE_BYTE,
    FDAA_DATA_TYPE_WORD,
    FDAA_DATA_TYPE_DWORD,
    FDAA_DATA_TYPE_INT,
    FDAA_DATA_TYPE_DINT,
    FDAA_DATA_TYPE_REAL
};

class ConfigReader
{
public:
    ConfigReader() = delete;
    ~ConfigReader() = delete;

    // 读取存储配置文件信息：CurrentDataStorageConfig.ds
    static bool ReadDSConfigFile(const std::string &configFilePath, FileInfo &fileInfo);
    // 读取模块配置文件信息：CurrentIoConfig.ds
    static bool ReadIOConfigFile(const std::string &configFilePath, std::vector<ModuleInfo> &modules, std::map<std::string, FDAADataType> &mapTagsType, std::map<std::string, std::set<std::string>> &mapTagsName);
};

#endif // CONFIG_READER_H