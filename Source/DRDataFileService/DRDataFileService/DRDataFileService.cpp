#include "DRDataFileService.h"
#include "ConfigReader.h"
#include <memory>
CCVLog g_dfsLog;


DRDataFileService::DRDataFileService() 
: 
CServiceBase ("dsfdatafileservice",  true, "DatArchiveService")
{
	ExitWhenInitFailed(false);//默认就是不退出的
}

void DRDataFileService::GetRmStatusThreadCallback()
{
    pthread_setname_np(pthread_self(), "rm_status");
    while (!m_bStop.load())
    {
        long nRmStatus = 0;
        auto nRes = GetRMStatus(&nRmStatus);
        if (nRes != 0)
        {
            CV_ERROR(g_dfsLog, nRes, "GetRMStatus failed");
        }
        else if (nRmStatus != m_nRmStatus)
        {
            CV_INFO(g_dfsLog, "m_nRmStatus changed : %ld -> %ld", m_nRmStatus, nRmStatus);
            m_nRmStatus = nRmStatus;
        }
        std::this_thread::sleep_for(std::chrono::milliseconds(300));
    }
}

long DRDataFileService::Init( int argc, char* args[] )
{
    m_pGetRmStatusThread.reset(new std::thread(&DRDataFileService::GetRmStatusThreadCallback, this));

	std::string strHisTagNumber = "";
    int32 nHisTagNumber = 100; //default value
    long ret = GetExetendedLicInfo("DatHisTagNumber", strHisTagNumber);
    if (ret == 0)
    {
        nHisTagNumber = std::stoi(strHisTagNumber);
    }

    std::string m_strCfgFile = CVComm.GetCVProjCfgPath();

    //加载存储配置文件
    if(!ConfigReader::ReadDSConfigFile(m_strCfgFile + "/CurrentDataStorageConfig.ds", m_fileInfo))
    {
        return -1;
    }

    m_fileInfo.strFileBasePath = m_strCfgFile + "/../" + m_fileInfo.strFileBasePath;

    //加载IO配置文件
    if (!ConfigReader::ReadIOConfigFile(m_strCfgFile + "/CurrentIoConfig.ds", m_vecModuleInfo, m_mapTagsType, m_mapTagsName))
    {
        return -1;
    }

    m_pDataReceiver = std::shared_ptr<DataReceiver>(new DataReceiver());

    if (!m_pDataReceiver->Init(nHisTagNumber,m_fileInfo,m_vecModuleInfo,m_mapTagsName,m_mapTagsType))
    {
        CV_ERROR(g_dfsLog, ret, "Failed to Init data file manager, ret = %d", ret);
		return -1;
    }

    m_pIoService = std::shared_ptr<boost::asio::io_service>(new boost::asio::io_service());
    if (nullptr == m_pIoService)
    {
        CV_ERROR(g_dfsLog,-1,"Failed to create io_service");
        return -1;
    }

    m_pTaskManager = std::shared_ptr<TaskManager>(new TaskManager(*m_pIoService, m_pDataReceiver));

    if (nullptr == m_pTaskManager)
    {
        CV_ERROR(g_dfsLog,-1,"Failed to create TaskManager");
        return -1;
    }


    m_pDataFileRecorder = DataFileRecorder::GetInstance();

    if(!m_pDataFileRecorder->Initialize(m_fileInfo, m_vecModuleInfo))
    {
        return false;
    }

    for(auto module:m_vecModuleInfo)
    {
        m_pTaskManager->add_task(module,m_pDataFileRecorder);
    }

	
    return ICV_SUCCESS;
}

void DRDataFileService::Refresh()
{
	return;
}

long DRDataFileService::Start()
{
    //数据接收
	m_pDataReceiver->Start();

    //文件开始可以接收数据
    if(!m_pDataFileRecorder->StartRecording())
    {
        CV_ERROR(g_dfsLog,-1,"StartRecording failed");
        return -1;// TODO: 改成错误码
    }

    //启动任务
    m_pTaskManager->start_all();

    //启动定时器
    m_pIoService->run();
    return ICV_SUCCESS;
}

void DRDataFileService::SetLogFileName()
{
	g_dfsLog.SetLogFileNameThread("DSFDataFileService");
}

void DRDataFileService::PrintStartUpScreen()
{
	PrintHelpScreen();
}

void DRDataFileService::PrintHelpScreen()
{
	printf("+====================================================================+\n");
	printf("|                     <<Welcome to DSFDataFileService>>					   |\n");
	printf("|  You can entering the following commands to configure the service  |\n");
	printf("|  q/Q:Quit														   |\n");
	printf("|  Others:Print tips												   |\n");
	printf("+====================================================================+\n");
}

long DRDataFileService::Fini()
{
	m_pDataReceiver->Stop();
	
	if (nullptr != m_pTaskManager)
    {
		m_pTaskManager->stop_all();
    }

    if (nullptr != m_pIoService)
    {
		m_pIoService->stop();
    }

    if (nullptr != m_pGetRmStatusThread && m_pGetRmStatusThread->joinable())
    {
        m_pGetRmStatusThread->join();
        m_pGetRmStatusThread.reset();
    }

    m_pDataFileRecorder->StopRecording();

	g_dfsLog.StopLogThread();


	return ICV_SUCCESS;
}

bool DRDataFileService::ProcessCmd( char c )
{
	return false;
}

void DRDataFileService::Misc()
{

}

CServiceBase* g_pServiceHandler = new DRDataFileService();
