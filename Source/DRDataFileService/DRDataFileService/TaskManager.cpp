#include "TaskManager.h"
#include <boost/asio.hpp>
#include "Task.h"
#include "common/LogHelper.h" //for log
#include "common/cvcomm.hxx"  //for cvcomm
extern CCVLog g_dfsLog;

TaskManager::TaskManager(boost::asio::io_service& io,std::shared_ptr<DataReceiver> dfManager) 
: 
m_ioService(io) ,
m_dfManager(dfManager)
{

}

void TaskManager::add_task(ModuleInfo moduleInfo, DataFileRecorder* dfRecorder) {
    auto task = std::make_shared<Task>(m_ioService, moduleInfo, dfRecorder,m_dfManager);
    m_tasks[moduleInfo.iModuleNo] = task;
    CV_INFO(g_dfsLog,"add_task: %s, interval: %d ms", moduleInfo.strModuleName.c_str(), moduleInfo.iTimeBase);
}

void TaskManager::start_all() {
    for (auto& [_, task] : m_tasks) task->start();
}

void TaskManager::stop_all() {
    for (auto& [_, task] : m_tasks) task->stop();
}
