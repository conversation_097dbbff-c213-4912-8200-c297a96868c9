#####################################################
## STLPORT Need -pthread/-pthreads flag.           ##
#####################################################

# if(EXISTS $ENV{DRDIR}/CMakeLocalCfg.cmake)
# 	include($ENV{DRDIR}/CMakeLocalCfg.cmake)
# endif(EXISTS $ENV{DRDIR}/CMakeLocalCfg.cmake)

INCLUDE($ENV{DRDIR}CMakeIncludeDir)

# IF(POLICY  CMP0015)
# 	message("--------------------------------------------->>>>>>>>>>>>>>>>CMP0015")
# 	cmake_policy(SET CMP0015 OLD)
# ENDIF(POLICY  CMP0015)

# IF(POLICY  CMP0011)
# 	message("--------------------------------------------->>>>>>>>>>>>>>>>CMP0011")
# 	cmake_policy(SET CMP0011 NEW)
# ENDIF(POLICY  CMP0011)

ADD_DEFINITIONS(-DLIC_VERIFY_NO_CV_LICENSE)
ADD_DEFINITIONS(-D_CRT_SECURE_NO_WARNINGS)

IF(MAKE_UNIT_TEST)
# Disable Warning
ENDIF(MAKE_UNIT_TEST)

IF(UNIX)
	IF(CMAKE_SYSTEM MATCHES "SunOS.*")
		ADD_DEFINITIONS(-DACE_NDEBUG)
		SET(CMAKE_CXX_COMPILER "CC")
		SET(CMAKE_C_COMPILER "cc")
		SET(SOLARIS 1)
		# On sun os add flag -pthreads
		SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -m64 -mt -D_INCLUDE_LONGLONG -D_REENTRANT -lpthread -xmemalign=8i -D_XOPEN_SOURCE -D_XOPEN_SOURCE_EXTENDED=1 -D__EXTENSIONS__ -DACE_LACKS_WCSSTR")
		SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -m64 -mt -D_INCLUDE_LONGLONG -D_REENTRANT -lpthread -xs -xmemalign=8i")
		IF (SUNCSTD)
		  SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -library=Cstd")
		ELSE(SUNCSTD)
		  SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -library=stlport4")
		ENDIF (SUNCSTD)
	ELSE(CMAKE_SYSTEM MATCHES "SunOS.*")
		# On linux add flag -pthread
		SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -ldl -lpthread")
	ENDIF(CMAKE_SYSTEM MATCHES "SunOS.*")
ENDIF(UNIX)

IF(${CMAKE_SYSTEM_NAME} MATCHES HP-UX)
		SET(CMAKE_SHARED_LIBRARY_SUFFIX ".so")
		SET(CMAKE_CXX_COMPILER "aCC")
		SET(CMAKE_C_COMPILER "cc")
		SET(HPUX 1)
        	SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} +DD64 -mt -Aa -D_INCLUDE_LONGLONG -D_REENTRANT -lpthread")
        	SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} +DD64 -mt -Aa -D_INCLUDE_LONGLONG -D_REENTRANT -lpthread")
		SET(CMAKE_ROOT /usr/local/share/cmake-2.8)
ENDIF(${CMAKE_SYSTEM_NAME} MATCHES HP-UX)

IF(${CMAKE_SYSTEM_NAME} MATCHES AIX)
                SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -qrtti=all -q64 -g -D_THREAD_SAFE")
                SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -qrtti=all -q64 -g -D_THREAD_SAFE")
ENDIF(${CMAKE_SYSTEM_NAME} MATCHES AIX)

#Setting Executable and Library Output Dir
SET(EXE_DIR executable)
SET(LIB_DIR library)
SET(INC_DIR include)

# First for the generic no-config case (e.g. with mingw)
SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY $ENV{DRDIR}../${EXE_DIR})
SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY $ENV{DRDIR}../${EXE_DIR})
SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY $ENV{DRDIR}../${LIB_DIR})

# Second, for multi-config builds (e.g. msvc)
FOREACH(OUTPUTCONFIG ${CMAKE_CONFIGURATION_TYPES})
  STRING(TOUPPER ${OUTPUTCONFIG} OUTPUTCONFIG)
  SET(CMAKE_RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG} $ENV{DRDIR}../${EXE_DIR})
  SET(CMAKE_LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG} $ENV{DRDIR}../${EXE_DIR})
  SET(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG} $ENV{DRDIR}../${LIB_DIR})
ENDFOREACH(OUTPUTCONFIG CMAKE_CONFIGURATION_TYPES)


#####################################################
## Set Executable Path For Unit Tests and samples. ##
#####################################################

# SET(UNITTEST_DIR unittest)
# SET(SAMPLE_DIR samples)

#####################################################
## USE THIS COMMAND TO TURN DEBUG INFO(-g) ON:     ##
##      cmake -DCOMPILE_WITH_DEBUG_INFO:BOOL=ON .  ##
#####################################################

OPTION(COMPILE_WITH_DEBUG_INFO "Compile with Debuging info" ON)
IF(COMPILE_WITH_DEBUG_INFO)
	#MESSAGE("Compile with Debuging info")
	SET(CMAKE_BUILD_TYPE "Debug")
ENDIF(COMPILE_WITH_DEBUG_INFO)

#MESSAGE("CurrentProject Source Dir:" ${PROJECT_SOURCE_DIR})
INCLUDE_DIRECTORIES(${PROJECT_SOURCE_DIR}/include)
#INCLUDE(${PROJECT_SOURCE_DIR}/pkgs/version.txt OPTIONAL RESULT_VARIABLE VERSION_RESULT)

LINK_DIRECTORIES($ENV{DRDIR}../${LIB_DIR})
LINK_DIRECTORIES($ENV{DRDIR}../${EXE_DIR})

IF(MSVC)
    set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /wd4996")	
	SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} /wd4098 /wd4099 /wd4273 /wd4535")
	SET(CMAKE_CXX_FLAGS_RELWITHDEBINFO  "${CMAKE_CXX_FLAGS_RELWITHDEBINFO} /Od")
	SET(CMAKE_C_FLAGS_RELWITHDEBINFO  "${CMAKE_C_FLAGS_RELWITHDEBINFO} /Od")
	if( CMAKE_SIZEOF_VOID_P EQUAL 8 )
		set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /wd4267")
		set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} /wd4244")
	endif( CMAKE_SIZEOF_VOID_P EQUAL 8 )	
ENDIF(MSVC)

IF(WIN32)
	set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} /STACK:10000000")
	set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} /STACK:10000000")
ENDIF(WIN32)

IF(UNIX)	
	IF(CMAKE_SYSTEM MATCHES "Linux")
		set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,-z,stack-size=10000000")
		set(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -Wl,-z,stack-size=10000000")
	ENDIF(CMAKE_SYSTEM MATCHES "Linux")
ENDIF(UNIX)

SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-deprecated-declarations -Wno-return-type -Wno-format-truncation -Wno-write-strings -Wno-format -Wno-conversion-null -Wno-overflow -Wno-int-to-pointer-cast -Wno-delete-incomplete")