#Setting Executable and Library Output Path
#if lib type is shared library
IF(LIB_TYPE MATCHES "SHARED")
	INCLUDE($ENV{DRDIR}AddVersionResource)
ENDIF(LIB_TYPE MATCHES "SHARED")

ADD_LIBRARY(${TARGET_NAME} ${LIB_TYPE} ${SRCS})

# AddLinuxVersionResouce
IF(LIB_TYPE MATCHES "SHARED")
	IF(UNIX)
		SET_TARGET_PROPERTIES(${TARGET_NAME} PROPERTIES SOVERSION ${TARGET_API_VERSION} VERSION ${TARGET_VERSION} )
	ENDIF(UNIX)
ENDIF(<PERSON>IB_TYPE MATCHES "SHARED")

#if lib type is shared library
IF(LIB_TYPE MATCHES "SHARED")
	##############################################################################################################
	# Add this to Resolve "relocations remain against allocatable but non-writable sections and relocation error"#
	##############################################################################################################	
	IF(UNIX)
		# message("------------------------------------")
        #IF(${CMAKE_SYSTEM_NAME} MATCHES AIX) ##cycy HPUX NOT NEED

        #ELSE(${CMAKE_SYSTEM_NAME} MATCHES AIX)
		#	SET(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS} -mimpure-text")
        #ENDIF(${CMAKE_SYSTEM_NAME} MATCHES AIX)

		# IF(TARGET_NAME MATCHES "cv6comm")
		# ELSEIF(TARGET_NAME MATCHES "cv6logimpl")
		# ELSE(TARGET_NAME MATCHES "cv6comm")
		# 	SET(LINK_LIBS ${LINK_LIBS} cv6logimpl) #TO BE DELETED WHEN MODIFIED CVLOG!!!!!
		# ENDIF(TARGET_NAME MATCHES "cv6comm")
		
		IF(CMAKE_SYSTEM MATCHES "SunOS.*")
			SET(CMAKE_SHARED_LINKER_FLAGS "${CMAKE_SHARED_LINKER_FLAGS}")
		ENDIF(CMAKE_SYSTEM MATCHES "SunOS.*")

		IF(${CMAKE_SYSTEM_NAME} MATCHES HP-UX)
			SET(LINK_LIBS ${LINK_LIBS} Csup std_v2)
		ENDIF(${CMAKE_SYSTEM_NAME} MATCHES HP-UX)
	ENDIF(UNIX)	
	
	TARGET_LINK_LIBRARIES(${TARGET_NAME} ${LINK_LIBS})
ENDIF(LIB_TYPE MATCHES "SHARED")

IF(TARGET_VERSION)
	SET_TARGET_PROPERTIES(${TARGET_NAME} PROPERTIES VERSION ${TARGET_VERSION})
ENDIF(TARGET_VERSION)

IF(LIB_TYPE MATCHES "STATIC")
        IF(${CMAKE_SYSTEM_NAME} MATCHES Linux)
#                MESSAGE(${TARGET_NAME} ":" ${CMAKE_SYSTEM_NAME})
                SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fPIC")
                SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -fPIC")
#               SET_SOURCE_FILES_PROPERTIES(${_SOURCE} PROPERTIES COMPILE_FLAGS "-fPIC")
#               SET_TARGET_PROPERTIES(${TARGET_NAME} PROPERTIES COMPILE_FLAGS "-fPIC")
        ENDIF(${CMAKE_SYSTEM_NAME} MATCHES Linux)

		IF(CMAKE_SYSTEM MATCHES "SunOS.*")
			SET(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -xcode=pic32")
			SET(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -xcode=pic32")
		ENDIF(CMAKE_SYSTEM MATCHES "SunOS.*")
ENDIF(LIB_TYPE MATCHES "STATIC")

IF(UNIX)
ELSE(UNIX)
	#add /MP (MultiProcessor) Option to increase building speed!
	SET_TARGET_PROPERTIES( ${TARGET_NAME} PROPERTIES COMPILE_FLAGS "/MP" ) 
ENDIF(UNIX)

IF(MSVC)
   SET_TARGET_PROPERTIES(${TARGET_NAME} PROPERTIES LINK_FLAGS "/ignore:4099")
ENDIF(MSVC)

#SET_TARGET_PROPERTIES(${TARGET_NAME} PROPERTIES LIBRARY_OUTPUT_DIRECTORY $ENV{DRDIR}../${EXE_DIR})
#SET_TARGET_PROPERTIES(${TARGET_NAME} PROPERTIES RUNTIME_OUTPUT_DIRECTORY $ENV{DRDIR}../${EXE_DIR})
#SET_TARGET_PROPERTIES(${TARGET_NAME} PROPERTIES ARCHIVE_OUTPUT_DIRECTORY $ENV{DRDIR}../${LIB_DIR})

#LIBRARY DESTINATION, ��̬���ӿ�����·����ARCHIVE DESTINATION����̬��·����RUNTIME DESTINATION��Exe·��
#INSTALL(TARGETS ${TARGET_NAME} 
#		RUNTIME DESTINATION ${CMAKE_CURRENT_BINARY_DIR}/${EXE_DIR}
#		LIBRARY DESTINATION $ENV{DRDIR}../${LIB_DIR}
#		ARCHIVE DESTINATION ${CMAKE_CURRENT_BINARY_DIR}/${LIB_DIR})