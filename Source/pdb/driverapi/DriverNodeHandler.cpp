#include "proto/proto_driverapi_pdb.h"
#include "DriverNodeHander.h"
#include "common/cvGlobalHelper.h"
#include <vector>
#include "DriverApiNodeMgr.h"
#include "common/CVLog.h"
#include "common/LogHelper.h"

#define DEFAULT_CONNECT_TIMEOUT_MS 2000
extern CCVLog g_cvLogDriverAPI;
CDriverNodeHandler::CDriverNodeHandler()
{
	m_hSvcDataQue = NULL;
	m_hSvcCtrlQue = NULL;
	m_nTimeoutMS = DEFAULT_CONNECT_TIMEOUT_MS;
	m_bLocal = false;
	m_nRMStatus = RM_STATUS_INACTIVE;
	m_lSvcPort = 0;
	m_bMultiLink = true;
	m_bConnected = false;
	m_bRegistered = false;
}
CDriverNodeHandler::~CDriverNodeHandler()
{
	m_hSvcDataQue = NULL;
	m_hSvcCtrlQue = NULL;
	m_hLocalDataQue = NULL;
	m_nTimeoutMS = DEFAULT_CONNECT_TIMEOUT_MS;
	m_bLocal = false;
	m_nRMStatus = RM_STATUS_INACTIVE;
	m_lSvcPort = 0;
	m_bMultiLink = true;
	m_bConnected = false;
	m_bRegistered = false;
}


int32 CDriverNodeHandler::Init(std::string strDrvName, std::string strSvcIP, unsigned short usPort,long nLocalDataQueueID)
{
	long lRet = ICV_SUCCESS;
	m_lSvcPort = usPort;
	if (usPort == 0)
	{
		return -1;
	}

	m_strSvcIP = strSvcIP;

	m_hSvcDataQue = CVNDK_RegRemoteQueue(DRVAPI_SERVER_DATA_QUEUE_ID, m_strSvcIP.c_str(), m_lSvcPort);
	if (m_hSvcDataQue == NULL)
	{
		return -1;
	}

	m_hSvcCtrlQue = CVNDK_RegRemoteQueue(DRVAPI_SERVER_CTRL_QUEUE_ID, m_strSvcIP.c_str(), m_lSvcPort);
	if (m_hSvcDataQue == NULL)
	{
		return -1;
	}

	m_hLocalDataQue = CVNDK_RegLocalQueue(nLocalDataQueueID);
	if (m_hLocalDataQue == NULL)
	{
		CV_ERROR(g_cvLogDriverAPI, -1, "CVNDK_RegLocalQueue %d", nLocalDataQueueID);
		return -1;
	}
	CV_INFO(g_cvLogDriverAPI, "CVNDK_RegLocalQueue %d", nLocalDataQueueID);

	SetDrvName(strDrvName);
	//���ӷ���
	lRet = CVNDK_Connect(m_hSvcDataQue);
	if(lRet == ICV_SUCCESS)
	{
		m_bConnected = true;
	}
	else
	{
		m_bConnected = false;
	}
	
	
	return ICV_SUCCESS;
}

int32 CDriverNodeHandler::Release()
{
	long lRet = ICV_SUCCESS;

	if(m_bConnected == true)
	{
		//yangqi  DisConnect�����ϵ���ReleaseQueue�������⣬DisConnect�ᴥ��ioservice���첽���ã�ReleaseQueue��������Դ
		//CVNDK_DisConnect(m_hSvcDataQue);
		m_bConnected = false;
	}

	if (m_hSvcDataQue)
	{
		lRet = CVNDK_ReleaseQueue(m_hSvcDataQue);
		m_hSvcDataQue = NULL;
	}
	else
	{
		CV_ERROR(g_cvLogDriverAPI, EC_ICV_CVNDK_INVALID_PARAMETER, "CVNDK: release m_hSvcDataQue is NULL!");
	}
	if (m_hSvcCtrlQue)
	{
		lRet = CVNDK_ReleaseQueue(m_hSvcCtrlQue);
		m_hSvcCtrlQue = NULL;
	}
	else
	{
		CV_ERROR(g_cvLogDriverAPI, EC_ICV_CVNDK_INVALID_PARAMETER, "CVNDK: release m_hSvcCtrlQue is NULL!");
	}

	if(m_hLocalDataQue != NULL)
	{
		CVNDK_ReleaseQueue(m_hLocalDataQue);
		m_hLocalDataQue = NULL;
	}

	m_bConnected = false;

	return ICV_SUCCESS;
}

int32 CDriverNodeHandler::SendRegDrvData(char *pszReqMsg, long lReqMsgLen)
{
	CDriverApiNodeMgr * drivernodemgr = CDriverApiNodeMgr::instance(); 
	HQUEUE localqueue = drivernodemgr->m_hLocalCtrlQue;

	long lRet = CVNDK_Send(m_hSvcCtrlQue, localqueue, pszReqMsg, lReqMsgLen, ASYNC_SEND);
	if (lRet != ICV_SUCCESS)
	{
		 CV_ERROR(g_cvLogDriverAPI, lRet, "CVDrv SendRegDrvData: CVNDK_Send!");
		return lRet;
	}

	return ICV_SUCCESS;
}
// int32 CDriverNodeHandler::RecvRegDrvDataAck(void * &pszResponse, long *plResponseLen)
// {
// 	CDriverApiNodeMgr * drivernodemgr = CDriverApiNodeMgr::instance(); 
// 	HQUEUE hSvcQue = 0;
// 	HQUEUE localqueue = drivernodemgr->m_hLocalRegQue;
// 
// 	long lRcvLen = 0;
// 	long lRet = CVNDK_Recv(localqueue, &hSvcQue, &pszResponse, lRcvLen, m_nTimeoutMS);
// 	if (lRet != ICV_SUCCESS)
// 	{
// 		return lRet;
// 	}
// 
// 	*plResponseLen = lRcvLen;
// 	CVNDK_ReleaseQueue(hSvcQue);
// 	return ICV_SUCCESS;
// }
int32 CDriverNodeHandler::SendData(char *pszReqMsg, long lReqMsgLen)
{
    long lRet = CVNDK_GetConnectStat(m_hSvcDataQue);
    if (lRet != CVNDK_CONNECTED) {
        CV_ERROR(g_cvLogDriverAPI, lRet, "Inactive connection. Re-register!");
        lRet = DoRegister();
        if (lRet != ICV_SUCCESS) {
            CV_ERROR(g_cvLogDriverAPI, lRet, "Re-registration failed.");
            return lRet;
        }
    }
    lRet = CVNDK_Send(m_hSvcDataQue, m_hLocalDataQue, pszReqMsg, lReqMsgLen,ASYNC_SEND);
	if (lRet != ICV_SUCCESS)
	{
		CV_DEBUG(g_cvLogDriverAPI, "CVNDK_Send failed ip %s", m_strSvcIP.c_str());
		return lRet;
	}
	
	return ICV_SUCCESS;
}

int32 CDriverNodeHandler::RecvDataAck(void * &pszResponse, long *plResponseLen)
{
	HQUEUE hSvcQue = 0;

	long lRcvLen = 0;
	long lRet = CVNDK_Recv(m_hLocalDataQue, &hSvcQue, &pszResponse, lRcvLen, m_nTimeoutMS);
	if (lRet != ICV_SUCCESS)
	{
		return lRet;
	}

	*plResponseLen = lRcvLen;
	CVNDK_ReleaseQueue(hSvcQue);

	return lRet;
}
 void CDriverNodeHandler::DoSetBlockQuality(std::vector<int32> &vecTagID, TCV_TimeStamp *cvtime, int16 nQuality)
 {
	 int32 nRet = ICV_SUCCESS;
	 char* pBuf = NULL;
	 int32 nLen = 0;
	 if (!m_bMultiLink && m_nRMStatus == RM_STATUS_INACTIVE && GetLocal())
	 {
		 //�����ӣ��ǻ�����ϴ�����
		 return;
	 }

	 if (!m_bConnected)
	 {
		 return;
	 }
	 proto_driverapi_setblockquality_pack(vecTagID, cvtime, nQuality, &pBuf, &nLen);
	 nRet = SendData(pBuf, nLen);
	 proto_driverapi_free(pBuf);
	 return;
 }

 int32 CDriverNodeHandler::DoRegister()
 {
	 int32 lRet = ICV_SUCCESS;
	 char* pBufBody;
	 int32 nBodyLen;
	
	 //��֯ע�� body
	 //ע�����������ƣ�ֻ���򱾻�ע��
// 	 const char* szLocalIP = CVComm.GetLocalScadaIP();
// 	 if (0 == strcmp(szLocalIP, m_strSvcIP.c_str()))
	 if(GetLocal())
	 {
		 proto_driverapi_register_pack(m_strDrvName.c_str(), &pBufBody, &nBodyLen);
		 lRet = SendRegDrvData(pBufBody, nBodyLen);
		 proto_driverapi_free(pBufBody);
	 }
	 m_bRegistered = (ICV_SUCCESS == lRet);
	 return lRet;
 }
 void CDriverNodeHandler::SetLocal(bool bLocal)
 {
	 m_bLocal = bLocal;
 }
 bool  CDriverNodeHandler::GetLocal()
 {
	 return m_bLocal;
 }
 int32 CDriverNodeHandler::GetRmStatus()
 {
	 int32 lRet = ICV_SUCCESS;
	 int32 nBufLen;
	 char* pBuf;
	 void * pszResponse = NULL;
	 long lResponseLen = 0;
	 //��ȡ����״̬
	 if(m_bConnected)
	 {
		 TProtoDriverAPIHeader msgHeader(DRIVERAPI_CMD_QUERY_RY_STATUS);
		 proto_driverapi_header_pack(msgHeader, &pBuf, &nBufLen);
		 lRet = SendData(pBuf, nBufLen);
		 proto_driverapi_free(pBuf);
		 if (lRet == ICV_SUCCESS)
		 {
			 lRet = RecvDataAck(pszResponse, &lResponseLen);
			 if (lRet == ICV_SUCCESS)
			 {
				 int32 nRMStatus;
				 proto_driverapi_query_ry_status_ret_unpack((char*)pszResponse, lResponseLen,&nRMStatus);
				 //��������һ��4���ֽڵ�����״̬
				 m_nRMStatus = nRMStatus;
				 CVNDK_Free(pszResponse);
			 }
		 }
	 }
	 return lRet;
 }
 void CDriverNodeHandler::DoHeartBeat()
 {
	//ͨ���������ж�����״̬
	 //�����������󣬲���ȡ��Ӧ
	 int32 nRet = ICV_SUCCESS;
	 int32 nBufLen;
	 char* pBuf;
	 void * pszResponse = NULL;
	 long lResponseLen = 0;

	 //��ȡ����״̬
     if(m_bConnected == false)
	 {
		 //���ӷ���
		 CV_INFO(g_cvLogDriverAPI, "CVDrv DoHeartBeat: connect!");
		 nRet = CVNDK_Connect(m_hSvcDataQue);
		 if(nRet == ICV_SUCCESS)
		 {
			 CV_INFO(g_cvLogDriverAPI,"CVDrv DoHeartBeat: connect success!");
			 m_bConnected = true;
			 //ע������
			 nRet = DoRegister();
			 if (nRet != ICV_SUCCESS)
			 {
				 CV_ERROR(g_cvLogDriverAPI,nRet ,"CVDrv DoHeartBeat: DoRegister failed!");
				 return;
			 }
		 }
		 else
		 {
			 m_bConnected = false;
		 }
	 }
	 else
	 {
		 //����������û��ע�ᣬ����ע��
		 if (!m_bRegistered)
		 {
			 nRet = DoRegister();
			 if (nRet != ICV_SUCCESS)
			 {
				 CV_ERROR(g_cvLogDriverAPI,nRet, "CVDrv DoHeartBeat: DoRegister failed!");
				 return;
			 }
		 }
		 TProtoDriverAPIHeader msgHeader(DRIVERAPI_CMD_QUERY_RY_STATUS);
		 proto_driverapi_header_pack(msgHeader, &pBuf, &nBufLen);
		 nRet = SendData(pBuf, nBufLen);
		 proto_driverapi_free(pBuf);
		 if (nRet == ICV_SUCCESS)
		 {
			nRet = RecvDataAck(pszResponse, &lResponseLen);
			if (nRet == ICV_SUCCESS)
			{
				int32 nRMStatus;
				proto_driverapi_query_ry_status_ret_unpack((char*)pszResponse, lResponseLen,&nRMStatus);
				//��������һ��4���ֽڵ�����״̬
				m_nRMStatus = nRMStatus;
				CVNDK_Free(pszResponse);
			}
		 }
		 else
		 {
			  nRet = CVNDK_DisConnect(m_hSvcDataQue);
			  m_bConnected = false;
			  CV_WARN(g_cvLogDriverAPI, nRet, "CVDrv DoHeartBeat: Reconnect!");
			  //���ӷ���
			  nRet = CVNDK_Connect(m_hSvcDataQue);
			  if(nRet == ICV_SUCCESS)
			  {
				  CV_WARN(g_cvLogDriverAPI,nRet, "CVDrv DoHeartBeat: Reconnect success!");
				  m_bConnected = true;
				  //ע������
				  nRet = DoRegister();
				  if (nRet != ICV_SUCCESS)
				  {
					  CV_ERROR(g_cvLogDriverAPI, EC_ICV_DRIVER_API_REG_DRIVER_FAILED,"CVDrv DoHeartBeat: Reconnect DoRegister failed :%d!", nRet);
					  return;
				  }
			  }
			  else
			  {
				  m_bConnected = false;
			  }
		 }
	 }
	 
 }

 int32 CDriverNodeHandler::DoSaveData(std::vector<TProtoIDVTQ> *pVecData)
 {
	 int32 nRet = ICV_SUCCESS;

	//  if (!m_bMultiLink && m_nRMStatus == RM_STATUS_INACTIVE && GetLocal())
	//  {
	// 	//���أ������ӣ��ǻ�����ϴ�����
	// 	return nRet;
	//  }
	 if (!m_bConnected)
	 {
		 return EC_CONNECTIONISNULL;
	 }

	 char* pBufBody;
	 int32 nBodyLen;

	 //��֯SaveData body

	 proto_driverapi_save_data_pack(*pVecData, &pBufBody, &nBodyLen);
	 nRet = SendData(pBufBody, nBodyLen);
	 proto_driverapi_free(pBufBody);
	 if(nRet != ICV_SUCCESS)
	 {	 
		 CV_ERROR(g_cvLogDriverAPI, EC_ICV_DRIVER_API_SEND_DATA_FAILED, "CVDrv SendData failed :%d!", nRet);
		 return EC_ICV_DRIVER_API_SEND_DATA_FAILED;
	 }	 

	 return nRet;
 }

 int32 CDriverNodeHandler::DoSaveDrvStat(const char* szDrvName, const TCV_TimeStamp* pCVTime, long nDrvStatus)
 {
	 int32 nRet = ICV_SUCCESS;
	 if (!m_bMultiLink && m_nRMStatus == RM_STATUS_INACTIVE && GetLocal())
	 {
		 //�����ӣ��ǻ�����ϴ�����
		 return nRet;
	 }
	 if (!m_bConnected)
	 {
		 return EC_CONNECTIONISNULL;
	 }
	 char* pBufBody;
	 int32 nBodyLen;

	 //��֯SaveData body

	 proto_driverapi_save_DrvStatus_pack(szDrvName, pCVTime, nDrvStatus, &pBufBody, &nBodyLen);
	 nRet = SendData(pBufBody, nBodyLen);
	 proto_driverapi_free(pBufBody);
	 if(nRet != ICV_SUCCESS)
	 {	 
		 CV_ERROR(g_cvLogDriverAPI, EC_ICV_DRIVER_API_SEND_DATA_FAILED, "CVDrv SendData failed :%d!", nRet);
		 return EC_ICV_DRIVER_API_SEND_DATA_FAILED;
	 }	 

	 return nRet;
 }

 int32 CDriverNodeHandler::DoSaveDevStat(const char* szDrvName, const char*szDeviceName, const TCV_TimeStamp* pCVTime, long nDeviceStatus)
 {
	 int32 nRet = ICV_SUCCESS;
	//  if (!m_bMultiLink && m_nRMStatus == RM_STATUS_INACTIVE && GetLocal())
	//  {
	// 	 //�����ӣ��ǻ�����ϴ�����
	// 	 return nRet;
	//  }
	 if (!m_bConnected)
	 {
		 return EC_CONNECTIONISNULL;
	 }
	 char* pBufBody;
	 int32 nBodyLen;

	 //��֯SaveData body

	 proto_driverapi_save_DevStatus_pack(szDrvName, szDeviceName, pCVTime, nDeviceStatus, &pBufBody, &nBodyLen);
	 nRet = SendData(pBufBody, nBodyLen);
	 proto_driverapi_free(pBufBody);
	 if(nRet != ICV_SUCCESS)
	 {	 
		 CV_ERROR(g_cvLogDriverAPI, EC_ICV_DRIVER_API_SEND_DATA_FAILED, "CVDrv SendData failed :%d!", nRet);
		 return EC_ICV_DRIVER_API_SEND_DATA_FAILED;
	 }	 

	 return nRet;
 }
