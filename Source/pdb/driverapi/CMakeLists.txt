cmake_minimum_required(VERSION 3.10)
############FOR_MODIFIY_BEGIN##########
#Setting Project Name
PROJECT (drdriverapi)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
Set(SRCS ${SRCS} DriverApi.cpp DriverApiNodeMgr.cpp DriverNodeHandler.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME drdriverapi)
#Setting library type used when build a library
#STATIC/SHARED
SET(LIB_TYPE SHARED)

SET(LINK_LIBS tinyxml drnetqueue drcomm drlog drlogimpl)

################FOR_MODIFIY_END###########

INCLUDE($ENV{DRDIR}CMakeCommonLib)
