#include "processdb/DriverApi.h"
#include "DriverApiNodeMgr.h"
#include "common/CVLog.h"
#include "errcode/ErrCode_iCV_Common.hxx"
#include <iostream>
#include <vector>

using namespace std;

// 控制回调函数
void ControlCallback(TProtoDriverAPICTRLMsg *pData)
{
    // 处理控制消息
    std::cout << "Received control message: TagID=" << pData->m_nTagID
              << ", DataType=" << (int)pData->m_nDataType
              << ", Length=" << pData->m_nLenBuf
              << std::endl;
}
// 打印 TProtoIDVTQ 结构体内容
void PrintTProtoIDVTQ(const TProtoIDVTQ &data)
{
    cout << "TProtoIDVTQ {" << endl;
    cout << "  m_nTagID: " << data.m_nTagID << endl;
    cout << "  m_nSec: " << data.m_nSec << endl;
    cout << "  m_nMs: " << data.m_nMs << endl;
    cout << "  m_nQuality: " << (int)data.m_nQuality << endl;
    cout << "  m_nLenBuf: " << data.m_nLenBuf << endl;
    cout << "  m_nDataType: " << (int)data.m_nDataType << endl;

    if (data.m_pBuf != nullptr)
    {
        if (data.m_nDataType == 7) // int32 类型
        {
            int dValue = *reinterpret_cast<const int32_t *>(data.m_pBuf);
            cout << "  m_pBuf (dint): " << dValue << endl;
        }
        else if (data.m_nDataType == 0) // string 类型
        {
            cout << "  m_pBuf (string): " << data.m_pBuf << endl;
        }
        else if (data.m_nDataType == 2) //  INT16类型
        {
            int dValue = *reinterpret_cast<const int16_t *>(data.m_pBuf);
            cout << "  m_pBuf (int(16)): " << dValue << endl;
        }
        else if (data.m_nDataType == 10) // BOOL 类型
        {
            // 将char指针转换回bool指针，然后解引用它来获取bool值
            bool value = *reinterpret_cast<bool *>(data.m_pBuf);
            // 打印bool值
            cout << "  m_pBuf (bool): " << boolalpha << value << endl; // 使用std::boolalpha来打印true/false
        }
        else if (data.m_nDataType == 19) //char 类型
        {
            cout << "  m_pBuf (char): " << data.m_pBuf << endl;
        }
    }
    else
    {
        cout << "  m_pBuf: (nullptr)" << endl;
    }

    cout << "}" << endl;
}
int main()
{
    // 驱动名称
    std::string drvName = "SimulatedDriver";
    cout << drvName << endl;
    // 初始化驱动 API
    int32 initResult = CVDrv_Init(drvName, true); // 使用单连接模式
    if (initResult != ICV_SUCCESS)
    {
        std::cerr << "Failed to initialize driver API. Error code: " << initResult << std::endl;
        return -1;
    }
    cout << "initok" << endl;

    vector<TProtoIDVTQ> vecData;
    // 生成并发送一次模拟数据
    TProtoIDVTQ data;
    data.m_nTagID = 1000002; // 示例 tag ID
    data.m_nSec = 10000000;  // 示例时间戳的秒部分
    data.m_nMs = 10;         // 示例时间戳的毫秒部分
    data.m_nQuality = 192;   // 默认数据质量
    data.m_nDataType = 7;    // 示例数据类型

    // 填充数据内容
    int32_t intValue = 7; // 示例数据值
    data.m_nLenBuf = sizeof(intValue);
    data.m_pBuf = new char[data.m_nLenBuf];         // 动态分配内存
    memcpy(data.m_pBuf, &intValue, data.m_nLenBuf); // 复制数据
    vecData.push_back(data);
    

    TProtoIDVTQ data2;
    data2.m_nTagID = 1000000; // 示例 tag ID
    data2.m_nSec = 10000000;  // 示例时间戳的秒部分
    data2.m_nMs = 10;         // 示例时间戳的毫秒部分
    data2.m_nQuality = 192;   // 默认数据质量
    data2.m_nDataType = 2;    // 示例数据类型

    // 填充数据内容
    int16_t int16Value = 2; // 示例数据值
    data2.m_nLenBuf = sizeof(int16Value);
    data2.m_pBuf = new char[data2.m_nLenBuf];           // 动态分配内存
    memcpy(data2.m_pBuf, &int16Value, data2.m_nLenBuf); // 复制数据
    vecData.push_back(data2);


    TProtoIDVTQ data3;
    data3.m_nTagID = 1000001; // 示例 tag ID
    data3.m_nSec = 10000000;  // 示例时间戳的秒部分
    data3.m_nMs = 10;         // 示例时间戳的毫秒部分
    data3.m_nQuality = 192;   // 默认数据质量
    data3.m_nDataType = 10;   // BOOL

    bool boolValue = true; // 示例数据值
    data3.m_nLenBuf = sizeof(boolValue);
    data3.m_pBuf = new char[data3.m_nLenBuf];          // 动态分配内存
    memcpy(data3.m_pBuf, &boolValue, data3.m_nLenBuf); // 复制数据
    vecData.push_back(data3);

    TProtoIDVTQ data4;
    data4.m_nTagID = 1000004; // 示例 tag ID
    data4.m_nSec = 10000000;  // 示例时间戳的秒部分
    data4.m_nMs = 10;         // 示例时间戳的毫秒部分
    data4.m_nQuality = 192;   // 默认数据质量
    data4.m_nDataType = 19;   // Char

    char charValue = 'a'; // 示例数据值
    data4.m_nLenBuf = sizeof(char);
    data4.m_pBuf = new char[data4.m_nLenBuf];          // 动态分配内存
    memcpy(data4.m_pBuf, &charValue, data4.m_nLenBuf); // 复制数据
    vecData.push_back(data4);

    TProtoIDVTQ data5;
    data5.m_nTagID = 1000005; // 示例 tag ID
    data5.m_nSec = 10000000;  // 示例时间戳的秒部分
    data5.m_nMs = 10;         // 示例时间戳的毫秒部分
    data5.m_nQuality = 192;   // 默认数据质量
    data5.m_nDataType = 0;   // STRING

    string stringValue = "Hello world!"; // 示例数据值
    data5.m_nLenBuf = stringValue.length();      
    data5.m_pBuf = new char[data5.m_nLenBuf]; // 动态分配内存
    strcpy(data5.m_pBuf, stringValue.c_str()); // 复制数据
    vecData.push_back(data5);


    for(TProtoIDVTQ d : vecData){
        PrintTProtoIDVTQ(d);
    }

    long lRet = CVDrv_SaveData(&vecData);
    cout << "save over!" << endl;
    if (lRet != ICV_SUCCESS)
    {
        std::cerr << "Failed to send data. Error code: " << lRet << std::endl;
    }

    // 清理动态分配的内存
    for(TProtoIDVTQ d : vecData){
        delete[] d.m_pBuf;  // 释放动态分配的内存
    }
    

    // 清理资源
    CVDrv_Release(drvName);

    return 0;
}