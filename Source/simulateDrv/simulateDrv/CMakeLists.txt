cmake_minimum_required(VERSION 3.10)
############FOR_MODIFIY_BEGIN##########
#Setting Project Name
PROJECT (simulateDrv)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
Set(SRCS ${SRCS} main.cpp DriverApi.cpp DriverApiNodeMgr.cpp DriverNodeHandler.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME simulateDrv)

SET(LINK_LIBS drdriverapi tinyxml drnetqueue drcomm drlog drlogimpl)

################FOR_MODIFIY_END###########

INCLUDE($ENV{DRDIR}CMakeCommonExec)