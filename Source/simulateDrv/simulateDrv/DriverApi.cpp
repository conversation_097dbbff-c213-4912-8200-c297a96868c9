#include "processdb/DriverApi.h"
#include "DriverApiNodeMgr.h"
#include "common/CVLog.h"
#include "errcode/ErrCode_iCV_Common.hxx"
#include <map>

using namespace std;
CCVLog g_cvLogDriverAPI;

DRV_API int32 CVDrv_Init(std::string strDrvName, bool bMultiLink)
{
	CDriverApiNodeMgr* drivernodemgr = CDriverApiNodeMgr::instance();
	char szLogName[ICV_SHORTFILENAME_MAXLEN];
	memset(szLogName, 0, ICV_SHORTFILENAME_MAXLEN);
	sprintf(szLogName, "CVDriverApi_%s", strDrvName.c_str());
	g_cvLogDriverAPI.SetLogFileNameThread(szLogName);
	if (strDrvName.empty())
	{
		CV_ERROR(g_cvLogDriverAPI,EC_ICV_CC_DBNONAME,"CVDrv_Init: invalid strDrvName!");
		return EC_ICV_CC_DBNONAME;
	}

	CV_INFO(g_cvLogDriverAPI, "Initialize start strDrvName %s, bMultiLink %d", strDrvName.c_str(), (long)bMultiLink);
	long lRet = drivernodemgr->Initialize(strDrvName, bMultiLink);
	if (lRet != ICV_SUCCESS)
	{
		CV_ERROR(g_cvLogDriverAPI,lRet,"CVDrv_Init: failed to initialize!");
		return -1;
	}
	CV_INFO(g_cvLogDriverAPI, "Initialize end strDrvName %s, bMultiLink %d", strDrvName.c_str(), (long)bMultiLink);
	return ICV_SUCCESS;
}

DRV_API int32 CVDrv_SaveData(std::vector<TProtoIDVTQ> *pVecData)
{
	long lRet = ICV_SUCCESS;
	CDriverApiNodeMgr* drivernodemgr = CDriverApiNodeMgr::instance();

	if (pVecData == NULL)
	{
		CV_ERROR(g_cvLogDriverAPI, EC_ICV_DRIVER_API_NULL_PTR, "CVDrv_SaveData: pData is NULL!");
		return EC_ICV_DRIVER_API_NULL_PTR;
	}

		lRet = drivernodemgr->SaveData(pVecData);

	if (lRet != ICV_SUCCESS)
	{
		CV_ERROR(g_cvLogDriverAPI, lRet, "CVDrv_SaveData: Save data failed! ErrCode = %d", lRet);
		return lRet;
	}
	CV_TRACE(g_cvLogDriverAPI, "CVDrv_SaveData Success! Size = %d", pVecData->size());
	return ICV_SUCCESS;
}
DRV_API int32 CVDrv_SetBlockQuality(std::vector<int32> &vecTagID, TCV_TimeStamp *cvtime, int16 nQuality)
{
	long lRet = ICV_SUCCESS;
	CDriverApiNodeMgr* drivernodemgr = CDriverApiNodeMgr::instance();


	lRet = drivernodemgr->SetBlockQuality(vecTagID, cvtime, nQuality);

	if (lRet != ICV_SUCCESS)
	{
		CV_ERROR(g_cvLogDriverAPI, lRet, "CVDrv_SetBlockQuality: SetBlockQuality failed! ErrCode = %d", lRet);
		return lRet;
	}

	return ICV_SUCCESS;
}

DRV_API int32 CVDrv_SetDrvStatus(const char* szDrvName, const TCV_TimeStamp* pCVTime, long nDrvStatus)
{
	long lRet = ICV_SUCCESS;
	CDriverApiNodeMgr* drivernodemgr = CDriverApiNodeMgr::instance();

	if (szDrvName == NULL)
	{
		CV_ERROR(g_cvLogDriverAPI, EC_ICV_DRIVER_API_NULL_PTR, "CVDrv_SetDrvStatus: szDrvName is NULL!");
		return EC_ICV_DRIVER_API_NULL_PTR;
	}

	lRet = drivernodemgr->SaveDrvStat(szDrvName, pCVTime, nDrvStatus);

	if (lRet != ICV_SUCCESS)
	{
		CV_ERROR(g_cvLogDriverAPI, lRet, "CVDrv_SetDrvStatus: Save Driver Status failed! ErrCode = %d", lRet);
		return lRet;
	}
	return 0;
}

DRV_API int32 CVDrv_SetDeviceStatus(const char* szDrvName, const char*szDeviceName, const TCV_TimeStamp* pCVTime, long nDeviceStatus)
{
	long lRet = ICV_SUCCESS;
	CDriverApiNodeMgr* drivernodemgr = CDriverApiNodeMgr::instance();

	if (szDrvName == NULL || szDeviceName == NULL)
	{
		CV_ERROR(g_cvLogDriverAPI, EC_ICV_DRIVER_API_NULL_PTR, "CVDrv_SetDeviceStatus: szDrvName or szDeviceName is NULL!");
		return EC_ICV_DRIVER_API_NULL_PTR;
	}

	lRet = drivernodemgr->SaveDevStat(szDrvName, szDeviceName, pCVTime, nDeviceStatus);
	if (lRet != ICV_SUCCESS)
	{
		CV_ERROR(g_cvLogDriverAPI, lRet, "CVDrv_SetDeviceStatus: Save Device Status failed! ErrCode = %d", lRet);
		return lRet;
	}

	return 0;
}

DRV_API int32 CVDrv_SetControlCallback (void (*pFunControlCallback)(TProtoDriverAPICTRLMsg* pData))
{
	CDriverApiNodeMgr* drivernodemgr = CDriverApiNodeMgr::instance();
	if (pFunControlCallback == NULL)
	{
		CV_ERROR(g_cvLogDriverAPI, EC_ICV_DRIVER_API_SETCALLBACK_FAILD, "CVDrv_SetControlCallback: pFunControlCallback is NULL!");
		return EC_ICV_DRIVER_API_SETCALLBACK_FAILD;
	}

	drivernodemgr->SetCtrlCallBack(pFunControlCallback);
	return ICV_SUCCESS;
}

DRV_API int32 CVDrv_Release(std::string strDrvName)
{
	CDriverApiNodeMgr* drivernodemgr = CDriverApiNodeMgr::instance();
	drivernodemgr->Uninitialize();
    g_cvLogDriverAPI.StopLogThread();

	return ICV_SUCCESS;
}