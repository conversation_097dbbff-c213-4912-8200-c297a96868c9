#include "DSFMonitorConsole.h"

// DRSDK初始化，该操作基本不会失败
DSFMonitorConsole::DSFMonitorConsole()
{
    //初始化
    init();
}

// DRSDK初始化，该操作基本不会失败
DSFMonitorConsole::DSFMonitorConsole(const std::string &ip,const uint16_t port) : m_ip(ip), m_port(port)
{
    //初始化
    init();
}

DSFMonitorConsole::~DSFMonitorConsole()
{
    dsfapi::Free_DR_Sdk_Context(&m_context);
}

//连接状态
bool DSFMonitorConsole::connectStatus()
{
    return m_context->ConnectStatus();
}

//读取信号
void DSFMonitorConsole::readSignal()
{
    std::string tagName;
    std::cout << "Enter signal name to read: ";
    std::cin >> tagName;

    const char *pTagName[1] = {tagName.c_str()};
    dsfapi::TagValue *pTagValue = nullptr;
    int32 *pErrorCode = nullptr;
    int32 nTagValueCount = 0;

    auto res = dsfapi::DR_Read(m_context, pTagName, 1, &pTagValue, &pErrorCode, &nTagValueCount);
    if (res == 0 && nTagValueCount > 0 && pErrorCode[0] == 0) {
        uint16_t value = static_cast<uint16_t>(*pTagValue[0].Buffer());
        std::cout << "Signal "+std::string(tagName)+" value: " << value << std::endl;
    } else {
        std::cerr << "Failed to read signal. Error code: " << (pErrorCode ? pErrorCode[0] : -1) << std::endl;
    }

    dsfapi::Free_Int32_Ptr(&pErrorCode);
    dsfapi::Free_Tag_Value(&pTagValue);
}

//写入信号
void DSFMonitorConsole::writeSignal()
{
    std::string tagName;
    uint16_t signalValue, signalCount=1;
    std::cout << "Enter signal name to write: ";
    std::cin >> tagName;
    std::cout << "Enter signal value to write: ";
    std::cin >> signalValue;

    dsfapi::TagValue tagValue;
    tagValue.ReSet(reinterpret_cast<const char*>(&signalValue), sizeof(signalValue));

    const char *pTagName[1] = {tagName.c_str()};
    
    auto res = dsfapi::DR_Write(m_context, static_cast<const char**>(pTagName), &tagValue, signalCount);
    if (res == 0) {
        std::cout << "Signal "+std::string(tagName)+" written successfully!" << std::endl;
    } else {
        std::cerr << "Failed to write signal."<< std::endl;
    }
}

//选项
void DSFMonitorConsole::option()
{
    short action=-1;
    while (action!=0)
    {
        if (!connectStatus())
        {
            std::cerr << "Failed to connect to dsfdataservice. Retrying..." << std::endl;
            break;
        }
        
        std::cout << std::endl;
        std::cout << "Select an action:" << std::endl;
        std::cout << "1. Read a signal" << std::endl;
        std::cout << "2. Write a signal" << std::endl;
        std::cout << "0. Return" << std::endl;
        std::cin >> action;
        switch (action)
        {
            case 1:
                readSignal();
                break;
            case 2:
                writeSignal();
                break;
            case 0:
                std::cout << "Returning..." << std::endl;
                break;
            default:
                std::cerr << "Invalid action. Try again." << std::endl;
                break;
        }
    }
}

//初始化
void DSFMonitorConsole::init()
{
    //设置连接参数
    dsfapi::DRSdkConnectParam tConnectParam;
    strncpy(tConnectParam.szServerIp, m_ip.c_str(), sizeof(tConnectParam.szServerIp) - 1);
    tConnectParam.nServerPort = m_port;
    tConnectParam.nHeartbeatTimeoutMs = 3000;
    tConnectParam.nConnectTimeoutMs = 1000;

    //设置选项
    dsfapi::DRSdkOption tOption;
    tOption.nThreadPoolNum = 10;
    tOption.nRequestWaitTimeoutMs = 1000;

    //初始化DRSDK
    m_context = dsfapi::DR_Init(tConnectParam, tOption);
}
