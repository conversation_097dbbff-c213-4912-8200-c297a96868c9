#include "DSFMonitorConsole.h"

//创建监视器
void createMonitor()
{
    short option=-1;
    while (option!=0)
    {
        std::cout << std::endl;
        std::cout << "Select an option:" << std::endl;
        std::cout << "1. Connect to dsfdataservice with default parameters" << std::endl;
        std::cout << "2. Connect to dsfdataservice with custom IP and port" << std::endl;
        std::cout << "0. Return" << std::endl;
        std::cin >> option;
        switch (option)
        {
            case 1:
            {
                DSFMonitorConsole DSFMonitorConsole;
                if (DSFMonitorConsole.connectStatus())
                {
                    std::cout << "Connected to dsfdataservice successfully!" << std::endl;
                    DSFMonitorConsole.option();
                }
                else
                {
                    std::cerr << "Failed to connect to dsfdataservice. Retrying..." << std::endl;
                }
                break;
            }
            case 2:
            {
                std::string ip;
                uint16_t port;
                std::cout << "Enter dsfdataservice IP: ";
                std::cin >> ip;
                std::cout << "Enter dsfdataservice port: ";
                std::cin >> port;
                DSFMonitorConsole DSFMonitorConsole(ip,port);
                if (DSFMonitorConsole.connectStatus())
                {
                    std::cout << "Connected to dsfdataservice successfully!" << std::endl;
                    DSFMonitorConsole.option();
                }
                else
                {
                    std::cerr << "Failed to connect to dsfdataservice. Retrying..." << std::endl;
                }
                break;
            }
            case 0:
            {
                std::cout << "Returning..." << std::endl;
                return;
            }
            default:
            {
                std::cerr << "Invalid option. Try again." << std::endl;
                break;
            }
        }
    }
}

int main()
{
    // 设置环境变量，去除日志输出
    if (!setenv("DSF_LOG_OUT_MODE", "0", 1) == 0) {
        std::cerr << "Failed to set environment variable" << std::endl;
    }

    short option=-1;
    while (option!=0)
    {
        std::cout << std::endl;
        std::cout << "Program Menu:" << std::endl;
        std::cout << "1. Monitor" << std::endl;
        std::cout << "0. Exit" << std::endl;
        std::cin >> option;
        switch (option)
        {
            case 1:
            {
                //创建监视器
                createMonitor();
                break;
            }
            case 0:
            {
                std::cout << "Exiting..." << std::endl;
                break;
            }
            default:
            {
                std::cerr << "Invalid option. Try again." << std::endl;
                break;
            }
        }
    }

    return 0;
}