#ifndef DSFMonitorConsole_H
#define DSFMonitorConsole_H

#include "drsdk_inner.h"
#include "dsfapi.h"
#include <iostream>
#include <string>
#include <thread>

#define DEFAULT_IP "127.0.0.1"
#define DEFAULT_PORT 1234

class DSFMonitorConsole
{
public:
    DSFMonitorConsole();
    DSFMonitorConsole(const std::string &ip,const uint16_t port);
    ~DSFMonitorConsole();

    //连接状态
    bool connectStatus();
    //读取信号
    void readSignal();
    //写入信号
    void writeSignal();
    //选项
    void option();

private:
    std::string m_ip=DEFAULT_IP;
    uint16_t m_port=DEFAULT_PORT;
    dsfapi::DRSdkContext *m_context = nullptr;

    //初始化
    void init();
};

#endif // DSFMonitorConsole_H