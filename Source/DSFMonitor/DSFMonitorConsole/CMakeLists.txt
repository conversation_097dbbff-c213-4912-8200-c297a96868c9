cmake_minimum_required(VERSION 3.10)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

PROJECT (dsfmonitorconsole)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files And Head Files
file(GLOB SRC_LIST ${CMAKE_CURRENT_SOURCE_DIR}/*.cpp ${CMAKE_CURRENT_SOURCE_DIR}/*.h)
Set(SRCS ${SRCS} ${SRC_LIST})

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME dsfmonitorconsole)

include_directories(${CMAKE_SOURCE_DIR}/DRSdk/dsfapi)

SET(LINK_LIBS dsfapi pthread boost_system)

INCLUDE($ENV{DRDIR}CMakeCommonExec)