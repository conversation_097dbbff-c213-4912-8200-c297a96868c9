#pragma once

#include "drsdk_inner.h"
#include "dsfapi.h"
#include <iostream>
#include <string>
#include <thread>

#ifdef _WIN32
#ifdef DRSDK_EXPORTS
#define DRSDK_C_API __declspec(dllexport)
#else
#define DRSDK_C_API __declspec(dllimport)
#endif
#else
#define DRSDK_C_API
#endif

// TAG点的数据类型.在控制命令时会作为参数传入
#define TAG_DATATYPE_STRING			0		// ASCII string
#define TAG_DATATYPE_INT			1		// 16 bit Signed Integer value
#define TAG_DATATYPE_UINT			2		// 16 bit Unsigned Integer value
#define TAG_DATATYPE_REAL			3		// 32 bit IEEE float
#define TAG_DATATYPE_BOOL			4		// 1 bit value
#define TAG_DATATYPE_TIME			5		// 4 byte TIME (H:M:S:T)
#define TAG_DATATYPE_UDINT			6		// 32 bit integer value
#define TAG_DATATYPE_DINT			7		// 32 bit signed integer value
#define TAG_DATATYPE_LREAL			8		// 64 bit double
#define TAG_DATATYPE_BLOB			9		// blob, maximum 65535
#define TAG_DATATYPE_SINT			10		// 8 bit signed integer value
#define TAG_DATATYPE_USINT			11		// 8 bit unsigned integer value
#define TAG_DATATYPE_LINT			12		// 64 bit signed integer value
#define TAG_DATATYPE_ULINT			13		// 64 bit signed integer value
#define TAG_DATATYPE_LTIME			14		// 64 bit signed integer value
#define TAG_DATATYPE_BYTE			15		// 1 byte value
#define TAG_DATATYPE_WORD			16		// 16 bit unsigned integer value
#define TAG_DATATYPE_DWORD			17		// 32 bit unsigned integer value
#define TAG_DATATYPE_LWORD			18		// 64 bit unsigned integer value
#define TAG_DATATYPE_CHAR			19		// char
#define TAG_DATATYPE_UDT			20		// user defined type
#define TAG_DATATYPE_DATE           21      // 32 bit unsigned integer value
#define TAG_DATATYPE_TOD      		22      // 32 bit unsigned integer value, TIME OF DAY
#define TAG_DATATYPE_DT    			23      // 32 bit unsigned integer value, DATE AND TIME
#define TAG_DATATYPE_WCHAR          24      // utf-16
#define TAG_DATATYPE_WSTRING        25      // utf-16 string, maximum 16382
#define TAG_DATATYPE_LDATE          26      // 64 bit unsigned integer value
#define TAG_DATATYPE_LTOD   		27      // 64 bit unsigned integer value, LTIME OF DAY
#define TAG_DATATYPE_LDT  			28      // 64 bit unsigned integer value, LDATE AND TIME

// DSF数据服务默认IP
#define DEFAULT_IP "127.0.0.1"
// DSF数据服务默认端口
#define DEFAULT_PORT 1234

dsfapi::DRSdkContext *g_context = nullptr;

extern "C"
{
    /**
     * @brief          init dsfapi: Construct a DRSdkContext ; connect to server, alloc memery and create threads
     * @param [in]     ip  contains server ip
     * @param [in]     port  server port
     * @version        2025/05/14	mayisong	Initial Version
     */
    DRSDK_C_API void init(const char *ip=DEFAULT_IP, const uint16 port=DEFAULT_PORT);

    /**
     * @brief          this function should must be called to disconnect from dsf and release resources.
     * @version        2025/05/14	mayisong	Initial Version
     */
    DRSDK_C_API void freeDRSdkContext();

    /**
     * @brief          get connection status
     * @version        2025/05/14	mayisong	Initial Version
     */
    DRSDK_C_API bool connectStatus();

    /**
     * @brief		write control commands to dsf station
     * @param [in]	tagName   tag names array
     * @param [in]	value  tag value array. should fill buffer and length
     * @param [in]	length  the length of value
     * @return		Successfully returned 0, other returned error codes
     * @version		2025/05/14	mayisong	Initial Version
     */
    DRSDK_C_API int32 writeValue(const char *tagName, const char *value, const size_t length);

    /**
     * @brief		read tag values from dsf station
     * @param [in]	tagName   tag names array
     * @param [in]	nTagNameCount   the cout of tag
     * @param [out]	value   tag value array.
     *                          !!! [should release by Free_Tag_Value]
     * @param [out]	pTagValueCount  the count of tag value
     * @return		Successfully returned 0, other returned error codes
     * @version		2025/05/14	mayisong	Initial Version
     */
    DRSDK_C_API int32 readValue(const char *tagName, const uint16_t type, char *value);
}