#include "DSFAPIInterface.h"

//根据类型获取值
void getValueByType(char *buffer, const uint16_t &type, char *value)
{
    switch (type)
    {
        case TAG_DATATYPE_STRING:
        {
            //完整字符串
            // const uint8_t length=*reinterpret_cast<uint8_t*>(buffer+1);
            // strncpy(value, buffer, length+2);
            // value[length+2] = '\0';
            const uint8_t length=*reinterpret_cast<uint8_t*>(buffer+1);
            std::cout<<"值: "<<std::string(buffer)<<std::endl;
            strncpy(value, buffer+2, length);
            value[length] = '\0';
            break;
        }
        case TAG_DATATYPE_INT:
        {
            int16_t tmp=*reinterpret_cast<int16_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_UINT:
        {
            uint16_t tmp=*reinterpret_cast<uint16_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_REAL:
        {   
            float32 tmp=*reinterpret_cast<float32*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            //设置为默认科学计数法
            std::ostringstream oss;
            oss << std::defaultfloat << tmp;
            strcpy(value, oss.str().c_str());
            break;
        }
        case TAG_DATATYPE_BOOL:
        {            
            bool tmp=*reinterpret_cast<bool*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_TIME:
        {
            int32_t tmp=*reinterpret_cast<int32_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_UDINT:
        {
            uint32_t tmp=*reinterpret_cast<uint32_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_DINT:
        {
            int32_t tmp=*reinterpret_cast<int32_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_LREAL:
        {
            double tmp=*reinterpret_cast<double*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            //设置为默认科学计数法
            std::ostringstream oss;
            oss << std::defaultfloat << tmp;
            strcpy(value, oss.str().c_str());
            break;
        }
        case TAG_DATATYPE_BLOB:
            break;
        case TAG_DATATYPE_SINT:
        {
            int8_t tmp=*reinterpret_cast<int8_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_USINT:
        {
            uint8_t tmp=*reinterpret_cast<uint8_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_LINT:
        {
            int64_t tmp=*reinterpret_cast<int64_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_ULINT:
        {
            uint64_t tmp=*reinterpret_cast<uint64_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_LTIME:
        {
            int64_t tmp=*reinterpret_cast<int64_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_BYTE:
        {
            uint8_t tmp=*reinterpret_cast<uint8_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_WORD:
        {
            uint16_t tmp=*reinterpret_cast<uint16_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_DWORD:
        {
            uint32_t tmp=*reinterpret_cast<uint32_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_LWORD:
        {
            uint64_t tmp=*reinterpret_cast<uint64_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_CHAR:
        {
            uint8_t tmp=*reinterpret_cast<uint8_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());

            // std::cout<<"值: "<<std::string(buffer)<<std::endl;
            // strncpy(value, buffer, 8 - 1);
            // value[8 - 1] = '\0';
            break;
        }
        case TAG_DATATYPE_UDT:
        {
            // int16_t tmp1=*reinterpret_cast<int16_t*>(buffer);
            // bool tmp2=*reinterpret_cast<bool*>(buffer+2);
            // uint8_t tmp3=*reinterpret_cast<uint8_t*>(buffer+3);
            // int32_t tmp4=*reinterpret_cast<int32_t*>(buffer+4);
            // std::string valueStr("struct("+std::to_string(tmp1)+","+std::to_string(tmp2)+","+std::to_string(tmp3)+","+std::to_string(tmp4)+")");
            // std::cout<<"值: "<<valueStr<<std::endl;
            // strcpy(value, valueStr.c_str());
            break;
        }
        case TAG_DATATYPE_DATE:
        {
            uint32_t tmp=*reinterpret_cast<uint32_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_TOD:
        {
            uint32_t tmp=*reinterpret_cast<uint32_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        case TAG_DATATYPE_DT:
        {
            uint32_t tmp=*reinterpret_cast<uint32_t*>(buffer);
            std::cout<<"值: "<<tmp<<std::endl;
            strcpy(value, std::to_string(tmp).c_str());
            break;
        }
        default:
            break;
    }
}

DRSDK_C_API void init(const char *ip, const uint16 port)
{
    // 设置环境变量，去除日志输出
    if (!setenv("DSF_LOG_OUT_MODE", "0", 1) == 0) {
        std::cerr << "Failed to set environment variable" << std::endl;
    }

    //设置连接参数
    dsfapi::DRSdkConnectParam tConnectParam;
    strncpy(tConnectParam.szServerIp, ip, sizeof(tConnectParam.szServerIp) - 1);
    tConnectParam.nServerPort = port;
    tConnectParam.nHeartbeatTimeoutMs = 3000;
    tConnectParam.nConnectTimeoutMs = 1000;

    //设置选项
    dsfapi::DRSdkOption tOption;
    tOption.nThreadPoolNum = 10;
    tOption.nRequestWaitTimeoutMs = 1000;

    //初始化DRSDK
    g_context = dsfapi::DR_Init(tConnectParam, tOption);
}

DRSDK_C_API void freeDRSdkContext()
{
    dsfapi::Free_DR_Sdk_Context(&g_context);
}

DRSDK_C_API bool connectStatus()
{
    return g_context->ConnectStatus();
}

DRSDK_C_API int32 writeValue(const char *tagName, const char *value, const size_t length)
{
    std::cout<<"名称: "<<std::string(tagName)<<"，长度: "<<length<<std::endl;

    dsfapi::TagValue tagValue;
    tagValue.ReSet(value, length);
    const char *pTagName[1] = {tagName};
    const int32 count=1;
    
    auto res = dsfapi::DR_Write(g_context, static_cast<const char**>(pTagName), &tagValue, count);
    // if (res == 0) {
    //     std::cout << "Signal "+std::string(tagName)+" written successfully!" << std::endl;
    // } else {
    //     std::cerr << "Failed to write signal."<< std::endl;
    // }
    return res;
}

DRSDK_C_API int32 readValue(const char *tagName, const uint16_t type, char *value)
{
    std::cout<<"名称: "<<std::string(tagName)<<"，类型: "<<type<<std::endl;

    const char *pTagName[1] = {tagName};
    dsfapi::TagValue *pTagValue = nullptr;
    int32 *pErrorCode = nullptr;
    int32 nTagValueCount = 0;

    auto res = dsfapi::DR_Read(g_context, pTagName, 1, &pTagValue, &pErrorCode, &nTagValueCount);
    if (res == 0 && nTagValueCount > 0 && pErrorCode[0] == 0)
    {
        getValueByType(pTagValue[0].Buffer(),type,value);
        // std::cout << "Signal "+std::string(tagName)+" value: " << static_cast<uint16_t>(*value) << std::endl;
    } else {
        // std::cerr << "Failed to read signal. Error code: " << (pErrorCode ? pErrorCode[0] : -1) << std::endl;
    }
    const int32 errorCode=*pErrorCode;

    dsfapi::Free_Int32_Ptr(&pErrorCode);
    dsfapi::Free_Tag_Value(&pTagValue);

    return errorCode;
}