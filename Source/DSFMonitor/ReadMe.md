# 软件运行

```shell
#默认启动
./DSF_Monitor
#指定服务端（暂时不实现，否则控制台输出功能会增加复杂度）
# ./DSF_Monitor -s 127.0.0.1:1234
```

## 连接服务
```shell
Select an option:
1. Connect to server with default parameters
2. Connect to server with custom IP and port
3. Exit
# 选择1为默认参数连接
Done!
# 选择2为指定参数连接
Done!
# 选择3为退出
Done!
# 连接成功后会进入信号操作菜单
Done!
# 连接失败后会重连三次返回选择菜单
Done!
# 连接失败要给出错误信息（最好把错误码转换成字符串）
@mayisong
```

## 信号读值
```shell
# 输入信号地址
Done!
# 返回读取结果
Done!
```


## 信号写入
```shell
# 输入信号地址
# 输入信号值
# 返回写入结果
```