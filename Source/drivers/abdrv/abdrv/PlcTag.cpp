/************************************************************************
 *	Filename:		XXXXX
 *	Copyright:		XXXXX Company Software Co., Ltd.
 *
 *	Description:	$(Desp) .
 *
 *	@author:		XXXXX
 *	@version		DATE	AUTHOR	Initial Version
*************************************************************************/

#include "plctag.h"
#include "common/CVLog.h"
#include "common/RMAPIDef.h"
#include "driversdk/cvdrivercommon.h"
#include "plctag/libplctag.h"
#include "ace/OS_NS_stdio.h"
#include "ace/OS_NS_sys_stat.h"
#include "ace/OS_NS_sys_time.h"
#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif // _WIN32

#include "ListTag.h"
#include <sstream>
#include <set>
typedef std::map<std::string, struct tag_entry_s*> TagsMap;
typedef std::map<int, struct udt_entry_s*> UdtsMap;
std::map<std::string, TagsMap> deviceTags;
std::map<std::string, UdtsMap> deviceUdts;
std::map<int32, int32_t> tagIds;
std::map<int32, std::string> txtTagAddrs;
typedef struct {
    std::string addr;
    char rel_offset;
} bit_array_index;
std::map<int32, bit_array_index> bitArrayIndices;
std::set<int32> singleBitTags;

const char* GetPureAddress(const char* pAddr);

using namespace std;
struct tag
{
	int32_t tagid;
	DRVHANDLE hDataBlock;
};
//map<DRVHANDLE, int32_t> g_mapTagID;
map<string, tag> g_mapTagID;
//用以记录第一次注册失败的点，会在
//map<DRVHANDLE, list<string> > g_mapErrTag;
typedef std::vector<TagInfo> TagInfoVector;
//map<DRVHANDLE, map<DRVHANDLE, CVDATABLOCK*> > g_mapDataBlocks;
CCVLog g_CVLogPLCTagDrv;
int g_nRMStatus = RM_STATUS_ACTIVE; // 默认假设为活动状态
#define CONNPRAM_LEN 32
#define RECONNECT_DELAY_MS 1000
#define CVPLCTAG_DRIVE_NAME "abdrv"
#define TIMEOUT 300
#define DINT_LEN 4
#define NO_NEED_TO_CHANGE 1
#define NEED_CHANGE 2
#define CPU_CONTROLLOGIX "ControlLogix"
#define CPU_SLC "SLC"
#define CPU_MICROLOGIX "micrologix"
#define TAG_STRING_TEMPLATE "protocol=ab_eip&gateway=%s&path=1,%s&cpu=%s&elem_count=%s&name=%s"

// 定义用于存储通信时间的索引
#define DEVICE_USER_DATA_PTR_INDEX_LAST_SUCCESS_TIME 0
//设备状态点变更的超时次数
#define DEVICE_STATUS_CHANGE_TIMEOUT_COUNT 3
//针对string,
/**
 *  针对stringPLC类型的改值函数，比较string内的字符串实际长度大小和配置长度大小
 *  改变读到的块内容，用来进行更新数据块,避免驱动框架读到意外的内容.
 *  @param  -[in/out]  uint8_t buffer: [数据]
 *  @param	-[in]  int nByteOffset : [偏移]
 *  @param	-[in]  int nTagSize : tag点配置的长度
 */
void ChangeContent(uint8_t *buffer, int nBitOffset, int nTagSize)
{
	int nByteOffset = nBitOffset / BITS_PER_BYTE;
	if (nByteOffset < 4)
	{
		CV_ERROR(g_CVLogPLCTagDrv, -1, "", REQUIRED_VERSION);
		return;
	}
	
	unsigned int nCapacity = 0;
	memcpy(&nCapacity, buffer + (nByteOffset - DINT_LEN), DINT_LEN);
	char* szTag = new char[nTagSize];
	memset(szTag, 0x00, nTagSize);
	if (nCapacity >= 0)
	{
		int nSize = nCapacity <= nTagSize ? nCapacity : nTagSize;
		memcpy(szTag, buffer + nByteOffset, nSize);
		memcpy(buffer + nByteOffset, szTag, nTagSize);
	}
	delete[] szTag;
}

//由于ACE的sleep函数有时候不准确，尝试使用该方法进行sleep
void sleepcp(int milliseconds) // 跨平台 sleep 函数
{
#ifdef _WIN32
	Sleep(milliseconds);
#else
	usleep(milliseconds * 1000);
#endif // _WIN32
}

//如果设备的扩展参数3未填或格式不对则返回默认值
string GetPara(const char* szPara, const char* szSplit, const char* szDefaultVal)
{
	if (szPara == NULL || szSplit == NULL)
	{
		return szDefaultVal;
	}
	string strPara = szPara;
	strPara += ";";
	int nPos = strPara.find(szSplit);
	if (nPos != string::npos)
	{
		nPos += strlen(szSplit);
		string strTemp = strPara.substr(nPos);
		int nPos2 = strTemp.find(';');
		string strContent = strTemp.substr(0, nPos2);
		return strContent;
	}
	return szDefaultVal;
}

/**
*  获取驱动版本号.
*
*  @version   07/20/2012   Initial Version.
*/
CVDRIVER_EXPORTS long GetDrvFrameVersion()
{
	return 2;
}

CVDRIVER_EXPORTS long Begin()
{
	g_CVLogPLCTagDrv.SetLogFileNameThread(CVPLCTAG_DRIVE_NAME);
	return 0;
}

/**
 *  初始化函数，驱动EXE启动时该函数被调用，可在该函数中实现自定义初始化操作.
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012    Initial Version.
 */
CVDRIVER_EXPORTS long Initialize()
{
	//TODO：初始化操作
	if (plc_tag_check_lib_version(REQUIRED_VERSION) != PLCTAG_STATUS_OK) {
		CV_ERROR(g_CVLogPLCTagDrv, 0, "Required compatible library version %d.%d.%d not available", REQUIRED_VERSION);
	}

	return DRV_SUCCESS;
}

/**
 *  驱动EXE退出时该函数被调用.
 *  在该函数中可以释放自定义资源、断开设备连接等操作.
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012    Initial Version.
 */
CVDRIVER_EXPORTS long UnInitialize()
{
    plc_tag_shutdown();
	return DRV_SUCCESS;
}

/**
 * 检查并更新设备通信状态
 * 根据读取结果和最后一次成功通信时间判断设备状态
 * 
 * @param hDevice 设备句柄
 * @param bSuccessRead 是否成功读取数据
 * @return DRV_SUCCESS 执行成功
 */
long CheckAndUpdateDeviceStatus(DRVHANDLE hDevice, bool bSuccessRead)
{
    // 获取当前时间
    ACE_Time_Value currentTime = ACE_OS::gettimeofday();
    
    // 获取设备信息
    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    if (NULL == pDevice) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Failed to get device info");
        return -1;
    }
    
    // 获取上次成功通信时间
    ACE_Time_Value* pLastSuccessTime = (ACE_Time_Value*)Drv_GetUserDataPtr(hDevice, DEVICE_USER_DATA_PTR_INDEX_LAST_SUCCESS_TIME);
    // 如果时间指针为空，创建新的时间记录
    if (NULL == pLastSuccessTime) {
        pLastSuccessTime = new ACE_Time_Value();
        *pLastSuccessTime = ACE_Time_Value(0, 0);  // 初始化为0时间
        Drv_SetUserDataPtr(hDevice, DEVICE_USER_DATA_PTR_INDEX_LAST_SUCCESS_TIME, (void*)pLastSuccessTime);
        CV_DEBUG(g_CVLogPLCTagDrv, "Device '%s' created last success time record initialized to 0", 
             pDevice->pszName);
    }
    
    // 根据读取结果更新设备状态
    if (bSuccessRead)
    {
        // 成功读取，更新时间戳并确保设备状态为GOOD
        *pLastSuccessTime = currentTime;
        Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
        CV_DEBUG(g_CVLogPLCTagDrv, "Device '%s' communication successful, updating last success time at %ld sec", 
             pDevice->pszName, pLastSuccessTime->sec());
    }
    else
    {
        // 检查是否超过3倍超时时间
        ACE_Time_Value timeoutPeriod(0, pDevice->nRecvTimeout * 1000 * DEVICE_STATUS_CHANGE_TIMEOUT_COUNT); // 3倍超时时间，转换为微秒
            
        if (currentTime - *pLastSuccessTime > timeoutPeriod)
        {
            // 超时时间过长，将设备状态置为BAD
            CV_ERROR(g_CVLogPLCTagDrv, -1, "Device '%s' communication timeout for more than %d ms, set status to BAD", 
                     pDevice->pszName, pDevice->nRecvTimeout * DEVICE_STATUS_CHANGE_TIMEOUT_COUNT);
            Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
        }
    }
    
    return DRV_SUCCESS;
}
CVDRIVER_EXPORTS long OnDeviceTimer(DRVHANDLE hDevice)
{
     // 增加主备状态判断
     int nCurRMStatus = Drv_IsActiveHost();
     if (nCurRMStatus != RM_STATUS_ACTIVE)
     {
         // 如果是备机状态，不执行读操作
         CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
         CV_INFO(g_CVLogPLCTagDrv, "Device '%s' is in inactive state, skip reading", pDevice->pszName);
         g_nRMStatus = nCurRMStatus;
         return DRV_SUCCESS;
     }
     
     // 检测状态变化并记录
     if (g_nRMStatus != nCurRMStatus)
     {
         CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
         CV_INFO(g_CVLogPLCTagDrv, "Device '%s' state changed from %d to %d", pDevice->pszName, g_nRMStatus, nCurRMStatus);
         g_nRMStatus = nCurRMStatus;
     }
	int rc;
	string strCombName;
	map<int32_t, int> map_PLCTags;
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	long lCount = Drv_GetDataBlockCount(hDevice);
	CV_TRACE(g_CVLogPLCTagDrv, "device: %s count of datablock: %ld", pDevice->pszName, lCount);
	int nErrLimit = atoi(pDevice->pszParam2);
	if (nErrLimit <0 || nErrLimit >= 1000)
	{
		CV_TRACE(g_CVLogPLCTagDrv, "Device:%s, pszParam2: %s, out of bound ,set to 3", pDevice->pszName, pDevice->pszParam2);
		nErrLimit = 3;
	}
	CVDATABLOCK* pDataBlock = new CVDATABLOCK[lCount];
	CVDATABLOCK* pDBArr = pDataBlock;
	long nRet = Drv_GetDatablocks(hDevice, pDataBlock, lCount);
	CV_TRACE(g_CVLogPLCTagDrv, "Before Read");
    //批量读
	for (int i = 0; i < lCount; i++)
	{
		strCombName = pDevice->pszName;
		strCombName += pDBArr->pszName;
		map<string, tag>::iterator iter;
		iter = g_mapTagID.find(strCombName);
		if (iter->second.tagid >= 0)
		{
			rc = plc_tag_read(iter->second.tagid, 0);
			if (rc != PLCTAG_STATUS_OK && rc != PLCTAG_STATUS_PENDING)
			{
				CV_ERROR(g_CVLogPLCTagDrv, -1, "ERROR: plc_tag_read, DeviceName:%s, BlockName:%s, Address:%s! Got error code %d: %s!", pDevice->pszName, pDBArr->pszName, pDBArr->pszAddress, rc, plc_tag_decode_error(rc));
			}
			map_PLCTags.insert(std::make_pair(iter->second.tagid, DATA_STATUS_TIMEOUT_FAILURE));
		} else {
            int lefttick = Drv_GetUserData(iter->second.hDataBlock, 6);
            if (lefttick == 0) {
                char buf[250] = { 0 };
                memset(buf, 0x00, 250);
                string strType = GetPara(pDevice->pszParam3, "cpu=", CPU_CONTROLLOGIX);
                snprintf(buf, sizeof(buf), TAG_STRING_TEMPLATE, pDevice->pszConnParam, pDevice->pszParam1, strType.c_str(), pDBArr->pszParam1, pDBArr->pszAddress);
                CV_DEBUG(g_CVLogPLCTagDrv, "Try to create tag for \"%s\" with group name \"%s\" tagid %d", buf, strCombName.c_str(), iter->second.tagid);
                int32_t nTag = plc_tag_create(buf, pDevice->nRecvTimeout);
                if (nTag < 0) {
                    Drv_SetUserData(iter->second.hDataBlock, 6, 3);
                    CV_DEBUG(g_CVLogPLCTagDrv, "Failed to create tagid: %d for device: %s datablock: %s address: %s", nTag, pDevice->pszName, pDBArr->pszName, pDBArr->pszAddress);
                } else {
                    iter->second.tagid = nTag;
                    CV_INFO(g_CVLogPLCTagDrv, "Succeed to create tagid: %d for device: %s datablock: %s address: %s", nTag, pDevice->pszName, pDBArr->pszName, pDBArr->pszAddress);
                }
            } else {
                Drv_SetUserData(iter->second.hDataBlock, 6, lefttick - 1);
            }
        }
		pDBArr++;
	}
	delete[] pDataBlock;
	CV_TRACE(g_CVLogPLCTagDrv, "After Read");

	/* wait for all to finish */
	bool done = 1;
	int nSleepCount = 0;
	ACE_Time_Value currentTimeTicket;
	ACE_Time_Value timeoutTimeTicket;
	ACE_Time_Value tvTimeWait;
	tvTimeWait.msec((int)pDevice->nRecvTimeout);
	timeoutTimeTicket = ACE_OS::gettimeofday() + tvTimeWait;
	
	do {
		done = 1;
		map<int32_t, int>::iterator iter;
		for (iter = map_PLCTags.begin(); iter != map_PLCTags.end(); iter++)
		{
			if (iter->second != DATA_STATUS_TIMEOUT_FAILURE)
				continue;
			rc = plc_tag_status(iter->first);
			if (rc != PLCTAG_STATUS_OK) {
				CV_TRACE(g_CVLogPLCTagDrv, "waiting tagid: %d", iter->first);
				done = 0;
			}
			else
			{
				iter->second = DATA_STATUS_OK;
			}
		}
		if (!done) {
			//sleep 1ms
			sleepcp(1);
			currentTimeTicket = ACE_OS::gettimeofday();
		}
	} while (timeoutTimeTicket > currentTimeTicket && !done);
	CV_TRACE(g_CVLogPLCTagDrv, "After Sleep");
	if (!done) {
		CV_WARN(g_CVLogPLCTagDrv, -1, "Timeout waiting for tags to be ready!Device:%s", pDevice->pszName);
	}

	CVDATABLOCK* pDataBlock2 = new CVDATABLOCK[lCount];
	CVDATABLOCK* pDBArr2 = pDataBlock2;
	nRet = Drv_GetDatablocks(hDevice, pDataBlock2, lCount);

    
    // 初始化成功读取标志
    bool bSuccessRead = false;

	for (int j = 0; j < lCount; j++)
	{
		strCombName = pDevice->pszName;
		strCombName += pDBArr2->pszName;
		map<string, tag>::iterator iter;
		iter = g_mapTagID.find(strCombName);
		CV_DEBUG(g_CVLogPLCTagDrv, "pDBArr2->pszName: %s", pDBArr2->pszName);
		if (iter != g_mapTagID.end())
		{
			int nErrCount = Drv_GetUserData(iter->second.hDataBlock, 5);
			CV_DEBUG(g_CVLogPLCTagDrv, "tagid: %d, ElemBits: %d, ElemNum: %d", iter->second.tagid, pDBArr2->nElemBits, pDBArr2->nElemNum);
			map<int32_t, int>::iterator iterPLCTags;
			iterPLCTags = map_PLCTags.find(iter->second.tagid);
			if (iterPLCTags != map_PLCTags.end() && iterPLCTags->second != DATA_STATUS_OK)
			{
				rc = plc_tag_abort(iter->second.tagid);
				CV_INFO(g_CVLogPLCTagDrv, "Abort ongoing request because tag not ready, Device: %s, DataBlock:%s, Address:%s, result %d: %s!", pDevice->pszName, pDBArr2->pszName, pDBArr2->pszAddress, rc, plc_tag_decode_error(rc));
				if (nErrCount < nErrLimit)
				{
					nErrCount++;
					Drv_SetUserData(iter->second.hDataBlock, 5, nErrCount);
					CV_WARN(g_CVLogPLCTagDrv, -1, "Tag not ready, Device:%s, DataBlock:%s, Address:%s, nErrCount:%d.", pDevice->pszName, pDBArr2->pszName, pDBArr2->pszAddress, nErrCount);	
				}
				else
				{
					CV_ERROR(g_CVLogPLCTagDrv, -1, "Tag not ready, Device:%s, DataBlock:%s, Address:%s. nErrCount larger than nErrLimit(%d) UpdateBlockStatus:DATA_STATUS_TIMEOUT_FAILURE!", pDevice->pszName, pDBArr2->pszName, pDBArr2->pszAddress, nErrLimit);
					Drv_UpdateBlockStatus(hDevice, iter->second.hDataBlock, DATA_STATUS_TIMEOUT_FAILURE);
				}
				pDBArr2++;
				continue;
			}
			uint8_t *buffer = (uint8_t*)malloc(pDBArr2->nElemBits * pDBArr2->nElemNum / BITS_PER_BYTE);
			rc = plc_tag_get_raw_bytes(iter->second.tagid, 0, buffer, pDBArr2->nElemBits * pDBArr2->nElemNum / BITS_PER_BYTE);
			if (rc != PLCTAG_STATUS_OK) {
				//plc_tag_unlock(iter->second);
				free(buffer);
				if (nErrCount <= nErrLimit)
				{
					nErrCount++;
					Drv_SetUserData(iter->second.hDataBlock, 5, nErrCount);
					CV_ERROR(g_CVLogPLCTagDrv, 0, "ERROR: Unable to read the data, DeviceName:%s, BlockName:%s, Address:%s!, nErrCount:%d. Got error code %d: %s!", pDevice->pszName, pDBArr2->pszName, pDBArr2->pszAddress, nErrCount, rc, plc_tag_decode_error(rc));
				}
				else
				{
					CV_ERROR(g_CVLogPLCTagDrv, 0, "ERROR: Unable to read the data, DeviceName:%s, BlockName:%s, Address:%s!, nErrCount larger than nErrLimit(%d). Got error code %d: %s!", pDevice->pszName, pDBArr2->pszName, pDBArr2->pszAddress, nErrLimit, rc, plc_tag_decode_error(rc));
					Drv_UpdateBlockStatus(hDevice, iter->second.hDataBlock, DATA_STATUS_TIMEOUT_FAILURE);
				}
				pDBArr2++;
				continue;
			}
            else{//if (rc != PLCTAG_STATUS_OK)
                // 成功从PLC读取到了一个数据，标记为成功
                bSuccessRead = true;
            }
			bool bString = false;
			int* nbitOffset = NULL;
			int* nTagLength = NULL;
			int nArrayLength = 0;
			int nFlag = Drv_GetUserData(iter->second.hDataBlock, 4);
			if (nFlag == NO_NEED_TO_CHANGE)
			{
				CV_TRACE(g_CVLogPLCTagDrv, "Device:%s, pszName: %s, No string need change", pDevice->pszName, pDBArr2->pszName);
				bString = false;
			}
			else if (nFlag == NEED_CHANGE)
			{
				CV_TRACE(g_CVLogPLCTagDrv, "Device:%s, pszName: %s, some string need to be changed", pDevice->pszName, pDBArr2->pszName);
				nbitOffset = (int *)Drv_GetUserDataPtr(iter->second.hDataBlock, 1);
				nTagLength = (int *)Drv_GetUserDataPtr(iter->second.hDataBlock, 2);
				nArrayLength = Drv_GetUserData(iter->second.hDataBlock, 3);
				bString = true;
			}
			else
			{
				nbitOffset = NULL;
				nTagLength = NULL;
				
				bool bRet = Drv_BlockContainType(DT_ASCII, nbitOffset, nTagLength, &nArrayLength, iter->second.hDataBlock);
				if (bRet)
				{

					Drv_SetUserDataPtr(iter->second.hDataBlock, 1, (void*)(nbitOffset));
					Drv_SetUserDataPtr(iter->second.hDataBlock, 2, (void*)(nTagLength));
					Drv_SetUserData(iter->second.hDataBlock, 3, nArrayLength);
					Drv_SetUserData(iter->second.hDataBlock, 4, NEED_CHANGE);
					bString = true;
					CV_INFO(g_CVLogPLCTagDrv, "Device:%s, pszAddress: %s, find string need to be changed, ArrayLength:%d", pDevice->pszName, pDBArr2->pszAddress, nArrayLength);
				}
				else
				{
					Drv_SetUserData(iter->second.hDataBlock, 4, NO_NEED_TO_CHANGE);
					bString = false;
					CV_DEBUG(g_CVLogPLCTagDrv, "Device:%s, pszAddress: %s, not find string need to be changed", pDevice->pszName, pDBArr2->pszAddress);
				}
			}
			
			if (bString)
			{
				CV_DEBUG(g_CVLogPLCTagDrv, "Got string needed to be changed!");
				for (int i = 0; i < nArrayLength; i++)
				{
					ChangeContent(buffer, nbitOffset[i], nTagLength[i]);
				}
			}
			long lRet = Drv_UpdateBlockData(hDevice, iter->second.hDataBlock, (const char*)buffer, 0, pDBArr2->nElemBits * pDBArr2->nElemNum / BITS_PER_BYTE, DATA_STATUS_OK, NULL);
			Drv_SetUserData(iter->second.hDataBlock, 5, 0);
			if (lRet)
			{
				CV_ERROR(g_CVLogPLCTagDrv, 0, "Update Block:%s Address:%s Error!Device:%s EC:%ld", pDBArr2->pszName, pDBArr2->pszAddress, pDevice->pszName, lRet);
			}
			else
			{
				CV_DEBUG(g_CVLogPLCTagDrv, "Update Block:%s Address:%s, Device:%s, Success", pDBArr2->pszName, pDBArr2->pszAddress, pDevice->pszName);
			}
			free(buffer);
		}
		CV_TRACE(g_CVLogPLCTagDrv, "pDBArr2++");
		pDBArr2++;
	}
    // 处理完所有数据块后更新设备状态
    long checkRet = CheckAndUpdateDeviceStatus(hDevice, bSuccessRead);
    if (checkRet != DRV_SUCCESS) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Failed to check and update device status");
    }
	CV_TRACE(g_CVLogPLCTagDrv, "End");
	delete[] pDataBlock2;
	return DRV_SUCCESS;
}

//****************************************************************************


CVDRIVER_EXPORTS long OnDataBlockTimer(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	//由于暂时不修改驱动配置界面，因此配置中的数据块参数会包含轮询周期
	//为了避免驱动框架打印大量错误日志，因此添加该空方法
	return DRV_SUCCESS;
}



/**
 *  当有控制命令时该函数被调用.
 *  在该函数中根据传递的参数，向设备下发控制命令。
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *  @param	-[in]  DRVHANDLE hDataBlock : [数据块句柄]
 *  @param	-[in]  int nTagDataType : tag点类型
 *  @param  -[in]  int nTagByteOffset: [tag点在块中的字节偏移量]
 *  @param  -[in]  int nTagBitOffset: [字节内的位偏移量]
 *  @param	-[in]  char *szCmdData : [控制指令]
 *  @param  -[in]  int nCmdDataLenBits: [控制数据长度,单位是bit]
 *
  *  @return : 控制执行反馈结果
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnWriteCmdEx(DRVHANDLE hDevice, DRVHANDLE hDataBlock, string strTagName,string strTagAddr,int32 nTagID,uint8 nDataType,uint8 nTagType, int nTagByteOffset, int nTagBitOffset, char* szCmdData, int nCmdDataLenBits)
{
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    string strPureAddr = GetPureAddress(strTagAddr.c_str());
    std::map<int32, std::string>::iterator it = txtTagAddrs.find(nTagID);
    if (it != txtTagAddrs.end()) {
        strPureAddr = it->second;
    }
    std::map<int32, bit_array_index>::iterator bitit = bitArrayIndices.find(nTagID);
    if (bitit != bitArrayIndices.end()) {
        strPureAddr = bitit->second.addr;
    }
	char buf[250] = { 0 };
	memset(buf, 0x00, 250);
	string strType = GetPara(pDevice->pszParam3, "cpu=", CPU_CONTROLLOGIX);
	snprintf(buf, sizeof(buf), TAG_STRING_TEMPLATE, pDevice->pszConnParam, pDevice->pszParam1, strType.c_str(), "1", strPureAddr.c_str());
    std::map<int32, int32_t>::iterator tagit = tagIds.find(nTagID);
    int32_t nTag;
    if (tagit == tagIds.end()) {
        nTag = plc_tag_create(buf, pDevice->nRecvTimeout);
        CV_INFO(g_CVLogPLCTagDrv, "Creating tag \"%s\" on write", buf);
        if (nTag < 0) {
            CV_ERROR(g_CVLogPLCTagDrv, -1, "Could not create tag for device: %s, address: %s with error: %s", pDevice->pszName, strTagAddr.c_str(), plc_tag_decode_error(nTag));
            CV_ERROR(g_CVLogPLCTagDrv, -1, "Failed to write device: %s, address: %s because creating tag failed", pDevice->pszName, strTagAddr.c_str());
            return -1;
        } else {
            tagIds.insert(make_pair(nTagID, nTag));
        }
    } else {
        nTag = tagit->second;
    }
	int rc = PLCTAG_STATUS_OK;
    rc = plc_tag_lock(nTag);
    if (rc != PLCTAG_STATUS_OK) {
        CV_ERROR(g_CVLogPLCTagDrv, 0, "plc_tag_lock failed! device: %s, address: %s with error %d: %s!", pDevice->pszName, strTagAddr.c_str(), rc, plc_tag_decode_error(rc));
        return -1; /* punt, no lock */
    }
    uint32_t nLenBytes = 0;
    nLenBytes = nCmdDataLenBits / BITS_PER_BYTE;

    if (nCmdDataLenBits % BITS_PER_BYTE)
        nLenBytes++;

    if (nDataType == TAG_DATATYPE_STRING)
    {
        //由于库里setstring方法在写非标准string时长度默认了64，会导致写值失败，因此对TXT变量由框架自己写LEN和DATA属性的内容
        uint32_t datalen = strlen(szCmdData);
        //先写string结构体前四个字节表示长度的内容
        rc = plc_tag_set_raw_bytes(nTag, 0, (uint8_t *)&datalen, DINT_LEN);
        if (rc != PLCTAG_STATUS_OK) {
            CV_ERROR(g_CVLogPLCTagDrv, 0, "Unable to set length, device:%s, address:%s! Got error code %d: %s!", pDevice->pszName, strTagAddr.c_str(), rc, plc_tag_decode_error(rc));
            plc_tag_unlock(nTag);
            return -1;
        }
        rc = plc_tag_set_raw_bytes(nTag, DINT_LEN, (uint8_t *)szCmdData, nLenBytes);
        if (rc != PLCTAG_STATUS_OK) {
            CV_ERROR(g_CVLogPLCTagDrv, 0, "Unable to set data, device:%s, address:%s! Got error code %d: %s!", pDevice->pszName, strTagAddr.c_str(), rc, plc_tag_decode_error(rc));
            plc_tag_unlock(nTag);
            return -1;
        }
        rc = plc_tag_write(nTag, RECONNECT_DELAY_MS);
        if (rc != PLCTAG_STATUS_OK) {
            CV_ERROR(g_CVLogPLCTagDrv, 0, "Unable to write string data, device:%s, address:%s! Got error code %d: %s!", pDevice->pszName, strTagAddr.c_str(), rc, plc_tag_decode_error(rc));
            plc_tag_unlock(nTag);
            return -1;
        }
        CV_INFO(g_CVLogPLCTagDrv, "Write Success, device:%s, address: %s", pDevice->pszName, strTagAddr.c_str());
    }
    else if (nDataType == TAG_DATATYPE_BOOL)
    {
        std::set<int32>::iterator singlebitit = singleBitTags.find(nTagID);
        char szData = *szCmdData;
        int nData = (int)szData;
        if (bitit != bitArrayIndices.end()) {
            rc = plc_tag_read(nTag, RECONNECT_DELAY_MS);        // read before write to make sure that updated data is written
            if (rc != PLCTAG_STATUS_OK) {
                CV_ERROR(g_CVLogPLCTagDrv, 0, "Unable to read data when write bool array, device:%s, address:%s! Got error code %d: %s!", pDevice->pszName, strTagAddr.c_str(), rc, plc_tag_decode_error(rc));
                plc_tag_unlock(nTag);
                return -1;
            }
            CV_INFO(g_CVLogPLCTagDrv, "Write bool array device:%s, address: %s bit relative offset: %d", pDevice->pszName, strTagAddr.c_str(), bitit->second.rel_offset);
            rc = plc_tag_set_bit(nTag, bitit->second.rel_offset, nData);
            if (rc != PLCTAG_STATUS_OK) {
                CV_ERROR(g_CVLogPLCTagDrv, 0, "Unable to set write bit in array, device:%s, address:%s! Got error code %d: %s!", pDevice->pszName, strTagAddr.c_str(), rc, plc_tag_decode_error(rc));
                plc_tag_unlock(nTag);
                return -1;
            }
        } else if (singlebitit != singleBitTags.end()) {
            rc = plc_tag_set_bit(nTag, 0, nData);
            if (rc != PLCTAG_STATUS_OK) {
                CV_ERROR(g_CVLogPLCTagDrv, 0, "Unable to set write bit data, device:%s, address:%s! Got error code %d: %s!", pDevice->pszName, strTagAddr.c_str(), rc, plc_tag_decode_error(rc));
                plc_tag_unlock(nTag);
                return -1;
            }
        } else {    // For boolean in UDT or scattered boolean tags
            uint8_t nData = (uint8_t)szData;
            rc = plc_tag_set_uint8(nTag, 0, nData);
            if (rc != PLCTAG_STATUS_OK) {
                CV_ERROR(g_CVLogPLCTagDrv, 0, "Unable to set write bool data, device:%s, address:%s! Got error code %d: %s!", pDevice->pszName, strTagAddr.c_str(), rc, plc_tag_decode_error(rc));
                plc_tag_unlock(nTag);
                return -1;
            }
        }
        rc = plc_tag_write(nTag, RECONNECT_DELAY_MS);
        if (rc != PLCTAG_STATUS_OK) {
            CV_ERROR(g_CVLogPLCTagDrv, 0, "Unable to write bool data, device:%s, address:%s! Got error code %d: %s!", pDevice->pszName, strTagAddr.c_str(), rc, plc_tag_decode_error(rc));
            plc_tag_unlock(nTag);
            return -1;
        }
        CV_INFO(g_CVLogPLCTagDrv, "Write Success, device:%s, address: %s", pDevice->pszName, strTagAddr.c_str());
    }
    else
    {
        CV_INFO(g_CVLogPLCTagDrv, "Write Length: %d", nLenBytes);
        rc = plc_tag_set_raw_bytes(nTag, 0, (uint8_t*)szCmdData, nLenBytes);
        if (rc != PLCTAG_STATUS_OK) {
            CV_ERROR(g_CVLogPLCTagDrv, 0, "Unable to set write data, device:%s, address:%s! Got error code %d: %s!", pDevice->pszName, strTagAddr.c_str(), rc, plc_tag_decode_error(rc));
            plc_tag_unlock(nTag);
            return -1;
        }
        rc = plc_tag_write(nTag, RECONNECT_DELAY_MS);
        if (rc != PLCTAG_STATUS_OK) {
            CV_ERROR(g_CVLogPLCTagDrv, 0, "Unable to write the data, device:%s, address:%s! Got error code %d: %s!", pDevice->pszName, strTagAddr.c_str(), rc, plc_tag_decode_error(rc));
            plc_tag_unlock(nTag);
            return -1;
        }
        CV_INFO(g_CVLogPLCTagDrv, "Write Success, device:%s, address: %s", pDevice->pszName, strTagAddr.c_str());
        plc_tag_unlock(nTag);
    }
	
	return 0;
}

/**
 *  添加设备时该函数被调用.
 *  该函数主要针对非tcp连接设备，用户可以通过设备句柄获取设备连接参数，初始化连接设备
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    string plc = GetPara(pDevice->pszParam3, "cpu=", CPU_CONTROLLOGIX);
    string path = string("1,") + pDevice->pszParam1;
    std::map<int, struct udt_entry_s*> udtsmap;
    std::map<std::string, struct tag_entry_s*> tagsmap;
    CV_INFO(g_CVLogPLCTagDrv, "Get all tags when device \"%s\" is added.", pDevice->pszName);
    if (deviceTags.find(pDevice->pszName) == deviceTags.end()) {
        int rc = get_all_tags(pDevice->pszConnParam, path.c_str(), plc.c_str(), tagsmap, udtsmap);
        deviceTags.insert(std::make_pair(pDevice->pszName, tagsmap));
        deviceUdts.insert(std::make_pair(pDevice->pszName, udtsmap));
        print_all_tags(pDevice->pszName, tagsmap, udtsmap);
        if (rc != PLCTAG_STATUS_OK) {
            CV_ERROR(g_CVLogPLCTagDrv, -1, "Device %s get all tags failed with error code %d %s", pDevice->pszName, rc, plc_tag_decode_error(rc));
        }
    }
    //校验设备超时参数
    if(pDevice->nRecvTimeout <= 0)
    {
        pDevice->nRecvTimeout = TIMEOUT;
        CV_DEBUG(g_CVLogPLCTagDrv, "Device %s recv timeout is set to default value %d ms", pDevice->pszName, pDevice->nRecvTimeout);
    }
    // 初始化设备上次成功通信时间
    ACE_Time_Value* pLastSuccessTime = new ACE_Time_Value();
    *pLastSuccessTime = ACE_Time_Value(0, 0);  // 初始化为0时间
    Drv_SetUserDataPtr(hDevice, DEVICE_USER_DATA_PTR_INDEX_LAST_SUCCESS_TIME, (void*)pLastSuccessTime);
    CV_DEBUG(g_CVLogPLCTagDrv, "When add Device, Device '%s' created last success time record initialized to 0", 
             pDevice->pszName);
    return DRV_SUCCESS;
}

/**
 *  删除设备时该函数被调用.
 *  该函数主要针对非tcp连接设备，用户可以通过设备句柄获取设备信息，处理断开设备、释放相关资源等操作
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
    // OnDeviceDelete may be called multiple times, so checking is needed to avoid this.
    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    string strDevice = pDevice->pszName;
    if (deviceTags.find(strDevice) != deviceTags.end()) {
        free_all_tags(deviceTags[strDevice], deviceUdts[strDevice]);
        deviceTags.erase(strDevice);
        deviceUdts.erase(strDevice);
    }
    // 释放设备上次成功通信时间的内存
    ACE_Time_Value* pLastSuccessTime = (ACE_Time_Value*)Drv_GetUserDataPtr(hDevice, DEVICE_USER_DATA_PTR_INDEX_LAST_SUCCESS_TIME);
    if (pLastSuccessTime != NULL) {
        delete pLastSuccessTime;
    }
	return DRV_SUCCESS;
}




/**
 *  添加数据块时该函数被调用.
 *  用户需要在该函数中返回数据块大小
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *  @param  -[in]  DRVHANDLE hDataBlock: [数据块句柄]
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	//hDataBlock的用户数据目前使用1-5，指针和非指针实为两个数组，只是方便维护不用相同index
	//1-2为用户指针，1-4位string结构体相关，5则是异常计数

    // setting all tags to -1 to handle the case that some device is offline.
    // In that case, plc_tag_create timeout will block other device.

    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
	string strName = pDevice->pszName;
	strName += pDataBlock->pszName;
	tag Tag;
	Tag.tagid = -1;
	Tag.hDataBlock = hDataBlock;
	g_mapTagID.insert(make_pair(strName, Tag));
    Drv_SetUserData(hDataBlock, 6, 3);  // 6 refers to tag checking interval
	//开始时将所有数据块的异常计数设置为0
	Drv_SetUserData(hDataBlock, 5, 0);
	return DRV_SUCCESS;
}

/**
 *  删除数据块时该函数被调用.
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *  @param  -[in]  DRVHANDLE hDataBlock: [数据块句柄]
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	//TODO：执行数据块相关清理操作
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
	map<string, tag>::iterator iter;
	string strCombName = pDevice->pszName;
	strCombName += pDataBlock->pszName;
	iter = g_mapTagID.find(strCombName);
	if (iter != g_mapTagID.end())
	{
		plc_tag_destroy(iter->second.tagid);
	}

	return DRV_SUCCESS;
}

const char * GetPureAddress(const char *pAddr)
{
	static char szAddress[ICV_IOADDR_MAXLEN + 1];
	memset(szAddress, 0, ICV_IOADDR_MAXLEN + 1);
	string strAddrTmp = pAddr;
	int nPos = strAddrTmp.find(":");
	if (nPos != std::string::npos)
	{
		string strIP = strAddrTmp.substr(nPos+1);
		strncpy(szAddress, strIP.c_str(), strIP.length());
		/*nPos = strIP.find(":");
		if (nPos != std::string::npos)
		{
			strIP = strIP.substr(0, nPos);
			strncpy(szAddress, strIP.c_str(), strIP.length());
		}
		else
		{
			strncpy(szAddress, strIP.c_str(), strIP.length());
		}*/
	}
	else
	{
		return NULL;
	}

	return szAddress;
}

int GetBitOffSet(const char *pAddr)
{
	int nBitOffSet = 0;
	int nBtye = 0;//存地址后第一个冒号后的数字
	int nBit = 0;//存地址后第二个冒号后的数字
	static char szAddress[ICV_IOADDR_MAXLEN + 1];
	memset(szAddress, 0, ICV_IOADDR_MAXLEN + 1);
	string strAddrTmp = pAddr;
	int nPos = strAddrTmp.find("#");
	if (nPos != std::string::npos)
	{
		strAddrTmp = strAddrTmp.substr(0, nPos);
	}
	//去掉设备名称
	nPos = strAddrTmp.find(":");
	if (nPos != std::string::npos)
	{
		//寻找字节偏移
		string strTmp = strAddrTmp.substr(nPos + 1);
		nPos = strTmp.find(":");
		if (nPos != std::string::npos)
		{
			//寻找位偏移
			strTmp = strTmp.substr(nPos + 1);
			nPos = strTmp.find(":");
			if (nPos != std::string::npos)
			{
				string strByte = strTmp.substr(0, nPos);
				string strBit = strTmp.substr(nPos + 1);
				nBtye = atoi(strByte.c_str());
				nBit = atoi(strBit.c_str());
				nBitOffSet = nBtye * BITS_PER_BYTE + nBit;
			}
			else
			{
				string strByte = strTmp.substr(nPos + 1);
				nBtye = atoi(strByte.c_str());
				nBitOffSet = nBtye * BITS_PER_BYTE;
			}
		}
		else
		{
			nBitOffSet = 0;
		}
	}

	return nBitOffSet;
}

// address section of tag name separated by '.' (dot).
struct addr_section {
    string name;
    int arrIdx;
    bool isArray;
    bool isString;
    int stringCapacity;
    bool isBitString;
    int bitStringCount;
    char bitRelOffset;       // relative offset for bool array index
    bool isSingleBit;
    uint16_t type;
    uint32_t offset;
    uint32_t elemsize;
    uint16_t bitidx;            // bit index for boolean or integer bit position
    uint16_t num_dimensions;    // indicate if the name is an array in device
    uint16_t dimension0;
};

static bool IsStructOrArray(string strAddr, vector<addr_section>& sectionvec) {
    string del = ".";
    size_t pos = 0;
    bool isStruct = false;
    bool isInTagArray = false;
    do {
        struct addr_section section;
        section.arrIdx = 0;
        section.type = 0;
        section.offset = 0;
        section.elemsize = 0;
        section.bitidx = 0;
        section.num_dimensions = 0;
        section.isString = false;
        section.stringCapacity = 0;
        section.isBitString = 0;
        section.bitStringCount = 0;
        section.isSingleBit = false;
        pos = strAddr.find(del);
        string token;
        if (pos == string::npos) {
            token = strAddr;
        } else {
            token = strAddr.substr(0, pos);
            isStruct = true;
        }
        size_t openpos = token.find("[", 0);
        string afteropen = token.substr(openpos + 1);
        size_t n = afteropen.find_first_not_of("0123456789");
        if (n == afteropen.length() - 1 && afteropen[n] == ']') {
            isInTagArray = true;
            section.name = token.substr(0, openpos);
            section.isArray = true;
            section.arrIdx = atoi(afteropen.substr(0, afteropen.length() - 1).c_str());
        } else {
            section.name = token;
            section.isArray = false;
        }
        sectionvec.push_back(section);
        if (pos == string::npos) {
            break;
        }
        strAddr.erase(0, pos + del.length());
    } while (pos != string::npos);
    struct addr_section& section = sectionvec.back();
    pos = section.name.rfind("DATA#");
    if (pos != string::npos) {
        size_t lastpos = section.name.find_first_not_of("0123456789", pos + 5);
        if (lastpos == string::npos) {
            section.isString = true;
            section.stringCapacity = atoi(section.name.substr(pos + 5).c_str());
            section.name = section.name.substr(0, pos + 4);
        }
    }
    return isStruct || isInTagArray;
}

static bool FieldSanityCheck(addr_section &lastsection, struct udt_entry_s *lastsectionudt, addr_section &section) {
    if (lastsection.num_dimensions > 0 && !lastsection.isArray) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Field \"%s\" cannot be used in non-array element \"%s\" in \"%s.%s\"", section.name.c_str(), lastsection.name.c_str(), lastsection.name.c_str(), section.name.c_str());
        return false;
    }
    if (lastsection.type & TYPE_IS_STRUCT) {
        lastsection.elemsize = lastsectionudt->instance_size;
        bool found = false;
        int idx = 0;
        for (idx = 0; idx < lastsectionudt->num_fields; ++idx) {
            if (strcmp(section.name.c_str(), lastsectionudt->fields[idx].name) == 0) {
                section.type = lastsectionudt->fields[idx].type;      // save for later use
                section.offset = lastsectionudt->fields[idx].offset;
                if (!(section.type & TYPE_IS_STRUCT)) {
                    if ((section.type & 0xFF) == 0xC1) {    // atomic type, but not bit boolean
                        section.bitidx = lastsectionudt->fields[idx].metadata;
                    }
                }
                if (section.type & 0x2000) {    // If is array
                    section.num_dimensions = 1;
                    section.dimension0 = lastsectionudt->fields[idx].metadata; // save dimensions for array field
                }
                found = true;
                break;
            }
        }
        if (!found) {
            CV_ERROR(g_CVLogPLCTagDrv, -1, "Field \"%s\" is not found in UDT \"%s\"", section.name.c_str(), lastsectionudt->name);
            return false;
        }
        if (section.isArray) {
            int16_t atomic_type = section.type & 0xFF;
            if (!(section.type & 0x2000)) { // if not an array
                CV_ERROR(g_CVLogPLCTagDrv, -1, "Field \"%s[%d]\" is not array type in UDT \"%s\"", section.name.c_str(), section.arrIdx, lastsectionudt->name);
                return false;
            } else if (!(section.type & TYPE_IS_STRUCT) && (atomic_type == 0xD1 || atomic_type == 0xD2 || atomic_type == 0xD3 || atomic_type == 0xD4)) { // if bit array
                int bitsize = 0;
                switch (atomic_type) {
                    case 0xD1: bitsize = 8;  /* "8-bit bit string"; */ break;
                    case 0xD2: bitsize = 16; /* "16-bit bit string";*/ break;
                    case 0xD3: bitsize = 32; /* "32-bit bit string";*/ break;
                    case 0xD4: bitsize = 64; /* "64-bit bit string";*/ break;
                }
                if (section.arrIdx >= section.dimension0 * bitsize) {
                    CV_ERROR(g_CVLogPLCTagDrv, -1, "Field \"%s[%d]\" is out of range of bit string in UDT \"%s\"", section.name.c_str(), section.arrIdx, lastsectionudt->name);
                    return false;
                } else {
                    section.isBitString = true;
                    section.bitStringCount = section.arrIdx / bitsize + 1;
                    section.bitRelOffset = section.arrIdx % bitsize;
                }
            } else if (section.arrIdx >= section.dimension0) {
                CV_ERROR(g_CVLogPLCTagDrv, -1, "Field \"%s[%d]\" is out of range in UDT \"%s\"", section.name.c_str(), section.arrIdx, lastsectionudt->name);
                return false;
            }
        }
        if (section.isString) {
            if (section.stringCapacity > section.dimension0) {
                CV_ERROR(g_CVLogPLCTagDrv, -1, "Field \"%s#%d\" is out of range in UDT \"%s\"", section.name.c_str(), section.stringCapacity, lastsectionudt->name);
                return false;
            }
        }

    } else {    // When it goes here, the current name can only be a number. For example, address such as int[0].7
        uint16_t last_atomic_type = lastsection.type & 0xFF; /* MAGIC */
        if (section.name.find_first_not_of("0123456789") != string::npos) {
            CV_ERROR(g_CVLogPLCTagDrv, -1, "Field \"%s\" is not valid in \"%s.%s\"", section.name.c_str(), lastsection.name.c_str(), section.name.c_str());
            return false;
        }
        int bitidx = atoi(section.name.c_str());
        int size = 0;
        switch (last_atomic_type) {
            case 0xC2: size = 1; /* type = "SINT: Signed 8-bit integer value"; */ break;
            case 0xC3: size = 2; /* type = "INT: Signed 16-bit integer value"; */ break;
            case 0xC4: size = 4; /* type = "DINT: Signed 32-bit integer value"; */ break;
            case 0xC5: size = 8; /* type = "LINT: Signed 64-bit integer value"; */ break;
            case 0xC6: size = 1; /* type = "USINT: Unsigned 8-bit integer value"; */ break;
            case 0xC7: size = 2; /* type = "UINT: Unsigned 16-bit integer value"; */ break;
            case 0xC8: size = 4; /* type = "UDINT: Unsigned 32-bit integer value"; */ break;
            case 0xC9: size = 8; /* type = "ULINT: Unsigned 64-bit integer value"; */ break;
            default: size = 0; break;
        }
        if (!(size > 0)) {
            CV_ERROR(g_CVLogPLCTagDrv, -1, "Field \"%s\" is not valid for a non-integer type in \"%s.%s\"", section.name.c_str(), lastsection.name.c_str(), section.name.c_str());
            return false;
        }
        if (bitidx >= size * BITS_PER_BYTE) {
            CV_ERROR(g_CVLogPLCTagDrv, -1, "Field \"%s\" is out of range in \"%s.%s\"", section.name.c_str(), lastsection.name.c_str(), section.name.c_str());
            return false;
        } else {
            section.isSingleBit = true;
            section.bitidx = bitidx;      // set metadata to bit index, for example cip.5
        }
    }

    return true;
}

static int BitOffsetInTag(const string &strDevice, const string &strPureAddr, vector<addr_section>& sectionvec) {
    if (deviceTags.find(strDevice) == deviceTags.end()) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Cannot find tag lists of device \"%s\"", strDevice.c_str());
        return -1;
    }
    TagsMap tagsmap = deviceTags[strDevice];
    UdtsMap udtsmap = deviceUdts[strDevice];

    // The first tag name is handled separately.
    addr_section& tagsection = sectionvec[0];
    if (tagsmap.find(tagsection.name) == tagsmap.end()) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Cannot find tag \"%s\" of address \"%s\" in device \"%s\"", tagsection.name.c_str(), strPureAddr.c_str(), strDevice.c_str());
        return -1;
    }
    uint16_t type = tagsmap[tagsection.name]->type;
    if (type & TYPE_IS_SYSTEM) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Type of tag \"%s\" of address \"%s\" in device \"%s\" is SYSTEM", tagsection.name.c_str(), strPureAddr.c_str(), strDevice.c_str());
        return -1;
    } else if (type & TYPE_IS_STRUCT) {     // such as motor., motorarray[3].
        int udtid = type & TYPE_UDT_ID_MASK;
        if (udtsmap.find(udtid) == udtsmap.end()) {
            CV_ERROR(g_CVLogPLCTagDrv, -1, "UDT of tag \"%s\" of address \"%s\" in device \"%s\" is not found", tagsection.name.c_str(), strPureAddr.c_str(), strDevice.c_str());
            return -1;
        }
    }
    tagsection.type = type;
    tagsection.elemsize = tagsmap[tagsection.name]->elem_size;
    tagsection.num_dimensions = tagsmap[tagsection.name]->num_dimensions;
    // support only 1 dimension array now
    tagsection.dimension0 = tagsmap[tagsection.name]->dimensions[0];

    // tag name sanity check
    if (tagsection.isArray) {     // if it is an array
        int16_t atomic_type = tagsection.type & 0xFF;
        if (!(tagsection.num_dimensions > 0)) { // if not an array
            CV_ERROR(g_CVLogPLCTagDrv, -1, "Tag \"%s[%d]\" is not array type of address \"%s\" in device \"%s\"", tagsection.name.c_str(), tagsection.arrIdx, strPureAddr.c_str(), strDevice.c_str());
            return -1;
        } else if (!(tagsection.type & TYPE_IS_STRUCT ) && (atomic_type == 0xD1 || atomic_type == 0xD2 || atomic_type == 0xD3 || atomic_type == 0xD4)) { // if bit array
            int bitsize = 0;
            switch (atomic_type) {
                case 0xD1: bitsize = 8;  /* "8-bit bit string"; */ break;
                case 0xD2: bitsize = 16; /* "16-bit bit string";*/ break;
                case 0xD3: bitsize = 32; /* "32-bit bit string";*/ break;
                case 0xD4: bitsize = 64; /* "64-bit bit string";*/ break;
            }
            if (tagsection.arrIdx >= tagsection.dimension0 * bitsize) {
                CV_ERROR(g_CVLogPLCTagDrv, -1, "Tag \"%s[%d]\" is out of range of bit string of address \"%s\" in device \"%s\"", tagsection.name.c_str(), tagsection.arrIdx, strPureAddr.c_str(), strDevice.c_str());
                return -1;
            } else {
                tagsection.isBitString = true;
                tagsection.bitStringCount = tagsection.arrIdx / bitsize + 1;
                tagsection.bitRelOffset = tagsection.arrIdx % bitsize;
            }
        } else if (tagsection.arrIdx >= tagsection.dimension0) {
            CV_ERROR(g_CVLogPLCTagDrv, -1, "Type of tag \"%s[%d]\" of address \"%s\" in device \"%s\" is out of range", tagsection.name.c_str(), tagsection.arrIdx, strPureAddr.c_str(), strDevice.c_str());
            return -1;
        }
    }

    for (size_t i = 1; i < sectionvec.size(); ++i) {
        int bSanity;
        if (sectionvec[i-1].type & TYPE_IS_STRUCT) {
            int lastudtid = sectionvec[i-1].type & TYPE_UDT_ID_MASK;
            struct udt_entry_s* lastsectionudt = udtsmap[lastudtid];
            bSanity = FieldSanityCheck(sectionvec[i - 1], lastsectionudt, sectionvec[i]);
        } else {
            bSanity = FieldSanityCheck(sectionvec[i - 1], NULL, sectionvec[i]);
        }
        if (!bSanity) {
            CV_ERROR(g_CVLogPLCTagDrv, -1, "Checking of field \"%s\" of address \"%s\" is failed in device \"%s\"", sectionvec[i].name.c_str(), strPureAddr.c_str(), strDevice.c_str());
            return -1;
        }
    }

    int bitoffset = 0;
    for (size_t i = 0; i < sectionvec.size(); ++i) {
        struct addr_section section = sectionvec[i];
        if (section.type & TYPE_IS_STRUCT) {
            bitoffset += (section.arrIdx * section.elemsize + section.offset) * BITS_PER_BYTE;
        } else {    // It is the last part of the address chain
            uint16_t atomic_type = section.type & 0xFF; /* MAGIC */
            // enumerate all cases
            if (atomic_type == 0xD1 || atomic_type == 0xD2 || atomic_type == 0xD3 || atomic_type == 0xD4) { // if bit array
                bitoffset += section.offset * BITS_PER_BYTE + section.arrIdx;   // it works when there is no subscript
            } else if (section.name.find_first_not_of("0123456789") == string::npos) { // bit position, such as int.7
                bitoffset += section.bitidx;
            } else if (atomic_type == 0xC1) {   // if boolean
                bitoffset += section.offset * BITS_PER_BYTE + section.bitidx;
            } else {    // if integer array
                int size = type_to_size(section.type);
                if (size == -1) {
                    CV_ERROR(g_CVLogPLCTagDrv, -1, "Type of tag \"%s\" of address \"%s\" in device \"%s\" is not supported", tagsection.name.c_str(), strPureAddr.c_str(), strDevice.c_str());
                    return -1;
                } else {
                    bitoffset += (section.arrIdx * size + section.offset) * BITS_PER_BYTE;
                }
            }
        }
    }

    return bitoffset;
}

CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum,
	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
{
	if (NULL == pOutDevTags || NULL == pnTagsNum || \
		NULL == pTagGrps || NULL == pnTagGrpsNum)
		return -1;

	*pnTagsNum = nTagsNum;

	memset(pTagGrps, 0, *pnTagGrpsNum * sizeof(TagGroupInfo));

    *pnTagGrpsNum = 0;

	std::copy(pDevTags, pDevTags + nTagsNum, pOutDevTags);

	char szGroupName[ICV_DATABLOCKNAME_MAXLEN + 1];
	memset(szGroupName, 0, ICV_DATABLOCKNAME_MAXLEN + 1);

    std::map<std::string, int> nameGroupIdMap;
    for (int i = 0; i < nTagsNum; ++i)
    {
        string strAddr = pDevTags[i].szAddress;
        string strDevice;
        size_t pos = 0;
        if ((pos = strAddr.find(":")) != string::npos) {
            strDevice = strAddr.substr(0, pos);
        }
        string strPureAddr = GetPureAddress(strAddr.c_str());

        pOutDevTags[i].nTagType = pDevTags[i].nTagType;

        vector<addr_section> sectionvec;
        bool needTransform = false;
        int elem_count = 0;
        int bitoffset = -1;
        bool isStruct = IsStructOrArray(strPureAddr, sectionvec);
        // Ignore txt tag without valid '#' suffix.
        if (pOutDevTags[i].nTagType == ICV_TAGTYPE_TX && !sectionvec.back().isString) {
            CV_ERROR(g_CVLogPLCTagDrv, -1, "Tag \"%s\" address \"%s\" length suffix is invalid", pOutDevTags[i].szTagName, pOutDevTags[i].szAddress);
            continue;
        }
        if (isStruct) {
            bitoffset = BitOffsetInTag(strDevice, strPureAddr, sectionvec);
            if (!(bitoffset < 0)) {
                needTransform = true;
            }
        }

        // handle bit array index
        addr_section bitsection;
        bitsection.isBitString = false;
        if (sectionvec[0].isBitString) {
            bitsection = sectionvec[0];
        } else if (sectionvec.back().isBitString) {
            bitsection = sectionvec.back();
        }
        if (bitsection.isBitString) {
            size_t pos = strPureAddr.rfind("[");
            string amendedAddr = strPureAddr.substr(0, pos);
            stringstream ss;
            ss << (bitsection.bitStringCount - 1);
            amendedAddr += string("[") + ss.str() + string("]");
            bit_array_index tmp = { amendedAddr, bitsection.bitRelOffset };
            bitArrayIndices.insert(make_pair(pDevTags[i].nTagID, tmp));
        }

        // single bit
        if (sectionvec.back().isSingleBit) {
            singleBitTags.insert(pDevTags[i].nTagID);
        }

        if (sectionvec.back().isString) {
            size_t pos = strPureAddr.rfind(".DATA#");
            string amendedAddr = strPureAddr.substr(0, pos);
            txtTagAddrs.insert(make_pair(pDevTags[i].nTagID, amendedAddr));
        }

        // Ignore txt tag with length suffix out of range.
        if (needTransform && sectionvec.back().isString) {
            if (sectionvec.back().stringCapacity > sectionvec.back().dimension0) {
                CV_ERROR(g_CVLogPLCTagDrv, -1, "Tag \"%s\" address \"%s\" length suffix %d is out of range %d", pOutDevTags[i].szTagName, pOutDevTags[i].szAddress, sectionvec.back().stringCapacity, sectionvec.back().dimension0);
                continue;
            }
        }

        if (needTransform) {
            CV_INFO(g_CVLogPLCTagDrv, "Address \"%s\" is transformed", strAddr.c_str());
            if (nameGroupIdMap.find(sectionvec[0].name) == nameGroupIdMap.end()) {
                nameGroupIdMap.insert(make_pair(sectionvec[0].name, *pnTagGrpsNum));

                sprintf(pOutDevTags[i].szGrpName, "group%d", *pnTagGrpsNum);

                strncpy(pTagGrps[*pnTagGrpsNum].szAddress, sectionvec[0].name.c_str(), ICV_IOADDR_MAXLEN);
                pTagGrps[*pnTagGrpsNum].nElemBits = BITS_PER_BYTE;

                if (!sectionvec[0].isArray) {
                    strcpy(pTagGrps[*pnTagGrpsNum].szParam1, "1");
                    pTagGrps[*pnTagGrpsNum].nElemNum = sectionvec[0].elemsize;
                    if (sectionvec[0].elemsize > ((1 << 16) - 1))   {     // datablock nElemNum limit is 65535
                        CV_WARN(g_CVLogPLCTagDrv, -1, "Group %s address: %s bytes count: %d exceeds datablock limit", pTagGrps[*pnTagGrpsNum].szGroupName, pTagGrps[*pnTagGrpsNum].szAddress, pTagGrps[*pnTagGrpsNum].nElemNum);
                    }
                } else {
                    if (sectionvec[0].isBitString) {
                        snprintf(pTagGrps[*pnTagGrpsNum].szParam1, ICV_EXTEND_PARAM_LEN, "%d", sectionvec[0].bitStringCount);
                        pTagGrps[*pnTagGrpsNum].nElemNum = sectionvec[0].bitStringCount * sectionvec[0].elemsize;
                        if (sectionvec[0].bitStringCount * sectionvec[0].elemsize > ((1 << 16) - 1))   {     // datablock nElemNum limit is 65535
                            CV_WARN(g_CVLogPLCTagDrv, -1, "Group %s address: %s bytes count: %d exceeds datablock limit", pTagGrps[*pnTagGrpsNum].szGroupName, pTagGrps[*pnTagGrpsNum].szAddress, pTagGrps[*pnTagGrpsNum].nElemNum);
                        }
                    } else {
                        snprintf(pTagGrps[*pnTagGrpsNum].szParam1, ICV_EXTEND_PARAM_LEN, "%d", sectionvec[0].arrIdx + 1);
                        pTagGrps[*pnTagGrpsNum].nElemNum = (sectionvec[0].arrIdx + 1) * sectionvec[0].elemsize;
                        if ((sectionvec[0].arrIdx + 1) * sectionvec[0].elemsize > ((1 << 16) - 1))   {     // datablock nElemNum limit is 65535
                            CV_WARN(g_CVLogPLCTagDrv, -1, "Group %s address: %s bytes count: %d exceeds datablock limit", pTagGrps[*pnTagGrpsNum].szGroupName, pTagGrps[*pnTagGrpsNum].szAddress, pTagGrps[*pnTagGrpsNum].nElemNum);
                        }
                    }
                }
                sprintf(pTagGrps[*pnTagGrpsNum].szGroupName, "group%d", *pnTagGrpsNum);

                (*pnTagGrpsNum)++;
            } else {
                int groupid = nameGroupIdMap[sectionvec[0].name];
                int elem_count = atoi(pTagGrps[groupid].szParam1);
                if (sectionvec[0].isArray) {
                    if (!sectionvec[0].isBitString && elem_count < sectionvec[0].arrIdx + 1) {
                        snprintf(pTagGrps[groupid].szParam1, ICV_EXTEND_PARAM_LEN, "%d", sectionvec[0].arrIdx + 1);
                        pTagGrps[groupid].nElemNum = (sectionvec[0].arrIdx + 1) * sectionvec[0].elemsize;
                        if ((sectionvec[0].arrIdx + 1) * sectionvec[0].elemsize > ((1 << 16) - 1))   {     // datablock nElemNum limit is 65535
                            CV_WARN(g_CVLogPLCTagDrv, -1, "Group %s address: %s bytes count: %d exceeds datablock limit", pTagGrps[groupid].szGroupName, pTagGrps[groupid].szAddress, pTagGrps[groupid].nElemNum);
                        }
                    } else if (sectionvec[0].isBitString && elem_count < sectionvec[0].bitStringCount) {
                        snprintf(pTagGrps[groupid].szParam1, ICV_EXTEND_PARAM_LEN, "%d", sectionvec[0].bitStringCount);
                        pTagGrps[groupid].nElemNum = sectionvec[0].bitStringCount * sectionvec[0].elemsize;
                        if (sectionvec[0].bitStringCount * sectionvec[0].elemsize > ((1 << 16) - 1))   {     // datablock nElemNum limit is 65535
                            CV_WARN(g_CVLogPLCTagDrv, -1, "Group %s address: %s bytes count: %d exceeds datablock limit", pTagGrps[groupid].szGroupName, pTagGrps[groupid].szAddress, pTagGrps[groupid].nElemNum);
                        }
                    }
                }
                sprintf(pOutDevTags[i].szGrpName, "group%d", groupid);
            }

            pOutDevTags[i].nBitOffSet = bitoffset;
        } else {
            CV_INFO(g_CVLogPLCTagDrv, "Address \"%s\" is not transformed", strAddr.c_str());
            pOutDevTags[i].nBitOffSet = 0;
            sprintf(pOutDevTags[i].szGrpName, "group%d", *pnTagGrpsNum);

            strncpy(pTagGrps[*pnTagGrpsNum].szAddress, strPureAddr.c_str(), ICV_IOADDR_MAXLEN);
            pTagGrps[*pnTagGrpsNum].nElemBits = BITS_PER_BYTE;
            strcpy(pTagGrps[*pnTagGrpsNum].szParam1, "1");
            sprintf(pTagGrps[*pnTagGrpsNum].szGroupName, "group%d", *pnTagGrpsNum);
            switch (pDevTags[i].nDataType) {
                case TAG_DATATYPE_LINT:
                case TAG_DATATYPE_ULINT:
                case TAG_DATATYPE_LREAL:
                case TAG_DATATYPE_LWORD:
                    pTagGrps[*pnTagGrpsNum].nElemNum = 8;
                    break;

                case TAG_DATATYPE_INT:
                case TAG_DATATYPE_UINT:
                case TAG_DATATYPE_WORD:
                    pTagGrps[*pnTagGrpsNum].nElemNum = 2;
                    break;

                case TAG_DATATYPE_REAL:
                case TAG_DATATYPE_UDINT:
                case TAG_DATATYPE_DINT:
                case TAG_DATATYPE_TIME:
                case TAG_DATATYPE_DWORD:
                    pTagGrps[*pnTagGrpsNum].nElemNum = 4;
                    break;

                case TAG_DATATYPE_BOOL:
                    pTagGrps[*pnTagGrpsNum].nElemNum = 1;
                    break;

                case TAG_DATATYPE_STRING:
                {
					//现在此段逻辑不会再进入，string也会被认为是一个结构体在上面被处理
                    int nPos = strPureAddr.find("#");
                    //const char* pTemp = strstr(pDevTags[*pnTagGrpsNum].szAddress, "#");
                    if (nPos != std::string::npos)
                    {
						//非标准string的DATA元素个数不再是1，而是被认为是一个个字符的集合体
                        //取值长度要加上开头的长度数值
                        string strLength = strPureAddr.substr(nPos + 1);
                        string strAddress = strPureAddr.substr(0, nPos);
                        pTagGrps[*pnTagGrpsNum].nElemNum = atoi(strLength.c_str());
                        strcpy(pTagGrps[*pnTagGrpsNum].szParam1, strLength.c_str());
                        strncpy(pTagGrps[*pnTagGrpsNum].szAddress, strAddress.c_str(), ICV_IOADDR_MAXLEN);
                    }
                }
                break;

                case TAG_DATATYPE_SINT:
                case TAG_DATATYPE_USINT:
                case TAG_DATATYPE_BYTE:
                case TAG_DATATYPE_CHAR:
                    pTagGrps[*pnTagGrpsNum].nElemNum = 1;
                default:
                    break;
            }
            (*pnTagGrpsNum)++;
        }

        CV_DEBUG(g_CVLogPLCTagDrv, "Tag \"%s\" details: address %s group %s nBitOffset %d", pOutDevTags[i].szTagName, pOutDevTags[i].szAddress, pOutDevTags[i].szGrpName, pOutDevTags[i].nBitOffSet);
	}

    for (int i = 0; i < *pnTagGrpsNum; ++i) {
        CV_INFO(g_CVLogPLCTagDrv, "Group %s address: %s elements count: %s bytes count: %d", pTagGrps[i].szGroupName, pTagGrps[i].szAddress, pTagGrps[i].szParam1, pTagGrps[i].nElemNum);
    }
	return DRV_SUCCESS;
}
