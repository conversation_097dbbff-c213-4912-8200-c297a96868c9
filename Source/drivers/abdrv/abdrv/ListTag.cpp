#include "ListTag.h"
#include "common/CVLog.h"
#include <map>
#include <string>
#include <utility>
#include <sstream>

#define TAG_STRING_TEMPLATE "protocol=ab-eip&gateway=%s&path=%s&plc=%s&name="

extern CCVLog g_CVLogPLCTagDrv;

/* a local cache of all found UDT definitions. */
static std::map<std::string, struct tag_entry_s*> p_tagsmap;
static std::map<int, struct udt_entry_s*> p_udtsmap;
static struct udt_entry_s *udts[MAX_UDTS] = { NULL };
static uint16_t udts_to_process[MAX_UDTS] = {0};
static int last_udt = 0;
static int current_udt = 0;

char *setup_tag_string(const char *host, const char *path, const char *plc)
{
    char tag_string[TAG_STRING_SIZE+1] = {0};

    if(!host || strlen(host) == 0) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Hostname or IP address must not be zero length!");
        return NULL;
    }

    if(!path || strlen(path) == 0) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "PLC path must not be zero length!");
        return NULL;
    }

    /* build the tag string. */
    snprintf(tag_string, TAG_STRING_SIZE, TAG_STRING_TEMPLATE, host, path, plc);

    return strdup(tag_string);
}

int open_tag(char *base, char *tag_name)
{
    int32_t tag = PLCTAG_ERR_CREATE;
    char tag_string[TAG_STRING_SIZE+1] = {0,};

    /* build the tag string. */
    strncpy(tag_string, base, TAG_STRING_SIZE);

    strncat(tag_string, tag_name, TAG_STRING_SIZE);

    tag = plc_tag_create(tag_string, TIMEOUT_MS);
    CV_INFO(g_CVLogPLCTagDrv, "Open tag string \"%s\" to %d", tag_string, tag);
    if(tag < 0) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Unable to open tag string \"%s\"! Return code %s", tag_string, plc_tag_decode_error(tag));
    }

    return tag;
}


int get_tag_list(int32_t tag, struct tag_entry_s **tag_list, struct tag_entry_s *parent)
{
    int rc = PLCTAG_STATUS_OK;
    uint16_t last_tag_entry_id = 0;
    int payload_size = 0;
    int offset = 0;

    /* go get it. */
    rc = plc_tag_read(tag, TIMEOUT_MS);
    if(rc != PLCTAG_STATUS_OK) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Tag %d error %s trying to send CIP request!", tag, plc_tag_decode_error(rc));
        return PLCTAG_ERR_ABORT;
    }

    /* process the raw data. */
    payload_size = plc_tag_get_size(tag);
    if(payload_size < 0) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Error getting payload size!");
        return PLCTAG_ERR_ABORT;
    }

    /* check the payload size. */
    if(payload_size < 4) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Unexpectedly small payload size %d!", payload_size);
        return PLCTAG_ERR_ABORT;
    }

    /* process each entry */
    do {
        rc = process_tag_entry(tag, &offset, &last_tag_entry_id, tag_list, parent);
    } while(rc == PLCTAG_STATUS_OK && offset < payload_size);

    return PLCTAG_STATUS_OK;
}




int process_tag_entry(int32_t tag, int *offset, uint16_t *last_tag_id, struct tag_entry_s **tag_list, struct tag_entry_s *parent)
{
    int rc = PLCTAG_STATUS_OK;
    uint16_t tag_type = 0;
    uint16_t element_length = 0;
    uint16_t array_dims[3] = {0,};
    int tag_name_len = 0;
    char *tag_name = NULL;
    struct tag_entry_s *tag_entry = NULL;

    /* each entry looks like this:
        uint32_t instance_id    monotonically increasing but not contiguous
        uint16_t symbol_type    type of the symbol.
        uint16_t element_length length of one array element in bytes.
        uint32_t array_dims[3]  array dimensions.
        uint16_t string_len     string length count.
        uint8_t string_data[]   string bytes (string_len of them)
    */

    *last_tag_id = (uint16_t)plc_tag_get_uint32(tag, *offset);
    *offset += 4;

    tag_type = plc_tag_get_uint16(tag, *offset);
    *offset += 2;

    element_length = plc_tag_get_uint16(tag, *offset);
    *offset += 2;

    array_dims[0] = (uint16_t)plc_tag_get_uint32(tag, *offset);
    *offset += 4;
    array_dims[1] = (uint16_t)plc_tag_get_uint32(tag, *offset);
    *offset += 4;
    array_dims[2] = (uint16_t)plc_tag_get_uint32(tag, *offset);
    *offset += 4;

    /* get the tag name length. */
    tag_name_len = plc_tag_get_string_length(tag, *offset);
    // *offset += 2;

    /* allocate space for the the tag name.  Add one for the zero termination. */
    tag_name = (char *)calloc((size_t)(unsigned int)(tag_name_len + 1), 1);
    if(!tag_name) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Unable to allocate memory for the tag name!");
        return PLCTAG_ERR_NO_MEM;
    }

    rc = plc_tag_get_string(tag, *offset, tag_name, tag_name_len + 1);
    if(rc != PLCTAG_STATUS_OK) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Tag %d unable to get tag name string, error %s!", tag, plc_tag_decode_error(rc));
        free(tag_name);
        return rc;
    }

    /* skip past the string. */
    (*offset) += plc_tag_get_string_total_length(tag, *offset);

    /* allocate the new tag entry. */
    tag_entry = (tag_entry_s *)calloc(1, sizeof(*tag_entry));

    if(!tag_entry) {
        CV_ERROR(g_CVLogPLCTagDrv, -1,  "Unable to allocate memory for tag entry!");
        return PLCTAG_ERR_NO_MEM;
    }

    /* fill in the fields. */
    tag_entry->name = tag_name;
    tag_entry->parent = parent;
    tag_entry->type = tag_type;
    tag_entry->elem_size = element_length;
    tag_entry->num_dimensions = (uint16_t)((tag_type & TAG_DIM_MASK) >> 13);
    tag_entry->dimensions[0] = array_dims[0];
    tag_entry->dimensions[1] = array_dims[1];
    tag_entry->dimensions[2] = array_dims[2];

    /* calculate the element count. */
    tag_entry->elem_count = 1;
    for(uint16_t i=0; i < tag_entry->num_dimensions; i++) {
        tag_entry->elem_count = (uint16_t)((uint16_t)tag_entry->elem_count * (uint16_t)(tag_entry->dimensions[i]));
    }

    /* link it up to the list */
    tag_entry->next = *tag_list;
    *tag_list = tag_entry;

    p_tagsmap.insert(std::make_pair(tag_entry->name, tag_entry));

    return PLCTAG_STATUS_OK;
}



int type_to_size(uint16_t element_type)
{
    int size = 0;
    if(element_type & TYPE_IS_SYSTEM) {
        return -1;
    } else if(element_type & TYPE_IS_STRUCT) {
        return -1;
    } else {
        uint16_t atomic_type = element_type & 0xFF; /* MAGIC */
        const char *type = NULL;
        switch(atomic_type) {
            case 0xC1: size = -1; type = "BOOL: Boolean value"; break;
            case 0xC2: size = 1; type = "SINT: Signed 8-bit integer value"; break;
            case 0xC3: size = 2; type = "INT: Signed 16-bit integer value"; break;
            case 0xC4: size = 4; type = "DINT: Signed 32-bit integer value"; break;
            case 0xC5: size = 8; type = "LINT: Signed 64-bit integer value"; break;
            case 0xC6: size = 1; type = "USINT: Unsigned 8-bit integer value"; break;
            case 0xC7: size = 2; type = "UINT: Unsigned 16-bit integer value"; break;
            case 0xC8: size = 4; type = "UDINT: Unsigned 32-bit integer value"; break;
            case 0xC9: size = 8; type = "ULINT: Unsigned 64-bit integer value"; break;
            case 0xCA: size = 4; type = "REAL: 32-bit floating point value, IEEE format"; break;
            case 0xCB: size = 8; type = "LREAL: 64-bit floating point value, IEEE format"; break;
            case 0xCC: size = -1; type = "Synchronous time value"; break;
            case 0xCD: size = -1; type = "Date value"; break;
            case 0xCE: size = -1; type = "Time of day value"; break;
            case 0xCF: size = -1; type = "Date and time of day value"; break;
            case 0xD0: size = -1; type = "Character string, 1 byte per character"; break;
            case 0xD1: size = 1; type = "8-bit bit string"; break;
            case 0xD2: size = 2; type = "16-bit bit string"; break;
            case 0xD3: size = 4; type = "32-bit bit string"; break;
            case 0xD4: size = 8; type = "64-bit bit string"; break;
            case 0xD5: size = -1; type = "Wide char character string, 2 bytes per character"; break;
            case 0xD6: size = -1; type = "High resolution duration value"; break;
            case 0xD7: size = -1; type = "Medium resolution duration value"; break;
            case 0xD8: size = -1; type = "Low resolution duration value"; break;
            case 0xD9: size = -1; type = "N-byte per char character string"; break;
            case 0xDA: size = -1; type = "Counted character sting with 1 byte per character and 1 byte length indicator"; break;
            case 0xDB: size = -1; type = "Duration in milliseconds"; break;
            case 0xDC: size = -1; type = "CIP path segment(s)"; break;
            case 0xDD: size = -1; type = "Engineering units"; break;
            case 0xDE: size = -1; type = "International character string (encoding?)"; break;
            default: size = -1; break;
        }
    }
    return size;
}

char *get_element_type(uint16_t element_type)
{
    char elemstr[200+1] = { 0 };
    if(element_type & TYPE_IS_SYSTEM) {
        snprintf(elemstr, 200, "element type SYSTEM %04x", (unsigned int)(element_type));
    } else if(element_type & TYPE_IS_STRUCT) {
        snprintf(elemstr, 200, "element type UDT (0x%04x) %s", (unsigned int)(element_type), udts[(size_t)(unsigned int)(element_type & TYPE_UDT_ID_MASK)]->name);
    } else {
        uint16_t atomic_type = element_type & 0xFF; /* MAGIC */
        const char *type = NULL;

        switch(atomic_type) {
            case 0xC1: type = "BOOL: Boolean value"; break;
            case 0xC2: type = "SINT: Signed 8-bit integer value"; break;
            case 0xC3: type = "INT: Signed 16-bit integer value"; break;
            case 0xC4: type = "DINT: Signed 32-bit integer value"; break;
            case 0xC5: type = "LINT: Signed 64-bit integer value"; break;
            case 0xC6: type = "USINT: Unsigned 8-bit integer value"; break;
            case 0xC7: type = "UINT: Unsigned 16-bit integer value"; break;
            case 0xC8: type = "UDINT: Unsigned 32-bit integer value"; break;
            case 0xC9: type = "ULINT: Unsigned 64-bit integer value"; break;
            case 0xCA: type = "REAL: 32-bit floating point value, IEEE format"; break;
            case 0xCB: type = "LREAL: 64-bit floating point value, IEEE format"; break;
            case 0xCC: type = "Synchronous time value"; break;
            case 0xCD: type = "Date value"; break;
            case 0xCE: type = "Time of day value"; break;
            case 0xCF: type = "Date and time of day value"; break;
            case 0xD0: type = "Character string, 1 byte per character"; break;
            case 0xD1: type = "8-bit bit string"; break;
            case 0xD2: type = "16-bit bit string"; break;
            case 0xD3: type = "32-bit bit string"; break;
            case 0xD4: type = "64-bit bit string"; break;
            case 0xD5: type = "Wide char character string, 2 bytes per character"; break;
            case 0xD6: type = "High resolution duration value"; break;
            case 0xD7: type = "Medium resolution duration value"; break;
            case 0xD8: type = "Low resolution duration value"; break;
            case 0xD9: type = "N-byte per char character string"; break;
            case 0xDA: type = "Counted character sting with 1 byte per character and 1 byte length indicator"; break;
            case 0xDB: type = "Duration in milliseconds"; break;
            case 0xDC: type = "CIP path segment(s)"; break;
            case 0xDD: type = "Engineering units"; break;
            case 0xDE: type = "International character string (encoding?)"; break;
        }

        if(type) {
            snprintf(elemstr, 200, "(%04x) %s", (unsigned int)element_type, type);
        } else {
            snprintf(elemstr, 200, "UNKNOWN TYPE %04x", (unsigned int)element_type);
        }
    }
    return strdup(elemstr);
}


int get_udt_definition(char *tag_string_base, uint16_t udt_id)
{
    int rc = PLCTAG_STATUS_OK;
    int32_t udt_info_tag = 0;
    int tag_size = 0;
    char buf[32] = {0};
    int offset = 0;
    uint16_t template_id = 0;
    uint16_t num_members = 0;
    uint16_t struct_handle = 0;
    uint32_t udt_instance_size = 0;
    //uint32_t member_desc_size = 0;
    int name_len = 0;
    char *name_str = NULL;
    int name_index = 0;
    int field_index = 0;

    /* memoize, check to see if we have this type already. */
    if(udts[udt_id]) {
        return PLCTAG_STATUS_OK;
    }

    snprintf(buf, sizeof(buf), "@udt/%u", (unsigned int)udt_id);

    udt_info_tag = open_tag(tag_string_base, buf);
    if(udt_info_tag < 0) {
        if(udt_info_tag == PLCTAG_ERR_UNSUPPORTED) {
            CV_ERROR(g_CVLogPLCTagDrv, -1, "This PLC type does not support listing UDT definitions.");
            return PLCTAG_ERR_UNSUPPORTED;
        }

        CV_ERROR(g_CVLogPLCTagDrv, -1, "Unable to open UDT info tag, error %s!", plc_tag_decode_error(udt_info_tag));
        return PLCTAG_ERR_ABORT;
    }

    rc = plc_tag_read(udt_info_tag, TIMEOUT_MS);
    if(rc == PLCTAG_ERR_UNSUPPORTED) {
        plc_tag_destroy(udt_info_tag);
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Tag %d UDT tag introspection is not supported on this PLC.", udt_info_tag);
        return rc;
    } else if(rc != PLCTAG_STATUS_OK) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Tag %d error %s while trying to read UDT info!", udt_info_tag, plc_tag_decode_error(rc));
        return PLCTAG_ERR_ABORT;
    }

    tag_size = plc_tag_get_size(udt_info_tag);

    /* the format in the tag buffer is:
     *
     * A new header of 14 bytes:
     *
     * Bytes   Meaning
     * 0-1     16-bit UDT ID
     * 2-5     32-bit UDT member description size, in 32-bit words.
     * 6-9     32-bit UDT instance size, in bytes.
     * 10-11   16-bit UDT number of members (fields).
     * 12-13   16-bit UDT handle/type.
     *
     * Then the raw field info.
     *
     * N x field info entries
     *     uint16_t field_metadata - array element count or bit field number
     *     uint16_t field_type
     *     uint32_t field_offset
     *
     * int8_t string - zero-terminated string, UDT name, but name stops at first semicolon!
     *
     * N x field names
     *     int8_t string - zero-terminated.
     *
     */

    /* get the ID, number of members and the instance size. */
    template_id = plc_tag_get_uint16(udt_info_tag, 0);
    //member_desc_size = plc_tag_get_uint32(udt_info_tag, 2);
    udt_instance_size = plc_tag_get_uint32(udt_info_tag, 6);
    num_members = plc_tag_get_uint16(udt_info_tag, 10);
    struct_handle = plc_tag_get_uint16(udt_info_tag, 12);

    /* skip past this header. */
    offset = 14;

    /* just a sanity check */
    if(template_id != udt_id) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "The ID, %x, of the UDT we are reading is not the same as the UDT ID we requested, %x!",(unsigned int)template_id, (unsigned int)udt_id);
        return PLCTAG_ERR_ABORT;
    }

    /* allocate a UDT struct with this info. */
    udts[(size_t)udt_id] = (udt_entry_s *)calloc(1, sizeof(struct udt_entry_s) + (sizeof(struct udt_field_entry_s) * num_members));
    if(!udts[(size_t)udt_id]) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Unable to allocate a new UDT definition structure!");
        return PLCTAG_ERR_NO_MEM;
    }

    udts[(size_t)udt_id]->id = udt_id;
    udts[(size_t)udt_id]->num_fields = num_members;
    udts[(size_t)udt_id]->struct_handle = struct_handle;
    udts[(size_t)udt_id]->instance_size = udt_instance_size;

    /* first section is the field type and size info for all fields. */
    for(int field_index=0; field_index < udts[udt_id]->num_fields; field_index++) {
        uint16_t field_metadata = 0;
        uint16_t field_element_type = 0;
        uint32_t field_offset = 0;

        field_metadata = plc_tag_get_uint16(udt_info_tag, offset);
        offset += 2;

        field_element_type = plc_tag_get_uint16(udt_info_tag, offset);
        offset += 2;

        field_offset = plc_tag_get_uint32(udt_info_tag, offset);
        offset += 4;

        udts[udt_id]->fields[field_index].metadata = field_metadata;
        udts[udt_id]->fields[field_index].type = field_element_type;
        udts[udt_id]->fields[field_index].offset = field_offset;

        /* make sure that we have or will get any UDT field types */
        if((field_element_type & TYPE_IS_STRUCT) && !(field_element_type & TYPE_IS_SYSTEM)) {
            uint16_t child_udt = (field_element_type & TYPE_UDT_ID_MASK);

            if(!udts[child_udt]) {
                udts_to_process[last_udt] = child_udt;
                last_udt++;
            }
        }
    }

    /*
     * then get the template/UDT name.   This is weird.
     * Scan until we see a 0x3B, semicolon, byte.   That is the end of the
     * template name.   Actually we should look for ";n" but the semicolon
     * seems to be enough for now.
     */

    /* first get the zero-terminated string length */
    name_len = plc_tag_get_string_length(udt_info_tag, offset);
    if(name_len <=0 || name_len >= 256) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Unexpected raw UDT name length: %d!", name_len);
    }

    /* create a string for this. */
    name_str = (char *)calloc((size_t)(name_len + 1), (size_t)1);
    if(!name_str) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Unable to allocate UDT name string!");
        return PLCTAG_ERR_NO_MEM;
    }

    /* copy the name */
    rc = plc_tag_get_string(udt_info_tag, offset, name_str, name_len + 1);
    if(rc != PLCTAG_STATUS_OK) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Tag %d error %s retrieving UDT name string from the tag!", udt_info_tag, plc_tag_decode_error(rc));
        free(name_str);
        return PLCTAG_ERR_ABORT;
    }

    /* zero terminate the name when we hit the first semicolon. */
    for(name_index = 0; name_index < name_len && name_str[name_index] != ';'; name_index++) { };

    if(name_str[name_index] == ';') {
        name_str[name_index] = 0;
    }

    /* check the name length again. */
    name_len = (int)(unsigned int)strlen(name_str);
    if(name_len ==0 || name_len >= 256) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Unexpected UDT name length: %d!", name_len);
    }

    udts[udt_id]->name = name_str;

    /* skip past the UDT name. */
    offset += plc_tag_get_string_total_length(udt_info_tag, offset);

    /*
     * This is the second section of the data, the field names.   They appear
     * to be zero terminated.
     */

    /* loop over all fields and get name strings.  They are zero terminated. */
    for(field_index=0; field_index < udts[udt_id]->num_fields && offset < tag_size; field_index++) {
        /* first get the zero-terminated string length */
        name_len = plc_tag_get_string_length(udt_info_tag, offset);
        if(name_len <0 || name_len >= 256) {
            plc_tag_destroy(udt_info_tag);
            CV_ERROR(g_CVLogPLCTagDrv, -1, "Unexpected UDT field name length: %d!", name_len);
            return PLCTAG_ERR_ABORT;
        }

        /* create a string for this. */
        if(name_len > 0) {
            name_str = (char *)calloc((size_t)(name_len + 1), (size_t)1);
            if(!name_str) {
                plc_tag_destroy(udt_info_tag);
                CV_ERROR(g_CVLogPLCTagDrv, -1, "Unable to allocate UDT field name string!");
                return PLCTAG_ERR_NO_MEM;
            }

            /* copy the name */
            rc = plc_tag_get_string(udt_info_tag, offset, name_str, name_len + 1);
            if(rc != PLCTAG_STATUS_OK) {
                plc_tag_destroy(udt_info_tag);
                CV_ERROR(g_CVLogPLCTagDrv, -1, "Tag %d error %s retrieving UDT field name string from the tag!", udt_info_tag, plc_tag_decode_error(rc));
                free(name_str);
                return PLCTAG_ERR_ABORT;
            }

            udts[udt_id]->fields[field_index].name = name_str;

            offset += plc_tag_get_string_total_length(udt_info_tag, offset);
        } else {
            /* field name was zero length. */
            udts[udt_id]->fields[field_index].name = NULL;

            /*
             * The string is either zero length in which case we need to bump past the null
             * terminator or it is at the end of the tag and we need to step past the edge.
             */
            offset++;
        }
    }

    /* sanity check */
    if(offset != tag_size) {
        CV_INFO(g_CVLogPLCTagDrv, "UDT %x processed %d bytes out of %d bytes.", udt_id, offset, tag_size);
    }

    /* if we had a system tag, we might not have the full set of member/field names.  Fill in the gaps. */
    for(; field_index < udts[udt_id]->num_fields; field_index++) {
        /* field name was zero length. */
        udts[udt_id]->fields[field_index].name = NULL;
        CV_WARN(g_CVLogPLCTagDrv, -1, "UDT field %d is not named.", field_index);
    }

    p_udtsmap.insert(std::make_pair(udt_id, udts[udt_id]));

    plc_tag_destroy(udt_info_tag);

    return PLCTAG_STATUS_OK;
}

int get_all_tags(const char *host, const char *path, const char *plc, std::map<std::string, struct tag_entry_s*> &tagsmap, std::map<int, struct udt_entry_s*> &udtsmap)
{
    int rc = PLCTAG_STATUS_OK;
    char *tag_string_base = NULL;
    int32_t controller_listing_tag = 0;
    int32_t program_listing_tag = 0;
    int version_major = plc_tag_get_int_attribute(0, "version_major", 0);
    int version_minor = plc_tag_get_int_attribute(0, "version_minor", 0);
    int version_patch = plc_tag_get_int_attribute(0, "version_patch", 0);

    /* check the library version. */
    if(plc_tag_check_lib_version(REQUIRED_VERSION) != PLCTAG_STATUS_OK) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Required compatible library version %d.%d.%d not available, found %d.%d.%d!", REQUIRED_VERSION, version_major, version_minor, version_patch);
        return PLCTAG_ERR_ABORT;
    }
    
    struct tag_entry_s *tag_list = NULL;
    /* clear the UDTs. */
    for(int index = 0; index < MAX_UDTS; index++) {
        udts[index] = NULL;
    }
    p_tagsmap.clear();
    p_udtsmap.clear();

    tag_string_base = setup_tag_string(host, path, plc);
    if(tag_string_base == NULL) {
        CV_ERROR(g_CVLogPLCTagDrv, -1,"Unable to set up the tag string base!");
        return PLCTAG_ERR_ABORT;
    }
    CV_INFO(g_CVLogPLCTagDrv, "tag string base is %s", tag_string_base);

    /* set up the tag for the listing first. */
    controller_listing_tag = open_tag(tag_string_base, "@tags");
    if(controller_listing_tag <= 0) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Unable to create listing tag, error %s!", plc_tag_decode_error(controller_listing_tag));
        return PLCTAG_ERR_ABORT;
    }

    /* get the list of controller tags. */
    rc = get_tag_list(controller_listing_tag, &tag_list, NULL);
    if(rc != PLCTAG_STATUS_OK) {
        CV_ERROR(g_CVLogPLCTagDrv, -1, "Tag %d unable to get tag list or no tags visible in the target PLC, error %s!", controller_listing_tag, plc_tag_decode_error(rc));
        return PLCTAG_ERR_ABORT;
    }

    /*
     * now loop through the tags and get the list for the program tags.
     *
     * This is safe because we push the new tags on the front of the list and
     * so do not change any existing tag in the list.
     */
    for(struct tag_entry_s *entry = tag_list; entry; entry = entry->next) {
        if(strncmp(entry->name, "Program:", strlen("Program:")) == 0) {
            char buf[256] = {0};
            snprintf(buf, sizeof(buf), "%s.@tags", entry->name);
            program_listing_tag = open_tag(tag_string_base, buf);
            // Ignore errors for Program tags
            if(program_listing_tag <= 0) {
                CV_ERROR(g_CVLogPLCTagDrv, -1, "Unable to create listing tag \"%s\", error %s!", buf, plc_tag_decode_error(program_listing_tag));
            }

            rc = get_tag_list(program_listing_tag, &tag_list, entry);
            if(rc != PLCTAG_STATUS_OK) {
                CV_ERROR(g_CVLogPLCTagDrv, -1, "Unable to get program tag list or no tags visible in the target PLC for tag %d \"%s\", error %s!", program_listing_tag, buf, plc_tag_decode_error(rc));
            }

            plc_tag_destroy(program_listing_tag);
        }
    }

    /* loop through the tags and get the UDT information. */
    for(struct tag_entry_s *entry = tag_list; entry; entry = entry->next) {
        /* check the type of the tag's element type. */
        uint16_t element_type = entry->type;

        /* if this is a structure, make sure we have the definition. */
        if((element_type & TYPE_IS_STRUCT) && !(element_type & TYPE_IS_SYSTEM)) {
            uint16_t udt_id = element_type & TYPE_UDT_ID_MASK;

            udts_to_process[last_udt] = udt_id;
            last_udt++;

            if(last_udt >= MAX_UDTS) {
                plc_tag_destroy(controller_listing_tag);
                CV_ERROR(g_CVLogPLCTagDrv, -1, "More than %d UDTs are requested!", MAX_UDTS);
                return PLCTAG_ERR_ABORT;
            }
        }
    }

    /* get all the UDTs that we have touched. Note that this can add UDTs to the stack to process! */
    while(current_udt < last_udt) {
        uint16_t udt_id = udts_to_process[current_udt];

        /* see if we already have it. */
        if(udts[udt_id] == NULL) {
            rc = get_udt_definition(tag_string_base, udt_id);
            if(rc == PLCTAG_ERR_UNSUPPORTED) {
                CV_ERROR(g_CVLogPLCTagDrv, -1, "This kind of PLC does not support UDT introspection.");
                break;
            } else if(rc != PLCTAG_STATUS_OK) {
                plc_tag_destroy(controller_listing_tag);
                CV_ERROR(g_CVLogPLCTagDrv, -1, "Unable to get UDT template ID %u, error %s!", (unsigned int)(udt_id), plc_tag_decode_error(rc));
                return PLCTAG_ERR_ABORT;
            }
        } else {
            CV_DEBUG(g_CVLogPLCTagDrv,  "Already have UDT (%04x) %s.", (unsigned int)udt_id, udts[udt_id]->name);
        }

        current_udt++;
    }

    tagsmap = p_tagsmap;
    udtsmap = p_udtsmap;

    free(tag_string_base);

    /* Destroy this at the end to keep the session open. */
    plc_tag_destroy(controller_listing_tag);
    return PLCTAG_STATUS_OK;
}

int free_all_tags(std::map<std::string, struct tag_entry_s*> &tagsmap, std::map<int, struct udt_entry_s*> &udtsmap) {
    std::map<std::string, struct tag_entry_s*>::iterator tagit;
    for (tagit = tagsmap.begin(); tagit != tagsmap.end(); ++tagit) {
        free(tagit->second->name);
        tagit->second->name = NULL;
        free(tagit->second);
    }
    std::map<int, struct udt_entry_s*>::iterator udtit;
    for (udtit = udtsmap.begin(); udtit != udtsmap.end(); ++udtit) {
        free(udtit->second->name);
        udtit->second->name = NULL;
        for (int i = 0; i < udtit->second->num_fields; ++i) {
            struct udt_field_entry_s *field = &(udtit->second->fields[i]);
            if (field->name) {
                free(field->name);
            }
        }
        free(udtit->second);
    }
    return 0;
}

void print_all_tags(const std::string &device, std::map<std::string, struct tag_entry_s*>& tagsmap, std::map<int, struct udt_entry_s*>& udtsmap) {
    std::map<std::string, struct tag_entry_s*>::iterator tagit;
    for (tagit = tagsmap.begin(); tagit != tagsmap.end(); ++tagit) {
        std::ostringstream outstr;
        struct tag_entry_s* tag = tagit->second;
        outstr << "Device \"" << device << "\" ";
        if (!tag->parent) {
            outstr << "Tag \"" << tag->name;
        }
        else {
            outstr << "Tag \"" << tag->parent->name << "." << tag->name;
        }
        switch (tag->num_dimensions) {
        case 1:
            outstr << "[" << tag->dimensions[0] << "]";
            break;
        case 2:
            outstr << "[" << tag->dimensions[0] << "," << tag->dimensions[1] << "]";
            break;
        case 3:
            outstr << "[" << tag->dimensions[0] << "," << tag->dimensions[1] << "," << tag->dimensions[2] << "]";
            break;
        default:    break;
        }
        outstr << "\" ";
        char* elemstr = get_element_type(tag->type);
        outstr << elemstr;
        free(elemstr);
        outstr << " elem_size=" << tag->elem_size << " elem_count=" << tag->elem_count;
        CV_INFO(g_CVLogPLCTagDrv, "%s", outstr.str().c_str());
    }

    std::map<int, struct udt_entry_s*>::iterator udtit;
    for (udtit = udtsmap.begin(); udtit != udtsmap.end(); ++udtit) {
        struct udt_entry_s* udt = udtit->second;
        if (udt->name) {
            CV_INFO(g_CVLogPLCTagDrv, "Device \"%s\" UDT %s (ID %x, %d bytes, struct handle %x):", device.c_str(), udt->name, (unsigned int)(udt->id), (int)(unsigned int)udt->instance_size, (int)(unsigned int)udt->struct_handle);
        } else {
            CV_INFO(g_CVLogPLCTagDrv, "Device \"%s\" UDT *UNNAMED* (ID %x, %d bytes, struct handle %x):", device.c_str(), (unsigned int)(udt->id), (int)(unsigned int)udt->instance_size, (int)(unsigned int)udt->struct_handle);
        }
        for (int field_index = 0; field_index < udt->num_fields; field_index++) {
            std::ostringstream outstr;
            if (udt->fields[field_index].name) {
                outstr << "    Field " << field_index << ": " << udt->fields[field_index].name << ", offset " << udt->fields[field_index].offset;
            } else {
                outstr << "    Field " << field_index << ": " << "*UNNAMED*" << ", offset " << udt->fields[field_index].offset;
            }

            /* is it a bit? */
            if (udt->fields[field_index].type == 0xC1) {
                /* bit type, the metadata is the bit number. */
                outstr << ":" << (int)(unsigned int)(udt->fields[field_index].metadata);
            }

            /* is it an array? */
            if (udt->fields[field_index].type & 0x2000) { /* MAGIC */
                outstr << ", array [" << (int)(unsigned int)(udt->fields[field_index].metadata) << "] of type ";
            }
            else {
                outstr << ", type ";
            }

            char *elemstr = get_element_type(udt->fields[field_index].type);
            outstr << elemstr;
            free(elemstr);
            CV_INFO(g_CVLogPLCTagDrv, "%s", outstr.str().c_str());
        }
    }
}
