#pragma once

#include <stdbool.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <map>
#include <string>
#include "plctag/libplctag.h"

#define REQUIRED_VERSION 2,4,0
#define TAG_STRING_SIZE (200)
#define TIMEOUT_MS 5000

#define TYPE_IS_STRUCT ((uint16_t)0x8000)
#define TYPE_IS_SYSTEM ((uint16_t)0x1000)
#define TYPE_DIM_MASK ((uint16_t)0x6000)
#define TYPE_UDT_ID_MASK ((uint16_t)0x0FFF)
#define TAG_DIM_MASK ((uint16_t)0x6000)

#define MAX_UDTS (1 << 20)

struct program_entry_s {
    struct program_entry_s *next;
    char *program_name;
};

struct tag_entry_s {
    struct tag_entry_s *next;
    char *name;
    struct tag_entry_s *parent;
    uint16_t type;
    uint16_t elem_size;
    uint16_t elem_count;
    uint16_t num_dimensions;
    uint16_t dimensions[3];
};


struct udt_field_entry_s {
    char *name;
    uint16_t type;
    uint16_t metadata;
    uint32_t offset;
};


struct udt_entry_s {
    char *name;
    uint16_t id;
    uint16_t num_fields;
    uint16_t struct_handle;
    uint32_t instance_size;
    struct udt_field_entry_s fields[];
};

static char *setup_tag_string(const char *host, const char *path, const char *plc);
static int open_tag(char *base, char *tag_name);
static int get_tag_list(int32_t tag_id, struct tag_entry_s **tag_list, struct tag_entry_s *parent);
int type_to_size(uint16_t element_type);
static int process_tag_entry(int32_t tag, int *offset, uint16_t *last_tag_id, struct tag_entry_s **tag_list, struct tag_entry_s *parent);
static int get_udt_definition(char *base, uint16_t udt_id);
int get_all_tags(const char* host, const char* path, const char* plc, std::map<std::string, struct tag_entry_s*>& tagsmap, std::map<int, struct udt_entry_s*>& udtsmap);
int free_all_tags(std::map<std::string, struct tag_entry_s*>& tagsmap, std::map<int, struct udt_entry_s*>& udtsmap);
void print_all_tags(const std::string& device, std::map<std::string, struct tag_entry_s*>& tagsmap, std::map<int, struct udt_entry_s*>& udtsmap);