cmake_minimum_required(VERSION 3.10)

PROJECT (abdrv)

INCLUDE($ENV{DRDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
#Setting Source Files
SET(SRCS ${SRCS} ListTag.cpp PlcTag.cpp)

INCLUDE_DIRECTORIES(../../../../include/plctag)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME abdrv)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS plctag drdrivercommon ACE)


SET(SPECOUTDIR /drivers/abdrv)
INCLUDE($ENV{DRDIR}CMakeSpecOutPath)
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)

