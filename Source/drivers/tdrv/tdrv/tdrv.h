#ifndef _TDRV_DRIVER_H_
#define _TDRV_DRIVER_H_

#include <string>
#include <fstream>
#include <math.h>
#include "driversdk/cvdrivercommon.h"
#include "common/RMAPIDef.h"
#include "errcode/error_code.h"
#include "os/os_time.h"
#include "ace/Default_Constants.h"
#include "tDevice.h"
using namespace std;
using namespace BaoSky::PF::External;
#define ERROR_HMISUBSCRIBE_TOOMANY -99999   // 错误返回值，向plc订阅的点太多了
#define TDRV_DEFAULT_DEVICE_LOOPTIME	500	// 默认device循环时间 1000ms

void SubscibeCallback(string const& topicId, const vector<AttributeInfo>& pushData);
void ConnectCB(const string& sessionId, string connectState);
void SubChangeCB(const std::string& sessionId, const std::vector<std::string>& topicArray, eSubChangeState plcState);
#endif //_TDRV_DRIVER_H_ 