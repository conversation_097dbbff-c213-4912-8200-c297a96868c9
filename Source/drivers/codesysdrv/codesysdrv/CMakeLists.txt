cmake_minimum_required(VERSION 3.10)

PROJECT (codesysdrv)

INCLUDE($ENV{DRDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
#Setting Source Files
SET(SRCS ${SRCS} codesysdevice.cpp codesysdrv.cpp)

IF(${CMAKE_SYSTEM_NAME} MATCHES Windows)
	INCLUDE_DIRECTORIES(../../../../include/plchandler/Windows)
ENDIF(${CMAKE_SYSTEM_NAME} MATCHES Windows)

IF(CMAKE_SYSTEM MATCHES "Linux")
	INCLUDE_DIRECTORIES(../../../../include/plchandler/Linux)
ENDIF(CMAKE_SYSTEM MATCHES "Linux")

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME codesysdrv)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS drdrivercommon drcomm ACE drlog)
IF(${CMAKE_SYSTEM_NAME} MATCHES Windows)
	SET(LINK_LIBS ${LINK_LIBS} PLCHandlerDll)
ENDIF(${CMAKE_SYSTEM_NAME} MATCHES Windows)

IF(CMAKE_SYSTEM MATCHES "Linux")
	SET(LINK_LIBS ${LINK_LIBS} CmpPLCHandler)
ENDIF(CMAKE_SYSTEM MATCHES "Linux")

SET(SPECOUTDIR /drivers/codesysdrv)
INCLUDE($ENV{DRDIR}CMakeSpecOutPath)
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)

