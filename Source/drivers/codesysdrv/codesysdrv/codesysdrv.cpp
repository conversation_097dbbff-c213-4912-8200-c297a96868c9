/************************************************************************
 *	Filename:		XXXXX
 *	Copyright:		XXXXX Company Software Co., Ltd.
 *
 *	Description:	$(Desp) .
 *
 *	@author:		XXXXX
 *	@version		DATE	AUTHOR	Initial Version
*************************************************************************/
#include "driversdk/cvdrivercommon.h"
#include "errcode/error_code.h"
#include "codesysdrv.h"
#include "common/cvGlobalHelper.h"
#include "common/CommHelper.h"
#include "common/RMAPIDef.h"
#include "common/CVLog.h"
#include <sstream>

TagTypeDeviceMap g_mapDevices;
extern CCVLog g_codesysCVLog;

// bool isSingleLinkAndActiveHost(CCodesysDevice* pDev)
// {
// 	// �Ƿ�����
// 	if (!pDev->GetMultilink())
// 	{
// 		if (RM_STATUS_INACTIVE == Drv_IsActiveHost())
// 		{
// 			//todo:plchandler �Ƿ���״̬�ӿ�
// 			//PLCHANDLER_STATE EnumState = m_pPLCHandler->GetState();
// 			if (pDev->isConnected())
// 			{
// 				pDev->Disconnect();
// 				CV_INFO(g_codesysCVLog, "Device[%s] multilink = 0 ,RM_STATUS_INACTIVE!,disconnect!", pDev->GetDeviceInfo());
// 			}
// 			return false;
// 		}
// 		else
// 			return true;
// 	}
// 	else
// 		return true;
// 	
// 	return false;
// }

bool isReadyToCommWithPlc(CCodesysDevice* pDev)
{
	//������
	if (pDev->GetMultilink())
		return true;
	
	//�������ҷǻ��
	if (RM_STATUS_INACTIVE == Drv_IsActiveHost())
		return false;

	//�������һ��
	return true;
}

/**
*  ��ȡ�����汾��.
*
*  @version   07/20/2012   Initial Version.
*/
CVDRIVER_EXPORTS long GetDrvFrameVersion()
{
	return 2;
}


/**
 *  ��ʼ������������EXE����ʱ�ú��������ã����ڸú�����ʵ���Զ����ʼ������.
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     07/20/2012    Initial Version.
 */
CVDRIVER_EXPORTS long Begin()
{
	//��ʼ����־·��
	g_codesysCVLog.SetLogFileNameThread("codesysdrv");

	g_mapPLCDataType.insert(make_pair(DATATYPE_BOOL, string("BOOL")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_SINT, string("SINT")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_USINT, string("USINT")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_BYTE, string("BYTE")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_INT, string("INT")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_UINT, string("UINT")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_WORD, string("WORD")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_DINT, string("DINT")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_UDINT, string("UDINT")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_DWORD, string("DWORD")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_REAL, string("REAL")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_LREAL, string("LREAL")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_TIME, string("TIME")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_STRING, string("STRING")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_BITORBYTE, string("BITORBYTE")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_DATE, string("DATE")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_TOD, string("TOD")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_DT, string("DT")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_REF, string("REF")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_VOID, string("VOID")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_ULINT, string("ULINT")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_LTIME, string("LTIME")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_WSTRING, string("WSTRING")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_LWORD, string("LWORD")));
	g_mapPLCDataType.insert(make_pair(DATATYPE_BIT, string("BIT")));

	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_STRING, string("TXT")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_INT, string("INT16")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_UINT, string("UINT16")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_REAL, string("FLOAT")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_BOOL, string("BOOLEAN")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_INT, string("SINT16")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_TIME, string("TIME")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_ULINT, string("ULONG")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_LINT, string("SLONG")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_LREAL, string("DOUBLE")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_BLOB, string("BLOB")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_SINT, string("CHAR")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_USINT, string("UCHAR")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_LINT, string("INT64")));
	g_mapIplatDataType.insert(make_pair(TAG_DATATYPE_ULINT, string("UINT64")));

	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnBatchUpdateData(int& nMsTimeOut,int& DataSize)
{
	int timeout = 50;
	int datasize = 10000;

	nMsTimeOut = timeout;
	DataSize = datasize;
	return 0; 
}

/**
 *  ��ʼ������������EXE����ʱ�ú��������ã����ڸú�����ʵ���Զ����ʼ������.
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     07/20/2012    Initial Version.
 */
CVDRIVER_EXPORTS long Initialize()
{
	//_TODO����ʼ������

	return DRV_SUCCESS;
}

/**
 *  ����EXE�˳�ʱ�ú���������.
 *  �ڸú����п����ͷ��Զ�����Դ���Ͽ��豸���ӵȲ���.
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     07/20/2012    Initial Version.
 */
CVDRIVER_EXPORTS long UnInitialize()
{
	//_TODO���˳���������
	TagTypeDeviceMap::iterator iterDevice = g_mapDevices.begin();
	for (;iterDevice != g_mapDevices.end(); ++iterDevice)
	{
		SAFE_DELETE(iterDevice->second);
	}

	g_mapDevices.clear();
	CV_INFO(g_codesysCVLog,"driver exit...");
	g_codesysCVLog.StopLogThread();
	return DRV_SUCCESS;
}


/**
 *  ��ʱ��ȡ���ݺ���.
 *  �����豸ͨ���ṩ��ȡָ�����ݿ����ϢЭ�飬�ú�����Ҫʵ�����¹��ܣ�
 *  1�����ݵķ��ͺͽ���
 *  2���������ݿ�ʵʱ���ݵ�ֵ������״̬��ʱ���
 *  3�����ڷ�tcpͨ��Э�飬ͨ����Ҫ�ڸýӿڼ������״̬
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *  @param  -[in]  DRVHANDLE hDataBlock: [���ݿ���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnReadData(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	//_TODO��������������ṩ��Drv_UpdateBlockData��Drv_UpdateBlockStatus�������ݿ���Ϣ
	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
	if (pDevice == NULL)
		return DEVLINK_FAILED;

	TagTypeDeviceMap::iterator it = g_mapDevices.find(std::string(pDevice->pszName));
	if (it == g_mapDevices.end())
	{
		Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
		return DEVLINK_FAILED;
	}
		

	//�����ӷǻ��
	if (!isReadyToCommWithPlc(it->second))
	{
		//�Ѿ�����
		if (it->second->isConnected())
		{
			//�Ͽ�����
			//it->second->GetPLCHandler()->Disconnect();
			it->second->Disconnect();
			CV_INFO(g_codesysCVLog, "Device[%s] multilink = 0 ,RM_STATUS_INACTIVE!,disconnect!", it->second->GetDeviceInfo());	
		}
		Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
		return DEVLINK_FAILED;
	}

    // INACTIVE do not need read data 
    if (RM_STATUS_INACTIVE == Drv_IsActiveHost())
    {
        return DEVLINK_FAILED;
    }

    //�����ӻ��δ��������������
	if (!it->second->isConnected() && !it->second->GetMultilink())
	{
		long lRet = it->second->Connect(pDevice);
		if(lRet != RESULT_OK)
			Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
		else
			Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
	}

	if (it->second->isReconnect())
	{
	    long lRet = it->second->Connect(pDevice);
		if(lRet != RESULT_OK)
			Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
		else
			Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
	}

	return it->second->ReadTags(hDevice);

}

/**
 *  ���п�������ʱ�ú���������.
 *  �ڸú����и��ݴ��ݵĲ��������豸�·��������
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *  @param	-[in]  DRVHANDLE hDataBlock : [���ݿ���]
 *  @param	-[in]  int nTagDataType : tag������
 *  @param  -[in]  int nTagByteOffset: [tag���ڿ��е��ֽ�ƫ����]
 *  @param  -[in]  int nTagBitOffset: [�ֽ��ڵ�λƫ����]
 *  @param	-[in]  char *szCmdData : [����ָ��]
 *  @param  -[in]  int nCmdDataLenBits: [�������ݳ���,��λ��bit]
 *
  *  @return : ����ִ�з������
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDataBlock, int nTagByteOffset, int nTagBitOffset, char *szCmdData, int nCmdDataLenBits)
{
	//_TODO������szShortAddr,��ȡ��Ӧ�����ݿ���Ϣ

	//_TODO�����ɿ���ָ����Ϳ�����Ϣ

	//_TODO���������Ʒ������
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	if (pDevice == NULL)
		return DEVLINK_FAILED;

	TagTypeDeviceMap::iterator it = g_mapDevices.find(std::string(pDevice->pszName));
	if(it == g_mapDevices.end())
		return DEVLINK_FAILED;

	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
	// // Remove the first two characters of the string uniformly in the driver framework
    // string strDataType = pDataBlock->pszBlockType;
    // if (TAG_DATATYPE_STRING == stoi(strDataType))
    // {
	// 	szCmdData+=2;
	// 	nCmdDataLenBits-=16;
    // }
	// CV_INFO(g_codesysCVLog, "pDataBlock pszBlockType: %s, nCmdDataLenBits: %d", pDataBlock->pszBlockType,
	// 	nCmdDataLenBits);
    return it->second->OnWriteCmd(pDataBlock->pszAddress, szCmdData, nCmdDataLenBits);
}

/**
 *  �����豸ʱ�ú���������.
 *  �ú�����Ҫ��Է�tcp�����豸���û�����ͨ���豸�����ȡ�豸���Ӳ�������ʼ�������豸
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
	//_TODO�����ڷ�tcp�豸�����豸���ӵȲ���
	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);

	CCodesysDevice* pCodesysDevice = new CCodesysDevice(hDevice);
	g_mapDevices[pDevice->pszName] = pCodesysDevice;

	TagTypeDeviceMap::iterator iterDevice = g_mapDevices.find(pDevice->pszName);
	int nRet = 0;
	if (iterDevice != g_mapDevices.end())
	{
		nRet = iterDevice->second->Connect(pDevice);
	}
	return DRV_SUCCESS;
}

/**
 *  ɾ���豸ʱ�ú���������.
 *  �ú�����Ҫ��Է�tcp�����豸���û�����ͨ���豸�����ȡ�豸��Ϣ�������Ͽ��豸���ͷ������Դ�Ȳ���
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
	//_TODO�����ڷ�tcp�豸�����豸�Ͽ��Ȳ���
	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
	TagTypeDeviceMap::iterator iterDevice = g_mapDevices.find(pDevice->pszName);
	if (iterDevice != g_mapDevices.end())
	{
		iterDevice->second->Disconnect();
		Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
		g_mapDevices.erase(iterDevice);
	}
	return DRV_SUCCESS;
}


/**
 *  �������ݿ�ʱ�ú���������.
 *  �û���Ҫ�ڸú����з������ݿ��С
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *  @param  -[in]  DRVHANDLE hDataBlock: [���ݿ���]
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	//_TODO��ִ�����ݿ�����Զ������
	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);

	CVDATABLOCK* pDB = Drv_GetDataBlockInfo(hDataBlock);
	int nTypeCV;
	std::stringstream(std::string(pDB->pszBlockType))>>nTypeCV;
	//g_mapDevices[pDevice->pszName]->AddTag(pDB->pszAddress, nTypeCV, pDB->pszAddress, pDB->nElemNum);
	g_mapDevices[pDevice->pszName]->AddTag(pDB->pszAddress, nTypeCV, pDB->pszName, pDB->nElemNum);

	return DRV_SUCCESS;
}

/**
 *  ɾ�����ݿ�ʱ�ú���������.
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *  @param  -[in]  DRVHANDLE hDataBlock: [���ݿ���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	//_TODO��ִ�����ݿ������������

	Drv_UpdateBlockStatus(hDevice, hDataBlock, DATA_STATUS_DEVICE_FAILURE);

	return DRV_SUCCESS;
}


const char * GetPureAddress(const char *pAddr)
{
	static char szAddress[ICV_IOADDR_MAXLEN + 1];
	memset(szAddress, 0, sizeof(szAddress));
	const char *pTemp = strstr(pAddr, ":");
	if (pTemp != NULL)
	{
		const char *pTemp1 = strstr(pTemp + 1, "#");
		//if (NULL == pTemp1)
			//pTemp1 = strstr(pTemp + 1, "#");

		if (pTemp1 != NULL)
			memcpy(szAddress, pTemp + 1, pTemp1 - pTemp - 1);
		else
			strncpy(szAddress, pTemp + 1, ICV_IOADDR_MAXLEN);
	}
	return szAddress;
}

CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum, TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
{
	if (NULL == pOutDevTags || NULL == pnTagsNum || NULL == pTagGrps || NULL == pnTagGrpsNum)
		return -1;
	
	//���ڵ��豸��һ�������һ�����ݿ飬��˵�ĸ��������ݿ�ĸ���һ��
	*pnTagsNum = nTagsNum;
	*pnTagGrpsNum = 0;

	memset(pTagGrps, 0, *pnTagGrpsNum * sizeof(TagGroupInfo));

	int nBlockNum = 0;

	std::map<string, string> mapDataBlockAddr;//keyΪ��ַ,valueΪGroupName

	std::copy(pDevTags, pDevTags + nTagsNum, pOutDevTags);
	
	char szGroupName[ICV_DATABLOCKNAME_MAXLEN + 1];
	memset(szGroupName, 0, sizeof(szGroupName));

//	g_Devs.clear();
	for (int i = 0; i < nTagsNum; ++i)
	{
		string strAddress = GetPureAddress(pDevTags[i].szAddress);
		std::map<string, string>::iterator iter = mapDataBlockAddr.find(strAddress);
		if (iter != mapDataBlockAddr.end())
		{
			cvcommon::Safe_StrNCopy(pOutDevTags[i].szGrpName, iter->second.c_str(), ICV_DATABLOCKNAME_MAXLEN + 1);
			continue;
		}

		cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szAddress, GetPureAddress(pDevTags[i].szAddress), ICV_IOADDR_MAXLEN);

		pTagGrps[nBlockNum].nElemBits = 8;
		//icg�ǵ��豸�����Կ��ϲ�����ɨ�����ڣ����������ظ�ɨ�裬����ϵͳCPU
		pTagGrps[nBlockNum].nPollRate = 0;

// 		char buffer[ICV_IOADDR_MAXLEN+1];
// 		memcpy(buffer, pDevTags[i].szAddress, ICV_IOADDR_MAXLEN+1);
// 
// 		char *pszAddress = strstr(buffer, ":");	//�豸��Ϣ
// 		if (pszAddress == NULL) //û���豸��Ϣ
// 			continue;
// 		*pszAddress = '\0';
// 		pszAddress++;
// 
// 		//strncpy_s(pTagGrps[i].szAddress, pszAddress, ICV_IOADDR_MAXLEN);
// 		strncpy(pTagGrps[i].szAddress, GetPureAddress(pDevTags[i].szAddress), ICV_IOADDR_MAXLEN);
// 		//����groupName
// 		char groupName[ICV_DATABLOCKNAME_MAXLEN+1];
// 		//_snprintf_s(groupName, ICV_DATABLOCKNAME_MAXLEN, "%s", pTagGrps[i].szAddress);
// 		ACE_OS::snprintf(groupName, ICV_DATABLOCKNAME_MAXLEN, "%s", pTagGrps[i].szAddress);
// 
// 		//����Ƿ���tag Ϊ��ͬ���豸��ַ����ʹ��������Ǹ�groupName����
// 		TagTypeDeviceMap::iterator it = g_mapDevices.find(buffer); 
// 		if (it != g_mapDevices.end())
// 		{
// 			const char* oldGrpName = it->second->GetGroupName(pszAddress);
// 			if (oldGrpName != NULL)
// 				//strncpy_s(groupName, oldGrpName, ICV_DATABLOCKNAME_MAXLEN);
// 				CVStringHelper::Safe_StrNCpy(groupName, oldGrpName, ICV_DATABLOCKNAME_MAXLEN);
// 		}
// 
// 		//strncpy_s(pTagGrps[i].szGroupName, groupName, ICV_DATABLOCKNAME_MAXLEN);
// 		//strncpy_s(pOutDevTags[i].szGrpName, groupName, ICV_DATABLOCKNAME_MAXLEN);
// 		CVStringHelper::Safe_StrNCpy(pTagGrps[i].szGroupName, groupName, ICV_DATABLOCKNAME_MAXLEN);
// 		CVStringHelper::Safe_StrNCpy(pOutDevTags[i].szGrpName, groupName, ICV_DATABLOCKNAME_MAXLEN);
	
		sprintf(pTagGrps[nBlockNum].szGroupName, "group%d", nBlockNum);
		sprintf(pOutDevTags[i].szGrpName, "group%d", nBlockNum);
		mapDataBlockAddr.insert(std::make_pair(pTagGrps[nBlockNum].szAddress, pTagGrps[nBlockNum].szGroupName));

		sprintf(pTagGrps[nBlockNum].szGroupType, "%d", pDevTags[i].nDataType);
		switch(pDevTags[i].nDataType)
		{
		case TAG_DATATYPE_BOOL:
		case TAG_DATATYPE_SINT:
		case TAG_DATATYPE_USINT:
		case TAG_DATATYPE_BYTE:
		case TAG_DATATYPE_CHAR:
			pTagGrps[nBlockNum].nElemNum = 1;
			break;
		case TAG_DATATYPE_INT:
		case TAG_DATATYPE_UINT:
		case TAG_DATATYPE_WORD:
			pTagGrps[nBlockNum].nElemNum = 2;
			break;
		case TAG_DATATYPE_REAL:
		case TAG_DATATYPE_UDINT:
		case TAG_DATATYPE_DINT:
		case TAG_DATATYPE_DWORD:
			pTagGrps[nBlockNum].nElemNum = 4;
			break;
		case TAG_DATATYPE_LREAL:
		case TAG_DATATYPE_LINT:
		case TAG_DATATYPE_ULINT:
		case TAG_DATATYPE_LWORD:
			pTagGrps[nBlockNum].nElemNum = 8;
			break;
		case TAG_DATATYPE_BLOB:
			{
				pTagGrps[nBlockNum].nElemNum = 256;
				const char *pTemp = strstr(pDevTags[i].szAddress, "#");
				if (pTemp != NULL) pTagGrps[nBlockNum].nElemNum = atoi(pTemp + 1);
			}
			break;
		case TAG_DATATYPE_STRING:
			{
				pTagGrps[nBlockNum].nElemNum = 256;
				const char *pTemp = strstr(pDevTags[i].szAddress, "#");
				if (pTemp != NULL) pTagGrps[nBlockNum].nElemNum = atoi(pTemp + 1);
			}
			break;
		default:
			break;
		}
		nBlockNum++;
	}
	*pnTagGrpsNum = nBlockNum;
	return DRV_SUCCESS;
}