#include "codesysdevice.h"
#include "codesysdrv.h"
#include "common/DrvDef.h"
#include "common/cvGlobalHelper.h"
#include "driversdk/cvdrivercommon.h"
#include "errcode/error_code.h"
#include "plchandler/CmpPLCHandlerDep.h"
#include <mutex>
#include <stdio.h>
#if defined(__linux) || defined(__linux__) || defined(linux)
#include <stdlib.h>
#endif
#define PLCH_USE_DLL

CCVLog g_codesysCVLog;

std::map<PlcTypeClass, std::string> g_mapPLCDataType;
std::map<unsigned long, std::string> g_mapIplatDataType;
constexpr uint32 g_nReconnectTimes = 3;

extern "C"
{
    USE_STMT
}
extern TagTypeDeviceMap g_mapDevices;
extern bool isReadyToCommWithPlc(CCodesysDevice *pDev);

static int CDECL ImportSystemFunctions(void)
{
    IMPORT_STMT;
    return ERR_OK;
}

// �˷�������hdAPI�޸Ķ���
bool verify_ip(const char *szIp)
{
    if (NULL == szIp)
        return false;

    int32 nNumPoint = 0;
    int32 nIPDigit = 0;

    // exclude .xxx.xxx.xxx
    if ('.' == szIp[0])
        return false;

    int32 i = 0;
    while (szIp[i] != '\0')
    {
        if ('.' == szIp[i])
        {
            // exclude xxx..xxx.xxx
            if ('.' == szIp[i - 1])
                return false;
            nIPDigit = 0;
            nNumPoint++;
        }
        else if ('0' <= szIp[i] && '9' >= szIp[i])
        {
            nIPDigit = 10 * nIPDigit + (int32)(szIp[i] - '0');
            // exclude xxx.256.xxx.xxx
            if (nIPDigit > 255)
                return false;
        }
        else
        {
            // not digit and not '.'
            return false;
        }
        i++;
        // exclude xxx.xxx.xxx.xxxx
        if (i > 15)
            return false;
    }

    if (3 != nNumPoint || '.' == szIp[i - 1]) // point number is invalid & exclude xxx.xxx.xxx.
        return false;

    if (0 == strcmp(szIp, "0.0.0.0"))
    {
        return false;
    }

    return true;
}

CCodesysDevice::CCodesysDevice(DRVHANDLE hDevice)
{
    m_pPLCHandler = NULL;
    m_pPLCHandlerBackUp = NULL;
    m_pCurrActPLCHandler = NULL;
    m_hLogger = RTS_INVALID_HANDLE;
    m_ppszVars = NULL;
    m_iNumOfVars = 0;
    m_ulNumOfSymbols = 0;
    m_hVarList = NULL;
    m_hVarListBackUp = NULL;
    m_hVarListRed = NULL;
    m_hVarListRedBackUp = NULL;
    m_nCodesysReadType = CODESYS_READ_TYPE_CYCLE;
    m_nCycleRate = 1000;
    m_bSwitch = false;
    m_bMultilink = true;

    // Connect
    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    m_devName = pDevice->pszName;
    m_nCycleRate = pDevice->nCycleRate;

    // After testing, the sync reading method was found to be abnormal during redundant switching.
    // So it is fixed as a loop reading
    // if (strcmp(pDevice->pszParam1, "1") == 0)
    m_nCodesysReadType = CODESYS_READ_TYPE_CYCLE;
    // else
    //     m_nCodesysReadType = CODESYS_READ_TYPE_SYNC;

    m_szRedundantState = std::string("Application.PersistentVars.redu.eRedundancyState"); // 20250116 default value
    if (nullptr != pDevice->pszParam3 && strlen(pDevice->pszParam3) > 0)
    {
        m_szRedundantState = std::string(pDevice->pszParam3);
    }

    SetConnectParam(pDevice->pszConnParam);
}

CCodesysDevice::~CCodesysDevice()
{
    Disconnect();
}

long CCodesysDevice::SetConnectParam(const char *szConnParam)
{
    CV_INFO(g_codesysCVLog, "PLCHandler[%s %s multilink=%d]", m_devName.c_str(), szConnParam, m_bMultilink);
    // todo:multilink
    std::string strParam = string(szConnParam);
    transform(strParam.begin(), strParam.end(), strParam.begin(), (int (*)(int))tolower);
    strParam += ";";
    int nPos = strParam.find("multilink=");
    if (nPos != string::npos)
    {
        string strMultilink = strParam.substr(nPos + 10, strParam.length() - (nPos + 10));
        nPos = strMultilink.find(';');
        if (nPos != string::npos)
            m_bMultilink = atoi(strMultilink.substr(0, nPos).c_str());
    }

    // ip=**************,**************;ip2=**************,**************;
    // 同一设备多个ip使用逗号分隔
    m_vecMainIP.clear();
    m_vecBackupIP.clear();
    std::string strIP = string(szConnParam);
    if (!verify_ip(strIP.c_str()))
    {
        strIP += ";";
        int nPos = strIP.find("ip=");
        if (nPos != string::npos)
        {
            string strIPAddr = strIP.substr(nPos + 3, strIP.length() - (nPos + 3));
            int nPos1 = strIPAddr.find(';');
            if (nPos1 != string::npos)
            {
                std::string ipList = strIPAddr.substr(0, nPos1).c_str();
                std::stringstream ss(ipList);
                std::string ip;
                while (std::getline(ss, ip, ','))
                {
                    if (!ip.empty())
                        m_vecMainIP.push_back(ip);
                }
            }
        }

        nPos = strIP.find("ip2=");
        if (nPos != string::npos)
        {
            string strIPAddr = strIP.substr(nPos + 4, strIP.length() - (nPos + 4));
            int nPos1 = strIPAddr.find(';');
            if (nPos1 != string::npos)
                if (nPos1 != string::npos)
                {
                    std::string ipList = strIPAddr.substr(0, nPos1).c_str();
                    std::stringstream ss(ipList);
                    std::string ip;
                    while (std::getline(ss, ip, ','))
                    {
                        if (!ip.empty())
                            m_vecBackupIP.push_back(ip);
                    }
                }
        }
    }
    else
    {
        m_vecMainIP.push_back(strIP.substr(0, strIP.find_first_of(';')));
    }

    return DRV_SUCCESS;
}

long CCodesysDevice::Connect(CVDEVICE *pDevice)
{
    CV_INFO(g_codesysCVLog, "Try to connect PLCHandler[%s %s]", m_devName.c_str(), pDevice->pszConnParam);
    if (m_pPLCHandler != NULL || m_pPLCHandlerBackUp != NULL)
        Disconnect();

    // connect 之后，m_bReconnect 设置成false
    m_bReconnect.store(false);

    char szCurfilename[1024], szPathIni[1024], szPathLog[1024];
    memset(szPathLog, 0, 1024);
    memset(szCurfilename, 0, 1024);
#if defined(__linux) || defined(__linux__) || defined(linux)
    char pathbuf[128] = {0};
    readlink("/proc/self/exe", pathbuf, sizeof(pathbuf));
    CVStringHelper::Safe_StrNCpy(szCurfilename, pathbuf, sizeof(szCurfilename));
    // printf("%s\n", pathbuf);
#else
    GetModuleFileName(NULL, szCurfilename, 1024);
#endif

    // char * pch = strrchr(szCurfilename, '\\');
    char *pch = strrchr(szCurfilename, ACE_DIRECTORY_SEPARATOR_CHAR);
    *pch = '\0';

    //_snprintf(szPathIni, 1024, "%s\\%s.ini", szCurfilename, pDevice->pszName);
    //_snprintf(szPathLog, 1024, "%s\\%s.log", szCurfilename, pDevice->pszName);
    // ACE_OS::snprintf(szPathIni, 1024, "%s\\%s.ini", szCurfilename, pDevice->pszName);
    // ACE_OS::snprintf(szPathLog, 1024, "%s\\%s.log", szCurfilename, pDevice->pszName);
    ACE_OS::snprintf(szPathIni, 1024, "%s%c%s.ini", szCurfilename, ACE_DIRECTORY_SEPARATOR_CHAR, pDevice->pszName);
    ACE_OS::snprintf(szPathLog, 1024, "%s%c%s.log", szCurfilename, ACE_DIRECTORY_SEPARATOR_CHAR, pDevice->pszName);

    size_t nMainIPSize = m_vecMainIP.size();
    size_t nBackupIPSize = m_vecBackupIP.size();

    if (0 == nMainIPSize)
    {
        CV_ERROR(g_codesysCVLog, 0, "nMainIPSize is 0, connectParam=%s.", pDevice->pszConnParam);
        return DEVLINK_FAILED;
    }

    if (0 == nBackupIPSize) // ����PLC����
    {
        /*  wuzheqiang 20250424:The previous configuration was as follows, with a timeout of 30 seconds.
            "timeout=20000\n"
            "tries=3\n"
            "waittime=20\n"
            "reconnecttime=1\n
        */

        m_mainIP = m_vecMainIP[m_nMainIPIndex % nMainIPSize];
        m_devInfo = m_devName + ":" + m_mainIP;

        // ���� ini �ļ�
        FILE *fpPLCini = fopen(szPathIni, "w+");
        if (fpPLCini != NULL)
        {
            fprintf(fpPLCini,
                    "[Server]\n"
                    "PLCs=1\n"
                    "PLC0=%s\n"
                    "\n[PLC:%s]\n"
                    "interfacetype=ARTI3\n"
                    "active=1\n"
                    "logevents=0\n"
                    "logfilter=16#000000FF\n"
                    "timeout=1000\n"
                    "tries=1\n"
                    "waittime=1\n"
                    "reconnecttime=1\n"
                    "parameters=1\n"
                    "parameter0=IpAddress\n"
                    "value0=%s\n",
                    pDevice->pszName, pDevice->pszName, m_mainIP.c_str()); // ip
            fclose(fpPLCini);
        }
        else
        {
            return DEVLINK_FAILED;
        }
    }
    else // ����PLC����
    {
        m_mainIP = m_vecMainIP[m_nMainIPIndex % nMainIPSize];
        m_backupIP = m_vecBackupIP[m_nBackupIPIndex % nBackupIPSize];
        m_devInfo = m_devName + ":" + m_mainIP + " " + m_backupIP;
        // ���� ini �ļ�
        FILE *fpPLCini = fopen(szPathIni, "w+");
        if (fpPLCini != NULL)
        {
            fprintf(fpPLCini,
                    "[Server]\n"
                    "PLCs=2\n"
                    "PLC0=%s_main\n"
                    "PLC1=%s_backup\n"
                    "\n[PLC:%s_main]\n"
                    "interfacetype=ARTI3\n"
                    "active=1\n"
                    "logevents=0\n"
                    "logfilter=16#000000FF\n"
                    "timeout=1000\n"
                    "tries=1\n"
                    "waittime=1\n"
                    "reconnecttime=1\n"
                    "parameters=1\n"
                    "parameter0=IpAddress\n"
                    "value0=%s\n"
                    "\n[PLC:%s_backup]\n"
                    "interfacetype=ARTI3\n"
                    "active=1\n"
                    "logevents=0\n"
                    "logfilter=16#000000FF\n"
                    "timeout=1000\n"
                    "tries=1\n"
                    "waittime=1\n"
                    "reconnecttime=1\n"
                    "parameters=1\n"
                    "parameter0=IpAddress\n"
                    "value0=%s\n",
                    pDevice->pszName, pDevice->pszName, pDevice->pszName, m_mainIP.c_str(), pDevice->pszName,
                    m_backupIP.c_str()); // ip
            fclose(fpPLCini);
        }
        else
        {
            return DEVLINK_FAILED;
        }
    }

    unsigned long ulID = 0;
    m_pPLCHandler = new CPLCHandler(ulID, szPathIni, m_hLogger);

    if (m_pPLCHandler == NULL)
    {
        CV_ERROR(g_codesysCVLog, 0, "PLCHandler[%s] init failed", GetDeviceInfo());
        return DEVLINK_FAILED;
    }
    m_pCurrActPLCHandler = m_pPLCHandler;

    m_pPLCHandler->SetLogging(TRUE, LOG_STD);
    m_pPLCHandler->SetLogFileCapacity(1024000, 5);
    m_pPLCHandler->SetLogFile(szPathLog);

    s_pfCMGetAPI = CMGetAPI;
    s_pfCMGetAPI2 = CMGetAPI2;
    ImportSystemFunctions();
    CAL_SysTaskWaitSleep(RTS_INVALID_HANDLE, 250);

    if (!isReadyToCommWithPlc(g_mapDevices[pDevice->pszName]))
    {
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] multilink=0,RM_STATUS_INACTIVE!", GetDeviceInfo());
        return ICV_SUCCESS;
    }

    long lResult = m_pPLCHandler->Connect(pDevice->nRecvTimeout, &m_stateCallBack, 1);
    if (lResult == RESULT_OK)
    {
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] connect successfully", GetDeviceInfo());
    }
    else if (lResult == RESULT_COMPONENTS_NOT_LOADED)
    {
        CV_ERROR(g_codesysCVLog, lResult, "**** Device %s Connect() failed: Not all components loaded ****",
                 GetDeviceInfo());
    }
    else if (lResult == RESULT_ITF_FAILED)
    {
        CV_ERROR(
            g_codesysCVLog, lResult,
            "**** Device %s Connect() failed: Interface is not ready (invalid configuration or missing drivers) ****",
            GetDeviceInfo());
    }
    else if (lResult == RESULT_NO_CONFIGURATION)
    {
        CV_ERROR(g_codesysCVLog, lResult, "**** Device %s Connect() failed: No configuration available ****",
                 GetDeviceInfo());
    }
    else if (lResult == RESULT_PLCHANDLER_INACTIVE)
    {
        CV_ERROR(g_codesysCVLog, lResult, "**** Device %s Connect() failed: PLCHandler inactive ****", GetDeviceInfo());
    }
    else
    {
        if (m_pPLCHandler->GetState() == STATE_NO_SYMBOLS)
        {
            CV_ERROR(g_codesysCVLog, lResult, "**** Device %s Connect() failed: No symbol information available ****",
                     GetDeviceInfo());
        }
        else if (m_pPLCHandler->GetState() == STATE_PLC_NOT_CONNECTED)
        {
            CV_ERROR(g_codesysCVLog, lResult, "**** Device %s Connect() failed: PLC not connected ****",
                     GetDeviceInfo());
        }
        else
        {
            CV_ERROR(g_codesysCVLog, lResult, "**** Device %s Connect() failed: Error code %d ****", GetDeviceInfo(),
                     lResult);
        }
    }

    if (0 == m_vecBackupIP.size())
    {
        return lResult;
    }

    // 1��Ӧ����ini�е�����
    m_pPLCHandlerBackUp = new CPLCHandler(1, szPathIni, m_hLogger);

    if (m_pPLCHandlerBackUp == NULL)
    {
        CV_ERROR(g_codesysCVLog, 0, "PLCHandler[%s] init failed", GetDeviceInfo());
        return DEVLINK_FAILED;
    }

    m_pPLCHandlerBackUp->SetLogging(TRUE, LOG_STD);
    m_pPLCHandlerBackUp->SetLogFileCapacity(1024000, 5);
    m_pPLCHandlerBackUp->SetLogFile(szPathLog);

    lResult = m_pPLCHandlerBackUp->Connect(pDevice->nRecvTimeout, &m_stateCallBackBackUp, 1);
    if (lResult == RESULT_OK)
    {
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] connect successfully", GetDeviceInfo());
        return DEVLINK_SUCCESS;
    }
    else if (lResult == RESULT_COMPONENTS_NOT_LOADED)
    {
        CV_ERROR(g_codesysCVLog, lResult, "**** Device %s Connect() failed: Not all components loaded ****",
                 GetDeviceInfo());
        return DEVLINK_FAILED;
    }
    else if (lResult == RESULT_ITF_FAILED)
    {
        CV_ERROR(
            g_codesysCVLog, lResult,
            "**** Device %s Connect() failed: Interface is not ready (invalid configuration or missing drivers) ****",
            GetDeviceInfo());
        return DEVLINK_FAILED;
    }
    else if (lResult == RESULT_NO_CONFIGURATION)
    {
        CV_ERROR(g_codesysCVLog, lResult, "**** Device %s Connect() failed: No configuration available ****",
                 GetDeviceInfo());
        return DEVLINK_FAILED;
    }
    else if (lResult == RESULT_PLCHANDLER_INACTIVE)
    {
        CV_ERROR(g_codesysCVLog, lResult, "**** Device %s Connect() failed: PLCHandler inactive ****", GetDeviceInfo());
        return DEVLINK_FAILED;
    }
    else
    {
        if (m_pPLCHandlerBackUp->GetState() == STATE_NO_SYMBOLS)
        {
            CV_ERROR(g_codesysCVLog, lResult, "**** Device %s Connect() failed: No symbol information available ****",
                     GetDeviceInfo());
        }
        else if (m_pPLCHandlerBackUp->GetState() == STATE_PLC_NOT_CONNECTED)
        {
            CV_ERROR(g_codesysCVLog, lResult, "**** Device %s Connect() failed: PLC not connected ****",
                     GetDeviceInfo());
        }
        else
        {
            CV_ERROR(g_codesysCVLog, lResult, "**** Device %s Connect() failed: Error code %d ****", GetDeviceInfo(),
                     lResult);
        }

        return DEVLINK_FAILED;
    }
}

void CCodesysDevice::Disconnect()
{

    if (m_pCurrActPLCHandler == m_pPLCHandler)
    {
        if (m_hVarList != NULL)
        {
            if (m_nCodesysReadType == CODESYS_READ_TYPE_CYCLE)
            {
                m_pPLCHandler->CycDeleteVarList(m_hVarList, 1); // �ͷ�ѭ����ȡ�ı����б�
            }
            else
            {
                m_pPLCHandler->SyncReadVarsFromPlcReleaseValues(m_hVarList); // �ͷ�ͬ����ȡ�ı����б�
            }
            m_hVarList = NULL;
        }
    }
    else
    {
        if (m_hVarListBackUp != NULL)
        {
            if (m_nCodesysReadType == CODESYS_READ_TYPE_CYCLE)
            {
                m_pPLCHandlerBackUp->CycDeleteVarList(m_hVarListBackUp, 1); // �ͷ�ѭ����ȡ�ı����б�
            }
            else
            {
                m_pPLCHandlerBackUp->SyncReadVarsFromPlcReleaseValues(m_hVarListBackUp); // �ͷ�ͬ����ȡ�ı����б�
            }
            m_hVarListBackUp = NULL;
        }
    }

    if (m_pPLCHandler != NULL)
    {
        m_pPLCHandler->Disconnect();
        m_pPLCHandler->~CPLCHandler();
        m_pPLCHandler = NULL;
        CV_WARN(g_codesysCVLog, -1, "PLCHandler[%s] disconnected.", GetDeviceInfo());
    }

    if (0 < m_vecBackupIP.size())
    {
        if (m_pPLCHandlerBackUp != NULL)
        {
            m_pPLCHandlerBackUp->Disconnect();
            m_pPLCHandlerBackUp->~CPLCHandler();
            m_pPLCHandlerBackUp = NULL;
            CV_WARN(g_codesysCVLog, -1, "PLCHandler[%s] disconnected.", GetDeviceInfo());
        }
    }

    if (m_ppszVarsOrig != NULL)
    {
        // �ͷ� strdup ʹ�õĿռ�
        for (unsigned long i = 0; i < m_ulNumOfSymbolsOrig; ++i)
        {
            free(m_ppszVarsOrig[i]);
        }

        delete[] m_ppszVarsOrig;
        m_ppszVarsOrig = NULL;
        m_ulNumOfSymbolsOrig = 0;
    }

    if (m_ppszVars != NULL)
    {
        delete[] m_ppszVars;
        m_ppszVars = nullptr;
        m_ulNumOfSymbols = 0;
    }
}

long CCodesysDevice::ReadData(void)
{
    ACE_READ_GUARD_RETURN(ACE_RW_Thread_Mutex, guard, m_rwThreadMutex, EC_ICV_DA_GUARD_RETURN);
    long lRet = RESULT_OK;

    if (m_pPLCHandler == NULL)
        return DEVLINK_FAILED;
    unsigned long ulNumOfValues = 0;
    HCYCLIST hCycVarList = NULL;
    PlcVarValue **ppValues = NULL;

    if (m_nCodesysReadType == CODESYS_READ_TYPE_CYCLE)
    {
        if (m_hVarList == NULL)
        {
            // ����ѭ����ȡ
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycDefineVarList", GetDeviceInfo());
            m_hVarList = m_pPLCHandler->CycDefineVarList(m_ppszVars, m_ulNumOfSymbols, m_nCycleRate, NULL, NULL, 0, 0);
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycDefineVarList", GetDeviceInfo());
        }

        if (NULL == m_hVarList)
        {
            CV_ERROR(g_codesysCVLog, RESULT_INVALID_PARAMETER, "PLCHandler[%s] m_hVarList %p", GetDeviceInfo(),
                     m_hVarList);
            return RESULT_INVALID_PARAMETER;
        }

        // CAL_SysTaskWaitSleep(RTS_INVALID_HANDLE, 300);
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycEnterVarAccess", GetDeviceInfo());
        m_pPLCHandler->CycEnterVarAccess(m_hVarList);
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycEnterVarAccess", GetDeviceInfo());

        CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycReadVars", GetDeviceInfo());
        lRet = m_pPLCHandler->CycReadVars(m_hVarList, &ppValues, &ulNumOfValues);
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycReadVars,ulNumOfValues=%u", GetDeviceInfo(), ulNumOfValues);

        if (lRet != RESULT_NO_UPDATE && lRet != RESULT_OK)
        {
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycLeaveVarAccess", GetDeviceInfo());
            m_pPLCHandler->CycLeaveVarAccess(m_hVarList);
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycLeaveVarAccess", GetDeviceInfo());

            CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycDeleteVarList", GetDeviceInfo());
            m_pPLCHandler->CycDeleteVarList(m_hVarList);
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycDeleteVarList", GetDeviceInfo());
            m_hVarList = NULL;
            return RESULT_OK;
        }
    }
    else
    {
        if (m_hVarList == NULL)
        {
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] start SyncReadVarsFromPlc", GetDeviceInfo());
            m_hVarList = m_pPLCHandler->SyncReadVarsFromPlc(m_ppszVars, m_ulNumOfSymbols, &ppValues, &ulNumOfValues);
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] end SyncReadVarsFromPlc, ulNumOfValues=%u", GetDeviceInfo(),
                    ulNumOfValues);
            lRet = RESULT_OK;
        }
        else
        {
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] start SyncReadVarsFromPlc", GetDeviceInfo());
            lRet = m_pPLCHandler->SyncReadVarsFromPlc(m_hVarList, &ppValues, &ulNumOfValues);
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] end SyncReadVarsFromPlc, ulNumOfValues=%u", GetDeviceInfo(),
                    ulNumOfValues);
        }
    }

    if (lRet == RESULT_OK && ppValues != NULL && ulNumOfValues == m_ulNumOfSymbols)
    {
        for (TagTypeDeviceTagMap::iterator it = m_mapTags.begin(); it != m_mapTags.end(); ++it)
        {
            it->second.lQuality = DATA_STATUS_CONFIG_ERROR;
        }
        for (unsigned long i = 0; i < ulNumOfValues; ++i)
        {
            TagTypeDeviceTagMap::iterator it = m_mapTags.find(std::string(m_ppszVars[i]));
            if (it == m_mapTags.end())
            {
                continue;
            }
            if (ppValues[i]->bQuality)
            {
                memcpy(it->second.szData, ppValues[i]->byData, it->second.iSize);
                if (m_szRedundantState == it->first)
                {
                    char szHexBuf[MAX_BLOB_TEXT_LEN * 3];
                    uint32 nHexBuf = sizeof(szHexBuf);
                    cvcommon::HexDumpBuf((unsigned char *)it->second.szData, it->second.iSize, szHexBuf, &nHexBuf);
                    CV_INFO(g_codesysCVLog, "PLCHandler[%s] Tag[%s] iSize[%d] szData %s", GetDeviceInfo(),
                            it->first.c_str(), it->second.iSize, szHexBuf);
                }
            }
            it->second.lQuality = ppValues[i]->bQuality ? DATA_STATUS_OK : DATA_STATUS_LAST_UNKNOWN;
            it->second.nTypeMatch = TYPE_MATCH;
            CV_TRACE(g_codesysCVLog, "PLCHandler[%s] Tag[%s] lQuality %d bTypeMatch %d", GetDeviceInfo(),
                     it->first.c_str(), it->second.lQuality, it->second.nTypeMatch);
        }
    }
    else if (lRet == RESULT_NO_UPDATE)
        lRet = RESULT_OK;
    else
        CV_ERROR(g_codesysCVLog, lRet, "PLCHandler[%s] Reading values error, errno %d", GetDeviceInfo(), lRet);
    if (m_nCodesysReadType == CODESYS_READ_TYPE_CYCLE)
    {
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycLeaveVarAccess", GetDeviceInfo());
        m_pPLCHandler->CycLeaveVarAccess(m_hVarList);
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycLeaveVarAccess", GetDeviceInfo());
    }

    return lRet;
}

// ����PLC�ɼ�
// ����plchandler��������/������ֵ
long CCodesysDevice::ReadData(CPLCHandler *pPLCHandler)
{
    ACE_READ_GUARD_RETURN(ACE_RW_Thread_Mutex, guard, m_rwThreadMutex, EC_ICV_DA_GUARD_RETURN);
    long lRet = RESULT_OK;

    if (pPLCHandler == NULL)
        return DEVLINK_FAILED;

    unsigned long ulNumOfValues = 0;
    HCYCLIST hCycVarList = NULL;
    PlcVarValue **ppValues = NULL;

    // CPLCHandler* pOldPLCHandler;
    if (!m_bSwitch)
    {
        m_pOldPLCHandler = pPLCHandler;
        m_bSwitch = true;
    }

    if (pPLCHandler != m_pOldPLCHandler)
    {
        m_pOldPLCHandler = pPLCHandler;
        m_hVarList = NULL; // �����л�������m_hVarList
        m_hVarListBackUp = NULL;
    }

    if (pPLCHandler == m_pPLCHandler)
    {
        if (m_nCodesysReadType == CODESYS_READ_TYPE_CYCLE)
        {
            if (m_hVarList == NULL)
            {
                // ����ѭ����ȡ
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycDefineVarList", GetDeviceInfo());
                m_hVarList =
                    pPLCHandler->CycDefineVarList(m_ppszVars, m_ulNumOfSymbols, m_nCycleRate, NULL, NULL, 0, 0);
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycDefineVarList", GetDeviceInfo());
            }
            if (NULL == m_hVarList)
            {
                CV_ERROR(g_codesysCVLog, RESULT_INVALID_PARAMETER, "PLCHandler[%s] m_hVarList %p ", GetDeviceInfo(),
                         m_hVarList);
                return RESULT_INVALID_PARAMETER;
            }

            // CAL_SysTaskWaitSleep(RTS_INVALID_HANDLE, 300);
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycEnterVarAccess", GetDeviceInfo());
            pPLCHandler->CycEnterVarAccess(m_hVarList);
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycEnterVarAccess", GetDeviceInfo());

            CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycReadVars", GetDeviceInfo());
            lRet = pPLCHandler->CycReadVars(m_hVarList, &ppValues, &ulNumOfValues);
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycReadVars, ulNumOfValues=%u", GetDeviceInfo(), ulNumOfValues);

            if (lRet != RESULT_NO_UPDATE && lRet != RESULT_OK)
            {
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycLeaveVarAccess", GetDeviceInfo());
                pPLCHandler->CycLeaveVarAccess(m_hVarList);
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycLeaveVarAccess", GetDeviceInfo());

                CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycDeleteVarList", GetDeviceInfo());
                pPLCHandler->CycDeleteVarList(m_hVarList);
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycDeleteVarList", GetDeviceInfo());

                m_hVarList = NULL;
                return RESULT_OK;
            }
        }
        else
        {
            if (m_hVarList == NULL)
            {
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] start SyncReadVarsFromPlc", GetDeviceInfo());
                m_hVarList = pPLCHandler->SyncReadVarsFromPlc(m_ppszVars, m_ulNumOfSymbols, &ppValues, &ulNumOfValues);
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] end SyncReadVarsFromPlc, ulNumOfValues=%u", GetDeviceInfo(),
                        ulNumOfValues);
                lRet = RESULT_OK;
            }
            else
            {
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] start SyncReadVarsFromPlc", GetDeviceInfo());
                lRet = pPLCHandler->SyncReadVarsFromPlc(m_hVarList, &ppValues, &ulNumOfValues);
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] end SyncReadVarsFromPlc, ulNumOfValues=%u", GetDeviceInfo(),
                        ulNumOfValues);
            }
        }

        if (lRet == RESULT_OK && ppValues != NULL && ulNumOfValues == m_ulNumOfSymbols)
        {
            for (TagTypeDeviceTagMap::iterator it = m_mapTags.begin(); it != m_mapTags.end(); ++it)
            {
                it->second.lQuality = DATA_STATUS_CONFIG_ERROR;
            }
            for (unsigned long i = 0; i < ulNumOfValues; ++i)
            {
                TagTypeDeviceTagMap::iterator it = m_mapTags.find(std::string(m_ppszVars[i]));
                if (it == m_mapTags.end())
                {
                    continue;
                }
                if (ppValues[i]->bQuality)
                {
                    memcpy(it->second.szData, ppValues[i]->byData, it->second.iSize);
                    if (m_szRedundantState == it->first)
                    {
                        char szHexBuf[MAX_BLOB_TEXT_LEN * 3];
                        uint32 nHexBuf = sizeof(szHexBuf);
                        cvcommon::HexDumpBuf((unsigned char *)it->second.szData, it->second.iSize, szHexBuf, &nHexBuf);
                        CV_INFO(g_codesysCVLog, "PLCHandler[%s] Tag[%s] iSize[%d] szData %s", GetDeviceInfo(),
                                it->first.c_str(), it->second.iSize, szHexBuf);
                    }
                }
                it->second.lQuality = ppValues[i]->bQuality ? DATA_STATUS_OK : DATA_STATUS_LAST_UNKNOWN;
                it->second.nTypeMatch = TYPE_MATCH;
                CV_TRACE(g_codesysCVLog, "PLCHandler[%s] Tag[%s] lQuality %d bTypeMatch %d", GetDeviceInfo(),
                         it->first.c_str(), it->second.lQuality, it->second.nTypeMatch);
            }
        }
        else if (lRet == RESULT_NO_UPDATE)
            lRet = RESULT_OK;
        else
            CV_ERROR(g_codesysCVLog, lRet, "PLCHandler[%s] Reading values error, errno %d", GetDeviceInfo(), lRet);
        if (m_nCodesysReadType == CODESYS_READ_TYPE_CYCLE)
        {
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycLeaveVarAccess", GetDeviceInfo());
            pPLCHandler->CycLeaveVarAccess(m_hVarList);
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycLeaveVarAccess", GetDeviceInfo());
        }
    }
    else if (pPLCHandler == m_pPLCHandlerBackUp)
    {
        if (m_nCodesysReadType == CODESYS_READ_TYPE_CYCLE)
        {
            if (m_hVarListBackUp == NULL)
            {
                // ����ѭ����ȡ
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycDefineVarList", GetDeviceInfo());
                m_hVarListBackUp =
                    pPLCHandler->CycDefineVarList(m_ppszVars, m_ulNumOfSymbols, m_nCycleRate, NULL, NULL, 0, 0);
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycDefineVarList", GetDeviceInfo());
            }

            // CAL_SysTaskWaitSleep(RTS_INVALID_HANDLE, 300);
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycEnterVarAccess", GetDeviceInfo());
            pPLCHandler->CycEnterVarAccess(m_hVarListBackUp);
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycEnterVarAccess", GetDeviceInfo());

            CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycReadVars", GetDeviceInfo());
            lRet = pPLCHandler->CycReadVars(m_hVarListBackUp, &ppValues, &ulNumOfValues);
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycReadVars, ulNumOfValues=%u", GetDeviceInfo(), ulNumOfValues);
            if (lRet != RESULT_NO_UPDATE && lRet != RESULT_OK)
            {
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycLeaveVarAccess", GetDeviceInfo());
                pPLCHandler->CycLeaveVarAccess(m_hVarListBackUp);
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycLeaveVarAccess", GetDeviceInfo());

                CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycDeleteVarList", GetDeviceInfo());
                pPLCHandler->CycDeleteVarList(m_hVarListBackUp);
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycDeleteVarList", GetDeviceInfo());

                m_hVarListBackUp = NULL;
                return RESULT_OK;
            }
        }
        else
        {
            if (m_hVarListBackUp == NULL)
            {
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] start SyncReadVarsFromPlc", GetDeviceInfo());
                m_hVarListBackUp =
                    pPLCHandler->SyncReadVarsFromPlc(m_ppszVars, m_ulNumOfSymbols, &ppValues, &ulNumOfValues);
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] end SyncReadVarsFromPlc, ulNumOfValues=%u", GetDeviceInfo(),
                        ulNumOfValues);
                lRet = RESULT_OK;
            }
            else
            {
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] start SyncReadVarsFromPlc", GetDeviceInfo());
                lRet = pPLCHandler->SyncReadVarsFromPlc(m_hVarListBackUp, &ppValues, &ulNumOfValues);
                CV_INFO(g_codesysCVLog, "PLCHandler[%s] end SyncReadVarsFromPlc, ulNumOfValues=%u", GetDeviceInfo(),
                        ulNumOfValues);
            }
        }

        if (lRet == RESULT_OK && ppValues != NULL && ulNumOfValues == m_ulNumOfSymbols)
        {
            for (TagTypeDeviceTagMap::iterator it = m_mapTags.begin(); it != m_mapTags.end(); ++it)
            {
                it->second.lQuality = DATA_STATUS_CONFIG_ERROR;
            }
            for (unsigned long i = 0; i < ulNumOfValues; ++i)
            {
                TagTypeDeviceTagMap::iterator it = m_mapTags.find(std::string(m_ppszVars[i]));
                if (it == m_mapTags.end())
                {
                    continue;
                }
                if (ppValues[i]->bQuality)
                {
                    memcpy(it->second.szData, ppValues[i]->byData, it->second.iSize);
                    if (m_szRedundantState == it->first)
                    {
                        char szHexBuf[MAX_BLOB_TEXT_LEN * 3];
                        uint32 nHexBuf = sizeof(szHexBuf);
                        cvcommon::HexDumpBuf((unsigned char *)it->second.szData, it->second.iSize, szHexBuf, &nHexBuf);
                        CV_INFO(g_codesysCVLog, "PLCHandler[%s] Tag[%s] iSize[%d] szData %s", GetDeviceInfo(),
                                it->first.c_str(), it->second.iSize, szHexBuf);
                    }
                }
                it->second.lQuality = ppValues[i]->bQuality ? DATA_STATUS_OK : DATA_STATUS_LAST_UNKNOWN;
                it->second.nTypeMatch = TYPE_MATCH;
                CV_TRACE(g_codesysCVLog, "PLCHandler[%s] Tag[%s] lQuality %d bTypeMatch %d", GetDeviceInfo(),
                         it->first.c_str(), it->second.lQuality, it->second.nTypeMatch);
            }
        }
        else if (lRet == RESULT_NO_UPDATE)
            lRet = RESULT_OK;
        else
            CV_ERROR(g_codesysCVLog, lRet, "PLCHandler[%s] Reading values error, errno %d", GetDeviceInfo(), lRet);
        if (m_nCodesysReadType == CODESYS_READ_TYPE_CYCLE)
        {
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] start CycLeaveVarAccess", GetDeviceInfo());
            pPLCHandler->CycLeaveVarAccess(m_hVarListBackUp);
            CV_INFO(g_codesysCVLog, "PLCHandler[%s] end CycLeaveVarAccess", GetDeviceInfo());
        }
    }

    return lRet;
}

long CCodesysDevice::OnWriteCmd(const char *address, char *szCmdData, int nCmdDataLenBits)
{
    char *ppszVars[1];
    unsigned char *ppbyValues[1];
    unsigned long pulValueSize[1];
    PlcSymbolDesc plcsymbol;
    unsigned char *szStr = NULL;
    ppszVars[0] = (char *)address;
    ppbyValues[0] = (unsigned char *)szCmdData;
    pulValueSize[0] = nCmdDataLenBits >= 8 ? nCmdDataLenBits / 8 : 1;

    long lRet = 0;
    // У������Ƿ�Ϸ������·�һ������ĵ㣬GetState״̬�᷵��PLC_NOT_CONNECTED
    std::map<std::string, int>::iterator iter = m_mapSymbolsFromPlc.find(address);
    if (iter == m_mapSymbolsFromPlc.end())
    {
        CV_ERROR(g_codesysCVLog, -1, "PLCHander[%s] has no symbol %s.", GetDeviceInfo(), address);
        return lRet;
    }
    // 	lRet = m_pPLCHandler->GetItem(ppszVars[0],&plcsymbol);
    // 	if(lRet == RESULT_OK)
    // 	{
    // 		if(plcsymbol.ulTypeId == DATATYPE_STRING)
    // 		{
    // 			szStr = new unsigned char(plcsymbol.ulSize);
    // 			memset(szStr, 0, plcsymbol.ulSize);
    // 			strcpy((char*)szStr, szCmdData);
    // 			pulValueSize[0] = plcsymbol.ulSize;
    // 			ppbyValues[0] = szStr;
    // 		}
    // 	}
    // 	else
    // 	{
    // 		return DEVLINK_SUCCESS;
    // 	}

    // ��־���Hex��������
    char szBuffer[ICV_BLOBVALUE_MAXLEN];
    unsigned int nHexBufferLen = ICV_BLOBVALUE_MAXLEN;
    memset(szBuffer, 0, sizeof(szBuffer));
    cvcommon::HexDumpBuf((unsigned char *)szCmdData,
                         nCmdDataLenBits % BITS_PER_BYTE == 0 ? nCmdDataLenBits / BITS_PER_BYTE
                                                              : nCmdDataLenBits / BITS_PER_BYTE + 1,
                         szBuffer, &nHexBufferLen);

    CV_INFO(g_codesysCVLog, "PLCHandler[%s] write %s  value : %s, nCmdDataLenBits : %d, ValueSize : %d",
            GetDeviceInfo(), address, szBuffer, nCmdDataLenBits, pulValueSize[0]);
    // ����plcдֵ
    if (0 == m_vecBackupIP.size())
    {
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] start SyncWriteVarsToPlc", GetDeviceInfo());
        lRet = m_pPLCHandler->SyncWriteVarsToPlc(ppszVars, 1, ppbyValues, NULL);
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] end SyncWriteVarsToPlc", GetDeviceInfo());
    }
    // ����plcдֵ����֤д����
    else
    {
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] start SyncWriteVarsToPlc", GetDeviceInfo());
        lRet = m_pCurrActPLCHandler->SyncWriteVarsToPlc(ppszVars, 1, ppbyValues, NULL);
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] end SyncWriteVarsToPlc", GetDeviceInfo());
    }
    if (lRet == RESULT_OK)
    {
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] write %s successfully", GetDeviceInfo(), address);
    }
    else
    {
        CV_ERROR(g_codesysCVLog, lRet, "PLCHandler[%s] write %s failed, errno %d", GetDeviceInfo(), address, lRet);
    }
    // 	if(plcsymbol.ulTypeId == DATATYPE_STRING)
    // 	{
    // 		if(szStr != NULL)
    // 			delete [] szStr;
    // 	}
    return DEVLINK_SUCCESS;
}

long CCodesysDevice::ReadType(bool bOffline)
{
    ACE_WRITE_GUARD_RETURN(ACE_RW_Thread_Mutex, guard, m_rwThreadMutex, EC_ICV_DA_GUARD_RETURN);

    CV_INFO(g_codesysCVLog, "PLCHandler[%s] start EnterItemAccess", GetDeviceInfo());
    long lRet = m_pPLCHandler->EnterItemAccess();
    CV_INFO(g_codesysCVLog, "PLCHandler[%s] end EnterItemAccess", GetDeviceInfo());
    if (!bOffline && lRet != RESULT_OK)
    {
        CV_ERROR(g_codesysCVLog, lRet, "PLCHandler[%s] EnterItemAccess failed,errno %d", GetDeviceInfo(), lRet);
        return DEVLINK_FAILED;
    }

    unsigned long ulNumOfSymbols;
    PlcSymbolDesc *pSymbols = NULL;
    CV_INFO(g_codesysCVLog, "PLCHandler[%s] start GetAllItems", GetDeviceInfo());
    lRet = m_pPLCHandler->GetAllItems(&pSymbols, &ulNumOfSymbols);
    CV_INFO(g_codesysCVLog, "PLCHandler[%s] end GetAllItems", GetDeviceInfo());
    if (lRet != RESULT_OK)
    {
        CV_ERROR(g_codesysCVLog, lRet, "PLCHandler[%s] get symbols failed,errno %d", GetDeviceInfo(), lRet);
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] start LeaveItemAccess", GetDeviceInfo());
        m_pPLCHandler->LeaveItemAccess();
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] end LeaveItemAccess", GetDeviceInfo());
        return DEVLINK_FAILED;
    }

    // free m_ppszVars Only GetAllItems return success in case of CycReadVars crash
    if (m_ppszVarsOrig != NULL)
    {

        // �ͷ� strdup ʹ�õĿռ�
        for (unsigned long i = 0; i < m_ulNumOfSymbolsOrig; ++i)
        {
            free(m_ppszVarsOrig[i]);
        }

        delete[] m_ppszVarsOrig;
        m_ppszVarsOrig = NULL;
        m_ulNumOfSymbolsOrig = 0;
        m_mapSymbolsFromPlc.clear();
    }

    CV_INFO(g_codesysCVLog, "PLCHandler[%s] get %d symbols successfully.", GetDeviceInfo(), ulNumOfSymbols);
    m_ppszVarsOrig = new char *[ulNumOfSymbols];
    for (unsigned long i = 0; i < ulNumOfSymbols; ++i)
    {
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] symbol name: %s, type: %s ,size: %d", GetDeviceInfo(),
                pSymbols[i].pszName, pSymbols[i].pszType, pSymbols[i].ulSize);
        m_ppszVarsOrig[i] = strdup(pSymbols[i].pszName);
        // add to map for write validation
        m_mapSymbolsFromPlc.insert(std::make_pair(pSymbols[i].pszName, i));

        TagTypeDeviceTagMap::iterator it = m_mapTags.find(pSymbols[i].pszName);
        if (it == m_mapTags.end())
            continue;

        if (isTypeMatch(it->second.typeCV, pSymbols[i].ulTypeId))
            it->second.nTypeMatch = TYPE_MATCH;
        else
            it->second.nTypeMatch = TYPE_NOT_MATCH;
        if (it->second.nTypeMatch == TYPE_NOT_MATCH)
        {
            std::map<PlcTypeClass, std::string>::iterator iterPLC =
                g_mapPLCDataType.find(static_cast<PlcTypeClass>(pSymbols[i].ulTypeId));
            string strPLCType = "UNKOWN";
            if (iterPLC != g_mapPLCDataType.end())
            {
                strPLCType = iterPLC->second;
            }

            std::map<unsigned long, std::string>::iterator iterIplat = g_mapIplatDataType.find(it->second.typeCV);
            string strIplatType = "UNKOWN";
            if (iterIplat != g_mapIplatDataType.end())
            {
                strIplatType = iterIplat->second;
            }

            CV_ERROR(g_codesysCVLog, -1,
                     "PLCHandler[%s] symbol name: %s iplatda data type %d %s does not support PLC data type %d %s.",
                     GetDeviceInfo(), pSymbols[i].pszName, it->second.typeCV, strIplatType.c_str(),
                     pSymbols[i].ulTypeId, strPLCType.c_str());
        }
    }

    if (!bOffline)
    {
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] start LeaveItemAccess", GetDeviceInfo());
        m_pPLCHandler->LeaveItemAccess();
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] end LeaveItemAccess", GetDeviceInfo());
    }

    m_iNumOfVars = m_mapTags.size();
    m_ulNumOfSymbolsOrig = ulNumOfSymbols;
    FilterTagSymbols(true);
    CV_INFO(g_codesysCVLog, "PLCHandler[%s] symbol iplatda/PLC size %d %d", GetDeviceInfo(), m_iNumOfVars,
            m_ulNumOfSymbolsOrig);

    return DEVLINK_SUCCESS;
}

long CCodesysDevice::ReadType(CPLCHandler *pPLCHandler, bool bOffline)
{
    ACE_WRITE_GUARD_RETURN(ACE_RW_Thread_Mutex, guard, m_rwThreadMutex, EC_ICV_DA_GUARD_RETURN);

    CV_INFO(g_codesysCVLog, "PLCHandler[%s] start EnterItemAccess", GetDeviceInfo());
    long lRet = pPLCHandler->EnterItemAccess();
    CV_INFO(g_codesysCVLog, "PLCHandler[%s] end EnterItemAccess", GetDeviceInfo());
    if (!bOffline && lRet != RESULT_OK)
    {
        CV_ERROR(g_codesysCVLog, lRet, "PLCHandler[%s] EnterItemAccess failed,errno %d", GetDeviceInfo(), lRet);
        return DEVLINK_FAILED;
    }

    unsigned long ulNumOfSymbols;
    PlcSymbolDesc *pSymbols = NULL;
    CV_INFO(g_codesysCVLog, "PLCHandler[%s] start GetAllItems", GetDeviceInfo());
    lRet = pPLCHandler->GetAllItems(&pSymbols, &ulNumOfSymbols);
    CV_INFO(g_codesysCVLog, "PLCHandler[%s] end GetAllItems", GetDeviceInfo());
    if (lRet != RESULT_OK)
    {
        CV_ERROR(g_codesysCVLog, lRet, "PLCHandler[%s] get symbols failed,errno %d", GetDeviceInfo(), lRet);
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] start LeaveItemAccess", GetDeviceInfo());
        pPLCHandler->LeaveItemAccess();
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] end LeaveItemAccess", GetDeviceInfo());
        return DEVLINK_FAILED;
    }

    // free m_ppszVarsOrig Only GetAllItems return success in case of CycReadVars crash
    if (m_ppszVarsOrig != NULL)
    {

        // �ͷ� strdup ʹ�õĿռ�
        for (unsigned long i = 0; i < m_ulNumOfSymbolsOrig; ++i)
        {
            free(m_ppszVarsOrig[i]);
        }

        delete[] m_ppszVarsOrig;
        m_ppszVarsOrig = NULL;
        m_ulNumOfSymbolsOrig = 0;
        m_mapSymbolsFromPlc.clear();
    }

    const char *szPlcName = pPLCHandler->GetName();
    CV_INFO(g_codesysCVLog, "PLCHandler[%s][%s]  get %d symbols successfully.", GetDeviceInfo(), szPlcName,
            ulNumOfSymbols);
    m_ppszVarsOrig = new char *[ulNumOfSymbols];
    for (unsigned long i = 0; i < ulNumOfSymbols; ++i)
    {
        CV_INFO(g_codesysCVLog, "PLCHandler[%s][%s] symbol name: %s, type: %s ,size: %d", GetDeviceInfo(), szPlcName,
                pSymbols[i].pszName, pSymbols[i].pszType, pSymbols[i].ulSize);
        m_ppszVarsOrig[i] = strdup(pSymbols[i].pszName);
        // add to map for write validation
        m_mapSymbolsFromPlc.insert(std::make_pair(pSymbols[i].pszName, i));

        TagTypeDeviceTagMap::iterator it = m_mapTags.find(pSymbols[i].pszName);
        if (it == m_mapTags.end())
            continue;

        if (isTypeMatch(it->second.typeCV, pSymbols[i].ulTypeId))
            it->second.nTypeMatch = TYPE_MATCH;
        else
            it->second.nTypeMatch = TYPE_NOT_MATCH;
        if (it->second.nTypeMatch == TYPE_NOT_MATCH)
        {
            std::map<PlcTypeClass, std::string>::iterator iterPLC =
                g_mapPLCDataType.find(static_cast<PlcTypeClass>(pSymbols[i].ulTypeId));
            string strPLCType = "UNKOWN";
            if (iterPLC != g_mapPLCDataType.end())
            {
                strPLCType = iterPLC->second;
            }

            std::map<unsigned long, std::string>::iterator iterIplat = g_mapIplatDataType.find(it->second.typeCV);
            string strIplatType = "UNKOWN";
            if (iterIplat != g_mapIplatDataType.end())
            {
                strIplatType = iterIplat->second;
            }

            CV_ERROR(g_codesysCVLog, -1,
                     "PLCHandler[%s] symbol name: %s iplatda data type [%d,%s] does not support PLC data type [%d,%s].",
                     GetDeviceInfo(), pSymbols[i].pszName, it->second.typeCV, strIplatType.c_str(),
                     pSymbols[i].ulTypeId, strPLCType.c_str());
        }
    }

    if (!bOffline)
    {
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] start LeaveItemAccess", GetDeviceInfo());
        pPLCHandler->LeaveItemAccess();
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] end LeaveItemAccess", GetDeviceInfo());
    }

    m_iNumOfVars = m_mapTags.size();
    m_ulNumOfSymbolsOrig = ulNumOfSymbols;
    FilterTagSymbols(true);
    CV_INFO(g_codesysCVLog, "PLCHandler[%s][%s] symbol iplatda/PLC size %d %d", GetDeviceInfo(), szPlcName,
            m_iNumOfVars, m_ulNumOfSymbolsOrig);

    return DEVLINK_SUCCESS;
}

bool CCodesysDevice::isTypeMatch(const unsigned long &nTypeCV, const unsigned long &nTypePLC)
{
    switch (nTypePLC)
    {
    case DATATYPE_BOOL:
        return nTypeCV == TAG_DATATYPE_BOOL;
    case DATATYPE_SINT:
    case DATATYPE_USINT:
    case DATATYPE_BYTE:
        return nTypeCV == TAG_DATATYPE_SINT || nTypeCV == TAG_DATATYPE_USINT;
    case DATATYPE_INT:
    case DATATYPE_UINT:
    case DATATYPE_WORD:
        return nTypeCV == TAG_DATATYPE_INT || nTypeCV == TAG_DATATYPE_UINT;
    case DATATYPE_DINT:
    case DATATYPE_UDINT:
    case DATATYPE_DWORD:
        return nTypeCV == TAG_DATATYPE_UDINT || nTypeCV == TAG_DATATYPE_DINT;
    case DATATYPE_REAL:
        return nTypeCV == TAG_DATATYPE_REAL;
    case DATATYPE_LREAL:
        return nTypeCV == TAG_DATATYPE_LREAL;
    case DATATYPE_TIME:
        return nTypeCV == TAG_DATATYPE_TIME || nTypeCV == TAG_DATATYPE_DINT;
    case DATATYPE_STRING:
        return nTypeCV == TAG_DATATYPE_STRING;
    case DATATYPE_BITORBYTE:
    case DATATYPE_DATE:
    case DATATYPE_TOD:
    case DATATYPE_DT:
    case DATATYPE_REF:
    case DATATYPE_VOID:
    case DATATYPE_LINT:
    case DATATYPE_ULINT:
    case DATATYPE_LTIME:
    case DATATYPE_WSTRING:
    case DATATYPE_LWORD:
    case DATATYPE_BIT:
        return false;
    default:
        return false;
    }
}

long CCodesysDevice::GetStatus()
{
    // ����plc
    if (0 == m_vecBackupIP.size())
    {
        if (m_pPLCHandler == NULL)
        {
            // m_pPLCHandler = new CPLCHandler(RTS_INVALID_HANDLE);
            return DEVLINK_FAILED;
        }

        CV_INFO(g_codesysCVLog, "PLCHandler[%s] start GetState", GetDeviceInfo());
        PLCHANDLER_STATE EnumState = m_pPLCHandler->GetState();
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] end GetState,EnumState = %d", GetDeviceInfo(), EnumState);
        if (STATE_PLC_NOT_CONNECTED == EnumState)
        {
            if (++m_nConnectFailedCnt > g_nReconnectTimes)
            {
                m_nMainIPIndex.fetch_add(1);
                m_bReconnect.store(true);
                m_nConnectFailedCnt = 0;
            }
        }
        return (EnumState == STATE_RUNNING) ? DEVLINK_SUCCESS : DEVLINK_FAILED;
    }

    PLCHANDLER_STATE EnumStateMain = STATE_TERMINATE;
    if (m_pPLCHandler != NULL)
    {
        EnumStateMain = m_pPLCHandler->GetState();
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] main GetState() = %d", GetDeviceInfo(), EnumStateMain);
    }

    PLCHANDLER_STATE EnumStateBack = STATE_TERMINATE;
    if (m_pPLCHandlerBackUp != NULL)
    {
        EnumStateBack = m_pPLCHandlerBackUp->GetState();
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] back GetState() = %d", GetDeviceInfo(), EnumStateBack);
    }

    // ����plc
    // if (m_pPLCHandler == NULL)
    //     m_pPLCHandler = new CPLCHandler(RTS_INVALID_HANDLE);
    // if (m_pPLCHandlerBackUp == NULL)
    //     m_pPLCHandlerBackUp = new CPLCHandler(RTS_INVALID_HANDLE);

    // PLCREDUNDANT_STATE EnumState;
    // ����PLCֻ��������״̬�Ƿ���active��active�ش���STATE_RUNNING״̬���������plc����GetState()
    int nStateMain = -1;
    if (STATE_RUNNING == EnumStateMain)
    {
        int nState = GetPlcRedundantStatus(m_pPLCHandler);
        switch (nState)
        {
        case RS_START:
            CV_ERROR(g_codesysCVLog, nState, "PLCHandler[%s_main] Plc in startup", GetDeviceInfo());
            break;
        case RS_SYNCHRO:
            CV_ERROR(g_codesysCVLog, nState, "PLCHandler[%s_main] Plc is synchronising", GetDeviceInfo());
            break;
        case RS_CYCLE_ACTIVE:
            CV_INFO(g_codesysCVLog, "PLCHandler[%s_main] Plc is running in active mode", GetDeviceInfo());
            // m_pCurrActPLCHandler = m_pPLCHandler;
            // return DEVLINK_SUCCESS;
            break;
        case RS_CYCLE_STANDBY:
            CV_WARN(g_codesysCVLog, nState, "PLCHandler[%s_main] Plc is running in standby mode", GetDeviceInfo());
            break;
            //
        case RS_CYCLE_STANDALONE:
            CV_WARN(g_codesysCVLog, nState, "PLCHandler[%s_main] Plc is running in standalone mode", GetDeviceInfo());
            // m_pCurrActPLCHandler = m_pPLCHandler;
            // return DEVLINK_SUCCESS;
            break;
        case RS_CYCLE_ERROR:
            CV_ERROR(g_codesysCVLog, nState,
                     "PLCHandler[%s_main] Plc is in error mode - error occured in cycle start or end", GetDeviceInfo());
            break;
        case RS_SIMULATION:
            CV_ERROR(g_codesysCVLog, nState,
                     "PLCHandler[%s_main] Plc is running in simulation mode -switched from standby", GetDeviceInfo());
            break;
        case RS_BOOTUP_ERROR:
            CV_ERROR(g_codesysCVLog, nState, "PLCHandler[%s_main] Plc is in error mode - error occured during boot up",
                     GetDeviceInfo());
            break;
        case RS_SHUTDOWN_ACTIVE:
            CV_ERROR(g_codesysCVLog, nState, "PLCHandler[%s_main] Plc is shutdown - was previously active",
                     GetDeviceInfo());
            break;
        case RS_SHUTDOWN_STANDBY:
            CV_ERROR(g_codesysCVLog, nState, "PLCHandler[%s_main] Plc is shutdown - was previously standby",
                     GetDeviceInfo());
            break;
        case RS_SYNCHRO_ERROR:
            CV_ERROR(g_codesysCVLog, nState,
                     "PLCHandler[%s_main] Plc is in error mode - error occured during synchronising", GetDeviceInfo());
            break;
        case RS_SIMULATION_START:
            CV_ERROR(g_codesysCVLog, nState,
                     "PLCHandler[%s_main] Plc is running in simulation mode - started in simulation mode",
                     GetDeviceInfo());
            break;
        case RS_NO_LICENSE:
            CV_ERROR(g_codesysCVLog, nState, "PLCHandler[%s_main] No valid license found", GetDeviceInfo());
            break;

        default:
            nState = -1;
            CV_ERROR(g_codesysCVLog, nState, "PLCHandler[%s_main] Plc unexpected redundant state!", GetDeviceInfo());
        }
        nStateMain = nState;
    }

    // ����״̬�쳣����鱸������״̬
    int nStateBack = -1;
    if (STATE_RUNNING == EnumStateBack)
    {
        int nState = GetPlcRedundantStatus(m_pPLCHandlerBackUp);
        switch (nState)
        {
        case RS_START:
            CV_ERROR(g_codesysCVLog, nState, "PLCHandler[%s_backup] Plc in startup", GetDeviceInfo());
            break;
        case RS_SYNCHRO:
            CV_ERROR(g_codesysCVLog, nState, "PLCHandler[%s_backup] Plc is synchronising", GetDeviceInfo());
            break;
        case RS_CYCLE_ACTIVE:
            CV_INFO(g_codesysCVLog, "PLCHandler[%s_backup] Plc is running in active mode", GetDeviceInfo());
            // m_pCurrActPLCHandler = m_pPLCHandlerBackUp;
            // return DEVLINK_SUCCESS;
            break;
        case RS_CYCLE_STANDBY:
            CV_WARN(g_codesysCVLog, nState, "PLCHandler[%s_backup] Plc is running in standby mode", GetDeviceInfo());
            break;
            //
        case RS_CYCLE_STANDALONE:
            CV_WARN(g_codesysCVLog, nState, "PLCHandler[%s_backup] Plc is running in standalone mode", GetDeviceInfo());
            // m_pCurrActPLCHandler = m_pPLCHandlerBackUp;
            // return DEVLINK_SUCCESS;
            break;
        case RS_CYCLE_ERROR:
            CV_ERROR(g_codesysCVLog, nState,
                     "PLCHandler[%s_backup] Plc is in error mode - error occured in cycle start or end",
                     GetDeviceInfo());
            break;
        case RS_SIMULATION:
            CV_ERROR(g_codesysCVLog, nState,
                     "PLCHandler[%s_backup] Plc is running in simulation mode -switched from standby", GetDeviceInfo());
            break;
        case RS_BOOTUP_ERROR:
            CV_ERROR(g_codesysCVLog, nState,
                     "PLCHandler[%s_backup] Plc is in error mode - error occured during boot up", GetDeviceInfo());
            break;
        case RS_SHUTDOWN_ACTIVE:
            CV_ERROR(g_codesysCVLog, nState, "PLCHandler[%s_backup] Plc is shutdown - was previously active",
                     GetDeviceInfo());
            break;
        case RS_SHUTDOWN_STANDBY:
            CV_ERROR(g_codesysCVLog, nState, "PLCHandler[%s_backup] Plc is shutdown - was previously standby",
                     GetDeviceInfo());
            break;
        case RS_SYNCHRO_ERROR:
            CV_ERROR(g_codesysCVLog, nState,
                     "PLCHandler[%s_backup] Plc is in error mode - error occured during synchronising",
                     GetDeviceInfo());
            break;
        case RS_SIMULATION_START:
            CV_ERROR(g_codesysCVLog, nState,
                     "PLCHandler[%s_backup] Plc is running in simulation mode - started in simulation mode",
                     GetDeviceInfo());
            break;
        case RS_NO_LICENSE:
            CV_ERROR(g_codesysCVLog, nState, "PLCHandler[%s_backup] No valid license found", GetDeviceInfo());
            break;

        default:
            nState = -1;
            CV_ERROR(g_codesysCVLog, nState, "PLCHandler[%s_backup] Plc unexpected redundant state!", GetDeviceInfo());
        }
        nStateBack = nState;
    }

    // 优先级：RS_CYCLE_STANDALONE > RS_CYCLE_ACTIVE
    if (RS_CYCLE_STANDALONE == nStateMain)
    {
        m_pCurrActPLCHandler = m_pPLCHandler;
        CV_INFO(g_codesysCVLog, "PLCHandler[%s]main  standalone", GetDeviceInfo());
        return DEVLINK_SUCCESS;
    }
    else if (RS_CYCLE_STANDALONE == nStateBack)
    {
        m_pCurrActPLCHandler = m_pPLCHandlerBackUp;
        CV_INFO(g_codesysCVLog, "PLCHandler[%s]backup standalone", GetDeviceInfo());
        return DEVLINK_SUCCESS;
    }
    else if (RS_CYCLE_ACTIVE == nStateMain)
    {
        m_pCurrActPLCHandler = m_pPLCHandler;
        CV_INFO(g_codesysCVLog, "PLCHandler[%s]main is active", GetDeviceInfo());
        return DEVLINK_SUCCESS;
    }
    else if (RS_CYCLE_ACTIVE == nStateBack)
    {
        m_pCurrActPLCHandler = m_pPLCHandlerBackUp;
        CV_INFO(g_codesysCVLog, "PLCHandler[%s]backup is active ", GetDeviceInfo());
        return DEVLINK_SUCCESS;
    }

    {
        // 如果上面判断没有一个符合读取数据的连接，并且是链接失败，则尝试更换ip 后重连。
        if (STATE_PLC_NOT_CONNECTED == EnumStateMain || STATE_PLC_NOT_CONNECTED == EnumStateBack)
        {
            if (++m_nConnectFailedCnt > g_nReconnectTimes)
            {
                if (STATE_PLC_NOT_CONNECTED == EnumStateMain)
                {
                    m_nMainIPIndex.fetch_add(1); // 链接失败了，切换到下一个IP
                }
                if (STATE_PLC_NOT_CONNECTED == EnumStateBack)
                {
                    m_nBackupIPIndex.fetch_add(1); // 链接失败了，切换到下一个IP
                }

                m_bReconnect.store(true);
                m_nConnectFailedCnt = 0;
            }
        }
    }

    CV_INFO(g_codesysCVLog, "PLCHandler[%s] both main and backup are unavailable!!!", GetDeviceInfo());
    return DEVLINK_FAILED;
}

const bool CCodesysDevice::isConnected()
{
    // if (m_pPLCHandler == NULL)
    //     m_pPLCHandler = new CPLCHandler(RTS_INVALID_HANDLE);

    // PLCHANDLER_STATE EnumState = m_pPLCHandler->GetState();

    // return (EnumState == STATE_PLC_NOT_CONNECTED) ? false : true;

    PLCHANDLER_STATE EnumState = STATE_PLC_NOT_CONNECTED;
    if (nullptr != m_pPLCHandler)
    {
        EnumState = m_pPLCHandler->GetState();
    }
    PLCHANDLER_STATE EnumStateBak = STATE_PLC_NOT_CONNECTED;
    if (nullptr != m_pPLCHandlerBackUp)
    {
        EnumStateBak = m_pPLCHandlerBackUp->GetState();
    }

    return (EnumState == STATE_PLC_NOT_CONNECTED && EnumStateBak == STATE_PLC_NOT_CONNECTED) ? false : true;
}

const char *CCodesysDevice::GetGroupName(const char *address)
{
    TagTypeDeviceTagMap::iterator it = m_mapTags.find(address);
    return (it != m_mapTags.end()) ? it->second.sGroupName.c_str() : NULL;
}

void CCodesysDevice::AddTag(const char *symbolName, const int nTypeCV, const char *grpName, int len)
{
    CTagTypeDeviceTag tag;
    tag.typeCV = nTypeCV;
    tag.sGroupName = grpName;
    tag.iSize = len;
    m_mapTags.insert(std::make_pair(std::string(symbolName), tag));
    CV_INFO(g_codesysCVLog, "PLCHandler[%s] AddTag %s ,nTypeCV %d, len %d.", GetDeviceInfo(), symbolName, nTypeCV, len);
}

void CCodesysDevice::FilterTagSymbols(bool bForce /*= false*/)
{
    static std::mutex mtx;
    std::lock_guard<std::mutex> lock(mtx); // lock the mutex to prevent concurrent access

    if (bForce || m_nLastSize != m_mapTags.size())
    {
        m_nLastSize = m_mapTags.size();
        std::vector<char *> matchedSymbols;

        for (size_t i = 0; i < m_ulNumOfSymbolsOrig; ++i)
        {
            const char *symbol = m_ppszVarsOrig[i];
            if (symbol == nullptr)
                continue;

            if (m_mapTags.find(symbol) != m_mapTags.end())
            {
                matchedSymbols.push_back(const_cast<char *>(symbol));
            }
        }
        if (m_ppszVars)
        {
            delete[] m_ppszVars;
            m_ppszVars = nullptr;
            m_ulNumOfSymbols = 0;
        }
        m_ulNumOfSymbols = matchedSymbols.size();
        m_ppszVars = new char *[m_ulNumOfSymbols];
        for (size_t i = 0; i < m_ulNumOfSymbols; ++i)
        {
            m_ppszVars[i] = matchedSymbols[i]; // 直接复用原始指针
        }

        CV_INFO(g_codesysCVLog, "PLCHandler[%s] FilterTagSymbols m_ulNumOfSymbolsOrig=%u, m_ulNumOfSymbols=%u.",
                GetDeviceInfo(), m_ulNumOfSymbolsOrig, m_ulNumOfSymbols);
    }
}
long CCodesysDevice::ReadTags(DRVHANDLE hDevice)
{
    /*
    The PLCHandler works connection based, that means it is only possible to send a service to the PLC,
    if there is an active connection. Internally this is managed by a state machine, which can be triggered
    from outside by calling Connect() or Disconnect(). This state machine is realized using an own background
    thread named ReconnectThread, which is created and started by the call of Connect() or if the PLCHandler
    leaves the state STATE_RUNNING because of a communication error, new project download to the PLC or an
    online change. The ReconnectThread is stopped, if the PLCHandler enters the state STATE_RUNNING after the
    connection is established successfully (again) or at the call of Disconnect(). At entering the state
    STATE_RUNNING, the PLCHandler starts a thread, which reads every 4 seconds the status of the PLC. This is
    done to prevent the PLC to close the channel again, because of inactivity of the client.
    */
    long lRet = GetStatus();
    if (lRet != DEVLINK_SUCCESS)
    {
        for (TagTypeDeviceTagMap::iterator it = m_mapTags.begin(); it != m_mapTags.end(); it++)
        {
            CV_TRACE(g_codesysCVLog, "PLCHandler[%s] tag [%s] GetStatus() = %d", GetDeviceInfo(), it->first.c_str(),
                     lRet);
            if (it->first.length() == 0 || it->second.sGroupName.length() == 0)
                continue;

            if (it->second.hDataBlock == NULL || it->second.pDataBlock == NULL)
            {
                it->second.hDataBlock = Drv_GetDataBlockByName(hDevice, it->second.sGroupName.c_str());
                it->second.pDataBlock = Drv_GetDataBlockInfo(it->second.hDataBlock);
            }

            if (it->second.hDataBlock != NULL && it->second.pDataBlock != NULL)
            {
                it->second.lQuality = DATA_STATUS_COMM_FAILURE;
                Drv_UpdateBlockStatus(hDevice, it->second.hDataBlock, DATA_STATUS_COMM_FAILURE);
            }
        }
        CV_ERROR(g_codesysCVLog, lRet, "PLCHandler[%s] all %d tags read failed.", GetDeviceInfo(), m_mapTags.size());
        Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
        return DEVLINK_FAILED;
    }
    Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
    FilterTagSymbols();
    // ����plc��ֵ
    if (0 == m_vecBackupIP.size())
    {
        if (this->ReadData() != DEVLINK_SUCCESS)
            return DEVLINK_FAILED;
    }
    // ����plc��ֵ
    else
    {
        if (this->ReadData(m_pCurrActPLCHandler) != DEVLINK_SUCCESS)
            return DEVLINK_FAILED;
    }

    for (TagTypeDeviceTagMap::iterator it = m_mapTags.begin(); it != m_mapTags.end(); it++)
    {
        if (it->first.length() == 0 || it->second.sGroupName.length() == 0)
            continue;

        if (it->second.hDataBlock == NULL || it->second.pDataBlock == NULL)
        {
            it->second.hDataBlock = Drv_GetDataBlockByName(hDevice, it->second.sGroupName.c_str());
            it->second.pDataBlock = Drv_GetDataBlockInfo(it->second.hDataBlock);
        }

        if (it->second.hDataBlock != NULL && it->second.pDataBlock != NULL)
        {
            if (it->second.nTypeMatch == TYPE_MATCH && it->second.lQuality == DATA_STATUS_OK)
            {
                DRVTIME drvTime;
                Drv_GetCurrentTime(&drvTime);
                Drv_UpdateBlockData(hDevice, it->second.hDataBlock, it->second.szData, DATA_STATUS_OK,
                                    it->second.pDataBlock->nElemNum, it->second.lQuality, &drvTime);
                CV_DEBUG(g_codesysCVLog, "PLCHandler[%s] Block[%s] UpdateData Success.", GetDeviceInfo(),
                         it->first.c_str());
            }
            else if (it->second.nTypeMatch == TYPE_MATCH && it->second.lQuality != DATA_STATUS_OK)
            {
                CV_TRACE(g_codesysCVLog, "PLCHandler[%s] tag [%s] lQuality not OK. %d %d", GetDeviceInfo(),
                         it->first.c_str(), it->second.nTypeMatch, it->second.lQuality);
                Drv_UpdateBlockStatus(hDevice, it->second.hDataBlock, it->second.lQuality);
            }
            else if (it->second.nTypeMatch == TYPE_NOT_MATCH && it->second.lQuality == DATA_STATUS_OK)
            {
                CV_TRACE(g_codesysCVLog, "PLCHandler[%s] tag [%s] type not match. %d %d", GetDeviceInfo(),
                         it->first.c_str(), it->second.nTypeMatch, it->second.lQuality);
                Drv_UpdateBlockStatus(hDevice, it->second.hDataBlock, DATA_STATUS_CONFIG_ERROR);
            }
            else if (it->second.nTypeMatch == TYPE_UNKOWN)
            {
                CV_TRACE(g_codesysCVLog, "PLCHandler[%s] tag [%s] type unkown, maybe this tag not in PLC. %d %d",
                         GetDeviceInfo(), it->first.c_str(), it->second.nTypeMatch, it->second.lQuality);
                Drv_UpdateBlockStatus(hDevice, it->second.hDataBlock, DATA_STATUS_CONFIG_ERROR);
            }
            else
            {
                CV_TRACE(g_codesysCVLog, "PLCHandler[%s] tag [%s] both type and lQuality not OK. %d %d",
                         GetDeviceInfo(), it->first.c_str(), it->second.nTypeMatch, it->second.lQuality);
                Drv_UpdateBlockStatus(hDevice, it->second.hDataBlock, DATA_STATUS_CONFIG_ERROR);
            }
        }
    }

    return DEVLINK_SUCCESS;
}

long CStateCallback::Notify(CPLCHandler *pPlcHandler, CallbackAddInfoTag CallbackAdditionalInfo)
{
    long lResult;

    if (pPlcHandler == NULL)
        return RESULT_FAILED;

    char *szPlcName = pPlcHandler->GetName();
    if (szPlcName == NULL)
        return RESULT_FAILED;

    TagTypeDeviceMap::iterator it = g_mapDevices.find(std::string(szPlcName));
    if (it == g_mapDevices.end())
    {
        // ���Բ�������PLC
        std::string strTemp = szPlcName;
        int nPos = strTemp.find("_main");
        if (nPos != std::string::npos)
        {
            strTemp = strTemp.substr(0, nPos);
        }
        else
        {
            nPos = strTemp.find("_backup");
            if (nPos != std::string::npos)
            {
                strTemp = strTemp.substr(0, nPos);
            }
        }

        it = g_mapDevices.find(strTemp);
        if (it == g_mapDevices.end())
            return RESULT_FAILED;
    }

    CCodesysDevice *pDevice = it->second;

    // if (!isReadyToCommWithPlc(g_mapDevices[szPlcName]))
    if (!isReadyToCommWithPlc(pDevice))
    {
        // pDevice->Disconnect();
        CV_INFO(g_codesysCVLog, "PLCHandler[%s] multilink=0,RM_STATUS_INACTIVE!", pDevice->GetDeviceInfo());
        return ICV_SUCCESS;
    }

    if (CallbackAdditionalInfo.ulType == PLCH_STATUS_CHANGE_CALLBACK)
    {
        lResult = CallbackAdditionalInfo.AddInf.lPLCHandlerState;
        if (lResult == STATE_RUNNING)
        {
            // get new symbols
            CV_INFO(g_codesysCVLog, "PLCHandler[%s][%s] (Re)Load symbols from PLC [STATE_RUNNING].",
                    pDevice->GetDeviceInfo(), pPlcHandler->GetName());
            // pDevice->ReadType();
            pDevice->ReadType(pPlcHandler);
        }
        else if (lResult == STATE_PLC_NOT_CONNECTED_SYMBOLS_LOADED)
        {
            CV_INFO(g_codesysCVLog,
                    "PLCHandler[%s][%s] STATE=%d [STATE_PLC_NOT_CONNECTED_SYMBOLS_LOADED]: PLC not connected, symbols "
                    "offline loaded\n",
                    pDevice->GetDeviceInfo(), pPlcHandler->GetName(), lResult);
            // pDevice->ReadType(true);
            pDevice->ReadType(pPlcHandler, true);
        }
        else
        {
            switch (lResult)
            {
            case STATE_PLC_NOT_CONNECTED:
                CV_INFO(
                    g_codesysCVLog,
                    "PLCHandler[%s][%s] STATE=%d [STATE_PLC_NOT_CONNECTED]: PLC not connected, tries to reconnect\n",
                    pDevice->GetDeviceInfo(), pPlcHandler->GetName(), lResult);
                break;
            case STATE_PLC_CONNECTED:
                CV_INFO(g_codesysCVLog,
                        "PLCHandler[%s][%s] STATE=%d [STATE_PLC_CONNECTED]: PLC connected, tries to load symbols\n",
                        pDevice->GetDeviceInfo(), pPlcHandler->GetName(), lResult);
                break;
            case STATE_NO_SYMBOLS:
                CV_INFO(
                    g_codesysCVLog,
                    "PLCHandler[%s][%s] STATE=%d [STATE_NO_CONFIG]: No symbol information available or not uptodate "
                    "(no project?)\n",
                    pDevice->GetDeviceInfo(), pPlcHandler->GetName(), lResult);
                break;
            case STATE_SYMBOLS_LOADED:
                CV_INFO(g_codesysCVLog,
                        "PLCHandler[%s][%s] STATE=%d [STATE_CONFIG_LOADED]: Symbol information is loaded, checks "
                        "projectId\n",
                        pDevice->GetDeviceInfo(), pPlcHandler->GetName(), lResult);
                break;
            default:
                break;
            }
        }
    }
    return RESULT_OK;
}

const int CCodesysDevice::GetPlcRedundantStatus(CPLCHandler *pPLCHandler)
{
    if (NULL == pPLCHandler)
        return DEVLINK_FAILED;

    long lRet = RESULT_OK;
    int nPlcRedundantState = -1;

    // raw szRedundantState:Application.PersistentVars.RedundantState
    // 2022.10.27 update szRedundantState:Application.PersistentVars.RedundantState.eRedundancyState
    //  const char* szRedundantState = "Application.PersistentVars.RedundantState.eRedundancyState";
    //  const char *szRedundantState = "Application.PersistentVars.redu.eRedundancyState";
    const char *szRedundantState = m_szRedundantState.c_str();

    //	PlcSymbolDesc *pSymbols = NULL;
    // 	long lRet = m_pPLCHandler->EnterItemAccess();
    // 	if(RESULT_OK == lRet)
    // 	{
    // 		lRet = m_pPLCHandler->GetItem((char*)szRedundantState,pSymbols);
    // 		if(RESULT_OK == lRet)
    // 		{
    //
    // 		}
    // 		m_pPLCHandler->LeaveItemAccess();
    // 	}

    char *ppszVars[1];
    unsigned long ulNumOfValues = 1;
    PlcVarValue **ppValues = NULL;
    ppszVars[0] = (char *)szRedundantState;

    if (pPLCHandler == m_pPLCHandler)
    {
        if (m_nCodesysReadType == CODESYS_READ_TYPE_CYCLE)
        {
            if (m_hVarListRed == NULL)
            {
                // ����ѭ����ȡ
                m_hVarListRed = pPLCHandler->CycDefineVarList(ppszVars, 1, 500, NULL, NULL, 0, 0);
            }

            pPLCHandler->CycEnterVarAccess(m_hVarListRed);

            lRet = pPLCHandler->CycReadVars(m_hVarListRed, &ppValues, &ulNumOfValues);
            if (lRet != RESULT_NO_UPDATE && lRet != RESULT_OK)
            {
                pPLCHandler->CycLeaveVarAccess(m_hVarListRed);
                pPLCHandler->CycDeleteVarList(m_hVarListRed);
                m_hVarListRed = NULL;
                CV_ERROR(g_codesysCVLog, lRet, "PLCHandler[%s] get redundant status failed.", GetDeviceInfo());
                return nPlcRedundantState;
            }
        }
        else
        {
            if (m_hVarListRed == NULL)
            {
                m_hVarListRed = pPLCHandler->SyncReadVarsFromPlc(ppszVars, 1, &ppValues, &ulNumOfValues);
                lRet = RESULT_OK;
            }
            else
            {
                lRet = pPLCHandler->SyncReadVarsFromPlc(m_hVarListRed, &ppValues, &ulNumOfValues);
            }
        }

        // ������״̬ active = 2 ,standby =3 ,standalone = 4
        if (lRet == RESULT_OK && ppValues != NULL && ulNumOfValues == 1)
        {
            if (ppValues[0]->bQuality)
            {
                nPlcRedundantState = *(int *)ppValues[0]->byData;
            }
        }

        if (m_nCodesysReadType == CODESYS_READ_TYPE_CYCLE)
        {
            pPLCHandler->CycLeaveVarAccess(m_hVarListRed);
        }
    }
    else if (pPLCHandler == m_pPLCHandlerBackUp)
    {
        if (m_nCodesysReadType == CODESYS_READ_TYPE_CYCLE)
        {
            if (m_hVarListRedBackUp == NULL)
            {
                // ����ѭ����ȡ
                m_hVarListRedBackUp = pPLCHandler->CycDefineVarList(ppszVars, 1, 500, NULL, NULL, 0, 0);
            }

            pPLCHandler->CycEnterVarAccess(m_hVarListRedBackUp);

            lRet = pPLCHandler->CycReadVars(m_hVarListRedBackUp, &ppValues, &ulNumOfValues);
            if (lRet != RESULT_NO_UPDATE && lRet != RESULT_OK)
            {
                pPLCHandler->CycLeaveVarAccess(m_hVarListRedBackUp);
                pPLCHandler->CycDeleteVarList(m_hVarListRedBackUp);
                m_hVarListRedBackUp = NULL;
                CV_ERROR(g_codesysCVLog, lRet, "PLCHandler[%s][%s] get redundant status failed.", GetDeviceInfo(),
                         pPLCHandler->GetName());
                return nPlcRedundantState;
            }
        }
        else
        {
            if (m_hVarListRedBackUp == NULL)
            {
                m_hVarListRedBackUp = pPLCHandler->SyncReadVarsFromPlc(ppszVars, 1, &ppValues, &ulNumOfValues);
                lRet = RESULT_OK;
            }
            else
            {
                lRet = pPLCHandler->SyncReadVarsFromPlc(m_hVarListRedBackUp, &ppValues, &ulNumOfValues);
            }
        }

        // ������״̬ active = 2 ,standby =3 ,standalone = 4
        if (lRet == RESULT_OK && ppValues != NULL && ulNumOfValues == 1)
        {
            if (ppValues[0]->bQuality)
            {
                nPlcRedundantState = *(int *)ppValues[0]->byData;
            }
        }

        if (m_nCodesysReadType == CODESYS_READ_TYPE_CYCLE)
        {
            pPLCHandler->CycLeaveVarAccess(m_hVarListRedBackUp);
        }
    }
    return nPlcRedundantState;
}