#include "TaskDrvDevStatus.h"
#include "ace/High_Res_Timer.h"

extern CCVLog g_CVDrvierCommonLog;

CTaskDrvDevStatus::CTaskDrvDevStatus(void)
{
	ACE_Select_Reactor *pSelectReactor = new ACE_Select_Reactor();
	m_pReactor = new ACE_Reactor(pSelectReactor, true);
	m_pReactor->timer_queue(&m_timerQueue);
	reactor(m_pReactor);

	m_pDrvDevStatusTimer = new CDrvDevStatusTimer(this);
	m_bExit = false;
}


CTaskDrvDevStatus::~CTaskDrvDevStatus(void)
{
	if(m_pDrvDevStatusTimer)
	{
		delete m_pDrvDevStatusTimer;
		m_pDrvDevStatusTimer = NULL;
	}

	if(m_pReactor)	
	{
		delete m_pReactor;
		m_pReactor = NULL;
	}
}

long CTaskDrvDevStatus::Start()
{
	m_bExit = false;
	int nRet = this->activate(THR_NEW_LWP | THR_JOINABLE |THR_INHERIT_SCHED);
	if (0 == nRet)
		return DRV_SUCCESS;

	return nRet;
}

void CTaskDrvDevStatus::Stop() 
{
	this->m_bExit = true;

	this->reactor()->end_reactor_event_loop();
	this->wait();
	CV_INFO(g_CVDrvierCommonLog, "DrvDevStatus thread exit!");
}


int CTaskDrvDevStatus::svc()
{
	this->reactor()->owner(ACE_OS::thr_self ());

	OnStart();
	while (!m_bExit)
	{
		this->reactor()->reset_reactor_event_loop();
		this->reactor()->run_reactor_event_loop(ReactorEventHook);
	}

	OnStop();
	return 0;
}

void CTaskDrvDevStatus::OnStart()
{
	if(m_pDrvDevStatusTimer)
		m_pDrvDevStatusTimer->Start();
}

void CTaskDrvDevStatus::OnStop()
{
	if(m_pDrvDevStatusTimer)
		m_pDrvDevStatusTimer->Stop();

	m_pReactor->close();
}
