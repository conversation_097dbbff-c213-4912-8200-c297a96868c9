/**************************************************************
 *  Filename:    Device.cpp
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: �豸��Ϣʵ����.
 *
 *  @author:     lijingjing
 *  @version     05/28/2008  lijingjing  Initial Version
 *  @version	10/26/2012  baoyuansong �޸�����̳�˳������ʹ���˶��ؼ̳У���ɵ���void*ָ�루ʵ��ָ���Ӷ���
 *  ǿ��תΪ�ǵ�һ���̳еĸ�ָ�����ʱ���������´���
 * class A{};class B{};class C:pulic A,public B{};
 * void *p = new C;
 * B *pB = (B *)p;
 * ��ʱ����B�ķ���ȥ����B���ڴ�ʵ���޸ĵ��п�����A���ڴ棬�����Ǵ�λ��B���ڴ�
 *  ������������ݷ��ʴ���.���ǵ�CDataBlock�࣬CDevice�඼�̳���CDrvObjectBase�����ڶ��û��ṩ�ӿ�ʱʹ�õ�
 * ��void*ָ�룬�������õ��û��������ǵ�void *ָ��ʱ�����ǲ���֪����ʵCDataBlock�໹��CDevice�࣬���ֱ��ת��
 * ΪCDrvObjectBaseָ������ȡ��������Ӧ���ݾͻ�������������ǽ��̳�˳������ˣ�����ȷ��ֱ��ת��ΪCDrvObjectBase����
 * ȷʵ��ָ��CDrvObjectBase�ڴ�
 **************************************************************/

#include "Device.h"
#include "TaskGroup.h"
#include "common/CommHelper.h"
#include "common/cvGlobalHelper.h"
#include "errcode/error_code.h"
#include <ace/ACE.h>
#include "common/RMAPI.h"
#include "string.h"
#include <ace/OS_NS_strings.h>
#include "common/gettimeofday.h"
#include "gettext/libintl.h"
#include "math.h"
#include "common/OPAPI.h"

//#define _(STRING) gettext(STRING)
#define _(STRING) STRING
// extern STATERWINSTANCE g_pStateRW;
extern CCVLog g_CVDrvierCommonLog;
extern bool  g_bMultiLink;
extern bool g_bNeedTrace;
extern std::map<long, string>  g_mapTraceTag;//id name
extern ACE_Recursive_Thread_Mutex g_mtxMapTraceTags;


/**
 *  ���캯��.
 *
 *
 *  @version     05/30/2008  lijingjing  Initial Version.
 */
CDevice::CDevice(CTaskGroup *pTaskGrp, const char *pszDeviceName):m_pTaskGroup(pTaskGrp)
{
	// �豸�Ƿ�֧�ֶ�����
	m_bMultiLink = false;  
	m_nDevObjectType = CONFIGOBJ_TYPE_DEVICE;
	
	m_strName = "";
	m_strName = pszDeviceName;

	m_strDesc = "";
	m_bConfigChanged = false; // ��һ�������ӣ������Ϊtrue�����ӡ��رա�������
	m_tvTimerBegin = CVLibHead::gettimeofday_tickcnt();
	m_nPollRate = 0;
	m_pDeviceConnection = NULL;
	m_strTaskID = "";

	m_pfnRecvCallback = NULL;
	m_lCallbackParam = 0;
	memset(m_szCallbackParam, 0x00, ICV_DEVICENAME_MAXLEN);
	m_pCallbackParam = NULL;
	m_nRecvTimeOut = 0;

	this->reference_counting_policy().value(ACE_Event_Handler::Reference_Counting_Policy::ENABLED);
}


// ��ʱ��ѯ�����ڵ���
int CDevice::handle_timeout(const ACE_Time_Value &current_time, const void *act)
{
	if (m_pTaskGroup != NULL)
		m_pTaskGroup->HandleWriteCmd();

	if (g_pfnOnReadData)
		g_pfnOnReadData((void *)this, NULL);
	else if (g_pfnOnDeviceTimer)
		g_pfnOnDeviceTimer((void *)this);
	else
		CV_ERROR(g_CVDrvierCommonLog, -1, _("[Device:%s]It found the OnReadData or OnDeviceTimer function was undefined when the device timer reached!"), // �豸: %s, �豸��ʱ������ʱ��������δ����OnReadData����OnDeviceTimer����!
				 this->m_strName.c_str());

	return DRV_SUCCESS;
}

/**
 *  ��������.
 *
 *
 *  @version     05/30/2008  lijingjing  Initial Version.
 */
CDevice::~CDevice()
{
	std::map<std::string, CDataBlock*>::iterator iterBlks = m_mapDataBlocks.begin();
	for (; iterBlks != m_mapDataBlocks.end(); iterBlks++)
	{
		CDataBlock * pDataBlock = (CDataBlock *)iterBlks->second;
		SAFE_DELETE(pDataBlock);
	}
	m_mapDataBlocks.clear();

	// ɾ���豸���ӣ�
	if(m_pDeviceConnection)
		delete m_pDeviceConnection;
	m_pDeviceConnection = NULL;
}

CDevice & CDevice::operator=( CDevice &theDevice )
{
	CDrvObjectBase::operator=(theDevice);
	m_nPollRate = theDevice.m_nPollRate;	// ��ѯʱ��
	m_strConnType = theDevice.m_strConnType;	// ��������
	m_strConnParam = theDevice.m_strConnParam;	// ���Ӳ���
	m_nRecvTimeOut = theDevice.m_nRecvTimeOut;	// ����Ϊ��λ
	m_bMultiLink = theDevice.m_bMultiLink;	// �豸�Ƿ�֧�ֶ�����
	m_strTaskID = theDevice.m_strTaskID;
	// m_pDeviceConnection = NULL; ������Ϊ�գ���Ϊ��������ʱ����ʹ����û�иı�Ҳ����뵽������
	return *this;
}

// ����������һ�����Զ�������λ֮��
void  CDevice::StartTimer()
{
	m_pTaskGroup->m_pReactor->cancel_timer(this);
	// �趨�豸�ϵĶ�ʱ��
	if(m_nPollRate > 0)
	{
		ACE_Time_Value	tvPollRate;		// ɨ�����ڣ���λms
		tvPollRate.msec(m_nPollRate);
		ACE_Time_Value tvStart = ACE_Time_Value::zero; 
		m_pTaskGroup->m_pReactor->schedule_timer(this, NULL, ACE_Time_Value::zero, tvPollRate);
		CV_INFO(g_CVDrvierCommonLog, "device %s schedule_timer pollrate %d", m_strName.c_str(), m_nPollRate);
	}

	map<string, CDataBlock*>::iterator iterBlk = m_mapDataBlocks.begin();
	for (iterBlk = m_mapDataBlocks.begin(); iterBlk != m_mapDataBlocks.end(); iterBlk++)
	{
		iterBlk->second->StartTimer();
	}
	 CV_INFO(g_CVDrvierCommonLog, "device %s StartTimer", m_strName.c_str());
}

void CDevice::Start()
{
	// �������ݿ�Ŀ�ʼʱ��.ֻ����һ��
	m_tvTimerBegin = CVLibHead::gettimeofday_tickcnt();

	//m_strConnParam += ";";
    string strConnParam = m_strConnParam + ";";
	// ip=127.0.0.1;ip2=***********;port=102;multiLink=1
	char *szConnType = (char *)m_strConnType.c_str();
	if(ACE_OS::strcasecmp(szConnType, "tcpclient") == 0)
	{
		CTcpClientHandler *pTcpClientHandler = new CTcpClientHandler();
		// ��Socket
		pTcpClientHandler->m_strDeviceName = m_strName;
		pTcpClientHandler->reactor(m_pTaskGroup->m_pReactor);
		if (m_pfnRecvCallback != NULL)
			pTcpClientHandler->SetRecvCallBackFunc(m_pfnRecvCallback, m_pCallbackParam, m_lCallbackParam, m_szCallbackParam);
		m_pDeviceConnection = pTcpClientHandler;
		m_pDeviceConnection->SetConnectParam((char *)strConnParam.c_str());
		GetMultiLink((char *)strConnParam.c_str());
		m_pDeviceConnection->Start();
	}
	else if(ACE_OS::strcasecmp(szConnType, "udpclient") == 0)
	{
		CUdpClientHandler *pUdpClientHandler = new CUdpClientHandler();
		// 设置设备名称
		pUdpClientHandler->m_strDeviceName = m_strName;
		m_pDeviceConnection = pUdpClientHandler;
		m_pDeviceConnection->SetConnectParam((char *)strConnParam.c_str());
		m_pDeviceConnection->Start();
	}
	else if(ACE_OS::strcasecmp(szConnType, "serial") == 0)
	{
		m_pDeviceConnection = new CSerialPort();
		m_pDeviceConnection->SetConnectParam((char *)strConnParam.c_str());
		m_bMultiLink = false;
		long lRet = m_pDeviceConnection->Start();
		std::string strSubKeyPrefix = g_strDrvName;
		strSubKeyPrefix += "#";
		strSubKeyPrefix += m_strName;
		// drv_redis_batchbegin();
		// drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_NAME, m_strName.c_str());
		//drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_CONSTATUS, lRet);
		// drv_redis_batchaddtime(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_STATUS);
		// drv_redis_batchsubmit();

		// TCV_TimeStamp timeStamp;
		// ACE_Time_Value timeValue = ACE_OS::gettimeofday();
		// timeStamp.tv_sec = (uint32_t)timeValue.sec();
		// timeStamp.tv_usec = (uint32_t)timeValue.usec();
		// char szTime[ICV_HOSTNAMESTRING_MAXLEN] = {'\0'};
		// cvcommon::CastTimeToASCII(szTime, ICV_HOSTNAMESTRING_MAXLEN, timeStamp);
		// char szKey[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#%s#status", g_strDrvName.c_str(), m_strName.c_str());
		// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", lRet, szTime);
		// STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);

	}
	else if (ACE_OS::strcasecmp(szConnType, "udpserver") == 0)
	{
		m_pDeviceConnection = new CUdpServerHandler();
		m_pDeviceConnection->SetConnectParam((char *)strConnParam.c_str());
		GetMultiLink((char *)strConnParam.c_str());
		m_pDeviceConnection->Start();
	}
	else if(ACE_OS::strcasecmp(szConnType, "tcpserver") == 0) // ip=127.0.0.1;ip=127.0.0.1/***********;port=102;
	{
		m_pDeviceConnection = NULL;	// �ȴ�����������ɺ���ܱ���ֵ
		unsigned short uPort = 0;
		list<string> listDeviceIP;
		int nRet = ParseDeviceConnStrOfTcpServer(uPort);
		if(nRet != ICV_SUCCESS)
		{
            //�豸 %s ��ΪTCPServer��������, ���ִ����������
			CV_ERROR(g_CVDrvierCommonLog, -1,_("It Found an error when device %s connected as TCPServer, Please check the parameters."), m_strName.c_str());
			return;
        } 

		// �����δ�������������������
		if(m_pTaskGroup->m_uListenPort == 0)
		{
			nRet = m_pTaskGroup->ListenOnPort(uPort);
			if(nRet != ICV_SUCCESS)
			{
				Drv_LogMessage(LOG_LEVEL_ERROR, _("It Found an error when device %s connected as TCPServer, listen on port %d failed."), m_strName.c_str(), uPort);//�豸 %s ��ΪTCPServer��������, �����ڶ˿� %d ʧ��
				//return;
			}
		}
		else if(m_pTaskGroup->m_uListenPort != 0 && m_pTaskGroup->m_uListenPort != uPort)
		{
			Drv_LogMessage(LOG_LEVEL_ERROR, _("Device(%s),Port(%d),ͬһ������²�ͬ�豸ֻ��������ͬһ������ţ�"), m_strName.c_str(), uPort);
		}
		else
		{
			Drv_LogMessage(LOG_LEVEL_ERROR, _("Device(%s),Port(%d),ͬһ������²�ͬ�豸ֻ��Ҫ����һ�ζ˿ںţ�"), m_strName.c_str(), uPort);

		}
	}
	else
	{
		m_pDeviceConnection = NULL;	
		GetMultiLinkWithDefaultValue((char *)strConnParam.c_str());//other��ʽmultilinkĬ��Ϊtrue������ά��ԭ�����߼� by wangyadong 20150106
        CV_INFO(g_CVDrvierCommonLog, _("You used other connections mode, parameter:%s"), m_strConnParam.c_str());//���������������ӷ�ʽ������ %s
	}

	StartTimer();
}

// ip=127.0.0.1;ip=127.0.0.1/***********;port=102;
int CDevice::ParseDeviceConnStrOfTcpServer(unsigned short &uPort)
{
	m_mapDeviceIPsOfTcpServer.clear();
    string strConnParam = m_strConnParam + ";";

	int nPos = strConnParam.find("port=");
	if(nPos == string::npos)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("It Found an error when device %s connected as TCPServer, can not find port %d parameter."), m_strName.c_str());//�豸 %s ��ΪTCPServer��������, �Ҳ���port����
		return -1;
	}

	string strPortNo = strConnParam.substr(nPos + strlen("port="));
	nPos = strPortNo.find(';');
	if(nPos != strPortNo.npos)
		strPortNo = strPortNo.substr(0, nPos);

	if(strPortNo.empty())
	{
        //�豸 %s ��ΪTCPServer��������, �Ҳ���port=����Ķ˿ںŲ���
		CV_ERROR(g_CVDrvierCommonLog, -2,  _("It Found an error when device %s connected as TCPServer, can not find port number parameter."), m_strName.c_str());
		return -2;
	}

	uPort = ::atoi(strPortNo.c_str());

	strConnParam = m_strConnParam + ";";
	// �Ϸ�����ip=127.xx.xx.xx/129.xx.xx.xx.xx/10.2.xx.xx;ip=128.xx.xx.xx/129.xx;ip=.....
	nPos = strConnParam.find("ip=");
	while(nPos != string::npos)
	{
		strConnParam = strConnParam.substr(nPos + strlen("ip=")); // 127.0.0.1/129.xx;ip=xxx.port=502;
		string strDevicesIP = strConnParam;
		int nPosEnd = strDevicesIP.find(';'); 
		if(nPosEnd != string::npos)
		{
			strConnParam = strDevicesIP.substr(nPosEnd + 1);
			strDevicesIP = strDevicesIP.substr(0, nPosEnd);
		}
		else
			strConnParam = "";

		// ����strDevicesIP�����ܺ��ж��*********/*********/....////
		strDevicesIP += "/";
		int nPosIP = strDevicesIP.find('/');
		while(nPosIP != string::npos)
		{
			string strDeviceIP = strDevicesIP.substr(0, nPosIP);
			if(!strDeviceIP.empty()) // avoding  �ո�
				m_mapDeviceIPsOfTcpServer[strDeviceIP] = strDeviceIP;
			strDevicesIP = strDevicesIP.substr(nPosIP + 1);
			nPosIP = strDevicesIP.find('/');
		}

		// ��������û��ip= ����
		nPos = strConnParam.find("ip=");
	}

	return ICV_SUCCESS;
}

void CDevice::Stop()
{
	if (m_nPollRate > 0)
	{
		m_pTaskGroup->m_pReactor->cancel_timer(this);// ���ڵ��豸��˵��ȡ���豸�ϵĶ�ʱ�� by wangyadong 20141218
		 CV_INFO(g_CVDrvierCommonLog, "device %s cancel_timer pollrate %d", m_strName.c_str(), m_nPollRate);
	}

	std::map<std::string, CDataBlock*>::iterator iterBlk = m_mapDataBlocks.begin();
	for (; iterBlk != m_mapDataBlocks.end(); iterBlk++)
		iterBlk->second->StopTimer();

	// Drv_LogMessage(DRV_LOGLEVEL_ERROR, "ֹͣ�豸����, �Ͽ��豸%s����!", m_strName.c_str());
	if(m_pDeviceConnection)
	{
		// char szDevice[ICV_IOADDR_MAXLEN];
		// memset(szDevice, 0x00, ICV_IOADDR_MAXLEN);
		// ACE_OS::snprintf(szDevice, sizeof(szDevice), "driver#%s#%s#*", g_strDrvName.c_str(), m_strName.c_str());
		// STATERW_DelStringCommand(&g_pStateRW, szDevice);
		m_pDeviceConnection->Stop();
	}
	 CV_INFO(g_CVDrvierCommonLog, "device %s Stop", m_strName.c_str());
}

static const char HEX[16] = {
	'0', '1', '2', '3',
	'4', '5', '6', '7',
	'8', '9', 'a', 'b',
	'c', 'd', 'e', 'f'
};
unsigned int HexDumpBuf( const unsigned char *szBuffer, unsigned int nBuffeLen, char *szHexBuf, unsigned int *pnHexBufLen )
{
	if (NULL == szBuffer || NULL == pnHexBufLen)
		return -1;

	if (NULL == szHexBuf)
	{
		*pnHexBufLen = 2*nBuffeLen;
		return EC_ICV_COMM_BUFFERTOOSHORT;
	}

	memset(szHexBuf, 0, *pnHexBufLen);

	//assure there is a terminal character
	if (*pnHexBufLen < 2*nBuffeLen)
	{
		*pnHexBufLen = 2*nBuffeLen ;
		return EC_ICV_COMM_BUFFERTOOSHORT;
	}

	for(unsigned int i = 0; i < nBuffeLen; i++) 
	{
		unsigned char t = szBuffer[i];
		szHexBuf[i*2] = HEX[t/16];
		szHexBuf[i*2 + 1] = HEX[t%16];
	}

	szHexBuf[2*nBuffeLen] = 0;

	return ICV_SUCCESS;
}

long CDevice::SaveWriteMsg(const CDataBlock *pDataBlock, long nErr, int nTagByteOffset, int nTagBitOffset, char *szCmdData, int nCmdDataBits)
{
	if (nCmdDataBits <= 0)
	{
		return ICV_SUCCESS;
	}

	string strKey = g_strDrvName;
	strKey += ".";
	strKey += m_strName;

	TiXmlDocument xmlDoc;
	TiXmlElement xmlRootElm("writemsg");

	char szHexBuffer[1024] = {'\0'};
	unsigned int nHexBufferLen = sizeof(szHexBuffer);
	cvcommon::HexDumpBuf((const unsigned char*)szCmdData, (unsigned int)ceil(nCmdDataBits/8.0), szHexBuffer, &nHexBufferLen);

	xmlRootElm.SetAttribute("value", szHexBuffer);
	xmlRootElm.SetAttribute("app", g_strDrvName.c_str());
	xmlRootElm.SetAttribute("device", m_strName.c_str());
	xmlRootElm.SetAttribute("datablock", pDataBlock->m_strName.c_str());
	xmlRootElm.SetAttribute("address", pDataBlock->m_strAddress.c_str());
	xmlRootElm.SetAttribute("byteoffset", nTagByteOffset);
	xmlRootElm.SetAttribute("bitoffset", nTagBitOffset);
	xmlRootElm.SetAttribute("bitswritten", nCmdDataBits);

	ACE_Time_Value timeValue = ACE_OS::gettimeofday();
	TCV_TimeStamp timeStamp;
	timeStamp.tv_sec = (uint32_t)timeValue.sec();
	timeStamp.tv_usec = (uint32_t)timeValue.usec();
	char szDateTime[ICV_HOSTNAMESTRING_MAXLEN] = {'\0'};
	cvcommon::CastTimeToASCII(szDateTime, ICV_HOSTNAMESTRING_MAXLEN, timeStamp);

	xmlRootElm.SetAttribute("timestamp", szDateTime);
	xmlRootElm.SetAttribute("errno", nErr);

	xmlDoc.InsertEndChild(xmlRootElm);

	TiXmlPrinter printer;

	// attach it to the document you want to convert in to a std::string 
	xmlDoc.Accept(&printer);

	// Create a std::string and copy your document data in to the string    
	// const char* szValue = printer.CStr();

	// long lRet = STATERW_RPUSHCommand(&g_pStateRW, strKey.c_str(), szValue);
	// if (lRet != ICV_SUCCESS)
	// {
	// 	CV_INFO(g_CVDrvierCommonLog, "STATERW_RPUSHCommand ERROR: RPUSH %s %s", strKey.c_str(), szValue);
	// }

	// lRet = STATERW_EXPIRECommand(&g_pStateRW, strKey.c_str(), 2592000);
	// if (lRet != ICV_SUCCESS)
	// {
	// 	CV_INFO(g_CVDrvierCommonLog, "STATERW_EXPIRECommand ERROR: EXPIRE %s %d", strKey.c_str(), 2592000);
	// }
	// return lRet;
	return 0;
}

void CDevice::OnWriteCommand(string strBlockName, string strTagName, string strTagAddr, int32 nTagID, uint8 nDataType, uint8 nTagType, int nTagByteOffset, int nTagBitOffset, char* szCmdData, int nCmdDataBits)
{
	int nCmdDataBytes = (int)ceil(nCmdDataBits/8.0f);
	// �õ����ݿ�����
	map<string, CDataBlock*>::iterator itDataBlock = m_mapDataBlocks.find(strBlockName);
	if(itDataBlock == m_mapDataBlocks.end())
	{
		//�������ݿ����� %s ���豸 %s ��δ�ҵ����ݿ�
		g_CVDrvierCommonLog.LogMessage(LOG_LEVEL_ERROR, _("According to the datablock name %s, it found no datablock in the device %s!"), m_strName.c_str(), strBlockName.c_str());
		return;
	}
	CDataBlock *pDataBlock = itDataBlock->second;
	long lCmdID = 0;
	CV_INFO(g_CVDrvierCommonLog, "OnWriteCommand Start:blockname %s, TagName %s, nTagID %d, nTagByteOffset %d nTagBitOffset %d nCmdDataBits %d",
		strBlockName.c_str(), strTagName.c_str(), nTagID, nTagByteOffset, nTagBitOffset, nCmdDataBits);
	long lStatus;
	if (g_pfnOnWriteCmdEx)
		lStatus = g_pfnOnWriteCmdEx(this, pDataBlock, strTagName, strTagAddr, nTagID, nDataType, nTagType, nTagByteOffset, nTagBitOffset, szCmdData, nCmdDataBits);
	else
		lStatus = g_pfnOnWriteCmd(this, pDataBlock, nTagByteOffset, nTagBitOffset, szCmdData, nCmdDataBits);
	CV_INFO(g_CVDrvierCommonLog, "OnWriteCommand End:blockname %s, TagName %s, nTagID %d, nTagByteOffset %d nTagBitOffset %d nCmdDataBits %d",
		strBlockName.c_str(), strTagName.c_str(), nTagID, nTagByteOffset, nTagBitOffset, nCmdDataBits);

	pDataBlock->UpdateBlockBuffer(szCmdData, nTagByteOffset, nTagBitOffset, nCmdDataBits);
	//SaveWriteMsg(pDataBlock, lStatus, nTagByteOffset, nTagBitOffset, szCmdData, nCmdDataBits);
	//STATERW_CtrlEventCommand(&g_pStateRW, g_strDrvName.c_str(), strField.c_str(), g_strDrvName.c_str(), szBuffer, nBufferLen, lStatus);

	// 		// ����д����
	// 		pDataBlock->m_nWriteCount ++;
	// 		CDataArea *pDataArea = g_pDIT->GetDataArea();
	// 		if (NULL != pDataArea)
	// 		{
	// 			long lRet = pDataArea->SetDataBlockData_i(pDataBlock->m_nDITBlockNumber, sizeof(int), 
	// 				(void*)&(pDataBlock->m_nWriteCount), pDataBlock->m_nDITBlockSize); //  - sizeof(TCV_TimeStamp)
	// 			if(lRet != DRV_SUCCESS)
	// 				g_CVDrvierCommonLog.LogErrMessage(lRet, _("Update datablock writecount in DIT error for the device (%s), the datablock (%s)!"),m_strName.c_str(), pDataBlock->m_strName.c_str());//���豸(%s)�����ݿ�(%s)����DIT��д��������!
	// 		}
	// 		
	// 		// ���ӿ��Ʒ�����¼
	// 		OUTPUT_ACK_DESC outputAck;
	// 		outputAck.nIpn = lCmdID;
	// 		outputAck.nStatus = lStatus;
	// 
	// 		COutputAckArea* pOutputAckArea = g_pDIT->GetOutputAckArea();
	// 		if (NULL == pOutputAckArea)
	// 			return;
	// 
	// 		pOutputAckArea->AddOutputAck(&outputAck);
	// 
	//������·׷��
	if (g_bNeedTrace)
	{
		ACE_Guard<ACE_Recursive_Thread_Mutex> lockguard(g_mtxMapTraceTags);
		{
			std::map<long, string>::iterator itMap = g_mapTraceTag.find(nTagID);
			if (itMap != g_mapTraceTag.end())
			{
				TRACE_TAG_MSG tracetagmsg;
				strcpy(tracetagmsg.szTagName, itMap->second.c_str());
				strcpy(tracetagmsg.szModName, "driver");
				tracetagmsg.time = ACE_OS::gettimeofday();
				tracetagmsg.bStatus = TRATAG_STATUS_GOOD;

				char szBuffer[ICV_BLOBVALUE_MAXLEN];
				unsigned int nHexBufferLen = ICV_BLOBVALUE_MAXLEN;
				memset(szBuffer, 0, sizeof(szBuffer));
				cvcommon::HexDumpBuf((unsigned char*)szCmdData, nCmdDataBytes, szBuffer, &nHexBufferLen);
				if (ICV_SUCCESS == lStatus)
					sprintf(tracetagmsg.strMsg, "control cmd. to device success(%d).hex value:%s", lStatus, szBuffer);
				else
				{
					tracetagmsg.bStatus = TRATAG_STATUS_BAD;
					sprintf(tracetagmsg.strMsg, "control cmd. to device failed(%d).hex value:%s", lStatus, szBuffer);
				}

				// int nRet = OP_API_Write_Trace_Tags_Msg(tracetagmsg);
				// CV_CHECK_FAIL_LOG(g_CVDrvierCommonLog, nRet, nRet, "OP_API_Write_Trace_Tags_Msg");
			}
		}
	}
}

bool IsActiveHost()
{	
	return 1;
	long lStatus = 0;
	GetRMStatus(&lStatus);
	return lStatus != 0 ;
}

int CDevice::SendToDevice(char *szBuffer, long lBufLen, long lTimeOutMS )
{
	// ���߲�������豸��Ϣ�����ı䣬��Ҫ���������豸
	if(!m_bMultiLink && !IsActiveHost())
	{
        //���豸��������ʧ��(���� %d ���ֽڣ���ʱ %d ), ���豸�ǵ������豸���ұ���Ϊ����
        CV_INFO(g_CVDrvierCommonLog,  _("Failed to send data to the device(%d bytes, %d timeout), this device is a single connected device and backup machine."),
			lBufLen, lTimeOutMS);
		return -1;
	}

	// _TODO ���豸���÷����˸ı䡣�����е����ˣ�Ӧ�����յ�֪ͨʱ�ͽ��������л�
	if(m_bConfigChanged) // ���豸���÷����˸ı䡣�����е����ˣ�
	{
		char *szConnType = (char *)m_strConnType.c_str();
		if(ACE_OS::strcasecmp(szConnType, "tcpserver") == 0)
		{
			// ֹͣ�豸
			Stop();
			Start(); // ������������!Ϊ�˱���˿ںŷ����ı�
		}
		else
		{
			// �豸���������Ǹտ�ʼ��ȷ���ˣ����治���޸���-------------------------------------------------
			if(m_pDeviceConnection)
			{
				if(!m_pDeviceConnection->IsConnected())
				{
                    CV_ERROR(g_CVDrvierCommonLog, -1, _("Config changes, the current device %s is not connected!"), this->m_strName.c_str());//�������÷����ı�, ��ǰ�豸%sδ����
				}
				else
				{
					CV_ERROR(g_CVDrvierCommonLog, -1, _("Config changes, disconnect current device %s!"), this->m_strName.c_str());//�������÷����ı�, �Ͽ��豸%s��ǰ����
				}
				m_pDeviceConnection->DisConnect();
                string strConnParam = m_strConnParam + ";";
				m_pDeviceConnection->SetConnectParam((char *)strConnParam.c_str());
				GetMultiLink((char *)strConnParam.c_str());
				m_pDeviceConnection->Connect();
			}
		} // if(m_nConnType == CONNTYPE_TCP_SERVER)

		m_bConfigChanged = false;
	} // m_bConfigChanged

	if(m_pDeviceConnection)
    {
        long nStatus =  m_pDeviceConnection->Send(szBuffer, lBufLen, lTimeOutMS);
        
        {
            ACE_TCHAR szHexBuffer[MAX_LOG_SIZE];
            unsigned int nHexBufferLen = MAX_LOG_SIZE;
            memset(szHexBuffer, 0, sizeof(szHexBuffer));
            cvcommon::HexDumpBuf((unsigned char *)szBuffer, lBufLen, szHexBuffer, &nHexBufferLen);
            CV_DEBUG(g_CVDrvierCommonLog, _("Send packets to the device %s, message:\n%s"), this->m_strName.c_str(), szHexBuffer);//���ͱ��ĵ��豸:%s, ����:\n%s
        }
		std::string strSubKeyPrefix = g_strDrvName;
		strSubKeyPrefix += "#";
		strSubKeyPrefix += m_strName;
		//drv_redis_batchbegin();
		// drv_redis_time_d(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_STATUS);
        return nStatus;
    }
	else
	{
        //���豸��������ʧ��(���� %d ���ֽڣ���ʱ %d ), �������Ӵ�
        CV_DEBUG(g_CVDrvierCommonLog,  _("Failed to send data to the device(%d bytes, %d timeout), Please check the connection string."),
			lBufLen, lTimeOutMS);
		return -35;
	}
}

int CDevice::RecvFromDevice(char *szBuffer, long lBufLen, long lTimeOutMS)
{
	if(!m_pDeviceConnection)
	{
        CV_DEBUG(g_CVDrvierCommonLog,  _("Failed to receive data from the device(%d bytes, %d timeout), Please check the connection string!"),//���豸��������ʧ��(���������� %d ���ֽڣ���ʱ %d ), �������Ӵ�
			lBufLen, lTimeOutMS);
		return -1;
	}
	return m_pDeviceConnection->Recv(szBuffer, lBufLen, lTimeOutMS);
}

int CDevice::RecvNBytesFromDevice( char *szBuffer, long lBufLen, long lTimeOutMS )
{
	if(!m_pDeviceConnection)
	{
        CV_DEBUG(g_CVDrvierCommonLog,  _("Failed to receive data from the device(%d bytes, %d timeout), Please check the connection string!"),//���豸��������ʧ��(���������� %d ���ֽڣ���ʱ %d ), �������Ӵ�
			lBufLen, lTimeOutMS);
		return -1;
	}

	return m_pDeviceConnection->Recv_n(szBuffer, lBufLen, lTimeOutMS);
}

int CDevice::DisonnectDevice()
{
	if(!m_pDeviceConnection)
		return -1;

	return m_pDeviceConnection->DisConnect();
}

int CDevice::ReConnectDevice(long lTimeOutMS)
{
	if(!m_pDeviceConnection)
		return -1;

	m_pDeviceConnection->DisConnect();
	return m_pDeviceConnection->Connect();
}

void CDevice::ClearRecvBuffer()
{
	if(m_pDeviceConnection)
		m_pDeviceConnection->ClearDeviceRecvBuffer();
}


// ���Ӳ����������tcp�����:IP,Port,��ѡ�����ӳ�ʱ���Ժ��ǿ����д���
// ����һ����tcpclient���ӷ�ʽ����ʽ: IP:***********;Port:502;ConnTimeOut:3000
long CDevice::GetMultiLink(char *szConnParam)
{
	m_bMultiLink = true;
	string strConnParam = szConnParam;
    CV_INFO(g_CVDrvierCommonLog, _("Set the device connection parameters:%s"), szConnParam);//�����豸���Ӳ��� %s
	strConnParam += ";"; // ����һ���ֺ�
	int nPos = strConnParam.find(';');
	while(nPos != string::npos)
	{
		string strOneParam = strConnParam.substr(0, nPos);
		strConnParam = strConnParam.substr(nPos + 1);// ��ȥ��һ���Ѿ��������Ĳ���
		nPos = strConnParam.find(';');

		if(strOneParam.empty())
			continue;

		int nPosPart = strOneParam.find('=');	// IP:**********
		if(nPosPart == string::npos)
			continue;

		// ��ȡ��ĳ���������ƺ�ֵ
		string strParamName = strOneParam.substr(0, nPosPart); // e.g. IP
		string strParamValue = strOneParam.substr(nPosPart + 1); // e.g. **********

		if(ACE_OS::strcasecmp("multilink", strParamName.c_str()) == 0)
		{
			m_bMultiLink = ::atoi(strParamValue.c_str());
			break;
		}
	}

	return DRV_SUCCESS;
}

//����other��ʽ�����Ƿ������ѡ���Ϊ��غ����Ƿ�������������������ͬ�� by wangyadong 20150106
long CDevice::GetMultiLinkWithDefaultValue(char *szConnParam)
{
	m_bMultiLink = true;
	string strConnParam = szConnParam;
	CV_INFO(g_CVDrvierCommonLog, _("Set the device connection parameters:%s"), szConnParam);//�����豸���Ӳ��� %s
	strConnParam += ";"; // ����һ���ֺ�
	int nPos = strConnParam.find(';');
	while(nPos != string::npos)
	{
		string strOneParam = strConnParam.substr(0, nPos);
		strConnParam = strConnParam.substr(nPos + 1);// ��ȥ��һ���Ѿ��������Ĳ���
		nPos = strConnParam.find(';');

		if(strOneParam.empty())
			continue;

		int nPosPart = strOneParam.find('=');	// IP:**********
		if(nPosPart == string::npos)
			continue;

		// ��ȡ��ĳ���������ƺ�ֵ
		string strParamName = strOneParam.substr(0, nPosPart); // e.g. IP
		string strParamValue = strOneParam.substr(nPosPart + 1); // e.g. **********

		if(ACE_OS::strcasecmp("multilink", strParamName.c_str()) == 0)
		{
			m_bMultiLink = ::atoi(strParamValue.c_str());
			break;
		}
	}

	return DRV_SUCCESS;
}