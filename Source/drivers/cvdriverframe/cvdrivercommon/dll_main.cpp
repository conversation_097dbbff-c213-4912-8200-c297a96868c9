/**************************************************************
 *  Filename:    Server.cpp
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: ������.
 *
 *  @author:     lijingjing
 *  @version     08/11/2008  lijingjing  Initial Version
**************************************************************/
#include <ace/Init_ACE.h>
#include <ace/Process_Mutex.h>
#include "MainTask.h"
#include <ace/OS_NS_unistd.h>
#include <iostream>
#include "driversdk/cvdrivercommon.h"
#include "MainTask.h"
#include "ace/Signal.h"
#include "DrvCtrlTask.h"
#include "common/cvGlobalHelper.h"
#include "gettext/libintl.h"
#include <sys/types.h>
#include "processdb/DriverApi.h"
#include "common/LicChecker.h"
#include <unordered_map>
#undef printf
//#define _(STRING) gettext(STRING)
#define _(STRING) STRING
using namespace std;


CCVLog g_CVDrvierCommonLog;
// �������������ж������Ƿ��Ѿ�����
ACE_Process_Mutex* g_pServerMutex = NULL;
extern bool g_bAlive;
// �ж����������Ƿ��Ѿ�����
bool IsServerRunning();

#define MAXBUFSIZE	1024
#define CMDLENGTH   255
#define BUFFERLENGTH 255
#define FGETSIZE    255
#define STR_DRVCTRL_NAME	"dsfdrvctrl"

TCV_TimeStamp g_tvValid;
           
/**
 *  @brief    (CtrlHandler ONLY works in WIN32). 
 *  (!!! IMPORTANT !!! SetConsoleCtrlHandler would kill all child-processes before CtrlHandler is called).
 */
#ifdef _WIN32
BOOL CtrlHandler(DWORD fdwCtrlType) 
{ 
    switch (fdwCtrlType) 
    { 
        // Handle the CTRL+C signal. 
        case CTRL_C_EVENT: 
            return true; 
 
        // CTRL+CLOSE
        case CTRL_CLOSE_EVENT: 
			//SignalToExit();
			g_bAlive = false;
            return true; 
 
        // Pass other signals to the next handler. 
        case CTRL_BREAK_EVENT: 
        case CTRL_LOGOFF_EVENT: 
        case CTRL_SHUTDOWN_EVENT: 
        default: 
            return false; 
    } 
} 
#endif

#ifdef _WIN32
	// for _kbhit in dos
	#include <conio.h>
#else
	// simulate the _kbhit in POSIX
#include <termios.h>
#include <sys/ioctl.h>
#include <sys/time.h>
#include <sys/types.h>

#ifdef Solaris
#include <sys/filio.h>
#endif

#undef  TERMIOSECHO
#define TERMIOSFLUSH

/*
* kbhit() -- a keyboard lookahead monitor
*
* returns the number of characters available to read
*/
static int kbhit ( void )
{
    struct timeval tv;
    struct termios old_termios, new_termios;
    int            error;
    int            count = 0;
	
    tcgetattr( 0, &old_termios );
    new_termios              = old_termios;
    /*
	* raw mode
	*/
    new_termios.c_lflag     &= ~ICANON;
    /*
	* disable echoing the char as it is typed
	*/
    new_termios.c_lflag     &= ~ECHO;
    /*
	* minimum chars to wait for
	*/
    new_termios.c_cc[VMIN]   = 1;
    /*
	* minimum wait time, 1 * 0.10s
	*/
    new_termios.c_cc[VTIME]  = 1;
    error                    = tcsetattr( 0, TCSANOW, &new_termios );
    tv.tv_sec                = 0;
    tv.tv_usec               = 100;
    /*
	* insert a minimal delay
	*/
    select( 1, NULL, NULL, NULL, &tv );
    error                   += ioctl( 0, FIONREAD, &count );
    error                   += tcsetattr( 0, TCSANOW, &old_termios );
    return( error == 0 ? count : -1 );
}  /* end of kbhit */
#endif

void RunInteractiveLoop()
{
#ifdef _WIN32
	if (! SetConsoleCtrlHandler( (PHANDLER_ROUTINE) CtrlHandler, TRUE) ) 
		CV_WARN(g_CVDrvierCommonLog, -1, ACE_TEXT(_("%T: Could not set control handler \n")));
#endif 
	
	// whislter to quit
	ACE_OS::printf(">>");

	while (g_bAlive)
	{
		if (ACE_OS::gettimeofday() > ACE_Time_Value(g_tvValid))
			break;

		if (kbhit())
		{
			char chCmd = tolower(getchar());
			switch(chCmd)
			{
			case 'q':
			case 'Q':
				g_bAlive = false;
				break;
			case 'r':
			case 'R':
//				MAIN_TASK->TriggerRefreshConfig();	// �ֹ�����һ����������
				break;

				// filter out ox0D0A
			case 0x0a:				
			default:
				ACE_OS::printf(">>");
				break;
			}
		}
		else
		{
			ACE_Time_Value tmWait(0, 100000);
	 		ACE_OS::sleep(tmWait);
		}
	}
}

ACE_DLL g_hDrvDll;	// ������̬��
ACE_DLL g_hDrvDDaDll;	// ����DDA��̬��
string	g_strDrvName;
bool  g_bMultiLink = true;

void SIGPIPE_Handler (int signo) {
	CV_ERROR(g_CVDrvierCommonLog, -1, _("SIGPIPE signaled!"));
}

long LoadDrvDllFuncs(string strDrvName);
CVDRIVER_EXPORTS int dll_main_i(int argc, char** argv)
{
	// �������ƾ���argv[0]
	g_strDrvName = argv[0];
	ACE::init();
	bool bLogInit = false;
#ifdef _WIN32
	char szAppName[ICV_SHORTFILENAME_MAXLEN];
	memset(szAppName, 0, ICV_SHORTFILENAME_MAXLEN);
	CVComm.GetCurrentAppName(szAppName);
	char szLogName[ICV_SHORTFILENAME_MAXLEN];
	memset(szLogName, 0, ICV_SHORTFILENAME_MAXLEN);
	sprintf(szLogName, "CVDriverCommon_%s", szAppName);
	g_CVDrvierCommonLog.SetLogFileNameThread(szLogName);
	CV_INFO(g_CVDrvierCommonLog, _("Driver %s started"), g_strDrvName.c_str());
	bLogInit = true;
#endif

	// ��ȡ��������ļ��������?��������
	// FIXME:��������?����Linux�������Է������������������������ֱ�Ӹ�ֵ�����Բ���ȥ��ȡ�ͽ����ˣ��������������������?�ſ��ġ�
	int nPos = string::npos;
	if((nPos = g_strDrvName.find_last_of(ACE_DIRECTORY_SEPARATOR_CHAR_A)) != string::npos)
		g_strDrvName = g_strDrvName.substr(nPos + 1);

	if((nPos = g_strDrvName.find_last_of('.')) != string::npos)
		g_strDrvName = g_strDrvName.substr(0, nPos);

	g_strDrvName = "icgdrv";

	// ������־�ļ�
	if (!bLogInit)
	{
		std::string strLogName = "CVDriverCommon_";
		strLogName += g_strDrvName.c_str();
		g_CVDrvierCommonLog.SetLogFileNameThread(strLogName.c_str());
	}

	if (IsServerRunning())	// ���ݻ������жϳ����Ƿ��Ѿ�����
	{
		CV_CRITICAL(g_CVDrvierCommonLog, -1, _("Driver %s failed to start! Process already exists, it will exit!"), g_strDrvName.c_str());//����%s����ʧ��! �����Ѿ�����, ���˳�!
		
		// ��Ϣ5���Ա��û��ܿ�����ʾ
		ACE_Time_Value tmWait(0, 5000000);
		ACE_OS::sleep(tmWait);

		return -1;
	}
	
	long lRet = LoadDrvDllFuncs(g_strDrvName.c_str());
	if(lRet != DRV_SUCCESS)
	{
        //����%s����ʧ��! ����������̬�⵼������ʧ�ܣ�����:%d��, ���˳�!
		CV_CRITICAL(g_CVDrvierCommonLog, -1,  _("Driver %s failed to start! Load export function of dll failed(return code:%d), it will exit!"), g_strDrvName.c_str(), lRet);

		// ��Ϣ5���Ա��û��ܿ�����ʾ
		ACE_Time_Value tmWait(0, 5000000);
		ACE_OS::sleep(tmWait);
		return -1;
	}

	if(g_pfnBegin)	// ȷ��Begin��һ������
		g_pfnBegin();

	ACE_Sig_Action sig_action;
	sig_action.handler(SIGPIPE_Handler);
	sig_action.register_action(SIGPIPE);
	DRIVERCTRL_TASK->Start();
	MAIN_TASK->Start();

	RunInteractiveLoop();

	// �û�ѡ���˳������˳�
	MAIN_TASK->Stop();
	DRIVERCTRL_TASK->Stop();

	if (g_pServerMutex)
	{
		g_pServerMutex->release();
		SAFE_DELETE(g_pServerMutex);
	}

	// ���������ķ���ʼ������
	if(g_pfnUnInit)
	{
		g_pfnUnInit();
	}

	// �ر�������̬��
	g_hDrvDll.close();

	g_hDrvDDaDll.close();


    g_CVDrvierCommonLog.StopLogThread();
	//drv_redis_uninit();
	return 0;
};

#if defined(__linux) || defined(__linux__) || defined(linux)
string GetProcessPidByName(const char *proc_name)
{

	FILE *fp;
	char buf[BUFFERLENGTH];
	memset(buf,0x00,sizeof(buf));
	char cmd[CMDLENGTH] = {'\0'};
	string pid;
	sprintf(cmd, "pidof %s", proc_name);
	if((fp = popen(cmd, "r")) != NULL)
	{
		if(fgets(buf, FGETSIZE, fp) != NULL)
		{
			pid = buf;
			CV_DEBUG(g_CVDrvierCommonLog,  _("the process ��%s��PID is %s"), proc_name,pid.c_str());
		}
	}

	pclose(fp);
	return pid;

}

//Parameter1:All pid with same process name
//Parameter2:Target pid which will NOT be killed
void KillOtherProcess(string strPidSet ,pid_t pidTarget)
{
	FILE *fp;
	char buf[BUFFERLENGTH];
	memset(buf,0x00,sizeof(buf));
	char cmd[CMDLENGTH] = {'\0'};
	stringstream stmTarget;
	stmTarget << pidTarget;
	string strTarget = stmTarget.str();
	string strContent,strTemp;
	//֮ǰ��ѯ�ķ��ؽ��ĩβ����һλ���������ַ�������strcmp�����Զ���?0�����ｫ���һλ�ص�?
	strContent = strPidSet.substr(0,strPidSet.length()-1);
	strTemp = strContent;
	CV_DEBUG(g_CVDrvierCommonLog, _("Before Kill Other Process:PidSet = %s, target pid = %s"), strContent.c_str(), strTarget.c_str());
	int nPos = strTemp.find(' ');
	while(nPos!=-1)
	{
		strContent = strTemp.substr(0,nPos);
		strTemp = strTemp.substr(nPos+1);
		if (strcmp(strContent.c_str(), strTarget.c_str()) != 0)
		{
			sprintf(cmd, "kill -9 %s", strContent.c_str());
			CV_WARN(g_CVDrvierCommonLog, 0,  _("kill cmd = %s"), cmd);
			fp = popen(cmd, "r");
			fgets(buf, FGETSIZE, fp);
			pclose(fp);
		}
		nPos = strTemp.find(' ');
	}
	CV_WARN(g_CVDrvierCommonLog, 0, _("Kill:strTarget = %s, strTemp = %s"), strTarget.c_str(), strTemp.c_str());
	//��strPidSet���ո������ж����һ�Σ���û�����жϵ�������?
	if (strcmp(strTemp.c_str(), strTarget.c_str()) != 0)
	{
		sprintf(cmd, "kill -9 %s", strTemp.c_str());
		CV_WARN(g_CVDrvierCommonLog, 0, _("kill cmd = %s"), cmd);
		fp = popen(cmd, "r");
		fgets(buf, FGETSIZE, fp);
		pclose(fp);
	}
	CV_INFO(g_CVDrvierCommonLog,  _("End of kill function"));



}

bool IsProcPidContainTarget(string strRtPid, pid_t pidTarget)
{
	stringstream stmTarget;
	stmTarget << pidTarget;
	string strTarget = stmTarget.str();
	string strContent,strTemp;
	//֮ǰ��ѯ�ķ��ؽ��ĩβ����һλ���������ַ�������strcmp�����Զ���?0�����ｫ���һλ�ص�?
	strContent = strRtPid.substr(0,strRtPid.length()-1);
	strTemp = strRtPid.substr(0,strRtPid.length()-1);
	CV_DEBUG(g_CVDrvierCommonLog, _("strTarget = %s, strContent = %s"), strTarget.c_str(), strContent.c_str());
	int nPos = strTemp.find(' ');
	while(nPos!=-1)
	{
		strContent = strTemp.substr(0,nPos);
		strTemp = strTemp.substr(nPos+1);
		if (strcmp(strContent.c_str(), strTarget.c_str()) == 0)
		{
			CV_INFO(g_CVDrvierCommonLog, _("Return Process Pid contains target process!"));
			return true;
		}
		nPos = strTemp.find(' ');
	}
	CV_DEBUG(g_CVDrvierCommonLog,  _("strTarget = %s, strTemp = %s"), strTarget.c_str(), strTemp.c_str());
	//��strRtPid���ո������ж����һ�Σ���û�����жϵ�������?
	if (strcmp(strTemp.c_str(), strTarget.c_str()) == 0)
	{
		CV_INFO(g_CVDrvierCommonLog, _("Return Process Pid contains target process!"));
		return true;
	}
	CV_DEBUG(g_CVDrvierCommonLog, _("No Such process in return pid"));
	return false;
}
#endif//defined(__linux) || defined(__linux__) || defined(linux)

/**
 *  �ж����������Ƿ��Ѿ�����.
 *
 *
 *  @version     07/07/2008  lijingjing  Initial Version.
 */
bool IsServerRunning()
{
#if defined(__linux) || defined(__linux__) || defined(linux)
	//�����ָ�����Ϊ1����������ʱtryacquire������?-1���޷�����kill��ʬ���̣�����Ƚ����ж��Ƿ�kill��������
	pid_t pid,ppid;
	string strdrvNamePid, strdrvctrlPid;
	pid = getpid();
	ppid = getppid();
	CV_DEBUG(g_CVDrvierCommonLog, _("pid = %d, ppid = %d, drvName = %s"), pid, ppid, g_strDrvName.c_str());
	strdrvNamePid = GetProcessPidByName(g_strDrvName.c_str());

	strdrvctrlPid = GetProcessPidByName(STR_DRVCTRL_NAME);
	if (IsProcPidContainTarget(strdrvctrlPid, ppid))
	{
		CV_WARN(g_CVDrvierCommonLog, 0, _("The parent Process is %s, so release Semaphore of %s, the driver will be loaded"), STR_DRVCTRL_NAME, g_strDrvName.c_str());
		if (IsProcPidContainTarget(strdrvNamePid, pid))
		{
			CV_INFO(g_CVDrvierCommonLog, _("Try to other kill other same name process"));
			KillOtherProcess(strdrvNamePid,pid);
		}
	}
	else
	{
		int nPos = strdrvNamePid.find(' ');
		if (nPos != -1)
		{
			CV_INFO(g_CVDrvierCommonLog, _("Detect this process's father is not %s and already exist one same process, so return server running"), STR_DRVCTRL_NAME);
			return true;
		}
	}
#endif
	g_pServerMutex = new ACE_Process_Mutex(g_strDrvName.c_str());
	int nRet = g_pServerMutex->tryacquire();
	if (nRet == -1)
	{
#if defined(__linux) || defined(__linux__) || defined(linux)
		if (IsProcPidContainTarget(strdrvctrlPid, ppid))
		{
			g_pServerMutex->release();
			CV_INFO(g_CVDrvierCommonLog, _("g_pServerMutex->release();"));
			return false;
		}
#endif
		SAFE_DELETE(g_pServerMutex);
		return true;
	}
	else
		return false;
}

long LoadDrvDllFuncs(string strDrvName)
{
	// �������Ƽ��ɣ���linux��windows�Զ������ǰ�? lib�ͺ�׺so��dll
	// ȱʡ��Ϊ����ExecutableĿ¼��ִ��.Drivers/modbus
	const char *pExecPath = CVComm.GetCVEnv();
	if (NULL == pExecPath)
	{
        //��ȡϵͳ��װĿ¼�µ�executableĿ¼ʧ�ܣ�
		CV_CRITICAL(g_CVDrvierCommonLog, EC_ICV_DA_IO_DLL_NOT_FOUND,  _("Obtain the executable directory under the installation directory failed!"));
		return EC_ICV_DA_IO_DLL_NOT_FOUND;
	}

	string strDllPath = pExecPath;
	strDllPath += ACE_DIRECTORY_SEPARATOR_CHAR_A;
	strDllPath += "drivers";
	strDllPath += ACE_DIRECTORY_SEPARATOR_CHAR_A;
	strDllPath += strDrvName;
	strDllPath += ACE_DIRECTORY_SEPARATOR_CHAR_A;
	strDllPath += strDrvName;

	long nErr = g_hDrvDll.open(strDllPath.c_str());
	if (nErr != DRV_SUCCESS)
	{
		CV_INFO (g_CVDrvierCommonLog,  "Can not load the driver DLL:  errmsg: %s" ,  g_hDrvDll.errmsg_.c_str());
		CV_CRITICAL(g_CVDrvierCommonLog, EC_ICV_DA_IO_DLL_NOT_FOUND, _("Can not load the driver DLL:%s, error: %d, errmsg: %s"), strDllPath.c_str(), nErr, g_hDrvDDaDll.error());//���ܼ�������DLL %s
		return EC_ICV_DA_IO_DLL_NOT_FOUND;
	}
	
	g_pfnBegin = (PFN_Begin)g_hDrvDll.symbol("Begin");
	g_pfnInit = (PFN_Initialize)g_hDrvDll.symbol("Initialize"); 
	g_pfnUnInit = (PFN_UnInitialize)g_hDrvDll.symbol("UnInitialize"); 
	g_pfnOnDeviceAdd = (PFN_OnDeviceAdd)g_hDrvDll.symbol("OnDeviceAdd"); 
	g_pfnOnDeviceDelete = (PFN_OnDeviceDelete)g_hDrvDll.symbol("OnDeviceDelete"); 
	g_pfnOnDataBlockAdd = (PFN_OnDataBlockAdd)g_hDrvDll.symbol("OnDataBlockAdd"); 
	g_pfnOnDataBlockDelete = (PFN_OnDataBlockDelete)g_hDrvDll.symbol("OnDataBlockDelete");    
	g_pfnOnDeviceTimer = (PFN_OnDeviceTimer)g_hDrvDll.symbol("OnDeviceTimer");  

	g_pfnOnDataBlockTimer = (PFN_OnDataBlockTimer)g_hDrvDll.symbol("OnDataBlockTimer");   
	//���δʵ��OnReadData�������ٵ���OnDataBlockTimer����
	g_pfnOnReadData = (PFN_OnReadData)g_hDrvDll.symbol("OnReadData");  
	g_pfnGetDrvFrameVersion = (PFN_GetDrvFrameVersion)g_hDrvDll.symbol("GetDrvFrameVersion");
	g_pfnOnWriteCmd = (PFN_OnWriteCmd)g_hDrvDll.symbol("OnWriteCmd");
	g_pfnOnWriteCmdEx = (PFN_OnWriteCmdEx)g_hDrvDll.symbol("OnWriteCmdEx");
	g_pfnTagsToGroups = (PFN_TagsToGroups)g_hDrvDll.symbol("TagsToGroups");
	g_pfnTagsToGroupsEx = (PFN_TagsToGroupsEx)g_hDrvDll.symbol("TagsToGroupsEx");

	g_pfnOnHeartBeat = (PFN_OnHeartBeat)g_hDrvDll.symbol("OnHeartBeat");
	g_pfnOnBatchUpdateData = (PFN_OnBatchUpdateData)g_hDrvDll.symbol("OnBatchUpdateData");
	g_pfnIsTagInBlock = (PFN_IsTagInBlock)g_hDrvDll.symbol("IsTagInBlock"); 
	g_pfnParseIoAddr = (PFN_ParseIoAddr)g_hDrvDll.symbol("ParseIoAddr"); 

	//nErr = LoadDDaDllFuncs(strDrvName);
	return DRV_SUCCESS;
} 

CVDRIVER_EXPORTS int SetLicenseInfo(TCV_TimeStamp &tvValid)
{
	g_tvValid = tvValid;
	return ICV_SUCCESS;
}
