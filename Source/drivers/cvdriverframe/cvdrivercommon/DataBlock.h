/**************************************************************
 *  Filename:    DataBlock.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: 数据块信息实体类.
 *
 *  @author:     lijingjing
 *  @version     05/28/2008  lijingjing  Initial Version
**************************************************************/

#ifndef _DATABLOCK_H_
#define _DATABLOCK_H_

#include <ace/Thread_Mutex.h>
#include <ace/Guard_T.h>
#include "common/Quality.h"
#include <list>
#include <vector>
#include "common/CommHelper.h"
#include "common/RMAPI.h"
#include "common/IODataSync.h"
#include "drvframework.h"
#include "data_types.h"

#define  INVALID_BLOCK_NUMBER	-1		// DIT中数据块创建不成功时的数据块号
class CDevice;

typedef struct _TagAddrID
{
	int32 nBitOffset;
	int32 nBitLen;
	uint32 nTagID;
	uint8 nDataType;
	uint8 nMDIAddrNo;
	uint8 nOverBlock;
} TagAddrID;

typedef struct _TagBlockAddr
{
	std::string strDeviceName;
	std::string strDataBlockName;
	std::string strTagAddr;
	int32 nBitOffset;
	int32 nBitLen;
	std::string strDeviceName1;
	std::string strDataBlockName1;
	std::string strTagAddr1;
	int32 nBitOffset1;
	int32 nBitLen1;
	std::string strDeviceName2;
	std::string strDataBlockName2;
	std::string strTagAddr2;
	int32 nBitOffset2;
	int32 nBitLen2;
	uint8 nDataType;
	uint8 nTagType;//点的类型
} TagBlockAddr;

class CDataBlock:  public CDrvObjectBase,public ACE_Event_Handler
{
public:
	CDataBlock(CDevice *pDevice);
	virtual ~CDataBlock();
	bool operator == (CDataBlock &theDataBlock);
	CDataBlock &operator=(CDataBlock &theDataBlock);
	void StartTimer();
	void StopTimer();

	virtual int handle_timeout(const ACE_Time_Value &current_time, const void *act);	// collect data
	
	// 读消息处理失败时设置数据块质量
	long ProcessReadError(bool bTimeout = true);
	void CreateBlockBuffer();
	void UpdateBlockBuffer(const char *szBuffer, long lOffset, long lBufLen);
	void UpdateBlockBuffer(const char *szBuffer, long lByteOffset, long lBitOffset, long lBufLen);
public:
	CVDATABLOCK	m_outDataBlockInfo;	// 用于具体驱动的配置信息
	CDevice*	m_pDevice;				// 所属设备指针

	std::vector<TagAddrID*> m_vecTagAddrID; 	

	// 下面地址是因为DIT中保存了DATABLOCK_CONFIG结构，因此这里需要保存一下以后后续使用
	string				m_strAddress;		// 起始地址
	unsigned short		m_nElemNum;			// 数据块长度
	unsigned short	    m_nElemBits;		// 寄存器的bit大小
	string              m_strBlockType;     // 数据块的类型, AO/DO/AI/DI，和功能码的值是相同的
	int					m_nPollRate;			// 扫描周期，单位ms
	int					m_nPollPhase;			// 扫描相位，单位ms
	unsigned int		m_nReadCount;
	unsigned int		m_nWriteCount;
	string				m_strTaskID;	// 任务号
	uint32              m_nBlockSize;
	char*				m_szBlockBuffer;
};

#endif  // _DATABLOCK_H_
