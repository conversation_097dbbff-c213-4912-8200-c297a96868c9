#ifndef TAG_READER_HXX
#define TAG_READER_HXX
#include "driversdk/cvdrivercommon.h"
#include <vector>
#include <string>

typedef std::vector<TagInfo> TagInfoVector;
class CppSQLite3DB;

class CTagReader
{
public:
	CTagReader(const char *pDrvName, const char *pDeviceName);
	void ReadTags(TagInfoVector &vecTagInfo);
protected:
	void ReadAITags(CppSQLite3DB *pSQLiteDB, TagInfoVector &vecTag);
	void ReadAOTags(CppSQLite3DB *pSQLiteDB, TagInfoVector &vecTag);
	void ReadBlobTags(CppSQLite3DB *pSQLiteDB, TagInfoVector &vecTag);
	void ReadDITags(CppSQLite3DB *pSQLiteDB, TagInfoVector &vecTag);
	void ReadDOTags(CppSQLite3DB *pSQLiteDB, TagInfoVector &vecTag);
	void ReadMDITags(CppSQLite3DB *pSQLiteDB, TagInfoVector &vecTag);
	void ReadTxtTags(CppSQLite3DB *pSQLiteDB, TagInfoVector &vecTag);

	void ReadAllTags(CppSQLite3DB *pSQLiteDB, TagInfoVector &vecTag);
private:
	void ReadAnalogTags(CppSQLite3DB *pSQLiteDB, int nTagType, const char *pTagTableName, TagInfoVector &vecTag);
	void ReadDigitalTags(CppSQLite3DB *pSQLiteDB, int nTagType, const char *pTagTableName, TagInfoVector &vecTag);
	std::string GetPDSCfgPath();
	std::string GetPDSCfgNewPath();
private:
	char m_szDrvName[ICV_DRIVERNAME_MAXLEN + 1];
	char m_szDeviceName[ICV_DEVICENAME_MAXLEN + 1];
};
#endif
