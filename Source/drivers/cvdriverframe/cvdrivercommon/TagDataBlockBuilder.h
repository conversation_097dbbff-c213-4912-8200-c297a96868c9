#ifndef CDEV_DATABLOCK_BUILDER_HXX
#define CDEV_DATABLOCK_BUILDER_HXX

#include "driversdk/cvdrivercommon.h"
#include <vector>
#include <string>
#include <list>
#include "TagReader.h"
#include "data_types.h"

class CDataBlock;
class CDevice;

typedef std::vector<TagGroupInfo> TagGroupInfoVector;
typedef std::multimap<std::string, TagInfo > TagInfoMultiMap;
typedef std::list<CDataBlock *> DataBlockList;

class CTagDataBlockBuilder
{
public:
	CTagDataBlockBuilder(const char *pDrvName, CDevice *pDevice);
	~CTagDataBlockBuilder(void);
	void MakeTagDataBlocks(TagInfoVector &vecTagInfo);
	void MakeTagDataBlocks(TagInfoVector &vecTagInfo, CDevice* pDevice, unsigned int nCurrConnIndex, unsigned int nTotalConNum);
	void ReadTags(TagInfoVector &vecTagInfo);
	int32 GetBitLenByDataType(uint8 nDataType, char* szIoAddr, int32 & nLength);
	//int32 ParseIoAddr(char* szIoAddr,  char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN], int32 & nLength);
	//bool IsTagInBlock(char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN], CVDATABLOCK * pcvDataBlock, int32&nBitOffSet);
	int32 ParseTagAndInsertBlock(TagInfoVector &vecTagInfo, CDataBlock *pDataBlock);
protected:
	void GetTagGrps(TagInfoVector &vecTagInfo, TagInfoVector &vecOutTagInfo, TagGroupInfoVector &vecGrpInfo);
	void GetTagGrps( TagInfoVector &vecTagInfo, TagInfoVector &vecOutTagInfo, TagGroupInfoVector &vecGrpInfo, CDevice* pDevice, unsigned int nCurrConnIndex, unsigned int nTotalConNum);
private:
	CTagReader *m_pTagReader;
	CDevice *m_pDevice;
};

#endif
