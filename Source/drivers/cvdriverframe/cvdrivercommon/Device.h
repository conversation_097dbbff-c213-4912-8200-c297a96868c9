/**************************************************************
 *  Filename:    Device.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: �豸��Ϣʵ����.
 *
 *  @author:     lijingjing
 *  @version     05/28/2008  lijingjing  Initial Version
**************************************************************/

#ifndef _DEVICE_H_
#define _DEVICE_H_

#include "DataBlock.h"
#include "common/DrvDef.h"
#include "drvframework.h"
#include "TcpClientHandler.h"
#include "SerialPort.h"
#include "UdpServerHandler.h"
#include "UdpClientHandler.h"

class CTaskGroup;
using namespace std;

class CDevice: public CDrvObjectBase,public ACE_Event_Handler
{
public:
	CDeviceConnection	*m_pDeviceConnection;	// tcp����
	int					m_nPollRate;	// ��ѯʱ��
	string				m_strConnType;	// ��������
	string				m_strConnParam;	// ���Ӳ���
	int					m_nRecvTimeOut;	// ����Ϊ��λ
	bool				m_bMultiLink;	// �豸�Ƿ�֧�ֶ�����
	string				m_strTaskID;	// �����

public:
	CDevice(CTaskGroup *pTaskGrp, const char *pszDeviceName);
	virtual ~CDevice();

	CDevice &operator=(CDevice &theDevice);
	virtual int handle_timeout(const ACE_Time_Value &current_time, const void *act);	// collect data
	void Start();
	void StartTimer();
	void Stop();
	int SendToDevice(char *szBuffer, long lBufLen, long lTimeOutMS );
	int RecvFromDevice(char *szBuff, long lBufLen, long lTimeOutMS);
	int RecvNBytesFromDevice(char *szBuff, long lBufLen, long lTimeOutMS);

	void PostWriteCmdToTask(string strDataBlkAddr, int nDataEguType, char *szCmdData, int nCmdDataLen);
	void OnWriteCommand(string strDataBlkAddr, string strTagName, string strTagAddr, int32 nTagID, uint8 nDataType, uint8 nTagType, int nBlkByteOffset, int nBlkBitOffset, char* szCmdData, int nCmdDataLen);
	int ReConnectDevice(long lTimeOutMS);
	int DisonnectDevice();
	void ClearRecvBuffer();
	// ����ƫ���ַ�������ƫ�Ƶ�ַ����CVDriverDDA.dll�е�Parse������ͬ
	void CalcTagOffsetInBlock(CDataBlock *pDataBlock, char *szByteOffsetAddr, char *szBitOffsetAddr, int nCmdDataLen, int *pnByteOffset, int *pnBitOffset);
	long GetMultiLink(char *szConnParam);
	long GetMultiLinkWithDefaultValue(char *szConnParam);
	long SaveWriteMsg(const CDataBlock *pDataBlock, long nErr, int nTagByteOffset, int nTagBitOffset, char *szCmdData, int nCmdDataBits);
protected:
	int ParseDeviceConnStrOfTcpServer(unsigned short &uPort);
public:
	CVDEVICE	m_outDeviceInfo;	// ���ھ���������������Ϣ
	CTaskGroup *m_pTaskGroup;			// �����豸��ָ��
	std::map<string, string>	m_mapDeviceIPsOfTcpServer;	// tcp����ʱ���豸IP�������ж��Ƿ�������IP����

	std::map<std::string, CDataBlock*> m_mapDataBlocks;	// ���ݿ��б�
	ACE_Time_Value		m_tvTimerBegin;

public:
	PFN_CVCommRecvCallBack m_pfnRecvCallback;		// ���յ�����ʱ�Ļص�����
	long	m_lCallbackParam;				// �ص������ĵ�1������
	char	m_szCallbackParam[ICV_DEVICENAME_MAXLEN];	// �ص������ĵ�2������
	void*	m_pCallbackParam;				// �ص������ĵ�3������
};

#endif  // _DEVICE_H_
