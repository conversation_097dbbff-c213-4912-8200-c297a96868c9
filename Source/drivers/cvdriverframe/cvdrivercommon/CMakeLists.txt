cmake_minimum_required(VERSION 3.10)

PROJECT (drdrivercommon)

INCLUDE($ENV{DRDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
#Setting Source Files
SET(SRCS ${SRCS} dll_main.cpp  DataBlock.cpp CVDriverCommon.cpp TcpClientHandler.cpp TcpServerHandler.cpp UdpServerHandler.cpp UdpClientHandler.cpp SerialPort.cpp drvframework.cpp Device.cpp  TagDataBlockBuilder.cpp HeartBeatTimer.cpp TaskHeartBeat.cpp TaskBatchUpdateData.cpp)
SET(SRCS ${SRCS}  TaskGroup.cpp Driver.cpp TagReader.cpp)
SET(SRCS ${SRCS} MainTask.cpp)
SET(SRCS ${SRCS} DrvCtrlTask.cpp TaskDrvDevStatus.cpp DrvDevStatusTimer.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME drdrivercommon)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS ACE tinyxml drlog drcomm drnetqueue drlogimpl cppsqlite sqlite3 drdriverapi shmqueue drrmapi)

IF(UNIX)
	SET(LINK_LIBS ${LINK_LIBS} pthread)
ELSE(UNIX)
	SET(LINK_LIBS ${LINK_LIBS} exceptionreport dbghelp)
ENDIF(UNIX)

INCLUDE($ENV{DRDIR}CMakeSpecOutPath)
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)