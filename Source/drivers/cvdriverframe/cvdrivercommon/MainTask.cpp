/**************************************************************
 *  Filename:    MainTask.cpp
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: �������������.
 *
 *  @author:    lijingjing
 *  @version    05/28/2008  lijingjing  Initial Version
 *  @version	9/17/2012  baoyuansong  ���ݿ������޸�Ϊ�ַ������ͣ������޸��������ݿ�ʱ�Ľӿ�
 *  @version	3/1/2013   baoyuansong  �����ɵ����ɿ���߼������豸��û���������ݿ�ʱ��ֱ���ɵ��������ݿ� .
**************************************************************/

#include <ace/Auto_Ptr.h>
#include "ace/Basic_Types.h"
#include "MainTask.h"
#include "DrvCtrlTask.h"
#include "errcode/error_code.h"
#include "TaskGroup.h"
#include "Device.h"
#include "DataBlock.h"
#include "common/DrvConfigXMLDef.h"
#include "common/cvcomm.hxx"
#include "common/cvGlobalHelper.h"
#include "driversdk/cvdrivercommon.h"
#include "TagDataBlockBuilder.h"
#include "math.h"
#include "ace/High_Res_Timer.h"
#include "gettext/libintl.h"
#include "TaskHeartBeat.h"
#include "TaskDrvDevStatus.h"
#include "TaskBatchUpdateData.h"
#include "processdb/DriverApi.h"
#include "common/OPAPI.h"

//#define _(STRING) gettext(STRING)
#define _(STRING) STRING
// extern STATERWINSTANCE g_pStateRW;
extern CCVLog g_CVDrvierCommonLog;
extern bool  g_bMultiLink;
extern bool g_bAlive;

std::map<long, string> g_mapAllTags;
std::map<long, string> g_mapTraceTag;
bool g_bNeedTrace;
ACE_Recursive_Thread_Mutex g_mtxMapTraceTags;
std::map<int32, ConfigErrTags > g_mapConfigErrTags;

#define  CHECK_NULL_RETURN_ERR(X) \
		{ if(!X) return EC_ICV_DA_CONFIG_FILE_STRUCTURE_ERROR; }

#define	XML_ELEMENTNODE_DATABLOCK	"datablock"
#define	XML_ELEMENTNODE_DEVICE		"device"
#define XML_ELEMENTNODE_NAME		"name"
#define XML_ELEMENTNODE_PARAM1		"param1"
#define XML_ELEMENTNODE_PARAM2		"param2"
#define XML_ELEMENTNODE_PARAM3		"param3"
#define XML_ELEMENTNODE_TASK		"task"
#define XML_ELEMENTNODE_CONNTYPE	"conntype"
#define XML_ELEMENTNODE_CONNPARAM	"connparam"
#define XML_ELEMENTNODE_CYCLERATE	"cyclerate"
#define XML_ELEMENTNODE_RECVTIMEOUT	"recvtimeout"
#define XML_ELEMENTNODE_CONNUM	    "connum"

#define XML_ELEMENTNODE_ADDRESS		"address"
#define XML_ELEMENTNODE_ELEMCOUNT	"elemcount"
#define XML_ELEMENTNODE_ELEMBITS	"elembits"
#define XML_ELEMENTNODE_ELEMBYTES	"elembytes"
#define XML_ELEMENTNODE_TYPE		"type"
#define XML_ELEMENTNODE_PHASE		"phase"

#define TaskGroup_NAME_PREFIX		"TaskGroup"

#define BITSFLOAT_PER_BYTE			8.0f
// DIT����ָ��
//void IOSyncCallBack(IOSYNCDATA *pSyncData, void* pCallbackParam);
void ControlCallback(TProtoDriverAPICTRLMsg* pData);



int tag_trace_callback(const std::list<OP_TRACE_TAG_Record>& allTraceTags )
{
	ACE_Guard<ACE_Recursive_Thread_Mutex> lockguard(g_mtxMapTraceTags);
	g_mapTraceTag.clear();
	if (allTraceTags.empty())
	{
		g_bNeedTrace = false;
		CV_INFO(g_CVDrvierCommonLog, "STOP Tracing.");
		return ICV_SUCCESS;
	}

	for(std::list<OP_TRACE_TAG_Record>::const_iterator iter = allTraceTags.begin(); iter != allTraceTags.end(); ++iter)
	{
		std::map<long, string>::iterator itMap = g_mapAllTags.find(iter->nTagID);
		if (itMap != g_mapAllTags.end() && (0 ==strcmp(iter->szFieldName, "F_CV") || 0 ==strcmp(iter->szFieldName, "A_CV")))
		{
			g_mapTraceTag.insert(make_pair(iter->nTagID, iter->szTagName));
			CV_INFO(g_CVDrvierCommonLog, "Trace Tag :%s tagid %d", iter->szTagName, iter->nTagID);
		}
	}	

	g_bNeedTrace = !g_mapTraceTag.empty();

	return ICV_SUCCESS;
}


/**
 *  ���캯��.
 *
 *  @param  -[in,out]  CDriver*  pDriver: [comment]
 *
 *  @version     05/30/2008  lijingjing  Initial Version.
 */
CMainTask::CMainTask()
{
	m_bStop = false;
	m_bStopped = false;
	m_bManualRefreshConfig = false;
	m_pOutputQuene = NULL;
	m_pTaskHeartBeat = new CTaskHeartBeat();
	m_pTaskDrvDevStatus = new CTaskDrvDevStatus();
	m_pTaskBatchUpdateData = new CTaskBatchUpdateData();
	m_pTaskBatchUpdateData2 = new CTaskBatchUpdateData2();
	m_pTaskGroupGuard = new CTaskGroupGuard();
	m_pTaskConfigErrTag = new CTaskConfigErrTag();
	mapTagIDBlockAddr.clear();
}

/**
 *  ��������.
 *
 *
 *  @version     05/30/2008  lijingjing  Initial Version.
 */
CMainTask::~CMainTask()
{
	if(m_pTaskHeartBeat)
	{
		delete m_pTaskHeartBeat;
		m_pTaskHeartBeat = NULL;
	}
	if(m_pTaskDrvDevStatus)
	{
		delete m_pTaskDrvDevStatus;
		m_pTaskDrvDevStatus = NULL;
	}
	if(m_pTaskBatchUpdateData)
	{
		delete m_pTaskBatchUpdateData;
		m_pTaskBatchUpdateData = NULL;
	}
	if(m_pTaskBatchUpdateData2)
	{
		delete m_pTaskBatchUpdateData2;
		m_pTaskBatchUpdateData2 = NULL;
	}

	if(m_pTaskGroupGuard)
	{
		delete m_pTaskGroupGuard;
		m_pTaskGroupGuard = NULL;
	}

	if (m_pTaskConfigErrTag)
	{
		delete m_pTaskConfigErrTag;
		m_pTaskConfigErrTag = NULL;
	}

	ClearTagMap();	
}

/**
 *  �麯�����̳���ACE_Task.
 *
 *
 *  @version     05/30/2008  lijingjing  Initial Version.
 */
int CMainTask::svc()
{	
	ACE_High_Res_Timer::global_scale_factor();
	int32 nRet = ICV_SUCCESS;
	CV_INFO(g_CVDrvierCommonLog, _("CMainTask thread start, threadid %x"), ACE_Thread::self());
	nRet = OnStart();
	if(nRet != DRV_SUCCESS)
	{
		CV_ERROR(g_CVDrvierCommonLog, nRet, _("CMainTask thread exit!"));// �豸��%s�߳��˳�
		m_bStopped = true;
		return nRet;
	}
	
	CV_INFO(g_CVDrvierCommonLog, _("CMainTask thread OnStart!"));
	while(!m_bStop)
	{
		// �ж��Ƿ����ֹ�������������
// 		if(m_bManualRefreshConfig)
// 		{
// 			CV_INFO(g_CVDrvierCommonLog, _("CMainTask thread Handle ManualRefreshConfig!"));
// 			HandleOnlineConfig();
// 			m_bManualRefreshConfig = false;
// 		}

		// ��ȡһ����������, �����һ��
		OUTPUTCMD output;
		nRet = m_pOutputQuene->dequeue(output);
		if (nRet != ICV_SUCCESS)
		{
			CV_INFO(g_CVDrvierCommonLog, _("CMainTask thread m_pOutputQuene Dequeue nRet : %d!"), nRet);
			continue;
		}

		switch(output.chOutputType)
		{
		case OUTPUT_ON:
		case OUTPUT_OFF:
			// ע���ע���쳣��������
			LoginException(&output);
			break;

		case OUTPUT_WRITE:
			// д��������
			{
				CV_INFO(g_CVDrvierCommonLog, _("CMainTask thread ProcessWriteCmd"));
				ACE_Time_Value tv_abs = ACE_OS::gettimeofday();
				ACE_Time_Value tv_timeout;
				tv_timeout.set(output.lSecond, output.lMSecond*1000);
				if(tv_abs > tv_timeout)
				{
					CV_ERROR(g_CVDrvierCommonLog, -1, _("CMainTask thread ProcessWriteCmd Tagid : %d timeout!, tv_timeout sec %d, tv_timeout usec : %d"), 
						output.nTagID, tv_timeout.sec(), tv_timeout.msec());
				}
				else
				{
					ProcessWriteCmd(&output);
				}
				break;
			}

		case OUTPUT_EXIT:
			// ֹͣ��������
			g_bAlive = false; // ֪ͨmain�����ر�.main������رձ��߳�
			break;
		case OUTPUT_REFRESH:
			// ����ˢ������
			HandleOnlineConfig();
			break;
		default:
			break;
		}
	}

	OnStop();

	CV_INFO(g_CVDrvierCommonLog, _("CMainTask thread stop, threadid %x"), ACE_Thread::self());
	CV_INFO(g_CVDrvierCommonLog, _("Driver %s normally exit!"), g_strDrvName.c_str());//���� %s �����˳���
	m_bStopped = true;
	return DRV_SUCCESS;	
}

/**
 *  ע���ע���쳣����.
 *
 *  @param  -[in]  OUTPUT_QUEUE_DESC*  pOutput: [comment]
 *
 *  @version     05/30/2008  lijingjing  Initial Version.
 */
long CMainTask::LoginException(OUTPUTCMD* pOutput)
{
	long nStatus = DRV_SUCCESS;
	/*
	//����DataBlock
	CDataBlock *pDataBlock = GetDataBlock((char *)pOutput->szTagAddr);
	if (NULL == pDataBlock)
	{
		Drv_LogMessage(DRV_LOGLEVEL_ERROR, "���ݵ�ַ��(%s)�޷����ҵ���Ӧ��DataBlock��", (char *)pOutput->szTagAddr);
		return -1;
	}

	EXCEPTION_INFO *pExceptionInfo = NULL;
	EXCEPTION_INFO *pExceptionInfoInList = NULL;
	bool bFound = false;
	std::list<EXCEPTION_INFO*>::iterator iter;


	switch (pOutput->chOutputType)
	{
	case OUTPUT_ON:
		// ���ҵ�ǰ�쳣�����Ƿ��Ѿ����ڣ������򲻽���ע��
		if (pOutput->nExcDataSize <= 0)
		{
            LH_WARN(_("The length of exception data is incorrect!")); //ע���쳣���ݵĳ��Ȳ���ȷ
			return EC_ICV_DA_EXCEPTION_ALREADY_EXISTS;
		}
		iter = pDataBlock->m_ExceptionList.begin();
		for ( ; iter != pDataBlock->m_ExceptionList.end(); iter++)
		{
			pExceptionInfoInList = *iter;
			if ((pExceptionInfoInList->nByteOffset == pOutput->nByteOffset)
				&& (pExceptionInfoInList->nBitOffset == pOutput->nBitOffset)
				&& (pExceptionInfoInList->chLbhType == pOutput->chLbhType)
				&& (pExceptionInfoInList->nLbhIPN == pOutput->nIpn)
				&& (pExceptionInfoInList->chEguType == pOutput->chEguType)
				&& (pExceptionInfoInList->nExcDataSize == pOutput->nExcDataSize)
				&& (pExceptionInfoInList->chOpt == pOutput->chOpt))
			{
				bFound = true;
				break;
			}
		}

		// �����ڣ������Ӹ�����¼
		if (!bFound)
		{
			// ����һ���쳣���ݵļ�¼
			// �����˳�ʱ��ɾ��new�Ķ���
			pExceptionInfo = new EXCEPTION_INFO;
			pExceptionInfo->chOpt = pOutput->chOpt;
			pExceptionInfo->fLow = pOutput->fLow;
			pExceptionInfo->fHigh = pOutput->fHigh;
			pExceptionInfo->nByteOffset = pOutput->nByteOffset;
			pExceptionInfo->nBitOffset = pOutput->nBitOffset;
			pExceptionInfo->chLbhType = pOutput->chLbhType;	
			pExceptionInfo->nLbhIPN = pOutput->nIpn;
			pExceptionInfo->chEguType = pOutput->chEguType;
			pExceptionInfo->nExcDataSize = pOutput->nExcDataSize;
			pDataBlock->m_ExceptionList.push_back(pExceptionInfo);
			pExceptionInfo->bTrigger = true;
            LH_DEBUG(_("Register exception data successfully!"));//ע���쳣���ݳɹ�!
		}
		else
		{
            LH_DEBUG(_("Exception data has been registered, the registration again failed!"));//�쳣�����Ѿ�ע������ٴ�ע��ʧ��
			nStatus = EC_ICV_DA_EXCEPTION_ALREADY_EXISTS;
		}
		break;
		
	case OUTPUT_OFF:
		// ���ҵ�ǰ�쳣�����Ƿ��Ѿ����ڣ�������ɾ��������¼
		iter = pDataBlock->m_ExceptionList.begin();
		for ( ; iter != pDataBlock->m_ExceptionList.end(); iter++)
		{
			pExceptionInfoInList = *iter;
			if ((pExceptionInfoInList->nByteOffset == pOutput->nByteOffset)
				&& (pExceptionInfoInList->nBitOffset == pOutput->nBitOffset)
				&& (pExceptionInfoInList->chLbhType == pOutput->chLbhType)
				&& (pExceptionInfoInList->nLbhIPN == pOutput->nIpn)
				&& (pExceptionInfoInList->chEguType == pOutput->chEguType)
				&& (pExceptionInfoInList->nExcDataSize == pOutput->nExcDataSize)
				&& (pExceptionInfoInList->chOpt == pOutput->chOpt) )
			{
				break;
			}
		}

		if (iter == pDataBlock->m_ExceptionList.end())
		{
            LH_WARN(_("Exception data has not yet been registered, canceled exception data failed!"));//�쳣������δע�����ע���쳣����ʧ��
			nStatus = EC_ICV_DA_EXCEPTION_NOT_FOUND;
		}
		else
		{
			pDataBlock->m_ExceptionList.erase(iter);
			delete pExceptionInfoInList;
            LH_DEBUG(_("Canceled exception data successfully!"));//ע���쳣���ݳɹ�
		}

		break;
	}
	*/
	return nStatus;	
}

// �����豸�����ݿ����Ƶõ������顢�豸�����ݿ�ָ��.��Ҫ����ͬ���豸�����ڶ���������е����
bool CMainTask::GetTaskGroupByDeviceAndDataBlockName(string strDeviceName, string strDataBlockName, 
	CTaskGroup *&pTaskGroup, CDevice *&pDevice, CDataBlock *pDataBlock)
{
	bool bFound = false;
	pTaskGroup = NULL;
	pDevice = NULL;
	pDataBlock = NULL;

	std::map<string, CTaskGroup*>::iterator iter = m_driverInfo.m_mapTaskGroups.begin();	
	// �����豸�飬�����豸
	for ( ; iter != m_driverInfo.m_mapTaskGroups.end(); iter++)
	{
		CTaskGroup *pTmpTaskGroup = iter->second;
		std::map<string, CDevice*>::iterator iter_device = pTmpTaskGroup->m_mapDevices.find(strDeviceName);
		if(iter_device !=  pTmpTaskGroup->m_mapDevices.end())
		{
			CDevice *pTmpDevice = iter_device->second;
			std::map<string, CDataBlock*>::iterator itDataBlock = pTmpDevice->m_mapDataBlocks.find(strDataBlockName);
			if(itDataBlock != pTmpDevice->m_mapDataBlocks.end())
			{
				pTaskGroup = pTmpTaskGroup;
				pDevice = pTmpDevice;
				pDataBlock = itDataBlock->second;
				return true;
			}
		}
	}
	return false;
}
/**
 *  ����д��������.
 *
 *  @param  -[in]  OUTPUT_QUEUE_DESC*  pOutput: [comment]
 *
 *  @version     07/10/2008  lijingjing  Initial Version.
 */
long CMainTask::ProcessWriteCmd(OUTPUTCMD* pOutput)
{
	long nStatus = DRV_SUCCESS;
	string strDeviceName, strDeviceName1, strDeviceName2;
	string strBlockName, strBlockName1, strBlockName2;
	string strTagAddr, strTagAddr1, strTagAddr2;
	uint8 nDataType, nTagType;
	long nByteOffset = 0;
	long nByteOffset1 = 0;
	long nByteOffset2 = 0;
	short nBitOffset = 0;
	short nBitOffset1 = 0;
	short nBitOffset2 = 0;

	// ���ݵ�ַ�õ��豸��Ϣ�������ַ������ð��:����
	/// string strFullAddr = pOutput->szTagAddr;
	std::map<int32, TagBlockAddr*>::iterator iter;
	{
		ACE_Guard<ACE_Recursive_Thread_Mutex> guard(m_lock);
		iter = mapTagIDBlockAddr.find(pOutput->nTagID);

		if(iter == mapTagIDBlockAddr.end())
		{
			CV_ERROR(g_CVDrvierCommonLog, -1,  _("ProcessWriteCmd mapTagIDBlockAddr find tagid : %d failed"), pOutput->nTagID);
			return -1;
		}

		strDeviceName = iter->second->strDeviceName;
		strBlockName = iter->second->strDataBlockName;
		strTagAddr = iter->second->strTagAddr;
		nDataType = iter->second->nDataType;
		nTagType = iter->second->nTagType;
		nByteOffset = iter->second->nBitOffset / ((int32)BITSFLOAT_PER_BYTE);
		nBitOffset = iter->second->nBitOffset % ((int32)BITSFLOAT_PER_BYTE);

		if(iter->second->nTagType == ICV_TAGTYPE_MDI)
		{
			strDeviceName1 = iter->second->strDeviceName1;
			strBlockName1 = iter->second->strDataBlockName1;
			strTagAddr1 = iter->second->strTagAddr1;
			nDataType = iter->second->nDataType;
			nTagType = iter->second->nTagType;
			nByteOffset1 = iter->second->nBitOffset1 / ((int32)BITSFLOAT_PER_BYTE);
			nBitOffset1 = iter->second->nBitOffset1 % ((int32)BITSFLOAT_PER_BYTE);

			strDeviceName2 = iter->second->strDeviceName2;
			strBlockName2 = iter->second->strDataBlockName2;
			strTagAddr2 = iter->second->strTagAddr2;
			nDataType = iter->second->nDataType;
			nTagType = iter->second->nTagType;
			nByteOffset2 = iter->second->nBitOffset2 / ((int32)BITSFLOAT_PER_BYTE);
			nBitOffset2 = iter->second->nBitOffset2 % ((int32)BITSFLOAT_PER_BYTE);
		}
	}
	CV_INFO(g_CVDrvierCommonLog, _("handling control command , the device (%s), datablock (%s), nByteOffset : %d, nBitOffset : %d."), strDeviceName.c_str(), strBlockName.c_str(), nByteOffset, nBitOffset);

	if(strDeviceName.empty() || strBlockName.empty())
	{
        //��������ʱ��δ�ҵ��豸(%s)�����ݿ�(%s)����
		CV_ERROR(g_CVDrvierCommonLog, -1, _("When handling control command , it found not the device (%s) or datablock (%s) name."), strDeviceName.c_str(), strBlockName.c_str());
		return -1;
	}
    
	CTaskGroup *pTaskGroup = NULL;
	CDevice* pDevice = NULL;
	CDataBlock *pDataBlock = NULL;
	char szCmdData[ICV_BLOBVALUE_MAXLEN];
	int32 nCmdDataLen = 0;
	if(iter->second->nTagType != ICV_TAGTYPE_MDI)
	{
			if(false == GetTaskGroupByDeviceAndDataBlockName(strDeviceName, strBlockName, pTaskGroup, pDevice, pDataBlock))
			{
				//��������ʱ���������������и��ݵ�ַ�豸 %s �����ݿ� %s δ�ҵ���������Ϣ
				CV_ERROR(g_CVDrvierCommonLog, -2, _("When handling control command , it found not task group info in all task groups by the device (%s) and datablock (%s) name."), 
					 strDeviceName.c_str(), strBlockName.c_str());
				return -2;
			}

			memset(szCmdData, 0, sizeof(szCmdData));
			if(ceil(pOutput->nCmdDataBits/BITSFLOAT_PER_BYTE) > sizeof(szCmdData))
			{
				//�� %s %s ��������ָ��ȳ��� %d��
				CV_ERROR(g_CVDrvierCommonLog, -3, _("The length of control command that sent to device(%s):datablock(%s) is exceed %d."), strDeviceName.c_str(), strBlockName.c_str(), sizeof(szCmdData));
				return -3;
			}    

			nCmdDataLen = (int32)ceil(pOutput->nCmdDataBits/BITSFLOAT_PER_BYTE);
			memcpy(szCmdData, pOutput->szCmdData, nCmdDataLen);

			pTaskGroup->PostWriteCmd(strDeviceName, strBlockName, strTagAddr, pOutput->nTagID, nDataType, nTagType, nByteOffset, nBitOffset, szCmdData, pOutput->nCmdDataBits);
	}
	else
	{
		memset(szCmdData, 0, sizeof(szCmdData));
		char nCmd;
		char nCmd0;
		char nCmd1;
		char nCmd2;
		if(ceil(pOutput->nCmdDataBits/BITSFLOAT_PER_BYTE) > sizeof(szCmdData))
		{
			//�� %s %s ��������ָ��ȳ��� %d��
			CV_ERROR(g_CVDrvierCommonLog, -3,  _("The length of control command that sent to device(%s):datablock(%s) is exceed %d."), strDeviceName.c_str(), strBlockName.c_str(), sizeof(szCmdData));
			return -3;
		}    

		nCmdDataLen = (int32)ceil(pOutput->nCmdDataBits/BITSFLOAT_PER_BYTE);
		if(nCmdDataLen != 1)
		{
			CV_ERROR(g_CVDrvierCommonLog, -1,  _("The length of control command that sent to device(%s):datablock(%s) is not 1."), strDeviceName.c_str(), strBlockName.c_str());
			return -1;
		}
		nCmd = pOutput->szCmdData[0];
		nCmd0 = nCmd & 0x01;
		nCmd1 = (nCmd & 0x02) >> 1;
		nCmd2 = (nCmd & 0x04) >> 2;

		if(false == GetTaskGroupByDeviceAndDataBlockName(strDeviceName, strBlockName, pTaskGroup, pDevice, pDataBlock))
		{
			//��������ʱ���������������и��ݵ�ַ�豸 %s �����ݿ� %s δ�ҵ���������Ϣ
			CV_ERROR(g_CVDrvierCommonLog, -2,  _("When handling control command , it found not task group info in all task groups by the device (%s) and datablock (%s) name."), 
				strDeviceName.c_str(), strBlockName.c_str());
			return -2;
		}

		pTaskGroup->PostWriteCmd(strDeviceName, strBlockName, strTagAddr, pOutput->nTagID, nDataType, nTagType, nByteOffset, nBitOffset, &nCmd0, BITS_PER_BYTE);

		if(false == GetTaskGroupByDeviceAndDataBlockName(strDeviceName1, strBlockName1, pTaskGroup, pDevice, pDataBlock))
		{
			//��������ʱ���������������и��ݵ�ַ�豸 %s �����ݿ� %s δ�ҵ���������Ϣ
			CV_ERROR(g_CVDrvierCommonLog, -2, _("When handling control command , it found not task group info in all task groups by the device (%s) and datablock (%s) name."), 
				strDeviceName1.c_str(), strBlockName1.c_str());
			return -2;
		}

		pTaskGroup->PostWriteCmd(strDeviceName1, strBlockName1, strTagAddr1, pOutput->nTagID, nDataType, nTagType, nByteOffset1, nBitOffset1, &nCmd1, BITS_PER_BYTE);

		if(false == GetTaskGroupByDeviceAndDataBlockName(strDeviceName2, strBlockName2, pTaskGroup, pDevice, pDataBlock))
		{
			//��������ʱ���������������и��ݵ�ַ�豸 %s �����ݿ� %s δ�ҵ���������Ϣ
			CV_ERROR(g_CVDrvierCommonLog, -2,  _("When handling control command , it found not task group info in all task groups by the device (%s) and datablock (%s) name."), 
				strDeviceName2.c_str(), strBlockName2.c_str());
			return -2;
		}

		pTaskGroup->PostWriteCmd(strDeviceName2, strBlockName2, strTagAddr2, pOutput->nTagID, nDataType, nTagType, nByteOffset2, nBitOffset2, &nCmd2, BITS_PER_BYTE);
	}

	return DRV_SUCCESS;
}


// ���߸�������
void CMainTask::HandleOnlineConfig()
{
// 	//�����dit2�ڴ���tag�������ݿ��ڴ�ӳ�䣬����ÿ��Ҫ�ж�tag��ı仯Ч�ʺܲ�
// 	CTagInfoArea *pTagInfoArea = g_pDIT->GetTagInfoArea();
// 	if (NULL != pTagInfoArea)
// 		pTagInfoArea->ResetTagInfo();
	int32 nRet = ICV_SUCCESS;
	CV_CRITICAL(g_CVDrvierCommonLog,-1,  _("Unexpected step!"));
	return;
	CDriver driver;
	driver.m_bAutoDeleted = false;	//����TaskGroup�����Զ�ɾ������Ϊ��ɾ�ĵĶ��ڳ����н����˴���

	ACE_Time_Value timeValue = ACE_OS::gettimeofday();
	TCV_TimeStamp timeStamp;
	timeStamp.tv_sec = (uint32_t)timeValue.sec();
	timeStamp.tv_usec = (uint32_t)timeValue.usec();
	char szTime[ICV_HOSTNAMESTRING_MAXLEN] = {'\0'};
	cvcommon::CastTimeToASCII(szTime, ICV_HOSTNAMESTRING_MAXLEN, timeStamp);

	//�����һ��tag Map,���¼���
	ClearTagMap();
	//��ʼ��ʼ��driverapi
	nRet = CVDrv_Release(g_strDrvName);
	if(nRet != ICV_SUCCESS)
	{
		CV_CRITICAL(g_CVDrvierCommonLog, nRet,  _("CVDrv_Release failed : %d"), nRet);
		return;
	}

	int nSuccess = LoadConfig(driver);

	//��ʼ��ʼ��driverapi
	nRet = CVDrv_Init(g_strDrvName, g_bMultiLink);
	if(nRet != ICV_SUCCESS)
	{
		CV_CRITICAL(g_CVDrvierCommonLog, nRet,  _("CVDrv_Init failed : %d"), nRet);
		return;
	}
	CVDrv_SetControlCallback(ControlCallback);

	// char szKey[ICV_HOSTNAMESTRING_MAXLEN];
	// memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
	// ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#lastcfgmodifiedtime", g_strDrvName.c_str());
	// STATERW_SetStringCommand(&g_pStateRW, szKey, szTime);
	if(nSuccess != DRV_SUCCESS)
	{
		g_CVDrvierCommonLog.LogMessage(LOG_LEVEL_ERROR, _("Load config file failed, return code:%d."), nSuccess);//���������ļ�ʧ�ܣ�������: %d
		std::map<string, CTaskGroup*>::iterator itTaskGrp = driver.m_mapTaskGroups.begin();
		for(; itTaskGrp != driver.m_mapTaskGroups.end();itTaskGrp ++)
        {
            CTaskGroup* pTaskGrpTmp = itTaskGrp->second;
            CV_WARN(g_CVDrvierCommonLog, nSuccess, _("(HandleOnlineConfig)delete TaskGroup name: %s,device num:%d"), pTaskGrpTmp->m_drvObjBase.m_strName.c_str(), pTaskGrpTmp->m_mapDevices.size());
            SAFE_DELETE(itTaskGrp->second);
        }
		
		driver.m_mapTaskGroups.clear();

		// char szKey[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szKey, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szKey, sizeof(szKey), "driver#%s#status", g_strDrvName.c_str());
		// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
		// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
		// ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", nSuccess, szTime);
		// STATERW_SetStringCommand(&g_pStateRW, szKey, szStatus);
		return;
	}

	// ���������ú���������֮��Ĳ����Ϣ
	std::map<string, CTaskGroup*> mapToDelete, mapToModify, mapToAdd;
	std::set_difference(m_driverInfo.m_mapTaskGroups.begin(), m_driverInfo.m_mapTaskGroups.end(), 
		driver.m_mapTaskGroups.begin(), driver.m_mapTaskGroups.end(), 
		std::inserter(mapToDelete, mapToDelete.begin()), MapKeyCmp<std::map<string, CTaskGroup*>::value_type>);

	std::set_intersection(driver.m_mapTaskGroups.begin(), driver.m_mapTaskGroups.end(), 
		m_driverInfo.m_mapTaskGroups.begin(), m_driverInfo.m_mapTaskGroups.end(), 
		std::inserter(mapToModify, mapToModify.begin()), MapKeyCmp<std::map<string, CTaskGroup*>::value_type>);

	std::set_difference(driver.m_mapTaskGroups.begin(), driver.m_mapTaskGroups.end(), 
		m_driverInfo.m_mapTaskGroups.begin(), m_driverInfo.m_mapTaskGroups.end(), 
		std::inserter(mapToAdd, mapToAdd.begin()), MapKeyCmp<std::map<string, CTaskGroup*>::value_type>);

	//ֹͣ��������
	if(m_pTaskHeartBeat)
		m_pTaskHeartBeat->Stop();

	if (m_pTaskHeartBeat)
	{
		delete m_pTaskHeartBeat;
		m_pTaskHeartBeat = NULL;
	}

	if(m_pTaskDrvDevStatus)
		m_pTaskDrvDevStatus->Stop();

	if (m_pTaskDrvDevStatus)
	{
		delete m_pTaskDrvDevStatus;
		m_pTaskDrvDevStatus = NULL;
	}

	if(m_pTaskBatchUpdateData)
		m_pTaskBatchUpdateData->Stop();

	if (m_pTaskBatchUpdateData)
	{
		delete m_pTaskBatchUpdateData;
		m_pTaskBatchUpdateData = NULL;
	}

	if(m_pTaskBatchUpdateData2)
		m_pTaskBatchUpdateData2->Stop();

	if (m_pTaskBatchUpdateData2)
	{
		delete m_pTaskBatchUpdateData2;
		m_pTaskBatchUpdateData2 = NULL;
	}

	if (m_pTaskConfigErrTag)
		m_pTaskConfigErrTag->Stop();

	if (m_pTaskConfigErrTag)
	{
		delete m_pTaskConfigErrTag;
		m_pTaskConfigErrTag = NULL;
	}

	// �ȴ���ɾ�����豸��
	DelTaskGrpsFromDIT(mapToDelete);
	
	// �����޸ĵ��豸��,mapToModify ��ŵ����µ��豸��
	ModifyTaskGrpsInDIT(mapToModify);

	// �޸ĵ��豸������ø��²����������߳�ִ�У������豸��Ĳ�����Ҫ�ȴ������̸߳���������ɺ�ſ�ʼִ�У��������ܱ�֤DIT���¼ӵĿ鲻���ڸ�������ʱ��ɾ��
	std::map<string, CTaskGroup*>::iterator iterNewTaskGrp = mapToModify.begin();
	for(; iterNewTaskGrp != mapToModify.end(); iterNewTaskGrp++)
	{
		std::map<string, CTaskGroup*>::iterator iterCurTaskGrp = m_driverInfo.m_mapTaskGroups.find(iterNewTaskGrp->first);
		if (iterCurTaskGrp == m_driverInfo.m_mapTaskGroups.end())
			continue;

		while (!iterCurTaskGrp->second->GetFreshDone())
		{
			ACE_Time_Value tv;
			tv.msec(DEFAULT_SLEEP_TIME);	// 50ms
			ACE_OS::sleep(tv);
		}
		iterCurTaskGrp->second->SetFreshDone(false);
	}

	// �������ӵ��豸��
	AddTaskGrps2DIT(mapToAdd);
	std::map<string, CTaskGroup *>::iterator itTaskGrp = mapToAdd.begin();
	for(; itTaskGrp != mapToAdd.end(); itTaskGrp ++)
	{
		m_driverInfo.m_mapTaskGroups.insert(std::map<string, CTaskGroup*>::value_type(itTaskGrp->first, itTaskGrp->second));
		itTaskGrp->second->m_pMainTask = this;
	}
	mapToAdd.clear();

	//������������
	if (m_pTaskHeartBeat == NULL)
		m_pTaskHeartBeat = new CTaskHeartBeat();
	
	if(m_pTaskHeartBeat)
		m_pTaskHeartBeat->Start();

	if (m_pTaskDrvDevStatus == NULL)
		m_pTaskDrvDevStatus = new CTaskDrvDevStatus();

	if(m_pTaskDrvDevStatus)
		m_pTaskDrvDevStatus->Start();

	if (m_pTaskBatchUpdateData == NULL)
		m_pTaskBatchUpdateData = new CTaskBatchUpdateData();

	if(m_pTaskBatchUpdateData)
		m_pTaskBatchUpdateData->Start();

	if (m_pTaskBatchUpdateData2 == NULL)
		m_pTaskBatchUpdateData2 = new CTaskBatchUpdateData2();

	if(m_pTaskBatchUpdateData2)
		m_pTaskBatchUpdateData2->Start();

	if (m_pTaskConfigErrTag == NULL)
		m_pTaskConfigErrTag = new CTaskConfigErrTag();

	if (m_pTaskConfigErrTag)
		m_pTaskConfigErrTag->Start();
}


long CMainTask::AddTaskGrps2DIT(std::map<string, CTaskGroup*> &mapAddTaskGrp)
{
	long nStatus = DRV_SUCCESS;
	//  	CDriverInfoArea *pDriverInfoArea = g_pDIT->GetDriverInfoArea();
	//  	CHECK_INIT_FALSE_RETURN_ERR(pDriverInfoArea);
	//  	CDataArea* pDataArea  = g_pDIT->GetDataArea();
	//  	CHECK_INIT_FALSE_RETURN_ERR(pDataArea);
 
 	std::map<string, CTaskGroup*>::iterator iterTaskGrp = mapAddTaskGrp.begin();
 	for(; iterTaskGrp != mapAddTaskGrp.end(); iterTaskGrp++)
 	{
 		AddDevices2DIT(iterTaskGrp->second->m_mapDevices);
 		long lStarted = (*iterTaskGrp).second->Start();
 		if(lStarted != DRV_SUCCESS)
 		{
 			//��¼��־����ȡ�����ļ�ʧ��
             //�����豸��(%s)�߳�ʧ�ܣ�������:%d, ɾ���豸��!
 			CV_ERROR(g_CVDrvierCommonLog, lStarted, _("Start device group(%s) thread failed, error code %d, delete it!"), (char *)(*iterTaskGrp).second->m_drvObjBase.m_strName.c_str(), lStarted);
 			
            CTaskGroup *pTaskGrpTmp = (CTaskGroup *)(*iterTaskGrp).second;
            CV_WARN(g_CVDrvierCommonLog, lStarted, _("(AddTaskGrps2DIT)delete TaskGroup name: %s,device num:%d"), pTaskGrpTmp->m_drvObjBase.m_strName.c_str(), pTaskGrpTmp->m_mapDevices.size());
 			SAFE_DELETE(pTaskGrpTmp);
 
 			m_driverInfo.m_mapTaskGroups.erase(iterTaskGrp++);
 			continue;
 		}
 		CV_DEBUG(g_CVDrvierCommonLog, "TaskGroup [%s] started.", (*iterTaskGrp).second->m_drvObjBase.m_strName.c_str());
 	}
 	
	return DRV_SUCCESS;
}

void CMainTask::AddDevices2DIT(std::map<string, CDevice*> &mapDevices)
{
 	std::map<string, CDevice*>::iterator iter = mapDevices.begin();
 	for (; iter != mapDevices.end();)
 	{
 		DEVICE_CONFIG devCfg;
 		strcpy(devCfg.szName, iter->second->m_strName.c_str());
//  		long nRet = pDriverInfoArea->AddDeviceConfig(&devCfg);
//  		if (nRet != DRV_SUCCESS)
//  		{
//              //������DIT��DriverInfoArea�����豸(%s)��Ϣʧ��!
//  			g_CVDrvierCommonLog.LogErrMessage(nRet, _("Failed to add device info (%s) to DriverInfoArea of driver DIT!"), devCfg.szName);
//  
//  			SAFE_DELETE(iter->second);
//  			mapDevices.erase(iter++);
//  			continue;
//  		}
//  		Drv_LogMessage(DRV_LOGLEVEL_INFO, _("Add device %s in DIT"), iter->second->m_strName.c_str());
 
 		AddDataBlocks2DIT(iter->second->m_mapDataBlocks);
 		int nDeviceSupportMultilink = 0;
 		if(g_pfnOnDeviceAdd)
 			g_pfnOnDeviceAdd(iter->second);
 		iter++;
 	}
}

int CMainTask::AddDataBlock2DIT(CDataBlock *pDataBlock)
{
	// �����������ݿ飬��Ҫ���������֮ǰ����Ϊ�������ϢӦ�����������Ļص��л�ȡ����
	long lRet = DRV_SUCCESS;
	if(g_pfnOnDataBlockAdd)
		lRet = g_pfnOnDataBlockAdd(pDataBlock->m_pDevice, pDataBlock);

 	if(lRet != DRV_SUCCESS)
 	{
         //�豸 %s �������ݿ� %s ʱ�����������з���ֵΪ %d������ʧ��!
         CV_ERROR(g_CVDrvierCommonLog, lRet, _("For device %s, add datablock %s failed because of driver overloaded. return code:%d."), 
 			pDataBlock->m_pDevice->m_strName.c_str(), pDataBlock->m_strName.c_str(), lRet);
 		return -1;
 	}
 
 	if( pDataBlock->m_nBlockSize <= 0)
 	{
         //�豸 %s �������ݿ� %s ʱ������Ϊ %d���Ƿ�������ʧ��!
 		CV_ERROR(g_CVDrvierCommonLog, -1, _("For device %s, add datablock %s failed because of the DITBlockSize length(%d) is illegal."), 
 			pDataBlock->m_pDevice->m_strName.c_str(), pDataBlock->m_strName.c_str(), pDataBlock->m_nBlockSize);
 		return -1;
 	}
 
//  	// ����DIT�е����ݿ�
//  	lRet = pDataArea->CreateDataBlock(&pDataBlock->m_nDITBlockNumber, pDataBlock->m_nDITBlockSize + sizeof(TCV_TimeStamp) + sizeof(int) * 2);		
//  	if (lRet != DRV_SUCCESS)
//  	{
//  		// �������ݿ�ʧ�ܣ���¼��־        
//          //�豸 %s �������ݿ��豸�ڴ������ݿ� %s ʱ��������Ϊ %d���Ƿ�������ʧ��!
//  		Drv_LogMessage(DRV_LOGLEVEL_ERROR, _("For device %s, create a device memory area failed for datablock %s, return code:%d."), 
//  			pDataBlock->m_pDevice->m_strName.c_str(), pDataBlock->m_strName.c_str(), lRet);
//  		return -2;
//  	}
//  	
//  	//����ʱ�������������д����
//  	char szTimeRWCount[sizeof(TCV_TimeStamp) + sizeof(int) * 2];
//  	memset(szTimeRWCount, 0, sizeof(szTimeRWCount));
//  	lRet = pDataArea->SetDataBlockData_i(pDataBlock->m_nDITBlockNumber, sizeof(TCV_TimeStamp) + sizeof(int) * 2, (void*)szTimeRWCount, pDataBlock->m_nDITBlockSize); 
//  
//  	// ����DataBlock��Ϣ��DIT��DriverInfoArea��
//  	DATABLOCK_CONFIG dataBlockCfg;
//  	strcpy(dataBlockCfg.szName, pDataBlock->m_strName.c_str());
//  	dataBlockCfg.nBlockNumber = pDataBlock->m_nDITBlockNumber;
//  
//  	// ����Ĳ������ݿ��ͨ����Ϣ
//  	dataBlockCfg.nStartWord = ::atoi(pDataBlock->m_strAddress.c_str());
//  	dataBlockCfg.nLength = pDataBlock->m_nElemNum;
//  	dataBlockCfg.nEndWord = dataBlockCfg.nStartWord + dataBlockCfg.nLength;
//  	dataBlockCfg.nWordBytes = pDataBlock->m_nElemBits;
//  	//���´����߼���Ҫ��������³���
//  	//dit�д�ŵ�DATABLOCK_CONFIG���ݽṹ��nWordBytes��ע�⣺��ʾ����λ�������ֽڣ�����һ���ֽڱ�ʾ��������ֵΪ255��
//  	//���û����õ�Ԫ�ش�С����32���ֽ�ʱ���޷���ţ���˵�Ԫ�ش�С����32���ֽ�ʱ����������Ԫ�ش�СΪ1���ֽڣ���8λ����
//  	//��Ԫ�ظ���ֵ���󣨴�ֵ����Ϊunsigned short���㹻����,�����ƻ����������޸�Ϊshort�����Ǳ�¶���û���CVDATABLOCK�и�
//  	//�ֶ�Ϊunsigned short��������ʱ���޸ģ���5.5���޸ģ��������Ϳ��������û����ڵ���Ԫ�ش�С����32���ֽڵ�����ͬʱ��
//  	//ditû���κ�Ӱ�죬��Ϊdda��customdriverconfig.exe������dataBlockCfg.nWordBytes*dataBlockCfg.nLength����dit���ȣ�����
//  	//������飬���û���κ�Ӱ��
//  	if (pDataBlock->m_nElemBits > ACE_OCTET_MAX)
//  	{
//  		int nSize = pDataBlock->m_nElemBits * pDataBlock->m_nElemNum;
//  		dataBlockCfg.nWordBytes = BITS_PER_BYTE;
//  		dataBlockCfg.nLength = nSize % BITS_PER_BYTE == 0 ? nSize/BITS_PER_BYTE : 1 + nSize/BITS_PER_BYTE;
//  	}
//  
//  	dataBlockCfg.pBlockHandle = NULL;
//  	dataBlockCfg.chBlockType = 0;
//  	lRet = pDriverInfoArea->AddDataBlockConfig((char *)pDataBlock->m_pDevice->m_strName.c_str(), &dataBlockCfg);
//  	if (lRet != DRV_SUCCESS && EC_ICV_DA_DATABLOCK_CONFIG_OVERLAP != lRet)
//  	{
//          //������DIT��DriverInfoArea�������ݿ���Ϣ[Dev=%s, DataBlock=%s]ʧ�ܻ򼺴���!
//  		Drv_LogMessage(DRV_LOGLEVEL_ERROR, _("Failed to add datablock info to DriverInfoArea of driver DIT [Dev=%s, DataBlock=%s], maybe it has been existed!"),
//  			pDataBlock->m_pDevice->m_strName.c_str(), dataBlockCfg.szName);
//  		// ��m_AddDataBlockList���Ƴ�
//  		lRet = pDataArea->DelDataBlock(pDataBlock->m_nDITBlockNumber);
//  		if (lRet != DRV_SUCCESS)
//              //������DIT��DriverInfoArea�������ݿ���Ϣ[Dev=%s, DataBlock=%s]ʧ�ܻ򼺴���!��DIT��������ɾ�����Ϊ%d�Ŀ�ʧ��!
//  			Drv_LogMessage(DRV_LOGLEVEL_ERROR, _("[Dev=%s, DataBlock=%s]Failed to remove datablock with number %d from driver DIT data area!"),
//  			pDataBlock->m_pDevice->m_strName.c_str(), dataBlockCfg.szName, pDataBlock->m_nDITBlockNumber);
//  		return -3;
//  	}   
//  
//  	// ��ʼ�����ݿ�����
//  	QUALITY_STATE nQuality;
//  	nQuality.nQuality	= QUALITY_BAD;
//  	nQuality.nSubStatus = SS_NON_SPECIFIC;
//  	nQuality.nLimit		= LIMIT_NOT_LIMITED;
//  	nQuality.nLeftOver  = 0;
//  
//  	unsigned short *pQuality = (unsigned short *)&nQuality;
//  	pDataArea->SetDataBlockStatus(pDataBlock->m_nDITBlockNumber, *pQuality);
//  
//  	// �����ڲ���ַΪ��ʼ��ַ
//  	pDataArea->SetStartAddress(pDataBlock->m_nDITBlockNumber, ::atoi(pDataBlock->m_strAddress.c_str()));
//  
//  	//��������ʹ�õ��ֽ���
//  	pDataArea->SetBytesInUse(pDataBlock->m_nDITBlockNumber, pDataBlock->m_nDITBlockSize + sizeof(TCV_TimeStamp) + 2*sizeof(int));
//  
//  	TCV_TimeStamp cvTimeStamp = (timeval)ACE_OS::gettimeofday();
//  
//  	pDataArea->SetDataBlockData(pDataBlock->m_nDITBlockNumber, sizeof(TCV_TimeStamp), &cvTimeStamp, pDataBlock->m_nDITBlockSize + 2*sizeof(int)); //  - sizeof(TCV_TimeStamp)
	return DRV_SUCCESS;
}

void CMainTask::AddDataBlocks2DIT(std::map<string, CDataBlock*> &mapNewDataBlocks)
{
	std::map<string, CDataBlock*>::iterator iterBlk = mapNewDataBlocks.begin();
	while(iterBlk != mapNewDataBlocks.end())
	{
		int nRet = AddDataBlock2DIT(iterBlk->second);
		if(nRet != ICV_SUCCESS)
		{
			// ��m_AddDataBlockList���Ƴ�
            CDevice *pDevice = iterBlk->second->m_pDevice;
            string strBlkName = iterBlk->first;
			SAFE_DELETE(iterBlk->second);
			mapNewDataBlocks.erase(iterBlk++);
            if (pDevice != NULL && pDevice->m_mapDataBlocks.find(strBlkName) != pDevice->m_mapDataBlocks.end())
            {
                pDevice->m_mapDataBlocks.erase(strBlkName);
            }
		}
		else
			iterBlk++;
	}
}

// ����DIT�е��豸��Ϣ
void CMainTask::ModifyDevicesInDIT(std::map<string, CDevice*> &mapCurDevices, std::map<string, CDevice*> &mapModifyDevices)
{
	std::map<string, CDevice*>::iterator iterModifyDevice = mapModifyDevices.begin();
	for (; iterModifyDevice != mapModifyDevices.end(); iterModifyDevice++)
	{
		std::map<string, CDevice*>::iterator iterCurDevice = mapCurDevices.find(iterModifyDevice->first);
		if (iterCurDevice == mapCurDevices.end())
			continue;
		
		std::map<string, CDataBlock*> mapOldToDelete, mapToModify, mapNewToAdd;
		std::map<string, CDataBlock*> &mapModifyDataBlocks = iterModifyDevice->second->m_mapDataBlocks;
		std::map<string, CDataBlock*> &mapCurDataBlocks = iterCurDevice->second->m_mapDataBlocks;

		// ���������ú���������֮��Ĳ����Ϣ
		std::set_difference(mapCurDataBlocks.begin(), mapCurDataBlocks.end(), 
			mapModifyDataBlocks.begin(), mapModifyDataBlocks.end(),
			std::inserter(mapOldToDelete, mapOldToDelete.begin()), MapKeyCmp<std::map<string, CDataBlock*>::value_type>);

		std::set_intersection(mapModifyDataBlocks.begin(), mapModifyDataBlocks.end(), 
			mapCurDataBlocks.begin(), mapCurDataBlocks.end(), 
			std::inserter(mapToModify, mapToModify.begin()), MapKeyCmp<std::map<string, CDataBlock*>::value_type>);

		std::set_difference(mapModifyDataBlocks.begin(), mapModifyDataBlocks.end(), 
			mapCurDataBlocks.begin(), mapCurDataBlocks.end(), 
			std::inserter(mapNewToAdd, mapNewToAdd.begin()), MapKeyCmp<std::map<string, CDataBlock*>::value_type>);

		// ɾ���������Ѿ�û�е����ݿ飬��ͣ����Щ���ϵĶ�ʱ������ɾ�� by wangyadong 20141224
		for(std::map<string, CDataBlock*>::iterator iterDel = mapOldToDelete.begin(); iterDel != mapOldToDelete.end(); ++iterDel)
		{
			iterDel->second->StopTimer();
		}
		DelDataBlocksFromDIT(mapOldToDelete);
		for(std::map<string, CDataBlock*>::iterator iterDel = mapOldToDelete.begin(); iterDel != mapOldToDelete.end(); ++iterDel)
		{
			//SAFE_DELETE(iterDel->second)
			mapCurDataBlocks.erase(iterDel->first);
		}
		mapOldToDelete.clear();

		// �������������µ����ÿ�
		for(std::map<string, CDataBlock*>::iterator iterAdd = mapNewToAdd.begin(); iterAdd != mapNewToAdd.end(); ++iterAdd)
		{
			iterAdd->second->m_pDevice = iterCurDevice->second;
			mapCurDataBlocks.insert(std::map<string, CDataBlock*>::value_type(iterAdd->first, iterAdd->second));
		}
        AddDataBlocks2DIT(mapNewToAdd);

		ModifyDataBlocksInDIT(mapCurDataBlocks, mapToModify);

		// ����������ʱ��
		iterCurDevice->second->StartTimer();

		//�������޸ĺ�ɾ�������Ѿ��Կ�����˴����������Ҫ��ɾ���豸��ʱ������տ��б�������������ظ�ɾ��
		iterModifyDevice->second->m_mapDataBlocks.clear();
		SAFE_DELETE(iterModifyDevice->second);
	}
}

//�Ƚ�����Device�����ж��Ƿ���Ҫ������(DrvConfigLoader��ʹ��)
// ���true��ʾ��Ҫ��������
bool CMainTask::IsDeviceConfigChanged(CDevice* pCurActiveDev, CDevice* pNewDev)
{
	// �����������û�ж���ȽϷ���,�Ƚ����е�����
	if(*pCurActiveDev == *pNewDev)
		return false;
	else
		return true;
}

void CMainTask::ModifyDataBlocksInDIT(std::map<string, CDataBlock*> &mapCurDataBlocks, std::map<string, CDataBlock*> &mapModifyDataBlocks)
{
 	std::map<string, CDataBlock*>::iterator iterModifyBlk = mapModifyDataBlocks.begin();
 	for (; iterModifyBlk != mapModifyDataBlocks.end(); iterModifyBlk++)
 	{
 		std::map<string, CDataBlock*>::iterator iterCurBlk = mapCurDataBlocks.find(iterModifyBlk->first);
 		if(iterCurBlk == mapCurDataBlocks.end())
 			continue;
 
 		CDataBlock* pCurDataBlock = iterCurBlk->second;
 		CDataBlock* pNewDataBlock = iterModifyBlk->second;
 		if (*pCurDataBlock == *pNewDataBlock)
 		{
 			//pCurDataBlock->StopTimer();//֮����߼��л������豸�����еĶ�ʱ������ʱǰͣ������ȻӰ�����ü���
 			CV_INFO(g_CVDrvierCommonLog, _("[Dev=%s]Datablock %s was not modified."), // �豸[%s]�µ�[%s]��δ�����޸�
 				pCurDataBlock->m_pDevice->m_strName.c_str(),	pNewDataBlock->m_strName.c_str());
 			SAFE_DELETE(pNewDataBlock);
 			continue;
 		}
 
 		pCurDataBlock->StopTimer(); // stop timer of current datablock first
 		DelDataBlockFromDIT(pCurDataBlock);
 		*pCurDataBlock = *pNewDataBlock;
 		AddDataBlock2DIT(pCurDataBlock);
 
 		CV_INFO(g_CVDrvierCommonLog, _("[Dev=%s]Modify DataBlock %s successfully."),
 			pCurDataBlock->m_pDevice->m_strName.c_str(), pCurDataBlock->m_strName.c_str());
 
 		SAFE_DELETE(pNewDataBlock);
 	}
}

long CMainTask::ModifyTaskGrpsInDIT(std::map<string, CTaskGroup*> &mapModifyTaskGrp)
{
 	long nStatus = DRV_SUCCESS;
/* 	CDriverInfoArea *pDriverInfoArea = g_pDIT->GetDriverInfoArea();*/
//  	CHECK_INIT_FALSE_RETURN_ERR(pDriverInfoArea);
//  	CDataArea* pDataArea  = g_pDIT->GetDataArea();
//  	CHECK_INIT_FALSE_RETURN_ERR(pDataArea);
 
 	std::map<string, CTaskGroup*>::iterator iterNewTaskGrp = mapModifyTaskGrp.begin();
 	for(; iterNewTaskGrp != mapModifyTaskGrp.end(); iterNewTaskGrp++)
 	{
 		std::map<string, CTaskGroup*>::iterator iterCurTaskGrp = m_driverInfo.m_mapTaskGroups.find(iterNewTaskGrp->first);
 		if (iterCurTaskGrp == m_driverInfo.m_mapTaskGroups.end())
 			continue;
 
 		iterCurTaskGrp->second->PostGroupOnlineModifyCmd(iterNewTaskGrp->second);
 		//iterCurTaskGrp->second->OnOnlineConfigModified(iterNewTaskGrp->second);
 		
 	}

	return nStatus;
}

long CMainTask::DelTaskGrpsFromDIT(std::map<string, CTaskGroup*> &mapDelTaskGrp)
{
	long nStatus = DRV_SUCCESS;
//  	CDriverInfoArea *pDriverInfoArea = g_pDIT->GetDriverInfoArea();
//  	CHECK_INIT_FALSE_RETURN_ERR(pDriverInfoArea);
//  	CDataArea* pDataArea  = g_pDIT->GetDataArea();
//  	CHECK_INIT_FALSE_RETURN_ERR(pDataArea);
 
 	std::map<string, CTaskGroup*>::iterator iterTaskGrp = mapDelTaskGrp.begin();
 	for(; iterTaskGrp != mapDelTaskGrp.end(); iterTaskGrp++)
 	{
 		iterTaskGrp->second->Stop();
 		//DelDevicesFromDIT(iterTaskGrp->second->m_mapDevices, pDataArea, pDriverInfoArea);
 
        CTaskGroup* pTaskGrp = iterTaskGrp->second;
        CV_WARN(g_CVDrvierCommonLog, -1, _("(DelTaskGrpsFromDIT)delete TaskGroup name: %s,device num:%d"), pTaskGrp->m_drvObjBase.m_strName.c_str(), pTaskGrp->m_mapDevices.size());
 		SAFE_DELETE(iterTaskGrp->second);
 
 		// �ӵ�ǰ������map��ɾ������
 		std::map<std::string, CTaskGroup *>::iterator itCurTaskGrp = m_driverInfo.m_mapTaskGroups.find(iterTaskGrp->first);
 		if(itCurTaskGrp != m_driverInfo.m_mapTaskGroups.end())
 			m_driverInfo.m_mapTaskGroups.erase(itCurTaskGrp);
 	}
 	mapDelTaskGrp.clear();
	return DRV_SUCCESS;
}


void CMainTask::DelDataBlockFromDIT(CDataBlock *pDataBlock)
{
//  	long lStatus = pDataArea->DelDataBlock(pDataBlock->m_nDITBlockNumber);
//  	if (lStatus != DRV_SUCCESS)
//  	{
//          //[Dev=%s]��DIT������ɾ�����ݿ�[���� = %s,��� = %d]ʧ��
//  		g_CVDrvierCommonLog.LogErrMessage(lStatus, _("[Dev=%s]Delete datablock from the driver DIT data area failed[name = %s,number = %d]."),
//  			(char *)pDataBlock->m_pDevice->m_strName.c_str(), pDataBlock->m_strName.c_str(), pDataBlock->m_nDITBlockNumber);
//  	}
//  	//������ɾ����Ӧ���豸�����������������Լ�������Ϣ
//  	lStatus = pDriverInfoArea->DelDataBlockConfig((char *)pDataBlock->m_pDevice->m_strName.c_str(), (char *)pDataBlock->m_strName.c_str());
//  	if (lStatus != DRV_SUCCESS)
//  	{
//          //[Dev=%s]��DIT������Ϣ��ɾ�����ݿ�[���� = %s,��� = %d]ʧ��
//  		g_CVDrvierCommonLog.LogErrMessage(lStatus, _("[Dev=%s]Delete datablock from the driver DIT info area failed[name = %s,number = %d]."),
//  			(char *)pDataBlock->m_pDevice->m_strName.c_str(), pDataBlock->m_strName.c_str(), pDataBlock->m_nDITBlockNumber);
//  	}
}

void CMainTask::DelDataBlocksFromDIT(std::map<std::string, CDataBlock*> &mapDataBlocksToDel)
{
// 	std::map<std::string, CDataBlock*>::iterator iterBlk = mapDataBlocksToDel.begin();
// 	while(iterBlk != mapDataBlocksToDel.end())
// 	{
// 		DelDataBlockFromDIT(iterBlk->second, pDataArea, pDriverInfoArea);
// 		iterBlk++;
// 	}
}

void CMainTask::DelDevicesFromDIT( std::map<string, CDevice*> &mapDevices)
{
// 	std::map<std::string, CDevice*>::iterator iterDev = mapDevices.begin();
// 	while(iterDev != mapDevices.end())
// 	{
// 		DelDataBlocksFromDIT(iterDev->second->m_mapDataBlocks, pDataArea, pDriverInfoArea);
// 		Drv_LogMessage(DRV_LOGLEVEL_INFO, _("Delete device %s in DIT"), iterDev->first.c_str());
// 		iterDev ++;
// 	}
}

long CMainTask::OnStart()
{
// 	���������ڴ�
// 	g_pDIT = new CDataImageTable(0xFFFF, (char *)g_strDrvName.c_str());
// 	if (!g_pDIT->InitDone())// ����֮ǰ��ȷ��DIT�Ѿ����������
// 	{
// 		// ��¼��־�������ڴ��ʼ��ʧ��
//         CV_ERROR(g_CVDrvierCommonLog, -1,  _("Initialize Shared memory failed!"));//�����ڴ��ʼ��ʧ��
// 		// ɾ�������ڴ�
// 		SAFE_DELETE(g_pDIT);
// 		return -1;
// 	}
// 	g_pDIT->ResetCtlBlock(0xFFFF);
//     CV_INFO(g_CVDrvierCommonLog, _("Initialize Shared memory successfully!"));//�����ڴ��ʼ���ɹ�
// 
// 	// ���DIT�е�������Ϣ
// 	ClearDIT(g_pDIT);

	// ��ʼ����������ͬ���ӿ�	
	//= IOSync_Init(g_strDrvName.c_str(), IOSyncCallBack, this);
	//if (nStatus != DRV_SUCCESS)
	//{
        //CV_ERROR(g_CVDrvierCommonLog, nStatus, _("Initialize the data synchronization interfaces of driver failed!"));//��ʼ����������ͬ���ӿ�ʧ��
	//	return -1;
	//}
    //CV_INFO(g_CVDrvierCommonLog, _("Initialize the data synchronization interfaces of driver successfully!"));//��ʼ����������ͬ���ӿڳɹ�!

	// ��ʼ��RMAPI�����ڻ�ȡSCADA������״̬
	int nStatus = 0;
	int32 nRet = ICV_SUCCESS;
	nStatus = RMAPI_Init();
	if (nStatus != DRV_SUCCESS)
	{
        CV_ERROR(g_CVDrvierCommonLog, nStatus, _("Initialize RMAPI failed!"));//��ʼ��RMAPIʧ��
	}
	else
	{
        CV_INFO(g_CVDrvierCommonLog, _("Initialize RMAPI successfully!"));//��ʼ��RMAPI�ɹ�
	}

	// CDataBlock *pDataBlock = new CDataBlock(NULL);
	nStatus = LoadConfig(m_driverInfo);
	if (nStatus != DRV_SUCCESS)
	{
		//��¼��־����ȡ�����ļ�ʧ��
		//��ȡ�����ļ�����,�����ļ�������,ȱ������,��ĳЩ����Ϊ��ֵ!
        CV_ERROR(g_CVDrvierCommonLog, nStatus, _("Read the configuration file error, maybe the file does not exist, or missing property, or some attribute is null!"));
		return -1;
	}
	//��ȡ�����ļ��ɹ�!
    CV_INFO(g_CVDrvierCommonLog, _("Load the config successfully!"));
	
	m_pOutputQuene = new CSimpleThreadQueue<OUTPUTCMD>;
	//��ʼ��ʼ��driverapi
	nRet = CVDrv_Init(g_strDrvName, g_bMultiLink);
	if(nRet != ICV_SUCCESS)
	{
		CV_CRITICAL(g_CVDrvierCommonLog, nRet, _("CVDrv_Init failed : %d"), nRet);
		return nRet;
	}
	CV_INFO(g_CVDrvierCommonLog, _("CVDrv_Init successfully!"));//��ȡ�����ļ��ɹ�!
	CVDrv_SetControlCallback(ControlCallback);
	if(g_pfnInit)
	{
		if((nStatus = g_pfnInit((void *)&m_driverInfo)) != DRV_SUCCESS)
		{
			CV_CRITICAL(g_CVDrvierCommonLog, nStatus, _("����������ʼ��ʧ��, ����: %d"), nStatus);
			return nStatus;
		}
	}

	//��ʼ��smartOP
	// nRet = OP_API_Init();
	// CV_INFO(g_CVDrvierCommonLog, "OP_API_Init nRet : %d.", nRet);
	// nRet = OP_API_Client_Init(tag_trace_callback);
	// g_bNeedTrace = false;
	// CV_INFO(g_CVDrvierCommonLog, "OP_API_Client_Init nRet : %d.", nRet);

	// 	nStatus = AddTaskGrps2DIT(m_driverInfo.m_mapTaskGroups);
	// 	if (nStatus != DRV_SUCCESS)
	// 	{
	// 		CV_ERROR(g_CVDrvierCommonLog, nStatus, _("Failed to add device group info to driver DIT!"));//�����豸�鵽DIT��ʧ��
	// 		return -1;
	// 	}

	if(m_pTaskHeartBeat)
		m_pTaskHeartBeat->Start();

	if(m_pTaskDrvDevStatus)
		m_pTaskDrvDevStatus->Start();

	if(m_pTaskBatchUpdateData)
		m_pTaskBatchUpdateData->Start();

	if(m_pTaskBatchUpdateData2)
		m_pTaskBatchUpdateData2->Start();

	if(m_pTaskGroupGuard)
		m_pTaskGroupGuard->Start();

	if (m_pTaskConfigErrTag)
		m_pTaskConfigErrTag->Start();

	return DRV_SUCCESS;
}

// ������ֹͣʱ
void CMainTask::OnStop()
{
	int32 nRet = ICV_SUCCESS;
	if(m_pTaskHeartBeat)
		m_pTaskHeartBeat->Stop();
	if(m_pTaskDrvDevStatus)
		m_pTaskDrvDevStatus->Stop();
	if(m_pTaskBatchUpdateData)
		m_pTaskBatchUpdateData->Stop();
	if(m_pTaskBatchUpdateData2)
		m_pTaskBatchUpdateData2->Stop();
	if(m_pTaskGroupGuard)
		m_pTaskGroupGuard->Stop();
// 	if (m_pTaskMainGuard)
// 		m_pTaskMainGuard->Stop();
	if (m_pTaskConfigErrTag)
		m_pTaskConfigErrTag->Stop();

	//����ɾ���豸�����ݿ�Ļص�, ֻ������˳�ʱ���ã������ط��Ͳ��ٵ���
	for(std::map<string, CTaskGroup*>::iterator itTaskGroup = m_driverInfo.m_mapTaskGroups.begin();itTaskGroup != m_driverInfo.m_mapTaskGroups.end(); ++itTaskGroup)
	{
		CTaskGroup *pTaskGrp = itTaskGroup->second;
		for(std::map<string, CDevice*>::iterator itDevice = pTaskGrp->m_mapDevices.begin();itDevice != pTaskGrp->m_mapDevices.end(); ++itDevice)
		{	
			CDevice *pDevice = itDevice->second;
			for (std::map<std::string, CDataBlock*>::iterator itDataBlock = pDevice->m_mapDataBlocks.begin(); 
				itDataBlock != pDevice->m_mapDataBlocks.end(); ++ itDataBlock)
			{
				if(g_pfnOnDataBlockDelete)
				{
					g_pfnOnDataBlockDelete(pDevice, itDataBlock->second);
				}
			}

			if(g_pfnOnDeviceDelete)
				g_pfnOnDeviceDelete(pDevice);
		}
	}

	//�ͷ�smartOP
	// nRet = OP_API_Client_Release();
	// CV_INFO(g_CVDrvierCommonLog, "OP_API_Client_Release nRet : %d.", nRet);
	// nRet = OP_API_Release();
	// CV_INFO(g_CVDrvierCommonLog, "OP_API_Release nRet : %d.", nRet);

	m_pOutputQuene->clear();
	SAFE_DELETE(m_pOutputQuene);
	
	nRet = CVDrv_Release(g_strDrvName);
	if(nRet != ICV_SUCCESS)
	{
		CV_CRITICAL(g_CVDrvierCommonLog, nRet, _("CVDrv_Release failed : %d"), nRet);
		return;
	}
}

/*
<?xml version="1.0" encoding="GB2312" ?>
<Driver name="SJBroadcastDriver" param1="" param2="" param3="">
<Device name="SJBroadCastDev1" taskid="1" conntype="tcpclient" connectParam="ip=127.0.0.1/***********;port=102;multiLink=1;" cyclerate="1000" param1="" param2="" param3="" description="">
<DataItem devAddress="����_1" param1="" param2="" param3="" description="" />
<DataItem devAddress="����_2" param1="" param2="" param3="" description="" />
</Device>
</Driver>
*/
long CMainTask::LoadConfig(CDriver &driver)
{
	long nStatus = DRV_SUCCESS;
	// �γ������ļ�����·��
	char szConfigFile[ICV_SHORTFILENAME_MAXLEN];
	char *szCVProjCfgPath = (char *)CVComm.GetCVProjCfgPath(); 
	if(szCVProjCfgPath)
		sprintf(szConfigFile, "%s%cdrivers%c%s.xml",szCVProjCfgPath, ACE_DIRECTORY_SEPARATOR_CHAR, ACE_DIRECTORY_SEPARATOR_CHAR, g_strDrvName.c_str());
	else
		sprintf(szConfigFile, "%s.xml", g_strDrvName.c_str());

	// ���������ļ�xml��ʽ
	TiXmlElement* pElmChild = NULL;
	TiXmlDocument doc(szConfigFile);
	if( !doc.LoadFile() ) 
	{
        //���������ļ� %s ʧ��, ������=%d!
		CV_CRITICAL(g_CVDrvierCommonLog, EC_ICV_DA_CONFIG_FILE_INVALID, _("Load config file(%s) failed, error code:%d."), szConfigFile, EC_ICV_DA_CONFIG_FILE_INVALID);
		return EC_ICV_DA_CONFIG_FILE_INVALID;
	}
	CV_INFO(g_CVDrvierCommonLog, _("Load the config successfully! File: %s."), szConfigFile);//���������ļ� %s �ɹ�

	// 2.Get Root Element.
	TiXmlElement* pElmRoot = doc.RootElement(); 
	CHECK_NULL_RETURN_ERR(pElmRoot);
	driver.LoadParamsFromXml(pElmRoot);
	TiXmlElement* pNodeDevice = pElmRoot->FirstChildElement(XML_ELEMENTNODE_DEVICE);

	//Ϊ�˼�������Ƿ��ظ�
	while(pNodeDevice != NULL)
	{
		string strDeviceName = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_NAME));
		string strDeviceTaskID =  NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_TASK));
		
		//���snap7���ƣ������ָ��������
		string strConnParam = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_CONNPARAM));
		string strConnNum ="";
		int nPos= strConnParam.find("connum=");
		if(nPos != string::npos)
		{
			strConnNum = strConnParam.substr(nPos + 7,strConnParam.length() - (nPos + 7));
			int nPos1 = strConnNum.find(';');
			if(nPos1 != string::npos)
				strConnNum = strConnNum.substr(0,nPos1);
		}
		string strTaskGrpName = TaskGroup_NAME_PREFIX;
		strTaskGrpName = strTaskGrpName + strDeviceTaskID ;

		TiXmlElement *pNodeDataBlock = pNodeDevice->FirstChildElement(XML_ELEMENTNODE_DATABLOCK);
		if (NULL != pNodeDataBlock)
		{
			string strTaskIDBlock =  NULLASSTRING(pNodeDataBlock->Attribute(XML_ELEMENTNODE_TASK));
			strTaskGrpName = strTaskGrpName  + TaskGroup_NAME_PREFIX + strTaskIDBlock;
		}

		//���snap7���������ӵĴ���������
		//long LoadDeviceInfo(CDriver *pDriver, CTaskGroup* pTaskGrp, TiXmlElement* pNodeDevice, unsigned int nCurrentConnectionNum, unsigned int nTotalConnectionNum);
		//����ʵ��ÿ�����Ӵ�����ͬ��tag��
		if(!strConnNum.empty())
		{
			unsigned int nTotalConNum = 0;
			unsigned int nCurrConnIndex = 0;

			nTotalConNum = ::atoi(strConnNum.c_str());
			if(nTotalConNum <=0)
				nTotalConNum = 1;
			else if(nTotalConNum >=10) //������������
				nTotalConNum = 10;
			CV_INFO(g_CVDrvierCommonLog,"Device[%s] connum=%d",strDeviceName.c_str(),nTotalConNum);
			for(nCurrConnIndex = 0; nCurrConnIndex <nTotalConNum; ++nCurrConnIndex)
			{	
				stringstream ss;
				ss << nCurrConnIndex;
				strTaskGrpName = TaskGroup_NAME_PREFIX + strDeviceTaskID ;
				strTaskGrpName = strTaskGrpName  + TaskGroup_NAME_PREFIX + ss.str();

				CTaskGroup *pTaskGroup = AssureGetTaskGroup(&driver, strTaskGrpName);
				LoadDeviceInfo(&driver, pTaskGroup, pNodeDevice, nCurrConnIndex, nTotalConNum);				
			}
			pNodeDevice = pNodeDevice->NextSiblingElement(XML_ELEMENTNODE_DEVICE);
		}
		else
		{
			strTaskGrpName = strTaskGrpName  + TaskGroup_NAME_PREFIX + "0";
			CTaskGroup *pTaskGroup = AssureGetTaskGroup(&driver, strTaskGrpName);
			LoadDeviceInfo(&driver, pTaskGroup, pNodeDevice);
			pNodeDevice = pNodeDevice->NextSiblingElement(XML_ELEMENTNODE_DEVICE);
		}
	}

	if(driver.m_mapTaskGroups.empty())
	{
		CV_WARN(g_CVDrvierCommonLog, -1, _("No TaskGroup configured"));
	}

	// ɾ��û���豸���ݿ���豸�������飨��Ϊ�豸��ȱʡ�Ὠ��һ��������TaskGroup�����ÿ�����ݿ鶼����������ź��豸������������ˣ�
	std::map<string, CTaskGroup*>::iterator itTaskGroup = driver.m_mapTaskGroups.begin();
	for(;itTaskGroup != driver.m_mapTaskGroups.end(); )
	{
		CTaskGroup *pTaskGrp = itTaskGroup->second;
		std::map<string, CDevice*>::iterator itDevice = pTaskGrp->m_mapDevices.begin();
		for(;itDevice != pTaskGrp->m_mapDevices.end(); )
		{
			CDevice *pDevice = itDevice->second;
			// ����豸��û���κ����ݿ飬��ɾ���豸
			if(pDevice->m_mapDataBlocks.size() <= 0)
			{
				CV_INFO(g_CVDrvierCommonLog,  _("(LoadConfig)device : %s has no datablocks"), pDevice->m_strName.c_str());
				SAFE_DELETE(pDevice);

				pTaskGrp->m_mapDevices.erase(itDevice++);
			}
			else
				itDevice ++;
		}

		// �����������û���κ��豸��ɾ���豸��
		if(pTaskGrp->m_mapDevices.size() <= 0)
		{
            CV_INFO(g_CVDrvierCommonLog,  _("(LoadConfig)delete TaskGroup name: %s,device num:%d"), pTaskGrp->m_drvObjBase.m_strName.c_str(), pTaskGrp->m_mapDevices.size());
			SAFE_DELETE(pTaskGrp);			
			driver.m_mapTaskGroups.erase(itTaskGroup ++);
		}
		else
			itTaskGroup++;
	}

	//��־��¼����Ŀ��
	for(std::map<string, CTaskGroup*>::iterator itTaskGroup = driver.m_mapTaskGroups.begin();itTaskGroup != driver.m_mapTaskGroups.end(); ++itTaskGroup)
	{
		CTaskGroup *pTaskGrp = itTaskGroup->second;
		for(std::map<string, CDevice*>::iterator itDevice = pTaskGrp->m_mapDevices.begin();itDevice != pTaskGrp->m_mapDevices.end(); ++itDevice)
		{			
			for (std::map<std::string, CDataBlock*>::iterator itDataBlock = itDevice->second->m_mapDataBlocks.begin(); 
				itDataBlock != itDevice->second->m_mapDataBlocks.end(); ++ itDataBlock)
			{
				CV_INFO(g_CVDrvierCommonLog, "DataBlock %s start addr %s element num %d elementbits %d taskid %s tagnum %d", itDataBlock->first.c_str(),
					itDataBlock->second->m_strAddress.c_str(), itDataBlock->second->m_nElemNum, itDataBlock->second->m_nElemBits, itDataBlock->second->m_strTaskID.c_str(), itDataBlock->second->m_vecTagAddrID.size());
				
				for (long i = 0; i < itDataBlock->second->m_vecTagAddrID.size(); i ++)
				{
					CV_INFO(g_CVDrvierCommonLog, "DataBlock %s  tag id %d", itDataBlock->first.c_str(), itDataBlock->second->m_vecTagAddrID[i]->nTagID);
				}
				
			}
		}
	}
	return DRV_SUCCESS;
}

// ����ĳ���豸��Ϣ��pDevice
long CMainTask::LoadDeviceInfo(CDriver *pDriver, CTaskGroup* pTaskGrp, TiXmlElement* pNodeDevice)
{
	string strDeviceName = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_NAME));
	// ����Ƿ��ظ�,����ظ���ȡ����ǰ���Ǹ�
	CDevice *pDevice = AssureGetDevice(pDriver, pTaskGrp, strDeviceName, NULL);
	if(!pDevice)
	{
        //��������ʱ: �����豸��(%s) ���豸�� %s��δ�ҵ��������豸!
		CV_WARN(g_CVDrvierCommonLog, -1, _(" When Loading config, according to the device name(%s), it did not find or generate one device in the device group %s."), 
			(char *)strDeviceName.c_str(), pTaskGrp->m_drvObjBase.m_strName.c_str());
		return -1;
	} 

	pDevice->LoadParamsFromXml(pNodeDevice);	// ��xml�ж�ȡȱʡ�����ļ�
	pDevice->m_strConnType = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_CONNTYPE));
	pDevice->m_strConnParam = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_CONNPARAM));


	char *szConnType = (char *)pDevice->m_strConnType.c_str();
	if(ACE_OS::strcasecmp(szConnType, "tcpclient") == 0)
	{
		pDevice->GetMultiLink((char *)pDevice->m_strConnParam.c_str());
	}
	else if(ACE_OS::strcasecmp(szConnType, "serial") == 0)
	{
		pDevice->m_bMultiLink = false;
	}
	else if (ACE_OS::strcasecmp(szConnType, "udpserver") == 0)
	{
		pDevice->GetMultiLink((char *)pDevice->m_strConnParam.c_str());
	}
	else if(ACE_OS::strcasecmp(szConnType, "tcpserver") == 0) // ip=127.0.0.1;ip=127.0.0.1/***********;port=102;
	{
		pDevice->m_bMultiLink = false; //TCPServerģʽ ��ʱĬ��Ϊ�����ӷ�ʽ
	}
	else
	{
		pDevice->GetMultiLinkWithDefaultValue((char *)pDevice->m_strConnParam.c_str());
	}
	// ֻҪ��һ���豸�ǵ����ӣ���ô��������������豸���ǵ������豸
	if(false == pDevice->m_bMultiLink)
		g_bMultiLink = pDevice->m_bMultiLink;
	
	const char *szCycleRate = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_CYCLERATE));
	if(szCycleRate)
		pDevice->m_nPollRate = ::atoi(szCycleRate);
	const char *szRecvTimeout = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_RECVTIMEOUT));
	if(szRecvTimeout)
		pDevice->m_nRecvTimeOut = ::atoi(szRecvTimeout);
	pDevice->m_strTaskID = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_TASK));

	if(g_pfnOnDeviceAdd)
		g_pfnOnDeviceAdd(pDevice);

	LoadDataBlockOfDevice(pDriver, pDevice, pNodeDevice);
	pTaskGrp->m_mapDevices[pDevice->m_strName] = pDevice;
	return DRV_SUCCESS;
}

// ����ĳ���豸��Ϣ��pDevice
long CMainTask::LoadDeviceInfo(CDriver *pDriver, CTaskGroup* pTaskGrp, TiXmlElement* pNodeDevice, unsigned int nCurrConnIndex, unsigned int nTotalConNum)
{
	string strDeviceName = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_NAME));
	// ����Ƿ��ظ�,����ظ���ȡ����ǰ���Ǹ�
	CDevice *pDevice = AssureGetDevice(pDriver, pTaskGrp, strDeviceName, NULL);
	if(!pDevice)
	{
		//��������ʱ: �����豸��(%s) ���豸�� %s��δ�ҵ��������豸!
		CV_WARN(g_CVDrvierCommonLog, -1, _(" When Loading config, according to the device name(%s), it did not find or generate one device in the device group %s."), 
			(char *)strDeviceName.c_str(), pTaskGrp->m_drvObjBase.m_strName.c_str());
		return -1;
	} 

	pDevice->LoadParamsFromXml(pNodeDevice);	// ��xml�ж�ȡȱʡ�����ļ�
	pDevice->m_strConnType = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_CONNTYPE));
	pDevice->m_strConnParam = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_CONNPARAM));


	char *szConnType = (char *)pDevice->m_strConnType.c_str();
	if(ACE_OS::strcasecmp(szConnType, "tcpclient") == 0)
	{
		pDevice->GetMultiLink((char *)pDevice->m_strConnParam.c_str());
	}
	else if(ACE_OS::strcasecmp(szConnType, "serial") == 0)
	{
		pDevice->m_bMultiLink = false;
	}
	else if (ACE_OS::strcasecmp(szConnType, "udpserver") == 0)
	{
		pDevice->GetMultiLink((char *)pDevice->m_strConnParam.c_str());
	}
	else if(ACE_OS::strcasecmp(szConnType, "tcpserver") == 0) // ip=127.0.0.1;ip=127.0.0.1/***********;port=102;
	{
		pDevice->m_bMultiLink = false; //TCPServerģʽ ��ʱĬ��Ϊ�����ӷ�ʽ
	}
	else
	{
		pDevice->GetMultiLinkWithDefaultValue((char *)pDevice->m_strConnParam.c_str());
	}
	// ֻҪ��һ���豸�ǵ����ӣ���ô��������������豸���ǵ������豸
	if(false == pDevice->m_bMultiLink)
		g_bMultiLink = pDevice->m_bMultiLink;

	const char *szCycleRate = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_CYCLERATE));
	if(szCycleRate)
		pDevice->m_nPollRate = ::atoi(szCycleRate);
	const char *szRecvTimeout = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_RECVTIMEOUT));
	if(szRecvTimeout)
		pDevice->m_nRecvTimeOut = ::atoi(szRecvTimeout);
	pDevice->m_strTaskID = NULLASSTRING(pNodeDevice->Attribute(XML_ELEMENTNODE_TASK));

	if(g_pfnOnDeviceAdd)
		g_pfnOnDeviceAdd(pDevice);

	LoadDataBlockOfDevice(pDriver, pDevice, pNodeDevice, nCurrConnIndex, nTotalConNum);
	pTaskGrp->m_mapDevices[pDevice->m_strName] = pDevice;
	return DRV_SUCCESS;
}

/**
 *  ��ȡ���ݿ�����.
 *
 *  @param  -[in,out]  CDevice*  pDevice: [comment]
 *  @param  -[in]  TiXmlElement*  node: [comment]
 *
 *  @version     07/14/2008  lijingjing  Initial Version.
 *  @version	3/17/2013  baoyuansong  ����豸û���������ݿ飬��ֱ�Ӵ����ݿ��ж�ȡ�㣬Ȼ������û��ӿڣ��������ݿ�.
 */
long CMainTask::LoadDataBlockOfDevice(CDriver *pDriver, CDevice* pDevice, TiXmlElement* pNodeDevice)
{	
	//ʹ�õ�ֱ�����ɿ�
	if(!pNodeDevice)
		return -1;
	DataBlockList listDataBlock;
	TagInfoVector vecTagInfo;
	CTagDataBlockBuilder objDataBlockBuild(g_strDrvName.c_str(), pDevice);
	objDataBlockBuild.ReadTags(vecTagInfo);
	for (int i = 0; i < vecTagInfo.size(); ++i) 
	{
		ConfigErrTags untag;
		//memcpy(&untag,&vecTagInfo[i],sizeof(TagInfo));
		untag.taginfo = vecTagInfo[i];
		untag.bError = true;
		g_mapConfigErrTags.insert(std::make_pair(vecTagInfo[i].nTagID, untag));
	}

	// ��ȡ���ݿ�����,����豸��û�������κ����ݿ飬��ֱ�Ӵ����ݿ��ж�ȡ��ص����Ϣ��Ȼ���Զ����
	TiXmlElement *pNodeDataBlock = pNodeDevice->FirstChildElement(XML_ELEMENTNODE_DATABLOCK);
	if (NULL == pNodeDataBlock)
	{
		objDataBlockBuild.MakeTagDataBlocks(vecTagInfo);
		return DRV_SUCCESS;
	}
	CV_INFO(g_CVDrvierCommonLog, "start load datablock");
	while (pNodeDataBlock)
	{
		string strDataBlockName = NULLASSTRING(pNodeDataBlock->Attribute(XML_ELEMENTNODE_NAME));
		string strDataBlockAddress = NULLASSTRING(pNodeDataBlock->Attribute(XML_ELEMENTNODE_ADDRESS));
		string strTaskID =  NULLASSTRING(pNodeDataBlock->Attribute(XML_ELEMENTNODE_TASK));
		// �������Ϊ�գ���ȡ��ַΪ����.���ڷǱ���豸������=��ַ��
		if(strDataBlockName.empty())
		{
			strDataBlockName = strDataBlockAddress;
		}

		if(strDataBlockName.empty() && strDataBlockAddress.empty())
		{
            //���ݿ����ƺ͵�ַ���ܶ�Ϊ�գ�
			CV_ERROR(g_CVDrvierCommonLog, -1,  _("Name and address of the datablock can not both be empty!"));
			pNodeDataBlock = pNodeDataBlock->NextSiblingElement(XML_ELEMENTNODE_DATABLOCK);
			continue;
		}
		
		string strTaskGrpName = TaskGroup_NAME_PREFIX;

		strTaskGrpName = strTaskGrpName + pDevice->m_strTaskID + strTaskGrpName + strTaskID;

		string strDeviceName = pDevice->m_strName;

		// ȷ����TaskGroup�ں�TaskID��ͬ���豸���У����û���豸�飬�������豸�顣
		CV_INFO(g_CVDrvierCommonLog, "AssureGetTaskGroup");
		CTaskGroup *pTaskGroup = AssureGetTaskGroup(pDriver, strTaskGrpName);
		if(!pTaskGroup)
		{
            //���ݿ�������� %s���豸���� %s��δ�ҵ������ɺ��ʵ��豸��
			CV_ERROR(g_CVDrvierCommonLog, -1, _("[Datablock]According to the task group(%s) and device name (%s), it did not find or generate the appropriate device group."), 
				strTaskGrpName.c_str(), strDeviceName.c_str());
			pNodeDataBlock = pNodeDataBlock->NextSiblingElement(XML_ELEMENTNODE_DATABLOCK);
			continue;
        }
		CV_INFO(g_CVDrvierCommonLog, "AssureGetDevice");
		// ��ȷ���豸�����и��豸�����û�������ɸ��豸��������򿽱�pDevice��Ϣ�������ɵ��豸
		pDevice = AssureGetDevice(pDriver, pTaskGroup, pDevice->m_strName, pDevice);
		if(!pDevice)
		{
            //���ݿ�������� %s���豸���� %s��δ�ҵ������ɺ��ʵ��豸
			CV_ERROR(g_CVDrvierCommonLog, -1, _("[Datablock]According to the task group(%s) and device name (), it did not find or generate the appropriate device."), 
				strTaskGrpName.c_str());
			pNodeDataBlock = pNodeDataBlock->NextSiblingElement(XML_ELEMENTNODE_DATABLOCK);
			continue;
		}

		// �������Ϊ�գ���ȡ��ַΪ����
		if(strDataBlockName.empty())
			strDataBlockName = strDataBlockAddress;

		// ����Ƿ����ظ�
		if(pDevice->m_mapDataBlocks.find(strDataBlockName)!= pDevice->m_mapDataBlocks.end())
		{
			CV_WARN(g_CVDrvierCommonLog, -1, _("Load Config:Device(%s) DataBlock(%s) Repeated"), pDevice->m_strName.c_str(), strDataBlockName.c_str());
			pNodeDataBlock = pNodeDataBlock->NextSiblingElement("DataItem");
			continue;
		}

		CDataBlock *pDataBlock = new CDataBlock(pDevice);
		// ���ݿ�����
		pDataBlock->LoadParamsFromXml(pNodeDataBlock);	// ��xml�ж�ȡȱʡ�����ļ�
		pDataBlock->m_strName = strDataBlockName;
		pDataBlock->m_strAddress = strDataBlockAddress;
		const char *szBlkLength = pNodeDataBlock->Attribute(XML_ELEMENTNODE_ELEMCOUNT);
		pDataBlock->m_nElemNum = (szBlkLength == NULL?0:atoi(szBlkLength));
		//Ԫ�ظ���Ϊ0�Ƿ����������Ӹÿ�
		if (pDataBlock->m_nElemNum <= 0)
		{
            //�豸%s�����ݿ�%sԪ�ظ���Ϊ%d�Ƿ�,�����ÿ�!
			CV_WARN(g_CVDrvierCommonLog, -1, _("Under the device %s, the number of elements of datablock %s is illegal(%d), Skip this block!"),  \
				pDevice->m_strName.c_str(), strDataBlockName.c_str(), pDataBlock->m_nElemNum);
			pNodeDataBlock = pNodeDataBlock->NextSiblingElement(XML_ELEMENTNODE_DATABLOCK);
			SAFE_DELETE(pDataBlock);
			continue;
		}
		const char *szElemSize = pNodeDataBlock->Attribute(XML_ELEMENTNODE_ELEMBYTES);
		if (szElemSize != NULL)
		{			
			int nBytes = atoi(szElemSize);

			// ������0.1����ʾһλ
			double dbBits = ::atof(szElemSize);
			int nBitsAdded = dbBits * 10 - nBytes * 10;
			pDataBlock->m_nElemBits = nBytes * BITS_PER_BYTE + nBitsAdded;
		}
		else
		{
			szElemSize = pNodeDataBlock->Attribute(XML_ELEMENTNODE_ELEMBITS);
			if (szElemSize != NULL)
				pDataBlock->m_nElemBits = atoi(szElemSize);
		}

		if(pDataBlock->m_nElemBits <= 0)
			pDataBlock->m_nElemBits = BITS_PER_BYTE;	//ÿ��Ԫ��ȱʡ����£���СΪ1���ֽڣ���ʹ��һ��DIҲռ��һ���ֽ�

		const char *szBlkType = pNodeDataBlock->Attribute(XML_ELEMENTNODE_TYPE);
		pDataBlock->m_strBlockType = (szBlkType == NULL ? "":szBlkType);
		const char *szCycleRate = pNodeDataBlock->Attribute(XML_ELEMENTNODE_CYCLERATE);
		pDataBlock->m_nPollRate = (szCycleRate == NULL?0:atoi(szCycleRate));
		const char *szPhase = pNodeDataBlock->Attribute(XML_ELEMENTNODE_PHASE);
		pDataBlock->m_nPollPhase = (szPhase == NULL?0:atoi(szPhase));

 		if ((pDataBlock->m_nElemNum * pDataBlock->m_nElemBits)%BITS_PER_BYTE == 0)
 			pDataBlock->m_nBlockSize = (pDataBlock->m_nElemNum * pDataBlock->m_nElemBits)/BITS_PER_BYTE;	// ȱʡΪ����
 		else
 			pDataBlock->m_nBlockSize = (pDataBlock->m_nElemNum * pDataBlock->m_nElemBits)/BITS_PER_BYTE + 1;	// ȱʡΪ����

		pDataBlock->CreateBlockBuffer();
		pDevice->m_mapDataBlocks[pDataBlock->m_strName] = pDataBlock;

		CV_INFO(g_CVDrvierCommonLog, "datablock name : %s, type: %s, addr:%s, elembits : %d, elemcount %d", 
			pDataBlock->m_strName.c_str(), pDataBlock->m_strBlockType.c_str(), pDataBlock->m_strAddress.c_str(), pDataBlock->m_nElemBits, pDataBlock->m_nElemNum);

		if(g_pfnOnDataBlockAdd)
			g_pfnOnDataBlockAdd(pDevice, pDataBlock);
		
		objDataBlockBuild.ParseTagAndInsertBlock(vecTagInfo, pDataBlock);
		pNodeDataBlock = pNodeDataBlock->NextSiblingElement(XML_ELEMENTNODE_DATABLOCK);
	}

	return DRV_SUCCESS;
}

long CMainTask::LoadDataBlockOfDevice(CDriver *pDriver, CDevice* pDevice, TiXmlElement* pNodeDevice, unsigned int nCurrConnIndex, unsigned int nTotalConNum)
{	
	//ʹ�õ�ֱ�����ɿ�
	if(!pNodeDevice)
		return -1;
	DataBlockList listDataBlock;
	TagInfoVector vecTagInfo;
	CTagDataBlockBuilder objDataBlockBuild(g_strDrvName.c_str(), pDevice);
	objDataBlockBuild.ReadTags(vecTagInfo);
	for (int i = 0; i < vecTagInfo.size(); ++i)
	{
		ConfigErrTags untag;
		untag.taginfo = vecTagInfo[i];
		untag.bError = true;
		g_mapConfigErrTags.insert(std::make_pair(vecTagInfo[i].nTagID, untag));
	}

	// ��ȡ���ݿ�����,����豸��û�������κ����ݿ飬��ֱ�Ӵ����ݿ��ж�ȡ��ص����Ϣ��Ȼ���Զ����
	TiXmlElement *pNodeDataBlock = pNodeDevice->FirstChildElement(XML_ELEMENTNODE_DATABLOCK);
	if (NULL == pNodeDataBlock)
	{
		objDataBlockBuild.MakeTagDataBlocks(vecTagInfo, pDevice, nCurrConnIndex, nTotalConNum);
		return DRV_SUCCESS;
	}
	CV_INFO(g_CVDrvierCommonLog, "start load datablock");
	while (pNodeDataBlock)
	{
		string strDataBlockName = NULLASSTRING(pNodeDataBlock->Attribute(XML_ELEMENTNODE_NAME));
		string strDataBlockAddress = NULLASSTRING(pNodeDataBlock->Attribute(XML_ELEMENTNODE_ADDRESS));
		string strTaskID =  NULLASSTRING(pNodeDataBlock->Attribute(XML_ELEMENTNODE_TASK));
		// �������Ϊ�գ���ȡ��ַΪ����.���ڷǱ���豸������=��ַ��
		if(strDataBlockName.empty())
		{
			strDataBlockName = strDataBlockAddress;
		}

		if(strDataBlockName.empty() && strDataBlockAddress.empty())
		{
			//���ݿ����ƺ͵�ַ���ܶ�Ϊ�գ�
			CV_ERROR(g_CVDrvierCommonLog, -1,  _("Name and address of the datablock can not both be empty!"));
			pNodeDataBlock = pNodeDataBlock->NextSiblingElement(XML_ELEMENTNODE_DATABLOCK);
			continue;
		}

		string strTaskGrpName = TaskGroup_NAME_PREFIX;

		strTaskGrpName = strTaskGrpName + pDevice->m_strTaskID + strTaskGrpName + strTaskID;

		string strDeviceName = pDevice->m_strName;

		// ȷ����TaskGroup�ں�TaskID��ͬ���豸���У����û���豸�飬�������豸�顣
		CV_INFO(g_CVDrvierCommonLog, "AssureGetTaskGroup");
		CTaskGroup *pTaskGroup = AssureGetTaskGroup(pDriver, strTaskGrpName);
		if(!pTaskGroup)
		{
			//���ݿ�������� %s���豸���� %s��δ�ҵ������ɺ��ʵ��豸��
			CV_ERROR(g_CVDrvierCommonLog, -1, _("[Datablock]According to the task group(%s) and device name (%s), it did not find or generate the appropriate device group."), 
				strTaskGrpName.c_str(), strDeviceName.c_str());
			pNodeDataBlock = pNodeDataBlock->NextSiblingElement(XML_ELEMENTNODE_DATABLOCK);
			continue;
		}
		CV_INFO(g_CVDrvierCommonLog, "AssureGetDevice");
		// ��ȷ���豸�����и��豸�����û�������ɸ��豸��������򿽱�pDevice��Ϣ�������ɵ��豸
		pDevice = AssureGetDevice(pDriver, pTaskGroup, pDevice->m_strName, pDevice);
		if(!pDevice)
		{
			//���ݿ�������� %s���豸���� %s��δ�ҵ������ɺ��ʵ��豸
			CV_ERROR(g_CVDrvierCommonLog, -1, _("[Datablock]According to the task group(%s) and device name (), it did not find or generate the appropriate device."), 
				strTaskGrpName.c_str());
			pNodeDataBlock = pNodeDataBlock->NextSiblingElement(XML_ELEMENTNODE_DATABLOCK);
			continue;
		}

		// �������Ϊ�գ���ȡ��ַΪ����
		if(strDataBlockName.empty())
			strDataBlockName = strDataBlockAddress;

		// ����Ƿ����ظ�
		if(pDevice->m_mapDataBlocks.find(strDataBlockName)!= pDevice->m_mapDataBlocks.end())
		{
			CV_WARN(g_CVDrvierCommonLog, -1, _("Load Config:Device(%s) DataBlock(%s) Repeated"), pDevice->m_strName.c_str(), strDataBlockName.c_str());
			pNodeDataBlock = pNodeDataBlock->NextSiblingElement("DataItem");
			continue;
		}

		CDataBlock *pDataBlock = new CDataBlock(pDevice);
		// ���ݿ�����
		pDataBlock->LoadParamsFromXml(pNodeDataBlock);	// ��xml�ж�ȡȱʡ�����ļ�
		pDataBlock->m_strName = strDataBlockName;
		pDataBlock->m_strAddress = strDataBlockAddress;
		const char *szBlkLength = pNodeDataBlock->Attribute(XML_ELEMENTNODE_ELEMCOUNT);
		pDataBlock->m_nElemNum = (szBlkLength == NULL?0:atoi(szBlkLength));
		//Ԫ�ظ���Ϊ0�Ƿ����������Ӹÿ�
		if (pDataBlock->m_nElemNum <= 0)
		{
			//�豸%s�����ݿ�%sԪ�ظ���Ϊ%d�Ƿ�,�����ÿ�!
			CV_WARN(g_CVDrvierCommonLog, -1, _("Under the device %s, the number of elements of datablock %s is illegal(%d), Skip this block!"),  \
				pDevice->m_strName.c_str(), strDataBlockName.c_str(), pDataBlock->m_nElemNum);
			pNodeDataBlock = pNodeDataBlock->NextSiblingElement(XML_ELEMENTNODE_DATABLOCK);
			SAFE_DELETE(pDataBlock);
			continue;
		}
		const char *szElemSize = pNodeDataBlock->Attribute(XML_ELEMENTNODE_ELEMBYTES);
		if (szElemSize != NULL)
		{			
			int nBytes = atoi(szElemSize);

			// ������0.1����ʾһλ
			double dbBits = ::atof(szElemSize);
			int nBitsAdded = dbBits * 10 - nBytes * 10;
			pDataBlock->m_nElemBits = nBytes * BITS_PER_BYTE + nBitsAdded;
		}
		else
		{
			szElemSize = pNodeDataBlock->Attribute(XML_ELEMENTNODE_ELEMBITS);
			if (szElemSize != NULL)
				pDataBlock->m_nElemBits = atoi(szElemSize);
		}

		if(pDataBlock->m_nElemBits <= 0)
			pDataBlock->m_nElemBits = BITS_PER_BYTE;	//ÿ��Ԫ��ȱʡ����£���СΪ1���ֽڣ���ʹ��һ��DIҲռ��һ���ֽ�

		const char *szBlkType = pNodeDataBlock->Attribute(XML_ELEMENTNODE_TYPE);
		pDataBlock->m_strBlockType = (szBlkType == NULL ? "":szBlkType);
		const char *szCycleRate = pNodeDataBlock->Attribute(XML_ELEMENTNODE_CYCLERATE);
		pDataBlock->m_nPollRate = (szCycleRate == NULL?0:atoi(szCycleRate));
		const char *szPhase = pNodeDataBlock->Attribute(XML_ELEMENTNODE_PHASE);
		pDataBlock->m_nPollPhase = (szPhase == NULL?0:atoi(szPhase));

		if ((pDataBlock->m_nElemNum * pDataBlock->m_nElemBits)%BITS_PER_BYTE == 0)
			pDataBlock->m_nBlockSize = (pDataBlock->m_nElemNum * pDataBlock->m_nElemBits)/BITS_PER_BYTE;	// ȱʡΪ����
		else
			pDataBlock->m_nBlockSize = (pDataBlock->m_nElemNum * pDataBlock->m_nElemBits)/BITS_PER_BYTE + 1;	// ȱʡΪ����

		pDataBlock->CreateBlockBuffer();
		pDevice->m_mapDataBlocks[pDataBlock->m_strName] = pDataBlock;

		CV_INFO(g_CVDrvierCommonLog, "datablock name : %s, type: %s, addr:%s, elembits : %d, elemcount %d", 
			pDataBlock->m_strName.c_str(), pDataBlock->m_strBlockType.c_str(), pDataBlock->m_strAddress.c_str(), pDataBlock->m_nElemBits, pDataBlock->m_nElemNum);

		if(g_pfnOnDataBlockAdd)
			g_pfnOnDataBlockAdd(pDevice, pDataBlock);

		objDataBlockBuild.ParseTagAndInsertBlock(vecTagInfo, pDataBlock);
		pNodeDataBlock = pNodeDataBlock->NextSiblingElement(XML_ELEMENTNODE_DATABLOCK);
	}

	return DRV_SUCCESS;
}

long CMainTask::LoadDevicesOfGroup(CDriver *pDriver, CTaskGroup* pTaskGrp, TiXmlElement* pNodeGrpDevice)
{
	TiXmlElement* pNodeDevice = pNodeGrpDevice->FirstChildElement(XML_ELEMENTNODE_DEVICE);
	while(pNodeDevice)
	{
		LoadDeviceInfo(pDriver, pTaskGrp, pNodeDevice);
		pNodeDevice = pNodeGrpDevice->NextSiblingElement(XML_ELEMENTNODE_DEVICE);
	}

	return DRV_SUCCESS;
}

// ȷ����TaskGroup�ں�TaskID��ͬ���豸���У����û���豸�飬�������豸�顣
// ��ȷ���豸�����и��豸�����û�������ɸ��豸��������򿽱�pDevice��Ϣ�������ɵ��豸
CTaskGroup * CMainTask::AssureGetTaskGroup(CDriver *pDriver, string strTaskGrpName)
{
	CTaskGroup *pTaskGroup = NULL;
	std::map<string, CTaskGroup*>::iterator itTaskGrp = pDriver->m_mapTaskGroups.find(strTaskGrpName);
	if(itTaskGrp != pDriver->m_mapTaskGroups.end()) // δ�ҵ����豸��
		return itTaskGrp->second;

	pTaskGroup = new CTaskGroup(strTaskGrpName.c_str(), "");
	pTaskGroup->m_pMainTask = this;
	pDriver->m_mapTaskGroups[strTaskGrpName] = pTaskGroup;
	
	return pTaskGroup;
}

// ȷ����TaskGroup�ں�TaskID��ͬ���豸���У����û���豸�飬�������豸�顣
// ��ȷ���豸�����и��豸�����û�������ɸ��豸��������򿽱�pDevice��Ϣ�������ɵ��豸
// ���û���豸�����½�һ����pDevice������ͬ���豸���������ֱ�ӷ����ҵ����豸
CDevice * CMainTask::AssureGetDevice(CDriver *pDriver, CTaskGroup *pTaskGroup, string strDeviceName, CDevice *pDevice)
{
	CDevice *pDeviceRet = NULL;
	std::map<string, CDevice*>::iterator itDevice = pTaskGroup->m_mapDevices.find(strDeviceName);
	if(itDevice != pTaskGroup->m_mapDevices.end()) // �ҵ��豸
	{
		pDeviceRet = itDevice->second;
		return pDeviceRet;
	}

	 // δ�ҵ��豸
	pDeviceRet = new CDevice(pTaskGroup, strDeviceName.c_str());
	pTaskGroup->m_mapDevices[strDeviceName] = pDeviceRet;

	if(pDevice)
	{
		*pDeviceRet = *pDevice;
		if(g_pfnOnDeviceAdd)
			g_pfnOnDeviceAdd(pDeviceRet);
	}



	return pDeviceRet;
}

long CMainTask::Start()
{
	return activate(THR_NEW_LWP | THR_JOINABLE |THR_INHERIT_SCHED);
}

void CMainTask::Stop()
{
	OUTPUTCMD output;
	m_bStop = true;

	memset(&output, 0, sizeof(OUTPUTCMD));

	output.chOutputType = OUTPUT_EXIT;
	if(m_pOutputQuene != NULL)
		m_pOutputQuene->enqueue(output);

	int nWaitResult = wait();
	//IOSync_Finalize();
	ACE_Time_Value tvSleep;
	tvSleep.msec(100);
	while(!m_bStopped)
		ACE_OS::sleep(tvSleep);
}
void ControlCallback(TProtoDriverAPICTRLMsg* pData)
{
	OUTPUTCMD Output;

	memset(&Output, 0, sizeof(OUTPUTCMD));

	Output.nTagID = pData->m_nTagID;
	if(pData->m_nLenBuf > MAX_OUTPUT_CMDDATA_SIZE)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1,_("the control command length is over 2048, nTagID : %d."), Output.nTagID);
		return;
	}
	memcpy(Output.szCmdData, pData->m_pBuf, pData->m_nLenBuf);
	if(pData->m_nDataType == DT_BIT)
	{
		Output.nCmdDataBits = 1;
	}
	else
	{
		Output.nCmdDataBits = pData->m_nLenBuf * BITS_PER_BYTE;
	}

	Output.nDataType = pData->m_nDataType;
	Output.chOutputType = OUTPUT_WRITE;
	Output.lSecond = pData->m_nTimeOutSec;
	Output.lMSecond = pData->m_nTimeOutMs;

	string strTagName = "";
	std::map<long, string>::iterator iter = g_mapAllTags.find(Output.nTagID);
	if (iter != g_mapAllTags.end())
		strTagName = iter->second;
	char szBuffer[ICV_BLOBVALUE_MAXLEN];
	unsigned int nHexBufferLen = ICV_BLOBVALUE_MAXLEN;
	memset(szBuffer, 0, sizeof(szBuffer));
	cvcommon::HexDumpBuf((unsigned char*)pData->m_pBuf, pData->m_nLenBuf, szBuffer, &nHexBufferLen);
	CV_INFO(g_CVDrvierCommonLog, _("the control command, nTagID : %d, TagName : %s, nCmdDataBits : %d, nDataType : %d, szHexValue : %s"),
		pData->m_nTagID, strTagName.c_str(), Output.nCmdDataBits, Output.nDataType, szBuffer);

	MAIN_TASK->m_pOutputQuene->enqueue(Output);

	//������·׷��
	if (g_bNeedTrace)
	{
		ACE_Guard<ACE_Recursive_Thread_Mutex> lockguard(g_mtxMapTraceTags);
		{
			std::map<long, string>::iterator itMap = g_mapTraceTag.find(Output.nTagID);
			if (itMap != g_mapTraceTag.end())
			{
				TRACE_TAG_MSG tracetagmsg;
				strcpy(tracetagmsg.szTagName, itMap->second.c_str());
				strcpy(tracetagmsg.szModName, "driver");
				tracetagmsg.bStatus = TRATAG_STATUS_GOOD;
				//char szVTQ[PDB_MAX_TEXT_VALUE_LEN];
				//CastTypeToASCII(pData->m_pBuf, pData->m_nLenBuf, pData->m_nDataType, szVTQ, sizeof(szVTQ));
				sprintf(tracetagmsg.strMsg, "control cmd. hex value:%s type:%d", szBuffer, pData->m_nDataType);
				tracetagmsg.time = ACE_OS::gettimeofday();
				// int nRet = OP_API_Write_Trace_Tags_Msg(tracetagmsg);
				// CV_CHECK_FAIL_LOG(g_CVDrvierCommonLog, nRet, nRet, "OP_API_Write_Trace_Tags_Msg");
			}
		}
	}
}

/**
 *  �������ݿ�.
 *
 *  @param  -[in]  char*  pszDeviceName: [comment]
 *  @param  -[in]  char*  pszDataBlockName: [comment]
 *
 *  @version     09/01/2008  lijingjing  Initial Version.
 */
CDataBlock* CMainTask::FindDataBlock(char* pszDeviceName, char* pszDataBlockName)
{
	// ���ݵĲ�������ȷ
	if (pszDeviceName == NULL || pszDataBlockName == NULL)
	{
        //���յ���������ʱ�����豸���ƻ������Ϊ��
        CV_ERROR(g_CVDrvierCommonLog, -1, _("Find the device name or block name is empty when receiving the control command."));
		return NULL;
	}

	std::map<string, CTaskGroup*>::iterator iter = m_driverInfo.m_mapTaskGroups.begin();	
	// �����豸�飬�����豸
	for ( ; iter != m_driverInfo.m_mapTaskGroups.end(); iter++)
	{
		CTaskGroup* pTaskGroup = iter->second;

		std::map<string, CDevice*>::iterator iter_device = pTaskGroup->m_mapDevices.find(pszDeviceName);
		if(iter_device ==  pTaskGroup->m_mapDevices.end())
			continue;

		CDevice* pDevice = iter_device->second;

		std::map<string, CDataBlock*>::iterator itDatablock = pDevice->m_mapDataBlocks.find(pszDataBlockName);
		if(itDatablock ==  pDevice->m_mapDataBlocks.end())
			continue;
		return itDatablock->second;
	}

	return NULL;	
}

// �ֹ�����һ����������
void CMainTask::TriggerRefreshConfig()
{
	m_bManualRefreshConfig = true;
}

void CMainTask::AddTag2Map(int32 nTagID, TagBlockAddr* pTagBlockAddr, uint8 nMDIAddrNo)
{
	if(pTagBlockAddr == NULL)
	{
		return;
	}
	ACE_Guard<ACE_Recursive_Thread_Mutex> guard(m_lock);
	std::map<int32, TagBlockAddr*>::iterator iter;
	iter = mapTagIDBlockAddr.find(nTagID);
	if(iter == mapTagIDBlockAddr.end())
	{
		mapTagIDBlockAddr.insert(std::make_pair(nTagID, pTagBlockAddr));
	}
	else
	{
		if(pTagBlockAddr->nTagType != ICV_TAGTYPE_MDI)
		{
			if(iter->second != NULL)
				delete iter->second;
			iter->second = pTagBlockAddr;
		}
		else
		{
			TagBlockAddr* pAddr = iter->second;
			if(nMDIAddrNo == 0)
			{
				pAddr->strDeviceName = pTagBlockAddr->strDeviceName;
				pAddr->strDataBlockName = pTagBlockAddr->strDataBlockName;
				pAddr->nBitLen = pTagBlockAddr->nBitLen;
				pAddr->nBitOffset = pTagBlockAddr->nBitOffset;
				pAddr->nDataType = pTagBlockAddr->nDataType;
			}
			else if(nMDIAddrNo == 1)
			{
				pAddr->strDeviceName1 = pTagBlockAddr->strDeviceName;
				pAddr->strDataBlockName1 = pTagBlockAddr->strDataBlockName;
				pAddr->nBitLen1 = pTagBlockAddr->nBitLen;
				pAddr->nBitOffset1 = pTagBlockAddr->nBitOffset;
				pAddr->nDataType = pTagBlockAddr->nDataType;
			}
			else if(nMDIAddrNo == 2)
			{
				pAddr->strDeviceName2 = pTagBlockAddr->strDeviceName;
				pAddr->strDataBlockName2 = pTagBlockAddr->strDataBlockName;
				pAddr->nBitLen2 = pTagBlockAddr->nBitLen;
				pAddr->nBitOffset2 = pTagBlockAddr->nBitOffset;
				pAddr->nDataType = pTagBlockAddr->nDataType;
			}
			delete pTagBlockAddr;
		}
	}
}
void CMainTask::ClearTagMap()
{
	ACE_Guard<ACE_Recursive_Thread_Mutex> guard(m_lock);
	std::map<int32, TagBlockAddr*>::iterator iter = mapTagIDBlockAddr.begin();
	for (; iter != mapTagIDBlockAddr.end(); iter++)
	{
		if(iter->second != NULL)
		{
			delete iter->second;
			iter->second = NULL;
		}
	}
	mapTagIDBlockAddr.clear();
}



CTaskGroupGuard::CTaskGroupGuard()
{
	m_bStop = false;
}

CTaskGroupGuard::~CTaskGroupGuard()
{

}

long CTaskGroupGuard::Start()
{
	return activate(THR_NEW_LWP | THR_JOINABLE |THR_INHERIT_SCHED);
}

void CTaskGroupGuard::Stop()
{
	m_bStop = true;
	wait();
}

static void DoExit()
{
	//ʹ���Լ�ɱ�Լ��ķ�����������
#if defined(__linux) || defined(__linux__) || defined(linux)
#define CMDLENGTH   255
#define	BUFFERLENGTH 255
#define FGETSIZE    255
				//kill pid
	FILE* fp;
	char buf[BUFFERLENGTH];
	memset(buf, 0x00, sizeof(buf));
	char cmd[CMDLENGTH] = { '\0' };
	string pid;
	sprintf(cmd, "pidof %s", g_strDrvName.c_str());
	if ((fp = popen(cmd, "r")) != NULL)
	{
		if (fgets(buf, FGETSIZE, fp) != NULL)
		{
			pid = buf;
			CV_INFO(g_CVDrvierCommonLog, "driver %sPID is %s", g_strDrvName.c_str(), pid.c_str());
		}
	}
	else
		::exit(1);
	pclose(fp);

	memset(cmd, 0x00, sizeof(cmd));
	memset(buf, 0x00, sizeof(buf));
	sprintf(cmd, "kill -9 %s", pid.c_str());
	CV_CRITICAL(g_CVDrvierCommonLog, -1, "killing driver %s ,cmd = %s", g_strDrvName.c_str(), cmd);
	if ((fp = popen(cmd, "r")) != NULL)
	{
		fgets(buf, FGETSIZE, fp);
	}
	else
		::exit(1);
	pclose(fp);
	CV_CRITICAL(g_CVDrvierCommonLog, -1, "Kill driver %s %s", g_strDrvName.c_str(), pid.c_str());

#else
	::exit(1);
#endif
}

int CTaskGroupGuard::svc()
{
	CV_INFO(g_CVDrvierCommonLog, "CTaskGroupGuard thread start,  threadid %x", ACE_Thread::self());
	std::map<string, CTaskGroup*>::iterator iter;
	for (iter = MAIN_TASK->m_driverInfo.m_mapTaskGroups.begin() ; iter != MAIN_TASK->m_driverInfo.m_mapTaskGroups.end(); iter++)
	{
		iter->second->Start();
	}

	while(!m_bStop)
	{
		//���taskgroup�Ƿ����˳��ģ��еĻ���taskgroup����	
		for (iter = MAIN_TASK->m_driverInfo.m_mapTaskGroups.begin(); iter != MAIN_TASK->m_driverInfo.m_mapTaskGroups.end(); ++iter)
		{
			if (0 == iter->second->thr_count())
			{
				CV_CRITICAL(g_CVDrvierCommonLog, -1, _("task group %s thread exit!"), iter->first.c_str());// �豸��%s�߳��˳�
				//�߳��쳣��ʱ���������̵��ڴ涼�������ˣ���������򵥴ֱ��������˳��ķ����˳���������drvctrl��������
				//iter->second->Stop();
				//iter->second->Terminate();
				//iter->second->Start();
				ACE_OS::sleep(1);
				DoExit();
			}
		}
		//��� MAIN_TASK
		if (0 == MAIN_TASK->thr_count())
		{
			CV_CRITICAL(g_CVDrvierCommonLog, -1, _("MainTask thread exit!"));
			ACE_OS::sleep(1);
			DoExit();
		}
		//��� DRVIERCTRL_TASK
		if (0 == DRIVERCTRL_TASK->thr_count())
		{
			CV_CRITICAL(g_CVDrvierCommonLog, -1, _("DRVIERCTRL_TASK thread exit!"));
			ACE_OS::sleep(1);
			DoExit();
		}

		ACE_OS::sleep(1);
	}

	// ֹͣ�߳�
	for (iter = MAIN_TASK->m_driverInfo.m_mapTaskGroups.begin() ; iter != MAIN_TASK->m_driverInfo.m_mapTaskGroups.end(); iter++)
	{
		iter->second->Stop();
	}
	CV_INFO(g_CVDrvierCommonLog, "CTaskGroupGuard thread stop threadid %x", ACE_Thread::self());
	return 0;
}

CTaskConfigErrTag::CTaskConfigErrTag()
{
	m_bStop = false;
}

CTaskConfigErrTag::~CTaskConfigErrTag()
{

}

long CTaskConfigErrTag::Start()
{
	return activate(THR_NEW_LWP | THR_JOINABLE | THR_INHERIT_SCHED);
}

void CTaskConfigErrTag::Stop()
{
	m_bStop = true;
	wait();
}

int CTaskConfigErrTag::svc()
{
	CV_INFO(g_CVDrvierCommonLog, "CTaskConfigErrTag thread start,  threadid %x", ACE_Thread::self());

	//��¼δ���䵽���ݿ�ĵĵ�
	char g_szConfigFile[ICV_SHORTFILENAME_MAXLEN] = {};
	char g_szConfigFile2[ICV_SHORTFILENAME_MAXLEN] = {};
	ofstream ss_ConfigErrTag;
	ofstream ss_ConfigErrTag2;
	char* szCVProjCfgPath = (char*)CVComm.GetCVEnv();
	if (szCVProjCfgPath)
	{
		string tmpFileName = g_strDrvName;
		string tmpFileName2 = g_strDrvName;
		sprintf(g_szConfigFile, "%s%cdrivers%c%s%c%s.txt", szCVProjCfgPath, ACE_DIRECTORY_SEPARATOR_CHAR, ACE_DIRECTORY_SEPARATOR_CHAR, g_strDrvName.c_str(), ACE_DIRECTORY_SEPARATOR_CHAR, tmpFileName.append("badcfgtags").c_str());
		sprintf(g_szConfigFile2, "%s%cdrivers%c%s%c%s.txt", szCVProjCfgPath, ACE_DIRECTORY_SEPARATOR_CHAR, ACE_DIRECTORY_SEPARATOR_CHAR, g_strDrvName.c_str(), ACE_DIRECTORY_SEPARATOR_CHAR, tmpFileName2.append("goodcfgtags").c_str());
	}
	else
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, "get iplatda environment variables failed!");
		return -1;
	}

	ss_ConfigErrTag.open(g_szConfigFile, ios::out | ios::trunc);
	ss_ConfigErrTag2.open(g_szConfigFile2, ios::out | ios::trunc);
	std::vector<int32 > vecTagID;
	for (std::map<int32, ConfigErrTags >::iterator iter = g_mapConfigErrTags.begin(); iter != g_mapConfigErrTags.end(); ++iter)
	{
		if (iter->second.bError) {
			CV_WARN(g_CVDrvierCommonLog, -1, "Tag %s Addr %s Config Error,Check Tag Addr!", iter->second.taginfo.szTagName, iter->second.taginfo.szAddress);
			vecTagID.push_back(iter->first);
			ss_ConfigErrTag << "TagName:\t" << iter->second.taginfo.szTagName << "\tTagAddr:\t"<< iter->second.taginfo.szAddress << endl;
		}
		else	
			ss_ConfigErrTag2 << "TagName:\t" << iter->second.taginfo.szTagName << "\tTagAddr:\t" << iter->second.taginfo.szAddress << endl;
	}

	ss_ConfigErrTag.close();
	ss_ConfigErrTag2.close();

	while (!m_bStop)
	{
		if (vecTagID.size() > 0)
		{
			TCV_TimeStamp tv = (timeval)ACE_OS::gettimeofday();
			int16 nQuality = RetValue2Quality(DATA_STATUS_CONFIG_ERROR);
			int nRet = CVDrv_SetBlockQuality(vecTagID, &tv, nQuality);
		}
		ACE_OS::sleep(1);
	}

	CV_INFO(g_CVDrvierCommonLog, "CTaskConfigErrTag thread stop threadid %x", ACE_Thread::self());
	return 0;
}