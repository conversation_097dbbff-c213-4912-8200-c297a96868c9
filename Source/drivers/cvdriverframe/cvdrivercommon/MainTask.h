/**************************************************************
 *  Filename:    CtrlProcessTask.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: 控制命令处理类.
 *
 *  @author:     lijingjing
 *  @version     05/28/2008  lijingjing  Initial Version
 *  @version     01/28/2012  wanyingjie  Initial Version
**************************************************************/

#ifndef _CTRL_PROCESS_TASK_H_
#define _CTRL_PROCESS_TASK_H_

#include <ace/Task.h>
#include "DataBlock.h"
#include "tinyxml/tinyxml.h"
#include "Driver.h"
#include <map>
#include <sstream>
#include <iostream>
#include <fstream>
#include "common/SimpleQueue.h"
#include "driversdk/cvdrivercommon.h"

using namespace  std;
#define MAX_OUTPUT_CMDDATA_SIZE		2048
typedef struct device_struct
{
	char szName[ICV_DEVICEIOADDR_MAXLEN];	// 设备名称
	//	long nDeviceID;			// 设备ID
	device_struct()
	{
		memset(this, 0, sizeof(device_struct));
	}
}DEVICE_CONFIG;
// 控制命令记录结构
typedef struct tagOUTPUTCMD
{ 	
	char uVersion;				// 版本号。如果是iCV5.0-5.2的控制命令类型，则无该字段，读取出来应该为chOutputType，为1-6.如果是本结构体则至少为20
	char chOutputType;			// 控制命令类型：写控制命令，注册异常数据，注销异常数据，停止驱动
	char szDeviceName[ICV_DEVICENAME_MAXLEN];		// 设备名和数据块名，形如"device0:AB_BLOCK"
	char szBlockName[ICV_DATABLOCKNAME_MAXLEN];
	int32 nTagID;
	uint8 nDataType;
	long nByteOffset;			// Bytes地址偏移
	short nBitOffset;			// word内的偏移量
	int	 nCmdDataBits;			// 按位计算的长度命令长度
	char szCmdData[MAX_OUTPUT_CMDDATA_SIZE];

	// 下面是pbscanner专用数据
	bool bACK;					// 是否需要控制反馈, pbScanner控制时是否需要确认标志
	long nIpn;					// 控制命令标记, 或异常数据标记，类似于事物号
	char chLbhType;				// 注册的异常数据类型
	long nExcDataSize;			// 注册注销异常数据的长度
	long lSecond;				// 客户端传过来的时间戳，单位是秒
	long lMSecond;
	tagOUTPUTCMD()
	{
		memset(this, 0, sizeof(tagOUTPUTCMD));
		uVersion = 20;	
	}

	~tagOUTPUTCMD()
	{		
	}
}OUTPUTCMD;

typedef struct _ConfigErrTags
{
	TagInfo taginfo;
	bool bError;
	_ConfigErrTags()
	{
		memset(this, 0, sizeof(_ConfigErrTags));
		this->bError = true;
	}

}ConfigErrTags;

//这个类的作用是监控TaskGroup的工作线程，线程退出时重新启动
class CTaskGroupGuard : public ACE_Task<ACE_MT_SYNCH>
{	
public:
	CTaskGroupGuard();
	virtual ~CTaskGroupGuard();

	long Start();
	void Stop();

protected:
	virtual int svc();	
	bool m_bStop;
};

class CTaskConfigErrTag : public ACE_Task<ACE_MT_SYNCH>
{
public:
	CTaskConfigErrTag();
	virtual ~CTaskConfigErrTag();

	long Start();
	void Stop();

protected:
	virtual int svc();
	bool m_bStop;
};

class CTaskGroup;
class CDevice;
class CTaskHeartBeat;
class CTaskBatchUpdateData;
class CTaskBatchUpdateData2;
class CTaskDrvDevStatus;
class CTaskGroupGuard;
class CTaskConfigErrTag;
class CMainTask : public ACE_Task<ACE_MT_SYNCH>
{
	friend class ACE_Singleton<CMainTask, ACE_Thread_Mutex>;
public:
	CMainTask();
	virtual ~CMainTask();

	ACE_Recursive_Thread_Mutex m_lock;
	long Start();
	void Stop();
	// _TODO 这个是回调中调用的，可能线程不安全
	CDataBlock* FindDataBlock(char* pszDeviceName, char* pszDataBlockName);
	void DelDevicesFromDIT( std::map<string, CDevice*> &mapDevices);
	void AddDevices2DIT(std::map<string, CDevice*> &mapDevices);
	void ModifyDevicesInDIT(std::map<string, CDevice*> &mapCurDevices, std::map<string, CDevice*> &mapModifyDevices);
	void AddTag2Map(int32 nTagID, TagBlockAddr* pTagBlockAddr, uint8 nMDIAddrNo);
	void ClearTagMap();
	// 当前运行的设备信息
	CDriver		m_driverInfo;
	std::map<int32, TagBlockAddr*> mapTagIDBlockAddr;
	CSimpleThreadQueue<OUTPUTCMD> *m_pOutputQuene;//用于接收控制信息
	void TriggerRefreshConfig();
	bool m_bStopped;
protected:
	bool m_bStop;
	bool m_bManualRefreshConfig;	// 手工触发在线配置一次
	CTaskHeartBeat* m_pTaskHeartBeat;
	CTaskDrvDevStatus* m_pTaskDrvDevStatus;
	CTaskBatchUpdateData* m_pTaskBatchUpdateData;
	CTaskBatchUpdateData2* m_pTaskBatchUpdateData2;
	CTaskGroupGuard* m_pTaskGroupGuard;
	CTaskConfigErrTag* m_pTaskConfigErrTag;
protected:
	virtual int svc();

	// 线程初始化
	long OnStart();

	// 线程中停止
	void OnStop();

	// 注册和注销异常数据
	long LoginException(OUTPUTCMD* pOutput);
	
	// 处理写控制命令
	long ProcessWriteCmd(OUTPUTCMD* pOutput);

	// 处理队列创建和释放
	long ProcessQueue(OUTPUTCMD* pOutput);

	// 从配置文件加载和重新加载所有的配置theTaskGroupList
	long LoadConfig(CDriver &driver);

	// 加载某个设备下的所有数据块到pDevice中
	long LoadDataBlockOfDevice(CDriver *pDriver, CDevice* pDevice, TiXmlElement* pNodeDataBlock);

	//
	long LoadDataBlockOfDevice(CDriver *pDriver, CDevice* pDevice, TiXmlElement* pNodeDevice, unsigned int nCurrentConnectionNum, unsigned int nTotalConnectionNum);

	// 加载某个设备组下的所有设备信息到pTaskGrp
	long LoadDevicesOfGroup(CDriver *pDriver, CTaskGroup* pTaskGrp, TiXmlElement* pNodeGrpDevice);

	// 加载某个设备信息到pDevice
	long LoadDeviceInfo(CDriver *pDriver, CTaskGroup* pTaskGrp, TiXmlElement* pNodeDevice);

	//
	long LoadDeviceInfo(CDriver *pDriver, CTaskGroup* pTaskGrp, TiXmlElement* pNodeDevice, unsigned int nCurrConnIndex, unsigned int nTotalConNum);

	// 在线重新加载配置
	void HandleOnlineConfig();

	//根据设备块查询字符串查找到datablock
	CDataBlock *GetDataBlock(char *szAddr);
	long SplitAddr(const char* pszBuf, char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN]);
	long AddTaskGrps2DIT(std::map<string, CTaskGroup*> &mapAddTaskGrp);
	void AddDataBlocks2DIT(std::map<string, CDataBlock*> &mapNewDataBlocks);
	int AddDataBlock2DIT(CDataBlock *pDataBlock);
	long ModifyTaskGrpsInDIT(std::map<string, CTaskGroup*> &mapModifyTaskGrp);
	void ModifyDataBlocksInDIT(std::map<string, CDataBlock*> &mapCurDataBlocks, std::map<string, CDataBlock*> &mapModifyDataBlocks);
	long DelTaskGrpsFromDIT(std::map<string, CTaskGroup*> &mapDelTaskGrp);
	void DelDataBlockFromDIT(CDataBlock *pDataBlock);
	void DelDataBlocksFromDIT( std::map<std::string, CDataBlock*> & mapDevs);
	bool IsDeviceConfigChanged(CDevice* pCurActiveDev, CDevice* pNewDev);
	CTaskGroup * AssureGetTaskGroup(CDriver *pDriver, string strTaskGrpName);
	CDevice * AssureGetDevice(CDriver *pDriver, CTaskGroup *pTaskGroup, string strDeviceName, CDevice *pDevice);
	bool GetTaskGroupByDeviceAndDataBlockName(string strDeviceName, string strDataBlockName, CTaskGroup *&pTaskGroup, CDevice *&pDevice, CDataBlock *pDataBlock);

	//OP服务回调函数
	//int tag_trace_callback(const std::list<OP_TRACE_TAG_Record>& allTraceTags );
};

#define MAIN_TASK ACE_Singleton<CMainTask, ACE_Thread_Mutex>::instance()

extern unsigned short RetValue2Quality(long lRet);

#endif  // _CTRL_PROCESS_TASK_H_
