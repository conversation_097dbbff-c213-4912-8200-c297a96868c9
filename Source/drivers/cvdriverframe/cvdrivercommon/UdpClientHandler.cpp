#include "UdpClientHandler.h"
#include "driversdk/cvdrivercommon.h"
#include "ace/Time_Value.h"
#include <ace/OS_NS_strings.h>
#include "processdb/DriverApi.h"
#include <ace/ACE.h>
#include "gettext/libintl.h"
#include "common/CommHelper.h"
#include "common/CVLog.h"

#define RECV_BUFFER_LEN 1024

extern string	g_strDrvName;
extern CCVLog 	g_CVDrvierCommonLog;

CUdpClientHandler::CUdpClientHandler(void)
{
	m_port = 0;
	m_moduleno = 0;
	m_pUdpSocket = NULL;
	m_pMcastSocket = NULL;
}

CUdpClientHandler::~CUdpClientHandler(void)
{
	DisConnect();
}

long CUdpClientHandler::SetConnectParam(char *szConnParam)
{
	if (NULL == szConnParam)
		return -1;

	// connparam="ip=*********;srcip=0.0.0.0;moduleno=200;port=2000;byteorder=1;"
	string strConnParam = szConnParam;

	// 获取端口号
	int nPos = strConnParam.find("port=");
	if(nPos == string::npos)
	{
		return -1;
	}

	string strPortNo = strConnParam.substr(nPos + strlen("port="));
	nPos = strPortNo.find(';');
	if(nPos != strPortNo.npos)
		strPortNo = strPortNo.substr(0, nPos);

	m_port = ::atoi(strPortNo.c_str());

	return DRV_SUCCESS;
}

long CUdpClientHandler::Connect()
{
	// 如果套接字已经创建，先关闭
	if(m_pUdpSocket || m_pMcastSocket)
		DisConnect();
	
	// 单播模式：绑定到指定端口，接收任何来源的数据
	m_pUdpSocket = new ACE_SOCK_Dgram();

	// 绑定到指定端口接收数据
	m_localAddr.set(m_port, (ACE_UINT32)INADDR_ANY);

	// 打开UDP套接字
	if(m_pUdpSocket->open(m_localAddr) == -1)
	{
		
		delete m_pUdpSocket;
		m_pUdpSocket = NULL;
		return -1;
	}

	CV_INFO(g_CVDrvierCommonLog, "Device %s listening on UDP port %d for receiving data from any source",
				m_strDeviceName.c_str(), m_port);

	return DRV_SUCCESS;
	
}

long CUdpClientHandler::DisConnect()
{
	if(m_pUdpSocket)
	{
		m_pUdpSocket->close();
		delete m_pUdpSocket;
		m_pUdpSocket = NULL;
	}
	if(m_pMcastSocket)
	{
		m_pMcastSocket->close();
		delete m_pMcastSocket;
		m_pMcastSocket = NULL;
	}
	return DRV_SUCCESS;
}

// 不实现
long CUdpClientHandler::Send(const char* pszSendBuf, long nSendBytes, long lTimeoutMs)
{
	return DRV_SUCCESS;
}

long CUdpClientHandler::Recv(char* szRecvBuf, long nRecvBytes, long lTimeoutMs)
{
	ACE_Time_Value tvTimeout;
	tvTimeout.msec(lTimeoutMs);
	ACE_INET_Addr remoteAddr;

	long lRecvBytes = 0;

	lRecvBytes = m_pUdpSocket->recv(szRecvBuf, nRecvBytes, remoteAddr, 0, &tvTimeout);

	return lRecvBytes;
}

long CUdpClientHandler::Recv_n(char *szReadBuf, long lExactReadBufLen, long lTimeOutMS)
{
	return Recv(szReadBuf, lExactReadBufLen, lTimeOutMS);
}

void CUdpClientHandler::ClearDeviceRecvBuffer()
{
	char szBuffer[RECV_BUFFER_LEN] = {'\0'};
	int nRecvBytes = 0;

	while((nRecvBytes = Recv(szBuffer, sizeof(szBuffer), 2000)) > 0)
	{
		if (nRecvBytes < sizeof(szBuffer))
			break;
	}
}

long CUdpClientHandler::Start()
{
	return Connect();
}

long CUdpClientHandler::Stop()
{
	return DisConnect();
}