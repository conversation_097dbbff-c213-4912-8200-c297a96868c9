#include "UdpClientHandler.h"
#include "driversdk/cvdrivercommon.h"
#include "ace/Time_Value.h"
#include <ace/OS_NS_strings.h>
#include "processdb/DriverApi.h"
#include <ace/ACE.h>
#include "gettext/libintl.h"
#include "common/CommHelper.h"
#include "common/CVLog.h"

#define RECV_BUFFER_LEN 1024

extern string	g_strDrvName;
extern CCVLog 	g_CVDrvierCommonLog;

CUdpClientHandler::CUdpClientHandler(void)
{
	m_port = 0;
	m_moduleno = 0;
	m_multicast = false;
	m_transfermode = false;
	m_pUdpSocket = NULL;
	m_pMcastSocket = NULL;
}

CUdpClientHandler::~CUdpClientHandler(void)
{
	DisConnect();
}

long CUdpClientHandler::SetConnectParam(char *szConnParam)
{
	if (NULL == szConnParam)
		return -1;

	// connparam="port=1234;multicast=0;transfermode=0"
	string strConnParam = szConnParam;

	// 获取端口号
	int nPos = strConnParam.find("port=");
	if(nPos == string::npos)
	{
        //设备 %s 作为UDPClinet进行连接, 找不到port参数
		CV_ERROR(g_CVDrvierCommonLog, -1, _("It Found an error when device %s connected as UDPClient, can not find port parameter."), m_strDeviceName.c_str());
		return -1;
	}

	string strPortNo = strConnParam.substr(nPos + strlen("port="));
	nPos = strPortNo.find(';');
	if(nPos != strPortNo.npos)
		strPortNo = strPortNo.substr(0, nPos);

	m_port = ::atoi(strPortNo.c_str());

	// 获取多播参数
	nPos = strConnParam.find("multicast=");
	if(nPos == string::npos)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("It Found an error when device %s connected as UDPClient, can not find multicast parameter."), m_strDeviceName.c_str());
		return -1;
	}
	string strMulticast = strConnParam.substr(nPos + strlen("multicast="));
	nPos = strMulticast.find(';');
	if(nPos != strMulticast.npos)
		strMulticast = strMulticast.substr(0, nPos);

	m_multicast = ::atoi(strMulticast.c_str());

	// 获取缓冲区模式
	nPos = strConnParam.find("transfermode=");
	if(nPos == string::npos)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("It Found an error when device %s connected as UDPClient, can not find transfermode parameter."), m_strDeviceName.c_str());
		return -1;
	}

	string strTransferMode = strConnParam.substr(nPos + strlen("transfermode="));
	nPos = strTransferMode.find(';');
	if(nPos != strTransferMode.npos)
		strTransferMode = strTransferMode.substr(0, nPos);

	m_transfermode = ::atoi(strTransferMode.c_str());

	// 获取模块序号（可选参数）
	nPos = strConnParam.find("moduleno=");
	if(nPos != string::npos)
	{
		string strModuleNo = strConnParam.substr(nPos + strlen("moduleno="));
		nPos = strModuleNo.find(';');
		if(nPos != strModuleNo.npos)
			strModuleNo = strModuleNo.substr(0, nPos);
		m_moduleno = ::atoi(strModuleNo.c_str());
	}

	return DRV_SUCCESS;
}

long CUdpClientHandler::Connect()
{
	// 如果套接字已经创建，先关闭
	if(m_pUdpSocket || m_pMcastSocket)
		DisConnect();

	try
	{
		if(m_multicast)
		{
			// 组播模式：创建组播套接字，绑定到指定端口接收组播数据
			m_pMcastSocket = new ACE_SOCK_Dgram_Mcast();

			// 绑定到指定端口
			m_localAddr.set(m_port, (ACE_UINT32)INADDR_ANY);

			// 打开组播套接字
			if(m_pMcastSocket->open(m_localAddr) == -1)
			{
				CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s failed to open multicast socket on port %d"),
					m_strDeviceName.c_str(), m_port);
				delete m_pMcastSocket;
				m_pMcastSocket = NULL;
				return -1;
			}

			CV_INFO(g_CVDrvierCommonLog, "Device %s opened multicast socket on port %d for receiving data",
				m_strDeviceName.c_str(), m_port);
		}
		else
		{
			// 单播模式：绑定到指定端口，接收任何来源的数据
			m_pUdpSocket = new ACE_SOCK_Dgram();

			// 绑定到指定端口接收数据
			m_localAddr.set(m_port, (ACE_UINT32)INADDR_ANY);

			// 打开UDP套接字
			if(m_pUdpSocket->open(m_localAddr) == -1)
			{
				CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s failed to open UDP socket on port %d"),
					m_strDeviceName.c_str(), m_port);
				delete m_pUdpSocket;
				m_pUdpSocket = NULL;
				return -1;
			}

			CV_INFO(g_CVDrvierCommonLog, "Device %s listening on UDP port %d for receiving data from any source",
				m_strDeviceName.c_str(), m_port);
		}

		return DRV_SUCCESS;
	}
	catch(...)
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s connect failed with exception"), m_strDeviceName.c_str());
		DisConnect();
		return -1;
	}
}

long CUdpClientHandler::DisConnect()
{
	if(m_pUdpSocket)
	{
		m_pUdpSocket->close();
		delete m_pUdpSocket;
		m_pUdpSocket = NULL;
	}
	if(m_pMcastSocket)
	{
		m_pMcastSocket->close();
		delete m_pMcastSocket;
		m_pMcastSocket = NULL;
	}
	return DRV_SUCCESS;
}

// 不实现
long CUdpClientHandler::Send(const char* pszSendBuf, long nSendBytes, long lTimeoutMs)
{
	return DRV_SUCCESS;
}

long CUdpClientHandler::Recv(char* szRecvBuf, long nRecvBytes, long lTimeoutMs)
{
	ACE_Time_Value tvTimeout;
	tvTimeout.msec(lTimeoutMs);
	ACE_INET_Addr remoteAddr;

	long lRecvBytes = 0;

	if(m_multicast && m_pMcastSocket)
	{
		// 组播接收
		lRecvBytes = m_pMcastSocket->recv(szRecvBuf, nRecvBytes, remoteAddr, 0, &tvTimeout);
	}
	else if(!m_multicast && m_pUdpSocket)
	{
		// 单播接收
		lRecvBytes = m_pUdpSocket->recv(szRecvBuf, nRecvBytes, remoteAddr, 0, &tvTimeout);
	}
	else
	{
		CV_ERROR(g_CVDrvierCommonLog, -1, _("Device %s socket not initialized"), m_strDeviceName.c_str());
		return -1;
	}

	return lRecvBytes;
}

long CUdpClientHandler::Recv_n(char *szReadBuf, long lExactReadBufLen, long lTimeOutMS)
{
	return Recv(szReadBuf, lExactReadBufLen, lTimeOutMS);
}

void CUdpClientHandler::ClearDeviceRecvBuffer()
{
	char szBuffer[RECV_BUFFER_LEN] = {'\0'};
	int nRecvBytes = 0;

	while((nRecvBytes = Recv(szBuffer, sizeof(szBuffer), 2000)) > 0)
	{
		if (nRecvBytes < sizeof(szBuffer))
			break;
	}
}

long CUdpClientHandler::Start()
{
	return Connect();
}

long CUdpClientHandler::Stop()
{
	return DisConnect();
}