#include "TaskHeartBeat.h"
#include "ace/High_Res_Timer.h"

extern CCVLog g_CVDrvierCommonLog;

CTaskHeartBeat::CTaskHeartBeat(void)
{
	ACE_Select_Reactor *pSelectReactor = new ACE_Select_Reactor();
	m_pReactor = new ACE_Reactor(pSelectReactor, true);
	m_pReactor->timer_queue(&m_timerQueue);
	reactor(m_pReactor);

	m_pHeartBeatTimer = new CHeartBeatTimer(this);
	m_bExit = false;
}


CTaskHeartBeat::~CTaskHeartBeat(void)
{
	if(m_pHeartBeatTimer)
	{
		delete m_pHeartBeatTimer;
		m_pHeartBeatTimer = NULL;
	}

	if(m_pReactor)	
	{
		delete m_pReactor;
		m_pReactor = NULL;
	}
}

long CTaskHeartBeat::Start()
{
	m_bExit = false;
	int nRet = this->activate(THR_NEW_LWP | THR_JOINABLE |THR_INHERIT_SCHED);
	if (0 == nRet)
		return DRV_SUCCESS;

	return nRet;
}

void CTaskHeartBeat::Stop() 
{
	this->m_bExit = true;

	this->reactor()->end_reactor_event_loop();
	this->wait();
	CV_WARN(g_CVDrvierCommonLog, -1, "Heart beat thread exit!");
}


int CTaskHeartBeat::svc()
{
	this->reactor()->owner(ACE_OS::thr_self ());

	OnStart();
	while (!m_bExit)
	{
		this->reactor()->reset_reactor_event_loop();
		this->reactor()->run_reactor_event_loop(ReactorEventHook);
	}

	OnStop();
	return 0;
}

void CTaskHeartBeat::OnStart()
{
	if(m_pHeartBeatTimer)
		m_pHeartBeatTimer->Start();
}

void CTaskHeartBeat::OnStop()
{
	if(m_pHeartBeatTimer)
		m_pHeartBeatTimer->Stop();

	m_pReactor->close();
}
