#include "TaskBatchUpdateData.h"
#include "ace/High_Res_Timer.h"
#include "OPAPI.h"

extern CCVLog g_CVDrvierCommonLog;
extern std::map<long, string>  g_mapTraceTag;//id name
extern bool g_bNeedTrace;
extern ACE_Recursive_Thread_Mutex g_mtxMapTraceTags;
extern CSimpleThreadQueue<ACE_Message_Block*> g_BatchDataQueue;
extern CSimpleThreadQueue<ACE_Message_Block*> g_BatchDataQueue2;

#define BATCH_TAG_NUM_MAX							8192
#define BATCH_SEND_INTERVAL							50			// ms
#define BATCH_DEQUEUE_WAITTIME						1			// ms


CTaskBatchUpdateData::CTaskBatchUpdateData(void)
{
	if(g_pfnOnBatchUpdateData)
	{
		g_pfnOnBatchUpdateData(m_nDequeueTimeOut,m_nDequeueSize);
		if(m_nDequeueTimeOut <= 0)
			m_nDequeueTimeOut = BATCH_SEND_INTERVAL;
		if(m_nDequeueSize <= 0)
			m_nDequeueSize = BATCH_TAG_NUM_MAX;
		//nDequeueTimeOut = nMsTimeOut;
		//nDequeueSize = nDataSize;
	}

}

CTaskBatchUpdateData::~CTaskBatchUpdateData(void)
{
	m_bExit = false;
	for (int i = 0;i < m_vecData.size(); ++i)
	{
		proto_cvdrvcomm_batch_updatedata_unpack_free(&m_vecData[i]);
	}
	m_vecData.clear();

}

long CTaskBatchUpdateData::Start()
{
	m_bExit = false;
	int nRet = this->activate(THR_NEW_LWP | THR_JOINABLE |THR_INHERIT_SCHED);
	if (0 == nRet)
		return DRV_SUCCESS;

	return nRet;
}

void CTaskBatchUpdateData::Stop() 
{
	this->m_bExit = true;
	this->wait();
	CV_WARN(g_CVDrvierCommonLog, -1, "BatchUpdateData thread exit!");
}

long CTaskBatchUpdateData::GetDataParams()
{

	return 0;
}


int CTaskBatchUpdateData::svc()
{
	int nRet = ICV_SUCCESS;

	ACE_Time_Value tv_wait;
	tv_wait.set_msec(BATCH_DEQUEUE_WAITTIME);
	ACE_Message_Block* pMsg = NULL;

	//�����е�tag������
	//const int32 nTAG_NUM_MAX = 8192;
	//const int32 nSEND_INTERVAL_MIN_MS = 50;
	const int32 nTAG_NUM_MAX = m_nDequeueSize;
	const int32 nSEND_INTERVAL_MIN_MS = m_nDequeueTimeOut;

	ACE_Time_Value  tmLastSend = ACE_OS::gettimeofday();
	ACE_Time_Value  tmThisSend = ACE_OS::gettimeofday();
	ACE_Time_Value  tmSubSend;

	while (!m_bExit)
	{
		if(!g_pfnOnBatchUpdateData)
		{
			ACE_OS::sleep(tv_wait);
			continue;
		}

		ACE_Time_Value tv_abs = ACE_OS::gettimeofday() + tv_wait;
		pMsg = NULL;
		nRet = g_BatchDataQueue.dequeue(pMsg, &tv_abs);
		if(NULL != pMsg)
		{
			//dequeque succeed
			TProtoIDVTQ vtq;
			proto_cvdrvcomm_batch_updatedata_unpack(pMsg->rd_ptr(),&vtq);
			m_vecData.push_back(vtq);
			pMsg->release();
		}

		tmThisSend = ACE_OS::gettimeofday();
		tmSubSend = tmThisSend - tmLastSend;
		int32 nWaitTimeMs = tmSubSend.get_msec();
		if (nWaitTimeMs > nSEND_INTERVAL_MIN_MS  || m_vecData.size()>= nTAG_NUM_MAX )  // ���ͼ������nSEND_INTERVAL_MIN_MS��size�Ƚϴ�ʱ����
		{
			if(m_vecData.size() > 0 )
			{
				//update
				CV_DEBUG(g_CVDrvierCommonLog,"nWaitTimeMs %d size %d",nWaitTimeMs,m_vecData.size());
				if (g_bNeedTrace)
				{
					ACE_Guard<ACE_Recursive_Thread_Mutex> lockguard(g_mtxMapTraceTags);
					{
						for (vector<TProtoIDVTQ>::const_iterator iter = m_vecData.begin(); iter != m_vecData.end(); iter++)
						{
							std::map<long, string>::iterator itMap = g_mapTraceTag.find(iter->m_nTagID);
							if (itMap != g_mapTraceTag.end())
							{
								TRACE_TAG_MSG tracetagmsg;
								char szVTQ[PDB_MAX_TEXT_VALUE_LEN];
								strcpy(tracetagmsg.szTagName, itMap->second.c_str());
								strcpy(tracetagmsg.szModName, "driver");
								QUALITY_STATE quality = (*(QUALITY_STATE *)&iter->m_nQuality);
								if (quality.nQuality == QUALITY_GOOD)
								{
									tracetagmsg.bStatus = TRATAG_STATUS_GOOD;
									CastTypeToASCII(iter->m_pBuf, iter->m_nLenBuf, iter->m_nDataType, szVTQ, sizeof(szVTQ));
									sprintf(tracetagmsg.strMsg, "good quality. value:%s type:%d", szVTQ, iter->m_nDataType);
								}
								else
								{
									tracetagmsg.bStatus = TRATAG_STATUS_BAD;
									sprintf(szVTQ, "tag bad quality!  quality %d", iter->m_nQuality);
								}
								tracetagmsg.time = ACE_OS::gettimeofday();
								// nRet = OP_API_Write_Trace_Tags_Msg(tracetagmsg);
								// CV_CHECK_FAIL_LOG(g_CVDrvierCommonLog, nRet, nRet, "OP_API_Write_Trace_Tags_Msg");
							}
						}	
					}
				}
				//GOOD tags
				nRet = CVDrv_SaveData(&m_vecData);
				for(int i = 0; i < m_vecData.size(); i++)
				{
					proto_cvdrvcomm_batch_updatedata_unpack_free(&m_vecData[i]);
				}
				CV_DEBUG(g_CVDrvierCommonLog, " BatchUpdateData, CVDrv_SaveData nRet : %d, m_vecData size : %d!", nRet, m_vecData.size());
				m_vecData.clear();
				
				//after send
				tmLastSend = ACE_OS::gettimeofday();
				continue;
			}
		}
	}

	return 0;
}

CTaskBatchUpdateData2::CTaskBatchUpdateData2(void)
{
	if(g_pfnOnBatchUpdateData)
	{
		g_pfnOnBatchUpdateData(m_nDequeueTimeOut,m_nDequeueSize);
		if(m_nDequeueTimeOut <= 0)
			m_nDequeueTimeOut = BATCH_SEND_INTERVAL;
		if(m_nDequeueSize <= 0)
			m_nDequeueSize = BATCH_TAG_NUM_MAX;
		//nDequeueTimeOut = nMsTimeOut;
		//nDequeueSize = nDataSize;
	}

}

CTaskBatchUpdateData2::~CTaskBatchUpdateData2(void)
{
	m_bExit = false;
	m_vecData2.clear();
}

long CTaskBatchUpdateData2::Start()
{
	m_bExit = false;
	int nRet = this->activate(THR_NEW_LWP | THR_JOINABLE |THR_INHERIT_SCHED);
	if (0 == nRet)
		return DRV_SUCCESS;

	return nRet;
}

void CTaskBatchUpdateData2::Stop() 
{
	this->m_bExit = true;
	this->wait();
	CV_WARN(g_CVDrvierCommonLog, -1, "BatchUpdateData2 thread exit!");
}

long CTaskBatchUpdateData2::GetDataParams()
{

	return 0;
}

int CTaskBatchUpdateData2::svc()
{
	int nRet2 = ICV_SUCCESS;

	ACE_Time_Value tv_wait;
	tv_wait.set_msec(BATCH_DEQUEUE_WAITTIME);
	ACE_Message_Block* pMsg2 = NULL;
	//�����е�tag������
	//const int32 nTAG_NUM_MAX = 8192;
	//const int32 nSEND_INTERVAL_MIN_MS = 50;
	const int32 nTAG_NUM_MAX = m_nDequeueSize;
	const int32 nSEND_INTERVAL_MIN_MS = m_nDequeueTimeOut;
	int16 nOldQuality = 0;
	int16 nNewQuality = 0;
	static bool bIsfirstcall = true;

	ACE_Time_Value  tmLastSend2 = ACE_OS::gettimeofday();
	ACE_Time_Value  tmThisSend2 = ACE_OS::gettimeofday();
	ACE_Time_Value  tmSubSend2;

	while (!m_bExit)
	{
		if(!g_pfnOnBatchUpdateData)
		{
			ACE_OS::sleep(tv_wait);
			continue;
		}

		ACE_Time_Value tv_abs2 = ACE_OS::gettimeofday() + tv_wait;
		pMsg2 = NULL;

		nRet2 = g_BatchDataQueue2.dequeue(pMsg2, &tv_abs2);
		if(NULL != pMsg2)
		{
			//dequeque succeed
			TProtoIDVTQ2 vtq2;
			proto_cvdrvcomm_batch_updatedata_unpack2(pMsg2->rd_ptr(),&vtq2);
			m_vecData2.push_back(vtq2);
			nNewQuality = vtq2.m_nQuality;
			if(bIsfirstcall)
			{
				nOldQuality = nNewQuality;
				bIsfirstcall = false;
			}
			pMsg2->release();
		}
		tmThisSend2 = ACE_OS::gettimeofday();
		tmSubSend2 = tmThisSend2 - tmLastSend2;
		int32 nWaitTimeMs2 = tmSubSend2.get_msec();
		if (nWaitTimeMs2 > nSEND_INTERVAL_MIN_MS || m_vecData2.size()>= nTAG_NUM_MAX || nNewQuality != nOldQuality)  // ���ͼ������nSEND_INTERVAL_MIN_MS��size�Ƚϴ�ʱ�����������ı�ʱ����
		{		
			if(m_vecData2.size() > 0)			
			{
				CV_DEBUG(g_CVDrvierCommonLog,"nWaitTimeMs %d size %d",nWaitTimeMs2,m_vecData2.size());
				//BAD tags
				std::vector<int32> vecTagID;
				for(int i = 0; i< m_vecData2.size(); ++i)
				{
					vecTagID.push_back(m_vecData2[i].m_nTagID);
				}
				TCV_TimeStamp cvTimeStamp = (timeval)ACE_OS::gettimeofday(); // ʱ���ȡ����ʱ��
			
				nRet2 = CVDrv_SetBlockQuality(vecTagID, &cvTimeStamp, nNewQuality == nOldQuality ? nNewQuality:nOldQuality);

				nOldQuality = nNewQuality;

				m_vecData2.clear();
				//after send
				tmLastSend2 = ACE_OS::gettimeofday();
				continue;
			}
		}
	}

	return 0;
}