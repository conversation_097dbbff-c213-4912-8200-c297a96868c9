#ifndef SNAP7_AUTOGROUP_BUILDER_H
#define SNAP7_AUTOGROUP_BUILDER_H
#include "driversdk/cvdrivercommon.h"
#include "DrvDef.h"
#include <ace/OS_NS_strings.h>
#include "TypeCast.h"
#include <vector>
#include <string.h>
#include <stdlib.h>
#include <stdio.h>
#include <sstream>
#include <iostream>
#include <cctype>

#define BLOCK_TYPE_SIZE		4

typedef struct _TagInfoWrapper
{
	TagInfo tagInfo;
	int nStartByteAddr;
	int nEndByteAddr;
	int nBitOffset;
	char szBlockType[BLOCK_TYPE_SIZE];
	int nBlockNumber;
}TagInfoWrapper;

struct BlockInfo
{
	std::string strBlockType;
	int DBNo;
	int nStartByte;
	int nBockLen;
	BlockInfo()
	{
		strBlockType =="";
		DBNo = 0;
		nStartByte = 0;
		nBockLen = 0;
	}
};

typedef std::vector<TagInfo> TagInfoVector;
typedef std::vector<TagInfoWrapper> TagInfoWrapperVector;
typedef std::vector<TagGroupInfo> TagGroupInfoVector;

class CSnap7AutoGroupBuilder
{
public:
	CSnap7AutoGroupBuilder(const TagInfo *pDevTags, int nTagNum);
	virtual ~CSnap7AutoGroupBuilder(void);
	void GroupTags(TagInfoVector &vecDevTags, TagGroupInfoVector &vecTagGrpInfo, unsigned int nPDUSize,  unsigned int nCurrentConnectionNum, unsigned int nTotalConnectionNum);
protected:   

	void GroupDBTags(unsigned int nPDUSize, TagInfoVector &vecDevTags, TagGroupInfoVector &vecTagGrpInfo);
	void GroupMBTags(unsigned int nPDUSize, TagInfoVector &vecDevTags, TagGroupInfoVector &vecTagGrpInfo);
	void GroupABTags(unsigned int nPDUSize, TagInfoVector &vecDevTags, TagGroupInfoVector &vecTagGrpInfo);
	void GroupEBTags(unsigned int nPDUSize, TagInfoVector &vecDevTags, TagGroupInfoVector &vecTagGrpInfo);
	void GroupTMTags(unsigned int nPDUSize, TagInfoVector& vecDevTags, TagGroupInfoVector& vecTagGrpInfo);

	void GetTagWrapperInfo(const TagInfo &tagInfo, TagInfoWrapper &modTagInfo);
	int GetBlockTypeAndDBNo(const char *pAddr,std::string& strBlockType,std::string& strBlockNo);
	const char *GenerateGroupName();
	int ReGenerateGroupName(TagInfoVector &vecDevTags,TagGroupInfoVector &vecTagGrpInfo);
private:

	TagInfoWrapperVector m_vecDBTags;
	TagInfoWrapperVector m_vecMBTags;
	TagInfoWrapperVector m_vecABTags;
	TagInfoWrapperVector m_vecEBTags;
	TagInfoWrapperVector m_vecTMTags;

	std::map<std::string, BlockInfo > m_mapBlockInfo;

	static unsigned int s_nGrpNum;
};

#endif

