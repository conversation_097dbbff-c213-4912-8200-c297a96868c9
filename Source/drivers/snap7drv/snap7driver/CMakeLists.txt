cmake_minimum_required(VERSION 3.10)

PROJECT (snap7drv)

INCLUDE($ENV{DRDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
#Setting Source Files
SET(SRCS ${SRCS} DrvStatus.cpp snap7.cpp snap7drv.cpp snap7AutoGroupBuilder.cpp)

INCLUDE_DIRECTORIES(../../../include/snap7drv)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME snap7drv)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS drdrivercommon drcomm ACE snap7 drhdOS)

SET(SPECOUTDIR /drivers/snap7drv)
INCLUDE($ENV{DRDIR}CMakeSpecOutPath)
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)

IF(${CMAKE_SYSTEM_NAME} MATCHES HP-UX)
	ADD_CUSTOM_COMMAND(TARGET ${TARGET_NAME} 
		POST_BUILD
		COMMAND ${CMAKE_COMMAND} -E copy $ENV{DRDIR}../${EXE_DIR}/drdriver $ENV{DRDIR}../${EXE_DIR}/${SPECOUTDIR}/${TARGET_NAME}
		COMMAND ${CMAKE_COMMAND} -E copy $ENV{DRDIR}../${EXE_DIR}/libdrdriverdda.so $ENV{DRDIR}../${EXE_DIR}/${SPECOUTDIR}/libdda.so
		COMMENT "cp cvdriver and dda to driver dir" VERBATIM
		)
ENDIF(${CMAKE_SYSTEM_NAME} MATCHES HP-UX)


IF(CMAKE_SYSTEM MATCHES "SunOS.*")
	ADD_CUSTOM_COMMAND(TARGET ${TARGET_NAME} 
		POST_BUILD
		COMMAND ${CMAKE_COMMAND} -E copy $ENV{DRDIR}../${EXE_DIR}/cvdriver $ENV{DRDIR}../${EXE_DIR}/${SPECOUTDIR}/${TARGET_NAME}
		COMMENT "cp cvdriver to driver dir" VERBATIM
		)
ENDIF(CMAKE_SYSTEM MATCHES "SunOS.*")


