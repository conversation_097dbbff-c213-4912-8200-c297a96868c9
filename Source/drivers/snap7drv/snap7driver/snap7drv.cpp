﻿/************************************************************************
 *	Filename:		snap7drv.cpp
 *	Copyright:		Shanghai Baosight Software Co., Ltd.
 *
 *	Description:	$(Desp) .
 *
 *	@author:		zhangliuqing
 *	@version		08/01/2018	zhangliuqing    Initial Version
*************************************************************************/

#include "snap7drv.h"
#include "snap7AutoGroupBuilder.h"
#include "common/CVLog.h"
#include "processdb/PDBDef.h"
#include "os/os_time.h"
#include "common/DrvDef.h"
#include "common/cvdefine.h"
#include "common/RMAPIDef.h"

using namespace std;
CCVLog g_CVLogSnap7Drv;
ofstream g_snapdrvCfg;
char g_szConfigFile[ICV_SHORTFILENAME_MAXLEN];

// nPDUSIZE_RESERVED_S7_300 S7-300的pdusize一般为240，我们定义多读最大子块个数为MaxVars的一半，避免多读请求长度超出pdusize；  requset = 2 + ItemsCount * /*12*/sizeof(TReqFunReadItem));
const int nPDUSIZE_RESERVED_S7_300 = 12 + 2 + (1+1+2+1)*MaxVars/2;	// sizeof（PS7ResHeader23） + size(PResFunReadParams) + size(ReturnCode + TransportSize + DataLength + '\0') * 10 
const int nPDUSIZE_RESERVED = 12 + 2 + (1+1+2+1)*MaxVars;	// sizeof（PS7ResHeader23） + size(PResFunReadParams) + size(ReturnCode + TransportSize + DataLength + '\0') * MaxVars 
const int HCPU_MAIN = 0x10;	// 冗余PLC master CPU 活动
const int HCPU_SLAVE = 0x20;	// 冗余PLC slave  CPU 活动
const int SNAP7_DEFAULT_PDUSIZE = 480;

// Errors areas definition
const uint32 ERRTCPMASK = 0x0000FFFF;
const uint32 ERRORISOMASK = 0x000F0000;
const uint32 ERRS7MASK  = 0xFFF00000;

map<string, ConnParam*> g_mapDeviceFor200;										// s7-200型号PLC容器，key是设备名，value是设备连接参数
map<DRVHANDLE, ConnParam*> g_mapDevice;										    // s7-300型号及以上PLC容器，key是设备句柄，value是设备连接参数
map<DRVHANDLE, map<DRVHANDLE, CVDATABLOCK*> > g_mapDataBlocks;					// 数据块容器，key是设备句柄，value是数据块句柄,数据块指针键值对
map<DRVHANDLE, vector<vector<TS7DataBlockItem> >* > g_mapMultiReadDataItems;		// 多读块容器，key是设备句柄，value是二维数组指针，指针指向多读块分组，内容包括数据块句柄，是否为主块以及multiread数据块类型，起始地址，数据块长度等信息

int S7_ConnectToPLC(DRVHANDLE hDevice);
int S7_CheckMasterCPU(int res, ConnParam* Cli);
static void time2string(const TCV_TimeStamp& tcvTime, std::string& strTime);

//************************************
// Returns:   DRV_SUCCESS/DATA_STATUS_CONFIG_ERROR
// parameter: char *szConnParam [in] pDevice->pszConnParam
// parameter:char* szIP [out] 将pDevice->pszConnParam参数列表转换为ip/ip2格式
// parameter:char* szConnType [out] e.g:op,pg,tsap
// parameter:char* szLocalTsap [out] e.g:0x4d57
// parameter:char* szRemoteTsap [out] e.g:0x4d57
//************************************
long GetConnPram(char *szConnParam, char* szIP, char* szConnType, char* szLocalTsap, char* szRemoteTsap, bool& bisMultiLink)
{
	string strConnParam = szConnParam;
	strConnParam += ";"; // 补上一个分号
	int nPos = strConnParam.find(';');
	while(nPos != string::npos)
	{
		string strOneParam = strConnParam.substr(0, nPos);
		strConnParam = strConnParam.substr(nPos + 1);// 除去这一个已经解析过的参数
		nPos = strConnParam.find(';');

		if(strOneParam.empty())
			continue;

		int nPosPart = strOneParam.find('=');	// IP:**********
		if(nPosPart == string::npos)
		{
			//continue;
			CV_ERROR(g_CVLogSnap7Drv,-1,"invalid connection params[%s]",szConnParam);
			return DATA_STATUS_CONFIG_ERROR;
		}

		// 获取到某个参数名称和值
		string strParamName = strOneParam.substr(0, nPosPart); // e.g. IP
		string strParamValue = strOneParam.substr(nPosPart + 1); // e.g. **********
		//PLC ip地址解析
		if(ACE_OS::strcasecmp("ip", strParamName.c_str()) == 0)
		{
			if(strParamValue.size() < CONNPRAM_LEN/2)
				strncpy(szIP, strParamValue.c_str(), CONNPRAM_LEN/2);
			else
			{
				CV_ERROR(g_CVLogSnap7Drv,-1,"invalid ip address! %s",strParamValue.c_str());
				return DATA_STATUS_CONFIG_ERROR;
			}
		}

		else if(ACE_OS::strcasecmp("ip2", strParamName.c_str()) == 0)
		{
			char szIP2[CONNPRAM_LEN];
			strncat(szIP,"/",1);
			if(strParamValue.size() < CONNPRAM_LEN/2)
			{
				strncpy(szIP2, strParamValue.c_str(), CONNPRAM_LEN);
				strncat(szIP,szIP2,CONNPRAM_LEN/2);
			}
			else
			{
				CV_ERROR(g_CVLogSnap7Drv,-1,"invalid ip address! %s",strParamValue.c_str());
				return DATA_STATUS_CONFIG_ERROR;
			}
		}

		//PLC连接类型
		else if(ACE_OS::strcasecmp("conntype", strParamName.c_str()) == 0)
		{
			strncpy(szConnType, strParamValue.c_str(), CONNPRAM_LEN - 1);
		}
	
		else if(ACE_OS::strcasecmp("localtsap", strParamName.c_str()) == 0)
		{
			strncpy(szLocalTsap, strParamValue.c_str(), CONNPRAM_LEN - 1);
		}

		else if(ACE_OS::strcasecmp("remotetsap", strParamName.c_str()) == 0)
		{
			strncpy(szRemoteTsap, strParamValue.c_str(), CONNPRAM_LEN - 1);
		}

		//是否单连接
		else if(ACE_OS::strcasecmp("multilink", strParamName.c_str()) == 0)
		{
			bisMultiLink = atoi(strParamValue.c_str());
		}
	}

	return DRV_SUCCESS;
}

static void time2string(const TCV_TimeStamp& tcvTime, std::string& strTime)
{
	time_t temp = tcvTime.tv_sec;
	//struct tm *pTmValue = localtime((time_t*)(&tcvTime.tv_sec)); 
	// 32bit的tcvTime.tv_sec强转为time_t(32bit,64bit都可能)调用localtime时有问题，要转换一次
	struct tm *pTmValue = localtime((time_t*)(&temp));
	char szTime[128] = { '\0' };
	sprintf(szTime, "%d%d%d_%d%d%d", pTmValue->tm_year + 1900, pTmValue->tm_mon + 1,
		pTmValue->tm_mday, pTmValue->tm_hour, pTmValue->tm_min, pTmValue->tm_sec);

	strTime = szTime;
}


/**
*  获取驱动版本号.
*
*  @version   07/20/2012   Initial Version.
*/
CVDRIVER_EXPORTS long GetDrvFrameVersion()
{
	return 2;
}

CVDRIVER_EXPORTS long Begin()
{
	g_CVLogSnap7Drv.SetLogFileNameThread(SNAP7_DRIVE_NAME);
	CV_INFO(g_CVLogSnap7Drv,"driver begin..."); 

	//snap7drvcfg.txt记录数据块信息，多读分组信息
	char *szCVProjCfgPath = (char *)CVComm.GetCVEnv(); 
	if(szCVProjCfgPath)
		sprintf(g_szConfigFile, "%s%cdrivers%csnap7drv%csnap7drvcfg.txt",szCVProjCfgPath, ACE_DIRECTORY_SEPARATOR_CHAR,ACE_DIRECTORY_SEPARATOR_CHAR,ACE_DIRECTORY_SEPARATOR_CHAR);
	else 
	{
		CV_ERROR(g_CVLogSnap7Drv,-1,"get iplatda environment variables failed!");
		return -1;  
	}

	g_snapdrvCfg.open(g_szConfigFile,ios::out|ios::trunc);

	return ICV_SUCCESS;
}

// 多读数据块内Tag点位按照起始地址排序，从小到大
bool compareTagInfoInBlockByStartAddr(TagInfoInBlock a, TagInfoInBlock b)
{
	return a.nBitOffset < b.nBitOffset;

}
// 多读数据块内Tag点位按照结束地址排序，从小到大
bool compareTagInfoInBlockByEndAddr(TagInfoInBlock a, TagInfoInBlock b)
{
	return a.nBitOffset+a.nBitLen < b.nBitOffset + b.nBitLen;
}

//
bool compareTagInfoInBlockByAddr(TagInfoInBlock a, TagInfoInBlock b)
{
	if(a.nBitOffset+a.nBitLen == b.nBitOffset + b.nBitLen)
		return a.nBitOffset < b.nBitOffset;

	return a.nBitOffset+a.nBitLen < b.nBitOffset + b.nBitLen;
}

//所有多读块大小排序，从小到大
bool compareMultiBlockRead(MultiReadBlock a, MultiReadBlock b)
{
	return a.nBlockLen < b.nBlockLen;
}

//所有多读块大小排序，从小到大
bool compareMultiBlockReadByCycleRate(MultiReadBlock a, MultiReadBlock b)
{
	if(a.nCycleRate == b.nCycleRate)
		return a.nBlockLen < b.nBlockLen;
	return a.nCycleRate < b.nCycleRate;
}

//多读块总长度计算
bool compareTotalLenWithPDUSize(std::vector<MultiReadBlock >& vec, int32 PDUSize)
{
	int nTotalLen = accumulate(vec.begin(),vec.end(),0,Csum());
	return nTotalLen < PDUSize;
}

void S7_SetMultiReadBlock(DRVHANDLE h)
{
	Drv_SetUserData(h,0,1);
}

void S7_SetMultiReadMasterBlock(DRVHANDLE h)
{
	Drv_SetUserData(h,1,1);
}

void S7_SetBlockTagCurrIndex(DRVHANDLE h, int lUserData)
{
	Drv_SetUserData(h,2,lUserData);
}

int S7_GetBlockTagCurrIndex(DRVHANDLE h)
{
	int nRet = Drv_GetUserData(h,2);
	return nRet;
}

void S7_SetBlockMaxIndexFlag(DRVHANDLE h)
{
	Drv_SetUserData(h,3,1);
}

int S7_GetBlockMaxIndexFlag(DRVHANDLE h)
{
	int nRet = Drv_GetUserData(h,3);
	return nRet;
}

void S7_SetMultiReadBlockGroupInfo(DRVHANDLE h, void* pUserPtr)
{
	Drv_SetUserDataPtr(h,0,(void*)pUserPtr); // 保存当前设备下所有分组块的指针，后面会用到
}

//调节超长数据块，单读多读通用
int S7_TryToAdjustBlockLen(TS7Client* pClient, DRVHANDLE hDevice, DRVHANDLE hDatablock,TS7DataBlockItem* datablockitem = NULL)
{
	int nRet = ICV_SUCCESS;

	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
	CVDATABLOCK *pDatablock = Drv_GetDataBlockInfo(hDatablock);
	if(NULL == pClient || NULL == pDevice || NULL == pDatablock)
		return EC_ICV_DRIVER_INVALID_PARAMETER;

	const char* szBlkType = pDatablock->pszBlockType;
	int nDBNumber = atoi(pDatablock->pszParam1);
	int nStart = atoi(pDatablock->pszAddress);
	int nSize = (int)pDatablock->nBlockDataSize;
	if(NULL != datablockitem)	//对多读特殊处理
	{
		nStart = datablockitem->ts7DataItem.Start;
		nSize = datablockitem->ts7DataItem.Amount;
	}

	std::vector<TagInfoInBlock > vecTaginfoinblock;
	Drv_GetTagInfoInBlock(hDatablock,vecTaginfoinblock); // 获取数据块内所有tag点偏移信息
	if(vecTaginfoinblock.empty())
	{
		Drv_SetElemNum(hDatablock,0);
		CV_INFO(g_CVLogSnap7Drv,"Device[%s] Block[%s] There are no tags in Block!",pDevice->pszName,pDatablock->pszName);
		return -1;
	}
	std::sort(vecTaginfoinblock.begin(),vecTaginfoinblock.end(),compareTagInfoInBlockByAddr);//块内tag点偏移排序

	int nTagPos = 0,nIndexMax,nIndexCurr,nBlockLenAdj = 0;	
	if(0 == S7_GetBlockMaxIndexFlag(hDatablock)) //指定的数据块只取一次nIndexMax
	{
		nIndexMax = vecTaginfoinblock.size() - 1;// tag点索引最大值
		S7_SetBlockTagCurrIndex(hDatablock,nIndexMax);
		S7_SetBlockMaxIndexFlag(hDatablock);//设置tag点最大索引标记
		CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] Block[%s] startaddr(%d) Tag indexMax(%d).",pDevice->pszName,pDatablock->pszName,nStart,nIndexMax);
	}
	nIndexCurr = S7_GetBlockTagCurrIndex(hDatablock);//获取tag点索引
	CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] Block[%s] startaddr(%d) Tag indexCurr(%d).",pDevice->pszName,pDatablock->pszName,nStart,nIndexCurr);

	//每个扫描周期减掉数据块内最后一个tag点的长度
	nIndexCurr--;
	if(nIndexCurr < 0) //所有点都尝试过仍超长
	{
		nBlockLenAdj = 0; // 数据块长度赋0
		Drv_SetElemNum(hDatablock,nBlockLenAdj);//
		if(NULL != datablockitem)
			datablockitem->ts7DataItem.Amount = nBlockLenAdj;//多读数据块长度
		S7_SetBlockTagCurrIndex(hDatablock,nIndexCurr);//设置新的tag点索引
		return -1;
	}

	nBlockLenAdj = (vecTaginfoinblock[nIndexCurr].nBitOffset + vecTaginfoinblock[nIndexCurr].nBitLen)%BITS_PER_BYTE == 0  
				 ? (vecTaginfoinblock[nIndexCurr].nBitOffset + vecTaginfoinblock[nIndexCurr].nBitLen)/BITS_PER_BYTE 
				 : (vecTaginfoinblock[nIndexCurr].nBitOffset + vecTaginfoinblock[nIndexCurr].nBitLen)/BITS_PER_BYTE + 1;
	CV_INFO(g_CVLogSnap7Drv,"Device[%s] Block[%s] remove TagID[%d] nBitOffset[%d] nBitLen[%d].Current BlockLen[%d].",pDevice->pszName,pDatablock->pszName,
		vecTaginfoinblock[nIndexCurr+1].nTagID,vecTaginfoinblock[nIndexCurr+1].nBitOffset,vecTaginfoinblock[nIndexCurr+1].nBitLen,nBlockLenAdj);
	

	Drv_SetElemNum(hDatablock,nBlockLenAdj);//重新设置数据块长度
	if(NULL != datablockitem)
		datablockitem->ts7DataItem.Amount = nBlockLenAdj;//多读数据块长度
	S7_SetBlockTagCurrIndex(hDatablock,nIndexCurr);//设置新的tag点索引
	if(0 == ACE_OS::strcasecmp(szBlkType,"DB"))
	{
		TS7BlockInfo blockInfo;
		//GetAgBlockInfo支持S7-300/400的DB类型，S7-200/1200/1500不支持
		nRet = pClient->GetAgBlockInfo(Block_DB, nDBNumber, &blockInfo); 
		CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] Block[%s] AgBlockInfo cost %d ms!",pDevice->pszName,pDatablock->pszName , pClient->ExecTime());
		if(0 != nRet || 0 == blockInfo.MC7Size /*nRet == item not available*/)	// 不支持的型号
		{
			CV_WARN(g_CVLogSnap7Drv,nRet, "Device[%s] Block[%s] MC7Size %d",pDevice->pszName,pDatablock->pszName,blockInfo.MC7Size);
			return nRet;
		}
		
		//300/400型号直接获取DB长度
		if(nStart > blockInfo.MC7Size - 1)
		{
			Drv_SetElemNum(hDatablock,0);
			if(NULL != datablockitem)
				datablockitem->ts7DataItem.Amount = 0;
			CV_WARN(g_CVLogSnap7Drv,-1,"Device[%s]:[%s] XBNumber[%d] StartAddr[%d] > PLC maximum BlockEndByteAddr[%d],skip this datablock!",
				pDevice->pszName,pDatablock->pszName,atoi(pDatablock->pszParam1),nStart,blockInfo.MC7Size - 1);
		}
		else
		{
			//计算调整后的数据块长度
			nBlockLenAdj = (blockInfo.MC7Size -1) - nStart + 1;
			Drv_SetElemNum(hDatablock,nBlockLenAdj);
			if(NULL != datablockitem)
				datablockitem->ts7DataItem.Amount = nBlockLenAdj;
			CV_WARN(g_CVLogSnap7Drv,-1,"Device[%s]:[%s] XBNumber[%d] BlockEndByteAddr[%d] > PLC maximum BlockEndByteAddr[%d],adjust nBlockDataSize [%d] ->[%d]!",
				pDevice->pszName,pDatablock->pszName,atoi(pDatablock->pszParam1),nStart + nSize - 1,blockInfo.MC7Size - 1,nSize,nBlockLenAdj);
		}
	}

	//重新获取数据块参数
// 	pDatablock = Drv_GetDataBlockInfo(hDatablock);
// 	if(!g_snapdrvCfg.is_open())
// 		g_snapdrvCfg.open(g_szConfigFile,ios::out|ios::app);
// 	g_snapdrvCfg <<"auto Adjust..."<<endl;
// 	g_snapdrvCfg <<"device:"<< pDevice->pszName <<":"<< hDevice << " blockname:"<< pDatablock->pszName<<" blocktype:"<<pDatablock->pszBlockType<<" blocknumber:" << pDatablock->pszParam1
// 		<<" blockaddr:"<< pDatablock->pszAddress<<" blocksize:" <<pDatablock->nBlockDataSize <<" cyclerate:"<< pDatablock->nCycleRate << endl;
// 	g_snapdrvCfg.close();

	return nRet;
}

CVDRIVER_EXPORTS long Initialize(DRVHANDLE hDriver)
{
	CV_INFO(g_CVLogSnap7Drv,"driver Initialize...");
	//TODO：初始化操作
	drv_redis_init();
	drv_redis_status_d(SNAP7_DRIVE_NAME, 0);
	drv_redis_time_d(SNAP7_DRIVE_NAME, DRV_REDIS_TIME_START);

	//初始化多读分组
	std::map<DRVHANDLE, std::map<DRVHANDLE, CVDATABLOCK*> >::iterator iter = g_mapDataBlocks.begin();
	for(; iter != g_mapDataBlocks.end(); ++iter)
	{
		 //获取多读配置信息
		int32 nPDUSize = 480;
		int nMaxMultiReadBlockCnt = MaxVars;
		std::map<string, ConnParam*>::iterator iterSnap7Device200 = g_mapDeviceFor200.find(string(Drv_GetDeviceInfo(iter->first)->pszName));
		if(iterSnap7Device200 != g_mapDeviceFor200.end())
		{
			if(false == iterSnap7Device200->second->m_param.m_isMultiRead)
				continue;

			nPDUSize = iterSnap7Device200->second->m_param.m_PDUSize - nPDUSIZE_RESERVED_S7_300;
			nMaxMultiReadBlockCnt = MaxVars/2;

			CV_INFO(g_CVLogSnap7Drv,"Device[%s] PDUSize 240",Drv_GetDeviceInfo(iter->first)->pszName);
		}
		else
		{
			std::map<DRVHANDLE, ConnParam*>::iterator iterSnap7Device = g_mapDevice.find(iter->first);
			if(iterSnap7Device != g_mapDevice.end())
			{
				if(false == iterSnap7Device->second->m_param.m_isMultiRead)
					continue;
				if(iterSnap7Device->second->m_param.m_PDUSize < 480){
					nPDUSize = iterSnap7Device->second->m_param.m_PDUSize - nPDUSIZE_RESERVED_S7_300;
					nMaxMultiReadBlockCnt = MaxVars/2;
					CV_INFO(g_CVLogSnap7Drv,"Device[%s] PDUSize 240",Drv_GetDeviceInfo(iter->first)->pszName);
				}
				else{
					nPDUSize = iterSnap7Device->second->m_param.m_PDUSize - nPDUSIZE_RESERVED;	
					CV_INFO(g_CVLogSnap7Drv,"Device[%s] PDUSize 480",Drv_GetDeviceInfo(iter->first)->pszName);
				}
			}
			
		}

		if(nPDUSize <= 0)
		{
			CV_ERROR(g_CVLogSnap7Drv,nPDUSize,"Devicve[%s] MultiRead AutoGroup failed.INVALID PDUSIZE (%d)",Drv_GetDeviceInfo(iter->first)->pszName,nPDUSize);
			continue;
		}
			
		//多读块长度优化
		std::vector<MultiReadBlock > vecMultiReadBlock;
		std::map<DRVHANDLE, CVDATABLOCK* >::iterator it = iter->second.begin();
		for(; it != iter->second.end(); ++it)
		{
			std::vector<TagInfoInBlock > vecTaginfoinblock;
			Drv_GetTagInfoInBlock(it->first,vecTaginfoinblock);	// 获取数据块内所有tag点偏移信息
			if(vecTaginfoinblock.empty())
			{
				CV_INFO(g_CVLogSnap7Drv,"Device[%s] Block[%s] There are no tags in Block!",Drv_GetDeviceInfo(iter->first)->pszName,it->second->pszName);
				continue;
			}
 			std::sort(vecTaginfoinblock.begin(),vecTaginfoinblock.end(),compareTagInfoInBlockByStartAddr); // 块内点偏移排序，从小到大
 			TagInfoInBlock tagStartAddrMin = vecTaginfoinblock.front();
 			std::sort(vecTaginfoinblock.begin(),vecTaginfoinblock.end(),compareTagInfoInBlockByEndAddr); // 块内点偏移排序，从小到大
 			TagInfoInBlock tagEndAddrMax = vecTaginfoinblock.back();
 			MultiReadBlock multireadblock;
 			multireadblock.nStart = atoi(it->second->pszAddress) + tagStartAddrMin.nBitOffset/BITS_PER_BYTE;
 			int nLastTagPos = (tagEndAddrMax.nBitOffset + tagEndAddrMax.nBitLen)%BITS_PER_BYTE == 0 ? (tagEndAddrMax.nBitOffset + tagEndAddrMax.nBitLen)/BITS_PER_BYTE : (tagEndAddrMax.nBitOffset + tagEndAddrMax.nBitLen)/BITS_PER_BYTE + 1;
 			multireadblock.nBlockLen = nLastTagPos - tagStartAddrMin.nBitOffset/BITS_PER_BYTE;	// 根据块内点的分布情况优化数据块长度
			/*CV_INFO(g_CVLogSnap7Drv,"Device[%s] mulitblock name[%s] blocklen[%d] PDUSize[%d]",Drv_GetDeviceInfo(iter->first)->pszName,it->second->pszName,multireadblock.nBlockLen,nPDUSize + nPDUSIZE_RESERVED);*/
 			if(multireadblock.nBlockLen >= nPDUSize) 
			{ 
				//todo:是否根据tag点偏移修正数据块长度

				CV_INFO(g_CVLogSnap7Drv,"Device[%s] mulitblock name[%s] blocklen[%d] exceed Length of Data in PDUSize[%d],this Block will use SingleRead.",Drv_GetDeviceInfo(iter->first)->pszName,it->second->pszName,multireadblock.nBlockLen,nPDUSize);
 				continue; //  数据块长度超出pdusize不计入多读块
			}
			if(multireadblock.nBlockLen > it->second->nBlockDataSize) 
			{
				CV_INFO(g_CVLogSnap7Drv,"Device[%s] mulitblock name[%s] blocklen[%d] exceed nBlockDataSize[%d],this Block will use SingleRead.",Drv_GetDeviceInfo(iter->first)->pszName,it->second->pszName,multireadblock.nBlockLen,it->second->nBlockDataSize);
				continue; //  数据块长度超出pdusize不计入多读块
			}

 			multireadblock.nBlockNumber = atoi(it->second->pszParam1);
 			multireadblock.BlockType = std::string(it->second->pszBlockType);
 			multireadblock.hBlock = it->first;
			multireadblock.nCycleRate = it->second->nCycleRate;
 			vecMultiReadBlock.push_back(multireadblock);
			CV_INFO(g_CVLogSnap7Drv, "Device[%s] mulitblock name[%s] blocktype[%s] start addr[%d] blocklen[%d] blockno[%d] tag num[%d] length of data in PDUSize[%d]",
				Drv_GetDeviceInfo(iter->first)->pszName,it->second->pszName, it->second->pszBlockType, multireadblock.nStart, multireadblock.nBlockLen, 
				multireadblock.nBlockNumber, vecTaginfoinblock.size(),nPDUSize);	
		}

		// 所有多读块按扫描周期排序
		std::sort(vecMultiReadBlock.begin(),vecMultiReadBlock.end(),compareMultiBlockReadByCycleRate);
		// 根据扫描周期，排满10/20个数据块或者达到pdusize上限来分组多读块
		std::vector<TS7DataBlockItem> vecTS7DataItem;
		std::vector<vector<TS7DataBlockItem> >* vVecTS7DataItem = new std::vector<vector<TS7DataBlockItem> >; // Drv_SetUserDataPtr
		int nRemainingSize = nPDUSize;
		int nTotalCount = vecMultiReadBlock.size();
		int nMaxItemCnt = 0;
		int nTotalBlockSize = 0;
		for(int i = 0; i < nTotalCount; ++i)
		{
			nTotalBlockSize += vecMultiReadBlock[i].nBlockLen;
			TS7DataBlockItem dataitem;
 			memset(&dataitem,0,sizeof(dataitem));
			byte* pBlockBuffer = new byte[vecMultiReadBlock[i].nBlockLen];// 多读块buffer
			memset(pBlockBuffer,0,vecMultiReadBlock[i].nBlockLen * sizeof(byte));

			//TODO:Encapsulate this code into methods
			if(vecMultiReadBlock[i].BlockType == "DB")
			{
				dataitem.ts7DataItem.Area = S7AreaDB;
				dataitem.ts7DataItem.DBNumber = vecMultiReadBlock[i].nBlockNumber;
			}
			else if(vecMultiReadBlock[i].BlockType == "AB")
			{
				dataitem.ts7DataItem.Area = S7AreaPA;
				dataitem.ts7DataItem.DBNumber = 0;
			}
			else if(vecMultiReadBlock[i].BlockType == "EB")
			{
				dataitem.ts7DataItem.Area = S7AreaPE;
				dataitem.ts7DataItem.DBNumber = 0;
			}
			else if(vecMultiReadBlock[i].BlockType == "MB")
			{
				dataitem.ts7DataItem.Area = S7AreaMK;
				dataitem.ts7DataItem.DBNumber = 0;
			}
			else if(vecMultiReadBlock[i].BlockType == "TM")
			{
				dataitem.ts7DataItem.Area = S7AreaTM;
				dataitem.ts7DataItem.DBNumber = 0;
			}
			else if(vecMultiReadBlock[i].BlockType == "CT")
			{
				dataitem.ts7DataItem.Area = S7AreaCT;
				dataitem.ts7DataItem.DBNumber = 0;
			}
			dataitem.ts7DataItem.Start = vecMultiReadBlock[i].nStart;
			dataitem.ts7DataItem.Amount = vecMultiReadBlock[i].nBlockLen;
			dataitem.ts7DataItem.WordLen = S7WLByte;
			dataitem.ts7DataItem.pdata = pBlockBuffer;
			dataitem.hBlock = vecMultiReadBlock[i].hBlock;
			dataitem.nCycleRate = vecMultiReadBlock[i].nCycleRate;
			S7_SetMultiReadBlock(dataitem.hBlock);// 标记是否为多读块
			nMaxItemCnt++;

			if(i+1 < nTotalCount ) // 最后一个块之前的块判断是否为相同扫描周期
			{
				if(vecMultiReadBlock[i].nCycleRate == vecMultiReadBlock[i+1].nCycleRate)
				{
					if(nRemainingSize < dataitem.ts7DataItem.Amount) // 当前分组内数据块总长度已满
					{
						vecTS7DataItem.back().isMasterBlock = true;
						S7_SetMultiReadMasterBlock(vecTS7DataItem.back().hBlock);// 当前分组内最后一个子块标记为主块
						vVecTS7DataItem->push_back(vecTS7DataItem);
						vecTS7DataItem.clear();
						nMaxItemCnt = 0;
						vecTS7DataItem.push_back(dataitem);	// 当前块放入下一组多读
						nRemainingSize = nPDUSize - vecTS7DataItem.back().ts7DataItem.Amount; // 重新计算总长度
					}
					else if(nMaxItemCnt >= nMaxMultiReadBlockCnt) // 当前分组快数目达到上限10/20个
					{
						dataitem.isMasterBlock = true;
						S7_SetMultiReadMasterBlock(dataitem.hBlock);// 当前分组内最后一个子块标记为主块
						vecTS7DataItem.push_back(dataitem);		
						vVecTS7DataItem->push_back(vecTS7DataItem);
						vecTS7DataItem.clear();
						nMaxItemCnt = 0;
						nRemainingSize = nPDUSize;	// 重新计算总长度
					}
					else
					{
						vecTS7DataItem.push_back(dataitem);	// 当前分组内添加这个块
						nRemainingSize = nRemainingSize - dataitem.ts7DataItem.Amount; // 总长度减去当前块长度
					}
				}
				else // 检测到其他扫描周期
				{
					if(nRemainingSize < dataitem.ts7DataItem.Amount) // 当前分组内数据块总长度已满
					{
						if(!vecTS7DataItem.empty())
						{
							vecTS7DataItem.back().isMasterBlock = true;
							S7_SetMultiReadMasterBlock(vecTS7DataItem.back().hBlock);// 当前分组内最后一个子块标记为主块
							vVecTS7DataItem->push_back(vecTS7DataItem);
							vecTS7DataItem.clear();
						}

						vecTS7DataItem.push_back(dataitem);	// 当前块放入下一组多读
						dataitem.isMasterBlock = true;
						S7_SetMultiReadMasterBlock(vecTS7DataItem.back().hBlock);// 当前分组内最后一个子块标记为主块
						vVecTS7DataItem->push_back(vecTS7DataItem);
						vecTS7DataItem.clear();
						nMaxItemCnt = 0;
						nRemainingSize = nPDUSize; // 重新计算总长度

					}
					else if(nMaxItemCnt >= nMaxMultiReadBlockCnt) // 当前分组快数目达到上限20个
					{
						dataitem.isMasterBlock = true;
						S7_SetMultiReadMasterBlock(dataitem.hBlock);// 当前分组内最后一个子块标记为主块
						vecTS7DataItem.push_back(dataitem);		
						vVecTS7DataItem->push_back(vecTS7DataItem);
						vecTS7DataItem.clear();
						nMaxItemCnt = 0;
						nRemainingSize = nPDUSize;	// 重新计算总长度
					}
					else
					{
						dataitem.isMasterBlock = true;
						S7_SetMultiReadMasterBlock(dataitem.hBlock);// 当前分组内最后一个子块标记为主块
						vecTS7DataItem.push_back(dataitem);	// 当前分组内添加这个块						
						vVecTS7DataItem->push_back(vecTS7DataItem);
						vecTS7DataItem.clear();
						nMaxItemCnt = 0;
						nRemainingSize = nPDUSize; //  重新计算总长度
					}
				}
			}
			else // 最后一个块
			{
				if(nTotalCount >= 2 && vecMultiReadBlock[i-1].nCycleRate == vecMultiReadBlock[i].nCycleRate)
				{
					if(nRemainingSize < dataitem.ts7DataItem.Amount) // 当前分组内数据块总长度已满
					{
						vecTS7DataItem.back().isMasterBlock = true;
						S7_SetMultiReadMasterBlock(vecTS7DataItem.back().hBlock);// 当前分组内最后一个子块标记为主块
						vVecTS7DataItem->push_back(vecTS7DataItem);
						vecTS7DataItem.clear();
						nMaxItemCnt = 0;
						vecTS7DataItem.push_back(dataitem);	// 当前块放入下一组多读
						nRemainingSize = nPDUSize;	// 重新计算总长度
					}
					else if(nMaxItemCnt >= nMaxMultiReadBlockCnt) // 当前分组快数目达到上限20个
					{
						dataitem.isMasterBlock = true;
						S7_SetMultiReadMasterBlock(dataitem.hBlock);// 当前分组最后一个子块标记为主块
						vecTS7DataItem.push_back(dataitem);		
						vVecTS7DataItem->push_back(vecTS7DataItem);
						vecTS7DataItem.clear();
						nMaxItemCnt = 0;
						nRemainingSize = nPDUSize;	// 重新计算总长度
					}
					else
					{
						vecTS7DataItem.push_back(dataitem);	// 当前分组内添加这个块
						nMaxItemCnt = 0;
						nRemainingSize = nPDUSize;	// 重新计算总长度
					}
				}
				else if(nTotalCount >= 2 && vecMultiReadBlock[i-1].nCycleRate != vecMultiReadBlock[i].nCycleRate)
				{

					if(!vecTS7DataItem.empty())
					{
						vecTS7DataItem.back().isMasterBlock = true;
						S7_SetMultiReadMasterBlock(vecTS7DataItem.back().hBlock);// 当前分组内最后一个子块标记为主块
						vVecTS7DataItem->push_back(vecTS7DataItem);
						vecTS7DataItem.clear();
					}

					vecTS7DataItem.push_back(dataitem);	// 当前块放入下一组多读
					dataitem.isMasterBlock = true;
					S7_SetMultiReadMasterBlock(vecTS7DataItem.back().hBlock);// 当前分组内最后一个子块标记为主块
					vVecTS7DataItem->push_back(vecTS7DataItem);
					vecTS7DataItem.clear();
					nMaxItemCnt = 0;				
					nRemainingSize = nPDUSize; // 重新计算总长度

				}
				else if(nTotalCount < 2)
				{
					vecTS7DataItem.push_back(dataitem);	// 当前分组内添加这个块
					nRemainingSize = nPDUSize;	// 重新计算总长度
				}
			}
		}

		if(!vecTS7DataItem.empty())	// 分组结束
		{
			vecTS7DataItem.back().isMasterBlock = true;
			vVecTS7DataItem->push_back(vecTS7DataItem);
			S7_SetMultiReadMasterBlock(vecTS7DataItem.back().hBlock);// 当前分组最后一个子块标记为主块
		}
	
		nMaxItemCnt = 0;
		S7_SetMultiReadBlockGroupInfo(iter->first,(void*)vVecTS7DataItem); // 保存当前设备下所有分组块的指针，后面会用到
		g_mapMultiReadDataItems.insert(make_pair(iter->first,vVecTS7DataItem)); //  使用Drv_SetUserDataPtr记录分组信息后这个map用处不大了
		CV_INFO(g_CVLogSnap7Drv,"Drv_SetUserDataPtr Device[%s] index[0] pUserPtr[0x%x]",Drv_GetDeviceInfo(iter->first)->pszName,(void*)vVecTS7DataItem);
		

	}

	//log记录多读分组情况
	map<DRVHANDLE, vector<vector<TS7DataBlockItem> >* >::iterator iterMultiReadDataItems = g_mapMultiReadDataItems.begin();
	for(;iterMultiReadDataItems != g_mapMultiReadDataItems.end(); ++iterMultiReadDataItems)
	{
		vector<vector<TS7DataBlockItem> >::iterator itervVecMultiReadDataItems = iterMultiReadDataItems->second->begin();
		for(;itervVecMultiReadDataItems != iterMultiReadDataItems->second->end(); ++ itervVecMultiReadDataItems)
		{
			std::string strMsg;
			vector<TS7DataBlockItem>::iterator itervecMultiReadDataItems = itervVecMultiReadDataItems->begin();
			for(;itervecMultiReadDataItems != itervVecMultiReadDataItems->end(); ++itervecMultiReadDataItems)
				strMsg = strMsg + " " + string(Drv_GetDataBlockInfo(itervecMultiReadDataItems->hBlock)->pszName);
			int nTotalLen = accumulate(itervVecMultiReadDataItems->begin(),itervVecMultiReadDataItems->end(),0,Csum1());
			CV_INFO(g_CVLogSnap7Drv,"Device[%s] MultiRead AutoGroup:%s totalLen %d cyclerate %d",Drv_GetDeviceInfo(iterMultiReadDataItems->first)->pszName,strMsg.c_str(),nTotalLen,itervVecMultiReadDataItems->back().nCycleRate);

			if(g_snapdrvCfg.is_open())
			{
				g_snapdrvCfg <<"device:"<< Drv_GetDeviceInfo(iterMultiReadDataItems->first)->pszName <<" multiread autogroup: " <<strMsg <<" totallen:"<<nTotalLen <<" cyclerate:"<< itervVecMultiReadDataItems->back().nCycleRate <<
					"\tmaster block:"<<string(Drv_GetDataBlockInfo(itervVecMultiReadDataItems->back().hBlock)->pszName) << endl;
			}
		}	
	}
	g_snapdrvCfg.close();

 	return DRV_SUCCESS;
}

/**
 *  驱动EXE退出时该函数被调用.
 *  在该函数中可以释放自定义资源、断开设备连接等操作.
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012    Initial Version.
 */
CVDRIVER_EXPORTS long UnInitialize()
{
	//TODO：退出清理操作
	//释放所有的设备	
	for (map<string, ConnParam*>::iterator iterDevice200 = g_mapDeviceFor200.begin();
		iterDevice200 != g_mapDeviceFor200.end();
		iterDevice200++)
	{
		{
			ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
			iterDevice200->second->Client->Disconnect();
			CV_WARN(g_CVLogSnap7Drv,0,"Device %s Disconnect!",iterDevice200->second->m_deviceName.c_str());
			delete iterDevice200->second;
		}
	}
	g_mapDeviceFor200.clear();

	for (map<DRVHANDLE, ConnParam*>::iterator iterDevice = g_mapDevice.begin();
		iterDevice != g_mapDevice.end();
		iterDevice++)
	{
		{
			ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
			iterDevice->second->Client->Disconnect();
			CV_WARN(g_CVLogSnap7Drv,0,"Device %s Disconnect!",iterDevice->second->m_deviceName.c_str());
			delete iterDevice->second;
		}
	}
	g_mapDevice.clear();

	//释放多读块内存
	for(map<DRVHANDLE, vector<vector<TS7DataBlockItem> >* >::iterator iterMultiReadDataItems = g_mapMultiReadDataItems.begin();
	 iterMultiReadDataItems != g_mapMultiReadDataItems.end(); ++iterMultiReadDataItems)
	{
		vector<vector<TS7DataBlockItem> >::iterator itervVecMultiReadDataItems = iterMultiReadDataItems->second->begin();
		for(;itervVecMultiReadDataItems != iterMultiReadDataItems->second->end(); ++ itervVecMultiReadDataItems)
		{
			vector<TS7DataBlockItem>::iterator itervecMultiReadDataItems = itervVecMultiReadDataItems->begin();
			for(;itervecMultiReadDataItems != itervVecMultiReadDataItems->end(); ++itervecMultiReadDataItems)
			{
				SAFE_DELETE_ARRAY(itervecMultiReadDataItems->ts7DataItem.pdata);		// 释放多读块buffer
			}
		}
		SAFE_DELETE(iterMultiReadDataItems->second);		// 释放vector<vector<TS7DataBlockItem> >*
	}
	g_mapMultiReadDataItems.clear();

	drv_redis_status_d(SNAP7_DRIVE_NAME, 0);
	drv_redis_time_d(SNAP7_DRIVE_NAME, DRV_REDIS_TIME_END);
	drv_redis_uninit();

    g_CVLogSnap7Drv.StopLogThread();
	return DRV_SUCCESS;
}


/**
 *  定时读取数据函数.
 *  这类设备通常提供读取指定数据块的信息协议，该函数主要实现如下功能：
 *  1、数据的发送和接收
 *  2、更新数据块实时数据的值、数据状态和时间戳
 *  3、对于非tcp通信协议，通常需要在该接口检查连接状态
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *  @param  -[in]  DRVHANDLE hDataBlock: [数据块句柄]
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.
 */
//CVDRIVER_EXPORTS long OnReadData(DRVHANDLE hDevice, DRVHANDLE hDatablock)

// Get BDC and convert to byte
int16 S7_BDCToByte (byte B)
{
  return (int16)(((B >> 4) * 10 ) + (B & 0x0F));
}
// Convert Byte to BDC
byte S7_ByteToBCD (byte Value)
{
	return (byte) ((( Value /10 ) << 4) | (Value % 10));
}

int16 SwapWord(int16 Value)
{
	return  ((Value >> 8) & 0xFF) | ((Value << 8) & 0xFF00);
}

int32 SwapDWord(int32 Value)
{
	return ((Value >> 24)& 0x000000FF) | ((Value << 8) & 0x00FF0000) | ((Value >> 8) & 0x0000FF00) | ((Value << 24)& 0xFF000000);
}

int16 EncodeBuffer(int32 nData)
{
	int32 nRetData;
	byte nTimeBase = 0;
	int16 nTimeBaseMS = 0;
	int16 nEncodeData;
	byte nHigh,high;
	byte nLow,low;
	int16 sret;

	nRetData = SwapDWord(nData);

	if(nRetData / 10 <= 999)
	{
		//时基为10毫秒
		nTimeBaseMS = 10;
		nTimeBase = 0;
	}
	else if(nRetData / 100 <= 999)
	{
		//时基为100毫秒
		nTimeBaseMS = 100;
		nTimeBase = 1;
	}
	else if(nRetData / 1000 <= 999)
	{
		//时基为1000毫秒
		nTimeBaseMS = 1000;
		nTimeBase = 2;
	}
	else if(nRetData / 10000 <= 999)
	{
		//时基为10000毫秒
		nTimeBaseMS = 10000;
		nTimeBase = 3;
	}
	else
	{
		return 0;
	}
	nEncodeData = (int16)(nRetData / nTimeBaseMS);

	nHigh = (byte)(nEncodeData/100);
	nLow= (byte)(nEncodeData%100);

	high = S7_ByteToBCD(nHigh);
	low = S7_ByteToBCD(nLow);

	int16 nTimeBase16 = (int16)nTimeBase;
	int16 nHigh16 = (int16)high;
	int16 nLow16 = (int16)low;

	sret = ((nTimeBase16<<4)&0x00F0) + (nHigh16&0x000F);

	sret = ((sret&0x00FF)<<8)&0xFF00;

	sret = sret + (nLow16&0x00FF);
	//转换为21格式
	sret = ((sret >> 8) & 0x00FF) | ((sret << 8) & 0xFF00);
	return sret;
}

int32 transferBuffer(int16 nData)
{
	nData = ((nData >> 8) & 0x00FF) | ((nData << 8) & 0xFF00);
	byte nTimeBase = (byte)((nData&0x3000)>>12);
	byte high = (byte)((nData&0x0F00)>>8);
	int16 nHigh, nLow;
	int32 nRetData;
	byte low = (byte)((nData&0x00FF));
	int16 nTimeBaseMS = 0;
	if(nTimeBase == 0)
	{
		nTimeBaseMS = 10;
	}
	else if(nTimeBase == 1)
	{
		nTimeBaseMS = 100;
	}
	else if(nTimeBase == 2)
	{
		nTimeBaseMS = 1000;
	}
	else if(nTimeBase == 3)
	{
		nTimeBaseMS = 10000;
	}
	nHigh = S7_BDCToByte(high);
	nLow = S7_BDCToByte(low);
	nRetData = (((int32)(nHigh))*100 + nLow)*nTimeBaseMS;
	nRetData = SwapDWord(nRetData);
	return nRetData;
}

int S7_SyncRead(DRVHANDLE hDatablock,std::map<string, ConnParam*>::iterator it,byte* Buffer)
{
	CVDATABLOCK *pDatablock = Drv_GetDataBlockInfo(hDatablock);

	const char* szBlkType = pDatablock->pszBlockType;
	int nDBNumber = atoi(pDatablock->pszParam1);
	int nStart = atoi(pDatablock->pszAddress);
	int nSize = (int)pDatablock->nBlockDataSize;
	if(nSize <= 0)
	{
		CV_DEBUG(g_CVLogSnap7Drv,"Device:[%s] Block[%s] blocksize = 0.",it->second->m_deviceName.c_str(),pDatablock->pszName);
		return ICV_SUCCESS;
	}

	int ret = 0;
	if(ACE_OS::strcasecmp(szBlkType,"DB")==0||ACE_OS::strcasecmp(szBlkType,"")==0)
		ret = it->second->Client->DBRead(nDBNumber,nStart,nSize,Buffer); 		
	else if(ACE_OS::strcasecmp(szBlkType,"AB")==0)
		ret = it->second->Client->ABRead(nStart,nSize,Buffer); 
	else if(ACE_OS::strcasecmp(szBlkType,"EB")==0)
		ret = it->second->Client->EBRead(nStart,nSize,Buffer); 
	else if(ACE_OS::strcasecmp(szBlkType,"MB")==0)
		ret = it->second->Client->MBRead(nStart,nSize,Buffer); 
	else if(ACE_OS::strcasecmp(szBlkType,"TM")==0)
		ret = it->second->Client->TMRead(nStart,nSize,Buffer); 
	else if(ACE_OS::strcasecmp(szBlkType,"CT")==0)
		ret = it->second->Client->CTRead(nStart,nSize,Buffer); 
	else 
		ret = -1;

	int nCostTime = it->second->Client->ExecTime();
	CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] XBRead datablock[%s] DBnumber[%d], cost %d ms!",it->second->m_deviceName.c_str(),pDatablock->pszName, nDBNumber,nCostTime);

	return ret;
}

int S7_SyncRead(DRVHANDLE hDatablock,std::map<DRVHANDLE, ConnParam*>::iterator it,byte* Buffer)
{
	CVDATABLOCK *pDatablock = Drv_GetDataBlockInfo(hDatablock);
	const char* szBlkType = pDatablock->pszBlockType;
	int nDBNumber = atoi(pDatablock->pszParam1);
	int nStart = atoi(pDatablock->pszAddress);
	int nSize = (int)pDatablock->nBlockDataSize;

	if(nSize <= 0)
	{
		CV_DEBUG(g_CVLogSnap7Drv,"Device:[%s] Block[%s] blocksize = 0.",it->second->m_deviceName.c_str(),pDatablock->pszName);
		return ICV_SUCCESS;
	}

	int ret = 0;
	if(ACE_OS::strcasecmp(szBlkType,"DB")==0||ACE_OS::strcasecmp(szBlkType,"")==0)
		ret = it->second->Client->DBRead(nDBNumber,nStart,nSize,Buffer); 		
	else if(ACE_OS::strcasecmp(szBlkType,"AB")==0)
		ret = it->second->Client->ABRead(nStart,nSize,Buffer); 
	else if(ACE_OS::strcasecmp(szBlkType,"EB")==0)
		ret = it->second->Client->EBRead(nStart,nSize,Buffer); 
	else if(ACE_OS::strcasecmp(szBlkType,"MB")==0)
		ret = it->second->Client->MBRead(nStart,nSize,Buffer); 
	else if(ACE_OS::strcasecmp(szBlkType,"TM")==0)
		ret = it->second->Client->TMRead(nStart,nSize,Buffer); 
	else if(ACE_OS::strcasecmp(szBlkType,"CT")==0)
		ret = it->second->Client->CTRead(nStart,nSize,Buffer); 
	else 
		ret = -1;

	int nCostTime = it->second->Client->ExecTime();
	CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] XBRead datablock[%s] DBnumber[%d], cost %d ms!",it->second->m_deviceName.c_str(),pDatablock->pszName, nDBNumber,nCostTime);

	return ret;
}

//多读不支持DB块内TM类型 200多读
int S7_SyncMultiRead(DRVHANDLE hDevice,std::map<std::string, ConnParam*>::iterator it,std::vector<TS7DataBlockItem>& vecdataBlockItem)
{
	long lRet = 0;
	std::string strMsg;
	std::vector<TS7DataItem > vecDataItem;
	int nTotolBlockLen = 0;
	for(int i =0; i < vecdataBlockItem.size(); ++i)
	{
		nTotolBlockLen += vecdataBlockItem[i].ts7DataItem.Amount;
		TS7DataItem items;
		memset(&items,0,sizeof(items));	
		items.Area = vecdataBlockItem[i].ts7DataItem.Area;
		items.DBNumber = vecdataBlockItem[i].ts7DataItem.DBNumber;
		items.Start = vecdataBlockItem[i].ts7DataItem.Start;
		items.WordLen = vecdataBlockItem[i].ts7DataItem.WordLen;
		items.Amount = vecdataBlockItem[i].ts7DataItem.Amount;
		items.pdata = vecdataBlockItem[i].ts7DataItem.pdata;

		vecDataItem.push_back(items);	// 获取多读块数组
		strMsg = strMsg + " " + string(Drv_GetDataBlockInfo(vecdataBlockItem[i].hBlock)->pszName);
	}

	int ret = it->second->Client->ReadMultiVars(&vecDataItem[0],vecDataItem.size());	// 多重读接口
	int nCostTime = it->second->Client->ExecTime();
	CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] MultiXBRead Group[0x%x]:%s,cost %d ms.result[%s].TotalBlockLen %d pdusize %d!",
		it->second->m_deviceName.c_str(), vecdataBlockItem.back().hBlock, strMsg.c_str(), nCostTime, CliErrorText(ret).c_str(),nTotolBlockLen, it->second->m_param.m_PDUSize);

	if(ICV_SUCCESS != ret)
	{
		CV_ERROR(g_CVLogSnap7Drv,ret,"Device[%s] MultiXBRead Group[0x%x]:%s read failed[%s].",it->second->m_deviceName.c_str(),
			vecdataBlockItem.back().hBlock,strMsg.c_str(),CliErrorText(ret).c_str());

		for(int k = 0; k < vecDataItem.size(); ++k) // 更新多读分组内所有数据块数据质量		
			Drv_UpdateBlockStatus(hDevice, vecdataBlockItem[k].hBlock, DATA_STATUS_COMM_FAILURE);		

		int nblockCount = Drv_GetDataBlockCount(hDevice);
		nblockCount = 1;
		it->second->m_nPlcReadBlockFailedCnt += vecdataBlockItem.size();
		if(it->second->m_nPlcReadBlockFailedCnt < nblockCount)
		{
			CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] CheckDisconnectCnt ReadBlockFailedCnt/totalBlockCnt(%d/%d)",it->second->m_deviceName.c_str(),it->second->m_nPlcReadBlockFailedCnt,nblockCount);
			return DRV_SUCCESS;
		}

		it->second->m_nPlcReadBlockFailedCnt = 0;		
		it->second->Client->Disconnect();
		Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
		ret = it->second->Client->Connect(); // try reconncet
		//it->second->m_currPlcStatus = it->second->Client->PlcStatus();
		it->second->m_currPlcStatus = 0x08;
		CV_WARN(g_CVLogSnap7Drv,0,"Device[%s] MultiRead reconnect(%s). plcstatus(%d)",it->second->m_deviceName.c_str(),CliErrorText(ret).c_str(),it->second->m_currPlcStatus);
		return DRV_SUCCESS;
	}

	for(int j = 0; j < vecDataItem.size(); ++j) // 更新多读分组内所有数据块
	{
		if(vecDataItem[j].Amount <= 0)
		{
			CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] MultiRead datablock[%s] XBNumber[%d] BlockLen[0],skip this datablock.",it->second->m_deviceName.c_str(),Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszName,atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszParam1));
			continue;
		}

		if(ICV_SUCCESS == vecDataItem[j].Result) 
		{
			it->second->m_nPlcReadBlockFailedCnt = 0;
			Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
 			int nStart = vecdataBlockItem[j].ts7DataItem.Start - atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszAddress);
			lRet = Drv_UpdateBlockData(hDevice,vecdataBlockItem[j].hBlock,(const char*)vecDataItem[j].pdata,nStart,vecDataItem[j].Amount,DATA_STATUS_OK,NULL); // 更新数据块

			CV_DEBUG(g_CVLogSnap7Drv, "Device[%s] MultiRead  datablock[%s] XBNumber[%d] start 0 amount %d updateblock lRet %d ",
				it->second->m_deviceName.c_str(), Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszName,atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszParam1), vecDataItem[j].Amount, lRet);
		}
		else if(errCliAddressOutOfRange == vecDataItem[j].Result)
		{
			Drv_UpdateBlockStatusEx(hDevice, vecdataBlockItem[j].hBlock,IPLAT_QUALITY_BAD,DATA_STATUS_COMM_FAILURE,IPLAT_LIMIT_NOT_LIMITED,IPLAT_CV_LV_IOADDRESS_DATALENGTH_TOOMUCH);
			CV_ERROR(g_CVLogSnap7Drv,vecDataItem[j].Result,"Device[%s] MultiRead datablock[%s] XBNumber[%d] failed[%s]. BlockLen %d nTotolBlockLen %d",it->second->m_deviceName.c_str()
				,Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszName,atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszParam1),CliErrorText(vecDataItem[j].Result).c_str(), vecDataItem[j].Amount,nTotolBlockLen);
			//调节数据块长度，使之不再报超长错误
			S7_TryToAdjustBlockLen(it->second->Client,hDevice,vecdataBlockItem[j].hBlock,&vecdataBlockItem[j]);
		}
		else if(errCliItemNotAvailable == vecDataItem[j].Result)
		{
			Drv_UpdateBlockStatusEx(hDevice, vecdataBlockItem[j].hBlock, IPLAT_QUALITY_BAD,DATA_STATUS_COMM_FAILURE,IPLAT_LIMIT_NOT_LIMITED,IPLAT_CV_LV_DATABLOCK_NOTFOUND);
			CV_ERROR(g_CVLogSnap7Drv,vecDataItem[j].Result,"Device[%s] MultiRead datablock[%s] XBNumber[%d] failed[%s]. BlockLen %d nTotolBlockLen %d",it->second->m_deviceName.c_str()
				,Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszName,atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszParam1),CliErrorText(vecDataItem[j].Result).c_str(), vecDataItem[j].Amount,nTotolBlockLen);
		}
		else if(errCliInvalidParams == vecDataItem[j].Result)
		{
			Drv_UpdateBlockStatus(hDevice, vecdataBlockItem[j].hBlock, DATA_STATUS_CONFIG_ERROR);
			CV_ERROR(g_CVLogSnap7Drv,vecDataItem[j].Result,"Device[%s] MultiRead datablock[%s] BlockType[%s] XBNumber[%d] failed[%s]. BlockLen %d nTotolBlockLen %d",it->second->m_deviceName.c_str()
				,Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszName,Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszBlockType,atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszParam1),CliErrorText(vecDataItem[j].Result).c_str(), vecDataItem[j].Amount,nTotolBlockLen);
		}
		else
		{
			Drv_UpdateBlockStatus(hDevice, vecdataBlockItem[j].hBlock, DATA_STATUS_COMM_FAILURE);
			CV_ERROR(g_CVLogSnap7Drv,vecDataItem[j].Result,"Device[%s] MultiRead datablock[%s] XBNumber[%d] failed[%s]. BlockLen %d nTotolBlockLen %d",it->second->m_deviceName.c_str()
				,Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszName,atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszParam1),CliErrorText(vecDataItem[j].Result).c_str(), vecDataItem[j].Amount,nTotolBlockLen);
		}
	}

	return ICV_SUCCESS;
}

//多读不支持DB块内TM类型，S300以上多读
int S7_SyncMultiRead(DRVHANDLE hDevice,std::map<DRVHANDLE, ConnParam*>::iterator it,std::vector<TS7DataBlockItem>& vecdataBlockItem)
{
	long lRet =0;
	std::string strMsg;
	std::vector<TS7DataItem > vecDataItem;
	int nTotolBlockLen = 0;
	for(int i =0; i < vecdataBlockItem.size(); ++i)
	{
		nTotolBlockLen += vecdataBlockItem[i].ts7DataItem.Amount;
		TS7DataItem items;
		memset(&items,0,sizeof(items));	
		items.Area = vecdataBlockItem[i].ts7DataItem.Area;
		items.DBNumber = vecdataBlockItem[i].ts7DataItem.DBNumber;
		items.Start = vecdataBlockItem[i].ts7DataItem.Start;
		items.WordLen = vecdataBlockItem[i].ts7DataItem.WordLen;
		items.Amount = vecdataBlockItem[i].ts7DataItem.Amount;
		items.pdata = vecdataBlockItem[i].ts7DataItem.pdata;

		vecDataItem.push_back(items);	// 获取多读块数组
		strMsg = strMsg + " " + string(Drv_GetDataBlockInfo(vecdataBlockItem[i].hBlock)->pszName);
	}

	int ret = it->second->Client->ReadMultiVars(&vecDataItem[0],vecDataItem.size());	// 多重读接口
	int nCostTime = it->second->Client->ExecTime();
	CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] MultiXBRead Group[0x%x]:%s,cost %d ms.TotalBlockLen %d pdusize %d!",
		it->second->m_deviceName.c_str(), vecdataBlockItem.back().hBlock, strMsg.c_str(), nCostTime, nTotolBlockLen, it->second->m_param.m_PDUSize);

	if(ICV_SUCCESS != ret)
	{
		CV_ERROR(g_CVLogSnap7Drv,ret,"Device[%s] MultiXBRead Group[0x%x]:%s read failed[%s].",it->second->m_deviceName.c_str(),
			vecdataBlockItem.back().hBlock,strMsg.c_str(),CliErrorText(ret).c_str());

		for(int k = 0; k < vecDataItem.size(); ++k) // 更新多读分组内所有数据块质量
			Drv_UpdateBlockStatus(hDevice, vecdataBlockItem[k].hBlock, DATA_STATUS_COMM_FAILURE);

		int nblockCount = Drv_GetDataBlockCount(hDevice);
		nblockCount = 1;
		it->second->m_nPlcReadBlockFailedCnt += vecdataBlockItem.size();
		if(it->second->m_nPlcReadBlockFailedCnt < nblockCount)
		{
			CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] CheckDisconnectCnt ReadBlockFailedCnt/totalBlockCnt(%d/%d)",it->second->m_deviceName.c_str(),it->second->m_nPlcReadBlockFailedCnt,nblockCount);
			return DRV_SUCCESS;
		}

		it->second->m_nPlcReadBlockFailedCnt = 0;
		it->second->Client->Disconnect();
		Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
		ret = it->second->Client->Connect(); // try reconncet
		it->second->m_currPlcStatus = it->second->Client->PlcStatus();
		CV_WARN(g_CVLogSnap7Drv,0,"Device[%s] MultiRead reconnect(%s). plcstatus(%d)",it->second->m_deviceName.c_str(),CliErrorText(ret).c_str(),it->second->m_currPlcStatus);
		return DRV_SUCCESS;
	}

	for(int j = 0; j < vecDataItem.size(); ++j)
	{	
		if(vecDataItem[j].Amount <= 0)
		{
			CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] MultiRead datablock[%s] XBNumber[%d] BlockLen[0],skip this datablock.",it->second->m_deviceName.c_str(),Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszName,atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszParam1));
			continue;
		}

		if(ICV_SUCCESS == vecDataItem[j].Result) // 更新多读分组内所有数据块
		{
			it->second->m_nPlcReadBlockFailedCnt = 0;
			Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
			int nStart = vecdataBlockItem[j].ts7DataItem.Start - atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszAddress);
			lRet = Drv_UpdateBlockData(hDevice,vecdataBlockItem[j].hBlock,(const char*)vecDataItem[j].pdata,nStart,vecDataItem[j].Amount,DATA_STATUS_OK,NULL); // 更新数据块

			CV_DEBUG(g_CVLogSnap7Drv, "Device[%s] MultiRead datablock[%s] XBNumber[%d] start 0 amount %d updateblock lRet %d ",
				it->second->m_deviceName.c_str(), Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszName,atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszParam1), vecDataItem[j].Amount, lRet);
		}
		else if(errCliAddressOutOfRange == vecDataItem[j].Result)
		{
			Drv_UpdateBlockStatusEx(hDevice, vecdataBlockItem[j].hBlock,IPLAT_QUALITY_BAD,IPLAT_SS_CONFIG_ERROR,IPLAT_LIMIT_NOT_LIMITED,IPLAT_CV_LV_IOADDRESS_DATALENGTH_TOOMUCH);
			CV_ERROR(g_CVLogSnap7Drv,vecDataItem[j].Result,"Device[%s] MultiRead datablock[%s] BlockType[%s] XBNumber[%d] failed[%s]. BlockLen %d nTotolBlockLen %d",it->second->m_deviceName.c_str()
				,Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszName,Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszBlockType,atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszParam1),CliErrorText(vecDataItem[j].Result).c_str(), vecDataItem[j].Amount,nTotolBlockLen);
			//调节数据块长度，使之不再报超长错误
			S7_TryToAdjustBlockLen(it->second->Client,hDevice, vecdataBlockItem[j].hBlock,&vecdataBlockItem[j]);
		}
		else if(errCliItemNotAvailable == vecDataItem[j].Result)
		{
			Drv_UpdateBlockStatusEx(hDevice, vecdataBlockItem[j].hBlock,IPLAT_QUALITY_BAD,IPLAT_SS_CONFIG_ERROR,IPLAT_LIMIT_NOT_LIMITED,IPLAT_CV_LV_DATABLOCK_NOTFOUND);
			CV_ERROR(g_CVLogSnap7Drv,vecDataItem[j].Result,"Device[%s] MultiRead datablock[%s] BlockType[%s] XBNumber[%d] failed[%s]. BlockLen %d nTotolBlockLen %d",it->second->m_deviceName.c_str()
				,Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszName,Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszBlockType,atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszParam1),CliErrorText(vecDataItem[j].Result).c_str(), vecDataItem[j].Amount,nTotolBlockLen);
		}
		else if(errCliInvalidParams == vecDataItem[j].Result)
		{
			Drv_UpdateBlockStatus(hDevice, vecdataBlockItem[j].hBlock, DATA_STATUS_CONFIG_ERROR);
			CV_ERROR(g_CVLogSnap7Drv,vecDataItem[j].Result,"Device[%s] MultiRead datablock[%s] BlockType[%s] XBNumber[%d] failed[%s]. BlockLen %d nTotolBlockLen %d",it->second->m_deviceName.c_str()
				,Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszName,Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszBlockType,atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszParam1),CliErrorText(vecDataItem[j].Result).c_str(), vecDataItem[j].Amount,nTotolBlockLen);
		}
		else
		{
			Drv_UpdateBlockStatus(hDevice, vecdataBlockItem[j].hBlock, DATA_STATUS_COMM_FAILURE);
			CV_ERROR(g_CVLogSnap7Drv,vecDataItem[j].Result,"Device[%s] MultiRead datablock[%s] BlockType[%s] XBNumber[%d] failed[%s]. BlockLen %d nTotolBlockLen %d",it->second->m_deviceName.c_str()
				,Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszName,Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszBlockType,atoi(Drv_GetDataBlockInfo(vecdataBlockItem[j].hBlock)->pszParam1),CliErrorText(vecDataItem[j].Result).c_str(), vecDataItem[j].Amount,nTotolBlockLen);
		}
	}

	return ICV_SUCCESS;
}

void UpDateBlockData(DRVHANDLE hDevice, DRVHANDLE hDatablock, byte* Buffer)
{
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	CVDATABLOCK *pDatablock = Drv_GetDataBlockInfo(hDatablock);
	if(pDatablock->nBlockDataSize <= 0)
	{
		CV_DEBUG(g_CVLogSnap7Drv,"Device[%s]:Block[%s] BlockLen[0],skip this datablock.",pDevice->pszName,pDatablock->pszName);
		return ;
	}
	const char* szBlkType = pDatablock->pszBlockType;
	if(ACE_OS::strcasecmp(pDatablock->pszParam2, "1") != 0)
	{
		Drv_UpdateBlockData(hDevice, hDatablock,(const char*)Buffer, 0, (int)pDatablock->nBlockDataSize, DATA_STATUS_OK, NULL);
		if(ACE_OS::strcasecmp(szBlkType,"DB") == 0)
		{
			CV_DEBUG(g_CVLogSnap7Drv,"Device[%s]:[%s]:DB[%d]Read Success!",pDevice->pszConnParam,pDevice->pszName,atoi(pDatablock->pszParam1));
		}
		else
		{
			CV_DEBUG(g_CVLogSnap7Drv,"Device[%s]:[%s]:%s[%s]Read Success!",pDevice->pszConnParam,pDevice->pszName,szBlkType,pDatablock->pszName);
		}
	}
	else
	{
		int16 TransBuffer[1024*64];
		for(int i = 0; i < (int)pDatablock->nBlockDataSize/2;)
		{
			int32 nRetdata = 0;
			int16 nTempData = *(int16*)(Buffer + i);
			CV_TRACE(g_CVLogSnap7Drv,"nTempData : %d!", nTempData);

			nRetdata = transferBuffer(nTempData);
			CV_TRACE(g_CVLogSnap7Drv,"nRetdata : %d!", nRetdata);
			memcpy(&TransBuffer[i], &nRetdata, sizeof(int32));
			i += 2;
		}

		Drv_UpdateBlockData(hDevice, hDatablock,(const char*)TransBuffer, 0, (int)pDatablock->nBlockDataSize, DATA_STATUS_OK, NULL);
		CV_DEBUG(g_CVLogSnap7Drv,"Device[%s]:[%s]:DB-TIME[%d]Read Success!",pDevice->pszConnParam,pDevice->pszName,atoi(pDatablock->pszParam1));				
	}

}

bool isReadyToDoReconnect(int64& tmLasReConnectMS,int nReconnTimeOutMS)
{
	// 小于重连超时则不能重连，避免频繁连接导致plc资源占满
	int64 nTimeSpan = os_get_curr_time_msec() - tmLasReConnectMS;
	if(nTimeSpan <  nReconnTimeOutMS)
		return false;

	tmLasReConnectMS = os_get_curr_time_msec();
	return true;
}



int32 S7_SyncReadFail(DRVHANDLE hDevice, DRVHANDLE hDatablock,map<string, ConnParam*>::iterator it, int nPlcstatus)
{
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	int nblockCount = Drv_GetDataBlockCount(hDevice);
	nblockCount = 1;
	CV_ERROR(g_CVLogSnap7Drv,nPlcstatus ,"Device[%s %s] RedundancyFlag(%d) Status(0x%x) NOT RUNNING,Please Check PLC !!!",
		pDevice->pszConnParam,it->second->m_deviceName.c_str(),it->second->m_param.m_rmflag,nPlcstatus);

	Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_COMM_FAILURE);

	CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] CheckDisconnectCnt ReadBlockFailedCnt/totalBlockCnt(%d/%d)",it->second->m_deviceName.c_str(),it->second->m_nPlcReadBlockFailedCnt,nblockCount);
	if(++it->second->m_nPlcReadBlockFailedCnt < nblockCount)
	{
		return DRV_SUCCESS;
	}
	it->second->m_nPlcReadBlockFailedCnt = 0;
	Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
	it->second->Client->Disconnect();

	// 小于重连超时则不能重连，避免频繁连接导致plc资源占满
	if(!isReadyToDoReconnect(it->second->m_nLasReConnectMS,it->second->m_nReConnTimeOutMS))
		return -1;

	int nRet = nPlcstatus = it->second->Client->Connect();
	//nPlcstatus = it->second->Client->PlcStatus();
	nPlcstatus = 0x08;
	it->second->m_currPlcStatus = nPlcstatus;
	CV_WARN(g_CVLogSnap7Drv,0,"Device[%s] reconnect(%s). plcstatus(%d)",it->second->m_deviceName.c_str(),CliErrorText(nRet).c_str(),nPlcstatus);

	return DRV_SUCCESS;
}

int32 S7_SyncReadFail(DRVHANDLE hDevice, DRVHANDLE hDatablock,map<DRVHANDLE, ConnParam*>::iterator it, int nPlcstatus)
{
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	int nblockCount = Drv_GetDataBlockCount(hDevice);
	nblockCount = 1;
	CV_ERROR(g_CVLogSnap7Drv,nPlcstatus ,"Device[%s %s] RedundancyFlag(%d) Status(0x%x) NOT RUNNING,Please Check PLC !!!",
		pDevice->pszConnParam,it->second->m_deviceName.c_str(),it->second->m_param.m_rmflag,nPlcstatus);
	Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_COMM_FAILURE);

	it->second->m_nPlcReadBlockFailedCnt++;
	CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] CheckDisconnectCnt ReadBlockFailedCnt/totalBlockCnt(%d/%d)",it->second->m_deviceName.c_str(),it->second->m_nPlcReadBlockFailedCnt,nblockCount);
	if(it->second->m_nPlcReadBlockFailedCnt < nblockCount)
	{	
		return DRV_SUCCESS;
	}
	it->second->m_nPlcReadBlockFailedCnt = 0;
	Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
	it->second->Client->Disconnect();

	// 小于重连超时则不能重连，避免频繁连接导致plc资源占满
	if(!isReadyToDoReconnect(it->second->m_nLasReConnectMS,it->second->m_nReConnTimeOutMS))
		return -1;

	int nRet = it->second->Client->Connect();
	nPlcstatus = it->second->Client->PlcStatus();
	it->second->m_currPlcStatus = nPlcstatus;
	CV_WARN(g_CVLogSnap7Drv,0,"Device[%s] reconnect(%s). plcstatus(%d)",it->second->m_deviceName.c_str(),CliErrorText(nRet).c_str(),nPlcstatus);

	return DRV_SUCCESS;
}

// s7-200
int32 S7_SyncMultiReadFail(DRVHANDLE hDevice, DRVHANDLE hDatablock,map<string, ConnParam*>::iterator iterSnap7Device200,vector<vector<TS7DataBlockItem> >* vVecdataitem,int nPlcStatus)
{
	if(NULL == hDevice || NULL == hDatablock || NULL == vVecdataitem)
		return EC_ICV_DA_INVALID_PARAMETER;

	CV_ERROR(g_CVLogSnap7Drv,nPlcStatus ,"Device[%s] RedundancyFlag(%d) Status(0x%x) NOT RUNNING,Please Check PLC !!!",
		iterSnap7Device200->second->m_deviceName.c_str(),iterSnap7Device200->second->m_param.m_rmflag,nPlcStatus);

	vector<vector<TS7DataBlockItem> >::iterator iter =  vVecdataitem->begin(); // 获取所有多读块分组
	for(; iter != vVecdataitem->end(); ++iter)
	{ 
		if(hDatablock == iter->back().hBlock)	//多读组每组最后一个块是主块
		{
			vector<TS7DataBlockItem>::iterator it = iter->begin();
			for(;it != iter->end(); ++it)
			{
				iterSnap7Device200->second->m_nPlcReadBlockFailedCnt++;
				Drv_UpdateBlockStatus(hDevice, it->hBlock, DATA_STATUS_COMM_FAILURE);	// 当前组所有多读块置bad
				CV_ERROR(g_CVLogSnap7Drv,nPlcStatus, "Device[%s] MultiRead datablock[%s] XBNumber[%d] start 0 amount %d updateblock failed,plcstatus(0x%x)[%s]",
					iterSnap7Device200->second->m_deviceName.c_str(), Drv_GetDataBlockInfo(it->hBlock)->pszName,atoi(Drv_GetDataBlockInfo(it->hBlock)->pszParam1), it->ts7DataItem.Amount,nPlcStatus,CliErrorText(nPlcStatus).c_str());
			}
		}
	}	

	int nblockCount = Drv_GetDataBlockCount(hDevice);
	nblockCount = 1;
	CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] CheckDisconnectCnt ReadBlockFailedCnt/totalBlockCnt(%d/%d)",iterSnap7Device200->second->m_deviceName.c_str(),iterSnap7Device200->second->m_nPlcReadBlockFailedCnt,nblockCount);
	if(iterSnap7Device200->second->m_nPlcReadBlockFailedCnt < nblockCount)
		return DRV_SUCCESS;


	Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
	iterSnap7Device200->second->m_nPlcReadBlockFailedCnt = 0;		
	iterSnap7Device200->second->Client->Disconnect();

	// 小于重连超时则不能重连，避免频繁连接导致plc资源占满
	if(!isReadyToDoReconnect(iterSnap7Device200->second->m_nLasReConnectMS,iterSnap7Device200->second->m_nReConnTimeOutMS))
		return -1;

	int nRet = iterSnap7Device200->second->Client->Connect(); // try reconncet
	//iterSnap7Device200->second->m_currPlcStatus = iterSnap7Device200->second->Client->PlcStatus();
	iterSnap7Device200->second->m_currPlcStatus = 0x08;
	CV_WARN(g_CVLogSnap7Drv,0,"Device[%s] MultiRead reconnect(%s). plcstatus(%d)",iterSnap7Device200->second->m_deviceName.c_str(),CliErrorText(nRet).c_str(),iterSnap7Device200->second->m_currPlcStatus);
	return DRV_SUCCESS;
}

//s7-300+
int32 S7_SyncMultiReadFail(DRVHANDLE hDevice, DRVHANDLE hDatablock,map<DRVHANDLE, ConnParam*>::iterator iterSnap7Device,vector<vector<TS7DataBlockItem> >* vVecdataitem,int nPlcStatus)
{
	if(NULL == hDevice || NULL == hDatablock || NULL == vVecdataitem)
		return EC_ICV_DA_INVALID_PARAMETER;

	CV_ERROR(g_CVLogSnap7Drv,nPlcStatus ,"Device[%s] RedundancyFlag(%d) Status(0x%x) NOT RUNNING,Please Check PLC !!!",
		iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.m_rmflag,nPlcStatus);

	vector<vector<TS7DataBlockItem> >::iterator iter =  vVecdataitem->begin(); // 获取所有多读块分组
	for(; iter != vVecdataitem->end(); ++iter)
	{ 
		if(hDatablock == iter->back().hBlock)	//多读组每组最后一个块是主块
		{
			vector<TS7DataBlockItem>::iterator it = iter->begin();
			for(;it != iter->end(); ++it)
			{
				iterSnap7Device->second->m_nPlcReadBlockFailedCnt++;
				Drv_UpdateBlockStatus(hDevice, it->hBlock, DATA_STATUS_COMM_FAILURE);	// 当前组所有多读块置bad
				CV_ERROR(g_CVLogSnap7Drv,nPlcStatus, "Device[%s] MultiRead datablock[%s] BlockType[%s] XBNumber[%d] start 0 amount %d updateblock failed,plcstatus(0x%x)[%s]",
					iterSnap7Device->second->m_deviceName.c_str(), Drv_GetDataBlockInfo(it->hBlock)->pszName, Drv_GetDataBlockInfo(it->hBlock)->pszBlockType,
					atoi(Drv_GetDataBlockInfo(it->hBlock)->pszParam1), it->ts7DataItem.Amount,nPlcStatus,CliErrorText(nPlcStatus).c_str());
			}
		}
	}

	int nblockCount = Drv_GetDataBlockCount(hDevice);
	nblockCount = 1;
	CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] CheckDisconnectCnt ReadBlockFailedCnt/totalBlockCnt(%d/%d)",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_nPlcReadBlockFailedCnt,nblockCount);
	if(iterSnap7Device->second->m_nPlcReadBlockFailedCnt < nblockCount)
		return DRV_SUCCESS;

	Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
	iterSnap7Device->second->m_nPlcReadBlockFailedCnt = 0;		
	iterSnap7Device->second->Client->Disconnect();

	// 小于重连超时则不能重连，避免频繁连接导致plc资源占满
	if(!isReadyToDoReconnect(iterSnap7Device->second->m_nLasReConnectMS,iterSnap7Device->second->m_nReConnTimeOutMS))
		return -1;

	int nRet = iterSnap7Device->second->Client->Connect(); // try reconncet
	iterSnap7Device->second->m_currPlcStatus = iterSnap7Device->second->Client->PlcStatus();
	CV_WARN(g_CVLogSnap7Drv,0,"Device[%s] MultiRead reconnect(%s). plcstatus(%d)",iterSnap7Device->second->m_deviceName.c_str(),CliErrorText(nRet).c_str(),iterSnap7Device->second->m_currPlcStatus);
	return DRV_SUCCESS;
}

bool S7_SwitchPLCAddr(DRVHANDLE hDevice, DRVHANDLE hDatablock,map<DRVHANDLE, ConnParam*>::iterator iterSnap7Device)
{
	if(!iterSnap7Device->second->m_param.m_rmflag)
		return false;

	int res;
	iterSnap7Device->second->Client->Disconnect(); 
	if(iterSnap7Device->second->m_param.ConnectAddr1 == iterSnap7Device->second->m_currPlcIP)
	{
		//当前是PLC1, 切换到PLC2 
		res= iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_param.ConnectAddr2.c_str(),iterSnap7Device->second->m_param.rack2,iterSnap7Device->second->m_param.slot2);
		if(ICV_SUCCESS != res)
		{
			iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr1;
			iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack1;
			iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot1;
			res = iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_currPlcIP.c_str(),iterSnap7Device->second->m_currPlcRack,iterSnap7Device->second->m_currPlcSlot);
			CV_ERROR(g_CVLogSnap7Drv,res,"RM :Device[%s] Switch to Slave PLC[%s] failed. Switch to Master PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr2.c_str(),iterSnap7Device->second->m_param.ConnectAddr1.c_str());
			return false;
		}
		else
		{
			iterSnap7Device->second->m_currPlcStatus = 0x09;
			iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr2;
			iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack2;
			iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot2;
			CV_WARN(g_CVLogSnap7Drv,res,"RM :Device[%s] Switch to Slave PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr2.c_str());
			return true;
		}
	}
	else
	{
		//当前是PLC2, 切换到PLC1  
		res = iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_param.ConnectAddr1.c_str(),iterSnap7Device->second->m_param.rack1,iterSnap7Device->second->m_param.slot1);
		if(ICV_SUCCESS != res)
		{
			iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr2;
			iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack2;
			iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot2;
			res = iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_currPlcIP.c_str(),iterSnap7Device->second->m_currPlcRack,iterSnap7Device->second->m_currPlcSlot);
			CV_ERROR(g_CVLogSnap7Drv,res,"RM :Device[%s] Switch to Master PLC[%s] failed. Switch to Slave PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr1.c_str(),iterSnap7Device->second->m_param.ConnectAddr2.c_str());
			return false;
		}
		else
		{
			iterSnap7Device->second->m_currPlcStatus = 0x09;
			iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr1;
			iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack1;
			iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot1;
			CV_WARN(g_CVLogSnap7Drv,res,"RM :Device[%s] Switch to Master PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr1.c_str());
			return true;
		}
	}		
}

int32 UpDateData(DRVHANDLE hDevice, DRVHANDLE hDatablock, bool isTsap)
{
	int ret = 0;
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	CVDATABLOCK *pDatablock = Drv_GetDataBlockInfo(hDatablock);
	int nblockCount = Drv_GetDataBlockCount(hDevice);
	const char* szBlkType = pDatablock->pszBlockType;

	byte Buffer[1024*64]; // 64 K buffer

	//tsap 为s7 200, 其它的配机架号和槽号
	if(isTsap)
	{
		std::map<string, ConnParam*>::iterator iterSnap7Device200;
		{
			ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
			iterSnap7Device200 = g_mapDeviceFor200.find(string(pDevice->pszName));
			if (iterSnap7Device200 == g_mapDeviceFor200.end())
			{
				CV_ERROR(g_CVLogSnap7Drv, EC_ICV_DRVCTRL_DEVICE_NOT_FOUND, "EC_ICV_DRVCTRL_DEVICE_NOT_FOUND");
				return EC_ICV_DRVCTRL_DEVICE_NOT_FOUND;
			}
		}
		// SOLO Devices 单设备
		if(!iterSnap7Device200->second->m_param.m_rmflag)
		{
			int res = iterSnap7Device200->second->m_currPlcStatus;
			res = 0x08;
			if(res == 0x08||res == errCliItemNotAvailable)  //  PLC solo mode status RUN  12582912 for smart200, item not available
			{
				ret = S7_SyncRead(hDatablock,iterSnap7Device200,Buffer);
				CV_DEBUG(g_CVLogSnap7Drv,"SOLO:Device[%s] datablock[%s] XBNumber[%d] result(%d)[%s]",pDevice->pszName,pDatablock->pszName,atoi(pDatablock->pszParam1),ret,CliErrorText(ret).c_str());
				//if(!((ret)^(~ErrS7Mask))) //iso + tcp 类型错误码
				if(!ret)
				{
					if(pDatablock->nBlockDataSize <= 0)
					{
						CV_DEBUG(g_CVLogSnap7Drv,"Device[%s]:Block[%s] BlockLen[0],skip this datablock.",pDevice->pszName,pDatablock->pszName);
						return ICV_SUCCESS;
					}

					iterSnap7Device200->second->m_nPlcReadBlockFailedCnt = 0;
					Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
					Drv_UpdateBlockData(hDevice, hDatablock,(const char*)Buffer, 0, (int)pDatablock->nBlockDataSize, DATA_STATUS_OK, NULL);
					CV_DEBUG(g_CVLogSnap7Drv,"SOLO : Device[%s]:[%s]:[%s]Read Success!",pDevice->pszConnParam,pDevice->pszName,pDatablock->pszName);
				}
				else if(errCliAddressOutOfRange == ret)	// adress out of range
				{
					Drv_UpdateBlockStatusEx(hDevice, hDatablock,IPLAT_QUALITY_BAD,IPLAT_SS_CONFIG_ERROR,IPLAT_LIMIT_NOT_LIMITED,IPLAT_CV_LV_IOADDRESS_DATALENGTH_TOOMUCH);
					CV_ERROR(g_CVLogSnap7Drv,ret,"SOLO:Device[%s]:[%s]:[%s] XBNumber[%d] Read Failed!Result:[%s]",pDevice->pszConnParam,pDevice->pszName,pDatablock->pszName,atoi(pDatablock->pszParam1),CliErrorText(ret).c_str());
					//调节数据块长度，使之不再报超长错误
					//TS7DataBlockItem datablockitem;
					S7_TryToAdjustBlockLen(iterSnap7Device200->second->Client, hDevice,hDatablock);
				}
				else if(errCliItemNotAvailable == ret)  // item not available
				{		
					Drv_UpdateBlockStatusEx(hDevice, hDatablock,IPLAT_QUALITY_BAD,IPLAT_SS_CONFIG_ERROR,IPLAT_LIMIT_NOT_LIMITED,IPLAT_CV_LV_DATABLOCK_NOTFOUND);
					CV_ERROR(g_CVLogSnap7Drv,ret,"SOLO:Device[%s]:[%s]:[%s] XBNumber[%d] Read Failed!Result:[%s]",pDevice->pszConnParam,pDevice->pszName,pDatablock->pszName,atoi(pDatablock->pszParam1),CliErrorText(ret).c_str());
				}
				else if(errCliInvalidParams == ret)
				{
					Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_CONFIG_ERROR);
					CV_ERROR(g_CVLogSnap7Drv,ret,"SOLO:Device[%s]:[%s]:[%s] XBNumber[%d] Read Failed!Result:[%s]",pDevice->pszConnParam,pDevice->pszName,pDatablock->pszName,atoi(pDatablock->pszParam1),CliErrorText(ret).c_str());
				}
				else  // 
				{
					Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_COMM_FAILURE);
					CV_ERROR(g_CVLogSnap7Drv,ret,"SOLO:Device[%s]:[%s]:[%s] XBNumber[%d] Read Failed!Result:[%s]",pDevice->pszConnParam,pDevice->pszName,pDatablock->pszName,atoi(pDatablock->pszParam1),CliErrorText(ret).c_str());
					S7_SyncReadFail(hDevice,hDatablock,iterSnap7Device200,ret);  //目前未过滤网络错误类返回码，因此所有读失败的块都做失败计数。后续考虑根据ErrMask筛选出网络错误码					
				}
			}
			else 
				S7_SyncReadFail(hDevice,hDatablock,iterSnap7Device200,res);

		}
		else	//  RM Devices 有冗余设备
		{
			CV_ERROR(g_CVLogSnap7Drv,-1 ,"S7-200 series Dose not support Redundance !!!");
		}
	}

	else	//  非tsap设备
	{
		std::map<DRVHANDLE, ConnParam*>::iterator iterSnap7Device;
		{
			ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
			iterSnap7Device = g_mapDevice.find(hDevice);
			if (iterSnap7Device == g_mapDevice.end())
			{
				CV_ERROR(g_CVLogSnap7Drv, EC_ICV_DRVCTRL_DEVICE_NOT_FOUND, "EC_ICV_DRVCTRL_DEVICE_NOT_FOUND");
				return EC_ICV_DRVCTRL_DEVICE_NOT_FOUND;
			}
		}
		if(!iterSnap7Device->second->m_param.m_rmflag)	// SOLO Devices
		{
			int res = iterSnap7Device->second->m_currPlcStatus;
			if(res == 0x08||res == errCliItemNotAvailable)  //  PLC solo mode status RUN  12582912 for smart200
			{
				ret = S7_SyncRead(hDatablock,iterSnap7Device,Buffer);
				CV_DEBUG(g_CVLogSnap7Drv,"SOLO:Device[%s] datablock[%s] XBNumber[%d] result(%d)[%s]",pDevice->pszName,pDatablock->pszName,atoi(pDatablock->pszParam1),ret,CliErrorText(ret).c_str());
				//if(!((ret)^(~ErrS7Mask))) //iso + tcp 类型错误码
				if(!ret)
				{
					iterSnap7Device->second->m_nPlcReadBlockFailedCnt = 0;
					Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
					UpDateBlockData(hDevice,hDatablock,Buffer);
				}
				else if(errCliAddressOutOfRange == ret)	// adress out of range
				{	
					Drv_UpdateBlockStatusEx(hDevice, hDatablock,IPLAT_QUALITY_BAD,IPLAT_SS_CONFIG_ERROR,IPLAT_LIMIT_NOT_LIMITED,IPLAT_CV_LV_IOADDRESS_DATALENGTH_TOOMUCH );
					CV_ERROR(g_CVLogSnap7Drv,ret,"SOLO:Device[%s]:[%s]:[%s] BlockType[%s] XBNumber[%d] Read Failed!Result:[%s]",pDevice->pszConnParam,pDevice->pszName,pDatablock->pszName,pDatablock->pszBlockType,atoi(pDatablock->pszParam1),CliErrorText(ret).c_str());
					//调节数据块长度，使之不再报超长错误
					//TS7DataBlockItem datablockitem;
					S7_TryToAdjustBlockLen(iterSnap7Device->second->Client, hDevice,hDatablock);
				}
				else if(errCliItemNotAvailable == ret)  // item not available
				{		
					Drv_UpdateBlockStatusEx(hDevice, hDatablock,IPLAT_QUALITY_BAD,IPLAT_SS_CONFIG_ERROR,IPLAT_LIMIT_NOT_LIMITED,IPLAT_CV_LV_DATABLOCK_NOTFOUND);
					CV_ERROR(g_CVLogSnap7Drv,ret,"SOLO:Device[%s]:[%s]:[%s] BlockType[%s] XBNumber[%d] Read Failed!Result:[%s]",pDevice->pszConnParam,pDevice->pszName,pDatablock->pszName,pDatablock->pszBlockType,atoi(pDatablock->pszParam1),CliErrorText(ret).c_str());
				}
				else if(errCliInvalidParams == ret)
				{
					Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_CONFIG_ERROR);
					CV_ERROR(g_CVLogSnap7Drv,ret,"SOLO:Device[%s]:[%s]:[%s] XBNumber[%d] Read Failed!Result:[%s]",pDevice->pszConnParam,pDevice->pszName,pDatablock->pszName,atoi(pDatablock->pszParam1),CliErrorText(ret).c_str());
				}
				else
				{
					Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_COMM_FAILURE); 
					CV_ERROR(g_CVLogSnap7Drv,ret,"SOLO:Device[%s]:[%s]:[%s] BlockType[%s] XBNumber[%d] Read Failed!Result:[%s]",pDevice->pszConnParam,pDevice->pszName,pDatablock->pszName,pDatablock->pszBlockType,atoi(pDatablock->pszParam1),CliErrorText(ret).c_str());
					S7_SyncReadFail(hDevice,hDatablock,iterSnap7Device,ret);  //目前未过滤网络错误类返回码，因此所有读失败的块都做失败计数。后续考虑根据ErrMask筛选出网络错误码					
				}
			}
			else 
				S7_SyncReadFail(hDevice,hDatablock,iterSnap7Device,res);	//
		}
		else	//  RM Devices 
		{
			CV_DEBUG(g_CVLogSnap7Drv,"RM:Device[%s] current connected PLC is %s",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_currPlcIP.c_str());
 			int nPlcStatus = 0x09;
			nPlcStatus = iterSnap7Device->second->m_currPlcStatus;
// 			{
// 				struct timeval tvCurrent;
// 				os_gettimeofday(&tvCurrent);
// 				if(tvCurrent.tv_sec >= iterSnap7Device->second->m_getStatusTime +  iterSnap7Device->second->m_param.PlcStatusPeriodSec)
// 				{
// 					nPlcStatus = iterSnap7Device->second->Client->PlcStatus();
// 					int nCostTime = iterSnap7Device->second->Client->ExecTime();
// 					CV_DEBUG(g_CVLogSnap7Drv,"RM:Device[%s] m_currPlcStatus:0x%x, cost %d ms!",iterSnap7Device->second->m_deviceName.c_str(),nPlcStatus, nCostTime);
// 					iterSnap7Device->second->m_currPlcStatus = nPlcStatus;
// 					iterSnap7Device->second->m_getStatusTime = tvCurrent.tv_sec;
// 				}
// 			}

			if(nPlcStatus != 0x09 && nPlcStatus != 0x08 && nPlcStatus != 0x0b)	
			{				
				CV_ERROR(g_CVLogSnap7Drv,-1 ,"RM:Device[%s] PLCStatus(0x%x)[%s] NOT RUNNING!!!",iterSnap7Device->second->m_deviceName.c_str(),nPlcStatus,CliErrorText(nPlcStatus).c_str());
				Drv_UpdateDevStatus(iterSnap7Device->first, DEV_STATUS_BAD);
				bool bRmSwithSuccess = false;//冗余切换是否成功
				int res;
				{
					iterSnap7Device->second->Client->Disconnect(); 
					if(iterSnap7Device->second->m_param.ConnectAddr1 == iterSnap7Device->second->m_currPlcIP)
					{
						//当前是PLC1, 切换到PLC2  plcstatus1秒查询一次这里不查询
						res= iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_param.ConnectAddr2.c_str(),iterSnap7Device->second->m_param.rack2,iterSnap7Device->second->m_param.slot2);
						if(ICV_SUCCESS != res)
						{
							iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr1;
							iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack1;
							iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot1;
							res = iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_currPlcIP.c_str(),iterSnap7Device->second->m_currPlcRack,iterSnap7Device->second->m_currPlcSlot);
							CV_ERROR(g_CVLogSnap7Drv,res,"RM:Device[%s] Switch to Slave PLC[%s] failed. Switch to Master PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr2.c_str(),iterSnap7Device->second->m_param.ConnectAddr1.c_str());
						}
						else
						{
							bRmSwithSuccess = true;
							iterSnap7Device->second->m_currPlcStatus = 0x09;
							iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr2;
							iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack2;
							iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot2;
							CV_WARN(g_CVLogSnap7Drv,res,"RM:Device[%s] Switch to Slave PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr2.c_str());
						}
					}
					else
					{
						//当前是PLC2, 切换到PLC1  plcstatus1秒查询一次这里不查询
						res = iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_param.ConnectAddr1.c_str(),iterSnap7Device->second->m_param.rack1,iterSnap7Device->second->m_param.slot1);
						if(ICV_SUCCESS != res)
						{
							iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr2;
							iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack2;
							iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot2;
							res = iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_currPlcIP.c_str(),iterSnap7Device->second->m_currPlcRack,iterSnap7Device->second->m_currPlcSlot);
							CV_ERROR(g_CVLogSnap7Drv,res,"RM:Device[%s] Switch to Master PLC[%s] failed. Switch to Slave PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr1.c_str(),iterSnap7Device->second->m_param.ConnectAddr2.c_str());
						}
						else
						{
							bRmSwithSuccess = true;
							iterSnap7Device->second->m_currPlcStatus = 0x09;
							iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr1;
							iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack1;
							iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot1;
							CV_WARN(g_CVLogSnap7Drv,res,"RM :Device[%s] Switch to Master PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr1.c_str());
						}
					}
				}
				//冗余切换不成功，更新数据块状态直接返回，切换成功代码向下走读取数据块
				if (!bRmSwithSuccess)
				{
					Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_COMM_FAILURE); 
					return ICV_SUCCESS;
				}				
			}	

			ret = S7_SyncRead(hDatablock,iterSnap7Device,Buffer);
			CV_DEBUG(g_CVLogSnap7Drv,"RM:S7_SyncRead result(%d)[%s]",ret,CliErrorText(ret).c_str());
			if(ICV_SUCCESS == ret)
			{
				//iterSnap7Device->second->m_nPlcReadBlockFailedCnt = 0;
				Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
				UpDateBlockData(hDevice,hDatablock,Buffer);
			} 
			else if(0x900000 == ret)
			{
				Drv_UpdateBlockStatusEx(hDevice, hDatablock,IPLAT_QUALITY_BAD,IPLAT_SS_CONFIG_ERROR,IPLAT_LIMIT_NOT_LIMITED,IPLAT_CV_LV_IOADDRESS_DATALENGTH_TOOMUCH);
				CV_ERROR(g_CVLogSnap7Drv,ret,"RM:Device[%s]:[%s]:[%s]Read BlockType[%s] XBNumber[%d] Failed!Result:[%s]",pDevice->pszConnParam,pDevice->pszName,pDatablock->pszName,pDatablock->pszBlockType,atoi(pDatablock->pszParam1),CliErrorText(ret).c_str());
				//调节数据块长度，使之不再报超长错误
				//TS7DataBlockItem datablockitem;
				S7_TryToAdjustBlockLen(iterSnap7Device->second->Client, hDevice,hDatablock);
			}
			else if(0xC00000 == ret)
			{
				Drv_UpdateBlockStatusEx(hDevice, hDatablock,IPLAT_QUALITY_BAD,IPLAT_SS_CONFIG_ERROR,IPLAT_LIMIT_NOT_LIMITED,IPLAT_CV_LV_DATABLOCK_NOTFOUND);
				CV_ERROR(g_CVLogSnap7Drv,ret,"RM:Device[%s]:[%s]:[%s]Read BlockType[%s] XBNumber[%d] Failed!Result:[%s]",pDevice->pszConnParam,pDevice->pszName,pDatablock->pszName,pDatablock->pszBlockType,atoi(pDatablock->pszParam1),CliErrorText(ret).c_str());
			}
			else if(errCliInvalidParams == ret)
			{
				Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_CONFIG_ERROR);
				CV_ERROR(g_CVLogSnap7Drv,ret,"RM:Device[%s]:[%s]:[%s]Read BlockType[%s] XBNumber[%d] Failed!Result:[%s]",pDevice->pszConnParam,pDevice->pszName,pDatablock->pszName,pDatablock->pszBlockType,atoi(pDatablock->pszParam1),CliErrorText(ret).c_str());			}
			else  //
			{
				Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_COMM_FAILURE); 
				CV_ERROR(g_CVLogSnap7Drv,ret,"RM:Device[%s]:[%s]:[%s]Read BlockType[%s] XBNumber[%d] Failed!Result:[%s]",pDevice->pszConnParam,pDevice->pszName,pDatablock->pszName,pDatablock->pszBlockType,atoi(pDatablock->pszParam1),CliErrorText(ret).c_str());
			}
		}
	}

	return ICV_SUCCESS;
}

// 判断当前数据块是否为多读块的主块 
bool isReadyToMultiUpDateData(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	int ret = Drv_GetUserData(hDatablock,1);
	return 1 == ret;
}

// 判断是当前数据块是否为多读块
bool isMultiUpDateData(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	int ret = Drv_GetUserData(hDatablock,0);
	return 1 == ret;
}

// 多读函数 
int32 MultiUpDateData(DRVHANDLE hDevice, DRVHANDLE hDatablock, bool isTsap)
 {
	int ret = ICV_SUCCESS;

	if(false == isReadyToMultiUpDateData(hDevice,hDatablock)) // 只有当前块是多读块主块才进行多读处理，避免多读块重复读
		return ICV_SUCCESS;

	vector<vector<TS7DataBlockItem> >* vVecdataitem = (vector<vector<TS7DataBlockItem> >*)Drv_GetUserDataPtr(hDevice,0); // 将initialize中保存的MultiReadGroupInfo指针取出来
	if( NULL == vVecdataitem)
	{
		Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_UNKNOWN_REASON);
		CV_ERROR(g_CVLogSnap7Drv,-1,"Device[%s] MultiRead Drv_GetUserDataPtr failed!",Drv_GetDeviceInfo(hDevice)->pszName);
		return ICV_SUCCESS;
	}

	//s7-200
	if(isTsap) // s7-200多读
	{
		std::map<string, ConnParam*>::iterator iterSnap7Device200 = g_mapDeviceFor200.find(string(Drv_GetDeviceInfo(hDevice)->pszName)); // 找到当前设备,需要知道连接参数中的client指针
		if(iterSnap7Device200 == g_mapDeviceFor200.end())
			return ICV_SUCCESS;

		int res = iterSnap7Device200->second->m_currPlcStatus;
		res = 0x08;
		if(res == 0x08||res == 0xC00000)
		{
			vector<vector<TS7DataBlockItem> >::iterator iter =  vVecdataitem->begin(); // 获取所有多读块分组
			for(; iter != vVecdataitem->end(); ++iter)
			{ 
				vector<TS7DataBlockItem>::iterator it = iter->begin();
				for(;it != iter->end(); ++it)
				{
					if(hDatablock == it->hBlock)
						S7_SyncMultiRead(hDevice,iterSnap7Device200,*iter);	 // 按照分组多重读
				}
			}	
		}
		else	
			S7_SyncMultiReadFail(hDevice,hDatablock,iterSnap7Device200,vVecdataitem,res);
		
		return ICV_SUCCESS;
	}

	//s7-300+ 多读
	std::map<DRVHANDLE, ConnParam*>::iterator iterSnap7Device;
	{
		ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
		iterSnap7Device = g_mapDevice.find(hDevice);	// 找到当前设备,需要知道连接参数中的client指针
		if(iterSnap7Device == g_mapDevice.end())
			return ICV_SUCCESS;
	}

	if(false == iterSnap7Device->second->m_param.m_rmflag)	// 单机PLC
	{
		int res = iterSnap7Device->second->m_currPlcStatus;
		if(res == 0x08||res == 0xC00000)
		{
			vector<vector<TS7DataBlockItem> >::iterator iter =  vVecdataitem->begin(); // 获取所有多读块分组
			for(; iter != vVecdataitem->end(); ++iter)
			{ 
				vector<TS7DataBlockItem>::iterator it = iter->begin();
				for(;it != iter->end(); ++it)
				{
					if(hDatablock == it->hBlock)
						S7_SyncMultiRead(hDevice,iterSnap7Device,*iter);	 // 按照分组多重读
				}
			}	
		}	
		else	
			S7_SyncMultiReadFail(hDevice,hDatablock,iterSnap7Device,vVecdataitem,res);
	}
	else // todo:多读冗余
	{
		CV_DEBUG(g_CVLogSnap7Drv,"RM:Device[%s] current connected PLC is %s",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_currPlcIP.c_str());
		int nPlcStatus = iterSnap7Device->second->m_currPlcStatus;
		if(nPlcStatus != 0x09 && nPlcStatus != 0x08 && nPlcStatus != 0x0b)	
		{				
			CV_ERROR(g_CVLogSnap7Drv,-1 ,"RM:Device[%s] PLCStatus(0x%x)[%s] NOT RUNNING!!!",iterSnap7Device->second->m_deviceName.c_str(),nPlcStatus,CliErrorText(nPlcStatus).c_str());
			Drv_UpdateDevStatus(iterSnap7Device->first, DEV_STATUS_BAD);
			bool bRmSwithSuccess = false;//冗余切换是否成功
			int res;
			{
				iterSnap7Device->second->Client->Disconnect(); 
				if(iterSnap7Device->second->m_param.ConnectAddr1 == iterSnap7Device->second->m_currPlcIP)
				{
					//当前是PLC1, 切换到PLC2  plcstatus1秒查询一次这里不查询
					res= iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_param.ConnectAddr2.c_str(),iterSnap7Device->second->m_param.rack2,iterSnap7Device->second->m_param.slot2);
					if(ICV_SUCCESS != res)
					{
						iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr1;
						iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack1;
						iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot1;
						res = iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_currPlcIP.c_str(),iterSnap7Device->second->m_currPlcRack,iterSnap7Device->second->m_currPlcSlot);
						CV_ERROR(g_CVLogSnap7Drv,res,"RM:Device[%s] Switch to Slave PLC[%s] failed. Switch to Master PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr2.c_str(),iterSnap7Device->second->m_param.ConnectAddr1.c_str());
					}
					else
					{
						bRmSwithSuccess = true;
						iterSnap7Device->second->m_currPlcStatus = 0x09;
						iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr2;
						iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack2;
						iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot2;
						CV_WARN(g_CVLogSnap7Drv,res,"RM:Device[%s] Switch to Slave PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr2.c_str());
					}
				}
				else
				{
					//当前是PLC2, 切换到PLC1  plcstatus1秒查询一次这里不查询
					res = iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_param.ConnectAddr1.c_str(),iterSnap7Device->second->m_param.rack1,iterSnap7Device->second->m_param.slot1);
					if(ICV_SUCCESS != res)
					{
						iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr2;
						iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack2;
						iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot2;
						res = iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_currPlcIP.c_str(),iterSnap7Device->second->m_currPlcRack,iterSnap7Device->second->m_currPlcSlot);
						CV_ERROR(g_CVLogSnap7Drv,res,"RM:Device[%s] Switch to Master PLC[%s] failed. Switch to Slave PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr1.c_str(),iterSnap7Device->second->m_param.ConnectAddr2.c_str());
					}
					else
					{
						bRmSwithSuccess = true;
						iterSnap7Device->second->m_currPlcStatus = 0x09;
						iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr1;
						iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack1;
						iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot1;
						CV_WARN(g_CVLogSnap7Drv,res,"RM :Device[%s] Switch to Master PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr1.c_str());
					}
				}
			}
			//冗余切换不成功，更新数据块状态直接返回，切换成功代码向下走读取数据块
			if (!bRmSwithSuccess)
			{
				//S7_SyncMultiReadFail(hDevice,hDatablock,iterSnap7Device,vVecdataitem,res);
				vector<vector<TS7DataBlockItem> >::iterator iter =  vVecdataitem->begin(); // 获取所有多读块分组
				for(; iter != vVecdataitem->end(); ++iter)
				{ 
					if(hDatablock == iter->back().hBlock)	//多读组每组最后一个块是主块
					{
						vector<TS7DataBlockItem>::iterator it = iter->begin();
						for(;it != iter->end(); ++it)
						{
							Drv_UpdateBlockStatus(hDevice, it->hBlock, DATA_STATUS_COMM_FAILURE);	// 当前组所有多读块置bad
							CV_ERROR(g_CVLogSnap7Drv,nPlcStatus, "Device[%s] MultiRead datablock[%s] BlockType[%s] XBNumber[%d] start 0 amount %d updateblock failed,plcstatus(0x%x)[%s]",
								iterSnap7Device->second->m_deviceName.c_str(), Drv_GetDataBlockInfo(it->hBlock)->pszName, Drv_GetDataBlockInfo(it->hBlock)->pszBlockType,
								atoi(Drv_GetDataBlockInfo(it->hBlock)->pszParam1), it->ts7DataItem.Amount,nPlcStatus,CliErrorText(nPlcStatus).c_str());
						}
					}
				}
				return ICV_SUCCESS;
			}				
		}	

		vector<vector<TS7DataBlockItem> >::iterator iter =  vVecdataitem->begin(); // 获取所有多读块分组
		for(; iter != vVecdataitem->end(); ++iter)
		{ 
			vector<TS7DataBlockItem>::iterator it = iter->begin();
			for(;it != iter->end(); ++it)
			{
				if(hDatablock == it->hBlock)
				{
					S7_SyncMultiRead(hDevice,iterSnap7Device,*iter);	 // 按照分组多重读
					return ICV_SUCCESS;
				}
			}
		}	
	}

	return ICV_SUCCESS;
}
//****************************************************************************


bool isSingleLinkAndActiveHost(DRVHANDLE hDevice,const char* szConnType)
{
	if(0 == ACE_OS::strcasecmp(szConnType, "tsap"))
	{
		std::map<string, ConnParam*>::iterator iterSnap7Device200;
		{
			ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
			iterSnap7Device200 = g_mapDeviceFor200.find(Drv_GetDeviceInfo(hDevice)->pszName);
		}
		if(iterSnap7Device200 != g_mapDeviceFor200.end())
		{
			if(false == iterSnap7Device200->second->m_bMultiLink)
			{
				if(RM_STATUS_INACTIVE == Drv_IsActiveHost())
				{
					if(iterSnap7Device200->second->Client->Connected())
					{
						iterSnap7Device200->second->Client->Disconnect();
						CV_INFO(g_CVLogSnap7Drv,"Device[%s] multilink = 0 ,RM_STATUS_INACTIVE!,disconnect!",iterSnap7Device200->second->m_deviceName.c_str());	
					}
					return false;
				}
				else
					return true;
			}
			else
				return true;
		}
	}
	else
	{
		std::map<DRVHANDLE, ConnParam*>::iterator iterSnap7Device;
		{
			ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
			iterSnap7Device = g_mapDevice.find(hDevice);
		}
		if(iterSnap7Device != g_mapDevice.end())
		{
			if(false == iterSnap7Device->second->m_bMultiLink)
			{
				if(RM_STATUS_INACTIVE == Drv_IsActiveHost())
				{

					if(iterSnap7Device->second->Client->Connected())
					{
						iterSnap7Device->second->Client->Disconnect();
						CV_INFO(g_CVLogSnap7Drv,"Device[%s] multilink = 0 ,RM_STATUS_INACTIVE!,disconnect!",iterSnap7Device->second->m_deviceName.c_str());	
					}
					return false;
				}
				else
					return true;
			}
			else
				return true;
		}
	}

	return false;
}

int S7_GetPlcStatus(DRVHANDLE hDevice)
{
	if(NULL == hDevice)
		return EC_ICV_DA_INVALID_PARAMETER;

	std::map<string, ConnParam*>::iterator iterSnap7Device200 = g_mapDeviceFor200.find(string(Drv_GetDeviceInfo(hDevice)->pszName)); // 找到当前设备,需要知道连接参数中的client指针
	if(iterSnap7Device200 != g_mapDeviceFor200.end())
	{
		int res = iterSnap7Device200->second->m_currPlcStatus;
		{
			struct timeval tvCurrent;
			os_gettimeofday(&tvCurrent);

			if(tvCurrent.tv_sec >= iterSnap7Device200->second->m_getStatusTime + iterSnap7Device200->second->m_param.PlcStatusPeriodSec)
			{
				//res = iterSnap7Device200->second->Client->PlcStatus();
				res = 0x08;
				//int nCostTime = iterSnap7Device200->second->Client->ExecTime();
				int nCostTime = 0;
				CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] m_currPlcStatus:0x%x, cost %d ms!",iterSnap7Device200->second->m_deviceName.c_str(),res, nCostTime);
				ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
				iterSnap7Device200->second->m_currPlcStatus = res;
				iterSnap7Device200->second->m_getStatusTime = tvCurrent.tv_sec;
			}
		}
	}
	else
	{
		std::map<DRVHANDLE, ConnParam*>::iterator iterSnap7Device = g_mapDevice.find(hDevice);
		if(iterSnap7Device != g_mapDevice.end())
		{
			int res = iterSnap7Device->second->m_currPlcStatus;
			{
				struct timeval tvCurrent;
				os_gettimeofday(&tvCurrent);

				if(tvCurrent.tv_sec >= iterSnap7Device->second->m_getStatusTime + iterSnap7Device->second->m_param.PlcStatusPeriodSec)
				{
					res = iterSnap7Device->second->Client->PlcStatus();
					int nCostTime = iterSnap7Device->second->Client->ExecTime();
					CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] m_currPlcStatus:0x%x, cost %d ms!",iterSnap7Device->second->m_deviceName.c_str(),res, nCostTime);
					ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
					iterSnap7Device->second->m_currPlcStatus = res;
					iterSnap7Device->second->m_getStatusTime = tvCurrent.tv_sec;
				}
			}
		}
	}
	return ICV_SUCCESS;
}

int S7_ConnectToPLC(DRVHANDLE hDevice)
{
	int res = ICV_SUCCESS;
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	if (!pDevice || !pDevice->pszConnParam)
	{
		CV_ERROR(g_CVLogSnap7Drv,-1,"failed to get device information");
		return EC_ICV_DRVCTRL_DEVICE_NOT_FOUND;
	}

	std::map<string, ConnParam*>::iterator iterSnap7Device200;
	{
		ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
		iterSnap7Device200 = g_mapDeviceFor200.find(Drv_GetDeviceInfo(hDevice)->pszName);
	}
	if(iterSnap7Device200 != g_mapDeviceFor200.end())
	{
		if(iterSnap7Device200->second->Client->Connected())
		{
			CV_TRACE(g_CVLogSnap7Drv,"Device:[%s %s] rack:%d slot:%d already connected.",pDevice->pszConnParam,pDevice->pszName,atoi(pDevice->pszParam1),
				atoi(pDevice->pszParam2));
			return ICV_SUCCESS;
		}

		// 小于重连超时则不能重连，避免频繁连接导致plc资源占满
		if(!isReadyToDoReconnect(iterSnap7Device200->second->m_nLasReConnectMS,iterSnap7Device200->second->m_nReConnTimeOutMS))
		{
			CV_TRACE(g_CVLogSnap7Drv,"Device:[%s %s] rack:%d slot:%d not ready to do reconnection.",pDevice->pszConnParam,pDevice->pszName,atoi(pDevice->pszParam1),
				atoi(pDevice->pszParam2));
			return -1;
		}

		res =iterSnap7Device200->second->Client->Connect();
		if(res == 0)
		{
			Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
			TS7CpuInfo cpuInfo = {};
			iterSnap7Device200->second->Client->GetCpuInfo(&cpuInfo);
			const char* szCpuType = cpuInfo.ModuleTypeName;
			iterSnap7Device200->second->m_currPlcStatus = iterSnap7Device200->second->Client->PlcStatus();
			CV_INFO(g_CVLogSnap7Drv,"Connect Success! Plcstatus(0x%x) Device:[%s %s %s] rack:%d slot:%d ",iterSnap7Device200->second->m_currPlcStatus,pDevice->pszConnParam,pDevice->pszName,szCpuType,
				atoi(pDevice->pszParam1),atoi(pDevice->pszParam2));
		}
		else
		{
			Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
			CV_ERROR(g_CVLogSnap7Drv,res,"Connect Failed!Device:[%s %s] rack:%d slot:%d ,Result:[%s]",pDevice->pszConnParam,pDevice->pszName,atoi(pDevice->pszParam1),
				atoi(pDevice->pszParam2),CliErrorText(res).c_str());
		}
		

	}
	else
	{
		std::map<DRVHANDLE, ConnParam*>::iterator iterSnap7Device;
		{
			ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
			iterSnap7Device = g_mapDevice.find(hDevice);
		}
		if(iterSnap7Device != g_mapDevice.end())
		{
			if(iterSnap7Device->second->Client->Connected())
			{
				CV_TRACE(g_CVLogSnap7Drv,"Device:[%s %s] rack:%d slot:%d already connected.",pDevice->pszConnParam,pDevice->pszName,atoi(pDevice->pszParam1),
					atoi(pDevice->pszParam2));
				return ICV_SUCCESS;
			}

			// 小于重连超时则不能重连，避免频繁连接导致plc资源占满
			if(!isReadyToDoReconnect(iterSnap7Device->second->m_nLasReConnectMS,iterSnap7Device->second->m_nReConnTimeOutMS))
			{
				CV_TRACE(g_CVLogSnap7Drv,"Device:[%s %s] rack:%d slot:%d not ready to do reconnection.",pDevice->pszConnParam,pDevice->pszName,atoi(pDevice->pszParam1),
					atoi(pDevice->pszParam2));
				return -1;
			}
			res = iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_param.ConnectAddr1.c_str(),iterSnap7Device->second->m_param.rack1,iterSnap7Device->second->m_param.slot1);
			S7_CheckMasterCPU(res,iterSnap7Device->second);

			if(res == 0)
			{
				Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
				TS7CpuInfo cpuInfo = {};
				iterSnap7Device->second->Client->GetCpuInfo(&cpuInfo);
				const char* szCpuType = cpuInfo.ModuleTypeName;
				iterSnap7Device->second->m_currPlcStatus = iterSnap7Device->second->Client->PlcStatus();
				CV_INFO(g_CVLogSnap7Drv,"Connect Success! Plcstatus(0x%x) Device:[%s %s %s] rack:%d slot:%d ",iterSnap7Device->second->m_currPlcStatus,pDevice->pszConnParam,pDevice->pszName,szCpuType,
					atoi(pDevice->pszParam1),atoi(pDevice->pszParam2));
			}
			else
			{
				Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
				CV_ERROR(g_CVLogSnap7Drv,res,"Connect Failed!Device:[%s %s] rack:%d slot:%d ,Result:[%s]",pDevice->pszConnParam,pDevice->pszName,atoi(pDevice->pszParam1),
					atoi(pDevice->pszParam2),CliErrorText(res).c_str());
			}
		}
	}

	return res;
}

CVDRIVER_EXPORTS long OnDataBlockTimer(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{

	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	char szIP[CONNPRAM_LEN];
	char szConnType[CONNPRAM_LEN];
	char szLocalTsap[CONNPRAM_LEN];
	char szRemoteTsap[CONNPRAM_LEN];
	bool bisMultiLink = true;

	memset(szIP, 0, CONNPRAM_LEN);
	memset(szConnType, 0, CONNPRAM_LEN);
	memset(szLocalTsap, 0, CONNPRAM_LEN);
	memset(szRemoteTsap, 0, CONNPRAM_LEN);

	int cvret = GetConnPram((char*)pDevice->pszConnParam, szIP, szConnType, szLocalTsap, szRemoteTsap, bisMultiLink);
	if(cvret)
	{
		Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_CONFIG_ERROR);	// 当前块置bad
		return DATA_STATUS_CONFIG_ERROR;
	}

	bool bisSingleLinkAndActive = isSingleLinkAndActiveHost(hDevice,szConnType);
	if(false == bisSingleLinkAndActive)
	{
		CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] MultiLink=0,RM_STATUS_INACTIVE!",Drv_GetDeviceInfo(hDevice)->pszName);
		return ICV_SUCCESS;
	}
	
	//do plc connection
	cvret = S7_ConnectToPLC(hDevice);
	if(cvret)
	{
		Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_COMM_FAILURE);	// 当前块置bad
		CV_WARN(g_CVLogSnap7Drv,cvret,"Device[%s]Waiting for Connection!",Drv_GetDeviceInfo(hDevice)->pszName);
		return ICV_SUCCESS;
	}

	//check plcstatus
	S7_GetPlcStatus(hDevice);

	//tsap 为s7 200, 其它的配机架号和槽号
	if(0 == ACE_OS::strcasecmp(szConnType, "tsap"))
	{
		if(isMultiUpDateData(hDevice,hDatablock))
			MultiUpDateData(hDevice,hDatablock,true);
		else
			UpDateData(hDevice,hDatablock,true);
	}
	else//非S7 200 的设备
	{
		if(isMultiUpDateData(hDevice,hDatablock))
			MultiUpDateData(hDevice,hDatablock,false);
		else
			UpDateData(hDevice,hDatablock,false);
	}

	return DRV_SUCCESS;
}

/**
 *  当有控制命令时该函数被调用.
 *  在该函数中根据传递的参数，向设备下发控制命令。
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *  @param	-[in]  DRVHANDLE hDataBlock : [数据块句柄]
 *  @param	-[in]  int nTagDataType : tag点类型
 *  @param  -[in]  int nTagByteOffset: [tag点在块中的字节偏移量]
 *  @param  -[in]  int nTagBitOffset: [字节内的位偏移量]
 *  @param	-[in]  char *szCmdData : [控制指令]
 *  @param  -[in]  int nCmdDataLenBits: [控制数据长度,单位是bit]
 *
  *  @return : 控制执行反馈结果
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDataBlock, int nTagByteOffset, int nTagBitOffset, char *szCmdData, int nCmdDataLenBits)
{
	//TODO：处理控制反馈结果
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	char szIP[CONNPRAM_LEN];
	char szConnType[CONNPRAM_LEN];
	char szLocalTsap[CONNPRAM_LEN];
	char szRemoteTsap[CONNPRAM_LEN];
	bool bisMultiLink = true;

	memset(szIP, 0, CONNPRAM_LEN);
	memset(szConnType, 0, CONNPRAM_LEN);
	memset(szLocalTsap, 0, CONNPRAM_LEN);
	memset(szRemoteTsap, 0, CONNPRAM_LEN);

	int cvret = GetConnPram((char*)pDevice->pszConnParam, szIP, szConnType, szLocalTsap, szRemoteTsap, bisMultiLink);
	if(cvret)
		return DATA_STATUS_CONFIG_ERROR;

	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);

	if (nCmdDataLenBits <= 0 || nCmdDataLenBits/8 > 512)
	{
		return EC_ICV_DA_WRITEMSG_LENGTH_ERROR;
	}

	int LenBytes = 0;
	//回读起始地址
	const int nReadBackStart = nTagByteOffset;

	LenBytes = nCmdDataLenBits/8;
	if(nCmdDataLenBits%8)
		LenBytes++;

	//日志记录控制设备，数据块，位偏移，内容
	char szBuffer[ICV_BLOBVALUE_MAXLEN];
	unsigned int nHexBufferLen = ICV_BLOBVALUE_MAXLEN;
	memset(szBuffer, 0, sizeof(szBuffer));
	cvcommon::HexDumpBuf((unsigned char*)szCmdData,nCmdDataLenBits%BITS_PER_BYTE == 0? nCmdDataLenBits/BITS_PER_BYTE:nCmdDataLenBits/BITS_PER_BYTE+1, szBuffer, &nHexBufferLen);
	CV_INFO(g_CVLogSnap7Drv,"CTRL:Devive[%s] Block[%s] StartAddr[%d] ByteOffset[%d] BitOffset[%d] Write[%s] WriteLen[%d].",
		pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszAddress),nTagByteOffset,nTagBitOffset,szBuffer,LenBytes);

	//do plc connection
	cvret = S7_ConnectToPLC(hDevice);
	if(cvret)
	{
		CV_ERROR(g_CVLogSnap7Drv,cvret,"CTRL:Device[%s] Not Connected,Writing data to PLC Failed!",Drv_GetDeviceInfo(hDevice)->pszName);
		return cvret;
	}

	//s7-200
	if(ACE_OS::strcasecmp(szConnType, "tsap") == 0)
	{
		std::map<string, ConnParam*>::iterator iterSnap7Device200;
		{
			ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
			iterSnap7Device200 = g_mapDeviceFor200.find(string(pDevice->pszName));
			if(iterSnap7Device200 == g_mapDeviceFor200.end())
				return EC_ICV_DEVICE_NOEXIST;
		}

		//冗余判断状态,单机直接写
		if(iterSnap7Device200->second->m_param.m_rmflag)
		{
			cvret = iterSnap7Device200->second->Client->PlcStatus();
			if(cvret != 0x08 && cvret != 0x09 && cvret != 0x0b && cvret != 0xC00000 && cvret != 0x300000)
			{
				CV_ERROR(g_CVLogSnap7Drv, cvret,"CTRL:Device[%s:%s] Status[%s] NOT RUNNING!",pDevice->pszConnParam,pDevice->pszName,CliErrorText(cvret).c_str());
				return DATA_STATUS_DEVICE_FAILURE;
			}
		}

		nTagByteOffset += atoi(pDataBlock->pszAddress);//加上起始地址的偏移

		const char* s1 = pDataBlock->pszBlockType;
		// BOOL
		if(nCmdDataLenBits < 8)
		{
			if(ACE_OS::strcasecmp(s1,"DB")==0||ACE_OS::strcasecmp(s1,"")==0){
				cvret = iterSnap7Device200->second->Client->WriteArea(S7AreaDB, atoi(pDataBlock->pszParam1), nTagByteOffset*8+nTagBitOffset, 1, S7WLBit, szCmdData);
				int nCostTime = iterSnap7Device200->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] Block[%s] DB[%d] Bit Write Success!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),nCostTime);}
				else{
					CV_ERROR(g_CVLogSnap7Drv, cvret,"Device[%s:%s] Block[%s] DB[%d] Bit Write Failed!Result[%s].cost time %d.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);}
			}
			else if(ACE_OS::strcasecmp(s1,"AB")==0){
				cvret = iterSnap7Device200->second->Client->WriteArea(S7AreaPA, NULL , nTagByteOffset*8+nTagBitOffset, 1, S7WLBit, szCmdData);
				int nCostTime = iterSnap7Device200->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] AB[%s] Write Success!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);}
				else{
					CV_ERROR(g_CVLogSnap7Drv, cvret,"Device[%s:%s] AB[%s] Write Failed!Result[%s].cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);}
			}
			else if(ACE_OS::strcasecmp(s1,"EB")==0){
				cvret = iterSnap7Device200->second->Client->WriteArea(S7AreaPE, NULL , nTagByteOffset*8+nTagBitOffset, 1, S7WLBit, szCmdData);
				int nCostTime = iterSnap7Device200->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] AB[%s] Write Success!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);}
				else{
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] AB[%s] Write Failed!Result[%s].cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);}
			}
			else if(ACE_OS::strcasecmp(s1,"MB")==0){
				cvret = iterSnap7Device200->second->Client->WriteArea(S7AreaMK, NULL , nTagByteOffset*8+nTagBitOffset, 1, S7WLBit, szCmdData);
				int nCostTime = iterSnap7Device200->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] AB[%s] Write Success!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);}
				else{
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] AB[%s] Write Failed!Result[%s].cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);}
			}
			else if(ACE_OS::strcasecmp(s1,"TM")==0){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s]:%s TM[%s]Write not support!",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName);
			}
			else if(ACE_OS::strcasecmp(s1,"CT")==0){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s]:%s CT[%s]Write not support!",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName);
			}
			else
				return DATA_STATUS_CONFIG_ERROR;
		}
		else
		{
			//
			if(ACE_OS::strcasecmp(s1,"DB")==0||ACE_OS::strcasecmp(s1,"")==0){
				cvret = iterSnap7Device200->second->Client->DBWrite(atoi(pDataBlock->pszParam1),nTagByteOffset,LenBytes,szCmdData);
				int nCostTime = iterSnap7Device200->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] Block[%s] DB[%d]Write Success!cost Time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),nCostTime);}
				else{
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] Block[%s] DB[%d] Write Failed!Result[%s].cost Time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);}
			}
			else if(ACE_OS::strcasecmp(s1,"AB")==0){
				cvret = iterSnap7Device200->second->Client->ABWrite(nTagByteOffset,LenBytes,szCmdData);
				int nCostTime = iterSnap7Device200->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] AB[%s] Write Success!cost Time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);}
				else{
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] AB[%s] Write Failed!Result[%s].cost Time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);}
			}
			else if(ACE_OS::strcasecmp(s1,"EB")==0){
				cvret = iterSnap7Device200->second->Client->EBWrite(nTagByteOffset,LenBytes,szCmdData);
				int nCostTime = iterSnap7Device200->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] EB[%s] Write Success!cost Time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);}
				else{
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] EB[%s] Write Failed!Result[%s].cost Time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);}
			}
			else if(ACE_OS::strcasecmp(s1,"MB")==0){
				cvret = iterSnap7Device200->second->Client->MBWrite(nTagByteOffset,LenBytes,szCmdData);
				int nCostTime = iterSnap7Device200->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] MB[%s] Write Success!cost Time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);}
				else{
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] MB[%s] Write Failed!Result[%s].cost Time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);}
			}
			else if(ACE_OS::strcasecmp(s1,"TM")==0){
				int nAmount = LenBytes;
				int nStart = nTagByteOffset / 2;
				CV_INFO(g_CVLogSnap7Drv, "TM amount %d, nStart %d.", nAmount, nStart);
				cvret = iterSnap7Device200->second->Client->TMWrite(nStart, nAmount,szCmdData);
				int nCostTime = iterSnap7Device200->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] TM[%s] Write Success!cost Time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);}
				else{
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] TM[%s] Write Failed!Result[%s].cost Time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);}
			}
			else if(ACE_OS::strcasecmp(s1,"CT")==0){
				int cvret = iterSnap7Device200->second->Client->CTWrite(nTagByteOffset,LenBytes,szCmdData);
				int nCostTime = iterSnap7Device200->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] CT[%s] Write Success!cost Time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);}
				else{
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] CT[%s] Write Failed!Result[%s].cost Time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);}
			}
			else
				return DATA_STATUS_CONFIG_ERROR;
		}
	}
	else//S7-300 +的设备
	{
		std::map<DRVHANDLE, ConnParam*>::iterator iterSnap7Device;
		{
			ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
			iterSnap7Device = g_mapDevice.find(hDevice);
			if(iterSnap7Device == g_mapDevice.end())
				return EC_ICV_DEVICE_NOEXIST;
		}

		//冗余判断状态,单机直接写
		if(iterSnap7Device->second->m_param.m_rmflag)
		{
			cvret = iterSnap7Device->second->Client->PlcStatus();
			int nCostTime = iterSnap7Device->second->Client->ExecTime();
			if(cvret != 0x08 && cvret != 0x09 && cvret != 0x0b && cvret != 0xC00000 && cvret != 0x300000) 
			{
				CV_ERROR(g_CVLogSnap7Drv,cvret,"CTRL:Device[%s:%s] Status[%s] NOT RUNNING!PlcStatus cost time %d",pDevice->pszConnParam,pDevice->pszName,CliErrorText(cvret).c_str(),nCostTime);
				if(!S7_SwitchPLCAddr(hDevice,  hDataBlock, iterSnap7Device))	// 冗余切换
					return DATA_STATUS_DEVICE_FAILURE;
			}
		}

		const char* s1 = pDataBlock->pszBlockType;
		// BOOL
		if(nCmdDataLenBits < 8)
		{
			nTagByteOffset += atoi(pDataBlock->pszAddress);//加上起始地址的偏移

			byte Buffer[1];
			if(ACE_OS::strcasecmp(s1,"DB")==0||ACE_OS::strcasecmp(s1,"")==0){
				cvret = iterSnap7Device->second->Client->WriteArea(S7AreaDB, atoi(pDataBlock->pszParam1), nTagByteOffset*8+nTagBitOffset, 1, S7WLBit, szCmdData);
				int nCostTime = iterSnap7Device->second->Client->ExecTime();
				if(!cvret)
				{
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] Block[%s] DB[%d] Bit Write Success!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),nCostTime);
					//read back
					if(iterSnap7Device->second->m_param.m_bReadback)
					{
						cvret = iterSnap7Device->second->Client->DBRead(atoi(pDataBlock->pszParam1),nTagByteOffset,1,Buffer);
						nCostTime = iterSnap7Device->second->Client->ExecTime();
						CV_INFO(g_CVLogSnap7Drv,"Device[%s] Read Back %s, start %d, size 1, cost %d ms!",iterSnap7Device->second->m_deviceName.c_str(),pDataBlock->pszName, nReadBackStart, nCostTime);
						if(!cvret)
							Drv_UpdateBlockData(hDevice, hDataBlock,(const char*)Buffer, nReadBackStart, 1, DATA_STATUS_OK, NULL);
						else
							CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] Block[%s] DB[%d] Bit Read back failed[%s]!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);
					}
				}
				else
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s]  Block[%s] DB[%d] Bit Write Failed!Result[%s].cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);
			}
			else if(ACE_OS::strcasecmp(s1,"AB")==0){
				cvret = iterSnap7Device->second->Client->WriteArea(S7AreaPA, NULL , nTagByteOffset*8+nTagBitOffset, 1, S7WLBit, szCmdData);
				int nCostTime = iterSnap7Device->second->Client->ExecTime();
				if(!cvret)
				{
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] AB[%s] Write Success!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);
					//read back
					if(iterSnap7Device->second->m_param.m_bReadback)
					{
						cvret = iterSnap7Device->second->Client->ABRead(nTagByteOffset,1,Buffer);
						nCostTime = iterSnap7Device->second->Client->ExecTime();
						CV_INFO(g_CVLogSnap7Drv,"Device[%s] Read Back %s, start %d size 1,cost %d ms!",iterSnap7Device->second->m_deviceName.c_str(),pDataBlock->pszName, nReadBackStart, nCostTime);
						if(!cvret)
							Drv_UpdateBlockData(hDevice, hDataBlock,(const char*)Buffer, nReadBackStart, 1, DATA_STATUS_OK, NULL);
						else
							CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] Block[%s] AB[%d] Bit Read back failed[%s]!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);
					}
				}
				else
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] AB[%s] Bit Write Failed!Result[%s].cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);
			}
			else if(ACE_OS::strcasecmp(s1,"EB")==0){
				cvret = iterSnap7Device->second->Client->WriteArea(S7AreaPE, NULL , nTagByteOffset*8+nTagBitOffset, 1, S7WLBit, szCmdData);
				int nCostTime = iterSnap7Device->second->Client->ExecTime();
				if(!cvret)
				{
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] EB[%s] Write Success!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);
					//read back
					if(iterSnap7Device->second->m_param.m_bReadback)
					{
						cvret = iterSnap7Device->second->Client->EBRead(nTagByteOffset,1,Buffer);
						nCostTime = iterSnap7Device->second->Client->ExecTime();
						CV_INFO(g_CVLogSnap7Drv,"Device[%s] Read Back %s, start %d, size 1, cost %d ms!",iterSnap7Device->second->m_deviceName.c_str(),pDataBlock->pszName, nReadBackStart, nCostTime);
						if(!cvret)
							Drv_UpdateBlockData(hDevice, hDataBlock,(const char*)Buffer, nReadBackStart, 1, DATA_STATUS_OK, NULL);
						else
							CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] Block[%s] EB[%d] Bit Read back failed[%s]!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);
					}
				}
				else
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] EB[%s] Write Failed!Result[%s].cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);
			}
			else if(ACE_OS::strcasecmp(s1,"MB")==0){
				cvret = iterSnap7Device->second->Client->WriteArea(S7AreaMK, NULL , nTagByteOffset*8+nTagBitOffset, 1, S7WLBit, szCmdData);
				int nCostTime = iterSnap7Device->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] MB[%s] Write Success!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);
					//read back
					if(iterSnap7Device->second->m_param.m_bReadback)
					{
						cvret = iterSnap7Device->second->Client->MBRead(nTagByteOffset,1,Buffer);
						nCostTime = iterSnap7Device->second->Client->ExecTime();
						CV_INFO(g_CVLogSnap7Drv,"Device[%s] Read Back %s, start %d size 1,cost %d ms!",iterSnap7Device->second->m_deviceName.c_str(),pDataBlock->pszName, nReadBackStart,nCostTime);
						if(!cvret)
							Drv_UpdateBlockData(hDevice, hDataBlock,(const char*)Buffer, nReadBackStart, 1, DATA_STATUS_OK, NULL);
						else
							CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] Block[%s] MB[%d] Bit Read back failed[%s]!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);
					}
				}
				else
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] MB[%s] Write Failed!Result[%s].cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);
			}
			else if(ACE_OS::strcasecmp(s1,"TM")==0){
					CV_INFO(g_CVLogSnap7Drv, "Device[%s:%s] TM[%s]Write not support!", pDevice->pszConnParam, pDevice->pszName, pDataBlock->pszName);
			}
			else if(ACE_OS::strcasecmp(s1,"CT")==0){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] CT[%s]Write not support!",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName);
			}
			else
				return DATA_STATUS_CONFIG_ERROR;
		}
		//
		else
		{
			byte Buffer[2048];
			//
			if(ACE_OS::strcasecmp(pDataBlock->pszParam2, "1") == 0)
			{
				nTagByteOffset = nTagByteOffset/2;
				nTagByteOffset += atoi(pDataBlock->pszAddress);//加上起始地址的偏移
			}
			else
			{
				nTagByteOffset += atoi(pDataBlock->pszAddress);//加上起始地址的偏移
			}
			if(ACE_OS::strcasecmp(s1,"DB")==0||ACE_OS::strcasecmp(s1,"")==0){
				//需要转换
				if(ACE_OS::strcasecmp(pDataBlock->pszParam2, "1") == 0)
				{
					int16  nCmdData16 = 0;
					if(nCmdDataLenBits != 4 * BITS_PER_BYTE)  //只支持4个字节的写入
					{
						return EC_ICV_DA_WRITEMSG_LENGTH_ERROR;
					}
					int32 nCmdData32 = *(int32*)szCmdData;
					nCmdData16 = EncodeBuffer(nCmdData32);
					cvret = iterSnap7Device->second->Client->DBWrite(atoi(pDataBlock->pszParam1), nTagByteOffset, sizeof(int16), &nCmdData16);
					int nCostTime = iterSnap7Device->second->Client->ExecTime();
					if(!cvret){
						CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] Block[%s] DB[%d] Write Success!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),nCostTime);
						//read back
						if(iterSnap7Device->second->m_param.m_bReadback)
						{
							cvret = iterSnap7Device->second->Client->DBRead(atoi(pDataBlock->pszParam1),nTagByteOffset,sizeof(int16),Buffer);
							nCostTime = iterSnap7Device->second->Client->ExecTime();
							CV_INFO(g_CVLogSnap7Drv,"Device[%s] Read Back %s, start %d,size 2, cost %d ms!",iterSnap7Device->second->m_deviceName.c_str(),pDataBlock->pszName, nReadBackStart, nCostTime);
							if(!cvret)
								Drv_UpdateBlockData(hDevice, hDataBlock,(const char*)Buffer, nReadBackStart, sizeof(int16), DATA_STATUS_OK, NULL);
							else
								CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] Block[%s] DB[%d] Bit Read back failed[%s]!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);
						}
					}
					else
						CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] DB[%d] Write Failed!Result[%s].cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);					
				}
				else
				{
					cvret = iterSnap7Device->second->Client->DBWrite(atoi(pDataBlock->pszParam1),nTagByteOffset,LenBytes,szCmdData);
					int nCostTime = iterSnap7Device->second->Client->ExecTime();
					if(!cvret){
						CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] DB[%d] Write Success!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,atoi(pDataBlock->pszParam1),nCostTime);
						//read back
						if(iterSnap7Device->second->m_param.m_bReadback)
						{
							cvret = iterSnap7Device->second->Client->DBRead(atoi(pDataBlock->pszParam1),nTagByteOffset,LenBytes,Buffer);
							nCostTime = iterSnap7Device->second->Client->ExecTime();
							CV_INFO(g_CVLogSnap7Drv,"Device[%s] Read Back %s, start %d, size %d, cost %d ms!",iterSnap7Device->second->m_deviceName.c_str(),pDataBlock->pszName, nReadBackStart, LenBytes, nCostTime);
							if(!cvret)
								Drv_UpdateBlockData(hDevice, hDataBlock,(const char*)Buffer, nReadBackStart, LenBytes, DATA_STATUS_OK, NULL);
							else
								CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] Block[%s] DB[%d] Read back failed[%s]!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);
						}
					}
					else
						CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] DB[%d] Write Failed!Result[%s].cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);
				}
			}
			else if(ACE_OS::strcasecmp(s1,"AB")==0){
				cvret = iterSnap7Device->second->Client->ABWrite(nTagByteOffset,LenBytes,szCmdData);
				int nCostTime = iterSnap7Device->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] AB[%s] Write Success!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);
					//read back
					if(iterSnap7Device->second->m_param.m_bReadback)
					{
						cvret = iterSnap7Device->second->Client->ABRead(nTagByteOffset,LenBytes,Buffer);
						nCostTime = iterSnap7Device->second->Client->ExecTime();
						CV_INFO(g_CVLogSnap7Drv,"Device[%s] Read Back %s, start %d size %d cost %d ms!",iterSnap7Device->second->m_deviceName.c_str(),pDataBlock->pszName, nReadBackStart, LenBytes, nCostTime);
						if(!cvret)
							Drv_UpdateBlockData(hDevice, hDataBlock,(const char*)Buffer, nReadBackStart, LenBytes, DATA_STATUS_OK, NULL);
						else
							CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] Block[%s] AB[%d] Read back failed[%s]!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);
					}
				}
				else
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] AB[%s] Write Failed!Result[%s].cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);
			}
			else if(ACE_OS::strcasecmp(s1,"EB")==0){
				cvret = iterSnap7Device->second->Client->EBWrite(nTagByteOffset,LenBytes,szCmdData);
				int nCostTime = iterSnap7Device->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] EB[%s] Write Success!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);
					//read back
					if(iterSnap7Device->second->m_param.m_bReadback)
					{
						cvret = iterSnap7Device->second->Client->EBRead(nTagByteOffset,LenBytes,Buffer);
						nCostTime = iterSnap7Device->second->Client->ExecTime();
						CV_INFO(g_CVLogSnap7Drv,"Device[%s] Read Back %s, start %d, size %d, cost %d ms!",iterSnap7Device->second->m_deviceName.c_str(),pDataBlock->pszName, nReadBackStart, LenBytes,nCostTime);
						if(!cvret)
							Drv_UpdateBlockData(hDevice, hDataBlock,(const char*)Buffer, nReadBackStart, LenBytes, DATA_STATUS_OK, NULL);
						else
							CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] Block[%s] EB[%d] Read back failed[%s]!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);
					}
				}
				else
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] EB[%s] Write Failed!Result[%s].cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);
			}
			else if(ACE_OS::strcasecmp(s1,"MB")==0){
				cvret = iterSnap7Device->second->Client->MBWrite(nTagByteOffset,LenBytes,szCmdData);
				int nCostTime = iterSnap7Device->second->Client->ExecTime();
				if(!cvret){
					CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] MB[%s] Write Success!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,nCostTime);
					//read back
					if(iterSnap7Device->second->m_param.m_bReadback)
					{
						cvret = iterSnap7Device->second->Client->MBRead(nTagByteOffset,LenBytes,Buffer);
						nCostTime = iterSnap7Device->second->Client->ExecTime();
						CV_INFO(g_CVLogSnap7Drv,"Device[%s] Read Back %s, start %d, size %d, cost %d ms!",iterSnap7Device->second->m_deviceName.c_str(),pDataBlock->pszName, nReadBackStart, LenBytes, nCostTime);
						if(!cvret)
							Drv_UpdateBlockData(hDevice, hDataBlock,(const char*)Buffer, nReadBackStart, LenBytes, DATA_STATUS_OK, NULL);
						else
							CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] Block[%s] MB[%d] Read back failed[%s]!cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,atoi(pDataBlock->pszParam1),CliErrorText(cvret).c_str(),nCostTime);
					}
				}
				else
					CV_ERROR(g_CVLogSnap7Drv,cvret,"Device[%s:%s] MB[%s] Write Failed!Result[%s].cost time %d ms.",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName,CliErrorText(cvret).c_str(),nCostTime);
			}
			else if(ACE_OS::strcasecmp(s1,"TM")==0){
			int nAmount = LenBytes;
			int nStart = nTagByteOffset / 2;
			CV_INFO(g_CVLogSnap7Drv, "TM amount %d, nStart %d.", nAmount, nStart);
				cvret = iterSnap7Device->second->Client->TMWrite(nStart, nAmount, szCmdData);
				int nCostTime = iterSnap7Device->second->Client->ExecTime();
				if (!cvret) {
					CV_INFO(g_CVLogSnap7Drv, "Device[%s:%s] TM[%s] Write Success!cost time %d ms.", pDevice->pszConnParam, pDevice->pszName, pDataBlock->pszName, nCostTime);
					//read back
					if (iterSnap7Device->second->m_param.m_bReadback)
					{
						cvret = iterSnap7Device->second->Client->TMRead(nTagByteOffset, LenBytes, Buffer);
						nCostTime = iterSnap7Device->second->Client->ExecTime();
						CV_INFO(g_CVLogSnap7Drv, "Device[%s] Read Back %s, start %d, size %d, cost %d ms!", iterSnap7Device->second->m_deviceName.c_str(), pDataBlock->pszName, nReadBackStart, LenBytes, nCostTime);
						if (!cvret)
							Drv_UpdateBlockData(hDevice, hDataBlock, (const char*)Buffer, nReadBackStart, LenBytes, DATA_STATUS_OK, NULL);
						else
							CV_ERROR(g_CVLogSnap7Drv, cvret, "Device[%s:%s] Block[%s] TM[%d] Read back failed[%s]!cost time %d ms.", pDevice->pszConnParam, pDevice->pszName, pDataBlock->pszName, atoi(pDataBlock->pszParam1), CliErrorText(cvret).c_str(), nCostTime);
					}
				}
				else
					CV_ERROR(g_CVLogSnap7Drv, cvret, "Device[%s:%s] TM[%s] Write Failed!Result[%s].cost time %d ms.", pDevice->pszConnParam, pDevice->pszName, pDataBlock->pszName, CliErrorText(cvret).c_str(), nCostTime);
			}
			else if(ACE_OS::strcasecmp(s1,"CT")==0){
				CV_INFO(g_CVLogSnap7Drv,"Device[%s:%s] CT[%s]Write not support!",pDevice->pszConnParam,pDevice->pszName,pDataBlock->pszName);
			}
			else
				return DATA_STATUS_CONFIG_ERROR;
		}
	}

	return DRV_SUCCESS;
}

long ConnParam::CheckRmConnParam(string ipaddr,string rack,string slot,string others)
{
 	others+=";";
	// ip address e.g. 127.0.0.1/********
	int nPos = ipaddr.find('/');
	if(nPos != string::npos)
	{
		string ConnAddr1 = ipaddr.substr(0, nPos);
		m_param.ConnectAddr1 = ConnAddr1;
		int ipaddrLen = ipaddr.length();
		string ConnAddr2 = ipaddr.substr(nPos+1,ipaddrLen - nPos);
		m_param.ConnectAddr2 = ConnAddr2;
	}	
	else
	{
		string ConnAddr1 = ipaddr.substr(0, nPos);
		m_param.ConnectAddr1 = ConnAddr1;
		m_param.ConnectAddr2 = "";
	}
	//  Rack e.g. 0/1
	int nPos1 = rack.find('/');
	if(nPos1 != string::npos)
	{
		string rack1 = rack.substr(0, nPos1);
		m_param.rack1 = atoi(rack1.c_str());
		int rackLen = rack.length();
		string rack2 = rack.substr(nPos1+1,rackLen - nPos1);
		m_param.rack2 = atoi(rack2.c_str());
	}
	else
	{
		m_param.rack1 = atoi(rack.c_str());
		m_param.rack2 = NULL;
	}
	//  Slot e.g. 2/4
	int nPos2 = slot.find('/');
	if(nPos2 != string::npos)
	{
		string slot1 = slot.substr(0, nPos2);
		m_param.slot1 = atoi(slot1.c_str());
		int slotLen = slot.length();
		string slot2 = slot.substr(nPos2+1,slotLen - nPos2);
		m_param.slot2 = atoi(slot2.c_str());
	}
	else
	{
		m_param.slot1 = atoi(slot.c_str());
		m_param.slot2 = NULL;
	}
	//other params init
	m_param.HisCheckCpuStatus = false;
	m_param.PlcStatusPeriodSec = 1;
	m_param.HcpuCycleRate = 1000;
	m_param.HcpuFailureCnt = 1;
	m_param.m_isMultiRead = false;
	m_param.m_PDUSize = 480;
	m_param.m_bReadback = false;

	// ry params
	int nPos3cpu= others.find("hcheckcpu=");
	if(nPos3cpu != string::npos)
	{
		string strHcheckcpu = others.substr(nPos3cpu + 10,others.length() - 10);
		int nP = strHcheckcpu.find(';');
		if(nP != string::npos)
			m_param.HisCheckCpuStatus = atoi(strHcheckcpu.substr(0,nP).c_str()) == 0 ? false : true;
	}
	int nPos3 = others.find("hcyclerate=");
	if(nPos3 != string::npos)
	{
		string strHcpuCycleRate = others.substr(nPos3 + 11,others.length() - 11);
		int nP = strHcpuCycleRate.find(';');
		if(nP != string::npos)
			m_param.HcpuCycleRate = atoi(strHcpuCycleRate.substr(0,nP).c_str());		
	}
	int nPos4 = others.find("hcount=");
	if(nPos4 != string::npos)
	{
		string strHcpuFailureCnt = others.substr(nPos4 + 7,others.length() - 7);
		int nP = strHcpuFailureCnt.find(';');
		if(nP != string::npos)
			m_param.HcpuFailureCnt = atoi(strHcpuFailureCnt.substr(0,nP).c_str());	
	}
	if(nPos != string::npos && nPos1 != string::npos && nPos2 != string::npos)
		m_param.m_rmflag = true;
	else
		m_param.m_rmflag = false;

	//muiltread params
	int nPos5 = others.find("ismultiread=");
	if(nPos5!= string::npos)
	{
		string stothersultiRead = others.substr(nPos5 + 12,others.length() - 12);
		int nP = stothersultiRead.find(';');
		if(nP != string::npos)
			m_param.m_isMultiRead = atoi(stothersultiRead.substr(0,nP).c_str()) == 0 ? false : true;
	}
	int nPos6 = others.find("pdusize=");
	if(nPos6 != string::npos)
	{
		string strotherspdusize = others.substr(nPos6 + 8,others.length() - 8);
		int nP = strotherspdusize.find(';');
		if(nP != string::npos)
			m_param.m_PDUSize = atoi(strotherspdusize.substr(0,nP).c_str());
	}
	int nPos7 = others.find("readafterwrite=");
	if(nPos7 != string::npos)
	{
		string strothersreadback = others.substr(nPos7 + 15,others.length() - 15);
		int nP = strothersreadback.find(';');
		if(nP != string::npos)
			m_param.m_bReadback = atoi(strothersreadback.substr(0,nP).c_str()) == 0 ? false : true;
	}
	int nPos8 = others.find("reconnectinterval=");
	if(nPos8 != string::npos)
	{
		string strothersreadback = others.substr(nPos8 + 18,others.length() - 18);
		int nP = strothersreadback.find(';');
		if(nP != string::npos)
			m_nReConnTimeOutMS = atoi(strothersreadback.substr(0,nP).c_str());
	}
	int nPosStatus = others.find("plcstatusperiod=");
	if(nPosStatus != string::npos)
	{
		string strPlcStatusPeriodMS = others.substr(nPosStatus + 16,others.length() - 16);
		int nP = strPlcStatusPeriodMS.find(';');
		if(nP != string::npos)
		{
			int ntempPeriod =  atoi(strPlcStatusPeriodMS.substr(0,nP).c_str());
			if(ntempPeriod <= 1000)
				m_param.PlcStatusPeriodSec =1;
			else
				m_param.PlcStatusPeriodSec = ntempPeriod/1000;
		}
	}
	else
	{
		if(!m_param.m_rmflag)
			m_param.PlcStatusPeriodSec =3600;// 单机默认3600s检测一次plcstatus
	}
	return ICV_SUCCESS;
}


// CVDRIVER_EXPORTS long OnHeartBeat()
// {
// 	map<DRVHANDLE, ConnParam*>::iterator iterSnap7Device = g_mapDevice.begin();
// 
// 	for ( ;iterSnap7Device != g_mapDevice.end();iterSnap7Device++)
// 	{
// 		if(iterSnap7Device->second->m_param.m_rmflag == false)
// 		{
// 			continue;
// 		}
// 		CV_TRACE(g_CVLogSnap7Drv, "Deivce[%s] Enter OnHeartBeat.%d times",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_heartbeatCount);
// 		iterSnap7Device->second->m_heartbeatCount ++;
// 		if (iterSnap7Device->second->m_heartbeatCount < iterSnap7Device->second->m_heartbeatPeriodMS)
// 		{
// 			continue;
// 		}
// 
// 		iterSnap7Device->second->m_heartbeatCount = 0;
// 
// 		if(iterSnap7Device->second->m_param.m_rmflag == true)		//	冗余设备
// 		{	
// 			std::string strSubKeyPrefix = SNAP7_DRIVE_NAME;
// 			strSubKeyPrefix += "#";
// 			strSubKeyPrefix += iterSnap7Device->second->m_deviceName;
// 
// 			//static int s_nPlcStatusCnt = 0;
// 
// // 			if(false == iterSnap7Device->second->Client->Connected()) // 先判断是否已连接
// // 			{
// // 				iterSnap7Device->second->m_rmPlcStatusCnt = 0;
// // 				iterSnap7Device->second->Client->Disconnect();
// // 
// // 				if(iterSnap7Device->second->m_currPlcIP == iterSnap7Device->second->m_param.ConnectAddr1)
// // 				{
// // 
// // 				}
// // 				int res = iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_param.ConnectAddr2.c_str(),
// // 					iterSnap7Device->second->m_param.rack2,iterSnap7Device->second->m_param.slot2);
// // 
// // 				iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr2;
// // 				iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack2;
// // 				iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot2;
// // 				CV_WARN(g_CVLogSnap7Drv,res,"RM :Device[%s] Switch to Master PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr1.c_str());
// // 				if(ICV_SUCCESS != res)
// // 				{
// // 					iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_param.ConnectAddr1.c_str(),
// // 					iterSnap7Device->second->m_param.rack1,iterSnap7Device->second->m_param.slot1);
// // 					iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr1;
// // 					iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack1;
// // 					iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot1;
// // 					CV_WARN(g_CVLogSnap7Drv,res,"RM :Device[%s] Switch to Slave PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr2.c_str());
// // 				}
// // 				//continue;
// // 			}
// 			
// 			int nPlcStatus,nHCpuStatus = 0;	
// 			nPlcStatus = iterSnap7Device->second->Client->PlcStatus();
// // 			if(true == iterSnap7Device->second->m_param.HisCheckCpuStatus)
// // 			{
// // 				nHCpuStatus = iterSnap7Device->second->Client->HCpuStatus();
// // 				CV_DEBUG(g_CVLogSnap7Drv, "Device[%s] nHCpuStatus 0x%x nPlcStatus 0x%x",iterSnap7Device->second->m_deviceName.c_str(),nHCpuStatus,nPlcStatus);
// // 			}
// // 			if(0x300000 == nPlcStatus || 0x300000 == nHCpuStatus)  // job pending 
// // 			{
// // 				continue;
// // 			}
// 			//ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
// 			// PLC status
// 			if(nPlcStatus != 0x09 && nPlcStatus != 0x08 && nPlcStatus != 0x0b)	
// 			{
// 				iterSnap7Device->second->m_rmPlcStatusCnt++;
// 				CV_TRACE(g_CVLogSnap7Drv,"RM:Device[%s] CNT[%d] PLCStatus(0x%x)[%s]",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_rmPlcStatusCnt,nPlcStatus,CliErrorText(nPlcStatus).c_str());
// 				if(iterSnap7Device->second->m_rmPlcStatusCnt >= iterSnap7Device->second->m_param.HcpuFailureCnt)
// 				{
// 					CV_ERROR(g_CVLogSnap7Drv,-1 ,"RM:Device[%s] CNT[%d] PLCStatus(0x%x)[%s] NOT RUNNING!!!",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_rmPlcStatusCnt,nPlcStatus,CliErrorText(nPlcStatus).c_str());
// 					Drv_UpdateDevStatus(iterSnap7Device->first, DEV_STATUS_BAD);
// 					iterSnap7Device->second->m_rmPlcStatusCnt = 0;
// 					iterSnap7Device->second->m_rmPlcStatus = 1;
// 					//iterSnap7Device->second->Client->Disconnect();
// 					int res;
// 					if(iterSnap7Device->second->m_currPlcIP == iterSnap7Device->second->m_param.ConnectAddr1)
// 					{
// 						res= iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_param.ConnectAddr2.c_str(),iterSnap7Device->second->m_param.rack2,iterSnap7Device->second->m_param.slot2);
// 						iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr2;
// 						iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack2;
// 						iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot2;
// 						CV_WARN(g_CVLogSnap7Drv,res,"RM :Device[%s] Switch to Slave PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr2.c_str());
// 					}
// 					else
// 					{
// 						iterSnap7Device->second->Client->ConnectTo(iterSnap7Device->second->m_param.ConnectAddr1.c_str(),iterSnap7Device->second->m_param.rack1,iterSnap7Device->second->m_param.slot1);
// 						iterSnap7Device->second->m_currPlcIP = iterSnap7Device->second->m_param.ConnectAddr1;
// 						iterSnap7Device->second->m_currPlcRack = iterSnap7Device->second->m_param.rack1;
// 						iterSnap7Device->second->m_currPlcSlot = iterSnap7Device->second->m_param.slot1;
// 						CV_WARN(g_CVLogSnap7Drv,res,"RM :Device[%s] Switch to Master PLC[%s].",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_param.ConnectAddr1.c_str());
// 					}
// 				}
// 			}
// 			else
// 			{
// 				iterSnap7Device->second->m_rmPlcStatusCnt = 0;
// 				iterSnap7Device->second->m_rmPlcStatus = 0;
// 				Drv_UpdateDevStatus(iterSnap7Device->first, DEV_STATUS_GOOD);
// 				CV_INFO(g_CVLogSnap7Drv,"RM:Device[%s] CNT[%d] PLCStatus(0x%x) is RUNNING!!!",iterSnap7Device->second->m_deviceName.c_str(),iterSnap7Device->second->m_rmPlcStatusCnt,nPlcStatus);
// 			}
// 
// 			if(false == iterSnap7Device->second->m_param.HisCheckCpuStatus)
// 				continue;
// 
// 			// HCPU status
// // 			if(nHCpuStatus == HCPU_MAIN )	//   rack 0  rack 1:10
// // 			{
// // 				iterSnap7Device->second->m_nHcpuStatusCnt = 0;
// // 				iterSnap7Device->second->m_rmPlcHcpuStatus = 0;
// // 				iterSnap7Device->second->m_bslave = false;
// // 
// // 				//write to redis
// // 				drv_redis_batchbegin();
// // 				drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_CONDEVICE, "0");
// // 				drv_redis_batchsubmit();
// // 
// // 				CV_INFO(g_CVLogSnap7Drv,"Master Device[%s] HCpuStatus OK!!!",iterSnap7Device->second->m_param.ConnectAddr1.c_str());
// // 			}
// // 			else if(nHCpuStatus == HCPU_SLAVE ) //  rack0 rack1 :01
// // 			{
// // 				iterSnap7Device->second->m_nHcpuStatusCnt = 0;
// // 				iterSnap7Device->second->m_rmPlcHcpuStatus = 1;
// // 				iterSnap7Device->second->m_bmaster = false;
// // 
// // 				//write to redis
// // 				drv_redis_batchbegin();
// // 				drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_CONDEVICE, "1");
// // 				drv_redis_batchsubmit();
// // 
// // 				CV_INFO(g_CVLogSnap7Drv,"Slave Device[%s] HCpuStatus OK!!!",iterSnap7Device->second->m_param.ConnectAddr2.c_str());	
// // 			}
// // 			else
// // 			{				
// // 				iterSnap7Device->second->m_nHcpuStatusCnt++;
// // 				CV_INFO(g_CVLogSnap7Drv,"Device[%s] nHCpuStatus(%d)(%d):[%s]",iterSnap7Device->second->m_deviceName.c_str(),nHCpuStatus,iterSnap7Device->second->m_nHcpuStatusCnt,CliErrorText(nHCpuStatus).c_str());
// // 				if(iterSnap7Device->second->m_nHcpuStatusCnt>= iterSnap7Device->second->m_param.HcpuFailureCnt )
// // 				{
// // 					iterSnap7Device->second->m_nHcpuStatusCnt = 0;
// // 					iterSnap7Device->second->m_rmPlcHcpuStatus = 2;	
// // 
// // 					//write to redis
// // 					Drv_UpdateDevStatus(iterSnap7Device->first, DEV_STATUS_BAD);
// // 					drv_redis_batchbegin();
// // 					drv_redis_batchaddvalue(strSubKeyPrefix.c_str(), DRV_REDIS_DEVICE_CONDEVICE, "2");
// // 					drv_redis_batchsubmit();
// // 					CV_ERROR(g_CVLogSnap7Drv,0,"Device[%s] nHCpuStatus(0x%x):[%s] abnormal...try to reconnect current device!!!",iterSnap7Device->second->m_deviceName.c_str(),nHCpuStatus,CliErrorText(nHCpuStatus).c_str());
// // 				}					
// // 			}		
// // 			iterSnap7Device->second->m_heartbeatCount = 0;
// 		}
// 	}
// 	return DRV_SUCCESS;
// }


int S7_CheckMasterCPU(int res, ConnParam* Cli)
{
	if(Cli->m_param.m_rmflag)
	{
		//if(res == 0)
		{
			if(HCPU_MAIN == Cli->Client->HCpuStatus())
			{
				 Cli->m_currPlcIP = Cli->m_param.ConnectAddr1;
				 Cli->m_currPlcRack = Cli->m_param.rack1;
				 Cli->m_currPlcSlot = Cli->m_param.slot1;
				 CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] current connected PLC is %s",Cli->m_deviceName.c_str(),Cli->m_param.ConnectAddr1.c_str());
				return ICV_SUCCESS;
			}
			Cli->Client->Disconnect();
			Cli->Client->ConnectTo(Cli->m_param.ConnectAddr2.c_str(),Cli->m_param.rack2, Cli->m_param.slot2);
			Cli->m_currPlcIP = Cli->m_param.ConnectAddr2;
			Cli->m_currPlcRack = Cli->m_param.rack2;
			Cli->m_currPlcSlot = Cli->m_param.slot2;
			CV_DEBUG(g_CVLogSnap7Drv,"Device[%s] current connected PLC is %s",Cli->m_deviceName.c_str(),Cli->m_param.ConnectAddr2.c_str());
		}
	}
	return ICV_SUCCESS;
}

/**
 *  添加设备时该函数被调用.
 *  该函数主要针对非tcp连接设备，用户可以通过设备句柄获取设备连接参数，初始化连接设备
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
	Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);

	char szIP[CONNPRAM_LEN];
	char szConnType[CONNPRAM_LEN];
	char szLocalTsap[CONNPRAM_LEN];
	char szRemoteTsap[CONNPRAM_LEN];
	bool bisMultiLink = true;

	memset(szIP, 0, CONNPRAM_LEN);
	memset(szConnType, 0, CONNPRAM_LEN);
	memset(szLocalTsap, 0, CONNPRAM_LEN);
	memset(szRemoteTsap, 0, CONNPRAM_LEN);


	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	if (!pDevice || !pDevice->pszConnParam)
	{
		CV_ERROR(g_CVLogSnap7Drv,-1,"failed to get device information");
		return EC_ICV_DRVCTRL_DEVICE_NOT_FOUND;
	}

	CV_INFO(g_CVLogSnap7Drv, "OnDeviceAdd, device name %s , connParam %s", pDevice->pszName, pDevice->pszConnParam);
	int cvret = GetConnPram((char*)pDevice->pszConnParam, szIP, szConnType, szLocalTsap, szRemoteTsap, bisMultiLink);
	if(cvret)
		return DATA_STATUS_CONFIG_ERROR;
			
	if(0 == ACE_OS::strcasecmp(szConnType, "tsap"))
	{
		std::map<string, ConnParam*>::iterator iterSnap7Device200;
		{
			ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
			iterSnap7Device200 = g_mapDeviceFor200.find(string(pDevice->pszName));
			if(iterSnap7Device200 != g_mapDeviceFor200.end())
			{
				return ICV_SUCCESS;
			}
		}
	}
	else
	{
		std::map<DRVHANDLE, ConnParam*>::iterator iterSnap7Device;
		{
			ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
			iterSnap7Device = g_mapDevice.find(hDevice);
			if(iterSnap7Device != g_mapDevice.end())
			{
				return ICV_SUCCESS;
			}
		}
	}

	int res = 0;
	//init connection m_params
	ConnParam *Cli = new ConnParam();
	Cli->m_currPlcStatus = 0x08;
	Cli->m_deviceName  = string(pDevice->pszName);
	Cli->m_nPlcReadBlockFailedCnt = 0;
	Cli->m_bMultiLink = bisMultiLink;
	Cli->m_nLasReConnectMS = 0;
	Cli->m_nReConnTimeOutMS =500;
	Cli->CheckRmConnParam(std::string(szIP),std::string(pDevice->pszParam1),std::string(pDevice->pszParam2),std::string(pDevice->pszParam3));

	//init PLC Client connection Params
	Cli->Client = new TS7Client;
	int32 nSendTimeoutMs = 300;
	int32 nRecvTimeoutMs = pDevice->nRecvTimeout;
	int32 nPingTimeoutMs = nSendTimeoutMs + nRecvTimeoutMs +200;
	//int32 nPDUSizeRequest = Cli->m_param.m_PDUSize;
	Cli->Client->SetParam(p_i32_SendTimeout,&nSendTimeoutMs);
	Cli->Client->SetParam(p_i32_RecvTimeout,&nRecvTimeoutMs);
	Cli->Client->SetParam(p_i32_PingTimeout,&nPingTimeoutMs);
	//Cli->Client->SetParam(p_i32_PDURequest,&nPDUSizeRequest);
	CV_INFO(g_CVLogSnap7Drv, "device[%s] nSendTimeoutMs[%d] nRecvTimeoutMs[%d] nPingTimeoutMs[%d]", pDevice->pszName, nSendTimeoutMs, nRecvTimeoutMs, nPingTimeoutMs);
	Cli->m_getStatusTime = 0;
	if (0 == ACE_OS::strcasecmp(szConnType, "tsap"))
	{
		word nLocalTsap, nRemoteTsap;
		sscanf(szLocalTsap,"%i",&nLocalTsap);
		sscanf(szRemoteTsap,"%i",&nRemoteTsap);
		Cli->m_strConnType = std::string(szConnType);
		Cli->Client->SetConnectionParams(szIP, nLocalTsap, nRemoteTsap);
	}
	else if(0 == ACE_OS::strcasecmp(szConnType, "pg"))
	{
		 Cli->Client->SetConnectionType(CONNTYPE_PG);
		 Cli->m_strConnType = std::string(szConnType);
	}
	else if(0 == ACE_OS::strcasecmp(szConnType, "op"))
	{
		Cli->Client->SetConnectionType(CONNTYPE_OP);
		Cli->m_strConnType = std::string(szConnType);
	}
	else if(0 == ACE_OS::strcasecmp(szConnType, "basic"))
	{
		Cli->Client->SetConnectionType(CONNTYPE_BASIC);
		Cli->m_strConnType = std::string(szConnType);
	}
	else
	{
		Cli->Client->SetConnectionType(CONNTYPE_OP);
		Cli->m_strConnType = std::string(szConnType);
	}

	if(0 == ACE_OS::strcasecmp(szConnType, "tsap"))
	{
		ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
		g_mapDeviceFor200.insert(make_pair(string(pDevice->pszName), Cli));	
	}
	else
	{	
		ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
		g_mapDevice.insert(make_pair(hDevice, Cli));	
	}
	return DRV_SUCCESS;
}

/**
 *  删除设备时该函数被调用.
 *  该函数主要针对非tcp连接设备，用户可以通过设备句柄获取设备信息，处理断开设备、释放相关资源等操作
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
	Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
	//TODO：对于非tcp设备处理设备断开等操作
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	if (!pDevice || !pDevice->pszConnParam)
	{
		CV_ERROR(g_CVLogSnap7Drv,-1,"failed to get device information");
		return EC_ICV_DRVCTRL_DEVICE_NOT_FOUND;
	}
	CV_INFO(g_CVLogSnap7Drv, "OnDeviceDelete, device name %s , connParam %s", pDevice->pszName, pDevice->pszConnParam);
	//set block's quality bad 
	DRVHANDLE hTargetDB = NULL;
	for(std::map<DRVHANDLE, CVDATABLOCK*>::iterator itDB = g_mapDataBlocks[hDevice].begin(); itDB != g_mapDataBlocks[hDevice].end(); ++itDB)
	{
		hTargetDB = itDB->first;
		Drv_UpdateBlockStatus(hDevice, itDB->first, DATA_STATUS_DEVICE_FAILURE);
	}

	//
	std::map<string, ConnParam*>::iterator iterDevice200 = g_mapDeviceFor200.find(string(pDevice->pszName));
	{
		ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
		if(iterDevice200 != g_mapDeviceFor200.end())
		{
			iterDevice200->second->Client->Disconnect();
			CV_WARN(g_CVLogSnap7Drv,0,"Device %s Disconnect!",iterDevice200->second->m_deviceName.c_str());
			delete iterDevice200->second;
			g_mapDeviceFor200.erase(iterDevice200);
		}
	}

	std::map<DRVHANDLE, ConnParam*>::iterator iterDevice = g_mapDevice.find(hDevice);
	{
		ACE_GUARD_RETURN(ACE_Recursive_Thread_Mutex, mon, g_mutexConnection, EC_ICV_DA_GUARD_RETURN);
		if(iterDevice != g_mapDevice.end())
		{
			iterDevice->second->Client->Disconnect();
			CV_WARN(g_CVLogSnap7Drv,0,"Device %s Disconnect!",iterDevice->second->m_deviceName.c_str());
			delete iterDevice->second;
			g_mapDevice.erase(iterDevice);
		}
	}

	return DRV_SUCCESS;
}


/**
 *  添加数据块时该函数被调用.
 *  用户需要在该函数中返回数据块大小
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *  @param  -[in]  DRVHANDLE hDataBlock: [数据块句柄]
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
    Drv_UpdateBlockStatus(hDevice, hDataBlock, DATA_STATUS_DEVICE_FAILURE);

	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
	if (!pDevice || !pDevice->pszConnParam || !pDataBlock)
	{
		CV_ERROR(g_CVLogSnap7Drv,-1,"failed to get device information");
		return EC_ICV_DRVCTRL_DEVICE_NOT_FOUND;
	}
	CV_INFO(g_CVLogSnap7Drv, "OnDataBlockAdd, device name %s , connParam %s datablockname %s", 
		pDevice->pszName, pDevice->pszConnParam, pDataBlock->pszName);

	if(g_snapdrvCfg.is_open())
	{
		g_snapdrvCfg <<"device:"<< pDevice->pszName <<":"<< hDevice << "\tblockname:\t"<< pDataBlock->pszName<<"\tblocktype:\t"<<pDataBlock->pszBlockType<<"\tblockno:\t" << pDataBlock->pszParam1
			<<"\tblockaddr:\t"<< pDataBlock->pszAddress<<"\t\tblocksize:\t" <<pDataBlock->nBlockDataSize <<"\tcyclerate:\t"<< pDataBlock->nCycleRate << endl;
	}

	//存储设备以及数据块配置信息
	std::map<DRVHANDLE, std::map<DRVHANDLE, CVDATABLOCK*> >::iterator it = g_mapDataBlocks.find(hDevice);
	if (it != g_mapDataBlocks.end())
	{
		it->second.insert(make_pair(hDataBlock, pDataBlock));
	}
	else
	{
		std::map<DRVHANDLE, CVDATABLOCK*> mapDBs;
		mapDBs.insert(make_pair(hDataBlock, pDataBlock));
		g_mapDataBlocks.insert(make_pair(hDevice, mapDBs));
	}

	return DRV_SUCCESS;
}

/**
 *  删除数据块时该函数被调用.
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *  @param  -[in]  DRVHANDLE hDataBlock: [数据块句柄]
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
	if (!pDevice || !pDevice->pszConnParam || !pDataBlock)
	{
		CV_ERROR(g_CVLogSnap7Drv,-1,"failed to get device information");
		return EC_ICV_DRVCTRL_DEVICE_NOT_FOUND;
	}

	CV_INFO(g_CVLogSnap7Drv, "OnDataBlockDelete, device name %s , connParam %s datablockname %s", 
		pDevice->pszName, pDevice->pszConnParam, pDataBlock->pszName);

	Drv_UpdateBlockStatus(hDevice, hDataBlock, DATA_STATUS_DEVICE_FAILURE); 

	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long TagsToGroupsEx(const TagInfo *pDevTags, int nTagsNum,
	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum,
	DRVHANDLE hDevice, unsigned int nCurrConnIndex, unsigned int nTotalConNum)
{
	if (NULL == pOutDevTags || NULL == pnTagsNum || \
		NULL == pTagGrps || NULL == pnTagGrpsNum || NULL == hDevice ||
		nTotalConNum <= 0)
		return EC_ICV_DRIVER_INVALID_PARAMETER;
	
	int nBlockLen = SNAP7_DEFAULT_PDUSIZE;
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	string strParam3 = string(pDevice->pszParam3);
	strParam3+=";";
	//获取自组块子块最大长度
	int nPos = strParam3.find("blocklen=");
  	if(nPos != string::npos)
	{
		string strPDU = strParam3.substr(nPos + 9,strParam3.length() - (nPos+9));
		int nPos1 = strPDU.find(';');
		if(nPos1 != string::npos)
			nBlockLen = atoi(strPDU.substr(0,nPos1).c_str());
	}
	CV_INFO(g_CVLogSnap7Drv,"Device[%s] start to handle autogroup %d/%d times.",pDevice->pszName,nCurrConnIndex + 1,nTotalConNum);

	TagInfoVector vecTagInfo;
	TagGroupInfoVector vecTagGrpInfo;
	CSnap7AutoGroupBuilder objGrpBuilder(pDevTags, nTagsNum);
	objGrpBuilder.GroupTags(vecTagInfo, vecTagGrpInfo, nBlockLen, nCurrConnIndex, nTotalConNum);

	*pnTagsNum = vecTagInfo.size();
	std::copy(vecTagInfo.begin(), vecTagInfo.end(), pOutDevTags);
	*pnTagGrpsNum = vecTagGrpInfo.size();
	std::copy(vecTagGrpInfo.begin(), vecTagGrpInfo.end(), pTagGrps);

	return DRV_SUCCESS;
}