#ifndef _CV_DRIVER_STATUS_INTERFACE_H
#define _CV_DRIVER_STATUS_INTERFACE_H

////////resdis///////////////////
#define  DRV_REDIS_TIME_START       "starttime"
#define  DRV_REDIS_TIME_CFGMODIFY   "lastcfgmodifiedtime"
#define  DRV_REDIS_TIME_DEVSWITCH   "devswitchtime"
#define  DRV_REDIS_TIME_END         "endtime"
#define  DRV_REDIS_STATUS           "status"
#define  DRV_REDIS_DEVICE_NAME      "devname"
#define  DRV_REDIS_DEVICE_ADDR      "devinaddr"
#define  DRV_REDIS_DEVICE_PORT      "devinport"
#define  DRV_REDIS_DEVICE_MULTILINK "multilink"
#define  DRV_REDIS_DEVICE_PARAM1    "param1"
#define  DRV_REDIS_DEVICE_PARAM2    "param2"
#define  DRV_REDIS_DEVICE_PARAM3    "param3"
#define  DRV_REDIS_DEVICE_PARAM4    "param4"
#define  DRV_REDIS_DEVICE_READ      "readcount"
#define  DRV_REDIS_DEVICE_WRITE     "writecount"
#define  DRV_REDIS_DEVICE_STATUS    "devtimestamp"
#define  DRV_REDIS_BLOCK_NAME       "blockname"
#define  DRV_REDIS_BLOCK_TYPE       "blocktype"
#define  DRV_REDIS_BLOCK_STATUS     "blocktimestamp"
#define  DRV_REDIS_BLOCK_READ       "readcount"
#define  DRV_REDIS_BLOCK_WRITE      "writecount"

#define  DRV_REDIS_MAINDEVICE_ADDR		"devinaddr_main"
#define  DRV_REDIS_MAINDEVICE_PORT      "devinport_main"
#define  DRV_REDIS_BACKDEVICE_ADDR      "devinaddr_back"
#define  DRV_REDIS_BACKDEVICE_PORT      "devinport_back"
#define  DRV_REDIS_DEVICE_CONSTATUS		"devconstate"
#define  DRV_REDIS_DEVICE_CONDEVICE		"devconmainorback"

long drv_redis_init();
long drv_redis_uninit();

/*szPrefix may be NULL, drivername, drivername#devicename, or drivername#devicename#datablockname*/

// for send multi value, limited by time
void drv_redis_batchbegin();
void drv_redis_batchaddvalue(const char* szPrefix, const char* szSubKeyName, const char* szSubKeyValue);
void drv_redis_batchaddvalue(const char* szPrefix, const char* szSubKeyName, const long lSubKeyValue);
void drv_redis_batchaddstatus(const char* szPrefix, const int nStatusCode);
void drv_redis_batchaddtime(const char* szPrefix, const char* szTimeName);
long drv_redis_batchsubmit();

// directly send
long drv_redis_status_d(const char* szPrefix, const int nStatusCode);
long drv_redis_time_d(const char* szPrefix, const char* szTimeName);
long drv_redis_value_d(const char* szPrefix, const char* szSubKeyName, const char* szSubKeyValue);
long drv_redis_value_d(const char* szPrefix, const char* szSubKeyName, const long lSubKeyValue);

// limited by time
long drv_redis_status_t(const char* szPrefix, const int nStatusCode);
long drv_redis_time_t(const char* szPrefix, const char* szTimeName);
long drv_redis_value_t(const char* szPrefix, const char* szSubKeyName, const char* szSubKeyValue);
long drv_redis_value_t(const char* szPrefix, const char* szSubKeyName, const long lSubKeyValue);

long drv_redis_del(const char* szPrefix);
#endif