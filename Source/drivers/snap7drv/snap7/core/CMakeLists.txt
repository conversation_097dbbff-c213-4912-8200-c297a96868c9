cmake_minimum_required(VERSION 3.10)

PROJECT (snap7)

INCLUDE($ENV{DRDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
#Setting Source Files
SET(SRCS ${SRCS} s7_client.cpp s7_isotcp.cpp s7_micro_client.cpp s7_partner.cpp s7_peer.cpp s7_server.cpp s7_text.cpp)
SET(SRCS ${SRCS} ../lib/snap7_libmain.cpp ../lib/snap7.def)
SET(SRCS ${SRCS} ../sys/snap_msgsock.cpp ../sys/snap_sysutils.cpp ../sys/snap_tcpsrvr.cpp ../sys/snap_threads.cpp)


#Setting Target Name (executable file name | library name)
SET(TARGET_NAME snap7)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

#SET(LINK_LIBS snap7lib snap7common)

#SET(SPECOUTDIR /drivers/snap7drv)
INCLUDE($ENV{DRDIR}CMakeSpecOutPath)
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)
