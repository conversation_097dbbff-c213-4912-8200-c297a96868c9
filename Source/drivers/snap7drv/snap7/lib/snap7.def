LIBRARY SNAP7.DLL
EXPORTS
  Cli_Create
  Cli_Destroy
  Cli_ConnectTo
  Cli_SetConnectionParams
  Cli_SetConnectionType
  Cli_Connect
  Cli_Disconnect
  Cli_GetParam
  Cli_SetParam
  Cli_SetAsCallback
  Cli_ReadArea
  Cli_WriteArea
  Cli_ReadMultiVars
  Cli_WriteMultiVars
  Cli_DBRead
  Cli_DBWrite
  Cli_MBRead
  Cli_MBWrite
  Cli_EBRead
  Cli_EBWrite
  Cli_ABRead
  Cli_ABWrite
  Cli_TMRead
  Cli_TMWrite
  Cli_CTRead
  Cli_CTWrite
  Cli_ListBlocks
  Cli_GetAgBlockInfo
  Cli_GetPgBlockInfo
  Cli_ListBlocksOfType
  Cli_Upload
  Cli_FullUpload
  Cli_Download
  Cli_Delete
  Cli_DBGet
  Cli_DBFill
  Cli_GetPlcDateTime
  Cli_SetPlcDateTime
  Cli_SetPlcSystemDateTime
  Cli_GetOrderCode
  Cli_GetCpuInfo
  Cli_GetCpInfo
  Cli_ReadSZL
  Cli_ReadSZLList
  Cli_PlcHotStart
  Cli_PlcColdStart
  Cli_PlcStop
  Cli_CopyRamToRom
  Cli_Compress
  Cli_GetPlcStatus
  Cli_GetProtection
  Cli_SetSessionPassword
  Cli_ClearSessionPassword
  Cli_IsoExchangeBuffer
  Cli_GetExecTime
  Cli_GetLastError
  Cli_GetPduLength
  Cli_AsReadArea
  Cli_AsWriteArea
  Cli_AsDBRead
  Cli_AsDBWrite
  Cli_AsMBRead
  Cli_AsMBWrite
  Cli_AsEBRead
  Cli_AsEBWrite
  Cli_AsABRead
  Cli_AsABWrite
  Cli_AsTMRead
  Cli_AsTMWrite
  Cli_AsCTRead
  Cli_AsCTWrite
  Cli_AsListBlocksOfType
  Cli_AsReadSZL
  Cli_AsReadSZLList
  Cli_AsUpload
  Cli_AsFullUpload
  Cli_AsDownload
  Cli_AsCopyRamToRom
  Cli_AsCompress
  Cli_AsDBGet
  Cli_AsDBFill
  Cli_CheckAsCompletion
  Cli_WaitAsCompletion
  Cli_ErrorText
  Cli_GetConnected
  Srv_Create
  Srv_Destroy
  Srv_GetParam
  Srv_SetParam
  Srv_StartTo
  Srv_Start
  Srv_Stop
  Srv_RegisterArea
  Srv_UnregisterArea
  Srv_LockArea
  Srv_UnlockArea
  Srv_GetStatus
  Srv_SetCpuStatus
  Srv_ClearEvents
  Srv_PickEvent
  Srv_GetMask
  Srv_SetMask
  Srv_SetEventsCallback
  Srv_SetReadEventsCallback
  Srv_SetRWAreaCallback
  Srv_ErrorText
  Srv_EventText
  Par_Create
  Par_Destroy
  Par_GetParam
  Par_SetParam
  Par_StartTo
  Par_Start
  Par_Stop
  Par_BSend
  Par_AsBSend
  Par_CheckAsBSendCompletion
  Par_WaitAsBSendCompletion
  Par_SetSendCallback
  Par_BRecv
  Par_CheckAsBRecvCompletion
  Par_SetRecvCallback
  Par_GetTimes
  Par_GetStats
  Par_GetLastError
  Par_GetStatus
  Par_ErrorText
