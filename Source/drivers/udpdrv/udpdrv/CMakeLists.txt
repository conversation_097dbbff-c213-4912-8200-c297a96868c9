cmake_minimum_required(VERSION 3.10)

PROJECT (udpdrv)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} udpdrv.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME udpdrv)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS drdrivercommon drcomm)

SET(SPECOUTDIR /drivers/udpdrv)
INCLUDE($ENV{DRDIR}CMakeSpecOutPath)
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)