#ifndef UDPDEVICE_H
#define UDPDEVICE_H

#include "driversdk/cvdrivercommon.h"
#include <vector>
#include <map>
#include <mutex>
#include <string>
#include "ace/INET_Addr.h"

class CUDPDevice;

class CBuffer
{
public:
    CBuffer(size_t size);
    virtual ~CBuffer(void);

    // 写入数据接口
    int write(const void* buffer, int len);

    // 读取数据接口
    std::vector<unsigned char> read(void);

    // 读取的次数，如果为0，则说明缓冲区刚被刷新，如果大于0，则代表被读取的次数
	unsigned int getReadCount(){ return m_uiCount; }

private:
    // 数据存储缓冲区
    std::vector<unsigned char> m_buffer;
    // 互斥锁，保护并发访问
    std::mutex m_mutex;
    unsigned int m_uiCount;
};

// class CBufferPharser
// {
// public:
// 	public:CBufferPharser(CUDPDevice* _aModule);
// 	virtual ~CBufferPharser(void);
// 	// 解析电文内容
// 	bool pharser(std::vector<unsigned char>& telegram);
// 	// 本次解析的内存中包含的数据包数量
// 	void setPackageNumber(int packageNumber);
// 	// 是否已经收到第一个包
// 	inline bool isGetFirstPackage(){return m_bGetFirstPackage;};
// private:
// 	//Big-Endian: 低地址存放高位
// 	class CBigPharser
// 	{
// 		CUDPDevice* m_device;
// 	public:
// 		CBigPharser(CUDPDevice* device):m_device(device){};

// 		bool pharser(std::vector<unsigned char>& telegram);
// 	};
// 	//Little-Endian: 低地址存放低位
// 	class CLitPharser
// 	{
// 		CUDPDevice* m_device;

// 	public:
// 		CLitPharser(CUDPDevice* device):m_device(device){};

// 		bool pharser(std::vector<unsigned char>& telegram);
// 	};

// private:
// 	CBigPharser m_bigPharser;
// 	CLitPharser m_litPharser;
// 	CUDPDevice* m_device;
// 	// 标记是否已经收到第一个包
// 	bool m_bGetFirstPackage;
// 	// 需要补充的包的数量
// 	int m_iPackageNumber;
// 	// 补充丢失的包
// 	void completePackage(int _number);
// 	// 记录丢失包的次数
// 	int m_iMissPackageCount;
// };

struct UDPDATABLOCK
{
    int moduleno;          // 模块序号
	int offset;            // 在数据报中的偏移
	DRVHANDLE hDataBlock;  // 数据块句柄
};

class CUDPDevice
{
public:
    CUDPDevice(DRVHANDLE hDevice, CVDEVICE *pDevice);
    virtual ~CUDPDevice(void);

	long ReadData();

    long AddData(DRVHANDLE hDevice, DRVHANDLE hDataBlock);

private:
// 	CBuffer m_buffer;

	DRVHANDLE m_hDevice;		// 设备句柄

    bool m_bFlagComplete;	//此标记用来是否加载数据块完毕

    std::map<int, UDPDATABLOCK> m_addrMap;

};

#endif