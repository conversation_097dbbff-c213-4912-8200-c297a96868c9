#ifndef _UDP_DRV_H_
#define _UDP_DRV_H_ 

#include "driversdk/cvdrivercommon.h"
#include "udpdevice.h"
#include <string>
#include <list>
#include <map>
using namespace std;

#define     UDP_PACKAGE_HEADER_LEN          8 
#define		DEFAULT_RESPONSE_MAXLEN			65535
#define     BITS_PER_BYTE                   8

enum
{
	EC_UDP_DEVICE_NOEXIST = 1,
	EC_UDP_DATABLOCK_NOEXIST,
};

// 添加设备
long AddDevice(DRVHANDLE hDevice);

#endif