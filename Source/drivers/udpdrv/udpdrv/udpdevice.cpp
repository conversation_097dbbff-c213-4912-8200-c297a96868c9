#include "udpdevice.h"
#include <cstring>
#include <algorithm>
#include <iostream>
#include <iomanip>
#include "driversdk/cvdrivercommon.h"
#include "common/CVLog.h"
#include "ace/OS_NS_sys_time.h"

extern CCVLog g_udpLog;

// CBuffer类实现
CBuffer::CBuffer(size_t size)
{
    m_uiCount = 0;
    // 初始化空的缓冲区
    m_buffer.reserve (std::vector<unsigned char>::size_type(size+1));
}

CBuffer::~CBuffer(void)
{
    
}

int CBuffer::write(const void* buffer, int len)
{
    if (!buffer || len <= 0)
        return 0;

    std::lock_guard<std::mutex> lock(m_mutex);

    // 获取当前缓冲区大小
    size_t oldSize = m_buffer.size();

    // 调整缓冲区大小以容纳新数据
    m_buffer.resize(oldSize + len);

    // 将新数据复制到缓冲区末尾
    memcpy(&m_buffer[oldSize], buffer, len);

    m_uiCount = 0;

    return len;
}

std::vector<unsigned char> CBuffer::read(void)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    // 创建返回数据的副本
    std::vector<unsigned char> result = m_buffer;

    // 清空缓冲区
    m_buffer.clear();

    m_uiCount++;

    return result;
}

CBufferParser::CBufferParser(CUDPDevice* device)
    : m_device(device)
    , m_bigParser(device)
    , m_litParser(device)
{
    
}

CBufferParser::~CBufferParser()
{

}

bool CBufferParser::parser(std::vector<unsigned char> telegram)
{
    
    return true;
}

// long CUDPDevice::AddData( DRVHANDLE hDevice, DRVHANDLE hDataBlock)
// {
//     CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);

// 	std::string strTagAddr = pDataBlock->pszAddress;
// 	UDPDATABLOCK uBlock;
//     unsigned int moduleno;
//     unsigned int offset;
//     uBlock.hDataBlock = hDataBlock;

//     // 获取模块序号和偏移
//     size_t nPos = strTagAddr.find(':');
//     if(nPos != std::string::npos)
// 	{
//         moduleno = atoi(strTagAddr.substr(nPos+1, strTagAddr.length()-nPos-1).c_str());
//         offset = atoi(strTagAddr.substr(0, nPos).c_str());
//         uBlock.moduleno = moduleno;
//         uBlock.offset = offset;
//         m_addrMap.insert(std::make_pair(offset, uBlock));
//     }
//     // else
// 	// {
// 	// 	CV_WARN(g_udpLog,-1, "Tag Name:%s. Address:%s. Type %s is not supported.", pDataBlock->pszName, pDataBlock->pszAddress, strType.c_str());
// 	// }

//     return DRV_SUCCESS;
// }

CUDPDevice::CUDPDevice(DRVHANDLE hDevice, CVDEVICE *pDevice)
{ 
    m_hDevice = hDevice;
    m_buffer = new CBuffer(DEFAULT_BUFFER_LEN);
    m_parser = new CBufferParser();
}

CUDPDevice::~CUDPDevice(void)
{

}

long CUDPDevice::AddData( DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
    CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);

	std::string strTagAddr = pDataBlock->pszAddress;
	UDPDATABLOCK uBlock;
    unsigned int moduleno;
    unsigned int offset;
    uBlock.hDataBlock = hDataBlock;

    // 获取模块序号和偏移
    size_t nPos = strTagAddr.find(':');
    if(nPos != std::string::npos)
	{
        moduleno = atoi(strTagAddr.substr(nPos+1, strTagAddr.length()-nPos-1).c_str());
        offset = atoi(strTagAddr.substr(0, nPos).c_str());
        uBlock.moduleno = moduleno;
        uBlock.offset = offset;
        m_addrMap.insert(std::make_pair(offset, uBlock));
    }

    return DRV_SUCCESS;
}

long CUDPDevice::read()
{
    CVDEVICE *pDevice = Drv_GetDeviceInfo(m_hDevice);

	// 从设备接收消息
	long lCurRecvLen = 0;
	char szResponse[DEFAULT_RESPONSE_MAXLEN] = {0};

	unsigned short nPackageLen = 0;

	long lRecvBytes = Drv_RecvFromDevice(m_hDevice, szResponse + lCurRecvLen, sizeof(szResponse) - lCurRecvLen, pDevice->nRecvTimeout);

	if(lRecvBytes)
	{
        m_buffer->write(szResponse, lRecvBytes);
    }

    m_parser->parser(m_buffer->read());
}