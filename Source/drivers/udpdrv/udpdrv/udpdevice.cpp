#include "udpdevice.h"
#include <cstring>
#include <algorithm>
#include "driversdk/cvdrivercommon.h"
#include "common/CVLog.h"

extern CCVLog g_udpLog;

CBuffer::CBuffer(size_t size) : m_totalSize(size)
{
	std::lock_guard<std::mutex> lock(m_mutex);
	m_vBuffer.reserve(size + 1);
}

CBuffer::~CBuffer(void)
{
}

bool CBuffer::isFull(void)
{
	std::lock_guard<std::mutex> lock(m_mutex);
	return _isFull();
}

int CBuffer::write(const void* buffer, int len)
{
	if (!buffer || len <= 0)
		return 0;

	std::lock_guard<std::mutex> lock(m_mutex);

	// 检查缓冲区是否有足够空间
	if (_remainingSpace() < static_cast<size_t>(len))
		return 0;

	// 清空现有数据，写入新数据
	m_vBuffer.clear();
	m_vBuffer.resize(len);
	memcpy(m_vBuffer.data(), buffer, len);

	return len;
}

// 追加写入数据 - 线程安全
int CBuffer::append(const void* buffer, int len)
{
	if (!buffer || len <= 0)
		return 0;

	std::lock_guard<std::mutex> lock(m_mutex);

	// 检查缓冲区是否有足够空间
	if (_remainingSpace() < static_cast<size_t>(len))
		return 0;

	int oldSize = m_vBuffer.size();
	m_vBuffer.resize(oldSize + len);
	memcpy(&m_vBuffer[oldSize], buffer, len);

	return len;
}

// 返回数据的副本，避免并发访问问题
std::vector<unsigned char> CBuffer::read(void)
{
	std::lock_guard<std::mutex> lock(m_mutex);
	return m_vBuffer; // 返回副本
}

// 读取数据到指定缓冲区 - 线程安全版本
int CBuffer::read(void* buffer, int maxLen)
{
	if (!buffer || maxLen <= 0)
		return 0;

	std::lock_guard<std::mutex> lock(m_mutex);

	int copyLen = std::min(static_cast<int>(m_vBuffer.size()), maxLen);
	if (copyLen > 0) {
		memcpy(buffer, m_vBuffer.data(), copyLen);
	}

	return copyLen;
}

// 读取并移除数据 - 类似队列操作
int CBuffer::readAndRemove(void* buffer, int maxLen)
{
	if (!buffer || maxLen <= 0)
		return 0;

	std::lock_guard<std::mutex> lock(m_mutex);

	int copyLen = std::min(static_cast<int>(m_vBuffer.size()), maxLen);
	if (copyLen > 0) {
		memcpy(buffer, m_vBuffer.data(), copyLen);
		// 移除已读取的数据
		m_vBuffer.erase(m_vBuffer.begin(), m_vBuffer.begin() + copyLen);
	}

	return copyLen;
}

// 获取当前数据大小
size_t CBuffer::size(void)
{
	std::lock_guard<std::mutex> lock(m_mutex);
	return m_vBuffer.size();
}

// 获取剩余空间大小
size_t CBuffer::remainingSpace(void)
{
	std::lock_guard<std::mutex> lock(m_mutex);
	return _remainingSpace();
}

void CBuffer::clear()
{
	std::lock_guard<std::mutex> lock(m_mutex);
	m_vBuffer.clear();
}

// 预留空间
void CBuffer::reserve(size_t size)
{
	std::lock_guard<std::mutex> lock(m_mutex);
	if (size <= m_totalSize) {
		m_vBuffer.reserve(size);
	}
}

// 检查是否为空
bool CBuffer::empty(void)
{
	std::lock_guard<std::mutex> lock(m_mutex);
	return m_vBuffer.empty();
}

// 获取最大容量
size_t CBuffer::capacity(void)
{
	return m_totalSize;
}

// 内部辅助方法（不加锁，由调用者保证线程安全）
bool CBuffer::_isFull() const
{
	return m_vBuffer.size() >= m_totalSize;
}

size_t CBuffer::_remainingSpace() const
{
	return m_totalSize - m_vBuffer.size();
}

CUDPDevice::CUDPDevice(DRVHANDLE hDevice, CVDEVICE *pDevice)
	: m_hDevice(hDevice)	
{
    std::string strConnParam = pDevice->pszConnParam;
}

CUDPDevice::~CUDPDevice()
{

}

long CUDPDevice::ReadData()
{
	
}

long CUDPDevice::AddData( DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
    CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);

	std::string strTagAddr = pDataBlock->pszAddress;
	UDPDATABLOCK uBlock;
    unsigned int moduleno;
    unsigned int offset;
    uBlock.hDataBlock = hDataBlock;

    // 获取模块序号和偏移
    size_t nPos = strTagAddr.find(':');
    if(nPos != std::string::npos)
	{
        moduleno = atoi(strTagAddr.substr(nPos+1, strTagAddr.length()-nPos-1).c_str());
        offset = atoi(strTagAddr.substr(0, nPos).c_str());
        uBlock.moduleno = moduleno;
        uBlock.offset = offset;
        m_addrMap.insert(std::make_pair(offset, uBlock));
    }
    // else
	// {
	// 	CV_WARN(g_udpLog,-1, "Tag Name:%s. Address:%s. Type %s is not supported.", pDataBlock->pszName, pDataBlock->pszAddress, strType.c_str());
	// }

    return DRV_SUCCESS;
}
