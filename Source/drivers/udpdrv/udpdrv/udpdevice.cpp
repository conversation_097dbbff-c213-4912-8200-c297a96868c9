#include "udpdevice.h"
#include <cstring>
#include <algorithm>
#include "driversdk/cvdrivercommon.h"
#include "common/CVLog.h"

extern CCVLog g_udpLog;



CUDPDevice::CUDPDevice(DRVHANDLE hDevice, CVDEVICE *pDevice)
	: m_hDevice(hDevice)	
{
    std::string strConnParam = pDevice->pszConnParam;
}

CUDPDevice::~CUDPDevice()
{

}

long CUDPDevice::ReadData()
{
	
}

long CUDPDevice::AddData( DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
    CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);

	std::string strTagAddr = pDataBlock->pszAddress;
	UDPDATABLOCK uBlock;
    unsigned int moduleno;
    unsigned int offset;
    uBlock.hDataBlock = hDataBlock;

    // 获取模块序号和偏移
    size_t nPos = strTagAddr.find(':');
    if(nPos != std::string::npos)
	{
        moduleno = atoi(strTagAddr.substr(nPos+1, strTagAddr.length()-nPos-1).c_str());
        offset = atoi(strTagAddr.substr(0, nPos).c_str());
        uBlock.moduleno = moduleno;
        uBlock.offset = offset;
        m_addrMap.insert(std::make_pair(offset, uBlock));
    }
    // else
	// {
	// 	CV_WARN(g_udpLog,-1, "Tag Name:%s. Address:%s. Type %s is not supported.", pDataBlock->pszName, pDataBlock->pszAddress, strType.c_str());
	// }

    return DRV_SUCCESS;
}
