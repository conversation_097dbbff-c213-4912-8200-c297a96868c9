#include "udpdevice.h"
#include <cstring>
#include <algorithm>
#include "driversdk/cvdrivercommon.h"
#include "common/CVLog.h"

extern CCVLog g_udpLog;

// CBuffer::CBuffer(size_t size)
// {
// 	m_vBuffer.reserve(std::vector<unsigned char>::size_type(size + 1));
// 	m_totalSize = size;
// }

// CBuffer::~CBuffer(void)
// {
// }

// bool CBuffer::isFull(void)
// {
// 	return m_totalSize <= m_vBuffer.size();
// }

// int CBuffer::write(const void* buffer, int len)
// {
// 	if (!buffer || len <= 0)
// 		return 0;

// 	// 检查缓冲区是否有足够空间
// 	if (m_vBuffer.size() + len > m_totalSize)
// 		return 0;

// 	int index = m_vBuffer.size();
// 	m_vBuffer.resize(index + len);

// 	memcpy(&m_vBuffer[index], buffer, len);

// 	return len;
// }

// std::vector<unsigned char>& CBuffer::read(void)
// {
// 	return m_vBuffer;
// }

// void CBuffer::clear()
// {
// 	m_vBuffer.clear();
// }

CUDPDevice::CUDPDevice(DRVHANDLE hDevice, CVDEVICE *pDevice)
	: m_hDevice(hDevice)	
{
    std::string strConnParam = pDevice->pszConnParam;
}

CUDPDevice::~CUDPDevice()
{

}

long CUDPDevice::ReadData()
{
	
}

long CUDPDevice::AddData( DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
    CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);

	std::string strTagAddr = pDataBlock->pszAddress;
	UDPDATABLOCK uBlock;
    unsigned int moduleno;
    unsigned int offset;
    uBlock.hDataBlock = hDataBlock;

    // 获取模块序号和偏移
    size_t nPos = strTagAddr.find(':');
    if(nPos != std::string::npos)
	{
        moduleno = atoi(strTagAddr.substr(nPos+1, strTagAddr.length()-nPos-1).c_str());
        offset = atoi(strTagAddr.substr(0, nPos).c_str());
        uBlock.moduleno = moduleno;
        uBlock.offset = offset;
        m_addrMap.insert(std::make_pair(offset, uBlock));
    }
    // else
	// {
	// 	CV_WARN(g_udpLog,-1, "Tag Name:%s. Address:%s. Type %s is not supported.", pDataBlock->pszName, pDataBlock->pszAddress, strType.c_str());
	// }

    return DRV_SUCCESS;
}
