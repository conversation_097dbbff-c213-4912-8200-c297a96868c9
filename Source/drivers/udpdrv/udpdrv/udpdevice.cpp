#include "udpdevice.h"
#include <cstring>
#include <algorithm>
#include <iostream>
#include <iomanip>
#include "driversdk/cvdrivercommon.h"
#include "common/CVLog.h"
#include "ace/OS_NS_sys_time.h"

extern CCVLog g_udpLog;

// CBuffer类实现
CBuffer::CBuffer(size_t size)
{
    m_uiCount = 0;
    // 初始化空的缓冲区
    m_buffer.reserve (std::vector<unsigned char>::size_type(size+1));
}

CBuffer::~CBuffer(void)
{
    
}

int CBuffer::write(const void* buffer, int len)
{
    if (!buffer || len <= 0)
        return 0;

    std::lock_guard<std::mutex> lock(m_mutex);

    // 获取当前缓冲区大小
    size_t oldSize = m_buffer.size();

    // 调整缓冲区大小以容纳新数据
    m_buffer.resize(oldSize + len);

    // 将新数据复制到缓冲区末尾
    memcpy(&m_buffer[oldSize], buffer, len);

    m_uiCount = 0;

    return len;
}

std::vector<unsigned char> CBuffer::read(void)
{
    std::lock_guard<std::mutex> lock(m_mutex);

    // 创建返回数据的副本
    std::vector<unsigned char> result = m_buffer;

    // 清空缓冲区
    m_buffer.clear();

    m_uiCount++;

    return result;
}

CBufferParser::CBufferParser()
{

}

CBufferParser::~CBufferParser()
{

}

bool CBufferParser::parser(std::vector<unsigned char> telegram)
{
    // 检查报文最小长度：模块索引号(2) + 报文长度(2) + 报文计数(4) = 8字节
    if (telegram.size() < 8) {
        std::cout << "Error: Telegram too short, minimum 8 bytes required, got " << telegram.size() << " bytes" << std::endl;
        return false;
    }

    // 解析报文头部
    size_t offset = 0;

    // 1. 解析模块索引号（int16，2字节）
    int16_t moduleIndex = 0;
    memcpy(&moduleIndex, &telegram[offset], sizeof(int16_t));
    offset += sizeof(int16_t);

    // 2. 解析报文长度（int16，2字节）
    int16_t messageLength = 0;
    memcpy(&messageLength, &telegram[offset], sizeof(int16_t));
    offset += sizeof(int16_t);

    // 3. 解析报文计数（int32，4字节）
    int32_t messageCount = 0;
    memcpy(&messageCount, &telegram[offset], sizeof(int32_t));
    offset += sizeof(int32_t);

    // 打印解析的头部信息
    std::cout << "=== Telegram Parser Results ===" << std::endl;
    std::cout << "Module Index: " << moduleIndex << std::endl;
    std::cout << "Message Length: " << messageLength << std::endl;
    std::cout << "Message Count: " << messageCount << std::endl;

    // 验证模块索引号
    if (moduleIndex != 200) {
        std::cout << "Warning: Expected module index 200, got " << moduleIndex << std::endl;
    }

    // 验证报文长度
    if (messageLength != 10) {
        std::cout << "Warning: Expected message length 10, got " << messageLength << std::endl;
    }

    // 计算数据部分的长度
    size_t dataLength = telegram.size() - offset;
    size_t expectedDataLength = messageLength - 6; // 报文长度 - 头部长度(模块索引2 + 报文计数4)

    std::cout << "Data Length: " << dataLength << " bytes" << std::endl;
    std::cout << "Expected Data Length: " << expectedDataLength << " bytes" << std::endl;

    // 检查数据长度是否足够
    if (dataLength < expectedDataLength) {
        std::cout << "Error: Insufficient data, expected " << expectedDataLength << " bytes, got " << dataLength << " bytes" << std::endl;
        return false;
    }

    // 4. 解析数据部分（int16类型）
    std::cout << "Data (int16 values): ";
    size_t numValues = expectedDataLength / sizeof(int16_t);

    for (size_t i = 0; i < numValues && offset + sizeof(int16_t) <= telegram.size(); i++) {
        int16_t value = 0;
        memcpy(&value, &telegram[offset], sizeof(int16_t));
        offset += sizeof(int16_t);

        std::cout << value;
        if (i < numValues - 1) {
            std::cout << ", ";
        }
    }
    std::cout << std::endl;

    // 如果还有剩余字节，显示为原始数据
    if (offset < telegram.size()) {
        std::cout << "Remaining bytes: ";
        for (size_t i = offset; i < telegram.size(); i++) {
            std::cout << "0x" << std::hex << std::setw(2) << std::setfill('0') << static_cast<int>(telegram[i]) << " ";
        }
        std::cout << std::dec << std::endl;
    }

    std::cout << "=== End of Parser Results ===" << std::endl;

    return true;
}

// CUDPDevice::CUDPDevice(DRVHANDLE hDevice, CVDEVICE *pDevice)
// 	: m_hDevice(hDevice), m_bTimerRunning(false), m_timerInterval(1000)  // 默认1秒间隔
// {
//     //std::string strConnParam = pDevice->pszConnParam;

//     m_buffer = new CBuffer(DEFAULT_BUFFER_LEN);

//     // 在构造函数末尾启动定时器
//     //StartTimer();
// }

// CUDPDevice::~CUDPDevice()
// {
//     // 停止定时器
//     StopTimer();
// }

// long CUDPDevice::ReadData()
// {
	
// }

// long CUDPDevice::AddData( DRVHANDLE hDevice, DRVHANDLE hDataBlock)
// {
//     CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);

// 	std::string strTagAddr = pDataBlock->pszAddress;
// 	UDPDATABLOCK uBlock;
//     unsigned int moduleno;
//     unsigned int offset;
//     uBlock.hDataBlock = hDataBlock;

//     // 获取模块序号和偏移
//     size_t nPos = strTagAddr.find(':');
//     if(nPos != std::string::npos)
// 	{
//         moduleno = atoi(strTagAddr.substr(nPos+1, strTagAddr.length()-nPos-1).c_str());
//         offset = atoi(strTagAddr.substr(0, nPos).c_str());
//         uBlock.moduleno = moduleno;
//         uBlock.offset = offset;
//         m_addrMap.insert(std::make_pair(offset, uBlock));
//     }
//     // else
// 	// {
// 	// 	CV_WARN(g_udpLog,-1, "Tag Name:%s. Address:%s. Type %s is not supported.", pDataBlock->pszName, pDataBlock->pszAddress, strType.c_str());
// 	// }

//     return DRV_SUCCESS;
// }

// // 定时器相关方法实现
// void CUDPDevice::StartTimer()
// {
//     if (m_bTimerRunning.load())
//         return; // 定时器已经在运行

//     m_bTimerRunning.store(true);
//     m_timerThread = std::thread(&CUDPDevice::TimerThreadFunc, this);
// }

// void CUDPDevice::StopTimer()
// {
//     if (!m_bTimerRunning.load())
//         return; // 定时器没有运行

//     m_bTimerRunning.store(false);

//     if (m_timerThread.joinable()) 
//     {
//         m_timerThread.join();
//     }
// }

// void CUDPDevice::TimerThreadFunc()
// {
//     while (m_bTimerRunning.load()) 
//     {
//         CVDEVICE *pDevice = Drv_GetDeviceInfo(m_hDevice);

// 	    // 从设备接收消息
// 	    long lCurRecvLen = 0;
// 	    char szResponse[DEFAULT_RESPONSE_MAXLEN] = {0};

// 	    unsigned short nPackageLen = 0;
// 	    CV_TRACE(g_udpLog, "Drv_RecvFromDevice Device %s  receive start ", pDevice->pszName);
// 	    long lRecvBytes = Drv_RecvFromDevice(m_hDevice, szResponse + lCurRecvLen, sizeof(szResponse) - lCurRecvLen, pDevice->nRecvTimeout);
// 	    CV_TRACE(g_udpLog, "Drv_RecvFromDevice Device %s  receive end   lRecvBytes = %d ", pDevice->pszName, lRecvBytes);

// 	    if(lRecvBytes)
// 	    {
//             m_buffer->write(szResponse, lRecvBytes);
//         }


//         std::this_thread::sleep_for(m_timerInterval);
//     }
// }


CUDPDevice::CUDPDevice(DRVHANDLE hDevice, CVDEVICE *pDevice)
{ 
    m_hDevice = hDevice;
    m_buffer = new CBuffer(DEFAULT_BUFFER_LEN);
    m_parser = new CBufferParser();
}

CUDPDevice::~CUDPDevice(void)
{

}

long CUDPDevice::AddData( DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
    CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);

	std::string strTagAddr = pDataBlock->pszAddress;
	UDPDATABLOCK uBlock;
    unsigned int moduleno;
    unsigned int offset;
    uBlock.hDataBlock = hDataBlock;

    // 获取模块序号和偏移
    size_t nPos = strTagAddr.find(':');
    if(nPos != std::string::npos)
	{
        moduleno = atoi(strTagAddr.substr(nPos+1, strTagAddr.length()-nPos-1).c_str());
        offset = atoi(strTagAddr.substr(0, nPos).c_str());
        uBlock.moduleno = moduleno;
        uBlock.offset = offset;
        m_addrMap.insert(std::make_pair(offset, uBlock));
    }

    return DRV_SUCCESS;
}

long CUDPDevice::read()
{
    CVDEVICE *pDevice = Drv_GetDeviceInfo(m_hDevice);

	// 从设备接收消息
	long lCurRecvLen = 0;
	char szResponse[DEFAULT_RESPONSE_MAXLEN] = {0};

	unsigned short nPackageLen = 0;

	long lRecvBytes = Drv_RecvFromDevice(m_hDevice, szResponse + lCurRecvLen, sizeof(szResponse) - lCurRecvLen, pDevice->nRecvTimeout);

	if(lRecvBytes)
	{
        m_buffer->write(szResponse, lRecvBytes);
    }


    m_parser->parser(m_buffer->read());
}