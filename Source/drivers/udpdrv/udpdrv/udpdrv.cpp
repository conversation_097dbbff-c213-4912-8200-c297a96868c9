#include <memory.h>
#include <map>
#include "udpdrv.h"
#include "common/cvcomm.hxx"
#include "driversdk/cvdrivercommon.h"
#include "common/TypeCast.h"
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <string>
#include "ace/Default_Constants.h"
#include "ace/DLL.h"
#include "common/cvcomm.hxx"
#include "gettext/libintl.h"
#include "common/CommHelper.h"
#include "common/CVLog.h"

#define EC_ICV_INVALID_PARAMETER                    100

CCVLog g_CVLogUDPDrv;
const char g_szDriverName[ICV_DRIVERNAME_MAXLEN] = "udpdrv";

std::map<DRVHANDLE, CUDPDevice*> g_mapDevices;
clock_t interval_start_time = clock();

CVDRIVER_EXPORTS long Begin()
{
	g_CVLogUDPDrv.SetLogFileNameThread(g_szDriverName);
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long Initialize(DRVHANDLE hDriver)
{
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long UnInitialize()
{
	//释放所有的设备	
	for (map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.begin();
		iterDevice != g_mapDevices.end(); iterDevice++)
	{
		delete iterDevice->second;
	}
	g_mapDevices.clear();

    g_CVLogUDPDrv.StopLogThread();
    return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnReadData(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	//对于点设备，hDataBlock是空的
	// map<DRVHANDLE, CUDPDevice*>::iterator iterUDPDevice = g_mapDevices.find(hDevice);
	// if (iterUDPDevice != g_mapDevices.end())
	// { 
	// 	clock_t interval_end_time = clock();
	// 	clock_t elapsed_time = interval_end_time - interval_start_time;
	// 	if (elapsed_time > 3000) // 间隔3000ms，进行一次连接状态检查
	// 	{
	// 		interval_start_time = clock(); //更新时间
	// 		CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	// 		bool isConnect = true;
			
	// 		long lCurRecvLen = 0;
	// 		char szResponse[DEFAULT_RESPONSE_MAXLEN] = {0};

	// 		unsigned short nPackageLen = 0;
	// 		CV_TRACE(g_CVLogUDPDrv, "Drv_RecvFromDevice Device %s  receive start ", pDevice->pszName);
	// 		long lRecvBytes = Drv_RecvFromDevice(hDevice, szResponse + lCurRecvLen, sizeof(szResponse) - lCurRecvLen, pDevice->nRecvTimeout);
	// 		CV_TRACE(g_CVLogUDPDrv, "Drv_RecvFromDevice Device %s  receive end   lRecvBytes = %d ", pDevice->pszName, lRecvBytes);

	// 		ACE_Time_Value* pLastRecvDataTime = (ACE_Time_Value*)Drv_GetUserDataPtr(hDevice, 1);
	// 		ACE_Time_Value* pDisconnectTimeout = (ACE_Time_Value*)Drv_GetUserDataPtr(hDevice, 0);
	// 		ACE_Time_Value tvCurrentTime = ACE_OS::gettimeofday();

	// 		if(lRecvBytes <= 0)
	// 		{
	// 			//接收数据失败
	// 			isConnect = false;
	// 		}
	// 		else if (lRecvBytes <= UDP_PACKAGE_HEADER_LEN)
	// 		{
	// 			//不合法的包长度
	// 			CV_WARN(g_CVLogUDPDrv, -1, "Device %s receive package of invalid size %d", pDevice->pszName, lRecvBytes);
	// 			return DRV_SUCCESS;
	// 		}


	// 		if (isConnect)
	// 		{ 
	// 			Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
	// 			CV_DEBUG(g_CVLogUDPDrv, "Drv_UpdateDevStatus DEV_STATUS_GOOD.");
	// 		}
	// 		else
	// 		{
	// 			Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
	// 			CV_DEBUG(g_CVLogUDPDrv, "Drv_UpdateDevStatus !!!DEV_STATUS_BAD!!!");
	// 		}
	// 	}

	// 	return iterUDPDevice->second->ReadData();
	// }
	// else
	// {
	// 	Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD); // 没有对应的设备，设备状态bad
	// 	CV_DEBUG(g_CVLogUDPDrv, "Drv_UpdateDevStatus !!!DEV_STATUS_BAD!!!");
	// 	return EC_UDP_DEVICE_NOEXIST;
	// }
}

/* 不实现 */
CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDatablock, int nTagByteOffset, int nTagBitOffset, char *szCmdDataBuff, int nCmdDataLenBits)
{
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	long lRet = DRV_SUCCESS;
	map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.find(hDevice);
	
	if (iterDevice == g_mapDevices.end())
	{
		AddDevice(hDevice);
	}

	iterDevice = g_mapDevices.find(hDevice);
	if (iterDevice != g_mapDevices.end())
	{
		CUDPDevice *pUdpDevice = iterDevice->second;
		CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
		if (!pDataBlock || !pDataBlock->pszAddress)
		{
			CV_ERROR(g_CVLogUDPDrv,-1, "failed to get datablock information");
			return EC_UDP_DATABLOCK_NOEXIST;
		}

		lRet = pUdpDevice->AddData(hDevice, hDataBlock);
        CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
        //CV_INFO(pDataBlock, "device: %s parameters: %s add datablock: %s", pDevice->pszName, pDevice->pszConnParam, pDataBlock->pszName);
	}
	else
	{
		lRet = EC_UDP_DEVICE_NOEXIST;
	}

	return lRet;
}

//获取版本信息  
CVDRIVER_EXPORTS long GetDrvFrameVersion()
{
    return 2;
}

const char * GetPureAddress(const char *pAddr)
{
	static char szAddress[ICV_IOADDR_MAXLEN + 1];
	memset(szAddress, 0, sizeof(szAddress));
	const char *pTemp = strstr(pAddr, ":");
	if (pTemp != NULL)
	{
		const char *pTemp1 = strstr(pTemp + 1, "#");

		if (pTemp1 != NULL)
			memcpy(szAddress, pTemp + 1, pTemp1 - pTemp - 1);
		else
			strncpy(szAddress, pTemp + 1, ICV_IOADDR_MAXLEN);
	}
	return szAddress;
}

CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum,
	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
{
	if (NULL == pOutDevTags || NULL == pnTagsNum || NULL == pTagGrps || NULL == pnTagGrpsNum)
		return EC_ICV_INVALID_PARAMETER;

	memset(pTagGrps, 0, *pnTagGrpsNum * sizeof(TagGroupInfo));

	*pnTagsNum = nTagsNum;
	*pnTagGrpsNum = 0;
	int nBlockNum = 0;

	std::map<string, string> mapDataBlockAddr;

	std::copy(pDevTags, pDevTags + nTagsNum, pOutDevTags);
	
	char szGroupName[ICV_DATABLOCKNAME_MAXLEN + 1];
	memset(szGroupName, 0, sizeof(szGroupName));

	for (int i = 0; i < nTagsNum; ++i)
	{
		string strAddress = GetPureAddress(pDevTags[i].szAddress);
		map<string, string>::iterator iter = mapDataBlockAddr.find(strAddress);
		if (iter != mapDataBlockAddr.end())
		{
			cvcommon::Safe_StrNCopy(pOutDevTags[i].szGrpName, iter->second.c_str(), ICV_DATABLOCKNAME_MAXLEN + 1);
			continue;
		}

		cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szAddress, GetPureAddress(pDevTags[i].szAddress), ICV_IOADDR_MAXLEN);
		pTagGrps[nBlockNum].nElemBits = 8;
		pTagGrps[nBlockNum].nPollRate = 0;
		sprintf(pTagGrps[nBlockNum].szGroupName, "group%d", nBlockNum);
		sprintf(pOutDevTags[i].szGrpName, "group%d", nBlockNum);
		mapDataBlockAddr.insert(make_pair(pTagGrps[nBlockNum].szAddress, pTagGrps[nBlockNum].szGroupName));

		switch(pDevTags[i].nDataType)
		{
		case TAG_DATATYPE_BOOL:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Boolean", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 1;
			break;
			//无符号1字节
		case TAG_DATATYPE_BYTE:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Byte", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 1;
			break;
		case TAG_DATATYPE_INT:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "INT", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 2;
			break;
		case TAG_DATATYPE_DINT:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "DINT", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 4;
			break;
		case TAG_DATATYPE_WORD:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Word", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 2;
			break;
		case TAG_DATATYPE_DWORD:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "DWord", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 4;
			break;
		case TAG_DATATYPE_REAL:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Float", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 4;
			break;
		case TAG_DATATYPE_USINT:
		case TAG_DATATYPE_CHAR:
		case TAG_DATATYPE_SINT:
		case TAG_DATATYPE_UINT:
		case TAG_DATATYPE_TIME:
		case TAG_DATATYPE_UDINT:
		case TAG_DATATYPE_LINT:
		case TAG_DATATYPE_LWORD:
		case TAG_DATATYPE_ULINT:
		case TAG_DATATYPE_LREAL:
		case TAG_DATATYPE_BLOB:
		case TAG_DATATYPE_STRING:
		default:
			break;
		}
		nBlockNum++;
	}
	*pnTagGrpsNum = nBlockNum;

    return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
	AddDevice(hDevice);
	return DRV_SUCCESS;
}
 
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
	Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
	map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.find(hDevice);
	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
	if (iterDevice != g_mapDevices.end())
	{
		// iterDevice->second->disconnect(true);
        CV_INFO(g_CVLogUDPDrv, "device: %s parameters: %s is deleted", pDevice->pszName, pDevice->pszConnParam);
		delete iterDevice->second;
		g_mapDevices.erase(iterDevice);
	}
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDataBlockTimer(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	return DRV_SUCCESS;
}
 
CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hCfgDataBlock)
{
	return DRV_SUCCESS;
}

// 添加设备
long AddDevice(DRVHANDLE hDevice)
{
	map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.find(hDevice);
	if (iterDevice == g_mapDevices.end())
	{
		CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
		if (!pDevice || !pDevice->pszConnParam)
		{
			CV_ERROR(g_CVLogUDPDrv,-1,"failed to get device information");
			return EC_UDP_DEVICE_NOEXIST;
		}

		CUDPDevice *pUDPDevice = new CUDPDevice(hDevice, pDevice);
		g_mapDevices.insert(make_pair(hDevice, pUDPDevice));
	}

	return DRV_SUCCESS;
}