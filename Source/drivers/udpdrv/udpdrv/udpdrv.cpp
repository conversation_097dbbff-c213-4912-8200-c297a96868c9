#include <memory.h>
#include <map>
#include "udpdrv.h"
#include "common/cvcomm.hxx"
#include "driversdk/cvdrivercommon.h"
#include "common/TypeCast.h"
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <string>
#include "ace/Default_Constants.h"
#include "ace/DLL.h"
#include "common/cvcomm.hxx"
#include "gettext/libintl.h"
#include "common/CommHelper.h"
#include "common/CVLog.h"

#define EC_ICV_INVALID_PARAMETER                    100

CCVLog g_CVLogUDPDrv;
const char g_szDriverName[ICV_DRIVERNAME_MAXLEN] = "udpdrv";

std::map<DRVHANDLE, CUDPDevice*> g_mapDevices;
clock_t interval_start_time = clock();

CVDRIVER_EXPORTS long Begin()
{
	g_CVLogUDPDrv.SetLogFileNameThread(g_szDriverName);
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long Initialize(DRVHANDLE hDriver)
{
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long UnInitialize()
{
	//释放所有的设备	
	for (map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.begin();
		iterDevice != g_mapDevices.end(); iterDevice++)
	{
		delete iterDevice->second;
	}
	g_mapDevices.clear();

    g_CVLogUDPDrv.StopLogThread();
    return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnReadData(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.find(hDevice);
	if (iterDevice != g_mapDevices.end())
	{
		iterDevice->second->read();
	}
	return DRV_SUCCESS;
}

/* 不实现 */
CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDatablock, int nTagByteOffset, int nTagBitOffset, char *szCmdDataBuff, int nCmdDataLenBits)
{
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	long lRet = DRV_SUCCESS;
	map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.find(hDevice);
	
	if (iterDevice == g_mapDevices.end())
	{
		AddDevice(hDevice);
	}

	iterDevice = g_mapDevices.find(hDevice);
	if (iterDevice != g_mapDevices.end())
	{
		CUDPDevice *pUdpDevice = iterDevice->second;
		CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
		if (!pDataBlock || !pDataBlock->pszAddress)
		{
			CV_ERROR(g_CVLogUDPDrv,-1, "failed to get datablock information");
			return EC_UDP_DATABLOCK_NOEXIST;
		}

		lRet = pUdpDevice->AddData(hDevice, hDataBlock);
        //CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
        //CV_INFO(pDataBlock, "device: %s parameters: %s add datablock: %s", pDevice->pszName, pDevice->pszConnParam, pDataBlock->pszName);
	}
	else
	{
		lRet = EC_UDP_DEVICE_NOEXIST;
	}

	return lRet;
}

//获取版本信息  
CVDRIVER_EXPORTS long GetDrvFrameVersion()
{
    return 2;
}

const char * GetPureAddress(const char *pAddr)
{
	static char szAddress[ICV_IOADDR_MAXLEN + 1];
	memset(szAddress, 0, sizeof(szAddress));
	const char *pTemp = strstr(pAddr, ":");
	if (pTemp != NULL)
	{
		const char *pTemp1 = strstr(pTemp + 1, "#");

		if (pTemp1 != NULL)
			memcpy(szAddress, pTemp + 1, pTemp1 - pTemp - 1);
		else
			strncpy(szAddress, pTemp + 1, ICV_IOADDR_MAXLEN);
	}
	return szAddress;
}

CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum,
	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
{
	if (NULL == pOutDevTags || NULL == pnTagsNum || NULL == pTagGrps || NULL == pnTagGrpsNum)
		return EC_ICV_INVALID_PARAMETER;

	memset(pTagGrps, 0, *pnTagGrpsNum * sizeof(TagGroupInfo));

	*pnTagsNum = nTagsNum;
	*pnTagGrpsNum = 0;
	int nBlockNum = 0;

	std::map<string, string> mapDataBlockAddr;

	std::copy(pDevTags, pDevTags + nTagsNum, pOutDevTags);
	
	char szGroupName[ICV_DATABLOCKNAME_MAXLEN + 1];
	memset(szGroupName, 0, sizeof(szGroupName));

	for (int i = 0; i < nTagsNum; ++i)
	{
		string strAddress = GetPureAddress(pDevTags[i].szAddress);
		map<string, string>::iterator iter = mapDataBlockAddr.find(strAddress);
		if (iter != mapDataBlockAddr.end())
		{
			cvcommon::Safe_StrNCopy(pOutDevTags[i].szGrpName, iter->second.c_str(), ICV_DATABLOCKNAME_MAXLEN + 1);
			continue;
		}

		cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szAddress, GetPureAddress(pDevTags[i].szAddress), ICV_IOADDR_MAXLEN);
		pTagGrps[nBlockNum].nElemBits = 8;
		pTagGrps[nBlockNum].nPollRate = 0;
		sprintf(pTagGrps[nBlockNum].szGroupName, "group%d", nBlockNum);
		sprintf(pOutDevTags[i].szGrpName, "group%d", nBlockNum);
		mapDataBlockAddr.insert(make_pair(pTagGrps[nBlockNum].szAddress, pTagGrps[nBlockNum].szGroupName));

		switch(pDevTags[i].nDataType)
		{
		case TAG_DATATYPE_BOOL:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Boolean", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 1;
			break;
			//无符号1字节
		case TAG_DATATYPE_BYTE:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Byte", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 1;
			break;
		case TAG_DATATYPE_INT:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "INT", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 2;
			break;
		case TAG_DATATYPE_DINT:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "DINT", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 4;
			break;
		case TAG_DATATYPE_WORD:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Word", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 2;
			break;
		case TAG_DATATYPE_DWORD:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "DWord", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 4;
			break;
		case TAG_DATATYPE_REAL:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Float", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 4;
			break;
		case TAG_DATATYPE_USINT:
		case TAG_DATATYPE_CHAR:
		case TAG_DATATYPE_SINT:
		case TAG_DATATYPE_UINT:
		case TAG_DATATYPE_TIME:
		case TAG_DATATYPE_UDINT:
		case TAG_DATATYPE_LINT:
		case TAG_DATATYPE_LWORD:
		case TAG_DATATYPE_ULINT:
		case TAG_DATATYPE_LREAL:
		case TAG_DATATYPE_BLOB:
		case TAG_DATATYPE_STRING:
		default:
			break;
		}
		nBlockNum++;
	}
	*pnTagGrpsNum = nBlockNum;

    return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
	AddDevice(hDevice);
	return DRV_SUCCESS;
}
 
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
	Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
	map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.find(hDevice);
	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
	if (iterDevice != g_mapDevices.end())
	{
		// iterDevice->second->disconnect(true);
        CV_INFO(g_CVLogUDPDrv, "device: %s parameters: %s is deleted", pDevice->pszName, pDevice->pszConnParam);
		delete iterDevice->second;
		g_mapDevices.erase(iterDevice);
	}
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnDataBlockTimer(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
	return DRV_SUCCESS;
}
 
CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hCfgDataBlock)
{
	return DRV_SUCCESS;
}

// 添加设备
long AddDevice(DRVHANDLE hDevice)
{
	map<DRVHANDLE, CUDPDevice*>::iterator iterDevice = g_mapDevices.find(hDevice);
	if (iterDevice == g_mapDevices.end())
	{
		CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
		if (!pDevice || !pDevice->pszConnParam)
		{
			CV_ERROR(g_CVLogUDPDrv,-1,"failed to get device information");
			return EC_UDP_DEVICE_NOEXIST;
		}

		CUDPDevice *pUDPDevice = new CUDPDevice(hDevice, pDevice);
		g_mapDevices.insert(make_pair(hDevice, pUDPDevice));
	}

	return DRV_SUCCESS;
}