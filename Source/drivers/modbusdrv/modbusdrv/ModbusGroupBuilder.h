#ifndef CMODBUS_GROUP_BUILDER_HXX
#define CMODBUS_GROUP_BUILDER_HXX
#include "driversdk/cvdrivercommon.h"
#include <vector>
#include <string.h>
#include <stdlib.h>
#include<stdio.h>
#define STATION_MAXLEN		4	
typedef struct _TagInfoWrapper
{
	TagInfo tagInfo;
	char szPureTagAddr[ICV_IOADDR_MAXLEN + 1];
	int nStartAddr;
	int nEndAddr;
	int nStationNo;
}TagInfoWrapper;

typedef std::vector<TagInfo> TagInfoVector;
typedef std::vector<TagInfoWrapper> TagInfoWrapperVector;
typedef std::vector<TagGroupInfo> TagGroupInfoVector;
class CModbusGroupBuilder
{
public:
	CModbusGroupBuilder(const TagInfo *pDevTags, int nTagNum);
	virtual ~CModbusGroupBuilder(void);
	void GroupTags(TagInfoVector &vecDevTags, TagGroupInfoVector &vecTagGrpInfo );
protected:
	void GroupDOTags(TagInfoVector &vecDevTags, TagGroupInfoVector &vecTagGrpInfo);
	void GroupDITags(TagInfoVector &vecDevTags, TagGroupInfoVector &vecTagGrpInfo);
	void GroupAITags(TagInfoVector &vecDevTags, TagGroupInfoVector &vecTagGrpInfo);
	void GroupAOTags(TagInfoVector &vecDevTags, TagGroupInfoVector &vecTagGrpInfo);
	void GetTagWrapperInfo(const TagInfo &tagInfo, TagInfoWrapper &modTagInfo);
	const char *GetPureAddress(const TagInfo &tagInfo, int& nStationNo);
	const char *GenerateGroupName();
private:
	TagInfoWrapperVector m_vecDOTags;
	TagInfoWrapperVector m_vecDITags;
	TagInfoWrapperVector m_vecAOTags;
	TagInfoWrapperVector m_vecAITags;
	static unsigned int m_nGrpNum;

	bool GetParam3(std::string& param3Str);
	bool GetDigitalElemNum(int& digitalElemNum);
	bool GetAnalogElemNum(int& analogElemNum);
};

#endif

