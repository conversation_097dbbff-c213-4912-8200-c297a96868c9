cmake_minimum_required(VERSION 2.6)

PROJECT (modbusdrv)

INCLUDE($ENV{DRDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
#Setting Source Files
SET(SRCS ${SRCS} modbusdrv.cpp ModbusGroupBuilder.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME modbusdrv)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS drdrivercommon drcomm)

SET(SPECOUTDIR /drivers/modbusdrv)
INCLUDE($ENV{DRDIR}CMakeSpecOutPath)
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)
