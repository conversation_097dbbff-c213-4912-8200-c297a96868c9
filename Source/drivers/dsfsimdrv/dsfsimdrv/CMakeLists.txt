cmake_minimum_required(VERSION 3.10)

PROJECT (dsfsimdrv)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} main.cpp dsfsimulatordrv.cpp configloader.cpp driversimreadtask.cpp driversimreadtimer.cpp)


#Setting Target Name (executable file name | library name)
SET(TARGET_NAME dsfsimdrv)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS ${LINK_LIBS} ACE shmqueue sqlite3 cppsqlite drcomm hdOS drlog drdriverapi)

############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonExec)

