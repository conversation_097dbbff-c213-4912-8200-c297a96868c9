#include "dsfsimulatordrv.h"
#include "configloader.h"

extern std::map<int32,TProtoIDVTQ*> g_mapTagDataRWnew;
const char* szDriverName = "dsfsimulatordrv";
CCVLog g_dsfdriverLog;

void fnCtrlCallBack(TProtoDriverAPICTRLMsg *pData)
{

	if (NULL == pData)
	{
		return;
	}

	std::map<int32, TProtoIDVTQ* >::iterator iter = g_mapTagDataRWnew.find(pData->m_nTagID);
	if(iter != g_mapTagDataRWnew.end())
	{
		TProtoIDVTQ* vtq = iter->second;

		switch(pData->m_nDataType)
		{
		case DT_BIT:
			memcpy(vtq->m_pBuf, pData->m_pBuf, pData->m_nLenBuf);
			CV_INFO(g_dsfdriverLog,"Tag id %d write bit data = %d",pData->m_nTagID,*(char*)pData->m_pBuf);
			printf("Tag id %d write bit data = %d\n", pData->m_nTagID, *(char*)pData->m_pBuf);
			break;
		case DT_UCHAR:	
			memcpy(vtq->m_pBuf, pData->m_pBuf, pData->m_nLenBuf);
			CV_INFO(g_dsfdriverLog,"Tag id %d write uchar data = %d",pData->m_nTagID,*(char*)pData->m_pBuf);
			printf("Tag id %d write uchar data = %d\n", pData->m_nTagID, *(char*)pData->m_pBuf);
			break;
		case DT_CHAR:
			memcpy(vtq->m_pBuf, pData->m_pBuf, pData->m_nLenBuf);
			CV_INFO(g_dsfdriverLog,"Tag id %d write char data = %d",pData->m_nTagID,*(char*)pData->m_pBuf);
			printf("Tag id %d write char data = %d\n", pData->m_nTagID, *(char*)pData->m_pBuf);
			break;
		case DT_SINT16:
			memcpy(vtq->m_pBuf, pData->m_pBuf, pData->m_nLenBuf);
			CV_INFO(g_dsfdriverLog,"Tag id %d write sint16 data = %d",pData->m_nTagID,(int)*(short*)pData->m_pBuf);
			printf("Tag id %d write sint16 data = %d\n", pData->m_nTagID, (int)*(short*)pData->m_pBuf);
			break;
		case DT_UINT16:
			memcpy(vtq->m_pBuf, pData->m_pBuf, pData->m_nLenBuf);
			CV_INFO(g_dsfdriverLog,"Tag id %d write uint16 data = %d",pData->m_nTagID,(int)*(short*)pData->m_pBuf);
			printf("Tag id %d write uint16 data = %d\n", pData->m_nTagID, (int)*(short*)pData->m_pBuf);
			break;
		case DT_ULONG:
			memcpy(vtq->m_pBuf, pData->m_pBuf, pData->m_nLenBuf);
			CV_INFO(g_dsfdriverLog,"Tag id %d write ulong data = %d",pData->m_nTagID,*(long*)pData->m_pBuf);
			printf("Tag id %d write ulong data = %d\n", pData->m_nTagID, *(long*)pData->m_pBuf);
			break;
		case DT_SLONG:
			memcpy(vtq->m_pBuf, pData->m_pBuf, pData->m_nLenBuf);
			CV_INFO(g_dsfdriverLog,"Tag id %d write slong data = %d",pData->m_nTagID,*(long*)pData->m_pBuf);
			printf("Tag id %d write slong data = %d\n", pData->m_nTagID, *(long*)pData->m_pBuf);
			break;
		case DT_FLT:
			memcpy(vtq->m_pBuf, pData->m_pBuf, pData->m_nLenBuf);
			CV_INFO(g_dsfdriverLog,"Tag id %d write float data = %.10f",pData->m_nTagID,*(float*)pData->m_pBuf);
			printf("Tag id %d write float data = %.10f\n", pData->m_nTagID, *(float*)pData->m_pBuf);
			break;
		case DT_DBL:
			memcpy(vtq->m_pBuf, pData->m_pBuf, pData->m_nLenBuf);
			CV_INFO(g_dsfdriverLog,"Tag id %d write double data = %.10f",pData->m_nTagID,*(double*)pData->m_pBuf);
			printf("Tag id %d write double data = %.10f\n", pData->m_nTagID, *(double*)pData->m_pBuf);
			break;
		case DT_ASCII:
			memcpy(vtq->m_pBuf, pData->m_pBuf, pData->m_nLenBuf);
			CV_INFO(g_dsfdriverLog,"Tag id %d write txt data = %s",pData->m_nTagID,pData->m_pBuf);
			printf("Tag id %d write txt data = %s", pData->m_nTagID, pData->m_pBuf);
			break;
		default:

			CV_ERROR(g_dsfdriverLog,-1,"Tag id %d error data type.",pData->m_nTagID);
			printf("Tag id %d error data type.\n", pData->m_nTagID);
			break;
		}
	}
}


int main(int argc, char* args[])
{
	if (argc != 3)
	{
		printf("Invalid input params.\n");
		printf("Usage: dsfsimulatordrv <drivername> <timebase>\n");
		printf("Press Enter to exit...");
		getchar();
		return -1;
	}

	CDSFSimulatorDrv simulator;

	simulator.SetLogFileName(szDriverName);

	//读取目标驱动所有的点
	long lRet = CConfigLoader::GetInstance().Init(args[1]);
	if (lRet != ICV_SUCCESS)
		return lRet;

    //模拟初始化驱动节点
	int32 nRet = CVDrv_Init(args[1], true);
	if (nRet != ICV_SUCCESS)
		return nRet;

    //设置控制回调函数
	nRet = CVDrv_SetControlCallback(fnCtrlCallBack);
	if (nRet != ICV_SUCCESS)
		return nRet;

    //启动模拟驱动
	simulator.Start(atoi(args[2]));

    //打印帮助信息
	while (true)
	{
		simulator.PrintHelpScreen();
		char key;
		std::cin >> key; 
		if (key == 'q' || key == 'Q') 
		{
			break;
		}
				
		CVLibHead::WaitSomeTime();
	}

    //结束模拟驱动
	simulator.Fini();
	printf("sub process %s_sim exit!\n", args[1]);
	return ICV_SUCCESS;

}