#ifndef CONFIGLOADER_H
#define CONFIGLOADER_H

#include "boost/thread/mutex.hpp"
#include "boost/thread/shared_mutex.hpp"
#include "proto/proto_comm.h"
#include "common/CppSQLite3.h"
#include "ace/OS_NS_stdio.h"
#include "common/cvdefine.h"
#include "common/cv_datatype.h"
#include "processdb/PDBDef.h"
#include "common/CVLog.h"
#include "common/cvcomm.hxx"
#include "common/TypeCast.h"
#include "errcode/error_code.h"
#include "driversdk/cvdrivercommon.h"
#include "common/os/os_time.h"
#include <string>
#include <list>
#include <vector>


class CConfigLoader
{
public:
	long Init(const char* szSimDrv);
	static CConfigLoader& GetInstance();

	CConfigLoader(const CConfigLoader&) = delete;
	CConfigLoader& operator=(const CConfigLoader&) = delete;

public:
	std::string m_strSimDriver;

private:

	long OpenConfigFile();
	void CloseConfigFile();
	void ReadTagsFromSqlite(const std::string & strSimDriver);

	CConfigLoader()= default;
	~CConfigLoader()= default;
private:
	CppSQLite3DB m_configDB;
	std::vector<TProtoIDVTQ> m_vecTagData;
};
#endif
