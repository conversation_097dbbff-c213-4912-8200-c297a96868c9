#ifndef IPLATDADRIVER_H
#define IPLATDADRIVER_H
#include "driversimreadtask.h"
#include "common/DrvDef.h"
#include "common/ServiceBase.h"
#include "common/Quality.h"

/*
	该类包装好供main函数调用
*/
class CDSFSimulatorDrv
{
public:
	CDSFSimulatorDrv()=default;
	virtual ~CDSFSimulatorDrv()=default;

	long Start(int nTimebase);
	void PrintHelpScreen();
	long Fini();
	void SetLogFileName(const char *szLogFileName);
private:
	CDriverSimReadTask m_simreadtask;
};


#endif

