#include "dsfsimulatordrv.h"
#include <boost/asio.hpp>
#include <boost/date_time/posix_time/posix_time.hpp>
#include <boost/thread.hpp>
#include <boost/bind.hpp>
#include <iostream>
#include "common/CppSQLite3.h"
#include "errcode/error_code.h"
#include "configloader.h"
#include "common/CVLog.h"
using namespace std;

extern std::map<int32,TProtoIDVTQ* > g_mapTagDataRWnew;
extern std::vector<int32 > g_vecTagData;
extern CCVLog g_dsfdriverLog;

long CDSFSimulatorDrv::Start(int nTimebase)
{
	m_simreadtask.Activate(nTimebase);
	return ICV_SUCCESS;
}


void CDSFSimulatorDrv::PrintHelpScreen()
{
	printf("\n");
	printf("+============================================+\n");
	printf("|  <<Welcome to use dsfsimulatordrv! >>   	 |\n");
	printf("|  Input the following command          	 |\n");
	printf("|  q/Q:exit                             	 |\n");
	printf("|  other key: display tip               	 |\n");
	printf("+============================================+\n");
}

long CDSFSimulatorDrv::Fini()
{
	m_simreadtask.ShutDown();

	TCV_TimeStamp cvTimeStamp = (timeval)ACE_OS::gettimeofday();

	std::map<int32,TProtoIDVTQ* >::iterator iter = g_mapTagDataRWnew.begin();
	for( ; iter != g_mapTagDataRWnew.end(); ++iter)
	{
		SAFE_DELETE(iter->second->m_pBuf);
		SAFE_DELETE(iter->second);
	}
	ACE_INT16  nNewQuality; // nNewQuality = 12

	((QUALITY_STATE*)&nNewQuality)->nQuality = QUALITY_BAD;
	((QUALITY_STATE*)&nNewQuality)->nSubStatus = SS_NOT_CONNECTED;
	((QUALITY_STATE*)&nNewQuality)->nLimit = LIMIT_NOT_LIMITED;
	((QUALITY_STATE*)&nNewQuality)->nLeftOver = 0;	

	CVDrv_SetBlockQuality(g_vecTagData,&cvTimeStamp,nNewQuality);

	CVDrv_Release(CConfigLoader::GetInstance().m_strSimDriver);

	g_dsfdriverLog.StopLogThread();

	return ICV_SUCCESS;
}

void CDSFSimulatorDrv::SetLogFileName(const char *szLogFileName)
{
	g_dsfdriverLog.SetLogFileNameThread(szLogFileName);
}