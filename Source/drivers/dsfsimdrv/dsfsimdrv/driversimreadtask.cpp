#include "driversimreadtask.h"
#include "configloader.h"

extern CCVLog g_dsfdriverLog;
extern std::map<int32,TProtoIDVTQ*> g_mapTagDataRWnew;
#define MAX_FUTURE_TIME				5

CDriverSimReadTask::CDriverSimReadTask()
{
	ACE_NEW(m_pTimerQue, CV_Timer_Queue);
	m_psimReadTimer = new CDriverSimReadTimer();
	m_bExit = false;
}

CDriverSimReadTask::~CDriverSimReadTask()
{
	SAFE_DELETE(m_pTimerQue);
	SAFE_DELETE(m_psimReadTimer);
}

long CDriverSimReadTask::Activate(int nTimebase)
{
	m_bExit = false;
	m_nTimebase = nTimebase;
	int nRet = this->activate(THR_NEW_LWP | THR_JOINABLE |THR_INHERIT_SCHED);
	if (0 == nRet)
		return 0;

	return nRet;
}

void CDriverSimReadTask::ShutDown()
{
	this->m_bExit = true;
	this->wait();
	CV_WARN(g_dsfdriverLog, -1, "CDriverSimReadTask thread exit!");
}


int CDriverSimReadTask::DoTask()
{
	//设置立马执行
	// const ACE_Time_Value future_time = ACE_OS::gettimeofday()+ ACE_Time_Value(1, 0);
	const ACE_Time_Value future_time = ACE_High_Res_Timer::gettimeofday_hr();
	//设置定时器间隔
	ACE_Time_Value interval(m_nTimebase/1000,m_nTimebase%1000 * 1000);
	long nTimerID = m_pTimerQue->schedule(m_psimReadTimer, NULL, future_time, interval);
	if(nTimerID == -1)
	{
		CV_ERROR(g_dsfdriverLog,nTimerID,"CDriverSimReadTask:Set Timer failed!");
		return -1;
	}

	ACE_Event timer;

	while (!m_bExit)
	{
		ACE_Time_Value max_tv(MAX_FUTURE_TIME);
		ACE_Time_Value *this_timeout = m_pTimerQue->calculate_timeout(&max_tv);

		if (*this_timeout == ACE_Time_Value::zero){
			m_pTimerQue->expire();
		}
		else{
			if (timer.wait(this_timeout, 0) == -1){
				m_pTimerQue->expire();
			}
		}
	}

	CV_INFO(g_dsfdriverLog,"CDriverSimReadTask::DoTask(),Stop Timer Event Loop!");

	m_pTimerQue->cancel(m_psimReadTimer);
	
	if (m_psimReadTimer != NULL)
	{
		delete m_psimReadTimer;
		m_psimReadTimer = NULL;
	}

	return ICV_SUCCESS;
}

int CDriverSimReadTask::svc()
{
	while(true)
	{
		try
		{
			return DoTask();
		}
		catch (...)
		{
			CV_ERROR(g_dsfdriverLog,-1,"Unknown Exception Occur when invoking DoTask, in CDriverSimReadTask::svc()");
		}

	}

	return 0;
}	
