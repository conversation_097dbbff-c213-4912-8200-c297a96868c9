#ifndef DRIVERSIMREADTASK_H
#define DRIVERSIMREADTASK_H

#include "proto/proto_comm.h"
#include "processdb/DriverApi.h"
#include "errcode/error_code.h"
#include "common/CommHelper.h"
#include "common/CV_Time.h"
#include "common/CV_Timer_Queue.h"
#include "common/os/os_time.h"
#include "common/gettimeofday.h"
#include "data_types.h"
#include <ace/Event.h>
#include <ace/Event_Handler.h>
#include <ace/Timer_Queue.h>

#include "common/CV_Timer_Queue.h"
#include "common/CVLog.h"
#include "driversimreadtimer.h"
#include <vector>

class CDriverSimReadTimer;
class CDriverSimReadTask: public ACE_Task<ACE_MT_SYNCH>  
{
public:
	CDriverSimReadTask();
	~CDriverSimReadTask();

public:
	virtual int svc(void) override;
	int DoTask();
	long Activate(int nTimebase = 1000);
	void ShutDown();

private:
	CDriverSimReadTimer* m_psimReadTimer;
	CV_Timer_Queue*	m_pTimerQue;
	bool m_bExit;
	int  m_nTimebase; // 时间基准，单位为毫秒
};

#endif
