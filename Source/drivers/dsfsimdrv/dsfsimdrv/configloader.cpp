#include "configloader.h"

extern CCVLog g_dsfdriverLog;
extern const char* szDriverName;
using namespace std;

std::map<int32,TProtoIDVTQ*> g_mapTagDataRWnew;//tagid和数据的映射
std::vector<int32> g_vecTagData;//tagid集合

long CConfigLoader::Init(const char* szSimDrv)
{
	long lRet = ICV_SUCCESS;

	m_strSimDriver = szSimDrv;

	if((lRet = OpenConfigFile()) == ICV_SUCCESS)
	{
		ReadTagsFromSqlite(m_strSimDriver);
		CloseConfigFile();
	}
	return lRet;
}

CConfigLoader& CConfigLoader::GetInstance()
{
	static CConfigLoader instance;
	return instance;
}


long CConfigLoader::OpenConfigFile()
{
	static char	szCfgFileAbsName[ICV_LONGFILENAME_MAXLEN] = {0};
	const char* pCfgPath = CVComm.GetCVProjCfgPath();
	if(pCfgPath == NULL)
	{
		CV_ERROR(g_dsfdriverLog,-1, "CConfigLoader: failed to get the path of icv config file.");
		return -1;
	}
	else if(strlen(pCfgPath) + strlen(ACE_DIRECTORY_SEPARATOR_STR) 
		+ strlen(PDB_CFG_FILE_NAME) > ICV_LONGFILENAME_MAXLEN)
	{
		CV_ERROR(g_dsfdriverLog,EC_ICV_COMM_PATHTOOLONG, "CConfigLoader: the config path exceeds the max path length!");
		return -1;
	}
	else
	{
		ACE_OS::snprintf(szCfgFileAbsName, sizeof(szCfgFileAbsName), "%s" ACE_DIRECTORY_SEPARATOR_STR "%s",
			pCfgPath, PDB_CFG_FILE_NAME);
	}
	
	try
	{
		m_configDB.open(szCfgFileAbsName);
	}
	catch (CppSQLite3Exception &e)
	{
		e.errorMessage();
		CV_ERROR(g_dsfdriverLog,EC_ICV_CONFIG_FILE_ERROR, "Fail to open DB(%s) %d, ErrMsg:%s", szCfgFileAbsName, e.errorCode() , e.errorMessage());
		return EC_ICV_CONFIG_FILE_ERROR;	
	}

	return ICV_SUCCESS;
}

void CConfigLoader::CloseConfigFile()
{
	m_configDB.close();
}

void CConfigLoader::ReadTagsFromSqlite(const std::string& strSimDriver)
{
	const char* tableName = "t_pb_tags";

	try
	{
		char szSQL[ICV_SQLSCRIPT_MAXLEN];
		memset(szSQL, 0, ICV_SQLSCRIPT_MAXLEN);
		sprintf(szSQL, "select DISTINCT fd_block_name, fd_iodrv, fd_tagid, fd_scan_intv, fd_data_type from %s \
					   where fd_iodrv = '%s';", tableName, strSimDriver.c_str());
		CppSQLite3Table tTagName = m_configDB.getTable(szSQL);
		int nRowCount = tTagName.numRows();

		uint32 nSec;
		uint16 nMsec;
		for (int i = 0; i < nRowCount; ++ i)
		{
			TProtoIDVTQ* tagvtq = new TProtoIDVTQ;
			memset(tagvtq, 0, sizeof(tagvtq));

			tTagName.setRow(i);
			
			tagvtq->m_nTagID = atoi(tTagName.fieldValue(2));
			os_get_curr_time_sec_and_msec(&nSec,&nMsec);
			tagvtq->m_nMs = (int16)nMsec;
			tagvtq->m_nSec = (int32)nSec;
			tagvtq->m_nQuality = 192;
			tagvtq->m_nDataType = atoi(tTagName.fieldValue(4));

			tagvtq->m_nLenBuf = g_DTSize[tagvtq->m_nDataType];
			tagvtq->m_pBuf = new char[tagvtq->m_nLenBuf];
			memset(tagvtq->m_pBuf,0,tagvtq->m_nLenBuf);

			g_vecTagData.push_back(tagvtq->m_nTagID);
			g_mapTagDataRWnew.insert(std::make_pair(tagvtq->m_nTagID,tagvtq));
		}

	}
	catch(CppSQLite3Exception& e)
	{
		CV_ERROR(g_dsfdriverLog,-1, "Fail to read table %s return %d, ErrMsg:%s", tableName, e.errorCode() , e.errorMessage());
	}

	std::cout<<"Read "<< g_mapTagDataRWnew.size() << " tags from table " << tableName << std::endl;
}