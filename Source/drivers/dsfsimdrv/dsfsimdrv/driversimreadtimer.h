#ifndef DRIVERSIMREADTIMER_H
#define DRIVERSIMREADTIMER_H


#include "proto/proto_comm.h"
#include "processdb/DriverApi.h"
#include "errcode/error_code.h"
#include "common/CV_Time.h"
#include "common/CV_Timer_Queue.h"
#include "common/os/os_time.h"
#include "data_types.h"
#include "ace/Task.h"
#include "ace/Event_Handler.h"
#include "ace/Reactor.h"
#include "ace/Thread_Manager.h"
#include "common/CVLog.h"
#include "driversimreadtask.h"
#include <vector>
#include <map>
using namespace std;

class CDriverSimReadTask;
/**
 *  @brief 定时事件处理逻辑类
 */
class CDriverSimReadTimer : public ACE_Event_Handler
{
public:
	CDriverSimReadTimer();
	virtual ~CDriverSimReadTimer();

	virtual int	handle_timeout(const ACE_Time_Value &current_time, const void *act);

private:
	int         m_nSliceSize = 10000;	// 每次上抛给采集服务的点数
	int32_t 	m_nValueCount = 0;		// 发送的数值
};



#endif