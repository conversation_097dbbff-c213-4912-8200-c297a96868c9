#include "driversimreadtimer.h"
#include "configloader.h"

extern std::map<int32,TProtoIDVTQ*> g_mapTagDataRWnew;
extern CCVLog g_dsfdriverLog;

CDriverSimReadTimer::CDriverSimReadTimer()
{
}


CDriverSimReadTimer::~CDriverSimReadTimer(void)
{
}

int CDriverSimReadTimer::handle_timeout(const ACE_Time_Value &current_time, const void *act)
{
	uint32 nSec;
	uint16 nMsec;
	long lRet;

	std::vector<TProtoIDVTQ> vecdata;

	m_nValueCount++;

	if(m_nValueCount > 255)
	{
		m_nValueCount = 0;
	}
	
	std::map<int32, TProtoIDVTQ* >::iterator iter = g_mapTagDataRWnew.begin();
	for( ; iter != g_mapTagDataRWnew.end(); ++iter)
	{

		TProtoIDVTQ* vtq = iter->second;
		TProtoIDVTQ tempVtq;

		os_get_curr_time_sec_and_msec(&nSec,&nMsec);
		vtq->m_nMs = (int16)nMsec;
		vtq->m_nSec = (int32)nSec;

		tempVtq.m_nMs = vtq->m_nMs;
		tempVtq.m_nSec = vtq->m_nSec;
		tempVtq.m_nDataType = vtq->m_nDataType;
		tempVtq.m_nQuality =  vtq->m_nQuality;
		tempVtq.m_nTagID =  vtq->m_nTagID;
		tempVtq.m_nLenBuf =  vtq->m_nLenBuf;
		tempVtq.m_pBuf = new char[tempVtq.m_nLenBuf];
		memset(tempVtq.m_pBuf,0,tempVtq.m_nLenBuf);

		/**
		 * 支持的类型：bool, char, word, dword, int, dint, float
		*/
		switch(tempVtq.m_nDataType)
		{
		case DT_BIT:
			{
				char cTemp;
			
				if((int)m_nValueCount % 2 == 0)
				{
					cTemp = 1;
					memcpy(tempVtq.m_pBuf, &cTemp, 1);
				}
				else
				{
					cTemp = 0;
					memcpy(tempVtq.m_pBuf, &cTemp, 1);
				}
				
				break;
			}

		case DT_BYTE:
			{
				char cTemp;
				cTemp = m_nValueCount;	
				memcpy(tempVtq.m_pBuf, &cTemp, 1);
				break;
			}

		case DT_WORLD:
			{
				uint16 nTemp;
				nTemp = m_nValueCount;
				memcpy(tempVtq.m_pBuf, &nTemp, tempVtq.m_nLenBuf);
				break;
			}
		//代码单词拼错了，应该是WORD
		case DT_DWORLD:
			{
				uint32 nTemp;
				nTemp = m_nValueCount;
				memcpy(tempVtq.m_pBuf, &nTemp, tempVtq.m_nLenBuf);
				break;
			}
		case DT_SINT16:	
			{
				int16 nTemp;
				nTemp = m_nValueCount;
				memcpy(tempVtq.m_pBuf, &nTemp, tempVtq.m_nLenBuf);
				break;
			}
		case DT_SLONG:
			{
				int32 nTemp;
				nTemp = m_nValueCount;
				memcpy(tempVtq.m_pBuf, &nTemp, tempVtq.m_nLenBuf);
				break;
			}

		case DT_FLT:
			{
				float nFloatData;
				nFloatData = (float)m_nValueCount;
				memcpy(tempVtq.m_pBuf, &nFloatData, tempVtq.m_nLenBuf);
				break;
			}
		default:
			{
				CV_ERROR(g_dsfdriverLog,-1,"TagId %d Invalid DataType %d",tempVtq.m_nTagID,tempVtq.m_nDataType);
				break;
			}	
		}	
		vecdata.push_back(tempVtq);
	}

	int nSlice = vecdata.size()/m_nSliceSize + 1;
	if(vecdata.size() > m_nSliceSize)
	{
		for(int i = 0; i < nSlice;++i )
		{
			if(i == nSlice - 1)
			{
				std::vector<TProtoIDVTQ> vecdataTmpEnd;
				int nSize =  vecdata.size() % m_nSliceSize;
				//CVDrv_SaveData接口如果传入的vecdata.size()为0，代码则会陷入异常状态：定时器不再执行，没有错误提示。
				if(nSize == 0)
				{
					break;
				}
				for(int k = 0;k < nSize;++k)
				{
					vecdataTmpEnd.push_back(vecdata[m_nSliceSize*i + k]);
				}
				lRet = CVDrv_SaveData(&vecdataTmpEnd);
				if(-1 == lRet)
				{
					CV_ERROR(g_dsfdriverLog,-1,"SimDriver update failed Tag Number:%d",vecdataTmpEnd.size());
				}
				else
				{
					CV_INFO(g_dsfdriverLog,"SimDriver update successed Tag Number:%d",vecdataTmpEnd.size());
				}
			}
			else
			{
				std::vector<TProtoIDVTQ> vecdataTmp;
				for(int j = 0;j< m_nSliceSize;++j)
				{			
					vecdataTmp.push_back(vecdata[m_nSliceSize*i + j]);
				}
				lRet = CVDrv_SaveData(&vecdataTmp);
				if(-1 == lRet)
				{
					CV_ERROR(g_dsfdriverLog,-1,"SimDriver update failed Tag Number:%d",vecdataTmp.size());
				}
				else
				{
					CV_INFO(g_dsfdriverLog,"SimDriver update successed Tag Number:%d",vecdataTmp.size());
				}
			}
		}
	}
	else
	{
		lRet = CVDrv_SaveData(&vecdata);
		
		if(-1 == lRet)
		{
			CV_ERROR(g_dsfdriverLog,-1,"SimDriver Tag Number:%d",vecdata.size());
		}
		else
		{
			CV_INFO(g_dsfdriverLog,"SimDriver update successed Tag Number:%d",vecdata.size());
		}
	}
	for(int p = 0;p < vecdata.size();++p)
	{
		SAFE_DELETE(vecdata[p].m_pBuf);
	}

	return lRet;
}