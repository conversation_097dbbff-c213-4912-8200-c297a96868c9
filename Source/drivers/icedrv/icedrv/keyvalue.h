// **********************************************************************
//
// Copyright (c) 2003-2018 ZeroC, Inc. All rights reserved.
//
// This copy of Ice is licensed to you under the terms described in the
// ICE_LICENSE file included in this distribution.
//
// **********************************************************************
//
// Ice version 3.7.1
//
// <auto-generated>
//
// Generated from file `keyvalue.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#ifndef __keyvalue_h__
#define __keyvalue_h__

#include <IceUtil/PushDisableWarnings.h>
#include <Ice/ProxyF.h>
#include <Ice/ObjectF.h>
#include <Ice/ValueF.h>
#include <Ice/Exception.h>
#include <Ice/LocalObject.h>
#include <Ice/StreamHelpers.h>
#include <Ice/Comparable.h>
#include <Ice/Proxy.h>
#include <Ice/Object.h>
#include <Ice/GCObject.h>
#include <Ice/Value.h>
#include <Ice/Incoming.h>
#include <Ice/FactoryTableInit.h>
#include <IceUtil/ScopedArray.h>
#include <Ice/Optional.h>
#include <IceUtil/UndefSysMacros.h>

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 > 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 1
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace DSF
{

class DataReceiver;
class DataReceiverPrx;

}

namespace DSF
{

enum class ValueType : unsigned char
{
    Decimal,
    Integer,
    Boolean,
    Text
};

struct DataUnit
{
    ::std::string strName;
    long long int lTime;
    ValueType eType;
    double dValue;
    long long int lValue;
    bool bValue;
    ::std::string strValue;

    /**
     * Obtains a tuple containing all of the exception's data members.
     * @return The data members in a tuple.
     */

    std::tuple<const ::std::string&, const long long int&, const ValueType&, const double&, const long long int&, const bool&, const ::std::string&> ice_tuple() const
    {
        return std::tie(strName, lTime, eType, dValue, lValue, bValue, strValue);
    }
};

using DataUnitSeq = ::std::vector<DataUnit>;

using Ice::operator<;
using Ice::operator<=;
using Ice::operator>;
using Ice::operator>=;
using Ice::operator==;
using Ice::operator!=;

}

namespace DSF
{

class DataReceiver : public virtual ::Ice::Object
{
public:

    using ProxyType = DataReceiverPrx;

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(::std::string id, const ::Ice::Current& current) const override;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector<::std::string> ice_ids(const ::Ice::Current& current) const override;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual ::std::string ice_id(const ::Ice::Current& current) const override;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual void sendData(DataUnitSeq dataSeq, const ::Ice::Current& current) = 0;
    /// \cond INTERNAL
    bool _iceD_sendData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&) override;
    /// \endcond
};

}

namespace DSF
{

class DataReceiverPrx : public virtual ::Ice::Proxy<DataReceiverPrx, ::Ice::ObjectPrx>
{
public:

    void sendData(const DataUnitSeq& dataSeq, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        _makePromiseOutgoing<void>(true, this, &DataReceiverPrx::_iceI_sendData, dataSeq, context).get();
    }

    template<template<typename> class P = ::std::promise>
    auto sendDataAsync(const DataUnitSeq& dataSeq, const ::Ice::Context& context = ::Ice::noExplicitContext)
        -> decltype(::std::declval<P<void>>().get_future())
    {
        return _makePromiseOutgoing<void, P>(false, this, &DataReceiverPrx::_iceI_sendData, dataSeq, context);
    }

    ::std::function<void()>
    sendDataAsync(const DataUnitSeq& dataSeq,
                  ::std::function<void()> response,
                  ::std::function<void(::std::exception_ptr)> ex = nullptr,
                  ::std::function<void(bool)> sent = nullptr,
                  const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _makeLamdaOutgoing<void>(response, ex, sent, this, &DataReceiverPrx::_iceI_sendData, dataSeq, context);
    }

    /// \cond INTERNAL
    void _iceI_sendData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>&, const DataUnitSeq&, const ::Ice::Context&);
    /// \endcond

    /**
     * Obtains the Slice type ID of this interface.
     * @return The fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:

    /// \cond INTERNAL
    DataReceiverPrx() = default;
    friend ::std::shared_ptr<DataReceiverPrx> IceInternal::createProxy<DataReceiverPrx>();

    virtual ::std::shared_ptr<::Ice::ObjectPrx> _newInstance() const override;
    /// \endcond
};

}

/// \cond STREAM
namespace Ice
{

template<>
struct StreamableTraits< ::DSF::ValueType>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 3;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits<::DSF::DataUnit>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 28;
    static const bool fixedLength = false;
};

template<typename S>
struct StreamReader<::DSF::DataUnit, S>
{
    static void read(S* istr, ::DSF::DataUnit& v)
    {
        istr->readAll(v.strName, v.lTime, v.eType, v.dValue, v.lValue, v.bValue, v.strValue);
    }
};

}
/// \endcond

/// \cond INTERNAL
namespace DSF
{

using DataReceiverPtr = ::std::shared_ptr<DataReceiver>;
using DataReceiverPrxPtr = ::std::shared_ptr<DataReceiverPrx>;

}
/// \endcond

#else // C++98 mapping

namespace IceProxy
{

namespace DSF
{

class DataReceiver;
/// \cond INTERNAL
void _readProxy(::Ice::InputStream*, ::IceInternal::ProxyHandle< ::IceProxy::DSF::DataReceiver>&);
::IceProxy::Ice::Object* upCast(::IceProxy::DSF::DataReceiver*);
/// \endcond

}

}

namespace DSF
{

class DataReceiver;
/// \cond INTERNAL
::Ice::Object* upCast(DataReceiver*);
/// \endcond
typedef ::IceInternal::Handle< DataReceiver> DataReceiverPtr;
typedef ::IceInternal::ProxyHandle< ::IceProxy::DSF::DataReceiver> DataReceiverPrx;
typedef DataReceiverPrx DataReceiverPrxPtr;
/// \cond INTERNAL
void _icePatchObjectPtr(DataReceiverPtr&, const ::Ice::ObjectPtr&);
/// \endcond

}

namespace DSF
{

enum ValueType
{
    Decimal,
    Integer,
    Boolean,
    Text
};

struct DataUnit
{
    ::std::string strName;
    ::Ice::Long lTime;
    ValueType eType;
    ::Ice::Double dValue;
    ::Ice::Long lValue;
    bool bValue;
    ::std::string strValue;
};

typedef ::std::vector<DSF::DataUnit> DataUnitSeq;

}

namespace DSF
{

/**
 * Base class for asynchronous callback wrapper classes used for calls to
 * IceProxy::DSF::DataReceiver::begin_sendData.
 * Create a wrapper instance by calling ::DSF::newCallback_DataReceiver_sendData.
 */
class Callback_DataReceiver_sendData_Base : public virtual ::IceInternal::CallbackBase { };
typedef ::IceUtil::Handle< Callback_DataReceiver_sendData_Base> Callback_DataReceiver_sendDataPtr;

}

namespace IceProxy
{

namespace DSF
{

class DataReceiver : public virtual ::Ice::Proxy<DataReceiver, ::IceProxy::Ice::Object>
{
public:

    void sendData(const ::DSF::DataUnitSeq& dataSeq, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        end_sendData(_iceI_begin_sendData(dataSeq, context, ::IceInternal::dummyCallback, 0, true));
    }

    ::Ice::AsyncResultPtr begin_sendData(const ::DSF::DataUnitSeq& dataSeq, const ::Ice::Context& context = ::Ice::noExplicitContext)
    {
        return _iceI_begin_sendData(dataSeq, context, ::IceInternal::dummyCallback, 0);
    }

    ::Ice::AsyncResultPtr begin_sendData(const ::DSF::DataUnitSeq& dataSeq, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendData(dataSeq, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendData(const ::DSF::DataUnitSeq& dataSeq, const ::Ice::Context& context, const ::Ice::CallbackPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendData(dataSeq, context, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendData(const ::DSF::DataUnitSeq& dataSeq, const ::DSF::Callback_DataReceiver_sendDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendData(dataSeq, ::Ice::noExplicitContext, cb, cookie);
    }

    ::Ice::AsyncResultPtr begin_sendData(const ::DSF::DataUnitSeq& dataSeq, const ::Ice::Context& context, const ::DSF::Callback_DataReceiver_sendDataPtr& cb, const ::Ice::LocalObjectPtr& cookie = 0)
    {
        return _iceI_begin_sendData(dataSeq, context, cb, cookie);
    }

    void end_sendData(const ::Ice::AsyncResultPtr& result);

private:

    ::Ice::AsyncResultPtr _iceI_begin_sendData(const ::DSF::DataUnitSeq&, const ::Ice::Context&, const ::IceInternal::CallbackBasePtr&, const ::Ice::LocalObjectPtr& cookie = 0, bool sync = false);

public:

    /**
     * Obtains the Slice type ID corresponding to this interface.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

protected:
    /// \cond INTERNAL

    virtual ::IceProxy::Ice::Object* _newInstance() const;
    /// \endcond
};

}

}

namespace DSF
{

class DataReceiver : public virtual ::Ice::Object
{
public:

    typedef DataReceiverPrx ProxyType;
    typedef DataReceiverPtr PointerType;

    virtual ~DataReceiver();

    /**
     * Determines whether this object supports an interface with the given Slice type ID.
     * @param id The fully-scoped Slice type ID.
     * @param current The Current object for the invocation.
     * @return True if this object supports the interface, false, otherwise.
     */
    virtual bool ice_isA(const ::std::string& id, const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a list of the Slice type IDs representing the interfaces supported by this object.
     * @param current The Current object for the invocation.
     * @return A list of fully-scoped type IDs.
     */
    virtual ::std::vector< ::std::string> ice_ids(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains a Slice type ID representing the most-derived interface supported by this object.
     * @param current The Current object for the invocation.
     * @return A fully-scoped type ID.
     */
    virtual const ::std::string& ice_id(const ::Ice::Current& current = ::Ice::emptyCurrent) const;

    /**
     * Obtains the Slice type ID corresponding to this class.
     * @return A fully-scoped type ID.
     */
    static const ::std::string& ice_staticId();

    virtual void sendData(const DataUnitSeq& dataSeq, const ::Ice::Current& current = ::Ice::emptyCurrent) = 0;
    /// \cond INTERNAL
    bool _iceD_sendData(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

    /// \cond INTERNAL
    virtual bool _iceDispatch(::IceInternal::Incoming&, const ::Ice::Current&);
    /// \endcond

protected:

    /// \cond STREAM
    virtual void _iceWriteImpl(::Ice::OutputStream*) const;
    virtual void _iceReadImpl(::Ice::InputStream*);
    /// \endcond
};

/// \cond INTERNAL
inline bool operator==(const DataReceiver& lhs, const DataReceiver& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) == static_cast<const ::Ice::Object&>(rhs);
}

inline bool operator<(const DataReceiver& lhs, const DataReceiver& rhs)
{
    return static_cast<const ::Ice::Object&>(lhs) < static_cast<const ::Ice::Object&>(rhs);
}
/// \endcond

}

/// \cond STREAM
namespace Ice
{

template<>
struct StreamableTraits< ::DSF::ValueType>
{
    static const StreamHelperCategory helper = StreamHelperCategoryEnum;
    static const int minValue = 0;
    static const int maxValue = 3;
    static const int minWireSize = 1;
    static const bool fixedLength = false;
};

template<>
struct StreamableTraits< ::DSF::DataUnit>
{
    static const StreamHelperCategory helper = StreamHelperCategoryStruct;
    static const int minWireSize = 28;
    static const bool fixedLength = false;
};

template<typename S>
struct StreamWriter< ::DSF::DataUnit, S>
{
    static void write(S* ostr, const ::DSF::DataUnit& v)
    {
        ostr->write(v.strName);
        ostr->write(v.lTime);
        ostr->write(v.eType);
        ostr->write(v.dValue);
        ostr->write(v.lValue);
        ostr->write(v.bValue);
        ostr->write(v.strValue);
    }
};

template<typename S>
struct StreamReader< ::DSF::DataUnit, S>
{
    static void read(S* istr, ::DSF::DataUnit& v)
    {
        istr->read(v.strName);
        istr->read(v.lTime);
        istr->read(v.eType);
        istr->read(v.dValue);
        istr->read(v.lValue);
        istr->read(v.bValue);
        istr->read(v.strValue);
    }
};

}
/// \endcond

namespace DSF
{

/**
 * Type-safe asynchronous callback wrapper class used for calls to
 * IceProxy::DSF::DataReceiver::begin_sendData.
 * Create a wrapper instance by calling ::DSF::newCallback_DataReceiver_sendData.
 */
template<class T>
class CallbackNC_DataReceiver_sendData : public Callback_DataReceiver_sendData_Base, public ::IceInternal::OnewayCallbackNC<T>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception&);
    typedef void (T::*Sent)(bool);
    typedef void (T::*Response)();

    CallbackNC_DataReceiver_sendData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallbackNC<T>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::DSF::DataReceiver::begin_sendData.
 */
template<class T> Callback_DataReceiver_sendDataPtr
newCallback_DataReceiver_sendData(const IceUtil::Handle<T>& instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_DataReceiver_sendData<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::DSF::DataReceiver::begin_sendData.
 */
template<class T> Callback_DataReceiver_sendDataPtr
newCallback_DataReceiver_sendData(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_DataReceiver_sendData<T>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::DSF::DataReceiver::begin_sendData.
 */
template<class T> Callback_DataReceiver_sendDataPtr
newCallback_DataReceiver_sendData(T* instance, void (T::*cb)(), void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_DataReceiver_sendData<T>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::DSF::DataReceiver::begin_sendData.
 */
template<class T> Callback_DataReceiver_sendDataPtr
newCallback_DataReceiver_sendData(T* instance, void (T::*excb)(const ::Ice::Exception&), void (T::*sentcb)(bool) = 0)
{
    return new CallbackNC_DataReceiver_sendData<T>(instance, 0, excb, sentcb);
}

/**
 * Type-safe asynchronous callback wrapper class with cookie support used for calls to
 * IceProxy::DSF::DataReceiver::begin_sendData.
 * Create a wrapper instance by calling ::DSF::newCallback_DataReceiver_sendData.
 */
template<class T, typename CT>
class Callback_DataReceiver_sendData : public Callback_DataReceiver_sendData_Base, public ::IceInternal::OnewayCallback<T, CT>
{
public:

    typedef IceUtil::Handle<T> TPtr;

    typedef void (T::*Exception)(const ::Ice::Exception& , const CT&);
    typedef void (T::*Sent)(bool , const CT&);
    typedef void (T::*Response)(const CT&);

    Callback_DataReceiver_sendData(const TPtr& obj, Response cb, Exception excb, Sent sentcb)
        : ::IceInternal::OnewayCallback<T, CT>(obj, cb, excb, sentcb)
    {
    }
};

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::DSF::DataReceiver::begin_sendData.
 */
template<class T, typename CT> Callback_DataReceiver_sendDataPtr
newCallback_DataReceiver_sendData(const IceUtil::Handle<T>& instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_DataReceiver_sendData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::DSF::DataReceiver::begin_sendData.
 */
template<class T, typename CT> Callback_DataReceiver_sendDataPtr
newCallback_DataReceiver_sendData(const IceUtil::Handle<T>& instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_DataReceiver_sendData<T, CT>(instance, 0, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param cb The success method of the callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::DSF::DataReceiver::begin_sendData.
 */
template<class T, typename CT> Callback_DataReceiver_sendDataPtr
newCallback_DataReceiver_sendData(T* instance, void (T::*cb)(const CT&), void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_DataReceiver_sendData<T, CT>(instance, cb, excb, sentcb);
}

/**
 * Creates a callback wrapper instance that delegates to your object.
 * Use this overload when your callback methods receive a cookie value.
 * @param instance The callback object.
 * @param excb The exception method of the callback object.
 * @param sentcb The sent method of the callback object.
 * @return An object that can be passed to an asynchronous invocation of IceProxy::DSF::DataReceiver::begin_sendData.
 */
template<class T, typename CT> Callback_DataReceiver_sendDataPtr
newCallback_DataReceiver_sendData(T* instance, void (T::*excb)(const ::Ice::Exception&, const CT&), void (T::*sentcb)(bool, const CT&) = 0)
{
    return new Callback_DataReceiver_sendData<T, CT>(instance, 0, excb, sentcb);
}

}

#endif

#include <IceUtil/PopDisableWarnings.h>
#endif
