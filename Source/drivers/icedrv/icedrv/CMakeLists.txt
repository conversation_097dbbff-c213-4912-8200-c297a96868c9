cmake_minimum_required(VERSION 3.10)
PROJECT (icedrv)

INCLUDE(${CMAKE_SOURCE_DIR}/CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
#Setting Source Files
SET(SRCS ${SRCS} icedrv.cpp icedevice.cpp keyvalue.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME icedrv)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS drdrivercommon ACE drcomm  Ice) #ssl crypto fastcdr fastrtps)


SET(SPECOUTDIR /drivers/icedrv)

INCLUDE(${CMAKE_SOURCE_DIR}/CMakeSpecOutPath)
############FOR_MODIFIY_END#########################
INCLUDE(${CMAKE_SOURCE_DIR}/CMakeCommonLib)
