/**
 * Filename        icedevice.cpp
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description
 *
 * Author          wuzheqiang
 * Version         06/18/2025    wuzheqiang    Initial Version
 **************************************************************/

#include "icedevice.h"
#include "TypeCast.h"
#include "ace/OS_NS_strings.h"
#include "common/DrvDef.h"
#include "common/RMAPI.h"
#include "errcode/error_code.h"
#include "os/os_time.h"
#include <ace/Guard_T.h>
#include <ace/Thread_Mutex.h>

extern CCVLog g_CVLogICEDrv;
constexpr int32 MAX_COUNT_PER_SECOND = 10000;

ICEDevice::ICEDevice(DRVHANDLE hDevice)
{
    m_hDevice = hDevice;
    CVDEVICE *pDevice = Drv_GetDeviceInfo(m_hDevice);
    SetConnectParam(pDevice->pszConnParam);
    if (pDevice->pszParam1)
    {
        m_nSendRetrySeconds = std::stoul(pDevice->pszParam1);
    }
    m_bStop = false;
}

ICEDevice::~ICEDevice()
{
    CV_INFO(g_CVLogICEDrv, "Destruction  ICEDevice");
    m_bStop = true;
}

// "ip=127.0.0.1;port=61234;name=dsfreceiver"
long ICEDevice::SetConnectParam(const char *szConnParam)
{
    string strConnParam = szConnParam;
    CV_INFO(g_CVLogICEDrv, "Set connection parameter %s", szConnParam);
    strConnParam += ";"; // ����һ���ֺ�
    int nPos = strConnParam.find(';');
    while (nPos != string::npos)
    {
        string strOneParam = strConnParam.substr(0, nPos);
        strConnParam = strConnParam.substr(nPos + 1); // ��ȥ��һ���Ѿ��������Ĳ���
        nPos = strConnParam.find(';');

        if (strOneParam.empty())
            continue;

        int nPosPart = strOneParam.find('='); // IP:**********
        if (nPosPart == string::npos)
            continue;

        // ��ȡ��ĳ���������ƺ�ֵ
        string strParamName = strOneParam.substr(0, nPosPart);   // e.g. IP
        string strParamValue = strOneParam.substr(nPosPart + 1); // e.g. **********

        if (ACE_OS::strcasecmp("ip", strParamName.c_str()) == 0)
            m_strIpaddr = strParamValue;
        else if (ACE_OS::strcasecmp("port", strParamName.c_str()) == 0)
            m_strPort = strParamValue;
        else if (ACE_OS::strcasecmp("name", strParamName.c_str()) == 0)
            m_strServiceName = strParamValue;
    }
    return DRV_SUCCESS;
}

long ICEDevice::CacheData()
{
    m_DataUnitSeqMap.clear();
    return DRV_SUCCESS;
}
long ICEDevice::ICEConnect()
{
    try
    {
        std::string strIce = m_strServiceName + ":default -h " + m_strIpaddr + " -p " + m_strPort;
        CV_INFO(g_CVLogICEDrv, "ICEDevice connection info: %s", strIce.c_str());
        // Ice::CommunicatorHolder ich(argc, argv);
        int argc = 0;
        char **argv = nullptr;
        Ice::InitializationData initData;
        initData.properties = Ice::createProperties();
        initData.properties->setProperty("Ice.Default.EncodingVersion", "1.0"); // 3.4只支持 1.0；3.7 默认1.1
        m_pCommunicator = Ice::initialize(argc, argv, initData);
        auto base = m_pCommunicator->stringToProxy(strIce);
        m_pDataReceiver = DSF::DataReceiverPrx::checkedCast(base);

        if (!m_pDataReceiver)
        {
            CV_ERROR(g_CVLogICEDrv, -1, "Invalid proxy.");
            return -1;
        }
        m_nConnStatus = true;
        CV_INFO(g_CVLogICEDrv, "Receiver connected.");
        return DRV_SUCCESS;
    }
    ICE_DRV_CATCH
}
long ICEDevice::ICEDisconnect()
{
    if (m_pCommunicator)
    {
        try
        {
            m_pCommunicator->destroy();
        }
        ICE_DRV_CATCH
    }

    return DRV_SUCCESS;
}
long ICEDevice::ICESendData()
{
    if (!m_nConnStatus)
    {
        ICEConnect();
        if (!m_nConnStatus)
        {

            CV_ERROR(g_CVLogICEDrv, -1, "Not connected, cannot send data.");
            return -1;
        }
    }

    auto send_func = [this](DSF::DataUnitSeq &seq) -> long {
        try
        {
            m_pDataReceiver->sendData(seq);
            return 0;
        }
        ICE_DRV_CATCH
    };
    {
        std::lock_guard<std::mutex> lock(m_DataUnitSeqMapMutex);
        auto cur_second = NowMillisecond() / 1000;
        for (auto it = m_DataUnitSeqMap.begin(); it != m_DataUnitSeqMap.end();)
        {
            const auto sec = it->first;
            long lSendRet = send_func(it->second);
            if (cur_second > sec + m_nSendRetrySeconds)
            {
                CV_INFO(g_CVLogICEDrv, "time_out, size=%d, sec=%d, cur_sec=%d", it->second.size(), sec, cur_second);
                it = m_DataUnitSeqMap.erase(it);
            }
            else if (0 == send_func(it->second))
            {
                CV_INFO(g_CVLogICEDrv, "Send data success,size=%d, sec=%d, cur_sec=%d", it->second.size(), sec,
                        cur_second);
                it = m_DataUnitSeqMap.erase(it);
            }

            else
            {
                ++it;
            }
        }
    }

    return DRV_SUCCESS;
}

long ICEDevice::ICECacheData(const DSF::DataUnit &dataUnit)
{
    std::lock_guard<std::mutex> lock(m_DataUnitSeqMapMutex);
    const auto sec = dataUnit.lTime / 1000;
    if (m_DataUnitSeqMap.find(sec) == m_DataUnitSeqMap.end())
    {
        DSF::DataUnitSeq seq;
        m_DataUnitSeqMap[sec] = seq;
    }
    if (m_DataUnitSeqMap[sec].size() >= MAX_COUNT_PER_SECOND) // 每秒最多
    {
        CV_ERROR(g_CVLogICEDrv, -1, "DataUnitSeq size is too large, size=%d", m_DataUnitSeqMap[sec].size());
        return -1;
    }
    m_DataUnitSeqMap[sec].push_back(dataUnit);

    while (m_DataUnitSeqMap.size() > m_nSendRetrySeconds) // 最多缓存几秒。这个比较粗糙，但是保险无节制缓存
    {
        m_DataUnitSeqMap.erase(m_DataUnitSeqMap.begin());
    }

    return DRV_SUCCESS;
}