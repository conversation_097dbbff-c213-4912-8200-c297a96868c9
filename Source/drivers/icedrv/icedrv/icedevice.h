/**
 * Filename        icedevice.h
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description
 *
 * Author          wuz<PERSON>qiang
 * Version         06/18/2025    wuzheqiang    Initial Version
 **************************************************************/
#ifndef _ICE_DEVICE_DRIVER_H_
#define _ICE_DEVICE_DRIVER_H_

#include "cv_datatype.h"
#include "cvdefine.h"
#include "driversdk/cvdrivercommon.h"
#include "keyvalue.h"
#include <Ice/Ice.h>
#include <chrono>
#include <map>
#include <memory>
#include <mutex>
using namespace std;
extern CCVLog g_CVLogICEDrv;
#define ICE_DRV_CATCH                                                                                                  \
    catch (const std::exception &ex)                                                                                   \
    {                                                                                                                  \
        CV_ERROR(g_CVLogICEDrv, -1, "std exception: %s", ex.what());                                                   \
        return 1;                                                                                                      \
    }                                                                                                                  \
    catch (...)                                                                                                        \
    {                                                                                                                  \
        CV_ERROR(g_CVLogICEDrv, -1, "unknown exception");                                                              \
        return 1;                                                                                                      \
    }

constexpr uint32 g_nDefSendRetrySeconds = 30; // 默认推送重试时间
typedef std::map<uint32, DSF::DataUnitSeq> DataUnitSeqMap;

inline uint64 NowMillisecond()
{
    using namespace std::chrono;
    auto now = system_clock::now();

    auto milliseconds_since_epoch = duration_cast<milliseconds>(now.time_since_epoch()).count();

    return static_cast<uint64>(milliseconds_since_epoch);
}
class ICEDevice
{
  public:
    ICEDevice(DRVHANDLE hDevice);
    virtual ~ICEDevice(void);

    long SetConnectParam(const char *szConnParam);
    long CacheData();
    DRVHANDLE GetDrvHandle()
    {
        return m_hDevice;
    }

    // ice interface
    long ICEConnect();
    long ICEDisconnect();
    long ICESendData();
    long ICECacheData(const DSF::DataUnit &dataUnit);

  public:
    bool m_bMultiLink = true; // 是否多连接
    bool m_bStop = false;     // 是否停止
    std::chrono::steady_clock::time_point m_interval_start_time;

  private:
    string m_strIpaddr = "";                             // ip 地址
    string m_strPort = "";                               // 端口号
    string m_strServiceName = "";                        // 服务名
    uint32 m_nSendRetrySeconds = g_nDefSendRetrySeconds; // 推送重试时间

    DRVHANDLE m_hDevice = nullptr;

    Ice::CommunicatorHolder m_pCommunicator;
    DSF::DataReceiverPrx m_pDataReceiver;
    DataUnitSeqMap m_DataUnitSeqMap;
    std::mutex m_DataUnitSeqMapMutex;

    bool m_nConnStatus = false; // 连接状态
};
#endif //_ICE_DEVICE_DRIVER_H_