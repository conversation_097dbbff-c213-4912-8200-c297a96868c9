/**
 * Filename        icedrv.h
 * Copyright       Shanghai Baosight Software Co., Ltd.
 * Description
 *
 * Author          wuz<PERSON><PERSON><PERSON>
 * Version         06/18/2025    wuzheqiang    Initial Version
 **************************************************************/

#ifndef _ICEDRV_DRIVER_H_
#define _ICEDRV_DRIVER_H_

#include "ace/Default_Constants.h"
#include "common/RMAPIDef.h"
#include "common/cvcomm.hxx"
#include "driversdk/cvdrivercommon.h"
#include "errcode/error_code.h"
#include "icedevice.h"
#include "os/os_time.h"
#include <fstream>
#include <math.h>
#include <string>
using namespace std;

typedef struct ICEDataBlock_t
{
    std::string strName = "";       // 需要设置的属性名
    unsigned int nSize = 0;         // value的长度  memcpy(&subData, szValue.Value().data(), nSize);
    int32 nBlockType = 0;           //
    DRVHANDLE hDatablock = nullptr; // 数据块handle
    DRVHANDLE hDevice = nullptr;    // 设备handle

} ICEDataBlock;

class DataReceiverI : public DSF::DataReceiver
{
  public:
    virtual void sendData(const DSF::DataUnitSeq &dataSeq, const Ice::Current &current) override;
};

class ICEServer
{
  public:
    ICEServer()
    {
        Init();
    }
    void Init()
    {
        m_usPort = CCVComm::GetInstance().GetServicePort("icedrv", m_usPort);
    }
    int run()
    {
        try
        {
            int argc = 0;
            const char **argv = nullptr;
            Ice::CommunicatorHolder ich(argc, argv);
            std::string strEndpoints = "default -p " + std::to_string(m_usPort);
            auto adapter = ich->createObjectAdapterWithEndpoints("DataReceiverAdapter", strEndpoints);
            Ice::ObjectPtr receiver = new DataReceiverI;                       // 也是一种智能指针
            adapter->add(receiver, Ice::stringToIdentity("DSF/DataReceiver")); // server name
            adapter->activate();

            ich->waitForShutdown();
            return 0;
        }
        ICE_DRV_CATCH
    }

  private:
    bool m_bRun = false;
    uint16 m_usPort = 55010;
};

#endif //_ICEDRV_DRIVER_H_