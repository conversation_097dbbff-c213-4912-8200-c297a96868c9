// **********************************************************************
//
// Copyright (c) 2003-2018 ZeroC, Inc. All rights reserved.
//
// This copy of Ice is licensed to you under the terms described in the
// ICE_LICENSE file included in this distribution.
//
// **********************************************************************
//
// Ice version 3.7.1
//
// <auto-generated>
//
// Generated from file `keyvalue.ice'
//
// Warning: do not edit this file.
//
// </auto-generated>
//

#include "keyvalue.h"
#include <IceUtil/PushDisableWarnings.h>
#include <Ice/LocalException.h>
#include <Ice/ValueFactory.h>
#include <Ice/OutgoingAsync.h>
#include <Ice/InputStream.h>
#include <Ice/OutputStream.h>
#include <IceUtil/PopDisableWarnings.h>

#if defined(_MSC_VER)
#   pragma warning(disable:4458) // declaration of ... hides class member
#elif defined(__clang__)
#   pragma clang diagnostic ignored "-Wshadow"
#elif defined(__GNUC__)
#   pragma GCC diagnostic ignored "-Wshadow"
#endif

#ifndef ICE_IGNORE_VERSION
#   if ICE_INT_VERSION / 100 != 307
#       error Ice version mismatch!
#   endif
#   if ICE_INT_VERSION % 100 > 50
#       error Beta header file detected
#   endif
#   if ICE_INT_VERSION % 100 < 1
#       error Ice patch level mismatch!
#   endif
#endif

#ifdef ICE_CPP11_MAPPING // C++11 mapping

namespace
{

const ::std::string iceC_DSF_DataReceiver_ids[2] =
{
    "::DSF::DataReceiver",
    "::Ice::Object"
};
const ::std::string iceC_DSF_DataReceiver_ops[] =
{
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "sendData"
};
const ::std::string iceC_DSF_DataReceiver_sendData_name = "sendData";

}

bool
DSF::DataReceiver::ice_isA(::std::string s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_DSF_DataReceiver_ids, iceC_DSF_DataReceiver_ids + 2, s);
}

::std::vector<::std::string>
DSF::DataReceiver::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector<::std::string>(&iceC_DSF_DataReceiver_ids[0], &iceC_DSF_DataReceiver_ids[2]);
}

::std::string
DSF::DataReceiver::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
DSF::DataReceiver::ice_staticId()
{
    static const ::std::string typeId = "::DSF::DataReceiver";
    return typeId;
}

/// \cond INTERNAL
bool
DSF::DataReceiver::_iceD_sendData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::OperationMode::Normal, current.mode);
    auto istr = inS.startReadParams();
    DataUnitSeq iceP_dataSeq;
    istr->readAll(iceP_dataSeq);
    inS.endReadParams();
    this->sendData(::std::move(iceP_dataSeq), current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

/// \cond INTERNAL
bool
DSF::DataReceiver::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_DSF_DataReceiver_ops, iceC_DSF_DataReceiver_ops + 5, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_DSF_DataReceiver_ops)
    {
        case 0:
        {
            return _iceD_ice_id(in, current);
        }
        case 1:
        {
            return _iceD_ice_ids(in, current);
        }
        case 2:
        {
            return _iceD_ice_isA(in, current);
        }
        case 3:
        {
            return _iceD_ice_ping(in, current);
        }
        case 4:
        {
            return _iceD_sendData(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond INTERNAL
void
DSF::DataReceiverPrx::_iceI_sendData(const ::std::shared_ptr<::IceInternal::OutgoingAsyncT<void>>& outAsync, const DataUnitSeq& iceP_dataSeq, const ::Ice::Context& context)
{
    outAsync->invoke(iceC_DSF_DataReceiver_sendData_name, ::Ice::OperationMode::Normal, ::Ice::FormatType::DefaultFormat, context,
        [&](::Ice::OutputStream* ostr)
        {
            ostr->writeAll(iceP_dataSeq);
        },
        nullptr);
}
/// \endcond

/// \cond INTERNAL
::std::shared_ptr<::Ice::ObjectPrx>
DSF::DataReceiverPrx::_newInstance() const
{
    return ::IceInternal::createProxy<DataReceiverPrx>();
}
/// \endcond

const ::std::string&
DSF::DataReceiverPrx::ice_staticId()
{
    return DataReceiver::ice_staticId();
}

namespace Ice
{
}

#else // C++98 mapping

namespace
{

const ::std::string iceC_DSF_DataReceiver_sendData_name = "sendData";

}

/// \cond INTERNAL
::IceProxy::Ice::Object* ::IceProxy::DSF::upCast(DataReceiver* p) { return p; }

void
::IceProxy::DSF::_readProxy(::Ice::InputStream* istr, ::IceInternal::ProxyHandle< DataReceiver>& v)
{
    ::Ice::ObjectPrx proxy;
    istr->read(proxy);
    if(!proxy)
    {
        v = 0;
    }
    else
    {
        v = new DataReceiver;
        v->_copyFrom(proxy);
    }
}
/// \endcond

::Ice::AsyncResultPtr
IceProxy::DSF::DataReceiver::_iceI_begin_sendData(const ::DSF::DataUnitSeq& iceP_dataSeq, const ::Ice::Context& context, const ::IceInternal::CallbackBasePtr& del, const ::Ice::LocalObjectPtr& cookie, bool sync)
{
    ::IceInternal::OutgoingAsyncPtr result = new ::IceInternal::CallbackOutgoing(this, iceC_DSF_DataReceiver_sendData_name, del, cookie, sync);
    try
    {
        result->prepare(iceC_DSF_DataReceiver_sendData_name, ::Ice::Normal, context);
        ::Ice::OutputStream* ostr = result->startWriteParams(::Ice::DefaultFormat);
        ostr->write(iceP_dataSeq);
        result->endWriteParams();
        result->invoke(iceC_DSF_DataReceiver_sendData_name);
    }
    catch(const ::Ice::Exception& ex)
    {
        result->abort(ex);
    }
    return result;
}

void
IceProxy::DSF::DataReceiver::end_sendData(const ::Ice::AsyncResultPtr& result)
{
    _end(result, iceC_DSF_DataReceiver_sendData_name);
}

/// \cond INTERNAL
::IceProxy::Ice::Object*
IceProxy::DSF::DataReceiver::_newInstance() const
{
    return new DataReceiver;
}
/// \endcond

const ::std::string&
IceProxy::DSF::DataReceiver::ice_staticId()
{
    return ::DSF::DataReceiver::ice_staticId();
}

DSF::DataReceiver::~DataReceiver()
{
}

/// \cond INTERNAL
::Ice::Object* DSF::upCast(DataReceiver* p) { return p; }

/// \endcond

namespace
{
const ::std::string iceC_DSF_DataReceiver_ids[2] =
{
    "::DSF::DataReceiver",
    "::Ice::Object"
};

}

bool
DSF::DataReceiver::ice_isA(const ::std::string& s, const ::Ice::Current&) const
{
    return ::std::binary_search(iceC_DSF_DataReceiver_ids, iceC_DSF_DataReceiver_ids + 2, s);
}

::std::vector< ::std::string>
DSF::DataReceiver::ice_ids(const ::Ice::Current&) const
{
    return ::std::vector< ::std::string>(&iceC_DSF_DataReceiver_ids[0], &iceC_DSF_DataReceiver_ids[2]);
}

const ::std::string&
DSF::DataReceiver::ice_id(const ::Ice::Current&) const
{
    return ice_staticId();
}

const ::std::string&
DSF::DataReceiver::ice_staticId()
{
#ifdef ICE_HAS_THREAD_SAFE_LOCAL_STATIC
    static const ::std::string typeId = "::DSF::DataReceiver";
    return typeId;
#else
    return iceC_DSF_DataReceiver_ids[0];
#endif
}

/// \cond INTERNAL
bool
DSF::DataReceiver::_iceD_sendData(::IceInternal::Incoming& inS, const ::Ice::Current& current)
{
    _iceCheckMode(::Ice::Normal, current.mode);
    ::Ice::InputStream* istr = inS.startReadParams();
    DataUnitSeq iceP_dataSeq;
    istr->read(iceP_dataSeq);
    inS.endReadParams();
    this->sendData(iceP_dataSeq, current);
    inS.writeEmptyParams();
    return true;
}
/// \endcond

namespace
{
const ::std::string iceC_DSF_DataReceiver_all[] =
{
    "ice_id",
    "ice_ids",
    "ice_isA",
    "ice_ping",
    "sendData"
};

}

/// \cond INTERNAL
bool
DSF::DataReceiver::_iceDispatch(::IceInternal::Incoming& in, const ::Ice::Current& current)
{
    ::std::pair<const ::std::string*, const ::std::string*> r = ::std::equal_range(iceC_DSF_DataReceiver_all, iceC_DSF_DataReceiver_all + 5, current.operation);
    if(r.first == r.second)
    {
        throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
    }

    switch(r.first - iceC_DSF_DataReceiver_all)
    {
        case 0:
        {
            return _iceD_ice_id(in, current);
        }
        case 1:
        {
            return _iceD_ice_ids(in, current);
        }
        case 2:
        {
            return _iceD_ice_isA(in, current);
        }
        case 3:
        {
            return _iceD_ice_ping(in, current);
        }
        case 4:
        {
            return _iceD_sendData(in, current);
        }
        default:
        {
            assert(false);
            throw ::Ice::OperationNotExistException(__FILE__, __LINE__, current.id, current.facet, current.operation);
        }
    }
}
/// \endcond

/// \cond STREAM
void
DSF::DataReceiver::_iceWriteImpl(::Ice::OutputStream* ostr) const
{
    ostr->startSlice(ice_staticId(), -1, true);
    ::Ice::StreamWriter< DataReceiver, ::Ice::OutputStream>::write(ostr, *this);
    ostr->endSlice();
}

void
DSF::DataReceiver::_iceReadImpl(::Ice::InputStream* istr)
{
    istr->startSlice();
    ::Ice::StreamReader< DataReceiver, ::Ice::InputStream>::read(istr, *this);
    istr->endSlice();
}
/// \endcond

/// \cond INTERNAL
void
DSF::_icePatchObjectPtr(DataReceiverPtr& handle, const ::Ice::ObjectPtr& v)
{
    handle = DataReceiverPtr::dynamicCast(v);
    if(v && !handle)
    {
        IceInternal::Ex::throwUOE(DataReceiver::ice_staticId(), v);
    }
}
/// \endcond

namespace Ice
{
}

#endif
