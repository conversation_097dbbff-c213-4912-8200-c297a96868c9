cmake_minimum_required(VERSION 3.10)
PROJECT(dsfdrvctrl)
add_subdirectory(drvctrl)
add_subdirectory(drvctrlSDK)
# SUBDIRS(cvdriverframe modbus modbusbs drvctrl snmp2 snap7drv IEC104 tdc tdcwritedrv icgdrv rda siemenss7drv cip  midvar s7fwdrv iplaturedrv dbdrv opcua melsec hitachidrv codesysdrv geniitek filetagdrv fileblockdrv bscidriver sgcidriver redisdrv ge abdrv omron mqttdrv)
# IF(${CMAKE_SYSTEM_NAME} MATCHES Windows)
# 	SUBDIRS(opc)
# ENDIF(${CMAKE_SYSTEM_NAME} MATCHES Windows)

# IF(${CMAKE_SYSTEM_NAME} MATCHES Linux)
# 	SUBDIRS(tdriver)
# ENDIF(${CMAKE_SYSTEM_NAME} MATCHES Linux)
