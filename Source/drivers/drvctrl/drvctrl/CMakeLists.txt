cmake_minimum_required(VERSION 2.6)
############FOR_MODIFIY_BEGIN#######################
PROJECT (dsfdrvctrl)
add_subdirectory(pdbcommon)

INCLUDE(${CMAKE_SOURCE_DIR}/CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} DrvController.cpp DrvCtrl.cpp DrvTable.cpp 
MainTasksManager.cpp NetWrapper.cpp ReqFetchTask.cpp DrvCtrlTask.cpp )

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME dsfdrvctrl)
#Setting library type used when build a library
SET(LIB_TYPE STATIC)

SET(LINK_LIBS ACE drlog drlogimpl drcomm tinyxml drnetqueue pdbcommon servicebase intl iconv shmqueue  licverify License  pthread)
IF(UNIX)
	IF(HPUX)
		SET(LINK_LIBS ${LINK_LIBS} pthread)
	ENDIF(HPUX)
	
	IF(CMAKE_SYSTEM MATCHES "SunOS.*")
		SET(LINK_LIBS ${LINK_LIBS} socket)
	ENDIF(CMAKE_SYSTEM MATCHES "SunOS.*")
ENDIF(UNIX)
if( CMAKE_SIZEOF_VOID_P EQUAL 4 )
	IF(CMAKE_SYSTEM MATCHES "Linux")
		SET(LINK_LIBS ${LINK_LIBS} SentinelKeys32)
	ENDIF(CMAKE_SYSTEM MATCHES "Linux")
endif( CMAKE_SIZEOF_VOID_P EQUAL 4 )

############FOR_MODIFIY_END#########################

INCLUDE($ENV{DRDIR}CMakeCommonExec)

IF(MSVC)
	if( CMAKE_SIZEOF_VOID_P EQUAL 8 )
		set_target_properties(${TARGET_NAME} PROPERTIES STATIC_LIBRARY_FLAGS "/machine:x64")
	endif( CMAKE_SIZEOF_VOID_P EQUAL 8 )	
ENDIF(MSVC)
