#ifndef _DRVCTRL_THREAD_BASE_H_
#define _DRVCTRL_THREAD_BASE_H_

#include <ace/Task.h>
#include "common/SimpleQueue.h"
#include "DrvCtrlDef.h"

class CDrvCtrlThreadBase : public ACE_Task_Base
{
public:
	virtual int putq(DrvCtrl_InternalMsg &Msg)
	{
		return m_SimpleThreadQueue.enqueue(Msg);
	}
	virtual int getq(DrvCtrl_InternalMsg &Msg, ACE_Time_Value *tv = 0)
	{
		return m_SimpleThreadQueue.dequeue(Msg, tv);
	}

	bool IsEmpty(){return (m_SimpleThreadQueue.size() == 0);}
protected :
	CSimpleThreadQueue<DrvCtrl_InternalMsg> m_SimpleThreadQueue;
};

#endif//_DRVCTRL_THREAD_BASE_H_
