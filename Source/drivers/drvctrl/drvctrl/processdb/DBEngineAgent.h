// ProcessBlockSchemaReg.h: interface for the CProcessBlockSchemaReg class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(_DBEGINE_AGENT_H_)
#define _DBEGINE_AGENT_H_

#include "processdb/ProcessBlockSchema.h"

//#include <ace/OS_String.h>
//#include "PbAccessorBase.h"
#include "common/CommHelper.h"
#include "processdb/EnumDef.h"
#include "common/OS.h"
#include "ace/Singleton.h"
#include <string>
#include "processdb/DbMdlSchemaDef.h"

using namespace std;

/**
 *  @brief    (register all predefined process block schema to process database). 
 *  (!!!hard coded, use a object factory may be more flexible, but seems unecessary).
 *  (one time initialization).
 *  (this class also create a cache Name2<blkType, blkIndex> map in BDB b+ table, which is used to speed up blk look up).
 */
class CDBEngineAgent
{
UNITTEST(CDBEngineAgent);

friend class ACE_Singleton<CDBEngineAgent, ACE_Thread_Mutex>;

private:
	CDBEngineAgent();
	virtual ~CDBEngineAgent();

public:
	// Initilize
	void	Init(const char* szProjHome, const char *szProjName);
	//(lookup blktype and blkindex by blkname).
	long	BlkName2Loc_Cache(const char *szBlkName, unsigned char &cBlkType, long &nBlkIndex);
	// Release resources
	void	Release();
	// Add Process Block to register.
	long	AddProcessBlock(unsigned char cBlkType, long &nRecNo, void * pBlockData);

	long	DeleteProcessBlock(const char* szBlkName);
	// Setup Process Block BDB File
	long	SetupPredefinedPBSchema(bool bNeedClean = true, int nNumProcessBlock = 10000);
	// Increase Runtime Version
	long	IncreaseRuntimeVersion();
	// Enum All ProcessBlock
	//ProcessBlkList* EnumProcessBlock();

	long		EnumProcessBlock(EnumBlkRec **ppBlkList, long *pnNumBlk);
	void		FreeProcessBlockList( EnumBlkRec **ppBlkList );

	long		UpdateProcessBlock(short nBlkType, long nBlkNumber, long nOffset, long nSize, const char * szDataBuf);
	long		ReadProcessBlock(short nBlkType, long nBlkNumber, long nOffset, long nSize, char **pszDataBuf);
	
	long		CheckVersion(TDbMdlVersion &versionCompare);
	long		ReadVersionFromPDB(TDbMdlVersion &mdlVer);

protected:

	//static SchemaInfo	*m_SchemaArray[PB_TYPE_MAX_NUM];

	string		m_strProjName;
	string		m_strProjHome;

};

#define DB_ENGINE_AGENT (*(ACE_Singleton<CDBEngineAgent, ACE_Thread_Mutex>::instance()))







#endif // !defined(_DBEGINE_AGENT_H_)
