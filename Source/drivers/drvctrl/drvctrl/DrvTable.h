// DrvTable.h: interface for the CDrvTable class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_DRVTABLE_H__0525CAB3_9042_4D6A_8897_C980DA56EB9D__INCLUDED_)
#define AFX_DRVTABLE_H__0525CAB3_9042_4D6A_8897_C980DA56EB9D__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif  // _MSC_VER > 1000

// #include "PicoStorage/PicoStorage.hpp"
#include "DrvCtrllerBase.h"
#include "common/stl_inc.h"
#include "processdb/ProjectSetting.h"
#include "common/HashMap.h"
#include "DrvCtrlDef.h"
#include "ace/Process_Mutex.h"

typedef std::map<string, string> MapDrvCfg;

class CDrvTable {
   public:
    void KillAll();
    void StopAll();
    void StartAll();
    void LaunchAll();
    void RefreshAll();

    long Init(long nArgc, const char* szArgv[]);
    CDrvCtrllerBase* GetDrv(const char* szDrvName);
    long AddDrv(const char* szDrvName, const char* szPath);
    long DelDrv(const char* szDrvName);
    void LaunchAndStartDrv(const char* szDrvName);
    void StopAndKillDrv(const char* szDrvName);
    long RefreshDrv(const char* szDrvName);
    long LoadDriverTable();
    long ReLoadDriverTable();
    void ClearDrvTable();
    void GetAllStatus(DrvCtrlXmlElement& arg);
    void GetDrivesByCfgChange(MapDrvCfg& mapDrvAdd, MapDrvCfg& mapDrvDel,
                              MapDrvCfg& mapUnChanged);
    // 获取所有驱动描述
    void GetAllDesc(DrvCtrlXmlElement &pRoot) const;
    CDrvTable();
    virtual ~CDrvTable();

    CProjectSetting m_setting;

    static void GetDefaultDrvPath(const ACE_TCHAR* szDrvName,
                                  string& strDrvPath);

   private:
    long LoadDrvCfg(std::map<string, string>& mapDrvCfg);
    // 驱动描述缓存
    std::map<std::string, std::string> mapDrvDesc;

   protected:
    Hash_Map<string, CDrvCtrllerBase*> m_mapDrv;
    
    typedef Hash_Map<string, CDrvCtrllerBase*>::_Iterator IterDrv;
    MapDrvCfg m_mapNewDrv;
    ACE_Process_Mutex m_mutexMapDrv;
};

#endif  // !defined(AFX_DRVTABLE_H__0525CAB3_9042_4D6A_8897_C980DA56EB9D__INCLUDED_)
