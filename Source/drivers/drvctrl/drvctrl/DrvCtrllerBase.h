#ifndef _DRV_CTRL_DRV_CTRLLER_BASE_H_
#define _DRV_CTRL_DRV_CTRLLER_BASE_H_

// 3 second wait for driver to exit
#define WAIT_EXIT_ELLAPSE_SECOND	3
#define WAIT_PROCESS_EXIT			5	// 5 sec.

class CDrvCtrllerBase
{
public:
	virtual long		StopDrvSvc() = 0;
	virtual long		StartDrvSvc() = 0;
	virtual long		RefreshDrvSvc() = 0;
	virtual long		KillDrvPollStub() = 0;
	virtual long		LaunchDrvPollStub() = 0;
	virtual ~CDrvCtrllerBase(){};
	virtual int			GetDriverStatus() = 0;
	virtual const char*		GetDriverName() = 0;

};

extern bool g_bExit;

#endif//_DRV_CTRL_DRV_CTRLLER_BASE_H_
