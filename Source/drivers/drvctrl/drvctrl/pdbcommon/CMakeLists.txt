cmake_minimum_required(VERSION 2.6)
###########FOR_MODIFIY_BEGIN#######################
#Setting Project Name
PROJECT (pdbcommon)

INCLUDE(${CMAKE_SOURCE_DIR}/CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} PDBCommHelper.cpp ProjectSetting.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME pdbcommon)
#Setting library type used when build a library
SET(LIB_TYPE STATIC)


############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)
IF(MSVC)
	if( CMAKE_SIZEOF_VOID_P EQUAL 8 )
		set_target_properties(${TARGET_NAME} PROPERTIES STATIC_LIBRARY_FLAGS "/machine:x64")
	endif( CMAKE_SIZEOF_VOID_P EQUAL 8 )
ENDIF(MSVC)
