/*!***********************************************************
 *  @file        ProcessBlockSchemaReg.cpp
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  @brief       (register all predefined process block schema to process database).
 *
 *  @author:     chenshengyu
 *  @version     07/11/2007  chenshengyu  Initial Version
 *  @version	 08/30/2007  chenshengyu  (Add: block name cache logic to speedup var2loc loopup in LRDA).
**************************************************************/

#include <ace/Assert.h>
#include "../processdb/PDBDef.h"
#include "../processdb/DBEngineAgent.h"

#include "../processdb/ControlDef.h"
#include "../processdb/PdbEngine.h"
#include "errcode/error_code.h"
#include "processdb/ProcessBlockSchemaReg.h"



CDBEngineAgent::CDBEngineAgent()
{

}

CDBEngineAgent::~CDBEngineAgent()
{
}

/**
 *  (intitalize all predefined process block schema to process db).
 *
 *  @return (void).
 *
 *  @version  07/12/2007  chenshengyu  Initial Version.
 */
long CDBEngineAgent::SetupPredefinedPBSchema( bool bNeedClean /*= true*/, int nNumProcessBlock )
{
	long nErr = DBE_InitialPDBEngine(m_strProjHome.c_str(), m_strProjName.c_str(), nNumProcessBlock);
	
	CHECK_ERROR_AND_RETURN_FAIL(nErr);

	// check the model table is opened already
	// 1. create predefined pb-schema
	DBE_InitializeSchemaTables();

	for (long nTbl = 0; nTbl < PB_TYPE_MAX_NUM; nTbl++)
	{
		if (PB_SCHEMA_REG.m_SchemaArray[nTbl])
		{
			DBE_AddProcessBlockTable((unsigned char)nTbl, PB_SCHEMA_REG.m_SchemaArray[nTbl]->nRecLen, PB_SCHEMA_REG.m_SchemaArray[nTbl]->nPageSize, 
				PB_SCHEMA_REG.m_SchemaArray[nTbl]->szTableName, PB_SCHEMA_REG.m_SchemaArray[nTbl]->nNumPerSegment);
		}
	}

	// 2. create blk-name cache table
	nErr  = DBE_CreateBlkNameCacheTable();
	CHECK_ERROR_AND_RETURN_FAIL(nErr);

	if (!bNeedClean)
	{
		return ICV_SUCCESS;
	}

	// *  @version  10/15/2007  chenshengyu  $(can't determine a table is open already yet).
	// just do a clean restart
	//CHECK_ERROR_AND_RETURN_FAIL(RemoveProcessBlockAll());
	return DBE_RemoveProcessBlockAll();
}

long CDBEngineAgent::IncreaseRuntimeVersion()
{
	return DBE_IncreaseRuntimeVersion();
}


void CDBEngineAgent::Release()
{

	DBE_Shutdown();
}

/**
 *  (lookup blktype and blkindex by blkname).
 *
 *  @param  -[in]  const char*  szBlkName: [block name]
 *  @param  -[out]  short&  sBlkType: [only valid when return value is ICV_SUCCESS]
 *  @param  -[out]  long&  nBlkIndex: [block index]
 *  @return (ICV_SUCCESS if found).
 *
 *  @version  08/31/2007  chenshengyu  Initial Version.
 */
long CDBEngineAgent::BlkName2Loc_Cache(const char *szBlkName,
											   unsigned char &sBlkType, long &nBlkIndex)
{
	return DBE_BlkName2Loc_Cache(szBlkName, &sBlkType, &nBlkIndex);
}

/**
 *  Initialize.
 *
 *
 *  @version     07/01/2008  chenzhiquan  Initial Version.
 */
void CDBEngineAgent::Init(const char* szProjHome, const char *szProjName)
{
	m_strProjName = szProjName;
	m_strProjHome = szProjHome;
}

/**
 *  Enum Process Blocks.
 *
 *
 *  @version     07/07/2008  chenzhiquan  Initial Version.
 */
long CDBEngineAgent::EnumProcessBlock(EnumBlkRec **ppBlkList, long *pnNumBlk)
{
	return DBE_EnumProcessBlock(ppBlkList, pnNumBlk);
}

long CDBEngineAgent::AddProcessBlock( unsigned char cBlkType, long &nRecNo, void * pBlockData )
{
	return DBE_AddProcessBlock(cBlkType, &nRecNo, pBlockData, PB_SCHEMA_REG.m_SchemaArray[cBlkType]->nRecLen);
}



long CDBEngineAgent::DeleteProcessBlock( const char* szBlkName )
{
	return DBE_RemoveProcessBlock(szBlkName);
}



long CDBEngineAgent::UpdateProcessBlock( short nBlkType, long nBlkNumber, long nOffset, long nSize, const char * szDataBuf )
{
	return DBE_UpdateProcessBlock(nBlkType, nBlkNumber, nOffset, nSize, szDataBuf);
}

long CDBEngineAgent::ReadProcessBlock( short nBlkType, long nBlkNumber, long nOffset, long nSize, char **pszDataBuf )
{
	return DBE_ReadProcessBlock(nBlkType, nBlkNumber, nOffset, nSize, pszDataBuf);
}

long CDBEngineAgent::CheckVersion( TDbMdlVersion &versionCompare )
{
	return DBE_CheckVersion(&versionCompare);
}

long CDBEngineAgent::ReadVersionFromPDB( TDbMdlVersion &mdlVer )
{
	return DBE_ReadVersionFromPDB(&mdlVer);
}

void CDBEngineAgent::FreeProcessBlockList( EnumBlkRec **ppBlkList )
{
	DBE_FreeProcessBlockList(ppBlkList);
}
