#ifndef POLL_STUB_H
#define POLL_STUB_H

#include <processdb/DrvctrlComm.h>

// @doc
// 
// @module	PollStub.h - PollStub header file |
//

//
// Copyright (c)1996, 1997 Intellution, Inc., All Rights Reserved
//
// This software contains information which represents trade secrets of
// Intellution and may not be copied  or disclosed to others except as
// provided in the license with Intellution.
//
// Maintenance:
//	
// Version	Date		Who		What
// -------	----------	------	-------------------------------
// 7.0		02/06/1997	rch		Created
//
//

// Various states to control starting and stopping of driver from iocntrl
#define INITIAL_STATE           DRVCTRL_DRV_STATUS_INITIAL
#define START_STATE             DRVCTRL_DRV_STATUS_START
#define STOP_STATE              DRVCTRL_DRV_STATUS_STOP
#define STOP_AND_EXIT_STATE     DRVCTRL_DRV_STATUS_STOP_AND_EXIT
#define START_FAIL_STATE		DRVCTRL_DRV_STATUS_START_FAIL
#define DRIVER_UNEXIST			DRVCTRL_DRV_STATUS_DRIVER_UNEXIST

#pragma pack (1)

// Shared memory between iocntrl and driver
typedef struct driver_to_ioc_struct
{
	short	SleepTime;
	short	ReqDriverState;	// This requests a new driver state phw082995
	short	DriverState;	// This is the actual driver state
} DRV_TO_IOC_SHARE;

#pragma pack ()

#endif
