/**
 * @file DRDrvctrlApi.h
 * @brief 
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2024-12-30
 * 
 * @copyright Copyright (c) 2024  by  宝信
 * 
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2024-12-30     <td>1.0     <td>wwk   <td>修改?
 * </table>
 */
#ifndef _DRDEPLOY_API_H_
#define _DRDEPLOY_API_H_

#include <vector>
#include <string>
#include <map>

#ifdef _WIN32
    #ifdef DRDEPLOY_EXPORTS
        #define DR_API extern "C" __declspec(dllexport)
    #else
        #define DR_API extern "C" __declspec(dllimport)
    #endif  // End of #ifdef DRDEPLOY_EXPORTS
#else
    #define DR_API extern "C"
#endif // End of #ifdef _WIN32


/**
 * @brief 驱动状态
 */
typedef enum
{
    DRV_UNKNOW = -1,  //未知状态/错误
    DRV_INITIAL_STATE,    //驱动加载状态       
    DRV_START_STATE,      //驱动启动状态
    DRV_STOP_STATE,       //停止状态
    DRV_STOP_AND_EXIT_STATE,  //停止退出状态   
    DRV_START_FAIL_STATE,	//启动失败状态	
    DRV_DRIVER_UNEXIST,		//驱动不存在	
}DRV_STATUS_E;

/**
 * @brief 驱动某个驱动
 * @param  drvName          驱动的名称
 * @param  IPAddr           ip
 * @param  port             port
 * @param  timeout          超时等待时间
 * @return DR_API 
 */
DR_API int StartDrv(const std::string  &drvName, const char *IPAddr = "127.0.0.1", unsigned short port = 55007, int timeout = 3000);


/**
 * @brief 停止某个驱动
 * @param  drvName          驱动的名称
 * @param  IPAddr           ip
 * @param  port             port
 * @param  timeout          超时等待时间
 * @return DR_API 
 */
DR_API int StopDrv(const std::string  &drvName, const char *IPAddr = "127.0.0.1", unsigned short port = 55007, int timeout = 3000);



/**
 * @brief Get the Status Drv object
 * @param  drvName          驱动的名称
 * @param  IPAddr           ip
 * @param  port             port
 * @param  timeout          超时等待时间
 * @return DR_API 
 */
DR_API DRV_STATUS_E GetStatusDrv(const std::string  &drvName, const char *IPAddr = "127.0.0.1", unsigned short port = 55007, int timeout = 3000);

/**
 * @brief Get the Status Drv All object
 * @param  DrvsInfoMap      获取所有驱动的状态
 * @param  drvName          驱动的名称
 * @param  IPAddr           ip
 * @param  port             port
 * @param  timeout          超时等待时间
 * @return DR_API 
 */
DR_API bool GetStatusDrvAll(std::map<std::string, DRV_STATUS_E> &DrvsInfoMap, const char *IPAddr = "127.0.0.1", unsigned short port = 55007, int timeout = 3000);

/**
 * @brief Get the description Drv All object
 * @param  DrvsDescMap      获取所有驱动的描述
 * @param  IPAddr           ip
 * @param  port             port
 * @param  timeout          超时等待时间
 * @return DR_API 
 */
DR_API bool GetDescriptionDrvAll(std::map<std::string, std::string> &DrvsDescMap, const char *IPAddr = "127.0.0.1", unsigned short port = 55007, int timeout = 3000);
#endif

