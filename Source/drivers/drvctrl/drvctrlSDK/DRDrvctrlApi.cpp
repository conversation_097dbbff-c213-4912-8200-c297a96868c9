/**
 * @file DRDrvctrlApi.cpp
 * @brief 
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2024-12-30
 * 
 * @copyright Copyright (c) 2024  by  ����
 * 
 * @par �޸���־:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2024-12-30     <td>1.0     <td>wwk   <td>�޸�?
 * </table>
 */
#include "DRDrvctrlApi.h"
#include <mutex>
#include "common/NetQueue.h"
#include "errcode/error_code.h"
#include <iostream>
#include <tinyxml2.h>
#include <unistd.h>

#define DSF_DRDRV_DEFAULT_NODEQUEUEID 101

std::mutex g_mutex;

static void init() __attribute__((constructor));
static void uninit() __attribute__((destructor));

static void init()
{
    long lRet = CVNDK_Init(SYNC_SEND);
}

static void uninit()
{
    long lRet = CVNDK_Finalize();
}

int StartDrv(const std::string  &drvName, const char *IPAddr, unsigned short port, int timeout)
{
    std::lock_guard<std::mutex> guard(g_mutex);

    HQUEUE hRemoteQueue = CVNDK_RegRemoteQueue(DSF_DRDRV_DEFAULT_NODEQUEUEID, IPAddr, port);
    if (NULL == hRemoteQueue)
        return EC_DSF_DP_API_PARSE_IPANDPORT;

    long lRet = CVNDK_Connect(hRemoteQueue);
    if (lRet != ICV_SUCCESS)
    {
        return EC_DSF_DP_API_CONNECTION;
    }

    HQUEUE hLocalQue = CVNDK_RegLocalQueue(1);
    if (!hLocalQue)
    {
        return EC_DSF_DP_API_SEND_DATA;
    }

    HQUEUE hCliQue = 0;
    char *pszResponse = NULL;
    long lRcvLen = 0;

    tinyxml2::XMLDocument doc;
	tinyxml2::XMLElement* pRoot = doc.NewElement("DrvCtrl");
    doc.InsertFirstChild(pRoot);  
	pRoot->SetAttribute("objtype", "driver");
    pRoot->SetAttribute("operate", "start");
    pRoot->SetAttribute("option", "");
    pRoot->SetAttribute("driver_name", drvName.c_str());
    
    tinyxml2::XMLPrinter printer;
    doc.Print(&printer);
    const char* xmlString = printer.CStr();

    lRet = CVNDK_Send(hRemoteQueue, hLocalQue, xmlString, strlen(xmlString));
    if (lRet != ICV_SUCCESS)
    {
        return EC_DSF_DP_API_SEND_DATA;
    }
    // std::cout << __FUNCTION__  << ":: " << "CVNDK_Send succ"<< std::endl;

    lRet = CVNDK_Recv(hLocalQue, &hCliQue, (void **)&pszResponse, lRcvLen, timeout);
    if (lRet != ICV_SUCCESS)
    {
        CVNDK_Free((void *&)pszResponse);
        return EC_DSF_DP_API_GET_STATUS;
    }
    
    tinyxml2::XMLDocument responseDoc;
    tinyxml2::XMLError eResult = responseDoc.Parse(pszResponse);
    if (eResult != tinyxml2::XML_SUCCESS)
    {
        std::cerr << "Failed to parse XML response: " << eResult << std::endl;
        CVNDK_Free((void *&)pszResponse);
        return EC_DSF_DP_PARAS_XMLPARAS_XML;
    }

    tinyxml2::XMLElement* pRootParse = responseDoc.RootElement();
    if (pRootParse == nullptr)
    {
        std::cerr << "No root element in XML response." << std::endl;
        CVNDK_Free((void *&)pszResponse);
        return EC_DSF_DP_PARAS_XMLPARAS_XML;
    }

    // ������������Ի���Ԫ��
    const char* result = pRootParse->Attribute("result");
    if (result != nullptr)
    {
        std::cout << __FUNCTION__  << ":: " << "CVNDK_Recv Result: " << result << std::endl;
    }
    else
    {
        std::cerr << __FUNCTION__  << ":: " << "CVNDK_Recv No result attribute in XML response." << std::endl;
    }
    lRet = CVNDK_DisConnect(hRemoteQueue);

    CVNDK_Free((void *&)pszResponse);
    CVNDK_ReleaseQueue(hRemoteQueue);
    CVNDK_ReleaseQueue(hLocalQue);
    return 0;
}


int StopDrv(const std::string  &drvName, const char *IPAddr, unsigned short port, int timeout)
{
    std::lock_guard<std::mutex> guard(g_mutex);

    HQUEUE hRemoteQueue = CVNDK_RegRemoteQueue(DSF_DRDRV_DEFAULT_NODEQUEUEID, IPAddr, port);
    if (NULL == hRemoteQueue)
        return EC_DSF_DP_API_PARSE_IPANDPORT;

    long lRet = CVNDK_Connect(hRemoteQueue);
    if (lRet != ICV_SUCCESS)
    {
        return EC_DSF_DP_API_CONNECTION;
    }

    HQUEUE hLocalQue = CVNDK_RegLocalQueue(1);
    if (!hLocalQue)
    {
        return EC_DSF_DP_API_SEND_DATA;
    }

    HQUEUE hCliQue = 0;
    char *pszResponse = NULL;
    long lRcvLen = 0;

    tinyxml2::XMLDocument doc;
	tinyxml2::XMLElement* pRoot = doc.NewElement("DrvCtrl");
    doc.InsertFirstChild(pRoot);  
	pRoot->SetAttribute("objtype", "driver");
    pRoot->SetAttribute("operate", "stop");
    pRoot->SetAttribute("option", "");
    pRoot->SetAttribute("driver_name", drvName.c_str());
    
    tinyxml2::XMLPrinter printer;
    doc.Print(&printer);
    const char* xmlString = printer.CStr();

    lRet = CVNDK_Send(hRemoteQueue, hLocalQue, xmlString, strlen(xmlString));
    if (lRet != ICV_SUCCESS)
    {
        return EC_DSF_DP_API_SEND_DATA;
    }
    // std::cout << __FUNCTION__  << ":: " << "CVNDK_Send succ"<< std::endl;

    lRet = CVNDK_Recv(hLocalQue, &hCliQue, (void **)&pszResponse, lRcvLen, timeout);
    if (lRet != ICV_SUCCESS)
    {
        CVNDK_Free((void *&)pszResponse);
        return EC_DSF_DP_API_GET_STATUS;
    }
    
    tinyxml2::XMLDocument responseDoc;
    tinyxml2::XMLError eResult = responseDoc.Parse(pszResponse);
    if (eResult != tinyxml2::XML_SUCCESS)
    {
        std::cerr << "Failed to parse XML response: " << eResult << std::endl;
        CVNDK_Free((void *&)pszResponse);
        return EC_DSF_DP_PARAS_XMLPARAS_XML;
    }

    tinyxml2::XMLElement* pRootParse = responseDoc.RootElement();
    if (pRootParse == nullptr)
    {
        std::cerr << "No root element in XML response." << std::endl;
        CVNDK_Free((void *&)pszResponse);
        return EC_DSF_DP_PARAS_XMLPARAS_XML;
    }

    // ������������Ի���Ԫ��
    const char* result = pRootParse->Attribute("result");
    if (result != nullptr)
    {
        std::cout << __FUNCTION__  << ":: " << "CVNDK_Recv Result: " << result << std::endl;
    }
    else
    {
        std::cerr << __FUNCTION__  << ":: " << "CVNDK_Recv No result attribute in XML response." << std::endl;
    }
    lRet = CVNDK_DisConnect(hRemoteQueue);

    CVNDK_Free((void *&)pszResponse);
    CVNDK_ReleaseQueue(hRemoteQueue);
    CVNDK_ReleaseQueue(hLocalQue);
    return 0;
}




DRV_STATUS_E GetStatusDrv(const std::string  &drvName, const char *IPAddr, unsigned short port, int timeout)
{
    std::lock_guard<std::mutex> guard(g_mutex);

    HQUEUE hRemoteQueue = CVNDK_RegRemoteQueue(DSF_DRDRV_DEFAULT_NODEQUEUEID, IPAddr, port);
    if (NULL == hRemoteQueue)
        return DRV_UNKNOW;

    long lRet = CVNDK_Connect(hRemoteQueue);
    if (lRet != ICV_SUCCESS)
    {
        return DRV_UNKNOW;
    }

    HQUEUE hLocalQue = CVNDK_RegLocalQueue(1);
    if (!hLocalQue)
    {
        return DRV_UNKNOW;
    }

    HQUEUE hCliQue = 0;
    char *pszResponse = NULL;
    long lRcvLen = 0;

    tinyxml2::XMLDocument doc;
	tinyxml2::XMLElement* pRoot = doc.NewElement("DrvCtrl");
    doc.InsertFirstChild(pRoot);  
	pRoot->SetAttribute("objtype", "driver");
    pRoot->SetAttribute("operate", "query");
    pRoot->SetAttribute("option", "mono");
    pRoot->SetAttribute("driver_name", drvName.c_str());
    
    tinyxml2::XMLPrinter printer;
    doc.Print(&printer);
    const char* xmlString = printer.CStr();

    lRet = CVNDK_Send(hRemoteQueue, hLocalQue, xmlString, strlen(xmlString));
    if (lRet != ICV_SUCCESS)
    {
        return DRV_UNKNOW;
    }
    // std::cout << __FUNCTION__  << ":: " << "CVNDK_Send succ"<< std::endl;

    lRet = CVNDK_Recv(hLocalQue, &hCliQue, (void **)&pszResponse, lRcvLen, timeout);
    if (lRet != ICV_SUCCESS)
    {
        CVNDK_Free((void *&)pszResponse);
        return DRV_UNKNOW;
    }
    
    tinyxml2::XMLDocument responseDoc;
    tinyxml2::XMLError eResult = responseDoc.Parse(pszResponse);
    if (eResult != tinyxml2::XML_SUCCESS)
    {
        std::cerr << "Failed to parse XML response: " << eResult << std::endl;
        CVNDK_Free((void *&)pszResponse);
        return DRV_UNKNOW;
    }

    tinyxml2::XMLElement* pRootParse = responseDoc.RootElement();
    if (pRootParse == nullptr)
    {
        std::cerr << "No root element in XML response." << std::endl;
        CVNDK_Free((void *&)pszResponse);
        return DRV_UNKNOW;
    }

    tinyxml2::XMLElement* pRecord = pRootParse->FirstChildElement("Record");
    if (pRecord == nullptr)
    {
        std::cerr << "No RESPONSE_RECORD element in XML response." << std::endl;
        CVNDK_Free((void *&)pszResponse);
        return DRV_UNKNOW;
    }

    // ������������Ի���Ԫ��
    const char* name = pRecord->Attribute("driver_name");
    DRV_STATUS_E eStatus = DRV_UNKNOW;
    eResult = pRecord->QueryIntAttribute("status", (int *)&eStatus);
    if (name != nullptr && eResult == tinyxml2::XML_SUCCESS)
    {
        // std::cout << __FUNCTION__  << ":: " << "CVNDK_Recv drvName: " <<  name  << "  status : " <<  eStatus<< std::endl;
    }
    else
    {
        std::cerr << __FUNCTION__  << ":: " << "CVNDK_Recv No result attribute in XML response. name �� " << name  <<  "eResult " << eResult << std::endl;
    }
    lRet = CVNDK_DisConnect(hRemoteQueue);

    CVNDK_Free((void *&)pszResponse);
    CVNDK_ReleaseQueue(hRemoteQueue);
    CVNDK_ReleaseQueue(hLocalQue);
    return eStatus;
}

bool GetDescriptionDrvAll(std::map<std::string, std::string> &DrvsDescMap, const char *IPAddr, unsigned short port, int timeout)
{
    std::lock_guard<std::mutex> guard(g_mutex);

    HQUEUE hRemoteQueue = CVNDK_RegRemoteQueue(DSF_DRDRV_DEFAULT_NODEQUEUEID, IPAddr, port);
    if (NULL == hRemoteQueue)
        return false;

    long lRet = CVNDK_Connect(hRemoteQueue);
    if (lRet != ICV_SUCCESS)
    {
        return false;
    }

    HQUEUE hLocalQue = CVNDK_RegLocalQueue(1);
    if (!hLocalQue)
    {
        return false;
    }

    HQUEUE hCliQue = 0;
    char *pszResponse = NULL;
    long lRcvLen = 0;

    tinyxml2::XMLDocument doc;
    tinyxml2::XMLElement* pRoot = doc.NewElement("DrvCtrl");
    doc.InsertFirstChild(pRoot);  
    pRoot->SetAttribute("objtype", "driver");
    pRoot->SetAttribute("operate", "query");
    pRoot->SetAttribute("option", "alldesc");
    
    tinyxml2::XMLPrinter printer;
    doc.Print(&printer);
    const char* xmlString = printer.CStr();
 
    lRet = CVNDK_Send(hRemoteQueue, hLocalQue, xmlString, strlen(xmlString));
    if (lRet != ICV_SUCCESS)
    {
        return false;
    }

    lRet = CVNDK_Recv(hLocalQue, &hCliQue, (void **)&pszResponse, lRcvLen, timeout);
    if (lRet != ICV_SUCCESS)
    {
        CVNDK_Free((void *&)pszResponse);
        return false;
    }
    
    tinyxml2::XMLDocument responseDoc;
    tinyxml2::XMLError eResult = responseDoc.Parse(pszResponse);
    if (eResult != tinyxml2::XML_SUCCESS)
    {
        std::cerr << "Failed to parse XML response: " << eResult << std::endl;
        CVNDK_Free((void *&)pszResponse);
        return false;
    }

    tinyxml2::XMLElement* pRootParse = responseDoc.RootElement();
    if (pRootParse == nullptr)
    {
        std::cerr << "No root element in XML response." << std::endl;
        CVNDK_Free((void *&)pszResponse);
        return false;
    }

    tinyxml2::XMLElement* pRecord = pRootParse->FirstChildElement("Record");
    while (pRecord != nullptr)
    {
        const char* name = pRecord->Attribute("driver_name");
        const char* desc = pRecord->Attribute("description");
        
        if (name != nullptr)
        {
            DrvsDescMap[name] = (desc != nullptr) ? desc : "";
        }
        
        pRecord = pRecord->NextSiblingElement("Record");
    }
    
    lRet = CVNDK_DisConnect(hRemoteQueue);
    CVNDK_Free((void *&)pszResponse);
    CVNDK_ReleaseQueue(hRemoteQueue);
    CVNDK_ReleaseQueue(hLocalQue);
    return true;
}

bool GetStatusDrvAll(std::map<std::string, DRV_STATUS_E> &DrvsInfoMap, const char *IPAddr, unsigned short port, int timeout)
{
    std::lock_guard<std::mutex> guard(g_mutex);

    HQUEUE hRemoteQueue = CVNDK_RegRemoteQueue(DSF_DRDRV_DEFAULT_NODEQUEUEID, IPAddr, port);
    if (NULL == hRemoteQueue)
        return false;

    long lRet = CVNDK_Connect(hRemoteQueue);
    if (lRet != ICV_SUCCESS)
    {
        return false;
    }

    HQUEUE hLocalQue = CVNDK_RegLocalQueue(1);
    if (!hLocalQue)
    {
        return false;
    }

    HQUEUE hCliQue = 0;
    char *pszResponse = NULL;
    long lRcvLen = 0;

    tinyxml2::XMLDocument doc;
	tinyxml2::XMLElement* pRoot = doc.NewElement("DrvCtrl");
    doc.InsertFirstChild(pRoot);  
	pRoot->SetAttribute("objtype", "driver");
    pRoot->SetAttribute("operate", "query");
    pRoot->SetAttribute("option", "all");
    
    tinyxml2::XMLPrinter printer;
    doc.Print(&printer);
    const char* xmlString = printer.CStr();

    lRet = CVNDK_Send(hRemoteQueue, hLocalQue, xmlString, strlen(xmlString));
    if (lRet != ICV_SUCCESS)
    {
        return false;
    }
    // std::cout << __FUNCTION__  << ":: " << "CVNDK_Send succ"<< std::endl;

    lRet = CVNDK_Recv(hLocalQue, &hCliQue, (void **)&pszResponse, lRcvLen, timeout);
    if (lRet != ICV_SUCCESS)
    {
        CVNDK_Free((void *&)pszResponse);
        return false;
    }
    
    tinyxml2::XMLDocument responseDoc;
    tinyxml2::XMLError eResult = responseDoc.Parse(pszResponse);
    if (eResult != tinyxml2::XML_SUCCESS)
    {
        std::cerr << "Failed to parse XML response: " << eResult << std::endl;
        CVNDK_Free((void *&)pszResponse);
        return false;
    }

    tinyxml2::XMLElement* pRootParse = responseDoc.RootElement();
    if (pRootParse == nullptr)
    {
        std::cerr << "No root element in XML response." << std::endl;
        CVNDK_Free((void *&)pszResponse);
        return false;
    }

    DRV_STATUS_E eStatus = DRV_UNKNOW;
    tinyxml2::XMLElement* pRecord = pRootParse->FirstChildElement("Record");
    while (pRecord != nullptr)
    {
        const char* name = pRecord->Attribute("driver_name");
        eResult = pRecord->QueryIntAttribute("status", (int *)&eStatus);
        if (name != nullptr && eResult == tinyxml2::XML_SUCCESS)
        {
            // std::cout << __FUNCTION__  << ":: " << "CVNDK_Recv drvName: " <<  name  << "  status : " <<  eStatus<< std::endl;
        }
        else
        {
            std::cerr << __FUNCTION__  << ":: " << "CVNDK_Recv No result attribute in XML response. name �� " << name  <<  "eResult " << eResult << std::endl;
        }
        DrvsInfoMap[name] = eStatus; 
        pRecord = pRecord->NextSiblingElement("Record");
    }
    lRet = CVNDK_DisConnect(hRemoteQueue);

    CVNDK_Free((void *&)pszResponse);
    CVNDK_ReleaseQueue(hRemoteQueue);
    CVNDK_ReleaseQueue(hLocalQue);
    return true;
}


int main(int argc, char *argv[])
{
    std::cout << "++++: " << argv[0] << std::endl;
    // int iret = StartDrv("dsftxdrv");
    // if (iret != 0)
    // {
    //     //输出错误iret
    //     std::cerr << "Failed to start driver. Error code: " << iret << std::endl;
    // }
    // DRV_STATUS_E eStat =  GetStatusDrv("dsftxdrv");  
    // std::cout << "eStat :" << eStat <<  std::endl;
    // StopDrv("drdriver");
    // sleep(1);
    // eStat =  GetStatusDrv("drdr");  
    // std::cout << "eStat :" << eStat <<  std::endl;
    // std::map<std::string, DRV_STATUS_E> DrvsInfoMap;
    // auto start = std::chrono::high_resolution_clock::now();
    // bool res = GetStatusDrvAll(DrvsInfoMap);
    // auto end = std::chrono::high_resolution_clock::now();
    // std::chrono::duration<double, std::milli> elapsed = end - start;
    // int iRet = res ? 1 : -1;
    // std::cout << "Elapsed time: " << elapsed.count() << " ms" << "res " << iRet <<  std::endl;
    // for(auto &item : DrvsInfoMap)
    // {
    //     std::cout << "drvName: " << item.first << "  status: " << item.second << std::endl;
    // }
    // 获取所有驱动的描述
    std::map<std::string, std::string> allDescs;
    if (GetDescriptionDrvAll(allDescs))
    {
        for (const auto &item : allDescs)
        {
            std::cout << "驱动: " << item.first << ", 描述: " << item.second << std::endl;
        }
    }

    return 0;
}
