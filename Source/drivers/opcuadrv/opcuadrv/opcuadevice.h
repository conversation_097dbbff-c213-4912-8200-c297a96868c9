/************************************************************************
*	Filename:		opcuadevice.h
*	Copyright:		Shanghai Baosight Company Software Co., Ltd.
*
*	Description:	opcua�豸��,������Ҫʵ���������ݣ�����opc�豸���Ͽ�opc�豸��ͬ����д���ݣ��첽��д����
*
*	@author:		zhangqiang
*	@version		2016-01-20	zhangqiang	Initial Version
*************************************************************************/
#ifndef OPCUADEVICE_H
#define OPCUADEVICE_H

#include <string>
#include <map>
#include <set>
#include "driversdk/cvdrivercommon.h"
#include "opcua.h"
#include "opcua/open62541.h"
#include <ace/Guard_T.h>

using namespace std;

int flatindex(int arrayDimSize, unsigned int* arrayDims, int arrayLength, vector<int> indices);

//#define StringType "String"
//#define NumericType "Numeric"
//#define GuidType "Guid"
//#define OpaqueType "Opaque"
//opcua�ڲ�������
enum
{
	EC_OPCUA_DEVICE_NOEXIST = 18001,	// �Ҳ����豸
	EC_OPCUA_DATABLOCK_NOEXIST,		// �Ҳ������ݿ�
	EC_OPCUA_DATATYPE_NOEXIST,		// �����ڵ���������
	EC_OPCUA_DISCONNECT,				// ���ӶϿ�
	EC_OPCUA_CONNECTIONISNULL,			//δ����
};
//OPCUADATABLOCK�������ļ��еĵ�ַ��ʽΪNS3|string|AirConditioner_1.Humidity
struct OPCUAADATABLOCK
{
	int nNumber;//�����ռ�(NS)�������
	int nType;//String or Numeric,�����String��������Ϊ0�����ΪNumeric��������Ϊ1
	DRVHANDLE hDataBlock;//���ݿ���
};

struct OPCUAVALUE
{
	int type;
	void* data;
};
//���Ӳ���

typedef struct AddrIndex {
    int idx;            // datablock index
    string origname;
    DRVHANDLE hDataBlock;
    vector<int> arrIndices;
} AddrIndex;

typedef struct NodeIdInfo {
    int ns = 0;     // namespace
    int numAddr = 0;    // numeric address
    string strAddr;    // string address
    bool operator==(const struct NodeIdInfo &rhs) const {
        return ns == rhs.ns && strAddr == rhs.strAddr && numAddr == numAddr;
    }
    bool operator<(const struct NodeIdInfo &rhs) const {
        return ns < rhs.ns || strAddr < rhs.strAddr || numAddr < rhs.numAddr;
    }
} NodeIdInfo;

typedef map<NodeIdInfo, vector<AddrIndex> > AddrIndexMap;

//��Ҫ��ʵ���࣬�̳е���������Ҫʵ���첽��дʱ�Ļص�����
class COPCUADevice
{
private:
	//void *m_pMainClient;		//���豸������
	//void *m_pBackClient;		//���豸������
	//void *m_pActiveClient;		//��ǰ�����
	//UA_Client* m_pClient;
	UA_Client* m_pClientMain;
	UA_Client* m_pClientBack;
	UA_Client* m_pActiveClient;

	string m_mainUrl;				//���豸������URL
	string m_backUrl;				//���豸������URL
	
	int m_nRMStatus;				//����״̬
	DRVHANDLE m_hDevice;		// �豸���

	bool m_bFlagComplete;	//�˱�������Ƿ�������ݿ����
    bool m_bFlagAsync; // true is async, false is sync
    int m_nAsyncTimeout;
	
	map<string, OPCUAADATABLOCK> m_strAddrMap;
	map<int, OPCUAADATABLOCK> m_numAddrMap;
	void* m_pStrReadQuest;
	void* m_pNumReadQuest;
	//void* m_pReadType;
	//unsigned char (*m_pStrReadValue)[32];
	unsigned char (*m_pStrReadValue)[512];
	unsigned char (*m_pNumReadValue)[512];

	bool *m_pStrReadHasValue;
	bool *m_pNumReadHasValue;
	bool m_bSingle;
    map<int, vector<AddrIndex> > m_strRequestToAddrIndexMap;
    map<int, vector<AddrIndex> > m_numRequestToAddrIndexMap;

    string m_username;
    string m_password;
public:
    AddrIndexMap m_strAsyncAddrIndexMap;
    AddrIndexMap m_numAsyncAddrIndexMap;

    vector<AddrIndexMap> m_strmapVec;
    vector<AddrIndexMap> m_nummapVec;
    map<UA_UInt32, AddrIndexMap> m_strSubidMap;
    map<UA_UInt32, AddrIndexMap> m_numSubidMap;
    int m_nSubBatch;
    int idx;
    bool m_bMonitorFailed;
    bool m_bSubInactive;

	int m_nPublishingInterval;		// �첽�������
	int m_nSampleInterval;			// �첽�������

	COPCUADevice(DRVHANDLE hDevice, CVDEVICE *pDevice);
	virtual ~COPCUADevice();

	// OPC UA service calls
	long read();
	long write(unsigned short nType, const int nID, unsigned short nNumber, const char* pName, void* pValue);
		CVDEVICE* pDevice = Drv_GetDeviceInfo(m_hDevice);
	long AddData(DRVHANDLE hDevice, DRVHANDLE hDataBlock);
	void disconnect(bool bDelete);
	void disconnect(void* pClientPara, bool bDelete);
    DRVHANDLE GetDeviceHandle() { return m_hDevice; }
private:
	void OnStartRead();
	long UpdateData2Dit(DRVHANDLE hDataBlock, void* val);//��opcua�豸����ֵ�����dit�ӿ�
	void UpdateBad2Dit();
    void SetUAClientConfig(UA_Client* pclient);
	void connect();
	long readSync();
	long readAsync();
	long writeSync(unsigned short nType, const int nID, unsigned short nNumber, const char* pName, void* pValue);
	long writeAsync(unsigned short nType, const int nID, unsigned short nNumber, const char* pName, void* pValue);
};

#endif // 
