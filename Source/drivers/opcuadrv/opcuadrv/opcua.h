/************************************************************************
*	Filename:		opcua.h
*	Copyright:		Shanghai Baosight Company Software Co., Ltd.
*
*	Description:	opcua����
*
*	@author:		zhangqiang
*	@version		2016-01-20	zhangqiang	Initial Version
*************************************************************************/
#ifndef OPCUA_H
#define OPCUA_H
#include <string>
#include "opcuadevice.h"
#define UA_TYPES_BOOLEAN 0
#define UA_TYPES_SBYTE 1
#define UA_TYPES_BYTE 2
#define UA_TYPES_INT16 3
#define UA_TYPES_UINT16 4
#define UA_TYPES_INT32 5
#define UA_TYPES_UINT32 6
#define UA_TYPES_INT64 7
#define UA_TYPES_UINT64 8
#define UA_TYPES_FLOAT 9
#define UA_TYPES_DOUBLE 10
#define UA_TYPES_STRING 11
#define UA_TYPES_DATETIME 12
#define UA_TYPES_GUID 13
#define UA_TYPES_BYTESTRING 14
#define UA_TYPES_XMLELEMENT 15
#define UA_TYPES_NODEID 16
#define UA_TYPES_EXPANDEDNODEID 17
#define UA_TYPES_STATUSCODE 18
#define UA_TYPES_QUALIFIEDNAME 19
#define UA_TYPES_LOCALIZEDTEXT 20
#define UA_TYPES_EXTENSIONOBJECT 21
#define UA_TYPES_DATAVALUE 22
#define UA_TYPES_VARIANT 23
#define UA_TYPES_DIAGNOSTICINFO 24

#define UA_VALUE_LENGTH		512
//#define UA_VALUE_LENGTH		512
//#define UA_NAME_LENGTH		64
#define UA_NAME_LENGTH		256

#define STRINGTYPE			"String"
#define NUMERICTYPE			"Numeric"
#define GUIDTYPE			"Guid"
#define OPAQUETYPE			"Opaque"

#define USER_DATABLOCKDATA_INDEX 0 //����첽����map�ĵ�ַ���󶨾��hDevice
#define USER_DEVICE_FAILED_NUMBER_INDEX 1 //��Ŷ�ȡ����ʧ�ܻ��ߴ���δ����״̬����ѯ����

using namespace std;

struct TOPCUAData
{
	int  nQuality;
	unsigned char pbuf[UA_VALUE_LENGTH];
};

// �����豸
long AddDevice(DRVHANDLE hDevice);
const char * GetPureAddress(const char *pAddr);
void FromCstring(string strContent, unsigned char *pContent, int &iID, bool bHex);
long LoadDllFunctions();
#endif //
