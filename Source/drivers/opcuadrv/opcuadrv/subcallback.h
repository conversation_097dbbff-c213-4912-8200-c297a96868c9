#pragma once
#include "opcua.h"
#include "opcua/open62541.h"

#ifdef UA_ENABLE_SUBSCRIPTIONS
void SetDataBlockData2Map(std::map<DRVHANDLE, TOPCUAData>* pMapDataBlockData, DRVHANDLE& hDataBlock);
void handler_dataChanged(UA_Client* client, UA_UInt32 subId, void* subContext,
    UA_UInt32 monId, void* monContext, UA_DataValue* value);

void deleteSubscriptionCallback(UA_Client* client, UA_UInt32 subscriptionId, void* subscriptionContext);

void subscriptionInactivityCallback(UA_Client* client, UA_UInt32 subId, void* subContext);
#endif

void stateCallback(UA_Client* client, UA_SecureChannelState channelState,
    UA_SessionState sessionState, UA_StatusCode connectStatus);
