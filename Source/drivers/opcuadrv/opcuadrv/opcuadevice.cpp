/************************************************************************
*	Filename:		opcuadevice.cpp
*	Copyright:		Shanghai Baosight Company Software Co., Ltd.
*
*	Description:	opcua�豸��
*
*	@author:		zhangqiang
*	@version		2016-01-20	zhangqiang	Initial Version
*************************************************************************/
#include "common/CVLog.h"
#include "common/RMAPIDef.h"
#include "opcuadevice.h"
#include "common/TypeCast.h"
#include <stdlib.h>
#include <stdio.h>
#include <algorithm>
#include "os_time.h"
#include "subcallback.h"

using namespace std;
ACE_RW_Mutex g_rwMutex;
extern CCVLog g_opcuaLog;

#define DEFAULT_PUBLISHING_INTERVAL		1000					// �������Ĭ��ֵ
#define MIN_PUBLISHING_INTERVAL			100						// ���������Сֵ
#define MAX_PUBLISHING_INTERVAL			60000					// ����������ֵ
#define PUBLISHING_INTERVAL_PREFIX		"publishinginterval="	// �������ȡֵǰ׺

#define DEFAULT_SAMPLE_INTERVAL			500						// �������Ĭ��ֵ
#define MIN_SAMPLE_INTERVAL				-1						// ���������Сֵ
#define MAX_SAMPLE_INTERVAL				50000					// ����������ֵ
#define SAMPLE_INTERVAL_PREFIX			"sampleinterval="		// �������ȡֵǰ׺
#define EMPTY_CONFIG_PARAM				""						// ���ò���Ϊ��

/**
 * Test if the address has array index.
 * @param  -[in]  addr: address
 * @param  -[out]  indexVec: index array parsed if returns true.
 * @return: true if containing valid array index, otherwise false.
 */
bool parseArrayIndex(const std::string addr, std::vector<int> &indexVec)
{
    std::string::size_type open_pos = 0;
    std::string::size_type close_pos = 0;
    std::string ind;
    while ((open_pos = addr.find("[", open_pos)) != std::string::npos) {
        if (open_pos == 0 || (close_pos > 0 && open_pos != close_pos + 1)) return false;
        close_pos = addr.find("]", open_pos);
        if (close_pos != std::string::npos) {
            ind = addr.substr(open_pos + 1, close_pos - open_pos - 1);
            if (ind.find_first_not_of("0123456789") != std::string::npos) {
                return false;
            } else {
                indexVec.push_back(atoi(ind.c_str()));
                open_pos = close_pos;
            }
        } else {
            return false;
        }
    }
    if (close_pos != addr.length() - 1) return false;
    else return true;
}

/*
* ����������Ƿ�Ƿ������������Сֵ�����ֵ��������ΪĬ��ֵ
*/
void CheckInvalidConfig(int& nConfigVal, int nMinVal, int nMaxVal, int nDefaultVal)
{
	if (nConfigVal < nMinVal || nConfigVal > nMaxVal)
	{
		nConfigVal = nDefaultVal;
	}
}


/* loadFile parses the certificate file.
 *
 * @param  path               specifies the file name given in argv[]
 * @return Returns the file content after parsing */
static UA_INLINE UA_ByteString
loadFile(const char *const path) {
    UA_ByteString fileContents = UA_STRING_NULL;

    /* Open the file */
    FILE *fp = fopen(path, "rb");
    if(!fp) {
        errno = 0; /* We read errno also from the tcp layer... */
        return fileContents;
    }

    /* Get the file length, allocate the data and read */
    fseek(fp, 0, SEEK_END);
    fileContents.length = (size_t)ftell(fp);
    fileContents.data = (UA_Byte *)UA_malloc(fileContents.length * sizeof(UA_Byte));
    if(fileContents.data) {
        fseek(fp, 0, SEEK_SET);
        size_t read = fread(fileContents.data, sizeof(UA_Byte), fileContents.length, fp);
        if(read != fileContents.length)
            UA_ByteString_clear(&fileContents);
    } else {
        fileContents.length = 0;
    }
    fclose(fp);

    return fileContents;
}

long OPCUAClientConnect(UA_Client* pClient, char* endpointUrl, const string& username, const string& password)
{
	UA_StatusCode retval = UA_STATUSCODE_GOOD;
	if (!username.empty() || !password.empty())
	{
		retval = UA_Client_connectUsername(pClient, endpointUrl, username.c_str(), password.c_str());
	}
	else
	{
		retval = UA_Client_connect(pClient, endpointUrl);
	}
	if (retval != UA_STATUSCODE_GOOD) {
		CV_ERROR(g_opcuaLog, retval,"UA_Client_connect failed. IP:%s", endpointUrl);
	}
	return retval;
}


long OPCUAClientDisconnect(void *pClientPara, bool bDelete)
{
	if (pClientPara == NULL)
		return UA_STATUSCODE_GOOD;
	UA_Client * pClient = (UA_Client *)pClientPara;
	UA_StatusCode retval = UA_Client_disconnect(pClient);
	if (bDelete)
	{
		UA_Client_delete(pClient);
	}
	return retval;
}

int flatindex(int arrayDimSize, unsigned int *arrayDims, int arrayLength, vector<int> indices)
{
    if (arrayDims == 0) {   // array dimension is 0 when 1D array
        if (indices.size() == 1 && indices[0] < arrayLength) return indices[0];
        else return -1;
    }
    if (indices.size() != arrayDimSize) {
        return -1;
    }
    for (int i = 0; i < arrayDimSize; ++i) {
        if (indices[i] >= arrayDims[i]) {
            return -1;
        }
    }
    int flatidx = 0;
    for (int i = 0; i < indices.size(); ++i) {
        int num = indices[i];
        for (int j = i+1; j < arrayDimSize; ++j) {
            num *= arrayDims[j];
        }
        flatidx += num;
    }
    return flatidx;
}

long OPCUAClientSyncRead(UA_Client* pClient, void* pReadreqPara, unsigned char pReadValue[][UA_VALUE_LENGTH],bool* pHasValue,unsigned int nSize, map<int, vector<AddrIndex> > requestToAddrIndexMap)//32 is the length of the value
{
	UA_ReadRequest *pReadreq = (UA_ReadRequest*)pReadreqPara;	
	UA_ReadResponse readresp = UA_Client_Service_read(pClient, *pReadreq);
	memset(pReadValue,0,nSize * UA_VALUE_LENGTH);
	memset(pHasValue,0,nSize);
	if (readresp.responseHeader.serviceResult == UA_STATUSCODE_GOOD &&
		readresp.resultsSize > 0)
	{
		for (int i = 0; i < readresp.resultsSize; ++i)
		{
			//CV_INFO(g_opcuaLog,"%d %d %s",readresp.results[i].hasValue,readresp.results[i].status,UA_StatusCode_name(readresp.results[i].status));
			if (readresp.results[i].hasValue && UA_Variant_isScalar(&readresp.results[i].value))
			{
                int addrIndex = requestToAddrIndexMap[i][0].idx;
				pHasValue[addrIndex] = true;
				//switch (readresp.results[i].value.type->typeIndex)
				{
					if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_BOOLEAN])
					{
						UA_Boolean value = *(UA_Boolean*)readresp.results[i].value.data;
						memcpy(*(pReadValue + addrIndex), &value, 1);//1���ֽ�
					}	
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_SBYTE])
					{
						UA_SByte value = *(UA_SByte*)readresp.results[i].value.data;
						memcpy(*(pReadValue + addrIndex), &value, 1);//1���ֽ�
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_BYTE])
					{
						UA_Byte value = *(UA_Byte*)readresp.results[i].value.data;
						memcpy(*(pReadValue + addrIndex), &value, 1);//1���ֽ�
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_INT16])
					{
						UA_Int16 value = *(UA_Int16*)readresp.results[i].value.data;
						memcpy(*(pReadValue + addrIndex), &value, 2);//2���ֽ�
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_UINT16])
					{
						UA_UInt16 value = *(UA_UInt16*)readresp.results[i].value.data;
						memcpy(*(pReadValue + addrIndex), &value, 2);//2���ֽ�
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_INT32])
					{
						UA_Int32 value = *(UA_Int32*)readresp.results[i].value.data;
						memcpy(*(pReadValue + addrIndex), &value, 4);//4���ֽ�
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_UINT32])
					{
						UA_UInt32 value = *(UA_UInt32*)readresp.results[i].value.data;
						memcpy(*(pReadValue + addrIndex), &value, 4);//4���ֽ�
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_FLOAT])
					{
						UA_Float value = *(UA_Float*)readresp.results[i].value.data;
						memcpy(*(pReadValue + addrIndex), &value, 4);//4���ֽ�
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_DOUBLE])
					{
						UA_Double value = *(UA_Double*)readresp.results[i].value.data;
						memcpy(*(pReadValue + addrIndex), &value, 8);//8���ֽ�
					}
					else if (readresp.results[i].value.type == &UA_TYPES[UA_TYPES_INT64])
					{
						UA_Int64 value = *(UA_Int64*)readresp.results[i].value.data;
						memcpy(*(pReadValue + addrIndex), &value, 8);//8���ֽ�
					}
					else if (readresp.results[i].value.type == &UA_TYPES[UA_TYPES_UINT64])
					{
						UA_UInt64 value = *(UA_UInt64*)readresp.results[i].value.data;
						memcpy(*(pReadValue + addrIndex), &value, 8);//8���ֽ�
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_STRING])
					{
						UA_String value = *(UA_String*)readresp.results[i].value.data;
						if(value.length < UA_VALUE_LENGTH)
							memcpy(*(pReadValue + addrIndex), value.data, value.length);
						else
							memcpy(*(pReadValue + addrIndex), value.data, UA_VALUE_LENGTH - 1);
					}
					else
					{
						pHasValue[addrIndex]=false;
					}
						
				}
			} else if (readresp.results[i].hasValue && readresp.results[i].value.arrayLength > 0) {
                int arrayDimSize = readresp.results[i].value.arrayDimensionsSize;
                unsigned int *arrayDims = readresp.results[i].value.arrayDimensions;
                int arrayLength = readresp.results[i].value.arrayLength;
                //switch (readresp.results[i].value.type->typeIndex)
				{
					if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_BOOLEAN])
					{
						UA_Boolean *pv = (UA_Boolean*)readresp.results[i].value.data;
                        for (int j = 0; j < requestToAddrIndexMap[i].size(); ++j) {
                            int addrIndex = requestToAddrIndexMap[i][j].idx;
                            int idx = flatindex(arrayDimSize, arrayDims, arrayLength, requestToAddrIndexMap[i][j].arrIndices);
                            if (idx < 0) {
                                CV_WARN(g_opcuaLog, -1, "Address %s array index is invalid", requestToAddrIndexMap[i][j].origname.c_str());
                                pHasValue[addrIndex]=false;
                                continue;
                            }
                            pHasValue[addrIndex] = true;
                            memcpy(*(pReadValue + addrIndex), pv+idx, 1);//1���ֽ�
                        }
					}	
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_SBYTE])
					{
						UA_SByte *pv = (UA_SByte*)readresp.results[i].value.data;
                        for (int j = 0; j < requestToAddrIndexMap[i].size(); ++j) {
                            int addrIndex = requestToAddrIndexMap[i][j].idx;
                            int idx = flatindex(arrayDimSize, arrayDims, arrayLength, requestToAddrIndexMap[i][j].arrIndices);
                            if (idx < 0) {
                                CV_WARN(g_opcuaLog, -1, "Address %s array index is invalid", requestToAddrIndexMap[i][j].origname.c_str());
                                pHasValue[addrIndex]=false;
                                continue;
                            }
                            pHasValue[addrIndex] = true;
                            memcpy(*(pReadValue + addrIndex), pv+idx, 1);//1���ֽ�
                        }
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_BYTE])
					{
						UA_Byte *pv = (UA_Byte*)readresp.results[i].value.data;
                        for (int j = 0; j < requestToAddrIndexMap[i].size(); ++j) {
                            int addrIndex = requestToAddrIndexMap[i][j].idx;
                            int idx = flatindex(arrayDimSize, arrayDims, arrayLength, requestToAddrIndexMap[i][j].arrIndices);
                            if (idx < 0) {
                                CV_WARN(g_opcuaLog, -1, "Address %s array index is invalid", requestToAddrIndexMap[i][j].origname.c_str());
                                pHasValue[addrIndex]=false;
                                continue;
                            }
                            pHasValue[addrIndex] = true;
                            memcpy(*(pReadValue + addrIndex), pv+idx, 1);//1���ֽ�
                        }
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_INT16])
					{
						UA_Int16 *pv = (UA_Int16*)readresp.results[i].value.data;
                        for (int j = 0; j < requestToAddrIndexMap[i].size(); ++j) {
                            int addrIndex = requestToAddrIndexMap[i][j].idx;
                            int idx = flatindex(arrayDimSize, arrayDims, arrayLength, requestToAddrIndexMap[i][j].arrIndices);
                            if (idx < 0) {
                                CV_WARN(g_opcuaLog, -1, "Address %s array index is invalid", requestToAddrIndexMap[i][j].origname.c_str());
                                pHasValue[addrIndex]=false;
                                continue;
                            }
                            pHasValue[addrIndex] = true;
                            memcpy(*(pReadValue + addrIndex), pv+idx, 2);//2���ֽ�
                        }
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_UINT16])
					{
						UA_UInt16 *pv = (UA_UInt16*)readresp.results[i].value.data;
                        for (int j = 0; j < requestToAddrIndexMap[i].size(); ++j) {
                            int addrIndex = requestToAddrIndexMap[i][j].idx;
                            int idx = flatindex(arrayDimSize, arrayDims, arrayLength, requestToAddrIndexMap[i][j].arrIndices);
                            if (idx < 0) {
                                CV_WARN(g_opcuaLog, -1, "Address %s array index is invalid", requestToAddrIndexMap[i][j].origname.c_str());
                                pHasValue[addrIndex]=false;
                                continue;
                            }
                            pHasValue[addrIndex] = true;
                            memcpy(*(pReadValue + addrIndex), pv+idx, 2);//2���ֽ�
                        }
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_INT32])
					{
						UA_Int32 *pv = (UA_Int32*)readresp.results[i].value.data;
                        for (int j = 0; j < requestToAddrIndexMap[i].size(); ++j) {
                            int addrIndex = requestToAddrIndexMap[i][j].idx;
                            int idx = flatindex(arrayDimSize, arrayDims, arrayLength, requestToAddrIndexMap[i][j].arrIndices);
                            if (idx < 0) {
                                CV_WARN(g_opcuaLog, -1, "Address %s array index is invalid", requestToAddrIndexMap[i][j].origname.c_str());
                                pHasValue[addrIndex]=false;
                                continue;
                            }
                            pHasValue[addrIndex] = true;
                            memcpy(*(pReadValue + addrIndex), pv+idx, 4);//4���ֽ�
                        }
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_UINT32])
					{
						UA_UInt32 *pv = (UA_UInt32*)readresp.results[i].value.data;
                        for (int j = 0; j < requestToAddrIndexMap[i].size(); ++j) {
                            int addrIndex = requestToAddrIndexMap[i][j].idx;
                            int idx = flatindex(arrayDimSize, arrayDims, arrayLength, requestToAddrIndexMap[i][j].arrIndices);
                            if (idx < 0) {
                                CV_WARN(g_opcuaLog, -1, "Address %s array index is invalid", requestToAddrIndexMap[i][j].origname.c_str());
                                pHasValue[addrIndex]=false;
                                continue;
                            }
                            pHasValue[addrIndex] = true;
                            memcpy(*(pReadValue + addrIndex), pv+idx, 4);//4���ֽ�
                        }
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_FLOAT])
					{
						UA_Float *pv = (UA_Float*)readresp.results[i].value.data;
                        for (int j = 0; j < requestToAddrIndexMap[i].size(); ++j) {
                            int addrIndex = requestToAddrIndexMap[i][j].idx;
                            int idx = flatindex(arrayDimSize, arrayDims, arrayLength, requestToAddrIndexMap[i][j].arrIndices);
                            if (idx < 0) {
                                CV_WARN(g_opcuaLog, -1, "Address %s array index is invalid", requestToAddrIndexMap[i][j].origname.c_str());
                                pHasValue[addrIndex]=false;
                                continue;
                            }
                            pHasValue[addrIndex] = true;
                            memcpy(*(pReadValue + addrIndex), pv+idx, 4);//4���ֽ�
                        }
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_DOUBLE])
					{
						UA_Double *pv = (UA_Double*)readresp.results[i].value.data;
                        for (int j = 0; j < requestToAddrIndexMap[i].size(); ++j) {
                            int addrIndex = requestToAddrIndexMap[i][j].idx;
                            int idx = flatindex(arrayDimSize, arrayDims, arrayLength, requestToAddrIndexMap[i][j].arrIndices);
                            if (idx < 0) {
                                CV_WARN(g_opcuaLog, -1, "Address %s array index is invalid", requestToAddrIndexMap[i][j].origname.c_str());
                                pHasValue[addrIndex]=false;
                                continue;
                            }
                            pHasValue[addrIndex] = true;
                            memcpy(*(pReadValue + addrIndex), pv+idx, 8);//8���ֽ�
                        }
					}
					else if (readresp.results[i].value.type == &UA_TYPES[UA_TYPES_INT64])
					{
						UA_Int64* pv = (UA_Int64*)readresp.results[i].value.data;
						for (int j = 0; j < requestToAddrIndexMap[i].size(); ++j) {
							int addrIndex = requestToAddrIndexMap[i][j].idx;
							int idx = flatindex(arrayDimSize, arrayDims, arrayLength, requestToAddrIndexMap[i][j].arrIndices);
							if (idx < 0) {
								CV_WARN(g_opcuaLog, -1, "Address %s array index is invalid", requestToAddrIndexMap[i][j].origname.c_str());
								pHasValue[addrIndex] = false;
								continue;
							}
						pHasValue[addrIndex] = true;
						int64_t dValue = *(UA_Int64*)(pv+idx);
						memcpy(*(pReadValue + addrIndex), &dValue, 8);//8���ֽ�
						}
					}
					else if (readresp.results[i].value.type == &UA_TYPES[UA_TYPES_UINT64])
					{
					UA_UInt64* pv = (UA_UInt64*)readresp.results[i].value.data;
					for (int j = 0; j < requestToAddrIndexMap[i].size(); ++j) {
						int addrIndex = requestToAddrIndexMap[i][j].idx;
						int idx = flatindex(arrayDimSize, arrayDims, arrayLength, requestToAddrIndexMap[i][j].arrIndices);
						if (idx < 0) {
							CV_WARN(g_opcuaLog, -1, "Address %s array index is invalid", requestToAddrIndexMap[i][j].origname.c_str());
							pHasValue[addrIndex] = false;
							continue;
						}
						pHasValue[addrIndex] = true;
						uint64_t dValue = *(UA_UInt64*)(pv + idx);
						memcpy(*(pReadValue + addrIndex), &dValue, 8);//8���ֽ�
					}
					}
					else if(readresp.results[i].value.type == &UA_TYPES[UA_TYPES_STRING])
					{
						UA_String *pv = (UA_String*)readresp.results[i].value.data;
                        for (int j = 0; j < requestToAddrIndexMap[i].size(); ++j) {
                            int addrIndex = requestToAddrIndexMap[i][j].idx;
                            int idx = flatindex(arrayDimSize, arrayDims, arrayLength, requestToAddrIndexMap[i][j].arrIndices);
                            if (idx < 0) {
                                CV_WARN(g_opcuaLog, -1, "Address %s array index is invalid", requestToAddrIndexMap[i][j].origname.c_str());
                                pHasValue[addrIndex]=false;
                                continue;
                            }
                            pHasValue[addrIndex] = true;
                            if((pv+idx)->length < UA_VALUE_LENGTH)
                                memcpy(*(pReadValue + addrIndex), (pv+idx)->data, (pv+idx)->length);
                            else
                                memcpy(*(pReadValue + addrIndex), (pv+idx)->data, UA_VALUE_LENGTH - 1);
                        }
					}
					else
					{
                        for (int j = 0; j < requestToAddrIndexMap[i].size(); ++j) {
                            int addrIndex = requestToAddrIndexMap[i][j].idx;
                            pHasValue[addrIndex]=false;
                        }
					}
                }
            }
		}
	}
	UA_StatusCode ret = readresp.responseHeader.serviceResult;
	UA_ReadResponse_deleteMembers(&readresp);
	return ret;
}

long OPCUAClientSyncWrite(UA_Client* pClient, unsigned short nDataType, const int nID, unsigned short nNumber, const char* pName, void* pValue)
{
    vector<int> indexVec;
    if (parseArrayIndex(pName, indexVec)) {
        // CV_WARN(g_opcuaLog, -1, "Write to array element %s is not supported.", pName);
        // return 0;

		//过滤数组的下标
		std::string arrayNmae = pName;
    	size_t pos = arrayNmae.find('[');          
    	if (pos != std::string::npos) 
		{    
        	arrayNmae = arrayNmae.substr(0, pos);          
		}

		UA_NodeId arrayNodeId;
		if(nID > 0)
			arrayNodeId = UA_NODEID_NUMERIC(nNumber, nID);
		else
			arrayNodeId = UA_NODEID_STRING_ALLOC(nNumber, arrayNmae.c_str());

		//读取数组所有元素的值
		UA_Variant value; 
    	UA_Variant_init(&value);
    	UA_StatusCode status = UA_Client_readValueAttribute(pClient, arrayNodeId, &value);
		if(status != UA_STATUSCODE_GOOD)
		{
			printf("读取数组失败\n");
			CV_INFO(g_opcuaLog,"读取数组失败");
		}
		else
		{
			printf("------------------读取成功\n");
			UA_Byte *arrayData = (UA_Byte *)value.data;
    		for (size_t i = 0; i < 6; i++) {
        		printf("元素 %d: %d\n", i, arrayData[i]);
    		}
		}

		//获取数组的维度
		UA_UInt32 rows = 0;
		size_t cols = 0;
		if(value.arrayDimensions)
		{
			rows = *value.arrayDimensions;
    	 	cols = value.arrayDimensionsSize;
		}
		else
		{
			cols = value.arrayLength;
		}

		//判断边界值
		if(indexVec.size() == 1)
		{
			if(indexVec[0] > cols)
				return 0;
		}
		else if(indexVec.size() == 2)
		{
			if(indexVec[0] > rows || indexVec[1] > cols)
				return 0;
		}

		int data_length = 0;
		switch(nDataType)
		{
			case UA_TYPES_BOOLEAN:
			data_length = 1;
			break;

			case UA_TYPES_SBYTE:
			data_length = 1;
			break;

			case UA_TYPES_BYTE:
			data_length = 1;
			break;

			case UA_TYPES_INT16:
			data_length = 2;
			break;

			case UA_TYPES_UINT16:
			data_length = 2;
			break;

			case UA_TYPES_INT32:
			data_length = 4;
			break;

			case UA_TYPES_UINT32:
			data_length = 4;
			break;

			case UA_TYPES_FLOAT:
			data_length = sizeof(float);
			break;

			case UA_TYPES_DOUBLE:
			data_length = sizeof(double);
			break;

			case UA_TYPES_INT64:
			data_length = 8;
			break;

			case UA_TYPES_UINT64:
			data_length = 8;
			break;

			default:
			break;
		}

		//对字符串和其他类型分开处理
		if(nDataType == UA_TYPES_STRING)
		{
			UA_String *stringValue = (UA_String *)value.data;

			if(indexVec.size() < 2)
				stringValue[indexVec[0]] = UA_String_fromChars((char*)pValue);
			else
				stringValue[cols*indexVec[0] + indexVec[1]] = UA_String_fromChars((char*)pValue);

			UA_Variant_setArray(&value,stringValue,value.arrayLength,&UA_TYPES[UA_TYPES_STRING]);
		}
		else
		{
			//偏移地址
			int array_length;
			if(indexVec.size()<2)
				array_length = (indexVec[0])*data_length;
			else 
				array_length = (cols*indexVec[0] + indexVec[1])*data_length;

			memcpy(value.data+array_length,pValue,data_length);
		}
		
    	// 写入整个数组（覆盖原有值）
    	status = UA_Client_writeValueAttribute(pClient, arrayNodeId, &value);
    	if (status != UA_STATUSCODE_GOOD) {
        	printf("写入失败: 0x%08x\n", status);
			CV_INFO(g_opcuaLog,"写入失败");
    	}
		else
		{
			printf("------------------写入成功");
		}
    }

	UA_WriteRequest wReq;
	UA_WriteRequest_init(&wReq);
	wReq.nodesToWrite = UA_WriteValue_new();
	wReq.nodesToWriteSize = 1;
	if(nID > 0)
		wReq.nodesToWrite[0].nodeId = UA_NODEID_NUMERIC(nNumber, nID); /* assume this node exists */
	else
		wReq.nodesToWrite[0].nodeId = UA_NODEID_STRING_ALLOC(nNumber, pName); /* assume this node exists */
	wReq.nodesToWrite[0].attributeId = UA_ATTRIBUTEID_VALUE;
	wReq.nodesToWrite[0].value.hasValue = UA_TRUE;
	wReq.nodesToWrite[0].value.value.type = &UA_TYPES[nDataType];
	wReq.nodesToWrite[0].value.value.storageType = UA_VARIANT_DATA_NODELETE; //do not free the integer on deletion
	if (nDataType == UA_TYPES_STRING)
	{
		char *pszTemp = (char*)pValue;
		UA_String strValue = UA_STRING(pszTemp); 
		wReq.nodesToWrite[0].value.value.data = &strValue;
	}
	else
		wReq.nodesToWrite[0].value.value.data = pValue;

	UA_WriteResponse wResp = UA_Client_Service_write(pClient, wReq);
	long lRet = wResp.responseHeader.serviceResult;
	UA_WriteRequest_deleteMembers(&wReq);
	UA_WriteResponse_deleteMembers(&wResp);
	return lRet;
}

void OPCUAGenerateStrReadRequest(void** pReadRequest, unsigned int size, unsigned short* pNumber, const char pName[][UA_NAME_LENGTH])//64 is the length of the variable name
{
	UA_ReadRequest* pReaddReq = (UA_ReadRequest*)malloc(sizeof(UA_ReadRequest));// = (UA_ReadRequest*)pReadRequest;
	UA_ReadRequest_init(pReaddReq);

	pReaddReq->nodesToRead = (UA_ReadValueId*)UA_Array_new(size, &UA_TYPES[UA_TYPES_READVALUEID]);//UA_ReadValueId_new();UA_TYPES_READVALUEID
	pReaddReq->nodesToReadSize = size;
	for (int i = 0; i < size; ++i)
	{
		char pTemp[UA_NAME_LENGTH];
		memcpy(pTemp, pName+i, UA_NAME_LENGTH);
		pReaddReq->nodesToRead[i].nodeId = UA_NODEID_STRING_ALLOC(*(pNumber + i), pTemp); /* assume this node exists */
		pReaddReq->nodesToRead[i].attributeId = UA_ATTRIBUTEID_VALUE;
	}
	*pReadRequest = pReaddReq;
	
}

void OPCUAGenerateNumReadRequest(void** pReadRequest, unsigned int size, unsigned short* pNumber, const int* pIDs)//64 is the length of the variable name
{
	UA_ReadRequest* pReaddReq = (UA_ReadRequest*)malloc(sizeof(UA_ReadRequest));// = (UA_ReadRequest*)pReadRequest;
	UA_ReadRequest_init(pReaddReq);
	pReaddReq->nodesToRead = (UA_ReadValueId*)UA_Array_new(size, &UA_TYPES[UA_TYPES_READVALUEID]);//UA_ReadValueId_new();UA_TYPES_READVALUEID
	pReaddReq->nodesToReadSize = size;
	for (int i = 0; i < size; ++i)
	{
		pReaddReq->nodesToRead[i].nodeId = UA_NODEID_NUMERIC(*(pNumber + i), *(pIDs + i)); /* assume this node exists */
		pReaddReq->nodesToRead[i].attributeId = UA_ATTRIBUTEID_VALUE;
	}
	*pReadRequest = pReaddReq;

}

void OPCUAFreeReadRequest(void* pReadRequest)
{
	UA_ReadRequest_deleteMembers((UA_ReadRequest*)pReadRequest);
	free(pReadRequest);
	
}

COPCUADevice::COPCUADevice( DRVHANDLE hDevice, CVDEVICE *pDevice )
	: m_hDevice(hDevice),m_nRMStatus(0)
{
	m_bFlagComplete = false;
	string strPara1 = pDevice->pszParam1;
	m_bSingle = Drv_IsSingleLink(hDevice);

	m_pStrReadQuest = NULL;
	m_pNumReadQuest = NULL;
	m_pStrReadValue = NULL;
	m_pNumReadValue = NULL;
	m_pStrReadHasValue = NULL;
	m_pNumReadHasValue = NULL;

	m_mainUrl = pDevice->pszConnParam;
	m_backUrl = pDevice->pszParam3;

    m_bFlagAsync = false;
	m_nAsyncTimeout = 1000;
    m_bMonitorFailed = false;
    m_bSubInactive = false;
    m_nSubBatch = 250;

	m_nPublishingInterval = DEFAULT_PUBLISHING_INTERVAL;
	m_nSampleInterval = DEFAULT_SAMPLE_INTERVAL;

    string strPara2 = pDevice->pszParam2;
    int pairDelimPos = strPara2.find(';');

    // param2 "username=user;password=pass;async=0;asynctimeout=1000"
    do {
        string tmpstr = strPara2.substr(0, pairDelimPos);
        int valueDelimPos = tmpstr.find('=');
        if (tmpstr.rfind("username=", 0) == 0) {
            m_username = tmpstr.substr(valueDelimPos + 1);
        } else if (tmpstr.rfind("password=", 0) == 0) {
            m_password = tmpstr.substr(valueDelimPos + 1);
        } else if (tmpstr.rfind("async=", 0) == 0) {
            m_bFlagAsync = atoi(tmpstr.substr(valueDelimPos + 1).c_str());
        } else if (tmpstr.rfind("asynctimeout=", 0) == 0) {
            m_nAsyncTimeout = atoi(tmpstr.substr(valueDelimPos + 1).c_str());
        } else if (tmpstr.rfind("batch=", 0) == 0) {
            m_nSubBatch = atoi(tmpstr.substr(valueDelimPos + 1).c_str());
        } else if (tmpstr.rfind(PUBLISHING_INTERVAL_PREFIX, 0) == 0) {
			std::string strSuffix = tmpstr.substr(valueDelimPos + 1);
			if (EMPTY_CONFIG_PARAM == strSuffix)
			{
				m_nPublishingInterval = DEFAULT_PUBLISHING_INTERVAL;
			}
			else
			{
				m_nPublishingInterval = atoi(strSuffix.c_str());
				// �������������÷Ƿ���ʹ��Ĭ��ֵ
				CheckInvalidConfig(m_nPublishingInterval, MIN_PUBLISHING_INTERVAL, MAX_PUBLISHING_INTERVAL, DEFAULT_PUBLISHING_INTERVAL);
			}
		} else if (tmpstr.rfind(SAMPLE_INTERVAL_PREFIX, 0) == 0) {
			std::string strSuffix = tmpstr.substr(valueDelimPos + 1);
			if (EMPTY_CONFIG_PARAM == strSuffix)
			{
				m_nPublishingInterval = DEFAULT_PUBLISHING_INTERVAL;
			}
			else
			{
				m_nSampleInterval = atoi(strSuffix.c_str());
				// �������������÷Ƿ���ʹ��Ĭ��ֵ
				CheckInvalidConfig(m_nSampleInterval, MIN_SAMPLE_INTERVAL, MAX_SAMPLE_INTERVAL, DEFAULT_SAMPLE_INTERVAL);
			}
		}
        if (pairDelimPos == string::npos) {
           CV_INFO(g_opcuaLog, "End analyze param2: %s, m_username: %s, m_password: %s m_bFlagSync: %d m_nSubBatch: %d, m_nPublishingInterval: %d, m_nSampleInterval: %d", pDevice->pszParam2, m_username.c_str(), m_password.c_str(), m_bFlagAsync, m_nSubBatch, m_nPublishingInterval, m_nSampleInterval);
			break;
		}
        strPara2 = strPara2.substr(pairDelimPos + 1);
        pairDelimPos = strPara2.find(';');
    } while (true);

    m_pClientMain = UA_Client_new();
    SetUAClientConfig(m_pClientMain);
    m_pClientBack = UA_Client_new();
    SetUAClientConfig(m_pClientBack);
 	m_pActiveClient = m_pClientMain;

}

COPCUADevice::~COPCUADevice()
{
	// disconnect if we're still connected
	if(m_pStrReadQuest)
	{
		OPCUAFreeReadRequest(m_pStrReadQuest);
	}
	if(m_pNumReadQuest)
	{
		OPCUAFreeReadRequest(m_pNumReadQuest);
	}
	if (m_pClientMain)
	{
		OPCUAClientDisconnect(m_pClientMain, true);
	}
	if (m_pClientBack)
	{
		OPCUAClientDisconnect(m_pClientBack, true);
	}
	m_pActiveClient = NULL;
	m_pClientMain = NULL;
	m_pClientBack = NULL;

	if(m_pStrReadValue)
	{
		delete []m_pStrReadValue;
		m_pStrReadValue = NULL;
	}
	if(m_pStrReadHasValue)
	{
		delete []m_pStrReadHasValue;
		m_pStrReadHasValue = NULL;
	}
	if(m_pNumReadValue)
	{
		delete []m_pNumReadValue;
		m_pNumReadValue = NULL;
	}
	if(m_pNumReadHasValue)
	{
		delete []m_pNumReadHasValue;
		m_pNumReadHasValue = NULL;
	}
}

void COPCUADevice::SetUAClientConfig(UA_Client *pclient)
{
	UA_ClientConfig* cc = UA_Client_getConfig(pclient);
	if (!m_username.empty() || !m_password.empty()) {
		// string derPath = "drivers/";
		// char szAppName[ICV_SHORTFILENAME_MAXLEN] = { 0 };
		// CVComm.GetCurrentAppName(szAppName);
		// UA_ByteString certificate = loadFile((derPath + szAppName + "/client_cert.der").c_str());
		// UA_ByteString privateKey = loadFile((derPath + szAppName + "/client_key.der").c_str());
		string derPath = CVComm.GetCVHome();
		UA_ByteString certificate = loadFile((derPath + "/executable/client_cert.der").c_str());
		UA_ByteString privateKey = loadFile((derPath + "/executable/client_key.der").c_str());
		if (certificate.length == 0 || privateKey.length == 0) {
			CV_ERROR(g_opcuaLog, -1, "Failed to load certificate, length of client_cert and client_key is %d %d", certificate.length, privateKey.length);
		}
		UA_ClientConfig_setDefaultEncryption(cc, certificate, privateKey, NULL, 0, NULL, 0);
		UA_CertificateVerification_AcceptAll(&cc->certificateVerification);
		UA_ApplicationDescription_clear(&cc->clientDescription);
		cc->clientDescription.applicationUri = UA_STRING_ALLOC("urn:open62541.da.app");
		cc->clientDescription.applicationType = UA_APPLICATIONTYPE_CLIENT;
		cc->timeout = Drv_GetDeviceInfo(m_hDevice)->nRecvTimeout;
		UA_ByteString_clear(&certificate);
		UA_ByteString_clear(&privateKey);
	}
	else {
		UA_ClientConfig_setDefault(cc);
		cc->timeout = Drv_GetDeviceInfo(m_hDevice)->nRecvTimeout;
	}
    
    if (m_bFlagAsync) {
        /* Set stateCallback */
        cc->clientContext = (void *)this;
        cc->stateCallback = stateCallback;
    #ifdef UA_ENABLE_SUBSCRIPTIONS
        cc->subscriptionInactivityCallback = subscriptionInactivityCallback;
    #endif
    }
}

void COPCUADevice::connect()
{
    CVDEVICE* pDevice = Drv_GetDeviceInfo(m_hDevice);
	//��������
	if (m_mainUrl.length() == 0)
	{
		CV_ERROR(g_opcuaLog, -1, "The name of the main server is empty.");
		return;
	}
	CV_INFO(g_opcuaLog, "Try to connect to main server %s:%s", pDevice->pszName, m_mainUrl.c_str());
	if (m_pClientMain == NULL)
	{
		m_pClientMain = UA_Client_new();
		SetUAClientConfig(m_pClientMain);
	}
	long lRetMain = OPCUAClientConnect(m_pClientMain, (char*)m_mainUrl.c_str(), m_username, m_password);
	if (lRetMain != UA_STATUSCODE_GOOD)
	{
		Drv_UpdateDevStatus(m_hDevice, DEV_STATUS_BAD);
		CV_ERROR(g_opcuaLog, -1, "Connect to main server %s:%s failed [ret = 0x%x]", pDevice->pszName, m_mainUrl.c_str(), lRetMain);
	}
	else
	{
		Drv_UpdateDevStatus(m_hDevice, DEV_STATUS_GOOD);
		m_pActiveClient = m_pClientMain;
		CV_INFO(g_opcuaLog, "Connect to main server %s:%s succeeded", pDevice->pszName, m_mainUrl.c_str());
		return;
	}
	if (m_backUrl.length() != 0)
	{
		//���ӱ���
		if (m_pClientBack == NULL)
		{
			m_pClientBack = UA_Client_new();
			SetUAClientConfig(m_pClientBack);
		}
		CV_INFO(g_opcuaLog, "Try to connect to back server %s:%s", pDevice->pszName, m_backUrl.c_str());
		long lRetBack = OPCUAClientConnect(m_pClientBack, (char*)m_backUrl.c_str(), m_username, m_password);
		if (lRetBack != UA_STATUSCODE_GOOD)
		{
			Drv_UpdateDevStatus(m_hDevice, DEV_STATUS_BAD);
			CV_ERROR(g_opcuaLog, -1, "COPCUADevice::connect to back server %s:%s failed [ret = 0x%x]", pDevice->pszName, m_backUrl.c_str(), lRetBack);
		}
		else
		{
			Drv_UpdateDevStatus(m_hDevice, DEV_STATUS_GOOD);
			m_pActiveClient = m_pClientBack;
			CV_INFO(g_opcuaLog, "COPCUADevice::connect to back server %s:%s succeeded.", pDevice->pszName, m_backUrl.c_str());
			return;
		}
	}

	if (m_bFlagAsync)
	{
		ACE_Write_Guard<ACE_RW_Mutex> writeGuard(g_rwMutex);
		std::map<DRVHANDLE, TOPCUAData>* pMapDataBlockData = (std::map<DRVHANDLE, TOPCUAData>*)Drv_GetUserDataPtr(m_hDevice, USER_DATABLOCKDATA_INDEX);
		if (pMapDataBlockData != NULL)
		{
			std::map<DRVHANDLE, TOPCUAData>::iterator iterMap = pMapDataBlockData->begin();
			for (; iterMap != pMapDataBlockData->end(); iterMap ++)
			{
				iterMap->second.nQuality = DATA_STATUS_COMM_FAILURE;
			}
		}
	}
	//���������豸�����ɹ�����˽��������ݿ鶼��ΪBAD
	long lCount = Drv_GetDataBlockCount(m_hDevice);
	CVDATABLOCK* pDataBlock = new CVDATABLOCK[lCount];
	CVDATABLOCK* pDBIndex = pDataBlock;
	long nRet = Drv_GetDatablocks(m_hDevice, pDataBlock, lCount);
	for (int i = 0; i < lCount; i++)
	{
		DRVHANDLE hDataBlock = Drv_GetDataBlockByName(m_hDevice, pDBIndex->pszName);
		Drv_UpdateBlockStatus(m_hDevice, hDataBlock, DATA_STATUS_COMM_FAILURE);
		pDBIndex++;
	}
	delete[] pDataBlock;
	pDataBlock = NULL;
	pDBIndex = NULL;

}

void COPCUADevice::disconnect(bool bDelete)
{
    CVDEVICE* pDevice = Drv_GetDeviceInfo(m_hDevice);
	long lRet = OPCUAClientDisconnect(m_pActiveClient, bDelete);
	if (lRet == UA_STATUSCODE_GOOD)
	{
		CV_INFO(g_opcuaLog, "Client %s disconnected from server succeeded.", pDevice->pszName);
	}
	else
	{
        CV_WARN(g_opcuaLog, -1, "Client %s disconnected from server failed with return code 0x%x.", pDevice->pszName, lRet);
	}

	if (bDelete)
	{
		if (m_pActiveClient == m_pClientMain)			
			m_pClientMain = NULL;
		else if(m_pActiveClient == m_pClientBack)
			m_pClientBack = NULL;	
        m_pActiveClient = NULL;
	}
}

void COPCUADevice::disconnect(void* pClientPara, bool bDelete)
{
    CVDEVICE* pDevice = Drv_GetDeviceInfo(m_hDevice);
	long lRet = OPCUAClientDisconnect(pClientPara, bDelete);
	if (lRet == UA_STATUSCODE_GOOD)
	{
		CV_INFO(g_opcuaLog, "Client %s disconnect from server succeeded.", pDevice->pszName);
	}
	else
	{
		CV_INFO(g_opcuaLog, "Client %s disconnect from server failed with return code 0x%x.", pDevice->pszName, lRet);
	}

	if (bDelete)
	{
		pClientPara = NULL;
	}
}

long COPCUADevice::read()
{
    OnStartRead();      // must be called before connect()

    CVDEVICE* pDevice = Drv_GetDeviceInfo(m_hDevice);
	//�ӻ�����л�Ϊ�ǻ�������Ͽ���ǰ������
	int nCurRMStatus = Drv_IsActiveHost();
	if (nCurRMStatus != RM_STATUS_ACTIVE)
	{
		if(m_nRMStatus == RM_STATUS_ACTIVE && m_bSingle)
		{
            CV_WARN(g_opcuaLog, -1, "Disconnect device:%s because of switching to inactive state", pDevice->pszName);
			disconnect(m_pActiveClient,false);
			Drv_UpdateDevStatus(m_hDevice, DEV_STATUS_BAD);
			m_nRMStatus = nCurRMStatus;
			return DRV_SUCCESS;
		}
		else
		{
			if(!m_bSingle)
			{
				m_nRMStatus = nCurRMStatus;
				return DRV_SUCCESS;
			}
		}
		//else
			//m_nRMStatus = nCurRMStatus;
	}

	//�ӷǻ�����л�Ϊ�������������ͼ�������ӣ��ڳ�ʼ����ʱ�����ǲ�δ��������.
	if ((m_nRMStatus != RM_STATUS_ACTIVE && nCurRMStatus == RM_STATUS_ACTIVE) )
	{
		if (m_bSingle)
		{
			CV_INFO(g_opcuaLog, "Connect to device:%s because of switching to active state", pDevice->pszName);
			connect();
		}
		//m_nRMStatus = nCurRMStatus;
	}
	//�״̬�£�����豸δ����ҲҪ��������
	if(m_nRMStatus == RM_STATUS_ACTIVE && !m_pActiveClient)
	{
		//����ʮ����ѯ����δ���ӵ�״̬ҲҪ���жϿ�����
		int nCount = Drv_GetUserData(m_hDevice, USER_DEVICE_FAILED_NUMBER_INDEX);
		nCount++;
		if (nCount >= 10)
		{
			disconnect(true);
			CV_ERROR(g_opcuaLog, -1, "Disconnect device:%s because of continue m_pActiveClient = NULL, Count :%d", pDevice->pszName, nCount);
			Drv_SetUserData(m_hDevice, USER_DEVICE_FAILED_NUMBER_INDEX, 0);
		}
		else
		{
			Drv_SetUserData(m_hDevice, USER_DEVICE_FAILED_NUMBER_INDEX, nCount);
		}
        CV_INFO(g_opcuaLog, "Connect to device:%s because of no active connection", pDevice->pszName);
		connect();
	}
	//�ǻ״̬���ҵ����ӣ����ö�ֵ
	if (m_nRMStatus != RM_STATUS_ACTIVE && nCurRMStatus != RM_STATUS_ACTIVE && m_bSingle)
	{
		m_nRMStatus = nCurRMStatus;
		return DRV_SUCCESS;
	}

	m_nRMStatus = nCurRMStatus;

    long lRet;
	//��disconnect(true)֮������m_pActiveClient�ᱻ��Ϊnull�������ⲿ�������ӳɹ����������ﲻ�ڽ���
    if (m_pActiveClient) {
        if (!m_bFlagAsync) {
            lRet = readSync();
        }
        else {
            lRet = readAsync();
			DRVTIME drvTime; 
			Drv_GetCurrentTime(&drvTime);
			{
				ACE_Read_Guard<ACE_RW_Mutex> readGuard(g_rwMutex);
				std::map<DRVHANDLE, TOPCUAData>* pMapDataBlockData = (std::map<DRVHANDLE, TOPCUAData>*)Drv_GetUserDataPtr(m_hDevice, USER_DATABLOCKDATA_INDEX);
				if (pMapDataBlockData != NULL)
				{
					std::map<DRVHANDLE, TOPCUAData>::iterator iter = pMapDataBlockData->begin();
					for (; iter != pMapDataBlockData->end(); iter++)
					{
						if (iter->second.nQuality != DATA_STATUS_OK)
						{
							Drv_UpdateBlockStatus(m_hDevice, iter->first, iter->second.nQuality);
						}
						else
						{
							Drv_UpdateDevStatus(m_hDevice, DEV_STATUS_GOOD);
							CVDATABLOCK* pDataBlock = Drv_GetDataBlockInfo(iter->first);
							int nSize = pDataBlock->nBlockDataSize < UA_VALUE_LENGTH ? pDataBlock->nBlockDataSize : UA_VALUE_LENGTH;
							Drv_UpdateBlockData(m_hDevice, iter->first, (char*)(iter->second.pbuf), 0, nSize, 0, &drvTime);
						}
					}
				}
			}
        }

        if (lRet != UA_STATUSCODE_GOOD)
        {
			CV_WARN(g_opcuaLog, -1, "Read failed ,device name: %s, lRet :0x % x", pDevice->pszName, lRet);
            //��ȡ������������ʧ�ܴ���
            int nCount = Drv_GetUserData(m_hDevice, USER_DEVICE_FAILED_NUMBER_INDEX);
            nCount++;
            if (nCount >= 10)
            {
                disconnect(true);
                CV_ERROR(g_opcuaLog, -1, "Disconnect device:%s because of continue read failed, Count :%d", pDevice->pszName, nCount);
                Drv_SetUserData(m_hDevice, USER_DEVICE_FAILED_NUMBER_INDEX, 0);
            }
            else
            {
                Drv_SetUserData(m_hDevice, USER_DEVICE_FAILED_NUMBER_INDEX, nCount);
            }
            CV_ERROR(g_opcuaLog, -1, "Connect to device:%s because of reading failure, ErrCode:0x%x", pDevice->pszName, lRet);
            connect();
        }
        else
        {
            if (m_bMonitorFailed || m_bSubInactive) {
                CV_ERROR(g_opcuaLog, -1, "Reconnecting to device:%s because of monitoring items failed %d or subscription inactive %d", pDevice->pszName, m_bMonitorFailed, m_bSubInactive);
                disconnect(true);
                connect();
                m_bMonitorFailed = false;
                m_bSubInactive = false;
            } else {
                Drv_SetUserData(m_hDevice, USER_DEVICE_FAILED_NUMBER_INDEX, 0);
            }
        }
    }
	return lRet;
}


//��Ҫ��������ֵ���µ�dit��
long COPCUADevice::readSync()
{
    long lRet = DRV_SUCCESS;
	unsigned int nSize = m_strAddrMap.size(); 
	CV_DEBUG(g_opcuaLog, "m_strAddrMap size:%d", nSize);
	if(nSize > 0)
	{
		memset(m_pStrReadValue, 0x00, nSize * UA_VALUE_LENGTH);
		CV_DEBUG(g_opcuaLog, "Device[%s] Before OPCUAClientSyncRead",Drv_GetDeviceInfo(m_hDevice)->pszName);
		int64 nJobStartMs = os_get_curr_time_msec();
		lRet = OPCUAClientSyncRead(m_pActiveClient , m_pStrReadQuest, m_pStrReadValue, m_pStrReadHasValue, nSize, m_strRequestToAddrIndexMap);
		CV_DEBUG(g_opcuaLog, "Device[%s] After OPCUAClientSyncRead,cost time %d ms.",  Drv_GetDeviceInfo(m_hDevice)->pszName, os_get_curr_time_msec() - nJobStartMs);
		if ( lRet !=  UA_STATUSCODE_GOOD)
		{
			Drv_UpdateDevStatus(m_hDevice, DEV_STATUS_BAD);
			for(map<string, OPCUAADATABLOCK>::iterator iter = m_strAddrMap.begin(); iter != m_strAddrMap.end(); ++ iter)
			{
				DRVHANDLE hDataBlock = iter->second.hDataBlock;
				Drv_UpdateBlockStatus(m_hDevice, hDataBlock, DATA_STATUS_COMM_FAILURE);
			}
			CV_WARN(g_opcuaLog,-1, "COPCUADevice::read failed [ret=0x%x].", lRet);
		}
		else
		{
			Drv_UpdateDevStatus(m_hDevice, DEV_STATUS_GOOD);
			int i = 0;
			for(map<string, OPCUAADATABLOCK>::iterator iter = m_strAddrMap.begin(); iter != m_strAddrMap.end(); ++ iter)
			{
				DRVHANDLE hDataBlock = iter->second.hDataBlock;
				if(m_pStrReadHasValue[i])	
					UpdateData2Dit(hDataBlock ,m_pStrReadValue + i);	
				else
				{				
					Drv_UpdateBlockStatus(m_hDevice, hDataBlock, DATA_STATUS_COMM_FAILURE);
					CV_ERROR(g_opcuaLog, -1, "bad tag:%s",Drv_GetDataBlockInfo(hDataBlock)->pszName);
				}
				++ i;
			}		
		}
	}
	
	nSize = m_numAddrMap.size();
	CV_DEBUG(g_opcuaLog, "m_numAddrMap size:%d", nSize);
	if(nSize > 0)
	{
		memset(m_pNumReadValue, 0x00, nSize * UA_VALUE_LENGTH);
		lRet = OPCUAClientSyncRead(m_pActiveClient, m_pNumReadQuest, m_pNumReadValue, m_pNumReadHasValue, nSize, m_numRequestToAddrIndexMap);

		if ( lRet !=  UA_STATUSCODE_GOOD)
		{
			for(map<int, OPCUAADATABLOCK>::iterator iter = m_numAddrMap.begin(); iter != m_numAddrMap.end(); ++ iter)
			{
				DRVHANDLE hDataBlock = iter->second.hDataBlock;
				Drv_UpdateBlockStatus(m_hDevice, hDataBlock, DATA_STATUS_COMM_FAILURE);
			}
			CV_WARN(g_opcuaLog,-1, "COPCUADevice::read failed [ret=0x%x].", lRet);
		}
		else
		{
			int i = 0;
			for(map<int, OPCUAADATABLOCK>::iterator iter = m_numAddrMap.begin(); iter != m_numAddrMap.end(); ++ iter)
			{
				DRVHANDLE hDataBlock = iter->second.hDataBlock;
				if(m_pNumReadHasValue[i])
					UpdateData2Dit(hDataBlock ,m_pNumReadValue + i);
				else
				{
					Drv_UpdateBlockStatus(m_hDevice, hDataBlock, DATA_STATUS_COMM_FAILURE);
					CV_ERROR(g_opcuaLog, -1,"bad tag:%s",Drv_GetDataBlockInfo(hDataBlock)->pszName);
				}
				++ i;
			}		
		}
	}
	

	return lRet;
}

long COPCUADevice::readAsync()
{
    return UA_Client_run_iterate(m_pActiveClient, m_nAsyncTimeout);
}

long COPCUADevice::write(unsigned short nType, const int nID, unsigned short nNumber, const char* pName, void* pValue)
{
	int nCurRMStatus = Drv_IsActiveHost();
	if (nCurRMStatus != RM_STATUS_ACTIVE && !m_bSingle)
	{
		m_nRMStatus = nCurRMStatus;
		return DRV_SUCCESS;
	}

	long lRet;
    if(!m_bFlagAsync)
		lRet = writeSync(nType, nID, nNumber, pName, pValue);
	else
		lRet = writeAsync(nType, nID, nNumber, pName, pValue);
	return lRet;
}

long COPCUADevice::writeSync(unsigned short nType, const int nID, unsigned short nNumber, const char* pName, void* pValue)
{
	long lRet = UA_STATUSCODE_GOOD;
	if (m_pActiveClient == NULL)
		lRet = UA_STATUSCODE_BADCOMMUNICATIONERROR;
	else {
		CV_DEBUG(g_opcuaLog, "Device[%s] Before OPCUAClientSyncWrite", Drv_GetDeviceInfo(m_hDevice)->pszName);
		int64 nJobStartMs = os_get_curr_time_msec();
		lRet = OPCUAClientSyncWrite(m_pActiveClient, nType, nID, nNumber, pName, pValue);
		CV_DEBUG(g_opcuaLog, "Device[%s] After OPCUAClientSyncWrite,cost time %d ms.", Drv_GetDeviceInfo(m_hDevice)->pszName, os_get_curr_time_msec() - nJobStartMs);
	}
	return lRet;
}

long COPCUADevice::writeAsync(unsigned short nType, const int nID, unsigned short nNumber, const char* pName, void* pValue)
{
	return writeSync(nType, nID, nNumber, pName, pValue);
}

long COPCUADevice::AddData( DRVHANDLE hDevice, DRVHANDLE hDataBlock )
{
	m_bFlagComplete = false;
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
	//m_dataBlockMap.insert(std::make_pair(pDataBlock->pszAddress, hDataBlock));
	string strTagAddr = pDataBlock->pszAddress;
	OPCUAADATABLOCK uaBlock;
	unsigned int nsIndex;
	uaBlock.hDataBlock = hDataBlock;
	string strAddr;
	string strType;
	size_t nPos = strTagAddr.find('|');
	if(nPos != string::npos)
	{
		nsIndex = atoi(strTagAddr.substr(2, nPos).c_str());
		uaBlock.nNumber = nsIndex;
		strAddr = strTagAddr.substr(nPos + 1);
		size_t nPos2 = strAddr.find('|');
		if(nPos2 != string::npos)
		{
			strType = strAddr.substr(0, nPos2);
			strAddr = strAddr.substr(nPos2 +1);
			if(strType.compare(STRINGTYPE) == 0)
				m_strAddrMap.insert(make_pair(strAddr, uaBlock));
			else if(strType.compare(NUMERICTYPE) == 0)
			{
				int nID = 0;
				sscanf(strAddr.c_str(), "%d", &nID);
				m_numAddrMap.insert(make_pair(nID, uaBlock));
			}
			else
			{
				CV_WARN(g_opcuaLog,-1, "Tag Name:%s. Address:%s. Type %s is not supported.", pDataBlock->pszName, pDataBlock->pszAddress, strType.c_str());
			}
		}
	}
	return DRV_SUCCESS;
}

void COPCUADevice::OnStartRead()
{
	if(!m_bFlagComplete)
	{
		unsigned int nSize = m_strAddrMap.size(); 
		if(nSize > 0)
		{
			int i = 0;
			unsigned short* pNumber = new unsigned short[nSize];
			char (*pName)[UA_NAME_LENGTH] = new char[nSize][UA_NAME_LENGTH]; 

            int requestIndex = 0;
            int requestSize = 0;
            vector<NodeIdInfo> nodeidVec;
			for(map<string, OPCUAADATABLOCK>::iterator iter = m_strAddrMap.begin(); iter != m_strAddrMap.end(); ++iter)
			{
				string strTagAddr = iter->first;
                char pTemp[UA_NAME_LENGTH] = {0};
                strncpy(pTemp, strTagAddr.c_str(), UA_NAME_LENGTH);
                AddrIndex addrindex;
                addrindex.idx = i;
                addrindex.origname = strTagAddr;
                addrindex.hDataBlock = iter->second.hDataBlock;
                vector<int> indexVec;
                NodeIdInfo anodeid;
                anodeid.strAddr = pTemp;
                anodeid.ns = iter->second.nNumber;
                if (parseArrayIndex(pTemp, indexVec)) {
                    addrindex.arrIndices = indexVec;
                    char *p = strchr(pTemp, '[');
                    memset(pTemp, 0, UA_NAME_LENGTH);
                    strncpy(pTemp, strTagAddr.c_str(), p - pTemp);
                    anodeid.ns = iter->second.nNumber;
                    anodeid.strAddr = pTemp;
                    if (std::find(nodeidVec.begin(), nodeidVec.end(), anodeid) == nodeidVec.end()) {
                        nodeidVec.push_back(anodeid);
                        *(pNumber + requestIndex) = iter->second.nNumber;
                        strncpy(*(pName + requestIndex), pTemp, UA_NAME_LENGTH);
                        ++requestIndex;
                    }
                } else {
                    *(pNumber + requestIndex) = iter->second.nNumber;
                    strncpy(*(pName + requestIndex), strTagAddr.c_str(), UA_NAME_LENGTH);
                    ++requestIndex;
                }
                if (m_strRequestToAddrIndexMap.find(requestIndex - 1) != m_strRequestToAddrIndexMap.end()) {
                    m_strRequestToAddrIndexMap[requestIndex - 1].push_back(addrindex);
                } else {
                    vector<AddrIndex> addrIndexVec;
                    addrIndexVec.push_back(addrindex);
                    m_strRequestToAddrIndexMap.insert(make_pair(requestIndex - 1, addrIndexVec));
                }
                if (m_strAsyncAddrIndexMap.find(anodeid) != m_strAsyncAddrIndexMap.end()) {
                    m_strAsyncAddrIndexMap[anodeid].push_back(addrindex);
                } else {
                    vector<AddrIndex> addrIndexVec;
                    addrIndexVec.push_back(addrindex);
                    m_strAsyncAddrIndexMap.insert(make_pair(anodeid, addrIndexVec));
                }
                ++i;
			}
            requestSize = requestIndex;

            if (!m_bFlagAsync) {
                OPCUAGenerateStrReadRequest(&m_pStrReadQuest, requestSize, pNumber, pName);
            }

			if(pNumber)
			{
				delete []pNumber;
				pNumber = NULL;
			}
			if(pName)
			{
				delete []pName;
				pName = NULL;
			}

			m_pStrReadValue = new unsigned char[nSize][UA_VALUE_LENGTH];
			memset(m_pStrReadValue, 0x00, nSize * UA_VALUE_LENGTH);
			m_pStrReadHasValue = new bool[nSize];
			memset(m_pStrReadHasValue, 0x00, nSize);
		}
		
		//
		nSize = m_numAddrMap.size(); 
		if(nSize > 0)
		{
			int i = 0;
			unsigned short* pNumber = new unsigned short[nSize];
			int *pIDs = new int[nSize]; 

            int requestIndex = 0;
            int requestSize = 0;
			for(map<int, OPCUAADATABLOCK>::iterator iter = m_numAddrMap.begin(); iter != m_numAddrMap.end(); ++iter)
			{
				*(pNumber + i) = iter->second.nNumber;
				*(pIDs + i) = iter->first;
                ++requestIndex;
                AddrIndex addrindex;
                addrindex.idx = i;
                addrindex.hDataBlock = iter->second.hDataBlock;
                NodeIdInfo anodeid;
                anodeid.ns = iter->second.nNumber;
                anodeid.numAddr = iter->first;
                if (m_numRequestToAddrIndexMap.find(requestIndex - 1) != m_numRequestToAddrIndexMap.end()) {
                    m_numRequestToAddrIndexMap[requestIndex - 1].push_back(addrindex);
                } else {
                    vector<AddrIndex> addrIndexVec;
                    addrIndexVec.push_back(addrindex);
                    m_numRequestToAddrIndexMap.insert(make_pair(requestIndex - 1, addrIndexVec));
                }
                if (m_numAsyncAddrIndexMap.find(anodeid) != m_numAsyncAddrIndexMap.end()) {
                    m_numAsyncAddrIndexMap[anodeid].push_back(addrindex);
                } else {
                    vector<AddrIndex> addrIndexVec;
                    addrIndexVec.push_back(addrindex);
                    m_numAsyncAddrIndexMap.insert(make_pair(anodeid, addrIndexVec));
                }
				++i;
			}
            requestSize = requestIndex;

            if (!m_bFlagAsync) {
                OPCUAGenerateNumReadRequest(&m_pNumReadQuest, requestSize, pNumber, pIDs);
            }

			if(pNumber)
			{
				delete []pNumber;
				pNumber = NULL;
			}
			if(pIDs)
			{
				delete []pIDs;
				pIDs = NULL;
			}

			m_pNumReadValue = new unsigned char[nSize][UA_VALUE_LENGTH];
			memset(m_pNumReadValue, 0x00, nSize * UA_VALUE_LENGTH);
			m_pNumReadHasValue = new bool[nSize];
			memset(m_pNumReadHasValue, 0x00, nSize);
		}
		

		m_bFlagComplete = true;
	}
	
}

long COPCUADevice::UpdateData2Dit(DRVHANDLE hDataBlock, void* val)
{
	
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
	long lValueStatus = 0;
	DRVTIME drvTime;
	Drv_GetCurrentTime(&drvTime);
	Drv_UpdateBlockData(m_hDevice, hDataBlock, (char*)(val), 0, pDataBlock->nBlockDataSize, lValueStatus, &drvTime);
	return DRV_SUCCESS;
}

//void COPCUADevice::UpdateBad2Dit()
//{
//	for(map<string, OPCUAADATABLOCK>::iterator iter = m_strAddrMap.begin(); iter != m_strAddrMap.end(); ++ iter)
//	{
//		DRVHANDLE hDataBlock = iter->second.hDataBlock;
//		Drv_UpdateBlockStatus(m_hDevice, hDataBlock, DATA_STATUS_COMM_FAILURE);
//	}
//}
