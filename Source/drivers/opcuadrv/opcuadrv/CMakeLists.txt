cmake_minimum_required(VERSION 3.10)
PROJECT (opcuadrv)

INCLUDE($ENV{DRDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
#ADD_DEFINITIONS(-DSXPLAT_LINUX -DNOT_AMALGATED)
#Setting Source Files
SET(SRCS ${SRCS} opcua.cpp opcuadevice.cpp subcallback.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME opcuadrv)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS drdrivercommon intl iconv ACE drcomm open62541 hdOS)

SET(SPECOUTDIR /drivers/opcuadrv)

#windows下bool定义冲突，因此关闭open62541.h中的相关开关
IF(${CMAKE_SYSTEM_NAME} MATCHES Windows)
	option(__bool_true_false_are_defined "bool defined" ON)
	if(__bool_true_false_are_defined)
		add_definitions(-D__bool_true_false_are_defined)
	endif()
ENDIF(${CMAKE_SYSTEM_NAME} MATCHES Windows)

#windows下ace库ssize_t定义冲突，因此关闭open62541定义冲突，因此关闭open62541.h中的相关开关
IF(${CMAKE_SYSTEM_NAME} MATCHES Windows)
	option(_SSIZE_T_DEFINED "ssize_t defined" ON)
	if(_SSIZE_T_DEFINED)
		add_definitions(-D_SSIZE_T_DEFINED)
	endif()
ENDIF(${CMAKE_SYSTEM_NAME} MATCHES Windows)
INCLUDE($ENV{DRDIR}CMakeSpecOutPath)
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)
#SET(CMAKE_LIBRARY_PATH ${CMAKE_LIBRARY_PATH})


