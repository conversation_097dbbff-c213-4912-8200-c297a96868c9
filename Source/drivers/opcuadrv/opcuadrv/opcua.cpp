/************************************************************************
*	Filename:		opucua.cpp
*	Copyright:		Shanghai Baosight Company Software Co., Ltd.
*
*	Description:	opcua����
*
*	@author:		zhangqiang
*	@version		2016-01-20	zhangqiang	Initial Version
*************************************************************************/
#include <map>
#include "opcua.h"
#include "driversdk/cvdrivercommon.h"
#include "common/TypeCast.h"
#include <stdlib.h>
#include <stdio.h>
#include <string.h>
#include <string>
#include "ace/Default_Constants.h"
#include "ace/DLL.h"
#include "common/cvcomm.hxx"
#include "gettext/libintl.h"
#include "common/CommHelper.h"

#define _(STRING) gettext(STRING)
#define EC_ICV_INVALID_PARAMETER                    100
#define UA_STATUSCODE_GOOD 0x00000000
using namespace std;

 CCVLog g_opcuaLog;
 const char g_szDriverName[ICV_DRIVERNAME_MAXLEN] = "opcuadrv";

map<DRVHANDLE, COPCUADevice*> g_mapDevice;

/**
*  ��ȡ�����汾��.
*
*  @version   01/20/2016   Initial Version.
*/
CVDRIVER_EXPORTS long GetDrvFrameVersion()
{
	return 2;
}

CVDRIVER_EXPORTS long OnBatchUpdateData(int& nMsTimeOut,int& DataSize)
{
	int timeout = 50;
	int datasize = 10000;

	nMsTimeOut = timeout;
	DataSize = datasize;
	return 0; 
}

// CVDRIVER_EXPORTS long OnBatchUpdateData()
// {
// 
// 	return 0; 
// }

CVDRIVER_EXPORTS long Begin()
{
	g_opcuaLog.SetLogFileNameThread(g_szDriverName);
	return DRV_SUCCESS;
}

/**
 *  ��ʼ������������EXE����ʱ�ú��������ã����ڸú�����ʵ���Զ����ʼ������.
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     01/20/2016    Initial Version.
 */
CVDRIVER_EXPORTS long Initialize(DRVHANDLE hDriver)
{
    CV_INFO(g_opcuaLog, "%s is initializing", g_szDriverName);
	long lRet = 0;// LoadDllFunctions();
	return lRet;
}

/**
 *  ����EXE�˳�ʱ�ú���������.
 *  �ڸú����п����ͷ��Զ�����Դ���Ͽ��豸���ӵȲ���.
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     01/20/2016    Initial Version.
 */
CVDRIVER_EXPORTS long UnInitialize()
{
    CV_INFO(g_opcuaLog, "%s is uninitializing", g_szDriverName);
    g_opcuaLog.StopLogThread();
	return DRV_SUCCESS;
}

/**
 *  �豸��ʱ��ִ�к���.
 *  �ú�����Ҫ��Էǿ��豸�������豸ͨ��ֻ�ṩһ���Զ�ȡ�豸�µ��������ݿ����ϢЭ�飬�ú�����Ҫʵ�����¹��ܣ�
 *  1�����ݵķ��ͺͽ���
 *  2������ʵʱ���ݵ�ֵ������״̬��ʱ���
 *  3�����ڷ�tcpͨ��Э�飬ͨ����Ҫ�ڸýӿڼ������״̬
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     01/20/2016   Initial Version.
 */
CVDRIVER_EXPORTS long OnDeviceTimer(DRVHANDLE hDevice)
{
	return DRV_SUCCESS;
}

/**
 *  ��ʱ��ȡ���ݺ���.
 *  �����豸ͨ���ṩ��ȡָ�����ݿ����ϢЭ�飬�ú�����Ҫʵ�����¹��ܣ�
 *  1�����ݵķ��ͺͽ���
 *  2���������ݿ�ʵʱ���ݵ�ֵ������״̬��ʱ���
 *  3�����ڷ�tcpͨ��Э�飬ͨ����Ҫ�ڸýӿڼ������״̬
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *  @param  -[in]  DRVHANDLE hDataBlock: [���ݿ���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 */
CVDRIVER_EXPORTS long OnReadData(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	//���ڵ��豸��hDataBlock�ǿյ�
	// ����ս��ջ�������������ǰ������������û�����ü����ջ����ڻ�������Ӱ�챾������
	map<DRVHANDLE, COPCUADevice*>::iterator iterOPCUADevice = g_mapDevice.find(hDevice);
	if (iterOPCUADevice != g_mapDevice.end())
	{
		iterOPCUADevice->second->read();
	}
	return 0;

}
/**
 *  ���ݿ鶨ʱ��ִ�к���.
 *  �ú�����Ҫ��Կ��豸�������豸ͨ���ṩ��ȡָ�����ݿ����ϢЭ�飬�ú�����Ҫʵ�����¹��ܣ�
 *  1�����ݵķ��ͺͽ���
 *  2���������ݿ�ʵʱ���ݵ�ֵ������״̬��ʱ���
 *  3�����ڷ�tcpͨ��Э�飬ͨ����Ҫ�ڸýӿڼ������״̬
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *  @param  -[in]  DRVHANDLE hDataBlock: [���ݿ���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     01/20/2016   Shining Initial Version.
 *  @version	 3/18/2013  baoyuansong �����ѯ���������ڴ�й¶�����ݿ����������ʼ��Ϊgood������.
 *  @version	3/20/2013  baoyuansong  �޸��޷�ͬʱ��ȡ������豸���ݵ�����
 */
CVDRIVER_EXPORTS long OnDataBlockTimer(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{

	return DRV_SUCCESS;
}

/**
 *  ���п�������ʱ�ú���������.
 *  �ڸú����и��ݴ��ݵĲ��������豸�·��������
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *  @param	-[in]  DRVHANDLE hDataBlock : [���ݿ���]
 *  @param	-[in]  int nTagDataType : tag������
 *  @param  -[in]  int nTagByteOffset: [tag���ڿ��е��ֽ�ƫ����]
 *  @param  -[in]  int nTagBitOffset: [�ֽ��ڵ�λƫ����]
 *  @param	-[in]  char *szCmdData : [����ָ��]
 *  @param  -[in]  int nCmdDataLenBits: [�������ݳ���,��λ��bit]
 *
  *  @return : ����ִ�з������
 *
 *  @version     01/20/2016   Initial Version.
 */
CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDataBlock, int nTagByteOffset, int nTagBitOffset, char *szCmdData, int nCmdDataLenBits)
{
	map<DRVHANDLE, COPCUADevice*>::iterator iterDevice = g_mapDevice.find(hDevice);
	if (iterDevice != g_mapDevice.end())
	{
		CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
		if (!pDataBlock || !pDataBlock->pszAddress || !pDataBlock->pszParam1)
		{
			CV_ERROR(g_opcuaLog, -1 , "Failed to get datablock information");
			return EC_OPCUA_DATABLOCK_NOEXIST;
		}
		int nID = -1;
		string strTagAddr = pDataBlock->pszAddress;
		string strAddr;
		string strType;
		unsigned short nsIndex;
		size_t nPos = strTagAddr.find('|');
		if(nPos != string::npos)
		{
			nsIndex = atoi(strTagAddr.substr(2, nPos).c_str());
			strAddr = strTagAddr.substr(nPos + 1);
			size_t nPos2 = strAddr.find('|');
			if(nPos2 != string::npos)
			{
				strType = strAddr.substr(0, nPos2);
				strAddr = strAddr.substr(nPos2 +1);
				if(strType.compare(STRINGTYPE) == 0)
					;
				else if(strType.compare(NUMERICTYPE) == 0)
				{
					sscanf(strAddr.c_str(), "%d", &nID);
				}
				else
				{
					CV_WARN(g_opcuaLog, -1,"Type %s is not supported.",strType.c_str());
				}
				
			}
		}

		
		int nType;
		string strDataType(pDataBlock->pszParam1);
		if(strcmp(strDataType.c_str(),"Boolean") == 0)
		{
			nType = UA_TYPES_BOOLEAN;
		}
		else if(strcmp(strDataType.c_str(),"Char") == 0)
		{
			nType = UA_TYPES_SBYTE;
		}
		else if(strcmp(strDataType.c_str(),"Byte") == 0)
		{
			nType = UA_TYPES_BYTE;
		}
		else if(strcmp(strDataType.c_str(),"Short")==0)
		{
			nType = UA_TYPES_INT16;	
		}
		else if(strcmp(strDataType.c_str(),"Word")==0)
		{
			nType = UA_TYPES_UINT16;
		}
		else if(strcmp(strDataType.c_str(),"Long")==0)
		{
			nType = UA_TYPES_INT32;
		}
		else if(strcmp(strDataType.c_str(),"DWord")==0)
		{
			nType = UA_TYPES_UINT32;
		}
		else if(strcmp(strDataType.c_str(),"Float")==0)
		{
			nType = UA_TYPES_FLOAT;
		}
		else if(strcmp(strDataType.c_str(),"Double")==0)
		{
			nType = UA_TYPES_DOUBLE;
		}
		else if (strcmp(strDataType.c_str(),"String")==0)
		{
			nType = UA_TYPES_STRING;
		}
		else if (strcmp(strDataType.c_str(), "Int64") == 0)
		{
			nType = UA_TYPES_INT64;
		}
		else if (strcmp(strDataType.c_str(), "UInt64") == 0)
		{
			nType = UA_TYPES_UINT64;
		}
		else
		{
			CV_ERROR(g_opcuaLog, -1 , "wrong data type %s cannot convert to icv standard data", strDataType.c_str());
			return EC_OPCUA_DATATYPE_NOEXIST;
		}
		CV_INFO(g_opcuaLog,"CTRL:Device[%s] write Tag Addr[%s]",Drv_GetDeviceInfo(hDevice)->pszName, strAddr.c_str());
		long lRet = iterDevice->second->write(nType, nID, nsIndex, strAddr.c_str(), szCmdData);
		if(lRet != UA_STATUSCODE_GOOD)
		{
			CV_ERROR(g_opcuaLog, -1, "Write occur error, result %d .",lRet);
		}
		else
		{
			CV_INFO(g_opcuaLog, "Write result %d .",lRet);
		}
		return lRet;
	}
	else
	{
		
		CV_ERROR(g_opcuaLog, -1 , "device not found");
		return EC_OPCUA_DEVICE_NOEXIST;
	}
}

/**
 *  �����豸ʱ�ú���������.
 *  �ú�����Ҫ��Է�tcp�����豸���û�����ͨ���豸�����ȡ�豸���Ӳ�������ʼ�������豸
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     01/20/2016   Initial Version.
 *  @version	3/20/2013  baoyuansong  �޸Ķ���.
 */
CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
	//��ʼ������badconnection����
	Drv_SetUserData(hDevice, 1, 0);

	//��map���첽��ֵʹ��
	std::map<DRVHANDLE, TOPCUAData>* pMapDataBlockData = new std::map<DRVHANDLE, TOPCUAData>;//keyΪ���ݿ�����valueΪ����������ֵ
	Drv_SetUserDataPtr(hDevice, USER_DATABLOCKDATA_INDEX, (void*)pMapDataBlockData);
	return AddDevice(hDevice);
}

/**
 *  ɾ���豸ʱ�ú���������.
 *  �ú�����Ҫ��Է�tcp�����豸���û�����ͨ���豸�����ȡ�豸��Ϣ�������Ͽ��豸���ͷ������Դ�Ȳ���
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     01/20/2016   Initial Version.
 */
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
	Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
	map<DRVHANDLE, COPCUADevice*>::iterator iterDevice = g_mapDevice.find(hDevice);
	CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
	if (iterDevice != g_mapDevice.end())
	{
		iterDevice->second->disconnect(true);
        CV_INFO(g_opcuaLog, "device: %s parameters: %s is deleted", 
            pDevice->pszName, pDevice->pszConnParam);
		delete iterDevice->second;
		g_mapDevice.erase(iterDevice);
	}

	std::map<DRVHANDLE, TOPCUAData>* pMapDataBlockData = (std::map<DRVHANDLE, TOPCUAData>*)Drv_GetUserDataPtr(hDevice, USER_DATABLOCKDATA_INDEX);
	if (pMapDataBlockData != NULL)
	{
		pMapDataBlockData->clear();
		SAFE_DELETE(pMapDataBlockData);
	}

	return DRV_SUCCESS;
}


/**
 *  �������ݿ�ʱ�ú���������.
 *  �û���Ҫ�ڸú����з������ݿ��С
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *  @param  -[in]  DRVHANDLE hDataBlock: [���ݿ���]
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     01/20/2016   Initial Version.
 */
CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	// ��ȡtag��
	long lRet = DRV_SUCCESS;
	map<DRVHANDLE, COPCUADevice*>::iterator iterOPCUADevice = g_mapDevice.find(hDevice);
	// ����ȵ���OnDataBlockAdd���ٵ���OnDeviceAdd����˵�һ��DataBlock��Ҫ����Device
	if (iterOPCUADevice == g_mapDevice.end())
	{
		AddDevice(hDevice);
	}

	iterOPCUADevice = g_mapDevice.find(hDevice);
	if (iterOPCUADevice != g_mapDevice.end())
	{
		COPCUADevice *pOPCUADevice = iterOPCUADevice->second;
		CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
		if (!pDataBlock || !pDataBlock->pszAddress)
		{
			CV_ERROR(g_opcuaLog,-1, "failed to get datablock information");
			return EC_OPCUA_DATABLOCK_NOEXIST;
		}

		lRet = pOPCUADevice->AddData(hDevice, hDataBlock);
        CVDEVICE* pDevice = Drv_GetDeviceInfo(hDevice);
        CV_INFO(g_opcuaLog, "device: %s parameters: %s add datablock: %s", 		
            pDevice->pszName, pDevice->pszConnParam, pDataBlock->pszName);
	}
	else
	{
		lRet = EC_OPCUA_DEVICE_NOEXIST;
	}

	return lRet;
}

/**
 *  ɾ�����ݿ�ʱ�ú���������.
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *  @param  -[in]  DRVHANDLE hDataBlock: [���ݿ���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     01/20/2016   Initial Version.
 */
CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	return DRV_SUCCESS;
}

/**
 *  ɾ�����ݿ�ʱ�ú���������.
 *  @param  -[in]  DRVHANDLE hDevice: [�豸���]
 *  @param  -[in]  DRVHANDLE hDataBlock: [���ݿ���]
 *
 *  @return DRV_SUCCESS: ִ�гɹ�
 *
 *  @version     01/20/2016   Initial Version.
 */
CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum,
	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
{
	if (NULL == pOutDevTags || NULL == pnTagsNum || \
		NULL == pTagGrps || NULL == pnTagGrpsNum)
		return EC_ICV_INVALID_PARAMETER;

	memset(pTagGrps, 0, *pnTagGrpsNum * sizeof(TagGroupInfo));
	//���ڵ��豸��һ�������һ�����ݿ飬��˵�ĸ��������ݿ�ĸ���һ��
	// 2023.06.05 Ϊ�˽����ͬ������ͬ��ַ��ֵ�����⣬������ʱ����Щ����Ϊһ�����ݿ�
	//*pnTagsNum = nTagsNum;
	//*pnTagGrpsNum = nTagsNum;
	*pnTagsNum = nTagsNum;
	*pnTagGrpsNum = 0;
	int nBlockNum = 0;

	map<string, string> mapDataBlockAddr;//keyΪ��ַ,valueΪGroupName
	std::copy(pDevTags, pDevTags + nTagsNum, pOutDevTags);


	char szGroupName[ICV_DATABLOCKNAME_MAXLEN + 1];
	memset(szGroupName, 0, sizeof(szGroupName));

	for (int i = 0; i < nTagsNum; ++i)
	{
		string strAddress = GetPureAddress(pDevTags[i].szAddress);
		map<string, string>::iterator iter = mapDataBlockAddr.find(strAddress);
		if (iter != mapDataBlockAddr.end())
		{
			cvcommon::Safe_StrNCopy(pOutDevTags[i].szGrpName, iter->second.c_str(), ICV_DATABLOCKNAME_MAXLEN + 1);
			continue;
		}

		cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szAddress, GetPureAddress(pDevTags[i].szAddress), ICV_IOADDR_MAXLEN);
		pTagGrps[nBlockNum].nElemBits = 8;
		//icg�ǵ��豸�����Կ��ϲ�����ɨ�����ڣ����������ظ�ɨ�裬����ϵͳCPU
		pTagGrps[nBlockNum].nPollRate = 0;
		sprintf(pTagGrps[nBlockNum].szGroupName, "group%d", nBlockNum);
		sprintf(pOutDevTags[i].szGrpName, "group%d", nBlockNum);
		mapDataBlockAddr.insert(make_pair(pTagGrps[nBlockNum].szAddress, pTagGrps[nBlockNum].szGroupName));

		switch(pDevTags[i].nDataType)
		{
		case TAG_DATATYPE_BOOL:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Boolean", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 1;
			break;
			//无符号1字节
		case TAG_DATATYPE_BYTE:
		case TAG_DATATYPE_USINT:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Byte", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 1;
			break;
			//有符号1字节
		case TAG_DATATYPE_CHAR:
		case TAG_DATATYPE_SINT:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Char", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 1;
			break;
		case TAG_DATATYPE_WORD:
		case TAG_DATATYPE_UINT:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Word", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 2;
			break;
		case TAG_DATATYPE_INT:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Short", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 2;
			break;
		case TAG_DATATYPE_TIME://暂定
		case TAG_DATATYPE_DWORD:
		case TAG_DATATYPE_UDINT:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "DWord", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 4;
			break;
		case TAG_DATATYPE_DINT:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Long", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 4;
			break;
		case TAG_DATATYPE_REAL:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Float", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 4;
			break;
		case TAG_DATATYPE_LINT:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Int64", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 8;
			break;
		case TAG_DATATYPE_LWORD:
		case TAG_DATATYPE_ULINT:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "UInt64", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 8;
			break;
		case TAG_DATATYPE_LREAL:
			cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "Double", ICV_EXTEND_PARAM_LEN);
			pTagGrps[nBlockNum].nElemNum = 8;
			break;
		case TAG_DATATYPE_BLOB:
			{
				cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "blob", ICV_EXTEND_PARAM_LEN);
				pTagGrps[nBlockNum].nElemNum = 256;
				const char *pTemp = strstr(pDevTags[i].szAddress, "#");
				if (pTemp != NULL)
				{
					pTagGrps[nBlockNum].nElemNum = atoi(pTemp + 1);
				}
			}
			break;
		case TAG_DATATYPE_STRING:
			{
				//cvcommon::Safe_StrNCopy(pTagGrps[i].szParam1, "string", ICV_EXTEND_PARAM_LEN);
				cvcommon::Safe_StrNCopy(pTagGrps[nBlockNum].szParam1, "String", ICV_EXTEND_PARAM_LEN);
				pTagGrps[nBlockNum].nElemNum = 256;
				const char *pTemp = strstr(pDevTags[i].szAddress, "#");
				if (pTemp != NULL)
				{
					pTagGrps[nBlockNum].nElemNum = atoi(pTemp + 1);
				}
			}
			break;
		default:
			break;
		}
		nBlockNum++;
	}
	*pnTagGrpsNum = nBlockNum;
	return DRV_SUCCESS;
}


long AddDevice(DRVHANDLE hDevice)
{
	map<DRVHANDLE, COPCUADevice*>::iterator iterDevice = g_mapDevice.find(hDevice);
	if (iterDevice == g_mapDevice.end())
	{
		CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
		if (!pDevice || !pDevice->pszConnParam)
		{
			CV_ERROR(g_opcuaLog, -1 , "failed to get device information");
			return EC_OPCUA_DEVICE_NOEXIST;
		}

		COPCUADevice *pOPCUADevice = new COPCUADevice(hDevice, pDevice);
		g_mapDevice.insert(make_pair(hDevice, pOPCUADevice));
        CV_INFO(g_opcuaLog, "device: %s parameters: %s is added", pDevice->pszName, pDevice->pszConnParam);
	}

	return DRV_SUCCESS;
}

const char * GetPureAddress(const char *pAddr)
{
	static char szAddress[ICV_IOADDR_MAXLEN + 1];
	memset(szAddress, 0, sizeof(szAddress));
	const char *pTemp = strstr(pAddr, ":");
	if (pTemp != NULL)
	{
		const char *pTemp1 = strstr(pTemp + 1, ":");
		if (NULL == pTemp1)
			pTemp1 = strstr(pTemp + 1, "#");

		if (pTemp1 != NULL)
		{
			if(pTemp1 - pTemp - 1 < ICV_IOADDR_MAXLEN)
				memcpy(szAddress, pTemp + 1, pTemp1 - pTemp - 1);
			else
				cvcommon::Safe_StrNCopy(szAddress, pTemp + 1, ICV_IOADDR_MAXLEN);
		}
		else
			cvcommon::Safe_StrNCopy(szAddress, pTemp + 1, ICV_IOADDR_MAXLEN);
	}
	return szAddress;
}



void FromCstring(string strContent, unsigned char *pContent, int &iID, bool bHex)
{
	long lSize = strContent.length();
	if (lSize < 2)
	{
		pContent[iID] = '\0'; 
		return;
	}

	string strTemp = strContent.substr(0, 2);
	int nID = 0;
	if (!bHex)
	{
		sscanf(strTemp.c_str(), "%x", &nID);
	}
	else
	{
		sscanf(strTemp.c_str(), "%d", &nID);
	}
	pContent[iID] = (char)nID;
	iID++;
	strTemp = strContent.substr(2);
	FromCstring(strTemp, pContent, iID, bHex);
}


