#ifndef _MELSEC_H_
#define _MELSEC_H_

#define		DEFAULT_REQUEST_MAXLEN			256
#define		DEFULT_READ_REQUEST_MAXLEN		32
#define		DEFAULT_RESPONSE_MAXLEN			4096
#define		BITS_PER_BYTE					8
#define		MC_DEVICE_QL_SERIES				"Q/L"
#define		MC_DEVICE_IQR_SERIES			"iQ-R"
#define		SLMP_DEVICE_IQR_SERIES			"iQ-R(SLMP)"
#define		MC_3E_HEAD_MESSAGE					0xd0
#define		MC_4E_HEAD_MESSAGE					0xd4
#define		RECVFAILEDCOUNT					3	
#endif //_MELSEC_H_ 
