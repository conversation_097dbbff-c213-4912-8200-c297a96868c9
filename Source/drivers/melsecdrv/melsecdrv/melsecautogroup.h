/************************************************************************
*	Filename:		melsecautogroup.h
*	Copyright:		Shanghai Baosight Company Software Co., Ltd.
*
*	Description:	三菱plc驱动自组块的类
*
*	@author:		zhangqiang
*	@version		2016-03-01	zhangqiang	Initial Version
*************************************************************************/
#ifndef MELSEC_AUTOGROUP_H
#define MELSEC_AUTOGROUP_H
#include <vector>
#include <algorithm>
#include <string>
#include "driversdk/cvdrivercommon.h"

using namespace std;
#define BLOCK_TYPE_SIZE						4
#define		MC_PLC_WORD						"Word"
#define		MC_PLC_BIT						"Bit"
typedef struct _TagInfoWrapper
{
	TagInfo tagInfo;
	char szPureStartAddr[ICV_IOADDR_MAXLEN + 1];//起始地址
	int nStartByteAddr;//字节为单位
	int nEndByteAddr;
	int nBitOffset;
	int nDeviceCode;
	char szType[BLOCK_TYPE_SIZE];
}TagInfoWrapper;

typedef std::vector<TagInfo> TagInfoVector;
typedef std::vector<TagInfoWrapper> TagInfoWrapperVector;
typedef std::vector<TagGroupInfo> TagGroupInfoVector;

class CMelsecAutoGroupBuilder
{
public:
	CMelsecAutoGroupBuilder(const TagInfo *pDevTags, int nTagNum);
	virtual ~CMelsecAutoGroupBuilder(void);
	void GroupTags(TagInfoVector &vecDevTags, TagGroupInfoVector &vecTagGrpInfo );
protected:
	void GetTagWrapperInfo(const TagInfo &tagInfo, TagInfoWrapper &modTagInfo);
	const char *GetStartByteAddress(const char *pAddr);
	const int GetTagBitOffset(const char *pAddr);
	const char *GetBlockType(const char *pAddr);
	const int GetAddrNumber(const char *pAddr);
	const char *GenerateGroupName();
	unsigned char GetParameter1(const string &strSymbol);
	const char* GetGroupType(const string &strSymbol);
	unsigned int GetStartHexAddress(const string& strSymbol, const char* szAddress);
private:
	static unsigned int m_nGrpNum;
	TagInfoWrapperVector m_vecTags;
};
#endif

