/************************************************************************
*	Filename:		melsecautogroup.cpp
*	Copyright:		Shanghai Baosight Company Software Co., Ltd.
*
*	Description:	三菱plc驱动自组块的类，点的格式为D,100，10，其中D为寄存器类型，100为起始地址，
					（根据寄存器类型采用10进制或16进制），10为字节长度。
*
*	@author:		zhangqiang
*	@version		2016-03-01	zhangqiang	Initial Version
*************************************************************************/
#include "melsecautogroup.h"
#include <string.h>
#include <string>
#include <cstdio>

using namespace std;

const int MAX_DB_BLOCKS_BYTE_LEN = 960;

struct TagInfoWrapperCompFuctor
{
	bool operator()(const TagInfoWrapper &left, const TagInfoWrapper &right)
	{
		if (left.nDeviceCode == right.nDeviceCode)//考虑寄存器类型，再考虑起始地址
		{
			if (left.nStartByteAddr < right.nStartByteAddr)
				return true;
			else 
			{
				if (left.nStartByteAddr == right.nStartByteAddr)
				{
					if (left.nEndByteAddr < right.nEndByteAddr)
						return true;
				}
			}
			return false;
		}
		else
			return left.nDeviceCode < right.nDeviceCode;
	}
};

unsigned int CMelsecAutoGroupBuilder::m_nGrpNum = 0;
CMelsecAutoGroupBuilder::CMelsecAutoGroupBuilder( const TagInfo *pDevTags, int nTagNum )
{
	m_nGrpNum = 0;
	TagInfoWrapper tagInfowp;
	for (int i = 0; i < nTagNum && pDevTags != NULL; i++)
	{
		memset(&tagInfowp, 0, sizeof(TagInfoWrapper));
		GetTagWrapperInfo(pDevTags[i], tagInfowp);
		m_vecTags.push_back(tagInfowp);
	}
}

CMelsecAutoGroupBuilder::~CMelsecAutoGroupBuilder(void)
{

}

/**
 *  $(Desp) 对tag点进行分组.
 *  $(Detail) .
 *
 *  @param		-[out]  TagInfoVector & vecDevTags : 存放tag点信息，该信息包括分组信息，框架传入的信息不含分组
 *  @param		-[out]  TagGroupInfoVector & vecTagGrpInfo : 存放所有tag点的分组信息，通过tag点的组名，可查询该tag点所在组
 *  @return		void.
 *
 *  @version	3/3/2016  zhangqiang  Initial Version.
 */
void CMelsecAutoGroupBuilder::GroupTags( TagInfoVector &vecDevTags, TagGroupInfoVector &vecTagGrpInfo )
{
	//按照先块号，后地址的方式排好序
	std::sort(m_vecTags.begin(), m_vecTags.end(), TagInfoWrapperCompFuctor());

	TagGroupInfo tagGrpInfo;
	memset(&tagGrpInfo, 0, sizeof(TagGroupInfo));
	
	int nCurrDeviceCode = 0;
	bool bFisrt = true;

	for (TagInfoWrapperVector::iterator iter = m_vecTags.begin(); iter != m_vecTags.end(); ++iter)
	{
		if (bFisrt/*iter == m_vecTags.begin()*/)
		{
			bFisrt = false;
			strncpy(tagGrpInfo.szAddress, iter->szPureStartAddr, ICV_IOADDR_MAXLEN);
			strncpy(tagGrpInfo.szGroupName, GenerateGroupName(), ICV_DATABLOCKNAME_MAXLEN);
			tagGrpInfo.nPollRate = iter->tagInfo.nPollRate;
			sprintf(tagGrpInfo.szParam1, "%x", iter->nDeviceCode);
			nCurrDeviceCode = iter->nDeviceCode;
			const char* szGroupType = GetGroupType(iter->szType);
			strcpy(tagGrpInfo.szGroupType, szGroupType);
			if(strcmp(szGroupType, MC_PLC_WORD) == 0)
			{
				tagGrpInfo.nElemBits = 16;//2字节
			}
			else
				tagGrpInfo.nElemBits = 1;//0.1字节
		}

		if (nCurrDeviceCode == iter->nDeviceCode)
		{
			unsigned int nStartByteAddr = 0;
			sscanf(tagGrpInfo.szAddress, "%x", & nStartByteAddr);
			int nDiff = iter->nEndByteAddr - nStartByteAddr;
			if (nDiff < MAX_DB_BLOCKS_BYTE_LEN)//字节偏移量
			{
				tagGrpInfo.nElemNum = std::max((int)tagGrpInfo.nElemNum, nDiff + 1);
			}
			else
			{
				vecTagGrpInfo.push_back(tagGrpInfo);
				//大于一次采集的字节数，生成一个新的组
				memset(&tagGrpInfo, 0, sizeof(TagGroupInfo));
				strncpy(tagGrpInfo.szGroupName, GenerateGroupName(), ICV_DATABLOCKNAME_MAXLEN);
				strncpy(tagGrpInfo.szAddress, iter->szPureStartAddr, ICV_IOADDR_MAXLEN);
				unsigned int nStartByteAddr = 0;
				sscanf(tagGrpInfo.szAddress, "%x", & nStartByteAddr);
				tagGrpInfo.nElemNum = iter->nEndByteAddr - nStartByteAddr + 1;
				tagGrpInfo.nPollRate = iter->tagInfo.nPollRate;
				sprintf(tagGrpInfo.szParam1, "%x", iter->nDeviceCode);
				const char* szGroupType = GetGroupType(iter->szType);
				strcpy(tagGrpInfo.szGroupType, szGroupType);
				if(strcmp(szGroupType, MC_PLC_WORD) == 0)
				{
					tagGrpInfo.nElemBits = 16;//2字节
				}
				else
					tagGrpInfo.nElemBits = 1;//0.1字节
			}
		}
		else
		{
			vecTagGrpInfo.push_back(tagGrpInfo);
			//不同的寄存器类型，生成一个新的组
			memset(&tagGrpInfo, 0, sizeof(TagGroupInfo));
			strncpy(tagGrpInfo.szGroupName, GenerateGroupName(), ICV_DATABLOCKNAME_MAXLEN);
			strncpy(tagGrpInfo.szAddress, iter->szPureStartAddr, ICV_IOADDR_MAXLEN);
			unsigned int nStartByteAddr = 0;
			sscanf(tagGrpInfo.szAddress, "%x", & nStartByteAddr);
			tagGrpInfo.nElemNum = iter->nEndByteAddr - nStartByteAddr + 1;
			tagGrpInfo.nPollRate = iter->tagInfo.nPollRate;
			sprintf(tagGrpInfo.szParam1, "%x", iter->nDeviceCode);
			nCurrDeviceCode = iter->nDeviceCode;
			const char* szGroupType = GetGroupType(iter->szType);
			strcpy(tagGrpInfo.szGroupType, szGroupType);
			if(strcmp(szGroupType, MC_PLC_WORD) == 0)
			{
				tagGrpInfo.nElemBits = 16;//2字节
			}
			else
				tagGrpInfo.nElemBits = 1;//0.1字节
		}

		strncpy(iter->tagInfo.szGrpName, tagGrpInfo.szGroupName, ICV_DATABLOCKNAME_MAXLEN);
		unsigned int nStartByteAddr = 0;
		sscanf(tagGrpInfo.szAddress, "%x", & nStartByteAddr);
		iter->tagInfo.nBitOffSet = (iter->nStartByteAddr - nStartByteAddr) * tagGrpInfo.nElemBits;
		iter->tagInfo.nBitOffSet += iter->nBitOffset;//加上当前字节内偏移
		vecDevTags.push_back(iter->tagInfo);
	}

	if (!m_vecTags.empty())
	{
		vecTagGrpInfo.push_back(tagGrpInfo);
	}
}


void CMelsecAutoGroupBuilder::GetTagWrapperInfo(const TagInfo &tagInfo, TagInfoWrapper &tagInfoWrapper)
{
	memset(&tagInfoWrapper, 0, sizeof(TagInfoWrapper));
	memcpy(&tagInfoWrapper.tagInfo, &tagInfo, sizeof(TagInfo));
	std::string strType = "";
	const char *pType = GetBlockType(tagInfo.szAddress);
	if (NULL != pType)
	{
		strType = pType;
		transform(strType.begin(), strType.end(), strType.begin(), (int (*)(int))toupper);
		tagInfoWrapper.nDeviceCode = GetParameter1(strType.c_str());
	}
	strncpy(tagInfoWrapper.szType, strType.c_str(), BLOCK_TYPE_SIZE);
	const char *pTagAddr = GetStartByteAddress(tagInfo.szAddress);
	if (NULL != pTagAddr)
		strcpy(tagInfoWrapper.szPureStartAddr, pTagAddr);
	tagInfoWrapper.nBitOffset = GetTagBitOffset(tagInfo.szAddress);//计算所在起始地址的位偏移量

	tagInfoWrapper.nStartByteAddr = GetStartHexAddress(tagInfoWrapper.szType, tagInfoWrapper.szPureStartAddr);//atoi(tagInfoWrapper.szPureStartAddr);//atoi已将起始地址取整(1.x->1),应该针对不同的类型选择10进制还是16进制
	sprintf(tagInfoWrapper.szPureStartAddr, "%x", tagInfoWrapper.nStartByteAddr);
	//tagInfoWrapper.nDBNo = GetTagBelongDBNo(tagInfo.szAddress);

	
	switch(tagInfo.nDataType)
	{
		//1字节以内，起始地址和终止地址一致
	case TAG_DATATYPE_BOOL:
		tagInfoWrapper.nEndByteAddr = tagInfoWrapper.nStartByteAddr;
		break;
		//1字节，起始地址和终止地址一致
	case TAG_DATATYPE_BYTE:
	case TAG_DATATYPE_CHAR:
	case TAG_DATATYPE_SINT:
	case TAG_DATATYPE_USINT:
		tagInfoWrapper.nEndByteAddr = tagInfoWrapper.nStartByteAddr;
		break;
		//2字节，起始地址+1
	case TAG_DATATYPE_INT:
	case TAG_DATATYPE_UINT:
	case TAG_DATATYPE_WORD:
		tagInfoWrapper.nEndByteAddr = tagInfoWrapper.nStartByteAddr + 1;
		break;
		//4字节，起始地址+3
	case TAG_DATATYPE_REAL:
	case TAG_DATATYPE_TIME:
	case TAG_DATATYPE_DINT:
	case TAG_DATATYPE_UDINT:
	case TAG_DATATYPE_DWORD:
		tagInfoWrapper.nEndByteAddr = tagInfoWrapper.nStartByteAddr + 3;
		break;
		//8字节，起始地址+7
	case TAG_DATATYPE_LREAL:
	case TAG_DATATYPE_LINT:
	case TAG_DATATYPE_ULINT:
	case TAG_DATATYPE_LWORD:
		tagInfoWrapper.nEndByteAddr = tagInfoWrapper.nStartByteAddr + 7;
		break;
		//blob和文本，以Tag配置地址中的长度为准
	case TAG_DATATYPE_BLOB:
	case TAG_DATATYPE_STRING:
		{
			int nLen = GetAddrNumber(tagInfo.szAddress);
			if (nLen > 0)
				tagInfoWrapper.nEndByteAddr = tagInfoWrapper.nStartByteAddr + nLen - 1;
			else
				tagInfoWrapper.nEndByteAddr = tagInfoWrapper.nStartByteAddr;
		}
		break;
	default:
		break;
	}
}


//获取字节起始地址
const char * CMelsecAutoGroupBuilder::GetStartByteAddress(const char *pAddr)
{
	static string strAddress = "";
	strAddress = "";

	string strTemp = pAddr;
	const char *pType = GetBlockType(strTemp.c_str());
	if (!pType || strlen(pType) <= 0)
		return NULL;	
	int nTypeLen = strlen(pType);

	string::size_type nIndex = strTemp.find(":");//去除device前缀
	if(nIndex != std::string::npos)
		strTemp = strTemp.substr(nIndex + 1);
	else
		return strAddress.c_str();

	//获取起始地址
	std::string strType = pType;
	transform(strType.begin(), strType.end(), strType.begin(), (int (*)(int))toupper);
	strTemp = strTemp.substr(0, nIndex);
	strTemp = strTemp.substr(strlen(pType));

	nIndex = strTemp.find(".");
	if(nIndex != std::string::npos)//<index>.<bitnr>情况
	{
		strTemp = strTemp.substr(0, nIndex);
	}	

	strAddress = strTemp;

	return strAddress.c_str();
}


//获取<index>.<bitnr>的<bitnr>值
const int CMelsecAutoGroupBuilder::GetTagBitOffset(const char *pAddr)
{
	static int nBitOffset = 0;
	nBitOffset = 0;

	string strTemp = pAddr;
	const char *pType = GetBlockType(strTemp.c_str());
	if (!pType || strlen(pType) <= 0)
		return nBitOffset;

	string::size_type nIndex = strTemp.find(":");//去除device前缀
	if(nIndex != std::string::npos)
		strTemp = strTemp.substr(nIndex + 1);
	else
		return nBitOffset;

	nIndex = strTemp.find(".");
	if (nIndex != std::string::npos)
	{
		strTemp = strTemp.substr(nIndex+1);
		nBitOffset = atoi(strTemp.c_str());
	}

	return nBitOffset;
}

//获取DB或DI或<range>
const char * CMelsecAutoGroupBuilder::GetBlockType(const char *pAddr)
{
	static string strAddress = "";
	strAddress = "";

	string strTemp = pAddr;
	string::size_type nIndex = strTemp.find(":");//去除device前缀
	if(nIndex != std::string::npos)
		strTemp = strTemp.substr(nIndex + 1);
	else
		return strAddress.c_str();	

	nIndex = strTemp.find(",");
	if(nIndex != std::string::npos)//
		strTemp = strTemp.substr(0, nIndex);

	if (strTemp.length() >= 2)//类型+块号 >= 2，最小Tx类型，最大DBxxx类型
	{
		char ch = strTemp.at(1);
		if (ch >= '0' && ch <= '9')//第二位是数字
			strAddress = strTemp.substr(0, 1);
		else
			strAddress = strTemp.substr(0, 2);
	}

	return strAddress.c_str();
}

//获取<number>值
const int CMelsecAutoGroupBuilder::GetAddrNumber(const char *pAddr)
{
	static int nNum = 0;
	nNum = 0;

	string strTemp = pAddr;
	const char *pType = GetBlockType(strTemp.c_str());
	if (!pType || strlen(pType) <= 0)
		return nNum;

	string::size_type nIndex = strTemp.find(":");//去除device前缀
	if(nIndex != std::string::npos)
		strTemp = strTemp.substr(nIndex + 1);
	else
		return nNum;

	nIndex = strTemp.find(",");
	if(nIndex != std::string::npos)//非C/T块
	{
		std::string strType = pType;
		transform(strType.begin(), strType.end(), strType.begin(), (int (*)(int))toupper);
		
		strTemp = strTemp.substr(nIndex + 1);
		nNum = atoi(strTemp.c_str());
	}

	return nNum;
}

const char * CMelsecAutoGroupBuilder::GenerateGroupName()
{
	static char szGroupName[ICV_DATABLOCKNAME_MAXLEN + 1];
	memset(szGroupName, 0, sizeof(szGroupName));
	sprintf(szGroupName, "group%u", m_nGrpNum++);
	return szGroupName;
}

unsigned char CMelsecAutoGroupBuilder::GetParameter1(const string &strSymbol)
{
	if (strSymbol.compare("SM") == 0)
		return 0x91;
	else if (strSymbol.compare("SD") == 0)
		return 0xA9;
	else if (strSymbol.compare("X") == 0)
		return 0x9C;
	else if (strSymbol.compare("Y") == 0)
		return 0x9D;
	else if (strSymbol.compare("M") == 0)
		return 0x90;
	else if (strSymbol.compare("L") == 0)
		return 0x92;
	else if (strSymbol.compare("F") == 0)
		return 0x93;
	else if (strSymbol.compare("V") == 0)
		return 0x94;
	else if (strSymbol.compare("B") == 0)
		return 0xA0;
	else if (strSymbol.compare("D") == 0)
		return 0xA8;
	else if (strSymbol.compare("W") == 0)
		return 0xB4;
	else if (strSymbol.compare("TS") == 0)
		return 0xC1;
	else if (strSymbol.compare("TC") == 0)
		return 0xC0;
	else if (strSymbol.compare("TN") == 0)
		return 0xC2;
	else if (strSymbol.compare("STS") == 0)
		return 0xC7;
	else if (strSymbol.compare("STC") == 0)
		return 0xC6;
	else if (strSymbol.compare("STN") == 0)
		return 0xC8;
	else if (strSymbol.compare("CS") == 0)
		return 0xC4;
	else if (strSymbol.compare("CC") == 0)
		return 0xC3;
	else if (strSymbol.compare("CN") == 0)
		return 0xC5;
	else if (strSymbol.compare("SB") == 0)
		return 0xA1;
	else if (strSymbol.compare("SW") == 0)
		return 0xB5;
	else if (strSymbol.compare("DX") == 0)
		return 0xA2;
	else if (strSymbol.compare("DY") == 0)
		return 0xA3;
	else if (strSymbol.compare("Z") == 0)
		return 0xCC;
	else if (strSymbol.compare("R") == 0)
		return 0xAF;
	else if (strSymbol.compare("ZR") == 0)
		return 0xB0;
}


const char* CMelsecAutoGroupBuilder::GetGroupType(const string &strSymbol)
{
	if(strSymbol.compare("SD") == 0 || strSymbol.compare("D") == 0 || strSymbol.compare("W") == 0 || strSymbol.compare("TN") == 0 ||
		strSymbol.compare("STN") == 0 || strSymbol.compare("CN") == 0 || strSymbol.compare("SW") == 0 || strSymbol.compare("Z") == 0 ||
		strSymbol.compare("R") == 0 || strSymbol.compare("ZR") == 0 )
	{
		return MC_PLC_WORD;
	}
	else
		return MC_PLC_BIT;

}

unsigned int CMelsecAutoGroupBuilder::GetStartHexAddress(const string& strSymbol, const char* szAddress)
{
	unsigned int nRetValue = 0;
	if ((strSymbol.compare("X") == 0)
		|| (strSymbol.compare("Y") == 0)
		|| (strSymbol.compare("B") == 0)
		|| (strSymbol.compare("W") == 0)
		|| (strSymbol.compare("SB") == 0)
		|| (strSymbol.compare("SW") == 0)
		|| (strSymbol.compare("DX") == 0)
		|| (strSymbol.compare("DY") == 0)
		|| (strSymbol.compare("ZR") == 0))
	{
		//trans to decimal
		sscanf(szAddress, "%x", &nRetValue);
	}
	else
	{
		nRetValue = atoi(szAddress);
	}
	return nRetValue;
}


