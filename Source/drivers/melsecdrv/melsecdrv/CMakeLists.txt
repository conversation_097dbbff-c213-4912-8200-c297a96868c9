cmake_minimum_required(VERSION 3.10)

PROJECT (melsecdrv)

INCLUDE($ENV{DRDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
ADD_DEFINITIONS(-DSXPLAT_LINUX)
#Setting Source Files
SET(SRCS ${SRCS} melsec.cpp melsecautogroup.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME melsecdrv)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS drdrivercommon drcomm)

SET(SPECOUTDIR /drivers/melsecdrv)
INCLUDE($ENV{DRDIR}CMakeSpecOutPath)
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)
