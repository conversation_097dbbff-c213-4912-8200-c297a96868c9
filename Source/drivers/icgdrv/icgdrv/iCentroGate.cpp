/************************************************************************
*	Filename:		iCentroGate.cpp
*	Copyright:		Shanghai Baosight Company Software Co., Ltd.
*
*	Description:	iCG驱动
*
*	@author:		Li Baoqin
*	@version		2013-01-12	Li Baoqin	Initial Version
*************************************************************************/

#include "iCentroGate.h"
#include <stdio.h>
#include <cstring>
#include <stdlib.h>
#include <iostream>
#include <algorithm>
#include "ace/OS_main.h"
#include "common/RMAPIDef.h"
#include "gettext/libintl.h"
#include <boost/filesystem/operations.hpp>
#include "common/cvcomm.hxx"

#define _(STRING) gettext(STRING)
typedef unsigned char BYTE;
#define EC_ICV_INVALID_PARAMETER                    100
using namespace std;
clock_t interval_start_time = clock();

map<DRVHANDLE, CICGDevice*> g_mapDevice;
int g_nHeartNum = 0;
int g_nTimeCount = 0;
#define HEART_TIMECOUNT		10

CCVLog g_CVLogICG;
//#define DBG_LOG(X) Drv_LogMessage X 

inline bool is_little_endian()
{
	int a = 1;
 	return (1 == *((char *)&a));
}

inline long AdjustMemory(char* pOrig, int nLength)
{
	if (is_little_endian())
		return DRV_SUCCESS;

	char* pReverse = pOrig + nLength - 1;
	char chTemp;
	for( ; pReverse > pOrig; pOrig++, pReverse--)
	{
		chTemp = *pOrig;
		*pOrig = *pReverse;
		*pReverse = chTemp;
	}

	return DRV_SUCCESS;
}
/**
*  获取驱动版本号.
*
*  @version   07/20/2012   Initial Version.
*/
CVDRIVER_EXPORTS long GetDrvFrameVersion()
{
	return 2;
}

CVDRIVER_EXPORTS long Begin()
{
	g_CVLogICG.SetLogFileNameThread("icgdrv");
#ifdef _WIN32 
	string strPath = CVComm.GetCVEnv();
	strPath.append("/drivers/icgdrv/_symlinkcache");
	boost::filesystem::path path_(strPath);
	int removed = boost::filesystem::remove_all( path_ ); // remove entire directory;
	CV_INFO(g_CVLogICG,"Try to remove cache file path: %s, result:%d.", path_.c_str(), removed);
#endif//_WIN32
	return DRV_SUCCESS;
}

/**
 *  初始化函数，驱动EXE启动时该函数被调用，可在该函数中实现自定义初始化操作.
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012    Initial Version.
 */
CVDRIVER_EXPORTS long Initialize(DRVHANDLE hDevice)
{
	return DRV_SUCCESS;
}
/**
 *  驱动EXE退出时该函数被调用.
 *  在该函数中可以释放自定义资源、断开设备连接等操作.
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012    Initial Version.
 */
CVDRIVER_EXPORTS long UnInitialize()
{
	//TODO：退出清理操作
	//释放所有的设备	
	for (map<DRVHANDLE, CICGDevice*>::iterator iterDevice = g_mapDevice.begin();
		iterDevice != g_mapDevice.end();
		iterDevice++)
	{
		delete iterDevice->second;
	}
	g_mapDevice.clear();

    g_CVLogICG.StopLogThread();
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnHeartBeat()
{
	CV_DEBUG(g_CVLogICG,"Enter OnHeartBeat.");
	g_nTimeCount++;
	if (g_nTimeCount < HEART_TIMECOUNT)
		return DRV_SUCCESS;

	for (map<DRVHANDLE, CICGDevice*>::iterator iterDevice = g_mapDevice.begin();
		iterDevice != g_mapDevice.end();
		iterDevice++)
	{
		iterDevice->second->WriteHeartData();
	}
	g_nTimeCount = 0;
	return DRV_SUCCESS;
}


/**
 *  定时读取数据函数.
 *  这类设备通常提供读取指定数据块的信息协议，该函数主要实现如下功能：
 *  1、数据的发送和接收
 *  2、更新数据块实时数据的值、数据状态和时间戳
 *  3、对于非tcp通信协议，通常需要在该接口检查连接状态
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *  @param  -[in]  DRVHANDLE hDataBlock: [数据块句柄]
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.icgicg
 */
CVDRIVER_EXPORTS long OnReadData(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	//TODO：发送和接收数据
	//TODO：调用驱动框架提供的Drv_UpdateBlockData、Drv_UpdateBlockStatus更新数据块信息
	//对于点设备，hDataBlock是空的
	map<DRVHANDLE, CICGDevice*>::iterator iterICGDevice = g_mapDevice.find(hDevice);
	if (iterICGDevice != g_mapDevice.end())
	{ 
		clock_t interval_end_time = clock();
		clock_t elapsed_time = interval_end_time - interval_start_time;
		if (elapsed_time > 3000) // 间隔3000ms，进行一次连接状态检查
		{
			interval_start_time = clock(); //更新时间
			CICGDevice* pICGDev = iterICGDevice->second;
			bool isConnect = false;
			for (list<CSymLink*>::iterator itr = pICGDev->m_lstLinks.begin(); itr != pICGDev->m_lstLinks.end(); itr++)
			{
				isConnect = isConnect || (*itr)->IsConnected();
			}
			if (isConnect)
			{ 
				Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
				CV_DEBUG(g_CVLogICG, "Drv_UpdateDevStatus DEV_STATUS_GOOD.");
			}
			else
			{
				Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD);
				CV_DEBUG(g_CVLogICG, "Drv_UpdateDevStatus !!!DEV_STATUS_BAD!!!");
			}
		}
		return iterICGDevice->second->ReadData();
	}
	else
	{
		Drv_UpdateDevStatus(hDevice, DEV_STATUS_BAD); // 没有对应的icg设备，设备状态bad
		CV_DEBUG(g_CVLogICG, "Drv_UpdateDevStatus !!!DEV_STATUS_BAD!!!");
		return EC_ICG_DEVICE_NOEXIST;
	}

}

/**
 *  当有控制命令时该函数被调用.
 *  在该函数中根据传递的参数，向设备下发控制命令。
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *  @param	-[in]  DRVHANDLE hDataBlock : [数据块句柄]
 *  @param	-[in]  int nTagDataType : tag点类型
 *  @param  -[in]  int nTagByteOffset: [tag点在块中的字节偏移量]
 *  @param  -[in]  int nTagBitOffset: [字节内的位偏移量]
 *  @param	-[in]  char *szCmdData : [控制指令]
 *  @param  -[in]  int nCmdDataLenBits: [控制数据长度,单位是bit]
 *
  *  @return : 控制执行反馈结果
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDataBlock, int nTagByteOffset, int nTagBitOffset, char *szCmdData, int nCmdDataLenBits)
{
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
	CV_INFO(g_CVLogICG,"Receive one write cmd, Device:%s, DataBlock:%s.", pDevice->pszName, pDataBlock->pszName);
	map<DRVHANDLE, CICGDevice*>::iterator iterICGDevice = g_mapDevice.find(hDevice);
	if (iterICGDevice != g_mapDevice.end())
	{
		
		if (!pDataBlock || !pDataBlock->pszAddress || !pDataBlock->pszParam1)
		{
			CV_ERROR(g_CVLogICG,-1,"Failed to get datablock information");
			return EC_ICG_DATABLOCK_NOEXIST;
		}

		string strDataType(pDataBlock->pszParam1);
		CWriteDataBuffer tagDataBuff(nTagByteOffset, nTagBitOffset, szCmdData, nCmdDataLenBits);
		int nAysncWrite = Drv_GetUserData(hDevice,0);
		bool bAysncWrite = (nAysncWrite == 1)? 1:0;
		return iterICGDevice->second->WriteData(hDataBlock, tagDataBuff, bAysncWrite);
	}
	else
	{
		CV_ERROR(g_CVLogICG,-1,"device not found");
		return EC_ICG_DEVICE_NOEXIST;
	}
}

/**
 *  添加设备时该函数被调用.
 *  该函数主要针对非tcp连接设备，用户可以通过设备句柄获取设备连接参数，初始化连接设备
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
	//TODO：对于非tcp设备处理设备连接等操作
	return AddDevice(hDevice);

}

/**
 *  删除设备时该函数被调用.
 *  该函数主要针对非tcp连接设备，用户可以通过设备句柄获取设备信息，处理断开设备、释放相关资源等操作
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
	//TODO：对于非tcp设备处理设备断开等操作
	map<DRVHANDLE, CICGDevice*>::iterator iterDevice = g_mapDevice.find(hDevice);
	if (iterDevice != g_mapDevice.end())
	{
		delete iterDevice->second;
		g_mapDevice.erase(iterDevice);
	}

	return DRV_SUCCESS;
}

/**
 *  添加数据块时该函数被调用.
 *  用户需要在该函数中返回数据块大小
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *  @param  -[in]  DRVHANDLE hDataBlock: [数据块句柄]
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	//TODO：执行数据块相关自定义操作
	// 获取tag点
	long lRet = DRV_SUCCESS;
	map<DRVHANDLE, CICGDevice*>::iterator iterICGDevice = g_mapDevice.find(hDevice);
	// 框架先调用OnDataBlockAdd，再调用OnDeviceAdd，因此第一个DataBlock需要新增Device
	if (iterICGDevice == g_mapDevice.end())
	{
		AddDevice(hDevice);
	}

	iterICGDevice = g_mapDevice.find(hDevice);
	if (iterICGDevice != g_mapDevice.end())
	{
		CICGDevice *pICGDevice = iterICGDevice->second;
		CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
		if (!pDataBlock || !pDataBlock->pszAddress)
		{
			CV_ERROR(g_CVLogICG,-1,"failed to get datablock information");
			return EC_ICG_DATABLOCK_NOEXIST;
		}

		lRet = pICGDevice->AddData(hDevice, hDataBlock);
	}
	else
	{
		lRet = EC_ICG_DEVICE_NOEXIST;
	}

	return lRet;

}

/**
 *  删除数据块时该函数被调用.
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *  @param  -[in]  DRVHANDLE hDataBlock: [数据块句柄]
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version     07/20/2012   Initial Version.
 */
CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hDataBlock)
{
	//TODO：执行数据块相关清理操作
	map<DRVHANDLE, CICGDevice*>::iterator iterICGDevice = g_mapDevice.find(hDevice);
	if (iterICGDevice != g_mapDevice.end())
	{
		CICGDevice *pICGDevice = iterICGDevice->second;
		CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
		if (!pDataBlock || !pDataBlock->pszAddress)
		{
			CV_ERROR(g_CVLogICG,-1,"failed to get datablock information");
			return EC_ICG_DATABLOCK_NOEXIST;
		}

		pICGDevice->DelData(hDataBlock);
		return DRV_SUCCESS;
	}
	else
	{
		return EC_ICG_DEVICE_NOEXIST;
	}

}


std::string GetConnStatusInfo( int nStatus )
{
	std::string csRet;
	switch(nStatus)
	{
	case SYMAPI_STATUS_OK:
		csRet = _("OK");
		break;
	case SYMAPI_STATUS_NOCONNECT:
		csRet = _("NOCONNECT");
		break;
	case SYMAPI_STATUS_DISCONNECT:
		csRet = _("DISCONNECT");
		break;
	case SYMAPI_STATUS_CONNECTING:
		csRet = _("CONNECTING");
		break;
	case SYMAPI_STATUS_CONNECTED:
		csRet = _("CONNECTED");
		break;
	case SYMAPI_STATUS_LOGIN:
		csRet = _("LOGIN");
		break;
	case SYMAPI_STATUS_LOGIN_OK:
		csRet = _("LOGIN_OK");
		break;
	case SYMAPI_STATUS_LOGIN_FAIL:
		csRet = _("LOGIN_FAIL");
		break;
	case SYMAPI_STATUS_REQCONFIG:
		csRet = _("REQCONFIG");
		break;
	case SYMAPI_STATUS_REQCONFIG_OK:
		csRet = _("REQCONFIG_OK");
		break;
	case SYMAPI_STATUS_REQCONFIG_FAIL:
		csRet = _("REQCONFIG_FAIL");
		break;
	case SYMAPI_STATUS_REQALL:
		csRet = _("REQALL");
		break;
	case SYMAPI_STATUS_REQALL_OK:
		csRet = _("REQALL_OK");
		break;
	case SYMAPI_STATUS_REQALL_FAIL:
		csRet = _("REQALL_FAIL");
		break;
	case SYMAPI_STATUS_VER:
		csRet = _("DATABASE_VERSION_CHANGE");
		break;
	case SYMAPI_STATUS_TX:
		csRet = _("OFFLINE_CACHE");
		break;
	default:
		csRet = _("UNKNOWN");
		break;
	}
	return csRet;
}

std::string GetErrInfo(int nRet)
{
	std::string csRet;
	switch(nRet)
	{
	case SYMAPI_ERR_OK:
		csRet = _("success");
		break;
	case SYMAPI_ERR_TIMEOUT:
		csRet = _("initialize timeout");
		break;
	case SYMAPI_ERR_UNCOMPRESS:
		csRet = _("unzip data failed");
		break;
	case SYMAPI_ERR_VER:
		csRet = _("database version changed");
		break;
	case SYMAPI_ERR_CONNECT:
		csRet = _("connection not performed");
		break;
	case SYMAPI_ERR_INIT:
		csRet = _("initialization uncompleted");
		break;
	case SYMAPI_ERR_FIND:
		csRet = _("data point not found");
		break;
	case SYMAPI_ERR_PARAM:
		csRet = _("parameter error");
		break;
	case SYMAPI_ERR_CMDBUF:
		csRet = _("command buffer overflow");
		break;
	case SYMAPI_ERR_CMDTIME:
		csRet = _("command timeout");
		break;
	case SYMAPI_ERR_CMDERR:
		csRet = _("command execution error");
		break;
	case SYMAPI_ERR_DISABLE:
		csRet = _("disalbed");
		break;
	case SYMAPI_ERR_LOGIN_FAIL:
		csRet = _("login failed");
		break;
	case SYMAPI_ERR_LOGIN_USER:
		csRet = _("user not existed");
		break;
	case SYMAPI_ERR_LOGIN_PASS:
		csRet = _("wrong password");
		break;
	case SYMAPI_ERR_LOGIN_INV:
		csRet = _("User is prohibited");
		break;
	case SYMAPI_ERR_LOGIN_DIS:
		csRet = _("restrict user to login");
		break;
	case SYMAPI_ERR_LOGIN_RE:
		csRet = _("user already logged in");
		break;
	case SYMAPI_ERR_LOGIN_NULL:
		csRet = _("user expired and need relogin");
		break;
	case SYMAPI_ERR_LOGIN_VER:
		csRet = _("Incompatible API version");
		break;
	default:
		csRet = _("unknown error");
		break;
	}
	return csRet;
}

std::string GetCmdStatus(int nRet)
{
	return GetErrInfo(nRet);
}

long AddDevice( DRVHANDLE hDevice )
{
	map<DRVHANDLE, CICGDevice*>::iterator iterDevice = g_mapDevice.find(hDevice);
	if (iterDevice == g_mapDevice.end())
	{
		CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
		if (!pDevice || !pDevice->pszConnParam)
		{
			CV_ERROR(g_CVLogICG,-1,"failed to get device information");
			return EC_ICG_DEVICE_NOEXIST;
		}

		CICGDevice *pICGDevice = new CICGDevice(hDevice, pDevice);
		g_mapDevice.insert(make_pair(hDevice, pICGDevice));
	}

	return DRV_SUCCESS;
}


//////////////////////////////////////////////////////////////////////////
//  CSymLink
//////////////////////////////////////////////////////////////////////////

/**
 *  创建链接，api自动连接icg.
 *
 *  @version     07/20/2012   Initial Version.
 */
void CSymLink::CreateLink()
{	
	CSymLinkApi *pLink = CreateSymLinkApi(0);
	if (!pLink)
	{
		CV_ERROR(g_CVLogICG,-1,"failed to establish connection");
	}
	pLink->SetCallerPtr(this);
	pLink->SetStatusChangeFunc(&CSymLink::OnLinkStatusChange);
	// 设置超时后，如果有链接不上的通道会干扰正常连接通道的通信
	pLink->SetLinkTimeOut(3000);	// 建立连接的超时时间
	pLink->SetPackTimeOut(1000);	// 连接建立后的心跳包超时时间
	pLink->SetDataChangeFunc(&CSymLink::OnDataChange);
	long nPort = atol(m_strPort.c_str());

	pLink->ConnectServer(m_strIP.c_str(), nPort, m_strUser.c_str(), m_strPwd.c_str(), 0);
	CV_DEBUG(g_CVLogICG,"connection established:handle=%x, ip=%s, port=%d, user=%s, pwd=%s", pLink, m_strIP.c_str(), nPort, m_strUser.c_str(), m_strPwd.c_str());
	m_pLink = pLink;
}

/**
 *  释放链接，关闭并释放连接. 
 *
 *  @version     07/20/2012   Initial Version.
 */
void CSymLink::FreeLink()
{
	if (m_pLink != NULL)
	{
		m_nStatus = SYMAPI_STATUS_DISCONNECT;
		// 删除链接之前，注销回调函数
		m_pLink->SetStatusChangeFunc(NULL);
		m_pLink->DisConnect();
		FreeSymLinkApi(m_pLink);
		m_pLink = NULL;
	}
}

/**
 *  状态改变回调函数.
 *  @param  -[in]  void* ptrSymLinkApi: [链接指针]
 *  @param  -[in]  void* ptrSymLink: [调用者指针，CSymLink*]
 *  @param  -[out] int nStatus: [状态]
 *
 *
 *  @version     07/20/2012   Initial Version.
 */
void CSymLink::OnLinkStatusChange( void* ptrSymLinkApi, void* ptrSymLink, int nStatus )
{
	CSymLinkApi* pLink = (CSymLinkApi*)ptrSymLinkApi;
	CSymLink* pObserver = (CSymLink*)ptrSymLink;
	if (NULL == pObserver || pLink != pObserver->m_pLink)
		return;
	
	if (nStatus > SYMAPI_STATUS_TX)
	{
		CV_ERROR(g_CVLogICG, -1, "Abnormal status %d.", nStatus);
		return;
	}
	CV_INFO(g_CVLogICG,"OnLinkStatusChange status=%s, status number:%d. IP:%s, port:%s, user:%s, password:%s.", GetConnStatusInfo(nStatus).c_str(), nStatus,  pObserver->m_strIP.c_str(), pObserver->m_strPort.c_str(), pObserver->m_strUser.c_str(), pObserver->m_strPwd.c_str());
	if(nStatus == SYMAPI_STATUS_LOGIN_FAIL && pLink)
	{
		CV_ERROR(g_CVLogICG,nStatus,"Login failed. GetLastErr return %d, IP:%s, port:%s, user:%s, password:%s.", pLink->GetLastErr(), pObserver->m_strIP.c_str(), pObserver->m_strPort.c_str(), pObserver->m_strUser.c_str(), pObserver->m_strPwd.c_str());
	}
	if(nStatus == SYMAPI_STATUS_VER)
	{
		return;
	}
	pObserver->m_nStatus = nStatus;
	if (nStatus >= SYMAPI_STATUS_NOCONNECT && nStatus <= SYMAPI_STATUS_CONNECTED && pObserver->m_nStatus != nStatus)
	{
		CV_DEBUG(g_CVLogICG,"connection st: handle=%x, ip=%s, status=%s", pObserver->m_pLink, pObserver->m_strIP.c_str(), GetConnStatusInfo(nStatus).c_str());
	}

}

void CSymLink::OnDataChange( void* ptr1, void* ptr2, int nChangeCount, int* pChangeHandle )
{
	CSymLinkApi* pLink = (CSymLinkApi*)ptr1;
	CSymLink* pObserver = (CSymLink*)ptr2;
	if (NULL == pObserver || pLink != pObserver->m_pLink)
		return;

	CV_DEBUG(g_CVLogICG,"data change: handle=%d, nChangeCount=%d", nChangeCount, *pChangeHandle);
}

CSymLink::~CSymLink()
{
	FreeLink();
}


//////////////////////////////////////////////////////////////////////////
//  CICGData
//////////////////////////////////////////////////////////////////////////

CICGData::CICGData( DRVHANDLE hDevice, DRVHANDLE hDataBlock, string &strDataAddress ) 
	: m_hDevice(hDevice), m_hDataBlock(hDataBlock), m_strDataAddress(strDataAddress)
{
	CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
	if (pDevice && pDevice->pszName)
	{
		m_strDeviceName = pDevice->pszName;
	}
}

/**
 *  读数据.
 *  @param  -[in]  CSymLink* pLink: [链接指针]
 *
 *  @return  读取结果 0-成功
 *
 *  @version     07/20/2012   Initial Version.
 *  @version	9/5/2013  baoyuansong  更新字符串时包含'\0'.
 */
long CICGData::ReadData( CSymLink* pLink, int nDBStatus )
{
	//如果pLink为NULL，说明通讯失败，设置数据质量
	//分成两个if判断的原因是||判断时若plink为null有可能pLink->m_pLink会崩溃
	if (pLink == NULL)
	{
		Drv_UpdateBlockStatus(m_hDevice, m_hDataBlock, DATA_STATUS_COMM_FAILURE);
		return DRV_SUCCESS;
	}
	else if (pLink->m_pLink == NULL)
	{
		Drv_UpdateBlockStatus(m_hDevice, m_hDataBlock, DATA_STATUS_COMM_FAILURE);
		return DRV_SUCCESS;
	}
	//获取时间戳
	DRVTIME drvTime;
	Drv_GetCurrentTime(&drvTime);
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(m_hDataBlock);
	const char *szTagAddr = pDataBlock->pszAddress;

	if (nDBStatus != DATA_STATUS_OK)
	{
		std::string strTagAddr = szTagAddr;
		std::string strIOStatus = ".IoStatus";
		if (std::mismatch(strIOStatus.rbegin(), strIOStatus.rend(), strTagAddr.rbegin()).first != strIOStatus.rend())
		{
			Drv_UpdateBlockStatus(m_hDevice, m_hDataBlock, nDBStatus);
			return DRV_SUCCESS;
		}
		else
			nDBStatus = DATA_STATUS_OK;
	}

	SymLinkApiData tagData;
	int nRet = pLink->m_pLink->GetDbTagDataByName(szTagAddr, tagData);
	if (nRet==SYMAPI_ERR_OK)
	{
		switch(tagData.Type)
		{
		case SYMAPI_DBTAGTYPE_INT:
			{
				int nTagData = tagData.GetInt();
				CV_DEBUG(g_CVLogICG,"read tag(int): %s, current value %d", szTagAddr, nTagData);
				
				if (pDataBlock->nElemNum == 1)
				{
					char nValue = (char)nTagData;
					CV_DEBUG(g_CVLogICG,"read tag(int): %s, current value %d", szTagAddr, nValue);
					Drv_UpdateBlockData(m_hDevice, m_hDataBlock, (char*)(&nValue), 0, pDataBlock->nElemNum, nDBStatus, &drvTime);
				}
				else if (pDataBlock->nElemNum == 2)
				{
					short nValue = (short)nTagData;
					CV_DEBUG(g_CVLogICG,"read tag(short int): %s, current value %d", szTagAddr, nValue);
					AdjustMemory((char*)(&nValue), 2);
					Drv_UpdateBlockData(m_hDevice, m_hDataBlock, (char*)(&nValue), 0, pDataBlock->nElemNum, nDBStatus, &drvTime);
				}
				else
				{
					AdjustMemory((char*)(&nTagData), 4);
					CV_INFO(g_CVLogICG,"read tag----------: %s, current value %d", szTagAddr, nTagData);
					Drv_UpdateBlockData(m_hDevice, m_hDataBlock, (char*)(&nTagData), 0, sizeof(nTagData), nDBStatus, &drvTime);
				}
			}
			break;
		case SYMAPI_DBTAGTYPE_EVENT:
			{
				int nTagData = tagData.Data.pSOEData->m_nValue;
				CV_DEBUG(g_CVLogICG,"read tag(int): %s, current value %d", szTagAddr, nTagData);
				if (pDataBlock->nElemNum == 1)
				{
					char nValue = (char)nTagData;
					CV_DEBUG(g_CVLogICG,"read tag(int): %s, current value %d", szTagAddr, nValue);
					Drv_UpdateBlockData(m_hDevice, m_hDataBlock, (char*)(&nValue), 0, pDataBlock->nElemNum, nDBStatus, &drvTime);
				}
				else if (pDataBlock->nElemNum == 2)
				{
					short nValue = (short)nTagData;
					CV_DEBUG(g_CVLogICG,"read tag(short int): %s, current value %d", szTagAddr, nValue);
					AdjustMemory((char*)(&nValue), 2);
					Drv_UpdateBlockData(m_hDevice, m_hDataBlock, (char*)(&nValue), 0, pDataBlock->nElemNum, nDBStatus, &drvTime);
				}
				else
				{
					AdjustMemory((char*)(&nTagData), 4);
					Drv_UpdateBlockData(m_hDevice, m_hDataBlock, (char*)(&nTagData), 0, sizeof(nTagData), nDBStatus, &drvTime);
				}
			}
			break;
		case SYMAPI_DBTAGTYPE_DOUBLE:
			{
				double dTagData = tagData.GetDouble();
				if (pDataBlock->nElemNum == 1)
				{
					char nValue = (char)dTagData;
					CV_DEBUG(g_CVLogICG,"read tag(double): %s, current value %d", szTagAddr, nValue);
					Drv_UpdateBlockData(m_hDevice, m_hDataBlock, (char*)(&nValue), 0, pDataBlock->nElemNum, nDBStatus, &drvTime);
				}
				else if (pDataBlock->nElemNum == 2)
				{
					short nValue = (short)dTagData;
					CV_DEBUG(g_CVLogICG,"read tag(short int): %s, current value %d", szTagAddr, nValue);
					AdjustMemory((char*)(&nValue), 2);
					Drv_UpdateBlockData(m_hDevice, m_hDataBlock, (char*)(&nValue), 0, pDataBlock->nElemNum, nDBStatus, &drvTime);
				}
				else if (pDataBlock->nElemNum == 4)
				{
					if (!strcmp(pDataBlock->pszParam1, "float"))
					{
						float fValue = (float)dTagData;
						CV_DEBUG(g_CVLogICG,"read tag(float): %s, current value %f", szTagAddr, fValue);
						AdjustMemory((char*)(&fValue), 4);
						Drv_UpdateBlockData(m_hDevice, m_hDataBlock, (char*)(&fValue), 0, pDataBlock->nElemNum, nDBStatus, &drvTime);
					}
					else if (!strcmp(pDataBlock->pszParam1, "unsigned"))
					{
						unsigned int nValue = (unsigned int)dTagData;
						CV_DEBUG(g_CVLogICG,"read tag(int): %s, current value %d", szTagAddr, nValue);
						AdjustMemory((char*)(&nValue), 4);
						Drv_UpdateBlockData(m_hDevice, m_hDataBlock, (char*)(&nValue), 0, pDataBlock->nElemNum, nDBStatus, &drvTime);
					}
					else
					{
						int nValue = (int)dTagData;
						CV_DEBUG(g_CVLogICG,"read tag(int): %s, current value %d", szTagAddr, nValue);
						AdjustMemory((char*)(&nValue), 4);
						Drv_UpdateBlockData(m_hDevice, m_hDataBlock, (char*)(&nValue), 0, pDataBlock->nElemNum, nDBStatus, &drvTime);
					}

				}
				else
				{
					CV_DEBUG(g_CVLogICG,"read tag(double): %s, current value %f", szTagAddr, dTagData);
					AdjustMemory((char*)(&dTagData), sizeof(double));
					Drv_UpdateBlockData(m_hDevice, m_hDataBlock, (char*)(&dTagData), 0, sizeof(double), nDBStatus, &drvTime);
				}
			}
			break;
		case SYMAPI_DBTAGTYPE_STR:
			{
				string strTagData = tagData.GetStr();
				CV_DEBUG(g_CVLogICG,"read tag(string): %s, current length: %d",szTagAddr, strTagData.length());
				char *pData = new char[pDataBlock->nBlockDataSize];
				memset(pData, 0, pDataBlock->nBlockDataSize);
				strncpy(pData, strTagData.c_str(), pDataBlock->nBlockDataSize);
				Drv_UpdateBlockData(m_hDevice, m_hDataBlock, pData, 0, pDataBlock->nBlockDataSize, nDBStatus, &drvTime);
				delete []pData;
			}
			break;
		case SYMAPI_DBTAGTYPE_BLOCK:
			if( tagData.Data.pBlockData != NULL && tagData.Data.pBlockData->m_tBlock.m_pData != NULL )
			{
				TDB_BLOCK &blkTagData = tagData.Data.pBlockData->m_tBlock;
				CV_DEBUG(g_CVLogICG,"read tag(blob): %s, current length: %d", szTagAddr, blkTagData.m_nDataLen);
				long nBufferLen = blkTagData.m_nDataLen <= pDataBlock->nBlockDataSize ? blkTagData.m_nDataLen:pDataBlock->nBlockDataSize;
				Drv_UpdateBlockData(m_hDevice, m_hDataBlock, (char*)blkTagData.m_pData, 0, nBufferLen, nDBStatus, &drvTime);
			}
			break;
		default:
			CV_INFO(g_CVLogICG,"tag: %s type error: %s", szTagAddr, GetErrInfo(nRet).c_str());
			break;
		}
	}
	else 
	{
		Drv_UpdateBlockStatus(m_hDevice, m_hDataBlock, DATA_STATUS_CONFIG_ERROR);
		CV_ERROR(g_CVLogICG,nRet,"tag: %s failed to get configuration %s", szTagAddr, GetErrInfo(nRet).c_str());
	}
	return DRV_SUCCESS;
}

/**
 *  将待写的数据转换为icg数据发送. 目前没有使用字节偏移和位偏移
 *  @param  -[in]  CWriteDataBuffer &tagDataBuffer: [写数据信息]
 *  @param  -[in]  SymLinkApiData &tagData: [icg数据]
 *
 *  @return  读取结果 0-成功
 *
 *  @version     07/20/2012   Initial Version.
 */
long CICGData::CastBuff2LinkData( CWriteDataBuffer &tagDataBuffer, SymLinkApiData &tagData, string &strDataType )
{
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(m_hDataBlock);
	if (!pDataBlock || !pDataBlock->pszParam1)
	{
		CV_ERROR(g_CVLogICG,-1,"failed to get datablock information");
		return EC_ICG_DATABLOCK_NOEXIST;
	}

	strDataType = pDataBlock->pszParam1;

	if(std::strcmp(strDataType.c_str(),"int")==0)
	{
		if (1 == tagDataBuffer.m_nCmdDataLenBits || 8 == tagDataBuffer.m_nCmdDataLenBits)//one bytes
		{
			char nData = 0;
			memcpy(&nData, tagDataBuffer.m_szCmdData, sizeof(nData));
			tagData = (int)nData;
		}
		else if (16 == tagDataBuffer.m_nCmdDataLenBits)//two bytes
		{
			short nData = 0;
			memcpy(&nData, tagDataBuffer.m_szCmdData, sizeof(nData));
			tagData = (int)nData;
		}
		else if (32 == tagDataBuffer.m_nCmdDataLenBits)//four bytes
		{
			int nData = 0;
			memcpy(&nData, tagDataBuffer.m_szCmdData, sizeof(nData));
			tagData = nData;
		}
		else
		{
			CV_ERROR(g_CVLogICG,-1,"the command length(%d) is overflow the tag data type", tagDataBuffer.m_nCmdDataLenBits);
		}
		CV_INFO(g_CVLogICG, "write int value %d", tagData.GetInt());
	}
	else if(std::strcmp(strDataType.c_str(),"float")==0)
	{
		float nData = 0;
		memcpy(&nData, tagDataBuffer.m_szCmdData, sizeof(nData));
		tagData = (double)nData;
		CV_INFO(g_CVLogICG, "write float value %f", tagData.GetDouble());
	}
	else if(std::strcmp(strDataType.c_str(),"unsigned")==0)
	{
		unsigned nData = 0;
		memcpy(&nData, tagDataBuffer.m_szCmdData, sizeof(nData));
		tagData = (double)nData;
		CV_INFO(g_CVLogICG, "write unsigned value %f", tagData.GetDouble());
	}
	else if (std::strcmp(strDataType.c_str(),"double")==0)
	{
		if (1 == tagDataBuffer.m_nCmdDataLenBits || 8 == tagDataBuffer.m_nCmdDataLenBits)//one bytes
		{
			char nData = 0;
			memcpy(&nData, tagDataBuffer.m_szCmdData, sizeof(nData));
			tagData = (double)nData;
		}
		else if (16 == tagDataBuffer.m_nCmdDataLenBits)//two bytes
		{
			short nData = 0;
			memcpy(&nData, tagDataBuffer.m_szCmdData, sizeof(nData));
			tagData = (double)nData;
		}
		else if (32 == tagDataBuffer.m_nCmdDataLenBits)//four bytes
		{
			float nData = 0;
			memcpy(&nData, tagDataBuffer.m_szCmdData, sizeof(nData));
			tagData = (double)nData;
		}
		else if (64 == tagDataBuffer.m_nCmdDataLenBits)//four bytes
		{
			double nData = 0;
			memcpy(&nData, tagDataBuffer.m_szCmdData, sizeof(nData));
			tagData = nData;
		}
		else
		{
			CV_ERROR(g_CVLogICG,-1,"the command length(%d) is overflow the tag data type", tagDataBuffer.m_nCmdDataLenBits);
		}
		CV_INFO(g_CVLogICG, "write double value %f", tagData.GetDouble());
	}
	else if (std::strcmp(strDataType.c_str(),"string")==0)
	{
		tagData = tagDataBuffer.m_szCmdData;
		//tagData.Type = SYMAPI_DBTAGTYPE_STR;
		if (tagData.GetStr()!=NULL)
		{
			CV_INFO(g_CVLogICG, "write string value %s", tagData.GetStr());
		}
	}
	else if (std::strcmp(strDataType.c_str(),"blob")==0)
	{
		CDbCommBlock *pBlock = SymLinkApiMallocBlock();
		int nLen = tagDataBuffer.m_nCmdDataLenBits/8;
		CV_INFO(g_CVLogICG, "write blob value len %d", nLen);
		if (nLen > 0)
		{
			pBlock->m_tBlock.m_nDataLen = nLen;
			pBlock->m_tBlock.m_pData = (unsigned char*)SymLinkApiMallocMem(nLen);
			memcpy(pBlock->m_tBlock.m_pData, tagDataBuffer.m_szCmdData, nLen);
		}
		tagData = pBlock;
		SymLinkApiFreeBlock(pBlock);
		////支持BLOB
		//int nlen = 0;
		//nlen = tagDataBuffer.m_nCmdDataLenBits/8;
		//int nBuffLen = 2 * nlen + 1;
		//char *pBuff =  new char [nBuffLen];
		//memset(pBuff, 0x00, nBuffLen);
		//for (int i = 0; i < nlen; i++)
		//{
		//	sprintf(pBuff +  2 * i,"%.2X", tagDataBuffer.m_szCmdData[i]);
		//}
		//tagData = pBuff;
		//tagData.Type = SYMAPI_DBTAGTYPE_STR;
	}
	else
	{
		CV_ERROR(g_CVLogICG,-1,"wrong data type %s cannot convert to iCG standard data", strDataType.c_str());
		return EC_ICG_DATATYPE_NOEXIST;
	}

	return DRV_SUCCESS;
}

/**
 *  写数据
 *  @param  -[in]  CSymLink* pLink: [数据发送链接]
 *  @param  -[in]  CWriteDataBuffer &tagDataBuffer: [写数据信息]
 *
 *  @return  读取结果 0-成功
 *
 *  @version     07/20/2012   Initial Version.
 */
long CICGData::WriteData( CSymLink* pLink, CWriteDataBuffer &tagDataBuffer, bool bAysncWrite)
{
	SymLinkApiData tagData;
	string strDataType("");
	long lRet = CastBuff2LinkData(tagDataBuffer, tagData, strDataType);
	if (lRet != DRV_SUCCESS)
	{
		return lRet;
	}

	int nResult=0;
	SymLinkApiString symLinErrStr;
	if (bAysncWrite)
	{
		tagData.nTimeMills = 1000; // 用毫秒等于1000来代表不等待PLC返回结果。SetDbTagDataByName直接返回。
	}
	CV_INFO(g_CVLogICG,"start write %d bytes to device %s, datablock %s on link[%s],  nTimeMills:%d", tagDataBuffer.m_nCmdDataLenBits/8, m_strDeviceName.c_str(), m_strDataAddress.c_str(), 
		pLink->m_strIP.c_str(),  tagData.nTimeMills);
	lRet = pLink->m_pLink->SetDbTagDataByName(m_strDataAddress.c_str(), tagData, &nResult, &symLinErrStr);		
	if (SYMAPI_ERR_OK == lRet)
	{
		CV_INFO(g_CVLogICG,"end write %d bytes to device %s, datablock %s on link[%s], %s, return %d, %s nTimeMills:%d", tagDataBuffer.m_nCmdDataLenBits/8, m_strDeviceName.c_str(), m_strDataAddress.c_str(), 
			pLink->m_strIP.c_str(), GetCmdStatus(lRet).c_str(), nResult, symLinErrStr.GetStr(), tagData.nTimeMills);
	}
	else
	{
		CV_ERROR(g_CVLogICG,lRet,"end write %d bytes to device: %s datablock %s on link[%s] failed, error info: %s, return %d, %s nTimeMills:%d", tagDataBuffer.m_nCmdDataLenBits/8, m_strDeviceName.c_str(), m_strDataAddress.c_str(), 
			pLink->m_strIP.c_str(),GetCmdStatus(lRet).c_str(),nResult,symLinErrStr.GetStr(), tagData.nTimeMills);
	}
	return lRet;
}


//////////////////////////////////////////////////////////////////////////
//  CICGGroup
//////////////////////////////////////////////////////////////////////////

CICGGroup::~CICGGroup()
{
	for (map<string, CICGData*>::iterator iterData = m_mapData.begin();
		iterData != m_mapData.end();
		iterData++)
	{
		delete iterData->second;
	}
	m_mapData.clear();
}

/**
 *  设备数据是否正常，标定该组的数据是否可以读写
 *  @param  -[in]  CSymLink* pLink: [数据发送链接]
 *
 *  @return  状态 true-正常，false-不正常
 *
 *  @version     07/20/2012   Initial Version.
 */
bool CICGGroup::Ready( CSymLink* pLink )
{
	// 读取设备状态，经过和旋思科技相关技术人确认，对于全局变量，不需要验证状态，因此直接返回true
	if (m_strStatusName == std::string("IoStatus"))
		return true;

	SymLinkApiData tagData;
	int nRet = pLink->m_pLink->GetDbTagDataByName(m_strStatusName.c_str(), tagData);
	if (nRet == SYMAPI_ERR_OK)
	{
		if (tagData.Type == SYMAPI_DBTAGTYPE_INT)
		{
			// 状态为1表示工作正常
			int nTagData = tagData.GetInt();
			if (nTagData == 1)
			{
				//查找IO_Work
				SymLinkApiData workData;
				int nWorkRet = pLink->m_pLink->GetDbTagDataByName(m_strIOWork.c_str(), workData);
				if (nWorkRet == SYMAPI_ERR_OK)
				{
					//数据为0才正常
					int nWorkData = 0;
					if (workData.Type == SYMAPI_DBTAGTYPE_INT)
						nWorkData = workData.GetInt();
					else if (workData.Type == SYMAPI_DBTAGTYPE_DOUBLE)
					{
						double dbTemp = workData.GetDouble();
						nWorkData = dbTemp;
					}
					else
					{
						CV_WARN(g_CVLogICG,nWorkRet,"CICGGroup::Ready return false, work: %s, invalid work type:%d, link(%s:%s,%s)", m_strIOWork.c_str(), workData.Type, pLink->m_strIP.c_str(), pLink->m_strPort.c_str(), pLink->m_strUser.c_str());
						return false;
					}
					if (nWorkData == 0)
						return true;
					else
					{
						CV_WARN(g_CVLogICG,nWorkData,"CICGGroup::Ready return false, work: %s, invalid work value:%d, link(%s:%s,%s)", m_strIOWork.c_str(), workData.GetInt(), pLink->m_strIP.c_str(), pLink->m_strPort.c_str(), pLink->m_strUser.c_str());
						return false;
					}
				}
				else if (nWorkRet == SYMAPI_ERR_FIND) //数据未找到，不管
					return true;
			}	
			else
			{
				CV_ERROR(g_CVLogICG,nTagData,"device status abnormal, tag: %s value: %d link(%s:%s,%s)", m_strStatusName.c_str(), nTagData, pLink->m_strIP.c_str(), pLink->m_strPort.c_str(), pLink->m_strUser.c_str());
			}
		}
		CV_WARN(g_CVLogICG,-1,"CICGGroup::Ready return false, tag: %s, invalid tag type:%d, link(%s:%s,%s)", m_strStatusName.c_str(), tagData.Type, pLink->m_strIP.c_str(), pLink->m_strPort.c_str(), pLink->m_strUser.c_str());
	}
	else
		CV_ERROR(g_CVLogICG,nRet,"GetDbTagDataByName failed, error code %d, tag: %s, link(%s:%s,%s)", nRet, m_strStatusName.c_str(), pLink->m_strIP.c_str(), pLink->m_strPort.c_str(), pLink->m_strUser.c_str());
	CV_DEBUG(g_CVLogICG,"GetDbTagDataByName return %d, tag: %s, link(%s:%s,%s)", nRet, m_strStatusName.c_str(), pLink->m_strIP.c_str(), pLink->m_strPort.c_str(), pLink->m_strUser.c_str());
	return false;
}

/**
 *  读数据.
 *  @param  -[in]  CSymLink* pLink: [链接指针]
 *
 *  @return  读取结果 0-成功
 *
 *  @version     07/20/2012   Initial Version.
 */
long CICGGroup::ReadData( CSymLink* pLink, int nDBStatus )
{
	for (map<string, CICGData*>::iterator iterData = m_mapData.begin();
		iterData != m_mapData.end();
		iterData++)
	{
		iterData->second->ReadData(pLink, nDBStatus);
	}
	return DRV_SUCCESS;
}

/**
 *  增加数据.
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *  @param  -[in]  DRVHANDLE hDataBlock: [数据块句柄]
 *
 *  @return  
 *
 *  @version     07/20/2012   Initial Version.
 */
long CICGGroup::AddData( DRVHANDLE hDevice, DRVHANDLE hDataBlock )
{
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
	if (m_mapData.find(string(pDataBlock->pszName)) == m_mapData.end())
	{
		string strAddress = pDataBlock->pszAddress;
		m_mapData.insert(make_pair(string(pDataBlock->pszName), new CICGData(hDevice, hDataBlock, strAddress)));
	}
	return DRV_SUCCESS;
}

/**
 *  获取状态名，数据按照状态名分组.
 *  @param  -[in]  string &strTagAddr: [数据地址]
 *
 *  @return  
 *
 *  @version     07/20/2012   Initial Version.
 */
string CICGGroup::GetStatusName( string &strTagAddr, string &strWorkName, string &strHeartName)
{
	// 规则：数据名channel.device.tag, 状态名channel.device.IoStatus
	string strStatusName;
	string::size_type nPos = strTagAddr.find_first_of('.');
	if (nPos == string::npos)
		strStatusName = "IoStatus";
	else
	{
		string::size_type nPos2 = strTagAddr.find_first_of('.', nPos + 1);
		if (nPos2 != string::npos)
		{
			strStatusName = strTagAddr.substr(0, nPos2) + string(".IoStatus");
			strWorkName = strTagAddr.substr(0, nPos2) + string(".Io_Work");
			strHeartName = strTagAddr.substr(0, nPos2) + string(".Io_Heart");
		}
		else
		{
			strStatusName = strTagAddr.substr(0, nPos) + string(".IoStatus");
			strWorkName = strTagAddr.substr(0, nPos) + string(".Io_Work");
			strHeartName = strTagAddr.substr(0, nPos) + string(".Io_Heart");
		}
	}
	return strStatusName;
}

/**
 *  删除数据.
 *  @param  -[in]  string &strTagName: [数据名称]
 *
 *  @return  
 *
 *  @version     07/20/2012   Initial Version.
 */
void CICGGroup::DelData( string &strTagName )
{
	map<string, CICGData*>::iterator iterData = m_mapData.find(strTagName);
	if (iterData != m_mapData.end())
	{
		delete iterData->second;
		m_mapData.erase(iterData);
	}
}

/**
 *  写数据
 *  @param  -[in]  CSymLink* pLink: [数据发送链接]
 *  @param  -[in]  string &strTagName: [数据名]
 *  @param  -[in]  CWriteDataBuffer &tagDataBuffer: [写数据信息]
 *
 *  @return  读取结果 0-成功
 *
 *  @version     07/20/2012   Initial Version.
 */
long CICGGroup::WriteData( CSymLink* pLink, string &strTagName, CWriteDataBuffer &tagDataBuffer, bool bAysncWrite)
{
	map<string, CICGData*>::iterator iterData = m_mapData.find(strTagName);
	if (iterData != m_mapData.end())
	{
		return iterData->second->WriteData(pLink, tagDataBuffer, bAysncWrite);
	}
	else
		return EC_ICG_DATABLOCK_NOEXIST;
}


//////////////////////////////////////////////////////////////////////////
//  CICGDevice
//////////////////////////////////////////////////////////////////////////

CICGDevice::CICGDevice( DRVHANDLE hDevice, CVDEVICE *pDevice )
	: m_hDevice(hDevice),m_nRMStatus(0)
{
	string strConnParam = pDevice->pszConnParam;
	CreateLinks(strConnParam, hDevice);

	//创建一个定时器
//	MyTimerHandler mt(1,1,hDevice);  
	//捕捉定时器到时事件，到时后，执行计时器的handle_timeout()方法
//	ACE_Reactor::instance ()->run_reactor_event_loop();
	
}

CICGDevice::~CICGDevice()
{
	for (list<CSymLink*>::iterator iterLink = m_lstLinks.begin();
		iterLink != m_lstLinks.end();
		iterLink++)
	{
		delete *iterLink;
	}
	m_lstLinks.clear();

	for (map<string, CICGGroup*>::iterator iterGroup = m_mapGroups.begin();
		iterGroup != m_mapGroups.end();
		iterGroup++)
	{
		delete iterGroup->second;
	}
	m_mapGroups.clear();
}

/**
 *  读数据，选择冗余链接中的一条通道，将所有组的数据更新一遍；如果没有连通的连接，设置数据质量为BAD.
 *
 *  @return  读取结果 0-成功
 *
 *  @version     07/20/2012   Initial Version.
 */
long CICGDevice::ReadData()
{
	// 检查每一个Group应该使用哪个link
	list<CICGGroup*> lstReadyGroup;
	list<CICGGroup*> lstLeftGroup;
	for (map<string, CICGGroup*>::iterator iterGroup = m_mapGroups.begin();
		iterGroup != m_mapGroups.end();
		iterGroup++)
	{
		lstReadyGroup.push_back(iterGroup->second);
	}

	//从活动主机切换为非活动主机，断开以前的链接
	int nCurRMStatus = Drv_IsActiveHost();
	if ( nCurRMStatus != RM_STATUS_ACTIVE)
	{
		if(m_nRMStatus == RM_STATUS_ACTIVE)
			std::for_each(m_lstLinks.begin(), m_lstLinks.end(), mem_fun(&CSymLink::FreeLink));
		m_nRMStatus = nCurRMStatus;
		return DRV_SUCCESS;
	}

	//从非活动主机切换为活动主机，必须试图建立连接，在初始化的时候我们并未进行连接
	if (m_nRMStatus != RM_STATUS_ACTIVE && nCurRMStatus == RM_STATUS_ACTIVE)
	{
		std::for_each(m_lstLinks.begin(), m_lstLinks.end(), mem_fun(&CSymLink::CreateLink));
		m_nRMStatus = nCurRMStatus;
	}

	// 按照优先级检查每一个link
	for (list<CSymLink*>::iterator iterLink = m_lstLinks.begin();
		iterLink != m_lstLinks.end();
		iterLink++)
	{
		CSymLink *pSymLink = *iterLink;

		// link为空或未连接成功，跳过
		if (pSymLink == NULL || pSymLink->m_pLink == NULL || !pSymLink->IsConnected())
			continue;
		
		// 读取准备好的Group
		for (list<CICGGroup*>::iterator iterGroup = lstReadyGroup.begin();
			iterGroup != lstReadyGroup.end();
			iterGroup++)
		{
			CICGGroup* pGroup = *iterGroup;
			if (pGroup->Ready(pSymLink))
			{
				// 状态正常则读取数据
				pGroup->ReadData(pSymLink);
			}
			else
			{
				// 不正常，则放到下一次读取列表中
				lstLeftGroup.push_back(pGroup);
			}
		}
		lstReadyGroup.clear();

		if (lstLeftGroup.empty())
		{
			// 所有Group已经读完，退出
			break;
		}
		else
		{
			// 还有未读取的Group，放到准备好的Group列表里
			lstReadyGroup.insert(lstReadyGroup.begin(), lstLeftGroup.begin(), lstLeftGroup.end());
			// 清理leftgroup
			lstLeftGroup.clear();
		}
	}

	// 还有未读完的Group，说明所有link都有问题，数据质量设置为BAD
	for(list<CICGGroup*>::iterator iterGroup = lstReadyGroup.begin();
		iterGroup != lstReadyGroup.end();
		iterGroup++)
	{
		if (m_lstLinks.size() == 0)
		{
			CV_INFO(g_CVLogICG,"m_lstLinks.size() = %d", m_lstLinks.size());
			CSymLink *pSymLinkNull = NULL;
			(*iterGroup)->ReadData(pSymLinkNull, DATA_STATUS_DEVICE_FAILURE);
		}
		else
		{
			(*iterGroup)->ReadData(*m_lstLinks.begin(), DATA_STATUS_DEVICE_FAILURE);
		}
	}

	return DRV_SUCCESS;
}

long CICGDevice::WriteHeartData()
{
	//从活动主机切换为非活动主机，断开以前的链接
	CV_DEBUG(g_CVLogICG,"Enter CICGDevice::WriteHeartData.");
	long lRet = 0;
	//此处不需要创建连接和断开连接，断开连接时由于多线程访问删除同一个指针，可能会触发崩溃，by zhangqiang，2018/08/16
	//int nCurRMStatus = Drv_IsActiveHost();
	//if ( nCurRMStatus != RM_STATUS_ACTIVE)
	//{
	//	if(m_nRMStatus == RM_STATUS_ACTIVE)
	//		std::for_each(m_lstLinks.begin(), m_lstLinks.end(), mem_fun(&CSymLink::FreeLink));
	//	m_nRMStatus = nCurRMStatus;
	//	return DRV_SUCCESS;
	//}

	////从非活动主机切换为活动主机，必须试图建立连接，在初始化的时候我们并未进行连接
	//if (m_nRMStatus != RM_STATUS_ACTIVE && nCurRMStatus == RM_STATUS_ACTIVE)
	//{
	//	std::for_each(m_lstLinks.begin(), m_lstLinks.end(), mem_fun(&CSymLink::CreateLink));
	//	m_nRMStatus = nCurRMStatus;
	//}

	// 按照优先级检查每一个link
	for (list<CSymLink*>::iterator iterLink = m_lstLinks.begin();
		iterLink != m_lstLinks.end();
		iterLink++)
	{
		CSymLink *pSymLink = *iterLink;

		// link为空或未连接成功，跳过
		if (pSymLink == NULL || pSymLink->m_pLink == NULL || !pSymLink->IsConnected())
			continue;

		for (map<string, CICGGroup*>::iterator iterGroup = m_mapGroups.begin();
			iterGroup != m_mapGroups.end();
			iterGroup++)
		{
			CICGGroup* pGroup = iterGroup->second;
			SymLinkApiData tagData;
			tagData = g_nHeartNum;
			g_nHeartNum++;
			int nRet=0;
			SymLinkApiString symLinErrStr;
			lRet = pSymLink->m_pLink->SetDbTagDataByName(pGroup->m_strIOHeart.c_str(), tagData, &nRet, &symLinErrStr);
			if (SYMAPI_ERR_OK == lRet)
			{
				CV_DEBUG(g_CVLogICG,"write heart [%s] success, return %d, %s", pGroup->m_strIOHeart.c_str(), nRet, symLinErrStr.GetStr());
			}
			else
			{
				CV_DEBUG(g_CVLogICG,"write heart [%s] failed, error info: %s, return %d, %s", pGroup->m_strIOHeart.c_str(), GetCmdStatus(lRet).c_str(),nRet,symLinErrStr.GetStr());
			}
		}
	}

	return DRV_SUCCESS;
}

/**
 *  增加数据，根据icg逻辑设置分组.
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *  @param  -[in]  DRVHANDLE hDataBlock: [数据块句柄]
 *
 *  @return  
 *
 *  @version     07/20/2012   Initial Version.
 */
long CICGDevice::AddData( DRVHANDLE hDevice, DRVHANDLE hDataBlock )
{
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
	string strTagAddr = pDataBlock->pszAddress;
	string strWorkName;
	string strHeartName;
	string strStatusName = CICGGroup::GetStatusName(strTagAddr, strWorkName, strHeartName);
	// 根据statusname选择group
	CICGGroup *pGroup = NULL;
	map<string, CICGGroup*>::iterator iterGroup = m_mapGroups.find(strStatusName);
	// 没有匹配的Group
	if (iterGroup == m_mapGroups.end())
	{
		pGroup = new CICGGroup;
		pGroup->m_strStatusName = strStatusName;
		pGroup->m_strIOWork = strWorkName;
		pGroup->m_strIOHeart = strHeartName;
		m_mapGroups.insert(make_pair(strStatusName, pGroup));
	}
	else
	{
		pGroup = iterGroup->second;
	}

	if (pGroup)
	{
		return pGroup->AddData(hDevice, hDataBlock);
	}
	else
	{
		return DRV_SUCCESS;
	}
}

/**
 *  删除数据.
 *  @param  -[in]  DRVHANDLE hDataBlock: [数据块句柄]
 *
 *  @return  
 *
 *  @version     07/20/2012   Initial Version.
 */
void CICGDevice::DelData( DRVHANDLE hDataBlock )
{
	// 找到对应的Group
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
	string strTagName = pDataBlock->pszName;
	string strTagAddr = pDataBlock->pszAddress;
	string strWorkName;
	string strHeartName;
	string strStatusName = CICGGroup::GetStatusName(strTagAddr, strWorkName, strHeartName);
	map<string, CICGGroup*>::iterator iterGroup = m_mapGroups.find(strStatusName);
	if (iterGroup != m_mapGroups.end())
	{
		// 找到匹配的Group
		CICGGroup *pGroup = iterGroup->second;
		pGroup->DelData(strTagName);
	}
}

/**
 *  使用连接参数创建链接. 
 *  @guide
 *      link=**********,3290,usr1,pwd1/**********,3290,usr2,/**********,5400,usr1,pwd1/**********,5400,usr2,
 *        link - 连接参数配置，分别是主设备主链接/主设备备链接/备设备主链接/备设备备链接
 *               每个链接包括ip, 端口, 用户名, 密码，其中密码可以为空
 *
 *  @param  -[in]  const ACE_TCHAR*  szFuncName: [function name]
 *  @param  -[in,out]  void**  pPFN: [function pointer]
 *
 *  @version     06/26/2008  chenzhiquan  Initial Version.
 */
void CICGDevice::CreateLinks( string &strConnParam , DRVHANDLE hDevice)
{
	//默认不使用异步写值方式,用户参数第0位表示是否使用异步写值，0为不使用
	Drv_SetUserData(hDevice, 0, 0);
	//连接之间用分号隔断
	string strTemp = "";
	strConnParam += ";"; // 补上一个分号
	int nPos = strConnParam.find(';');
	while(nPos != string::npos)
	{
		// 处理从字符串开始到第一个';'的字符串
		string strOneParam = strConnParam.substr(0, nPos);
		// 除去这一个待解析的参数
		strConnParam = strConnParam.substr(nPos + 1);
		nPos = strConnParam.find(';');

		// 两个';'之间无字符，continue
		if(strOneParam.empty())
			continue;

		int nPosPart = strOneParam.find('=');
		// 没有'='，不符合参数规则，continue
		if(nPosPart == string::npos)
		{
			CV_WARN(g_CVLogICG,-1,"CICGDevice::CreateLinks strConnParam %s not contain =.", strConnParam.c_str());
			continue;
		}

		// 获取到某个参数名称和值
		string strParamName = strOneParam.substr(0, nPosPart); // e.g. parity
		string strParamValue = strOneParam.substr(nPosPart + 1); // e.g. N

		// 只解析链接配置
		if(strParamName.compare("link") != 0 && strParamName.compare("asyncwrite") != 0)
		{
			CV_WARN(g_CVLogICG,-1,"CICGDevice::CreateLinks strConnParam %s not contain link or asyncwrite.", strConnParam.c_str());
			continue;
		}
		else if(strParamName.compare("asyncwrite") == 0)
		{
			int nAsyncWritePos = strParamValue.find(';');
			string strValTmp = strParamValue.substr(0, nAsyncWritePos);
			int nAsyncWrite = (atoi(strValTmp.c_str()) == 0)? 0 : 1;
			CV_INFO(g_CVLogICG,"Set asyncwrite parameter:%d.", nAsyncWrite);
			Drv_SetUserData(hDevice, 0, nAsyncWrite);
			continue;
		}
		// link
		strParamValue += "/";
		int nLinkPos = strParamValue.find('/');
		CSymLink *pSymLink = NULL;
		while (nLinkPos != string::npos)
		{
			// 将弃用的pSymLink释放
			if (pSymLink)
			{
				delete pSymLink;
				pSymLink = NULL;
			}

			// 处理一个链接
			strTemp = strParamValue.substr(0, nLinkPos);
			// 除去这一个链接
			strParamValue = strParamValue.substr(nLinkPos + 1);
			nLinkPos = strParamValue.find('/');
			
			// 新建一个pSymLink
			pSymLink = new CSymLink;
			// 第一个参数:IP
			int nParamPos = strTemp.find(',');
			if (nParamPos == string::npos)
				continue;
			pSymLink->m_strIP = strTemp.substr(0, nParamPos);
			strTemp = strTemp.substr(nParamPos + 1);

			// 第二个参数:Port
			nParamPos = strTemp.find(',');
			if (nParamPos == string::npos)
				continue;
			pSymLink->m_strPort = strTemp.substr(0, nParamPos);
			strTemp = strTemp.substr(nParamPos + 1);

			// 第三个参数:UserName
			nParamPos = strTemp.find(',');
			if (nParamPos == string::npos)
				continue;
			pSymLink->m_strUser = strTemp.substr(0, nParamPos);
			strTemp = strTemp.substr(nParamPos + 1);

			// 第四个参数:Password
			nParamPos = strTemp.find(',');
			pSymLink->m_strPwd = strTemp.substr(0, nParamPos);
			
			//如果是单连接设备并主机非活动节点，不进行连接
			if (RM_STATUS_ACTIVE == (m_nRMStatus = Drv_IsActiveHost()))
			{
				//创建链接
				pSymLink->CreateLink();
			}
			
			//放入列表
			m_lstLinks.push_back(pSymLink);
			pSymLink = NULL;
		}
	}

}

/**
 *  写数据，选择冗余链接中的一条连通通道，将数据下发.
 *  @param  -[in]  DRVHANDLE hDataBlock: [数据块句柄]
 *  @param  -[in]  CWriteDataBuffer &tagDataBuffer: [写数据信息]
 *
 *  @return  读取结果 0-成功
 *
 *  @version     07/20/2012   Initial Version.
 */
long CICGDevice::WriteData(  DRVHANDLE hDataBlock, CWriteDataBuffer &tagDataBuffer, bool bAysncWrite)
{
	CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDataBlock);
	string strTagName = pDataBlock->pszName;
	string strTagAddr = pDataBlock->pszAddress;

	CV_DEBUG(g_CVLogICG,"CICGDevice::WriteData dev %s tagaddr %s", m_strDeviceName.c_str(), strTagAddr.c_str())

	long lRet = EC_ICG_DISCONNECT;
	// 按照优先级检查每一个link
	for (list<CSymLink*>::iterator iterLink = m_lstLinks.begin();
		iterLink != m_lstLinks.end();
		iterLink++)
	{
		CSymLink *pSymLink = *iterLink;
		// link为空或未连接成功，跳过
		if (pSymLink == NULL || pSymLink->m_pLink == NULL || !pSymLink->IsConnected())
			continue;

		string strWorkName;
		string strHeartName;
		string strStatusName = CICGGroup::GetStatusName(strTagAddr, strWorkName, strHeartName);
		// 根据statusname选择group
		map<string, CICGGroup*>::iterator iterGroup = m_mapGroups.find(strStatusName);
		// 没有匹配的Group
		if (iterGroup == m_mapGroups.end())
		{
			CV_WARN(g_CVLogICG,-1,"tag %s not found in device %s, and write value failed", m_strDeviceName.c_str(), strTagAddr.c_str());
			lRet = EC_ICG_DATABLOCK_NOEXIST;
			break;
		}

		CICGGroup *pGroup = iterGroup->second;
		// 状态正常则写数据
		CV_DEBUG(g_CVLogICG,"Try to write to dev %s tagaddr %s on link (%s:%s,%s)", m_strDeviceName.c_str(), strTagAddr.c_str(), 
			pSymLink->m_strIP.c_str(), pSymLink->m_strPort.c_str(), pSymLink->m_strUser.c_str())
		if (pGroup->Ready(pSymLink))
		/*if(true)*/
		{
			lRet = pGroup->WriteData(pSymLink, strTagName, tagDataBuffer, bAysncWrite);
			// 写完后退出
			break;
		}
	}

	return lRet;

}

const char * GetPureAddress(const char *pAddr)
{
	static char szAddress[ICV_IOADDR_MAXLEN + 1];
	memset(szAddress, 0, sizeof(szAddress));
	const char *pTemp = strstr(pAddr, ":");
	if (pTemp != NULL)
	{
		const char *pTemp1 = strstr(pTemp + 1, ":");
		if (NULL == pTemp1)
			pTemp1 = strstr(pTemp + 1, "#");

		if (pTemp1 != NULL)
			memcpy(szAddress, pTemp + 1, pTemp1 - pTemp - 1);
		else
			strncpy(szAddress, pTemp + 1, ICV_IOADDR_MAXLEN);
	}
	return szAddress;
}

CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum,
	TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
{
	if (NULL == pOutDevTags || NULL == pnTagsNum || \
		NULL == pTagGrps || NULL == pnTagGrpsNum)
		return EC_ICV_INVALID_PARAMETER;
	
	//对于点设备，一个点就是一个数据块，因此点的个数与数据块的个数一致
	*pnTagsNum = nTagsNum;
	*pnTagGrpsNum = nTagsNum;

	memset(pTagGrps, 0, *pnTagGrpsNum * sizeof(TagGroupInfo));
	std::copy(pDevTags, pDevTags + nTagsNum, pOutDevTags);

	
	char szGroupName[ICV_DATABLOCKNAME_MAXLEN + 1];
	memset(szGroupName, 0, sizeof(szGroupName));

	for (int i = 0; i < nTagsNum; ++i)
	{
		pTagGrps[i].nElemBits = 8;
		//icg是点设备，所以块上不能有扫描周期，否则会造成重复扫描，消耗系统CPU
		pTagGrps[i].nPollRate = 0;
		strncpy(pTagGrps[i].szAddress, GetPureAddress(pDevTags[i].szAddress), ICV_IOADDR_MAXLEN);
		sprintf(pTagGrps[i].szGroupName, "group%d", i);
		sprintf(pOutDevTags[i].szGrpName, "group%d", i);
		
		switch(pDevTags[i].nDataType)
		{
		case TAG_DATATYPE_BOOL:
		case TAG_DATATYPE_CHAR:
		case TAG_DATATYPE_USINT:
		case TAG_DATATYPE_SINT:
		case TAG_DATATYPE_BYTE:
			strcpy(pTagGrps[i].szParam1, "int");
			pTagGrps[i].nElemNum = 1;
			break;
		case TAG_DATATYPE_INT:
		case TAG_DATATYPE_UINT:
		case TAG_DATATYPE_WORD:
			strcpy(pTagGrps[i].szParam1, "int");
			pTagGrps[i].nElemNum = 2;
			break;
		case TAG_DATATYPE_TIME:
		case TAG_DATATYPE_UDINT:
		case TAG_DATATYPE_DWORD:
			strcpy(pTagGrps[i].szParam1, "unsigned");
			pTagGrps[i].nElemNum = 4;
			break;
		case TAG_DATATYPE_DINT:
			strcpy(pTagGrps[i].szParam1, "int");
			pTagGrps[i].nElemNum = 4;
			break;
		case TAG_DATATYPE_REAL:
			strcpy(pTagGrps[i].szParam1, "float");
			pTagGrps[i].nElemNum = 4;
			break;
		case TAG_DATATYPE_LREAL:
		case TAG_DATATYPE_LINT:
		case TAG_DATATYPE_ULINT:
		case TAG_DATATYPE_LWORD:
			strcpy(pTagGrps[i].szParam1, "double");
			pTagGrps[i].nElemNum = 8;
			break;
		case TAG_DATATYPE_BLOB:
			{
				strcpy(pTagGrps[i].szParam1, "blob");
				pTagGrps[i].nElemNum = 256;
				const char *pTemp = strstr(pDevTags[i].szAddress, "#");
				if (pTemp != NULL)
				{
					pTagGrps[i].nElemNum = atoi(pTemp + 1);
				}
			}
			break;
		case TAG_DATATYPE_STRING:
			{
				strcpy(pTagGrps[i].szParam1, "string");
				pTagGrps[i].nElemNum = 256;
				const char *pTemp = strstr(pDevTags[i].szAddress, "#");
				if (pTemp != NULL)
				{
					pTagGrps[i].nElemNum = atoi(pTemp + 1);
				}
			}
			break;
		default:
			break;
		}
	}
	return DRV_SUCCESS;
}

CVDRIVER_EXPORTS long OnBatchUpdateData(int& nMsTimeOut,int& DataSize)
{
	nMsTimeOut = 50;
	DataSize = 8192;
	return 0; 
}


