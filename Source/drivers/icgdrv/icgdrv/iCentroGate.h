/************************************************************************
*	Filename:		iCentroGate.cpp
*	Copyright:		Shanghai Baosight Company Software Co., Ltd.
*
*	Description:	iCG驱动
*
*	@author:		Li Baoqin
*	@version		2013-01-12	Li Baoqin	Initial Version
*************************************************************************/
#ifndef ICENTROGATE_H
#define ICENTROGATE_H

#include <string.h>
#include <stdlib.h>
#include "driversdk/cvdrivercommon.h"
#include <string>
#include <vector>
#include <set>
#include <map>
#include <list>
#include "symlinkapi/SymLinkApi.h"


using namespace std;

// icg内部错误码
enum
{
	EC_ICG_DEVICE_NOEXIST = 17001,	// 找不到设备
	EC_ICG_DATABLOCK_NOEXIST,		// 找不到数据块
	EC_ICG_DATATYPE_NOEXIST,		// 不存在的数据类型
	EC_ICG_DISCONNECT,				// 连接断开
};

// 获取api模块状态
string GetConnStatusInfo(int nStatus);
// 获取api错误码信息
string GetErrInfo(int nRet);
// 获取api状态信息
string GetCmdStatus(int nRet);
// 添加设备
long AddDevice(DRVHANDLE hDevice);


/**
*  icg连接.
*  Description: 维护一条连接链路，包括创建，连接，重连和断开连接
*
*  @version  2013-01-12	Li Baoqin	Initial Version
*/
class CSymLink
{
public:
	string m_strIP;		// 连接IP
	string m_strPort;	// 连接端口
	string m_strUser;	// 连接用户名
	string m_strPwd;	// 连接密码
	CSymLinkApi *m_pLink; // 连接指针
private:
	int m_nStatus;		// 连接状态
public:
	CSymLink(): m_pLink(NULL), m_nStatus(SYMAPI_STATUS_DISCONNECT){}
	~CSymLink();
	void CreateLink();
	void FreeLink();
	bool IsConnected(){ return m_nStatus == SYMAPI_STATUS_OK; }
	// 回调函数，创建连接时注册，关闭连接时注销，连接状态改变时调用
	static void OnLinkStatusChange(void* ptrSymLinkApi, void* ptrSymLink, int nStatus);
	static void OnDataChange( void* ptr1, void* ptr2, int nChangeCount, int* pChangeHandle );
};

/**
*  写数据的结构体.
*  Description: 将写数据的参数做成集合，便于传递
*
*  @version  2013-01-12	Li Baoqin	Initial Version
*/
class CWriteDataBuffer
{
public:
	int m_nTagByteOffset;	// 字节偏移
	int m_nTagBitOffset;	// 位偏移
	int m_nCmdDataLenBits;	// 写下的位数
	char *m_szCmdData;		// 写下的数据

public:
	CWriteDataBuffer(int nTagByteOffset, int nTagBitOffset, char *szCmdData, int nCmdDataLenBits)
		:m_nTagByteOffset(nTagByteOffset), m_nTagBitOffset(nTagBitOffset), m_szCmdData(szCmdData), m_nCmdDataLenBits(nCmdDataLenBits)
	{
	}
};

/**
*  数据点.
*  Description: 一个数据点对应icg中一个点，读写控制都在这里实现
*
*  @version  2013-01-12	Li Baoqin	Initial Version
*/
class CICGData
{
public:

	DRVHANDLE m_hDataBlock;		// 数据块句柄，由DIT维护
	DRVHANDLE m_hDevice;		// 设备句柄，有DIT维护
	string    m_strDataAddress;	// 数据点地址，同时也是数据地址
	string    m_strDeviceName;	// 设备名，打印时不用重新解析

	CICGData(DRVHANDLE hDevice, DRVHANDLE hDataBlock, string &strDataAddr);
	long ReadData(CSymLink* pLink, int nDBStatus);
	long WriteData(CSymLink* pLink, CWriteDataBuffer &tagDataBuffer, bool bAysncWrite);
	long CastBuff2LinkData(CWriteDataBuffer &tagDataBuffer, SymLinkApiData &tagData, string &strDataType);
};

/**
*  数据组.
*  Description: 一个数据点对应icg中一个点，读写控制都在这里实现
*
*  @version  2013-01-12	Li Baoqin	Initial Version
*/
class CICGGroup
{
public:
	string m_strStatusName;				// 组状态名，也是数据地址；状态异常，该组数据都异常
	string m_strIOWork;
	string m_strIOHeart;

private:
	map<string, CICGData*> m_mapData;	// 数据映射，一个组对应一个逻辑设备

public:
	~CICGGroup();
	// 从点名获取状态名
	static string GetStatusName(string &strTagAddr, string &strWorkName, string &strHeartName);
	// 增加一个点
	long AddData(DRVHANDLE hDevice, DRVHANDLE hDataBlock);
	// 删除一个点
	void DelData(string &strTagName);
	// 检查该链路的组状态是否正常
	bool Ready(CSymLink* pLink);

	long ReadData(CSymLink* pLink, int nDBStatus = DATA_STATUS_OK);
	long WriteData(CSymLink* pLink, string &strTagName, CWriteDataBuffer &tagDataBuffer, bool bAysncWrite);
};

/**
*  设备.
*  Description: 一个设备对应一个网关，支持多链路冗余
*
*  @version  2013-01-12	Li Baoqin	Initial Version
*/
class CICGDevice
{
public:
	DRVHANDLE m_hDevice;		// 设备句柄
	string m_strDeviceName;		// 设备名
	list<CSymLink*> m_lstLinks;	// 冗余链路

	
private:
	map<string, CICGGroup*> m_mapGroups;
	int m_nRMStatus;
public:
	CICGDevice(DRVHANDLE hDevice, CVDEVICE *pDevice);
	~CICGDevice();
	void CreateLinks(string &strConnParam, DRVHANDLE hDevice);
	long AddData(DRVHANDLE hDevice, DRVHANDLE hDataBlock);
	void DelData(DRVHANDLE hDataBlock);
	long ReadData();
	long WriteData( DRVHANDLE hDataBlock, CWriteDataBuffer &tagDataBuffer, bool bAysncWrite);
	long WriteHeartData();
};

#endif
