cmake_minimum_required(VERSION 3.10)

PROJECT (icgdrv)

INCLUDE($ENV{DRDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
IF(WIN32)
#ADD_DEFINITIONS(-DSXPLAT_LINUX)
ELSE(WIN32)
ADD_DEFINITIONS(-DSXPLAT_LINUX)
ENDIF(WIN32)

#Setting Source Files
SET(SRCS ${SRCS} iCentroGate.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME icgdrv)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS drdrivercommon symlinkapi drcomm)

SET(SPECOUTDIR /drivers/icgdrv)
INCLUDE($ENV{DRDIR}CMakeSpecOutPath)
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)

