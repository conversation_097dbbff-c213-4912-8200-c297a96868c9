cmake_minimum_required(VERSION 3.10)

PROJECT (internaldrv)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} indrv.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME internaldrv)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS hiredis drdrivercommon drcomm ACE drhdOS)

SET(SPECOUTDIR /drivers/internaldrv)
INCLUDE($ENV{DRDIR}CMakeSpecOutPath)
INCLUDE($ENV{DRDIR}CMakeCommonLib)
