/************************************************************************
 *	Filename:		internaldrv.cpp
 *	Copyright:		Shanghai Baosight Software Co., Ltd.
 *
 *	Description:	内部虚拟驱动实现 .
 *
 *	@author:		
 *	@version		4/10/2025	chenjiankun	Initial Version
 *************************************************************************/

#ifndef _INTERNALDRV_DRIVER_H_
#define _INTERNALDRV_DRIVER_H_

#include "driversdk/cvdrivercommon.h"
#include "common/RMAPIDef.h"
#include "errcode/error_code.h"
#include "cvdefine.h"
#include "cv_datatype.h"
#include <map>
#include <chrono>

using namespace std;
// 设备连接状态
enum DevConnectState
{
    DisConnected = 0,
    Connected = 1
};

// 虚拟设备类
class CInternalDevice
{
public:
    CInternalDevice(DRVHANDLE hDevice) : m_hDevice(hDevice), m_nConnStatus(DevConnectState::DisConnected) {}
    ~CInternalDevice() {}

    DRVHANDLE GetDrvHandle() { return m_hDevice; }
    
    // 设备连接状态
    DevConnectState m_nConnStatus;

    //端口号
    int m_nPort;

private:
    DRVHANDLE m_hDevice;
};

#endif //_INTERNALDRV_DRIVER_H_