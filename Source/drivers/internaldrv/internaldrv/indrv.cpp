/************************************************************************
 *	Filename:		internaldrv.cpp
 *	Copyright:		Shanghai Baosight Software Co., Ltd.
 *
 *	Description:	内部虚拟驱动实现 .
 *
 *	@author:		
 *	@version		4/10/2025	chenjiankun	Initial Version
 *************************************************************************/

#include "indrv.h"
#include "common/CVLog.h"
#include "TypeCast.h"
#ifdef _WIN32
#include <windows.h>
#else
#include <unistd.h>
#endif // _WIN32

#include <sstream>
#include <set>
#include "common/CommHelper.h"
#include <map>
#include <algorithm>
#include <vector>
#include <iostream>
#include <unordered_map>
#include <hiredis/hiredis.h>

CCVLog g_drLogInternalDrv;
#define INTERNAL_DRIVE_NAME "internaldrv"
#define BITS_PER_BYTE 8                      // 一个字节占用的位数
#define INTERNAL_STRING_BLOCK_SIZE 256       // string类型的数据块默认长度
#define INTERNAL_DEFAULT_DEVICE_LOOPTIME 500 // 默认device循环时间 500ms
#define STRING_DEFAULT_MAX_LENGTH 82         //沿用天行的默认最大长度80+2,用于string类型的点的第一个字节

// 保存设备句柄
std::vector<DRVHANDLE> g_deviceHandle;
// 保存数据块轮询周期
std::unordered_map<std::string, int> g_tagScanIntv;

int32 ParseIoAddr(char *szIoAddr, char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN], int32 &nLength);
/**
 *  初始化函数，驱动EXE启动时该函数被调用，可在该函数中实现自定义初始化操作.
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version
 */
CVDRIVER_EXPORTS long Begin()
{
    g_drLogInternalDrv.SetLogFileNameThread(INTERNAL_DRIVE_NAME);
    return DRV_SUCCESS;
}

/**
 *  初始化函数，驱动EXE启动时该函数被调用，可在该函数中实现自定义初始化操作.
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version
 */
CVDRIVER_EXPORTS long Initialize()
{
    CV_INFO(g_drLogInternalDrv, "internaldrv driver Initialize...");
    return DRV_SUCCESS;
}

/**
 *  驱动EXE退出时该函数被调用.
 *  在该函数中可以释放自定义资源、断开设备连接等操作.
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version
 */
CVDRIVER_EXPORTS long UnInitialize()
{
    CV_INFO(g_drLogInternalDrv, "internaldrv driver UnInitialize...");
    g_drLogInternalDrv.StopLogThread();
    return DRV_SUCCESS;
}

/**
 * 从连接参数中解析Redis端口
 * @param connParam 连接参数字符串, 格式如 "port=6380;"
 * @param port 解析出的端口号将存储在此变量中
 * @param defaultPort 默认端口号，当解析失败时使用
 */
void ParseRedisPort(const char *connParam, int &port, int defaultPort = 6380)
{
    // 如果参数为空，设置默认端口
    if (!connParam || strlen(connParam) == 0)
    {
        port = defaultPort;
        return;
    }

    std::string param(connParam);
    std::string prefix = "port=";

    // 查找"port="
    size_t pos = param.find(prefix);
    if (pos == std::string::npos)
    {
        port = defaultPort;
        return;
    }

    // 获取端口值
    std::string portStr = param.substr(pos + prefix.length());

    // 如果有分隔符如分号，截取到分隔符前
    size_t endPos = portStr.find_first_of(";");
    if (endPos != std::string::npos)
        portStr = portStr.substr(0, endPos);

    // 转换为整数
    try
    {
        port = std::stoi(portStr);
    }
    catch (...)
    {
        CV_WARN(g_drLogInternalDrv, -1, "无法解析Redis端口 '%s', 使用默认值 %d", portStr.c_str(), defaultPort);
        port = defaultPort;
    }
}

/**
 *  添加设备时该函数被调用.
 *  该函数主要针对非tcp连接设备，用户可以通过设备句柄获取设备连接参数，初始化连接设备
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version
 */
CVDRIVER_EXPORTS long OnDeviceAdd(DRVHANDLE hDevice)
{
    if (NULL == hDevice)
        return EC_ICV_DRIVER_INVALID_PARAMETER;

    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    CInternalDevice *pInternalDevice = new CInternalDevice(hDevice);
    // 初始化端口值
    ParseRedisPort(pDevice->pszConnParam, pInternalDevice->m_nPort, 6380);
    CV_INFO(g_drLogInternalDrv, "设备 '%s' Redis端口设置为 %d", pDevice->pszName, pInternalDevice->m_nPort);
    Drv_SetUserDataPtr(hDevice, 0, static_cast<void *>(pInternalDevice));

    g_deviceHandle.push_back(hDevice);

    // 虚拟驱动不需要实际连接，直接设置设备状态为正常
    pInternalDevice->m_nConnStatus = DevConnectState::Connected;
    Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);

    CV_INFO(g_drLogInternalDrv, "虚拟设备添加成功: %s", pDevice->pszName);

    return DRV_SUCCESS;
}

/**
 *  删除设备时该函数被调用.
 *
 *  @param  -[in]  DRVHANDLE hDevice: [设备句柄]
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version
 */
CVDRIVER_EXPORTS long OnDeviceDelete(DRVHANDLE hDevice)
{
    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    CInternalDevice *pInternalDevice = static_cast<CInternalDevice *>(Drv_GetUserDataPtr(hDevice, 0));
    if (pInternalDevice != NULL)
    {
        CV_DEBUG(g_drLogInternalDrv, "OnDeviceDelete device name[%s]", pDevice->pszName);
        Drv_UpdateDevStatus(pInternalDevice->GetDrvHandle(), DEV_STATUS_BAD);
        delete pInternalDevice;
    }
    return DRV_SUCCESS;
}

/**
 *  自组块函数，采用符号模式，一个变量就是一个块，并创建块的属性
 *
 *  @param
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version
 */
CVDRIVER_EXPORTS long TagsToGroups(const TagInfo *pDevTags, int nTagsNum,
                                   TagInfo *pOutDevTags, unsigned int *pnTagsNum, TagGroupInfo *pTagGrps, unsigned int *pnTagGrpsNum)
{
    if (NULL == pOutDevTags || NULL == pnTagsNum || NULL == pTagGrps || NULL == pnTagGrpsNum)
        return -1;

    // 设定需要框架更新的点，没有筛选，读到多少配置，就使用多少点。没有填写地址的点已经被过滤了
    std::copy(pDevTags, pDevTags + nTagsNum, pOutDevTags);
    *pnTagsNum = nTagsNum;

    // 设定需要读取的数据块分组
    memset(pTagGrps, 0, *pnTagGrpsNum * sizeof(TagGroupInfo));
    map<string, string> checkGrpAddress; // 用来检查是否有地址重名的TagGrps
    // TagGroupInfo对应数据块，现在是一个地址一个数据块，都放到一个线程里跑
    for (int i = 0, indexGrp = 0; i < nTagsNum; ++i)
    {
        string tempGrpName = "group_" + to_string(pOutDevTags[i].nTagID); // group_tagid 作为group的名字
        // 检查szAddress是否重复
        map<string, string>::iterator resultIte = checkGrpAddress.find(pOutDevTags[i].szAddress);
        if (resultIte != checkGrpAddress.end()) // 已经存在的不用再插入
        {                                       // 相同的地址，给tag点的szGrpName复用已经存在的name，相同szGrpName的tag，在更新值时，使用相同地址的DataBlock进行更新
            Safe_CopyString(pOutDevTags[i].szGrpName, resultIte->second.c_str(), ICV_DATABLOCKNAME_MAXLEN + 1);
            continue;
        }
        else
        {
            Safe_CopyString(pOutDevTags[i].szGrpName, tempGrpName.c_str(), ICV_DATABLOCKNAME_MAXLEN + 1);
            checkGrpAddress.insert(make_pair(pOutDevTags[i].szAddress, pOutDevTags[i].szGrpName));
        }

        Safe_CopyString(pTagGrps[indexGrp].szGroupName, tempGrpName.c_str(), ICV_DATABLOCKNAME_MAXLEN + 1);
        Safe_CopyString(pTagGrps[indexGrp].szAddress, pOutDevTags[i].szAddress, ICV_IOADDR_MAXLEN + 1);

        switch (pOutDevTags[i].nDataType)
        {
        case TAG_DATATYPE_LREAL:
        case TAG_DATATYPE_LINT:
        case TAG_DATATYPE_ULINT:
        case TAG_DATATYPE_LWORD:
            pTagGrps[indexGrp].nElemNum = 8;
            break;

        case TAG_DATATYPE_INT:
        case TAG_DATATYPE_UINT:
        case TAG_DATATYPE_WORD:
            pTagGrps[indexGrp].nElemNum = 2;
            break;

        case TAG_DATATYPE_REAL:
        case TAG_DATATYPE_UDINT:
        case TAG_DATATYPE_DINT:
        case TAG_DATATYPE_DWORD:
            pTagGrps[indexGrp].nElemNum = 4;
            break;

        case TAG_DATATYPE_BOOL:
        case TAG_DATATYPE_SINT:
        case TAG_DATATYPE_USINT:
        case TAG_DATATYPE_BYTE:
        case TAG_DATATYPE_CHAR:
            pTagGrps[indexGrp].nElemNum = 1;
            break;
        case TAG_DATATYPE_STRING:
        // 暂定为256
			pTagGrps[indexGrp].nElemNum = INTERNAL_STRING_BLOCK_SIZE;
			break;
        case TAG_DATATYPE_UDT:
        {
            // 和TagDataBlockBuilder一样，尝试从地址中解析长度
            char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN];
            int32 nLength = 0;

            // 解析地址字符串，尝试从中获取长度信息
            if (ParseIoAddr(pOutDevTags[i].szAddress, szAddr, nLength) == ICV_SUCCESS && nLength > 0)
            {
                pTagGrps[indexGrp].nElemNum = nLength;
                CV_DEBUG(g_drLogInternalDrv, "UDT类型数据块 '%s' 地址指定长度为: %d",
                         pTagGrps[indexGrp].szGroupName, nLength);
            }
            else
            {
                // 如果解析失败或未指定长度，使用默认长度 (1024)
                pTagGrps[indexGrp].nElemNum = DEFAULT_UDT_LEN;
                CV_DEBUG(g_drLogInternalDrv, "UDT类型数据块 '%s' 使用默认长度: %d",
                         pTagGrps[indexGrp].szGroupName, DEFAULT_UDT_LEN);
            }
        }
        break;
        default:
            break;
        }
        pTagGrps[indexGrp].nElemBits = BITS_PER_BYTE;
        pTagGrps[indexGrp].nPollRate = 0; // 点的刷新率就是数据块的刷新率
        // type 先存tag点的type 使用c++11的标准，to_string也没问题
        strcpy(pTagGrps[indexGrp].szGroupType, std::to_string(pOutDevTags[i].nDataType).c_str());
        indexGrp++;
        *pnTagGrpsNum = indexGrp; // 记录pnTagGrpsNum数
        CV_DEBUG(g_drLogInternalDrv, "Tag \"%s\" details: address %s group %s nBitOffset %d", pOutDevTags[i].szTagName, pOutDevTags[i].szAddress, pOutDevTags[i].szGrpName, pOutDevTags[i].nBitOffSet);
    }
    return DRV_SUCCESS;
}

/**
 *  在device中添加数据块，准备注册用的数据
 *
 *  @param
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version
 */
CVDRIVER_EXPORTS long OnDataBlockAdd(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDatablock);

    // 虚拟驱动不需要实际连接，直接设置数据块状态为正常
    Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_OK);

    // 为数据块设置一个初始值
    char buffer[INTERNAL_STRING_BLOCK_SIZE] = {0};
    Drv_UpdateBlockData(hDevice, hDatablock, buffer, 0, pDataBlock->nBlockDataSize, DATA_STATUS_OK, NULL);

    CV_INFO(g_drLogInternalDrv, "虚拟数据块添加成功: %s, 设备: %s", pDataBlock->pszName, pDevice->pszName);

    return DRV_SUCCESS;
}

/**
 *  删除数据块时的操作
 *
 *  @param
 *
 *  @return DRV_SUCCESS: 执行成功
 *
 *  @version
 */
CVDRIVER_EXPORTS long OnDataBlockDelete(DRVHANDLE hDevice, DRVHANDLE hCfgDataBlock)
{
    Drv_UpdateBlockStatus(hDevice, hCfgDataBlock, DATA_STATUS_COMM_FAILURE);
    return DRV_SUCCESS;
}

void GetInternalNameFromAddress(string &Addr, const char *pszAddress)
{
    string strInAddress = pszAddress;
    // 全部转为大写
    transform(strInAddress.begin(), strInAddress.end(), strInAddress.begin(), [](unsigned char c)
              { return std::toupper(c); });
    string straddr = strInAddress;
    size_t pos = straddr.find_first_of(':');
    if (pos != string::npos && pos != straddr.size() - 1)
    {
        Addr = straddr.substr(pos + 1);
    }
    else
    {
        Addr = strInAddress;
    }
    pos = Addr.find_first_of('#');
    if (pos != string::npos)
        Addr = Addr.substr(0, pos);
}

/*
 *  @version     Initial Version.
 */
CVDRIVER_EXPORTS long OnWriteCmd(DRVHANDLE hDevice, DRVHANDLE hDatablock, int nTagByteOffset, int nTagBitOffset, char *szCmdData, int nCmdDataLenBits)
{
    CV_DEBUG(g_drLogInternalDrv, "recv write cmd");
    CVDEVICE *pDevice = Drv_GetDeviceInfo(hDevice);
    CVDATABLOCK *pDataBlock = Drv_GetDataBlockInfo(hDatablock);
    CInternalDevice *pInternalDevice = static_cast<CInternalDevice *>(Drv_GetUserDataPtr(hDevice, 0));
    if (NULL == pInternalDevice)
        return EC_ICV_DA_NULL_POINTER;

    if (nCmdDataLenBits <= 0 || nCmdDataLenBits / BITS_PER_BYTE > 512)
        return EC_ICV_DA_WRITEMSG_LENGTH_ERROR;

    int nCmdByteLen = nCmdDataLenBits % BITS_PER_BYTE == 0 ? nCmdDataLenBits / BITS_PER_BYTE : nCmdDataLenBits / BITS_PER_BYTE + 1;

    // 日志记录控制设备，数据块，位偏移，内容
    char szBuffer[ICV_BLOBVALUE_MAXLEN];
    unsigned int nHexBufferLen = ICV_BLOBVALUE_MAXLEN;
    memset(szBuffer, 0, sizeof(szBuffer));
    cvcommon::HexDumpBuf((unsigned char *)szCmdData, nCmdByteLen, szBuffer, &nHexBufferLen);

    string strDataType = pDataBlock->pszBlockType;
    CV_INFO(g_drLogInternalDrv, "CTRL:Devive[%s] Block[%s] StartAddr[%s] ByteOffset[%d] BitOffset[%d] Write[%s] WriteLen[%d].",
            pDevice->pszName, pDataBlock->pszName, pDataBlock->pszAddress, nTagByteOffset, nTagBitOffset, szBuffer, nCmdByteLen);

    // 处理string类型数据块，去掉开头设备名和结尾#长度后，作为写入redis的key
    string redisKey;
    GetInternalNameFromAddress(redisKey, pDataBlock->pszAddress);

    // 连接redis
    redisContext *redisConn = redisConnect("127.0.0.1", pInternalDevice->m_nPort);
    CV_DEBUG(g_drLogInternalDrv, "Redis连接成功, 端口: %d", pInternalDevice->m_nPort);
    if (redisConn == NULL || redisConn->err)
    {
        if (redisConn)
        {
            CV_ERROR(g_drLogInternalDrv, -1, "Redis连接错误: %s", redisConn->errstr);
            redisFree(redisConn);
        }
        else
        {
            CV_ERROR(g_drLogInternalDrv, -1, "Redis连接分配错误");
        }
        return -1;
    }
    // 和天行保持一致，写string类型需要添加两字节头信息：
    // 第一个字节表示最大长度，第二个字节表示实际长度，后面是字符串内容（不含结束符）
    if (TAG_DATATYPE_STRING == stoi(strDataType))
    {
        char szCmdDataTemp[ICV_BLOBVALUE_MAXLEN];
        memset(szCmdDataTemp, 0, sizeof(szCmdDataTemp));

        // 第一个字节：最大长度（从数据块定义获取）

        // 和TagDataBlockBuilder一样，尝试从地址中解析长度
        char szAddr[TAG_TOTAL_SEGMENT][ICV_IOADDR_MAXLEN];
        int32 nLength = 0;

        // 解析地址字符串，尝试从中获取长度信息
        if (ParseIoAddr(const_cast<char*>(pDataBlock->pszAddress), szAddr, nLength) == ICV_SUCCESS && nLength > 0)
        {
            //db表中#后面给的最大长度是不加头2字节信息的长度
            szCmdDataTemp[0] = nLength+2;
            CV_DEBUG(g_drLogInternalDrv, "字符串数据块 '%s' 最大长度: %d",
                pDataBlock->pszName, szCmdDataTemp[0]);
        }
        else
        {
            // 如果解析失败或未指定长度，使用默认长度 (80+2)
            szCmdDataTemp[0] = STRING_DEFAULT_MAX_LENGTH;
            CV_DEBUG(g_drLogInternalDrv, "字符串数据块 '%s' 最大长度使用默认长度: %d",
                pDataBlock->pszName, szCmdDataTemp[0]);
        }
        //这边修改一下逻辑，因为现在从采集服务下发下来的buffer可能是后面填充了很多0，所以需要用strlen计算一下实际长度作为actualLength
        //为防止下来的buffer不是以\0结尾的字符串，直接使用strlen会导致实际长度不准确，取strlen(szCmdData)与nCmdByteLen中的较小值
        unsigned char actualLength = static_cast<unsigned char>(std::min(strlen(szCmdData), static_cast<size_t>(nCmdByteLen)));

        // 第二个字节：实际长度
        szCmdDataTemp[1] = actualLength;

        // 拷贝实际内容（不包含结尾的\0）
        memcpy(szCmdDataTemp + 2, szCmdData, actualLength);

        // 更新总长度和指针
        nCmdByteLen = actualLength + 2;
        szCmdData = szCmdDataTemp;

        CV_INFO(g_drLogInternalDrv, "字符串数据添加头信息：最大长度=%d, 实际长度=%d",
                szCmdDataTemp[0], szCmdDataTemp[1]);
    }
    redisReply *reply = (redisReply *)redisCommand(redisConn, "SET %s %b",
                                                   redisKey.c_str(), szCmdData, nCmdByteLen);

    bool success = (reply != NULL && reply->type != REDIS_REPLY_ERROR);
    if (reply)
        freeReplyObject(reply);
    redisFree(redisConn);

    if (!success)
    {
        CV_ERROR(g_drLogInternalDrv, -1, "数据写入Redis失败, key: %s", redisKey.c_str());
        return -1;
    }

    CV_DEBUG(g_drLogInternalDrv, "数据成功写入Redis, key: %s", redisKey.c_str());

    // // 更新数据块状态
    // Drv_UpdateBlockData(hDevice, hDatablock, szCmdData, 0, nCmdByteLen, DATA_STATUS_OK, NULL);
    // Drv_UpdateBlockStatus(hDevice, hDatablock, DATA_STATUS_OK);
    // Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);

    return DRV_SUCCESS;
}

/**
 *  设备定时器回调函数
 */
CVDRIVER_EXPORTS long OnDeviceTimer(DRVHANDLE hDevice)
{
    Drv_UpdateDevStatus(hDevice, DEV_STATUS_GOOD);
    CV_DEBUG(g_drLogInternalDrv, "OnDeviceTimer start Device name[%s]", Drv_GetDeviceInfo(hDevice)->pszName);
    // 虚拟驱动不需要实际连接，直接返回成功
    return DRV_SUCCESS;
}

/**
 *  数据块定时器回调函数
 */
CVDRIVER_EXPORTS long OnDataBlockTimer(DRVHANDLE hDevice, DRVHANDLE hDatablock)
{
    // 为了避免驱动框架打印大量错误日志，因此添加该空方法
    return DRV_SUCCESS;
}

/**
 * 获取驱动框架版本
 */
CVDRIVER_EXPORTS long GetDrvFrameVersion()
{
    return 2;
}