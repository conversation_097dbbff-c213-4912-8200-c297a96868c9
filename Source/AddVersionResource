SET(<PERSON>Y<PERSON><PERSON><PERSON> $ENV{MYVERSION})
SET(MY<PERSON>BVERSION $ENV{MYSUBVERSION})

IF (NOT MYVERSION) 
IF(UNIX)
	SET(MYVERSION 3.20.0)
ELSE(UNIX)
    SET(MYVE<PERSON>ION 3.24.0)
ENDIF(UNIX)
	# MESSAGE("MYVERSION:\"" ${MYVERSION} "\"")
ENDIF(NOT MYVERSION)

IF (NOT MYSUBVERSION) 
	# SET(MYSUBVERSION $ENV{BUILD_NUMBER})
	SET(MYSUBVERSION 0)
ENDIF(NOT MYSUBVERSION)

IF (NOT MYSUBVERSION) 
	SET(MYSUBVERSION 0)
ENDIF(NOT MYSUBVERSION)

SET(TARGET_VERSION "${MYVERSION}.${MYSUBVERSION}")
SET(TARGET_API_VERSION "${MYVERSION}")

IF(UNIX)
#	SET_TARGET_PROPERTIES(${TARGET_NAME} PROPERTIES SOVERSION ${TARGET_API_VERSION} VERSION ${TARGET_VERSION} )
ELSE(UNIX)
	SET(TARGET_VERSION_SEPA	${TARGET_VERSION}) 
	IF( CMAKE_SIZEOF_VOID_P EQUAL 8 )
		SET(RC_FILE_DESC "${PROJECT_NAME} X64")
	ELSE( CMAKE_SIZEOF_VOID_P EQUAL 8 )
		SET(RC_FILE_DESC "${PROJECT_NAME}")
	ENDIF( CMAKE_SIZEOF_VOID_P EQUAL 8 )

	EXECUTE_PROCESS(COMMAND "cmd" " /C date /T" OUTPUT_VARIABLE RESULT)
	STRING(REGEX REPLACE "(....)[-/](..)[-/](..).*" "\\1" RC_YEAR ${RESULT})
	STRING(REGEX REPLACE "\\." "," TARGET_VERSION_SEPA ${TARGET_VERSION_SEPA})
	FILE(READ $ENV{DualiCVSRCDIR}/versionrc.template versioncont)
	STRING(REGEX REPLACE "RC_YEAR" ${RC_YEAR} versioncont ${versioncont})
	STRING(REGEX REPLACE "RC_FILE_VERSION" ${TARGET_VERSION_SEPA} versioncont ${versioncont})
	STRING(REGEX REPLACE "RC_FILE_DESC" ${RC_FILE_DESC} versioncont ${versioncont})
	STRING(REGEX REPLACE "RC_FILE_FULLNAME" ${RC_FILE_DESC} versioncont ${versioncont})
	STRING(REGEX REPLACE "RC_PRODUCT_VERSION" ${TARGET_API_VERSION} versioncont ${versioncont})
	FILE(WRITE ${CMAKE_CURRENT_BINARY_DIR}/version.rc ${versioncont})
	SET(SRCS ${SRCS} ${CMAKE_CURRENT_BINARY_DIR}/version.rc)
ENDIF(UNIX)
