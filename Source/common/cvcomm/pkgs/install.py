def postinstall(env, status):
    import os
    import os.path
    import sys
    import shutil
    import stat

    def cvcopy(src, dst):
        """Copy data and mode bits ("cp src dst").

        The destination may be a directory.

        """
        #rename the old file
        newname=''
        if os.path.exists(dst) == True:
            os.chmod(dst, stat.S_IWRITE | stat.S_IREAD)
            old_name = dst.split('.')[0]
            newname = old_name + '_bak.' + dst.split('.')[1]
            os.rename(dst, newname)

        #copy file
        shutil.copy(src, dst)

        #remove the old file
        if newname != '':
            os.remove(newname)


    def cvcopy2(src, dst):
        """Copy data and all stat info ("cp -p src dst").

        The destination may be a directory.

        """

        #rename the old file
        newname=''
        if os.path.exists(dst) == True:
            os.chmod(dst, stat.S_IWRITE | stat.S_IREAD)
            old_name = dst.split('.')[0]
            newname = old_name + '_bak.' + dst.split('.')[1]
            os.rename(dst, newname)

        #copy file
        shutil.copy2(src, dst)

        #remove the old file
        if newname != '':
            os.remove(newname)

    def cvrmtree(path, ignore_errors=False, onerror=None):
        """Recursively delete a directory tree.
        """
        shutil.rmtree(path, ignore_errors, onerror)

    def cvcopytree(src, dst, symlinks=False, ignore=None):
        shutil.copytree(src, dst, symlinks, ignore)
        
    try:
        #
        icvhome = env.target_root;
        exeDir =  icvhome + "/Executable/";

        if not os.path.exists(exeDir + "/Config/"):
            os.makedirs(exeDir + "/Config/");
        #
        projCfgFilePath = exeDir + "/Config/Projects.xml";
        projActiveProjPath = exeDir + "/Config/ActiveProject.xml";
        if 'win32' in sys.platform:
            path = icvhome.replace('/', '\\');
            parts = path.split('\\');
            parts = [ p for p in parts if p != ''];
            icvhome = '\\'.join(parts);
        else:
            path = icvhome.replace('\\', '/');
            parts = path.split('/');
            parts = [ p for p in parts if p != ''];
            icvhome = '/'.join(parts);
            icvhome = '/'+icvhome;
        #
        if not os.path.exists(projCfgFilePath):
        #
            cfgFile = file(projCfgFilePath,'w')
            cfgFile.write("""<?xml version='1.0' encoding='UTF-8'?>
<Projects bRunDefault='0'>
    <Project path='""" + icvhome + """/Projects/DefaultProject' bRun='0'/>
</Projects>""");
            cfgFile.close()

        defaultProjPath = icvhome + "/Projects/DefaultProject/" 

        if not os.path.exists(projActiveProjPath):
            #
            cfgFile = file(projActiveProjPath,'w')
            cfgFile.write("""<?xml version='1.0' encoding='UTF-8'?>
<ActiveProject ProjectPath='""" + icvhome + """/Projects/DefaultProject' />""");
            cfgFile.close()
    except Exception,e:
        print e;
        
if __name__ ==  "__main__":
    postinstall("d:/","")
	
