// NodeTable.cpp: implementation of the CNodeTable class.
//
//////////////////////////////////////////////////////////////////////


//#include "processdb/Schema_Xml.h"

//#include "common/pugxml.h"
//using namespace pug;

// for ace_file_io
#include <ace/OS_main.h>
#include <ace/FILE_Addr.h>
#include <ace/FILE_Connector.h>
#include <ace/FILE_IO.h>
#include <ace/OS_NS_string.h>
#include <ace/OS_NS_stdio.h>
#include "common/stl_inc.h"

#ifndef cvcomm_EXPORTS
#define cvcomm_EXPORTS
#endif

#ifdef _WIN32
#pragma warning(disable: 4786)
#endif

#include "common/NodeTable.h"

#include "tinyxml/tinyxml.h"
//#include "common/LogHelper.h"
#include "common/CommHelper.h"
#include "errcode/ErrCode_iCV_Common.hxx"

namespace cvcommon
{

class TNodeItemImpl
{
public:
	string	strNodeName;
	string	strNodeIp[MAX_NUM_OF_NODES];
	int		nActiveIp;
	bool	bIsDualBack;

	TNodeItemImpl(const char *szNodeName, const char *szNodeIpMain, const char *szNodeIpBackup);	

	const char* GetActiveIpAddr();
};

class CNodeTableImpl
{
public:


	bool	IsNodeExists(const ACE_TCHAR *szNodeName);
	TNodeItemImpl	*GetNodeItem(const ACE_TCHAR *szNodeName);

	long		LoadNodeTable(const ACE_TCHAR *szConfigPath);

	void		GetNodeListByFilter(const ACE_TCHAR *szFilter, std::list<TNodeItemImpl*> &listNode);

	void		ClearNodeTable();

	CNodeTableImpl();
	~CNodeTableImpl();
protected:
	void		AddNode(const ACE_TCHAR *szNodeName, const ACE_TCHAR *szNodeIpMain, const ACE_TCHAR *szNodeIpBack);
	//string		GetCwdOrCurDir();

	std::map<string, TNodeItemImpl*>	m_Nodes;
	typedef std::map<string, TNodeItemImpl*>::iterator IterNode;

};

CNodeTableImpl::CNodeTableImpl()
{

}

CNodeTableImpl::~CNodeTableImpl()
{
	ClearNodeTable();
}
/**
*  (get current module path in WIN32).
*  (get current woking dir in other OS).
*
*  @version  09/29/2007  chenshengyu  Initial Version.
*/

/*string CNodeTable::GetCwdOrCurDir()
{
	string strPath;
#ifdef _WIN32
	char szCurPath[MAX_PATH + 1];
	GetModuleFileName(NULL, szCurPath, MAX_PATH);
	strPath = szCurPath;
	
	string::size_type pos = strPath.rfind (ACE_DIRECTORY_SEPARATOR_CHAR);
	if (pos != string::npos)
		strPath = strPath.substr(0, pos + 1);
	
#else
	
	ACE_TCHAR szCwd[PATH_MAX];
	getcwd(szCwd, PATH_MAX);
	string strArg(szCwd);
	
	// 1. Separate the dll_name into the dir part and the file part. We
	// only decorate the file part to determine the names to try loading.
	string::size_type pos = strArg.rfind (ACE_DIRECTORY_SEPARATOR_CHAR);
	if (pos != string::npos)
		strPath = strArg.substr (0, pos + 1);
#endif
	
	return strPath;
}//*/

/**
 *  Add Node To Node Table.
 *
 *  @param  -[in,out]  const ACE_TCHAR*  szNodeName: [Node Name]
 *  @param  -[in,out]  const ACE_TCHAR*  szNodeIpMain: [Main IP]
 *  @param  -[in,out]  const ACE_TCHAR*  szNodeIpBack: [Backup IP]
 *
 *  @version     07/14/2008  chenzhiquan  Initial Version.
 */
void CNodeTableImpl::AddNode( const ACE_TCHAR *szNodeName, const ACE_TCHAR *szNodeIpMain, const ACE_TCHAR *szNodeIpBack )
{
	if (!szNodeName || !szNodeIpMain)
		return ;

	// remove exist
	IterNode itNode = m_Nodes.find(szNodeName);
	if (itNode != m_Nodes.end())
	{
		TNodeItemImpl *pNode = (*itNode).second;
		if (pNode)
		{
			pNode->strNodeIp[0] = szNodeIpMain;
			pNode->strNodeIp[1] = szNodeIpBack; // may be empty
		}
	}
	else
	{
		TNodeItemImpl *pNode = new TNodeItemImpl(szNodeName, szNodeIpMain, szNodeIpBack);
		m_Nodes.insert(std::make_pair(std::string(szNodeName), pNode));
	}
}

bool CNodeTableImpl::IsNodeExists( const ACE_TCHAR *szNodeName )
{
	IterNode itNode = m_Nodes.find(szNodeName);
	if (itNode != m_Nodes.end())
	{
		return true;
	}
	return false;
}

/**
 *  Load Scada List.
 *
 *  @param  -[in]  const ACE_TCHAR*  szConfigPath: [config Path]
 *
 *  @version     07/14/2008  chenzhiquan  Initial Version.
 */
long CNodeTableImpl::LoadNodeTable(const ACE_TCHAR *szConfigPath)
{
	string strSCADALstXML = string(szConfigPath) + ACE_DIRECTORY_SEPARATOR_STR DEFAULT_SCADALST_FILE_NAME;

	TiXmlDocument doc(strSCADALstXML.c_str());
	
	if( !doc.LoadFile() )
	{
		//LH_LOG((LOG_LEVEL_ERROR, ACE_TEXT("Loading \"SCADALst.xml\" Failed!, No Scada Node Found!")));
		return ICV_SUCCESS;//_TODO:
	}
	TiXmlElement* pElmRoot;
	// 2.Get Root Element.
	pElmRoot = doc.FirstChildElement(iCV_XML_DEFINE_SCADALST); 
	
	if(!pElmRoot)
	{// No Node Found
		return ICV_SUCCESS;
	}

	for (TiXmlElement* pElmScada = pElmRoot->FirstChildElement(iCV_XML_DEFINE_SCADALST_SCADA);
			pElmScada != NULL;
			pElmScada = pElmScada->NextSiblingElement(iCV_XML_DEFINE_SCADALST_SCADA))
	{
		const char* szName = pElmScada->Attribute(iCV_XML_DEFINE_NAME);
		const char* szMainIP = pElmScada->Attribute(iCV_XML_DEFINE_SCADALST_SCADA_MAINIP);
		const char* szBackIP = pElmScada->Attribute(iCV_XML_DEFINE_SCADALST_SCADA_BAKEIP);

		if (!szName)
		{
			//LH_ERROR_CODE((EC_ICV_COMM_SCADA_CFG_ERROR, "Scada Name Not Specified!"));
			continue;
		}
		AddNode(szName, szMainIP, szBackIP);
	}
	return ICV_SUCCESS;
}

/**
 *  Get Node Item Stucture.
 *
 *  @param  -[in,out]  const ACE_TCHAR*  szNodeName: [Node Name]
 *  @return Null if not found.
 *
 *  @version     07/23/2008  chenzhiquan  Initial Version.
 */
TNodeItemImpl	* CNodeTableImpl::GetNodeItem( const ACE_TCHAR *szNodeName )
{
	IterNode itNode = m_Nodes.find(szNodeName);
	if (itNode != m_Nodes.end())
	{
		return (*itNode).second;
	}
	
	return NULL;
}

void CNodeTableImpl::GetNodeListByFilter( const ACE_TCHAR *szFilter, std::list<TNodeItemImpl*> &listNode )
{
	listNode.clear();
	for (IterNode it = m_Nodes.begin(); it != m_Nodes.end(); it++)
	{
		if (szFilter != NULL && szFilter[0] != '\0')
		{
			if( it->first.find(szFilter, 0) == string::npos)
				continue;
		}
		listNode.push_back(it->second);
	}
}

void CNodeTableImpl::ClearNodeTable()
{
	for (IterNode itNode = m_Nodes.begin(); itNode != m_Nodes.end(); ++itNode )
	{
		TNodeItemImpl *pNode = (*itNode).second;
		SAFE_DELETE(pNode);
	}
	m_Nodes.clear();
}


TNodeItemImpl::TNodeItemImpl( const ACE_TCHAR *szNodeName, const ACE_TCHAR *szNodeIpMain, const ACE_TCHAR *szNodeIpBackup )
{
	strNodeName = szNodeName;
	strNodeIp[MAIN_NODE] = szNodeIpMain;
	strNodeIp[BACKUP_NODE] = szNodeIpBackup;
	if (szNodeIpBackup && '\0' != szNodeIpBackup[0])
	{
		bIsDualBack = true;
	}
	else
	{
		bIsDualBack = false;
	}//*/
	nActiveIp = MAX_NUM_OF_NODES;
}

const ACE_TCHAR* TNodeItemImpl::GetActiveIpAddr()
{
	if (strNodeIp[BACKUP_NODE].empty()) 
	{// NO Dual back node exists
		return strNodeIp[MAIN_NODE].c_str();
	}

	if (nActiveIp < 0 || nActiveIp >= MAX_NUM_OF_NODES)
	{
		return NULL;
	}
	return strNodeIp[nActiveIp].c_str();
}
}

bool CNodeTable::IsNodeExists( const char *szNodeName )
{
	//if(this->m_pImpl)
	return m_pImpl->IsNodeExists(szNodeName);
}

CNodeTable::CNodeTable()
{
	m_pImpl = new cvcommon::CNodeTableImpl();
}

CNodeTable::TNodeItem CNodeTable::GetNodeItem( const char *szNodeName )
{
	cvcommon::TNodeItemImpl *pNodeItemImpl = m_pImpl->GetNodeItem(szNodeName);
	TNodeItem nodeItem;
	nodeItem.m_pImpl = pNodeItemImpl;
	return nodeItem;
}

long CNodeTable::LoadNodeTable( const char *szConfigPath )
{
	return m_pImpl->LoadNodeTable(szConfigPath);
}

int CNodeTable::GetNodeListByFilter( const char *szFilter, TNodeItem NodeItem[], int nCount )
{
	std::list<cvcommon::TNodeItemImpl*> listNode;
	m_pImpl->GetNodeListByFilter(szFilter, listNode);
	int i= 0;
	for (std::list<cvcommon::TNodeItemImpl*>::iterator it = listNode.begin(); i < nCount && it != listNode.end(); i++, it++)
	{
		NodeItem[i].m_pImpl = *it;
	}
	return listNode.size();
}

void CNodeTable::ClearNodeTable()
{
	m_pImpl->ClearNodeTable();
}

CNodeTable::~CNodeTable()
{
	delete m_pImpl;
}
bool CNodeTable::TNodeItem::IsValid()
{
	return m_pImpl != NULL;
}

const char* CNodeTable::TNodeItem::GetNodeIp( int nIndex )
{
	if (!m_pImpl || nIndex < 0 || nIndex > MAX_NUM_OF_NODES)
	{
		return NULL;
	}
	return m_pImpl->strNodeIp[nIndex].c_str();
}

bool CNodeTable::TNodeItem::IsDualBack()
{
	if( m_pImpl)
	{
		return m_pImpl->bIsDualBack;
	}
	return false;
}

const char* CNodeTable::TNodeItem::GetNodeName()
{
	if( m_pImpl)
	{
		return m_pImpl->strNodeName.c_str();
	}
	return NULL;
	
}
