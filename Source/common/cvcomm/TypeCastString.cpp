#include "common/cv_datatype.h"
#include "common/Quality.h"

#include <ace/Basic_Types.h>
#include <ace/OS_NS_strings.h>
#include <ace/Assert.h>

//#include "processdb/ControlDef.h"
#include <ace/Time_Value.h>
#include <ace/OS_NS_time.h>
#include <ace/OS_NS_stdio.h>
#include <ace/ace_wchar.h>
#include <ace/Mutex.h>
#include "common/TypeCast.h"
#include "errcode/ErrCode_iCV_Common.hxx"
#include "gettext/libintl.h"
#include <map>
#include <string>

#define countof(x)	(sizeof(x)/sizeof(x[0]))
#define _(STRING) gettext(STRING)
#undef sprintf
namespace cvcommon
{

//CAUTION: This Structure Copyed From ProcessBlockSchema.h,DO NOT CHANGE IT WHEN YOU'RE NOT SURE
struct TPBDataSchema0
{
	TCV_TimeStamp	m_timestamp;			// timestamp (8 BYTE)
	QUALITY_STATE	m_quality;				// quality (2 BYTE)
	ACE_UINT16		m_datasize;				// datasize (2 BYTE)
	unsigned char	m_ucDataType;			// dataType (1 Byte)
	char			m_chPad[3];
	char			m_szData[1];	// data buffer, should + 1 when blob/text
};

//////////////////////////////////////////////////////////////////////////
CVCOMM_API unsigned short dataTypeSize[CV_MAX_NUMBER_DATATYPE_TOTAL]={
	ICV_TXTVALUE_MAXLEN,			// ASCII string, maximum: 256
	sizeof(ACE_INT16),				// 16 bit Signed Integer value
	sizeof(ACE_UINT16),				// 16 bit Unsigned Integer value
	sizeof(float),					// 32 bit IEEE float
	sizeof(char),					// byte is a// 1 bit valuelign to 1 byte
	4,								// 4 byte TIME (H:M:S:T)
	sizeof(ACE_UINT32),				// 32 bit integer value
	sizeof(ACE_INT32),				// 32 bit signed integer value
	sizeof(double),					// 64 bit double
	ICV_BLOBVALUE_MAXLEN,			// blob size// blob, maximum 64512(63k) is variable
	sizeof(char),					// 8 bit signed integer value
	sizeof(unsigned char),			// 8 bit unsigned integer value
	sizeof(ACE_INT64),				// 64 bit signed integer value
	sizeof(ACE_UINT64),				// 64 bit unsigned integer value
	sizeof(TCV_TimeStamp),			// 64 bit unsinged integer value
	sizeof(unsigned char),			// 1 bit unsigned char
	sizeof(int16_t),				// 16 bit unsigned int
	sizeof(int32_t),				// 32 bit unsigned int
	sizeof(int64_t),				// 64 bit unsinged integer value
	sizeof(char),					// 1 bit
	DSF_UDT_MAXLEN,					// user defined type
	sizeof(uint32_t), 				// 32 bit unsigned integer value
	sizeof(uint32_t),      			// 32 bit unsigned integer value, TIME OF DAY
	sizeof(uint32_t), 				// 32 bit unsigned integer value, DATE AND TIME
	sizeof(uint16_t),      			// utf-16
	DSF_WSTRING_MAXLEN,      		// utf-16 string, maximum 16382
	sizeof(uint64_t),     			// 64 bit unsigned integer value
	sizeof(uint64_t),      			// 64 bit unsigned integer value, LTIME OF DAY
	sizeof(uint64_t),      			// 64 bit unsigned integer value, LDATE AND TIME
};

const char * g_szEnableDesc[]={
	"Disabled",
	"Enabled",
};

const char * g_szScanModeDesc[]={
	"Periodic",
	"ByException",
	"Manual",
};

const char * g_szCtrlCmdResultDesc[]={
	_("Not Performed (RM INACTIVE)"),
	_("Send To Driver Successfully"),
	_("Succeed"),
	_("Timeout"),
	_("Failed"),
	_("UnknownResult")
};

/**
 *  @brief    (description of alarm/event types). 
 *  (see also enum EVENT_TYPE in alarm def).
 */
const char * g_szAlmTypeDesc[]={
	"OK",					// no alarm
	"LOLO",					// low low
	"LO",					// low
	"HI",					// high
	"HIHI",					// high high
	"ROC",					// rate of change
	"COS",					// change of state
	"DEV",					// deviation
	"FLOAT Error",			// Floating point error
	"IO Failure",			// general I/O failure
	"Comm Failure",			// Comm link failure
	"Under Range",			// under range (clamped at 0)
	"Over Range",			// over range (clamped at MAX)
	"Out of Range",			// out of range (value unknown)
	"Device Failure",		// Device failure.
	"Station Failure",		// Station Failure
	"Access Denied",		// Access denied (privledge).
	"NoData",				// On poll but no data yet
	"NoXData",				// Exception item but no data yet
	// 
	"Logic Expresssion Hit",	// logic expression hit
	"Timer Hit",				// time hit
	"Logic and Timer Hit",		// logic and timer event
	//
	"System Alert",				// system alert notification
	"Network Alert",			// netowrk notification
	"Operation Action",			// operation action
	"Response Notice",			// response notification
};

/**
 *  @brief    (description of alarm/event states). 
 *  (see also enum EVENT_STATUS in alarmdef.h).
 */
const char * g_szAlmStateDesc[]={
	"OK",			// no alarm(��ʼ״̬)
	"ALM",			// in alarm��������
	"RTN",			// return to normal(�ָ�)
	"",
	"ACK",			// acked alarm(ȷ��)
	"",
	"ACK & RTN",	// acked & recovered
	"",
	"DEL",			// manual delete data(�ֶ�ɾ��)
};

/**
 *  @brief    (description of alarm priority). 
 *  (see also enum EVENT_STATUS in alarmdef.h).
 */
// Delete by czq. Alm Priority is change to 0~255
// char * g_szAlmPriorityDesc[]={
// 	"Critical",
// 	"HiHi",
// 	"High",
// 	"Normal",
// 	"Low",
// 	"LoLo",
// 	"Minor",
// };

/**
 *  @brief    (description of data type). 
 *  (see also MAX_NUMBER_DATATYPE in LRDAdef.h).
 */
const char * g_szDataTypeDesc[]={
	"ASCII",			// ASCII string, maximum: 255
	"SINT16",			// 16 bit Signed Integer value
	"UINT16",			// 16 bit Unsigned Integer value
	"FLT",				// 32 bit IEEE float
	"BIT",				// 1 bit value
	"TIM",				// 4 byte TIME (H:M:S:T)
	"ULONG",			// 32 bit integer value
	"SLONG",			// 32 bit signed integer value
	"DBL",				// 64 bit double
	"BLOB",				// blob, maximum 65535
	"CHAR",				// 8 bit signed integer value
	"UCHAR",			// 8 bit unsigned integer value
	"INT64",			// 64 bit signed integer value
	"UINT64",			// 64 bit unsigned integer value
	"CV_TIME",			// 64 bit cv time (second + usecond)
	"",					// NIL
	"QUALITY",			// 16 bit opc Quality
};

// description of Quality Values
const char * g_szQualityDesc[]={
	QUALITY_BAD_DESC,
	QUALITY_UNCERTAIN_DESC,
	QUALITY_UNDEFINED_DESC,
	QUALITY_GOOD_DESC,
};

// Substatus for BAD
//const char * g_szQualityBadSubDesc[]={
//	"No specific reason",
//	"Configuration Error",
//	"Input not connected",
//	"Protocol Error cause of Failure",
//	"Sensor failure",
//	"Latched data",
//	"Time-out Error cause of Failure",
//	"Out of Service",
//};


// SubQuality Map in Cache
typedef std::map<unsigned short, std::string> SubDescMap;
typedef SubDescMap::iterator SubDescMapIter;
SubDescMap g_mapSubDesc;

/**
 *  SubQuality�����ַ�������������. 
 *  ����Quality��״̬�����ַ����������ӵ�g_mapSubDesc��
 *  Ŀǰ�ַ�������nQuality, nSubQuality, nLeftOver�����ֶΣ���szSubDesc, szCustomDescƴ�Ӷ���
 *  �ڲ�ʹ�ã��ʲ��������Ϸ��ԣ����ж�key�Ƿ��ظ�����Щ���ǳ���Ա��������
 */
class SubDescGenerator
{
public:
	SubDescGenerator(unsigned short nQuality, unsigned short nSubQuality, const char* szSubDesc)
	{
		unsigned short usKey = GenerateKey(nQuality, nSubQuality);
		g_mapSubDesc[usKey] = szSubDesc;
	}
	SubDescGenerator(unsigned short nQuality, unsigned short nSubQuality, const char* szSubDesc, unsigned short nLeftOver, const char* szCustomDesc)
	{
		//��ʽ��szSubDesc[szCustomDesc]
		unsigned short usKey = GenerateKey(nQuality, nSubQuality, nLeftOver);
		std::string strSubDesc = szSubDesc;
		strSubDesc += "[";
		strSubDesc += szCustomDesc;
		strSubDesc += "]";
		g_mapSubDesc[usKey] = strSubDesc;
	}
	//���ɹؼ��֣����˵�nLimit�ֶ�
	static unsigned short GenerateKey(QUALITY_STATE& state)
	{
		state.nLimit = 0;
		unsigned short usQuality = 0;
		memcpy(&usQuality, &state, sizeof(usQuality));
		return usQuality;
	}
	static unsigned short GenerateKey(unsigned short nQuality, unsigned short nSubQuality, unsigned short nLeftOver = 0, unsigned short nLimit = 0)
	{
		QUALITY_STATE state;
		state.nQuality = nQuality;
		state.nSubStatus = nSubQuality;
		state.nLeftOver = nLeftOver;
		state.nLimit = nLimit;
		return GenerateKey(state);
	}
};

//�������g_mapSubDesc��ʹ��ǰ������
SubDescGenerator g_generatorhelper[] = {
	////////////////// SubStatus for Good //////////////////
	SubDescGenerator(QUALITY_GOOD, SS_NON_SPECIFIC, SS_NON_SPECIFIC_GOOD_DESC),
	SubDescGenerator(QUALITY_GOOD, SS_LOCAL_OVERRIDE, SS_LOCAL_OVERRIDE_DESC),
	////////////////// SubStatus for UnCertain //////////////////
	SubDescGenerator(QUALITY_UNCERTAIN, SS_NON_SPECIFIC, SS_NON_SPECIFIC_UNCERTAIN_DESC),
	SubDescGenerator(QUALITY_UNCERTAIN, SS_LAST_USABLE, SS_LAST_USABLE_DESC),
	SubDescGenerator(QUALITY_UNCERTAIN, SS_SENSOR_NOT_ACCURATE, SS_SENSOR_NOT_ACCURATE_DESC),
	SubDescGenerator(QUALITY_UNCERTAIN, SS_EGU_UNITS_EXCEEDED, SS_EGU_UNITS_EXCEEDED_DESC),
	SubDescGenerator(QUALITY_UNCERTAIN, SS_SUBNORMAL, SS_SUBNORMAL_DESC),
	////////////////// SubStatus for Bad //////////////////
	SubDescGenerator(QUALITY_BAD, SS_NON_SPECIFIC, SS_NON_SPECIFIC_BAD_DESC),
	// ���ô����֧
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_DRIVERNAME_EMPTY, CV_LV_DRIVERNAME_EMPTY_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_DRIVER_NOTFOUND, CV_LV_DRIVER_NOTFOUND_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_ADDRESSLEN_TOOLONG, CV_LV_ADDRESSLEN_TOOLONG_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_ADDRESS_BEGINFROMDIGITAL, CV_LV_ADDRESS_BEGINFROMDIGITAL_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_ADDRESS_SEGNUM_TOOSMALL, CV_LV_ADDRESS_SEGNUM_TOOSMALL_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_ADDRESS_SEGNUM_TOOMUCH, CV_LV_ADDRESS_SEGNUM_TOOMUCH_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_DEVICENAME_EMPTY, CV_LV_DEVICENAME_EMPTY_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_IOADDRESS_EMPTY, CV_LV_IOADDRESS_EMPTY_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_IOADDRESS_EXCEEDLIMIT, CV_LV_IOADDRESS_EXCEEDLIMIT_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_IOADDRESS_BITOFFSET_NOTEMPTY, CV_LV_IOADDRESS_BITOFFSET_NOTEMPTY_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_IOADDRESS_DIGITALTAG_NOBITOFFSET, CV_LV_IOADDRESS_DIGITALTAG_NOBITOFFSET_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LA_IOADDRESS_BITOFFSET_EXCEEDLIMIT, CV_LA_IOADDRESS_BITOFFSET_EXCEEDLIMIT_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_IOADDRESS_BYTEOFFSET_EXCEEDLIMIT, CV_LV_IOADDRESS_BYTEOFFSET_EXCEEDLIMIT_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_IOADDRESS_DATALENGTH_TOOSMALL, CV_LV_IOADDRESS_DATALENGTH_TOOSMALL_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_IOADDRESS_DATALENGTH_TOOMUCH, CV_LV_IOADDRESS_DATALENGTH_TOOMUCH_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_DEVICE_NOTFOUND, CV_LV_DEVICE_NOTFOUND_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_DATABLOCK_NOTFOUND, CV_LV_DATABLOCK_NOTFOUND_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_RELATIONALBLK_NOTFOUND, CV_LV_RELATIONALBLK_NOTFOUND_DESC),
	SubDescGenerator(QUALITY_BAD, SS_CONFIG_ERROR, SS_CONFIG_ERROR_DESC, CV_LV_CA_EXPRESSION_INVALID, CV_LV_CA_EXPRESSION_INVALID_DESC),
	// δ���ӷ�֧
	SubDescGenerator(QUALITY_BAD, SS_NOT_CONNECTED, SS_NOT_CONNECTED_DESC),
	// �豸���Ϸ�֧
	SubDescGenerator(QUALITY_BAD, SS_DEVICE_FAILURE, SS_DEVICE_FAILURE_DESC),
	SubDescGenerator(QUALITY_BAD, SS_DEVICE_FAILURE, SS_DEVICE_FAILURE_DESC, CV_LV_RELATIONALBLK_QUALITYBAD, CV_LV_RELATIONALBLK_QUALITYBAD_DESC),
	SubDescGenerator(QUALITY_BAD, SS_DEVICE_FAILURE, SS_DEVICE_FAILURE_DESC, CV_LV_CA_CALC_FAILURE, CV_LV_CA_CALC_FAILURE_DESC),
	// ���������ϵķ�֧
	SubDescGenerator(QUALITY_BAD, SS_SENSOR_FAILURE, SS_SENSOR_FAILURE_DESC),
	// SS_LAST_KNOWN��֧
	SubDescGenerator(QUALITY_BAD, SS_LAST_KNOWN, SS_LAST_KNOWN_DESC),
	// SS_COMM_FAILURE��֧
	SubDescGenerator(QUALITY_BAD, SS_COMM_FAILURE, SS_COMM_FAILURE_DESC),
	// �����жϵķ�֧
	SubDescGenerator(QUALITY_BAD, SS_OUT_OF_SERVICE, SS_OUT_OF_SERVICE_DESC),
	SubDescGenerator(QUALITY_BAD, SS_OUT_OF_SERVICE, SS_OUT_OF_SERVICE_DESC, CV_LV_IO_NUMBER_LIMIT, CV_LV_IO_NUMBER_LIMIT_DESC),
	SubDescGenerator(QUALITY_BAD, SS_OUT_OF_SERVICE, SS_OUT_OF_SERVICE_DESC, CV_LV_SCHEDULE_FAILURE, CV_LV_SCHEDULE_FAILURE_DESC),
	SubDescGenerator(QUALITY_BAD, SS_OUT_OF_SERVICE, SS_OUT_OF_SERVICE_DESC, CV_LV_SCAN_DISABLED, CV_LV_SCAN_DISABLED_DESC),
	//��ɨ��
	SubDescGenerator(QUALITY_BAD, SS_SCAN_UNSUPPORT, SS_SCAN_UNSUPPORT_DESC),
	// tdrv 订阅的点对于PLC来说太多了
	SubDescGenerator(QUALITY_BAD, SS_SUBSCRIBE_TAG_TOOMANY, SS_SUBSCRIBE_TAG_TOOMANY_DESC),
};

// δ����״̬����Quality����ʧ��ʱ����
const char* g_szUndefinedDesc = SS_UNDEFINED_DESC;


/**
 *  A Safe call to strncpy. process the NULL pointer and Buffer too small problem.
 *
 *  @param  -[out]  char *  szDest: [buffer to be sent]
 *  @param  -[in]  char * szSource: [buffer length to be sent]
 *  @param  -[in]  size_t nDestBuffLen: [client queue handle]
 *  @return 
 *	- ==0 success
 *	- !=0 failure
 *
 *  @version     06/05/2008  shijunpu  Initial Version.
 */
long Safe_StrNCopy(char * szDest, const char * szSource, size_t nDestBuffLen)
{
	long lRet = ICV_SUCCESS;
	if(NULL == szDest)
		return EC_ICV_COMM_BUFFERTOOSHORT;

	if(NULL == szSource)
	{
		szDest[0] = '\0';
		return EC_ICV_COMM_BUFFERTOOSHORT;
	}

	// the most common condition
	if(strlen(szSource) < nDestBuffLen)
	{
		strcpy(szDest, szSource);
		return lRet;
	}

	// strlen(szSource) >= nDestBuffLen, some will be trimed
	strncpy(szDest, szSource, nDestBuffLen);
	szDest[nDestBuffLen - 1] = '\0';
	
	return EC_ICV_COMM_BUFFERTOOSHORT;
}

/**
 *  Cast TimeStamp to Buffer.
 *
 *  @param  -[in,out]  ACE_TCHAR  date_and_time[]: [buffer to out]
 *  @param  -[in]  int  date_and_timelen: [length of buffer]
 *  @param  -[in]  ACE_Time_Value  cur_time: [time value]
 *
 *  @version     07/08/2008  chenzhiquan  Initial Version.
 */
char * CastTimeToASCII (char date_and_time[],
				 int date_and_timelen,
				 const TCV_TimeStamp &cur_time)
{
	if (date_and_timelen < DT_SIZE_TIMESTR)
	{
		return 0;
	}

#if defined (WIN32)

	SYSTEMTIME local;
	FILETIME localFT;
	::GetLocalTime (&local);
	FILETIME ts = ACE_Time_Value(cur_time);
	::FileTimeToLocalFileTime(&ts, &localFT);
	FileTimeToSystemTime(&localFT, &local);

	date_and_time[0] = (local.wYear/1000)%10 + '0';
	date_and_time[1] = (local.wYear/100)%10 + '0';
	date_and_time[2] = (local.wYear/10)%10 + '0';
	date_and_time[3] = (local.wYear)%10 + '0';
	date_and_time[4] = '-';
	date_and_time[5] = (local.wMonth/10)%10 + '0';
	date_and_time[6] = (local.wMonth)%10 + '0';
	date_and_time[7] = '-';
	date_and_time[8] = (local.wDay/10)%10 + '0';
	date_and_time[9] = (local.wDay)%10 + '0';
	date_and_time[10] = ' ';
	date_and_time[11] = (local.wHour/10)%10 + '0';
	date_and_time[12] = (local.wHour)%10 + '0';
	date_and_time[13] = ':';
	date_and_time[14] = (local.wMinute/10)%10 + '0';
	date_and_time[15] = (local.wMinute)%10 + '0';
	date_and_time[16] = ':';
	date_and_time[17] = (local.wSecond/10)%10 + '0';
	date_and_time[18] = (local.wSecond)%10 + '0';
	date_and_time[19] = '.';
	date_and_time[20] = (local.wMilliseconds/100)%10 + '0';
	date_and_time[21] = (local.wMilliseconds/10)%10 + '0';
	date_and_time[22] = (local.wMilliseconds)%10 + '0';
	date_and_time[23] = '\0';
	return date_and_time;
#else  /* UNIX */
	// Depends ACE on *nix
	time_t secs = cur_time.tv_sec;
	struct tm* tmTmp = ACE_OS::localtime(&secs);
	ACE_OS::strftime(date_and_time, 
		date_and_timelen, 
		ACE_TEXT("%Y-%m-%d %H:%M:%S."),
		tmTmp);
	ACE_OS::sprintf (date_and_time + strlen(date_and_time),
		ACE_TEXT("%03d"),
		(int)(cur_time.tv_usec / 1000)); 
	return date_and_time;
#endif /* WIN32 */
}


const char * GetQualityDesc(unsigned short usQuality)
{
	QUALITY_STATE qs;
	memcpy(&qs, &usQuality, sizeof(QUALITY_STATE));
	return g_szQualityDesc[qs.nQuality];
}

//ֻ��ѯSubQuality�����ε�LeftOver
const char * GetQualitySubStatusDesc(unsigned short usQuality)
{
	QUALITY_STATE qs;
	memcpy(&qs, &usQuality, sizeof(qs));
	qs.nLeftOver = 0;
	memcpy(&usQuality, &qs, sizeof(usQuality));
	return GetQualitySubStatusDescEx(usQuality);
}

//��ȡ��չ�����״̬�ַ���
const char * GetQualitySubStatusDescEx(unsigned short usQuality)
{
	QUALITY_STATE qs;
	memcpy(&qs, &usQuality, sizeof(QUALITY_STATE));
	//�ؼ���key��Ҫ���˵�
	unsigned short usKey = SubDescGenerator::GenerateKey(qs);
	//��黺�����Ƿ���
	SubDescMapIter iterSubDesc = g_mapSubDesc.find(usKey);
	//������򷵻ػ����е��ַ���
	if (iterSubDesc != g_mapSubDesc.end())
	{
		return iterSubDesc->second.c_str();
	} 
	// ���û�У�����δ����״̬
	return  g_szUndefinedDesc;
}


const char * GetAlmStateDesc(unsigned short usState)
{
	static char szUnknown[ICV_DESCRIPTION_MAXLEN];
	if ((int)usState >= countof(g_szAlmStateDesc))
	{
		sprintf(szUnknown, "UnknownAlmState(%u)", usState);
		return szUnknown;
	}

	return g_szAlmStateDesc[usState];
}

const char * GetAlmTypeDesc(unsigned short usType)
{
	static char szUnknown[ICV_DESCRIPTION_MAXLEN];
	//if ((((int)usType) < 0) || (((int)usType) >= countof(g_szAlmTypeDesc)))
	if ((((int)usType) >= countof(g_szAlmTypeDesc)))
	{
		sprintf(szUnknown, "UnknownAlmType(%u)", usType);
		return szUnknown;
	}

	return g_szAlmTypeDesc[usType];
}

// char * GetAlmPriorityDesc(unsigned short usPriority)
// {
// 	static char szUnknown[PDB_MAX_NAME_LEN + 1];
// 	if ((usPriority < 0) || (usPriority >= AECONST_EP_COUNT))
// 	{
// 		sprintf(szUnknown, "UnknownAlmType(%u)", usPriority);
// 		return szUnknown;
// 	}
// 
// 	return g_szAlmPriorityDesc[usPriority];
// }

const char * GetDataTypeDesc( unsigned char usDataType )
{
	static char szUnknown[ICV_DESCRIPTION_MAXLEN];
	//if (((int)usDataType < 0) || ((int)usDataType >= CV_MAX_NUMBER_DATATYPE))
	if ((int)usDataType >= CV_MAX_NUMBER_DATATYPE_TOTAL)
	{
		sprintf(szUnknown, "UnknownDataType(%u)", usDataType);
		return szUnknown;
	}

	return g_szDataTypeDesc[usDataType];
}

const char * GetScanModeDesc(bool bMode)
{
	int nIndex = 1;
	if (!bMode)
		nIndex = 0;

	return g_szScanModeDesc[nIndex];
}

const char * GetEnableDesc(bool bEnable)
{
	int nIndex = 1;
	if (!bEnable)
		nIndex = 0;

	return g_szEnableDesc[nIndex];
}

/************************************************************************/
/*  DESCRIPTION TO VALUDE                                               */
/************************************************************************/

unsigned short GetQualityValue(const char * szQuality)
{
	for (unsigned short usQuality = 0; usQuality < countof(g_szQualityDesc); usQuality++)
	{
		if (ACE_OS::strcasecmp(g_szQualityDesc[usQuality], szQuality) == 0)
			return usQuality;
	}

	return PBFIELD_INVALID_VALUE;
}

//����SubQuality�ַ�����ѯQuality״̬
unsigned short GetQualitySubStatusValue(const char * szSubQuality)
{
	//����������ж�Ӧ�ַ������򷵻�key
	for (SubDescMapIter iterDesc = g_mapSubDesc.begin();
		iterDesc != g_mapSubDesc.end();
		iterDesc++)
	{
		if (ACE_OS::strcasecmp(szSubQuality, iterDesc->second.c_str()) == 0)
		{
			return iterDesc->first;
		}
	}
	//���򣬷���Ĭ��ֵ
	QUALITY_STATE qs;
	memset(&qs, 0x00, sizeof(qs));
	qs.nQuality = QUALITY_BAD;
	qs.nSubStatus = SS_NON_SPECIFIC;
	unsigned short usQuality = 0;
	memcpy(&usQuality, &qs, sizeof(unsigned short));
	return usQuality;
}

unsigned short GetAlmStateValue(const char * szState)
{
	for (unsigned short usState = 0; usState < countof(g_szAlmStateDesc); usState++)
	{
		if (ACE_OS::strcasecmp(g_szAlmStateDesc[usState], szState) == 0)
			return usState;
	}

	return PBFIELD_INVALID_VALUE;
}

unsigned short GetAlmTypeValue(const char * szType)
{
	for (unsigned short usType = 0; usType < countof(g_szAlmTypeDesc); usType++)
	{
		if (ACE_OS::strcasecmp(g_szAlmTypeDesc[usType], szType) == 0)
			return usType;
	}

	return PBFIELD_INVALID_VALUE;
}

// *  @version     06/10/2008  chenzhiquan  Alarm Priority Is Changed to 0~255.
// unsigned char GetAlmPriorityValue(const char * szPriority)
// {
// 	for (unsigned char ucPriority = 0; ucPriority < countof(g_szAlmPriorityDesc); ucPriority++)
// 	{
// 		if (ACE_OS::strcasecmp(g_szAlmPriorityDesc[ucPriority], szPriority) == 0)
// 			return ucPriority;
// 	}
// 
// 	return PBFIELD_INVALID_VALUE;
// }

unsigned short GetDataTypeValue(const char * szDataType)
{
	for (unsigned short usDataType = 0; usDataType < countof(g_szDataTypeDesc); usDataType++)
	{
		if (ACE_OS::strcasecmp(g_szDataTypeDesc[usDataType], szDataType) == 0)
			return usDataType;
	}

	return PBFIELD_INVALID_VALUE;
}

/**
 *  (Desp).
 *
 *  @param  -[in ]  const char *  szMode: [scan mode]
 *  @param  -[out]  bool&  bValue: [scan mode value]
 *  @return (true for succes, false for failed).
 *
 *  @version  12/31/2007  chenshengyu  Initial Version.
 */
bool  GetScanModeValue(const char * szMode, bool &bValue)
{
	ACE_ASSERT(szMode);
	if (szMode)
	{
		if (ACE_OS::strcasecmp(g_szScanModeDesc[PDB_PB_SCAN_BY_INTERVAL], szMode) == 0)
		{
			bValue = PDB_PB_SCAN_BY_INTERVAL;
			return true;
		}

		if (ACE_OS::strcasecmp(g_szScanModeDesc[PDB_PB_SCAN_BY_EXCEPTION], szMode) == 0)
		{
			bValue = PDB_PB_SCAN_BY_EXCEPTION;
			return true;
		}
	}

	return false;
}

/**
 *  (Desp).
 *
 *  @param  -[in ]  const char *  szEnable: [Enable String]
 *  @param  -[out]  bool&  bValue: [bool value]
 *  @return (true for succes, false for failed).
 *
 *  @version  12/31/2007  chenshengyu  Initial Version.
 */
bool GetEnableValue(const char * szEnable, bool &bValue)
{
	ACE_ASSERT(szEnable);
	if (szEnable)
	{
		if (ACE_OS::strcasecmp(g_szEnableDesc[0], szEnable) == 0)
		{
			bValue = false;
			return true;
		}

		if (ACE_OS::strcasecmp(g_szEnableDesc[1], szEnable) == 0)
		{
			bValue = true;
			return true;
		}
	}

	return false;
}

const char * GetCtrlCmdResultDesc( long nResult )
{
	if (nResult < CONTROL_RESULT_INACTIVE || nResult >= CONTROL_RESULT_MAX_RESULT)
	{
		return g_szCtrlCmdResultDesc[CONTROL_RESULT_MAX_RESULT + 2];
	}
	return g_szCtrlCmdResultDesc[nResult + 2];
		
}


/**
 *  Cast Type To ASCII* Buffer.
 *
 *  @param  -[in,out]  string&  str: [string]
 *  @param  -[in,out]  const char*  szSrcData: [source buffer]
 *  @param  -[in,out]  char  cSrcDataType: [src data type]
 *
 *  @version     07/09/2008  chenzhiquan  Initial Version.
 */
long CastCVTypeToASCII(const char *szSrcData, size_t nSrcSize, unsigned char cDataType, char *szDest, size_t nDestSize)
{
	const int STR_BUFFER_LENGTH = 256;
	size_t nLen = nSrcSize;
	if (nDestSize < nLen)
		nLen = nDestSize;

	//bool bStringData = false; // ASCII or unknown data type
	//_TODO: char *szRawData = (char *)_alloca(g_DataTypeSizeInByte[DT_ASCII] + 1);
	//char *szRawData = (char *)malloc(g_DTSize[DT_ASCII] + 1);
#define SAFE_COVERT(T)	T _temp; \
	memcpy(&_temp, szSrcData, sizeof(T));

	// Modified to avoid buffer overflow
	char szRawData[STR_BUFFER_LENGTH];	
	memset(szRawData, 0, sizeof(szRawData));
	
	const char *szTemp;

	switch(cDataType)
	{
	case DT_CHAR:		// 8 bit signed integer value
		ACE_OS::snprintf(szRawData, sizeof(szRawData), "%d", *(char *)(szSrcData) );
		break;
			
	case DT_SINT16:		// 16 bit Signed Integer value
		{
			SAFE_COVERT(short)
			ACE_OS::snprintf(szRawData, sizeof(szRawData), "%d", _temp );
		}
		break;

	case DT_UINT16:		// 16 bit Unsigned Integer value
		{
			SAFE_COVERT(unsigned short)
			ACE_OS::snprintf(szRawData, sizeof(szRawData), "%u", _temp);
		}
		break;

	case DT_FLT:		// 32 bit IEEE float
		{
			SAFE_COVERT(float)
			ACE_OS::snprintf(szRawData, sizeof(szRawData), "%f", _temp);
		}
		break;

	case DT_BIT:		// 1 bit value
		ACE_OS::snprintf(szRawData, sizeof(szRawData), "%u", (*(unsigned char *)(szSrcData)) != 0 );
		break;

	case DT_UCHAR:		// 8 bit unsigned integer value
		ACE_OS::snprintf(szRawData, sizeof(szRawData), "%u", *(unsigned char *)(szSrcData) );
		break;
	case DT_ULONG:		// 32 bit integer value
	case DT_DATE:
	case DT_TOD:
	case DT_DT:
		{
			SAFE_COVERT(unsigned int)
			ACE_OS::snprintf(szRawData, sizeof(szRawData), "%u", _temp);
		}
		break;

	case DT_SLONG:		// 32 bit signed integer value
		{
			SAFE_COVERT(int)
			ACE_OS::snprintf(szRawData, sizeof(szRawData), "%d", _temp);
		}
		break;

	case DT_DBL:		// 64 bit double
		{
			SAFE_COVERT(double)
			ACE_OS::snprintf(szRawData, sizeof(szRawData), "%f", _temp);
		}
		break;

	case DT_INT64:		// 64 bit signed integer value
		//_TODO:ACE_OS::snprintf(szRawData, "%d", *(__int64 *)(hNtf->szDataBuf) );
		{
			SAFE_COVERT(ACE_INT64)
			ACE_OS::snprintf(szRawData, sizeof(szRawData), "%lld", _temp);
		}
		break;

	case DT_UINT64:		// 64 bit unsigned integer value
			//_TODO: ACE_OS::snprintf(szRawData, "%u", *(unsigned __int64 *) _atoi64(hNtf->szDataBuf) );
			//ACE_OS::snprintf(szRawData, "%u", *(ACE_UINT64 *) ACE_OS::atoi(hNtf->szDataBuf) );
		{
			SAFE_COVERT(ACE_UINT64)
			ACE_OS::snprintf(szRawData, sizeof(szRawData), "%llu", _temp);
		}
		break;

	case DT_LTIME:		// 64 bit cv time (second + usecond)
		{
			TCV_TimeStamp ts;
			memcpy(&ts, szSrcData, sizeof(TCV_TimeStamp));
			CastTime2Buffer(szRawData, ICV_TXTVALUE_MAXLEN, ts);
		}
		break;

	case DT_QUALITY:
		{
			SAFE_COVERT(unsigned short)
			szTemp = GetQualityDesc(_temp);
			Safe_CopyString(szDest, szTemp, nDestSize);
		}
		return ICV_SUCCESS;
		// break;
	case DT_SUBQUALITY:
		{
			SAFE_COVERT(unsigned short)
			//��������չ��Ϣ������ʹ����չ�ӿ�ת��SubQuality
			szTemp = GetQualitySubStatusDescEx(_temp);
			Safe_CopyString(szDest, szTemp, nDestSize);
		}

		return ICV_SUCCESS;

	case DT_VTQ:		// value time quality!
		{
			//RS:111942
			SAFE_COVERT(TPBDataSchema0)
			char* szPtr = (char*)szSrcData;
			szPtr += (sizeof(_temp.m_timestamp) + sizeof(_temp.m_quality) + sizeof(_temp.m_datasize) + 1 + sizeof(_temp.m_chPad));
			
			if( _temp.m_quality.nQuality == QUALITY_BAD)
			{
				Safe_CopyString(szDest, ICV_BAD_QUALITY_STRING_VALUE, nDestSize);
				return EC_ICV_DATA_QUALITY_BAD;
			}
			return CastTypeToASCII(szPtr, _temp.m_datasize, _temp.m_ucDataType, szDest, nDestSize);
		}
	case DT_BLOB:		// blob, maximum 65535
		// �޸�����BlobתASCII����ÿһ���ֽ�תΪ���ֽ�ASCII�ַ���ʾ
		nLen = 0;
		do
		{
			// �޷��ٷ��������ֽڽ���ת�������Ѿ�ת�����
			if (nDestSize < 2*nLen+3 || nSrcSize<=nLen)
			{
				szDest[2*nLen] = '\0';
				break;
			}
			ACE_OS::snprintf(szDest+2*nLen, nDestSize-2*nLen, "%.2X", (unsigned char)(*(szSrcData+nLen)));
			nLen++;
		}while(1);
		return ICV_SUCCESS;

	case DT_TIME:		// 4 byte TIME (H:M:S:T)
	case DT_ASCII:		// ASCII string, maximum: 255
	default:
		//bStringData = true;
		strncpy(szDest, szSrcData, nLen);
		if (nDestSize > nLen)
			szDest[nLen] = 0;
		else 
			szDest[nDestSize-1] = 0;

		return ICV_SUCCESS;
	}

		// copy data to output buffer
	//if (!bStringData)
	//{
	Safe_CopyString(szDest, szRawData, nDestSize);
	//nLen = strlen(szRawData);
	//if (nDestSize < nLen)
	//	nLen = nDestSize;
	//strncpy(szDest, szRawData, nLen);
	//if (nDestSize > nLen)
	//	szDest[nLen] = 0;
	//}
	return ICV_SUCCESS;
}


long CastCvVTQ2Items(const char *szSrcData, size_t nSrcSize, unsigned short &nQuality, 
	unsigned char& nDataType, TCV_TimeStamp& time, char *&szDest, size_t& nDestSize)
{
	TPBDataSchema0 _temp;
	char* szPtr = (char*)szSrcData;
	memcpy(&_temp, szSrcData, sizeof(TPBDataSchema0));

	szPtr += (sizeof(_temp.m_timestamp) + sizeof(_temp.m_quality) + sizeof(_temp.m_datasize) + 1 + sizeof(_temp.m_chPad));

	nQuality = _temp.m_quality.nQuality ;
	nDataType = _temp.m_ucDataType;
	time.tv_sec = _temp.m_timestamp.tv_sec;
	time.tv_usec = _temp.m_timestamp.tv_usec;

	nDestSize = _temp.m_datasize;
	szDest = szPtr;

	return ICV_SUCCESS;
}


static const char HEX[16] = {
	'0', '1', '2', '3',
	'4', '5', '6', '7',
	'8', '9', 'a', 'b',
	'c', 'd', 'e', 'f'
};

/**
 *  $(Desp) ���16����.
 *  $(Detail) .
 *
 *  @param		-[in]  const unsigned char * szBuffer : ��Ҫת��Ϊ16������ʾ�����ݻ�����
 *  @param		-[in]  unsigned int nBuffeLen : ����������
 *  @param		-[out]  char * szHexBuf : 16�������������,����Ϊ�յ�ʱ��,ͨ��pnHexBufLen������Ҫ�ĳ���
 *  @param		-[out]  unsigned int * pnHexBufLen : 16�����������������,��szHexBufΪ��ʱ,pnHexBufLen������Ҫ�ĳ���
 *  @return		unsigned int.
 *
 *  @version	6/5/2013  baoyuansong  Initial Version.
 */
unsigned int HexDumpBuf( const unsigned char *szBuffer, unsigned int nBuffeLen, char *szHexBuf, unsigned int *pnHexBufLen )
{
	if (NULL == szBuffer || NULL == pnHexBufLen || nBuffeLen <= 0)
		return -1;

	if (NULL == szHexBuf)
	{
		*pnHexBufLen = 3*nBuffeLen;
		return EC_ICV_COMM_BUFFERTOOSHORT;
	}

	memset(szHexBuf, 0, *pnHexBufLen);

	//assure there is a terminal character
	if (*pnHexBufLen < 3*nBuffeLen)
	{
		*pnHexBufLen = 3*nBuffeLen ;
		return EC_ICV_COMM_BUFFERTOOSHORT;
	}

	for(unsigned int i = 0; i < nBuffeLen; i++) 
	{
		unsigned char t = szBuffer[i];
		szHexBuf[i*3] = HEX[t/16];
		szHexBuf[i*3 + 1] = HEX[t%16];
		szHexBuf[i*3 + 2] = ' ';
	}

	szHexBuf[3*nBuffeLen - 1] = 0;

	return ICV_SUCCESS;
}


}
