cmake_minimum_required(VERSION 3.10)
############FOR_MODIFIY_BEGIN#######################
#Setting Project Name
PROJECT (drcomm)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS}	CVComm.cxx 
					CVCommImpl.cpp
					CVProcessController.cpp 
					cvFileHelper.cpp 
					cvStringHelper.cpp 
					TypeCastString.cpp
					cvRegistryHelper.cpp
					cvRedisAccess.cpp
					NodeTable.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME drcomm)
#Setting library type used when build a library
#SET(LIB_TYPE STATIC)

SET(LIB_TYPE SHARED)

IF(WIN32)
SET(LINK_LIBS ACE cppsqlite tinyxml intl iconv Win32_Interop)
ELSE(WIN32)
SET(LINK_LIBS ACE cppsqlite tinyxml)
ENDIF(WIN32)

ADD_DEFINITIONS(-DCV6Comm_EXPORTS)

IF(HPUX)
SET(LINK_LIBS ${LINK_LIBS} pthread  )
ENDIF(HPUX)

############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)
IF(MSVC)
	if( CMAKE_SIZEOF_VOID_P EQUAL 8 )
		set_target_properties(${TARGET_NAME} PROPERTIES STATIC_LIBRARY_FLAGS "/machine:x64")
	endif( CMAKE_SIZEOF_VOID_P EQUAL 8 )
	SET_TARGET_PROPERTIES(${TARGET_NAME} PROPERTIES LINK_FLAGS "/ignore:4099")
ENDIF(MSVC)