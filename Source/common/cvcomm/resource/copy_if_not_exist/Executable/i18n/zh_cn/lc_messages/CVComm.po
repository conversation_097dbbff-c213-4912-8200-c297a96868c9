# SOME DESCRIPTIVE TITLE.
# Copyright (C) YEAR THE PACKAGE'S COPYRIGHT HOLDER
# This file is distributed under the same license as the PACKAGE package.
# <AUTHOR> <EMAIL>, YEAR.
#
#, fuzzy
msgid ""
msgstr ""
"Project-Id-Version: PACKAGE VERSION\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2012-01-18 14:08+0800\n"
"PO-Revision-Date: YEAR-MO-DA HO:MI+ZONE\n"
"Last-Translator: FULL NAME <EMAIL@ADDRESS>\n"
"Language-Team: LANGUAGE <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"

#: TypeCastString.cpp:67
msgid "Not Performed (RM INACTIVE)"
msgstr "不执行（冗余状态为非活动的）"

#: TypeCastString.cpp:68
msgid "Send To Driver Successfully"
msgstr "成功发送至驱动"

#: TypeCastString.cpp:69
msgid "Succeed"
msgstr "成功"

#: TypeCastString.cpp:70
msgid "Timeout"
msgstr "超时"

#: TypeCastString.cpp:71
msgid "Failed"
msgstr "失败"

#: TypeCastString.cpp:72
msgid "UnknownResult"
msgstr "未知结果"
