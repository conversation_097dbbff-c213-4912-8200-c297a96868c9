#ifndef _COMMON_TYPE_CAST_BASIC_H_
#define _COMMON_TYPE_CAST_BASIC_H_

#include "common/cvdefine.h"
#include "common/cv_datatype.h"
#include "common/CV_Time.h"
#include <cstdio>
#include "common/TypeCast.h"

/**
*  (copy a string with edge check).
*  (Detail).
*
*  @param  -[in,out]  char*  szDest: [dest]
*  @param  -[in,out]  const char*  szSrc: [source string]
*  @param  -[in,out]  long  nMaxDestLen: [including the trailing len]
*  @return (Return).
*
*  @version  03/28/2007  chenshengyu  Initial Version.
*/
/*inline void Safe_CopyString(char *szDest, const char *szSrc, long nMaxDestLen)
{
	if (!szSrc)
		return ;

	size_t nCopyLen = strlen(szSrc);
	if (nCopyLen >= nMaxDestLen)
		nCopyLen = nMaxDestLen - 1;
	strncpy(szDest, szSrc, nCopyLen);
	szDest[nCopyLen] ='\0';
}//*/

/**
*  Cast TimeStamp to Buffer.
*
*  @param  -[in,out]  ACE_TCHAR  date_and_time[]: [buffer to out]
*  @param  -[in]  int  date_and_timelen: [length of buffer]
*  @param  -[in]  ACE_Time_Value  cur_time: [time value]
*
*  @version     07/08/2008  chenzhiquan  Initial Version.
*/
//#define CastTime2BufferBasic(x,y,z) CVStringHelper::CastTime2Buffer(x,y,z)

/**
*  Cast Type To ASCII* Buffer.
*
*  @param  -[in,out]  string&  str: [string]
*  @param  -[in,out]  const char*  szSrcData: [source buffer]
*  @param  -[in,out]  char  cSrcDataType: [src data type]
*
*  @version     07/09/2008  chenzhiquan  Initial Version.
*/
static void CastTypeToASCII_Basic(const char *szSrcData, size_t nSrcSize, char cDataType, char *szDest, size_t nDestSize)
{
	size_t nLen = nSrcSize;
	const size_t STR_BUF_LENGTH =  256;
	if (nDestSize < nLen)
		nLen = nDestSize;

	//bool bStringData = false; // ASCII or unknown data type
	//TODO: char *szRawData = (char *)_alloca(g_DataTypeSizeInByte[DT_ASCII] + 1);
	//char *szRawData = (char *)malloc(g_DTSize[DT_ASCII] + 1);
	char szRawData[STR_BUF_LENGTH];
	memset(szRawData, 0, sizeof(szRawData));

	//char *szTemp;

	switch(cDataType)
	{
	case DT_CHAR:		// 8 bit signed integer value
		sprintf(szRawData, "%d", *(char *)(szSrcData) );
		break;

	case DT_SINT16:		// 16 bit Signed Integer value
		sprintf(szRawData, "%d", *(short *)(szSrcData) );
		break;
	case DT_UINT16:		// 16 bit Unsigned Integer value
		sprintf(szRawData, "%u", *(short *)(szSrcData) );
		break;
	case DT_FLT:		// 32 bit IEEE float
		sprintf(szRawData, "%f", *(float *)(szSrcData) );
		break;
	case DT_BIT:		// 1 bit value
		sprintf(szRawData, "%u", (*(unsigned char *)(szSrcData)) != 0 );
		break;

	case DT_UCHAR:		// 8 bit unsigned integer value
		sprintf(szRawData, "%u", *(unsigned char *)(szSrcData) );
		break;
	case DT_ULONG:		// 32 bit integer value
		sprintf(szRawData, "%u", *(unsigned int *)(szSrcData) );
		break;
	case DT_SLONG:		// 32 bit signed integer value
		sprintf(szRawData, "%d", *(int *)(szSrcData) );
		break;
	case DT_DBL:		// 64 bit double
		sprintf(szRawData, "%f", *(double *)(szSrcData) );
		break;

	case DT_LTIME:		// 64 bit cv time (second + usecond)
		{
			TCV_TimeStamp* pts = (TCV_TimeStamp*)szSrcData;
			//memcpy(&ts, szSrcData, sizeof(TCV_TimeStamp));
			cvcommon::CastTimeToASCII(szRawData, ICV_TXTVALUE_MAXLEN, *pts);
		}
		break;

	case DT_BLOB:		// blob, maximum 65535
	case DT_TIME:		// 4 byte TIME (H:M:S:T)
	case DT_ASCII:		// ASCII string, maximum: 127
	default:
		//bStringData = true;
		strncpy(szDest, szSrcData, nLen);
		if (nDestSize > nLen)
			szDest[nLen] = 0;
		return;
	}

	// copy data to output buffer
	//if (!bStringData)
	//{
	nLen = strlen(szRawData);
	if (nDestSize < nLen)
		nLen = nDestSize;
	strncpy(szDest, szRawData, nLen);
	if (nDestSize > nLen)
		szDest[nLen] = 0;
	//}
}


#endif //_COMMON_TYPE_CAST_BASIC_H_

