#ifndef _CV_REDIS_ACCESS_
#define _CV_REDIS_ACCESS_
#include "common/cvcomm.hxx"

#ifdef _WIN32
	#ifdef _STATERW_IMPLEMENT_DLL_
		#define STATERW_API_DEF extern "C" __declspec(dllexport)
	#else // _REDIS_IMPLEMENT_DLL_
		#define STATERW_API_DEF extern "C" __declspec(dllimport)
	#endif
#else // LINUX/UNIX
#define STATERW_API_DEF extern "C"
#endif

typedef void * STATERWINSTANCE;

STATERW_API_DEF long STATERW_HostInitialize(char *szIP, int nPort, struct timeval tv, STATERWINSTANCE *phInstState);
STATERW_API_DEF long STATERW_SetStringCommand(STATERWINSTANCE *phInstState, char *szKey, char *szValue);
STATERW_API_DEF long STATERW_GetStringCommand(STATERWINSTANCE *phInstState, char *szKey, char *szValue, long lValueLength);
STATERW_API_DEF long STATERW_DelStringCommand(STATERWINSTANCE *phInstState, char *szKey);
STATERW_API_DEF long STATERW_HostUnInitialize(STATERWINSTANCE phInstState);
STATERW_API_DEF long STATERW_RPUSHCommand(STATERWINSTANCE *phInstState, const char* szKey, const char* szValue);
STATERW_API_DEF long STATERW_EXPIRECommand(STATERWINSTANCE *phInstState, const char* szKey, unsigned int nTTL);
#endif
