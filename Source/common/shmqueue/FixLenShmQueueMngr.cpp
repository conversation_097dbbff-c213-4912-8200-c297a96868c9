/**************************************************************
 *  Filename:    ShmQueueMngr.cpp
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: ShmQueue Management.
 *    Handle Enviroment Access and so on..
 *
 *  @author:     chenzhiquan
 *  @version     06/05/2008  chenzhiquan  Initial Version
**************************************************************/

#include "include/common/FixLenShmQueueMngr.h"
#include <ace/Process_Mutex.h>
#include <ace/OS_NS_sys_stat.h>
#include "FixLenShmQueue.h"
#include "common/CVLog.h"
//#include "gettext/libintl.h"
//
//#define _(STRING) gettext(STRING)
//extern CCVLog CVLog;

/**
 *  Constructor.
 *
 *  @param  -[in]  const string&  strPath: [Path Of BerkeleyDB Enviroment]
 *
 *  @version     05/29/2008  chenzhiquan  Initial Version.
 */
CFixLenShmQueueMngr::CFixLenShmQueueMngr(const string& strPath): m_strPath(strPath)
{
	ACE_OS::mkdir(strPath.c_str());
}


/**
 *  Get Share Memory Queue.
 *
 *  @param  -[in,out]  const string&  szDBName: [comment]
 *  @param  -[in,out]  size_t  nRecordLength: [comment]
 *  @param  -[in,out]  bool  bCreateIfNotExist: [comment]
 *  @param  -[in,out]  size_t  nFileSizeNPage: [comment]
 *
 *  @version     01/05/2009  chenzhiquan  Initial Version.
 */
CShmQueueInterface* CFixLenShmQueueMngr::GetShmQueue(const string &szDBName, 
									  size_t nRecordLength, /*= BDBQUEUE_RECORD_LENGTH*/
									  bool bCreateIfNotExist, /*= true*/
									  size_t nMaxRecordsPerFile /*= 10240*/)
{
	CFixLenShmQueue* pShmQueue = NULL;

	{
		ACE_Read_Guard<ACE_RW_Mutex> readGuard(m_rwMutex);
		if (m_mapShmQueue.find(szDBName, pShmQueue) == ICV_SUCCESS)
		{
			// Found!
			return pShmQueue;
		}
	}

	{
		ACE_Write_Guard<ACE_RW_Mutex> writeGuard(m_rwMutex);
		// Double Check
		if (m_mapShmQueue.find(szDBName, pShmQueue) == ICV_SUCCESS)
		{
			// Found!
			return pShmQueue;
		}

		pShmQueue = new CFixLenShmQueue(m_strPath, szDBName);
		long nErr = pShmQueue->Setup(bCreateIfNotExist, nMaxRecordsPerFile, nRecordLength);
		if (nErr != ICV_SUCCESS)
		{
			//CVLog.LogMessage(LOG_LEVEL_ERROR, ACE_TEXT("Error Open setup queue(%s, %s) with Error Code %d"), m_strPath.c_str(), szDBName.c_str(), nErr);
			delete pShmQueue;
			return NULL;
		}
		else
		{
			//CVLog.LogMessage(LOG_LEVEL_INFO, ACE_TEXT("Open setup queue(%s, %s) successfully"), m_strPath.c_str(), szDBName.c_str());
		}
		nErr = m_mapShmQueue.bind(szDBName, pShmQueue);
		if(nErr != ICV_SUCCESS)
		{
			//CVLog.LogMessage(LOG_LEVEL_ERROR, ACE_TEXT("Bind FixLen Queue(%s, %s) with Error Code %d"), m_strPath.c_str(), szDBName.c_str(), nErr);
		}
		else
		{
			//CVLog.LogMessage(LOG_LEVEL_INFO, ACE_TEXT("Bind FixLen Queue(%s, %s) successfully"), m_strPath.c_str(), szDBName.c_str());
		}

		return pShmQueue;
	}
}

/**
 *  Destructor.
 *
 *
 *  @version     05/29/2008  chenzhiquan  Initial Version.
 */
CFixLenShmQueueMngr::~CFixLenShmQueueMngr()
{
	ShmQueueMap::_Iterator it(m_mapShmQueue);
	for (ShmQueueMap::_Entry *i = 0; it.next(i) != 0; ++it)
	{
		CFixLenShmQueue* pQueue = i->int_id_;
		delete pQueue;
	}
}


/**
 *  Release A Queue.
 *	@param  -[in]  const string&  szDBName: [CShmQueue]
 *
 *  @version     08/02/2008  chenzhiquan  Initial Version.
 */
long CFixLenShmQueueMngr::ReleaseQueue( CShmQueueInterface * pShmQueue, bool bRemoveFile )
{
	if (!pShmQueue)
	{
		return ICV_SUCCESS;
	}

	{
		ACE_Write_Guard<ACE_RW_Mutex> writeGuard(m_rwMutex);
		return this->ReleaseQueue_i(pShmQueue->GetQueFileName(), bRemoveFile);
	}
}

/**
 *  Release Queue.
 *
 *  @param  -[in,out]  const string&  strDBName: [comment]
 *
 *  @version     08/02/2008  chenzhiquan  Initial Version.
 */
long CFixLenShmQueueMngr::ReleaseQueue( const string &strDBName, bool bRemoveFile )
{
	ACE_Write_Guard<ACE_RW_Mutex> writeGuard(m_rwMutex);
	return this->ReleaseQueue_i(strDBName, bRemoveFile);
}

long CFixLenShmQueueMngr::ReleaseQueue_i( const string &strDBName, bool bRemoveFile )
{
	CFixLenShmQueue * pShmQueue = NULL;
	if(m_mapShmQueue.unbind(strDBName, pShmQueue) == ICV_SUCCESS)
	{// Found
		pShmQueue->TearDown(bRemoveFile);
		delete pShmQueue;
 		return ICV_SUCCESS;
	}
	return EC_ICV_BDBQUEUE_NOT_FOUND;
}

