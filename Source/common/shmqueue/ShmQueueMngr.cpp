/**************************************************************
 *  Filename:    ShmQueueMngr.cpp
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: ShmQueue Management.
 *    Handle Enviroment Access and so on..
 *
 *  @author:     chenzhiquan
 *  @version     06/05/2008  chenzhiquan  Initial Version
**************************************************************/

#include "common/ShmQueueMngr.h"
#include <ace/Process_Mutex.h>
#include <ace/OS_NS_sys_stat.h>
#include "ShmQueue.h"

/**
 *  Constructor.
 *
 *  @param  -[in]  const string&  strPath: [Path Of BerkeleyDB Enviroment]
 *
 *  @version     05/29/2008  chenzhiquan  Initial Version.
 */
CShmQueueMngr::CShmQueueMngr(const string& strPath): m_strPath(strPath)
{
	ACE_OS::mkdir(strPath.c_str());
}


/**
 *  Get CShmQueue from DBName.
 *
 *  @param  -[in]  const string&  szDBName: [comment]
 *  @param  -[in]  size_t  nRecordLength: [comment]
 *  @return NULL if failed.
 *
 *  @version     07/01/2008  chenzhiquan  Initial Version.
 */
CShmQueueInterface* CShmQueueMngr::GetShmQueue(const string &szDBName, 
									  size_t nRecordLength, /*= BDBQUEUE_RECORD_LENGTH*/
									  bool bCreateIfNotExist, /*= true*/
									  size_t nMaxRecordsPerFile /*= 10240*/)
{
	CShmQueueInterface* pShmQueue = NULL;

	{
		ACE_Read_Guard<ACE_RW_Mutex> readGuard(m_rwMutex);
		if (m_mapShmQueue.find(szDBName, pShmQueue) == ICV_SUCCESS)
		{// Found!
			return pShmQueue;
		}
	}

	{
		ACE_Write_Guard<ACE_RW_Mutex> writeGuard(m_rwMutex);
		// Double Check
		if (m_mapShmQueue.find(szDBName, pShmQueue) == ICV_SUCCESS)
		{// Found!
			return pShmQueue;
		}

		pShmQueue = new CShmQueue(m_strPath, szDBName, nRecordLength);
		long nErr = ((CShmQueue *)pShmQueue)->SetBDB(bCreateIfNotExist, nMaxRecordsPerFile);
		if (nErr != ICV_SUCCESS)
		{
			//LH_LOG((LOG_LEVEL_ERROR, ACE_TEXT("Error Open bdb \"%s\" File with Error Code %d"), szDBName.c_str(), nErr));
			delete pShmQueue;
			return NULL;
		}
		m_mapShmQueue.bind(szDBName, pShmQueue);
		return pShmQueue;
	}
}

/**
 *  Destructor.
 *
 *
 *  @version     05/29/2008  chenzhiquan  Initial Version.
 */
CShmQueueMngr::~CShmQueueMngr()
{
	ShmQueueMap::_Iterator it(m_mapShmQueue);
	for (ShmQueueMap::_Entry *i = 0; it.next(i) != 0; ++it)
	{
		CShmQueue* pQueue = (CShmQueue *)i->int_id_;
		delete pQueue;
	}
}


/**
 *  Release A Queue.
 *	@param  -[in]  const string&  szDBName: [CShmQueue]
 *
 *  @version     08/02/2008  chenzhiquan  Initial Version.
 */
long CShmQueueMngr::ReleaseQueue( CShmQueueInterface * pShmQueue, bool bRemoveFile )
{
	if (!pShmQueue)
	{
		return ICV_SUCCESS;
	}

	{
		CShmQueue *pShareMemQueue = (CShmQueue*) pShmQueue;
		ACE_Write_Guard<ACE_RW_Mutex> writeGuard(m_rwMutex);
		return this->ReleaseQueue_i(pShareMemQueue->m_strQueFileName, bRemoveFile);
	}
	
}

/**
 *  Release Queue.
 *
 *  @param  -[in,out]  const string&  strDBName: [comment]
 *
 *  @version     08/02/2008  chenzhiquan  Initial Version.
 */
long CShmQueueMngr::ReleaseQueue( const string &strDBName, bool bRemoveFile )
{
	ACE_Write_Guard<ACE_RW_Mutex> writeGuard(m_rwMutex);
	return this->ReleaseQueue_i(strDBName, bRemoveFile);
}

long CShmQueueMngr::ReleaseQueue_i( const string &strDBName, bool bRemoveFile )
{
	CShmQueueInterface * pShmQueue = NULL;
	if(m_mapShmQueue.unbind(strDBName, pShmQueue) == ICV_SUCCESS)
	{// Found
		string strDBFileName(strDBName);
		delete pShmQueue;
// 		if (bRemoveFile)
// 		{
// 			m_pDbEnv->dbremove(NULL, strDBFileName.c_str(), NULL, DB_AUTO_COMMIT);
// 			
// 		}
 		return ICV_SUCCESS;
	}
	return EC_ICV_BDBQUEUE_NOT_FOUND;
}

