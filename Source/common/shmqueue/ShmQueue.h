/**************************************************************
 *  Filename:    ShmQueue.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: ShmQueue.
 *
 *  @author:     ch<PERSON><PERSON>quan
 *  @version     06/05/2008  chenzhiquan  Initial Version
**************************************************************/
#ifndef _BDB_QUEUE_H_
#define _BDB_QUEUE_H_

#include "common/CommHelper.h"
#include "common/BufHelper.h"
#include "errcode/ErrCode_iCV_Common.hxx"
#include <ace/Process_Mutex.h>
#include <ace/Process_Semaphore.h>
#include "common/ShmQueueInterface.h"

typedef long (* ShmQueue_CallBack)(void*, char*, size_t); 

class ACE_Shared_Memory_MM;

class CShmQueue : public CShmQueueInterface
{
UNITTEST(CShmQueue);
friend class CShmQueueMngr;

typedef struct 
{
	unsigned char uchIndex;	//	Index Of Records
	unsigned char uchTotal;	//	Total Records 
	unsigned short usLength;//	Length Of Current Records
}tagShmQueue_head;			// Header of Records

typedef struct  
{
	ACE_UINT16	nReadRecordNo;
	ACE_UINT16	nWriteRecordNo;
	ACE_UINT32	nReadFileNo;
	ACE_UINT16	nWriteFileNo;
}tagShmQueue_Ctrl;

private:
	size_t m_nRecordLength;	// Length Of one Record in BerkeleyDB Queue, cannot be changed after initial
	string m_strRdFilePath;	// FilePath For Read
	string m_strWrFilePath; // FilePath For Write
	string m_strPath;


	ACE_Shared_Memory_MM*	m_pShmCtrl;				// Share Mem Pointer For Control Block
	ACE_Shared_Memory_MM*	m_pShmRead;				// Share Mem Pointer For Read Block
	ACE_Shared_Memory_MM*	m_pShmWrite;			// Share Mem Pointer For Write Block
	ACE_Process_Mutex		m_CtrlProcessMutex;		// Process Mutex For Control
	ACE_Process_Semaphore	m_Semaphore;			// Semaphore Used For Comunicate
	tagShmQueue_Ctrl*		m_pCtrlRecord;
	bool					m_bWakeUp;

private:
	CShmQueue(const string &strPath, const string &szDBFileName, size_t nRecordLength = SHMQUEUE_RECORD_LENGTH);
	virtual ~CShmQueue();
	long SetBDB(bool bCreateIfNotExist, size_t nFileNPages);

	long PushBlock(IN const char* szBuf, IN size_t nLength);
	long PopBlock(CBufferHelper<char> &szBuf);

public:
	// enqueue a buffer to bdbqueue
	virtual long EnQueue(IN const char* szBuf, IN size_t nLength);
	
	// dequeue from bdbqueue
	virtual long DeQueue(IN ShmQueue_CallBack CallBack, OUT void* pParam);

	// get queue records' number
	virtual long GetQueueRecordNum(OUT long* lCount);

	// wake up waiting queue
	virtual void WakeUpDeQueue();

};

#endif//_BDB_QUEUE_H_
