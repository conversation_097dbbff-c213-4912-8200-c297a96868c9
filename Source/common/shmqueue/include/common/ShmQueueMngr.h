/**************************************************************
 *  Filename:    ShmQueueMngr.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: ShmQueue Manager.
 *
 *  @author:     chen<PERSON>quan
 *  @version     06/05/2008  chenzhiquan  Initial Version
**************************************************************/
#ifndef _BDB_QUEUE_MNGR_H_
#define _BDB_QUEUE_MNGR_H_
#include "common/ShmQueueInterface.h"
#include "common/stl_inc.h"
#include "common/HashMap.h"
#include <ace/Process_Mutex.h>


class CShmQueueMngr : public CShmQueueMngrInterface
{
	typedef Hash_Map_NoMutex<string, CShmQueueInterface*> ShmQueueMap;
private:
	string m_strPath;
	ShmQueueMap m_mapShmQueue;
	ACE_RW_Mutex m_rwMutex;

public:
	CShmQueueMngr(const string& strPath);

	virtual ~CShmQueueMngr();

	// Get BDB Queue Object From ShmQueue Manager.
	virtual CShmQueueInterface*	GetShmQueue(const string &strDBName, 
							size_t nRecordLength = SHMQUEUE_RECORD_LENGTH, 
							bool bCreateIfNotExist = true,
							size_t nMaxRecordsPerFile = 10240);
	virtual long ReleaseQueue(CShmQueueInterface *pShmQueue, bool bRemoveFile = false);
	virtual long ReleaseQueue(const string &strDBName, bool bRemoveFile = false);
private:
	long ReleaseQueue_i(const string &strDBName, bool bRemoveFile = false);
};

#endif// _BDB_QUEUE_MNGR_H_
