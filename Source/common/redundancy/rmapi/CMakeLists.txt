cmake_minimum_required(VERSION 3.10)
############FOR_MODIFIY_BEGIN#######################
#Setting Project Name
PROJECT (drrmapi)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} RMAPI.cpp ../rmservice/ShareMemHelper.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME drrmapi)
#Setting library type used when build a library
#STATIC/SHARED
SET(LIB_TYPE SHARED)

SET(LINK_LIBS ACE drcomm drlog drlogimpl)
IF(HPUX)
SET(LINK_lIBS ${LINK_LIBS} pthread)
ENDIF(HPUX)

############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)
