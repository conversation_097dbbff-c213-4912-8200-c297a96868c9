/**************************************************************
 *  Filename:    RMConfigLoader.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Configuation Loader.
 *
 *  @author:     chen<PERSON><PERSON>n
 *  @version     05/20/2008  chenzhiquan  Initial Version
**************************************************************/
#ifndef _RM_CONFIG_LOADER_H_
#define _RM_CONFIG_LOADER_H_

#include <ace/INET_Addr.h>
//#include "common/pugxml.h"
#include "tinyxml/tinyxml.h"
#include "RMPeerCommunicator.h"
#include "RMStatusCtrl.h"

#include "common/stl_inc.h"
#include "common/CommHelper.h"
#include "common/RMConfigXMLDef.h"

#define RM_SVC_MIN_INTERVAL		50
#define RM_SVC_MAX_INTERVAL		3000
#define RM_SVC_MIN_THRESHOLD	4


#define CHECK_NULL_RETURN_ERR(X) \
	{if(!X) return EC_ICV_RMSERVICE_CONFIG_FILE_STRUCTURE_ERROR;}

class DSFRMConfigLoader
{
	UNITTEST(DSFRMConfigLoader);
private:
	// Load Strategy
	long LoadStrategy(TiXmlElement* node, DSFRMStatusCtrl *pRMStatusCtrl);
	// Load Peer Communicator
	long LoadPeerCommunicator(TiXmlElement* node, DSFRMStatusCtrl *pRMStatusCtrl);
	// Load UDP Communicator
	long LoadUDPCommunicator(TiXmlElement *node,  DSFRMStatusCtrl *pRMStatusCtrl, DSFRMPeerCommunicator* pComm);
	// Load Core Services
	long LoadCoreServices(TiXmlElement* node, DSFRMStatusCtrl *pRMStatusCtrl);

public:
	// Load Config File
	long LoadConfig(const string &strFile, DSFRMStatusCtrl *pRMStatusCtrl);
	// Load Fixed Config File
	long LoadFixedConfig(const string &strFile, DSFRMStatusCtrl *pRMStatusCtrl);
	// Get Sequence
	long GetSequence();
	// Get Strategy Information.
	void GetStrategyInfo(long &nInterval, long &nThreshold);
	// Get Peer Communicator
	DSFRMPeerCommunicator* GetPeerComm();

};

#endif//_RM_CONFIG_LOADER_H_
