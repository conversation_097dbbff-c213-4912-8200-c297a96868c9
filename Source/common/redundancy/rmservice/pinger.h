//
// ping.h
// ~~~~~~~~
//
// Copyright (c) 2003-2008 <PERSON> (chris at kohlhoff dot com)
//
// Distributed under the Boost Software License, Version 1.0. (See accompanying
// file LICENSE_1_0.txt or copy at http://www.boost.org/LICENSE_1_0.txt)
//
#ifndef _RM_PING_H_
#define _RM_PING_H_
#pragma once

//#include <ace/Singleton.h>
#include <boost/asio.hpp>
#include <boost/bind.hpp>
#include <boost/thread/thread.hpp>
#include <boost/thread/recursive_mutex.hpp> 
#include <boost/thread/mutex.hpp>
#include <boost/date_time/posix_time/posix_time.hpp>
#include <boost/shared_ptr.hpp>
#include <boost/progress.hpp>
#include <boost/function.hpp>
//#include <boost/interprocess/detail/atomic.hpp> 
#include <istream>
#include <iostream>
#include <ostream>
#include <cstdio>
#include "icmp_header.hpp"
#include "ipv4_header.hpp"
#include "RMPeerCommunicator.h"
//#include <ace/Guard_T.h>
	

using boost::asio::ip::icmp;
using boost::asio::deadline_timer;
namespace posix_time = boost::posix_time;

typedef boost::shared_mutex            WR_Mutex;  
typedef boost::unique_lock<WR_Mutex>   writeLock;  
typedef boost::shared_lock<WR_Mutex>   readLock;  


//ACE_RW_THREAD_MUTEX_H m_cMutex;

class CPinger
{
public:
	CPinger(boost::asio::io_service * io_service);
	virtual ~CPinger();
	void ping();
	void startthread();
	void stopthread();
	int setPingIP();
	//const char* destination;
	

	
	
	


public:
	boost::asio::io_service *m_io_service;
	//long lTimeoutTimes;
	//long lTimeout;
	

private:
	void start_send();
	void handle_timeout();
	void start_receive();
	void handle_receive(std::size_t length);
	static unsigned short get_identifier();
	icmp::resolver resolver_;
	icmp::endpoint destination_;
	icmp::socket socket_;
	deadline_timer timer_;
	unsigned short sequence_number_;
	posix_time::ptime time_sent_;
	boost::asio::streambuf reply_buffer_;
	std::size_t num_replies_;
	boost::shared_ptr<boost::thread> thrd;
	//bool m_bExit;
	
	
	
};

//CPinger m_p;
//typedef ACE_Singleton<CPinger,ACE_Thread_Mutex> CPingerSingletonl;
//#define  CPingerObj CPingerSingletonl::instance()

#endif