cmake_minimum_required(VERSION 3.10)

PROJECT(common)
SUBDIRS(cppsqlite proccomm cvcomm redundancy cvlog cvlogimpl cvsvcbase servicebase servicebasetest shmqueue os cviobuffer licverify)
# SUBDIRS(cppsqlite proccomm cvcomm redundancy cviobuffer cvlog cvlogimpl cvsvcbase epass2lite libexpr licverify servicebase shmqueue cvsock)
#SUBDIRS(cppsqlite proccomm cvcomm redundancy cviobuffer cvlog cvlogimpl cvsvcbase epass2lite libexpr licverify procmngr servicebase shmqueue rmdatasync cvsock)

# IF(${CMAKE_SYSTEM_NAME} MATCHES "Windows")
# 	#SUBDIRS(cvcomm/cvcommlibraryut cviobuffer/cviobufferut cvlog/cvlogut shmqueue/shmqueueut)
# 	#SUBDIRS(exceptionreport cvcpput)

# ENDIF(${CMAKE_SYSTEM_NAME} MATCHES "Windows")

# IF (UNIX)
	
# ELSE (UNIX)
# 	SUBDIRS(exceptionreport)
# ENDIF(UNIX)
