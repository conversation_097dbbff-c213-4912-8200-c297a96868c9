cmake_minimum_required(VERSION 3.10)

#Setting Project Name
PROJECT (drsvcbase)

############FOR_MODIFIY_BEGIN#######################
INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} cvsvcbaseimpl.cpp 
	)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME drsvcbase)
#Setting library type used when build a library
#SET(LIB_TYPE STATIC)
SET(LIB_TYPE SHARED)

SET(LINK_LIBS ACE drcomm tinyxml shmqueue drlog drlogimpl)
IF(HPUX)
SET(LINK_LIBS ${LINK_LIBS} pthread)
ENDIF(HPUX)

############FOR_MODIFIY_END#########################

INCLUDE($ENV{DRDIR}CMakeCommonLib)