cmake_minimum_required(VERSION 3.10)

############FOR_MODIFIY_BEGIN#######################
#Setting Project Name
PROJECT (cppsqlite)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} CppSQLite3.cpp )


IF(WIN32)
	SET(SRCS ${SRCS} CodingConv.cpp PathCheck.cpp)
ENDIF(WIN32)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME cppsqlite)
#Setting library type used when build a library
SET(LIB_TYPE STATIC)

############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)

target_link_libraries(cppsqlite PUBLIC $ENV{DRDIR}../library/libsqlite3.so)

IF(MSVC)
	if( CMAKE_SIZEOF_VOID_P EQUAL 8 )
		set_target_properties(${TARGET_NAME} PROPERTIES STATIC_LIBRARY_FLAGS "/machine:x64")
	endif( CMAKE_SIZEOF_VOID_P EQUAL 8 )
ENDIF(MSVC)

