cmake_minimum_required(VERSION 3.10)

PROJECT (os)
SET(TARGET_NAME drhdOS)

INCLUDE($ENV{DRDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
#Setting Source Files
IF (CMAKE_SYSTEM_NAME MATCHES Windows)
	SET(SRCS ${SRCS}
		OSWindows/os_dir.c
		OSWindows/os_event.c
		OSWindows/os_file.c
		OSWindows/os_io.c
		OSWindows/os_language.c
		OSWindows/os_lock.c
		OSWindows/os_mem.c
		OSWindows/os_network.cpp
		OSWindows/os_process.c
		OSWindows/os_sa.c
		OSWindows/os_service.c
		OSWindows/os_thread.c
		OSWindows/os_time.c)
	SET(LINK_LIBS ${LINK_LIBS} Ws2_32 iphlpapi)	
ELSE (CMAKE_SYSTEM_NAME MATCHES Windows)
	SET(SRCS ${SRCS} 
		OSLinux/os_dir.c
		OSLinux/os_event.c
		OSLinux/os_file.c
		OSLinux/os_io.c
		OSLinux/os_language.c
		OSLinux/os_lock.c
		OSLinux/os_mem.c
		OSLinux/os_network.c
		OSLinux/os_process.c
		OSLinux/os_service.c
		OSLinux/os_thread.c
		OSLinux/os_time.c)
ENDIF (CMAKE_SYSTEM_NAME MATCHES Windows)
#Setting Target Name (executable file name | library name)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

ADD_DEFINITIONS(-D_UNICODE)
ADD_DEFINITIONS(-DOS_EXPORTS)

IF(UNIX)
 IF(${CMAKE_SYSTEM_NAME} MATCHES SunOS)
	SET(LINK_LIBS ${LINK_LIBS} socket nsl)
 ENDIF()
 IF(${CMAKE_SYSTEM_NAME} MATCHES HP-UX)
	SET(LINK_LIBS ${LINK_LIBS} rt)
 ENDIF(${CMAKE_SYSTEM_NAME} MATCHES HP-UX)
ENDIF()

############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)

