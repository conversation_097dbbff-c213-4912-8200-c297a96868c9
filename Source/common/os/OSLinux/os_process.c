#include "os/os_process.h"
#include "errcode/error_code.h"
#include "errcode/hd_error_code.h"
#include "errno.h"
#include <string.h>
#include <stdlib.h>
#include <assert.h>
#include <signal.h>
#include <sys/wait.h>

int32 os_process_spawn(const char *szFileName, const char *argv, ProcessHandle *pProcess)
{
	int32 nRet=0;
	int32 i=0;
	assert(szFileName != NULL);
	assert(pProcess != NULL);

	*pProcess = fork();
	if (*pProcess < 0)
	{
		return EC_RD_PROCESS_SPAWN;
	}
	else if (*pProcess == 0)	// child process
	{
		bool bAbsolutePath = false;
		int nRet;
		int i = 0;
		while (szFileName[i] != '\0')
		{
			if (szFileName[i] == '/')
			{
				bAbsolutePath = true;
				break;
			}
			i++;
		}
		if (bAbsolutePath == false)
		{
			int nLen = strlen(szFileName);
			char *szNewFileName = (char*)malloc(nLen + 3); /* "./" and '\0' */
			strcpy(szNewFileName, "./");
			strcat(szNewFileName, szFileName);
			nRet = execl(szNewFileName, argv, NULL);
			free(szNewFileName);
		}
		else
		{
			nRet = execl(szFileName, argv, NULL);
		}
		if (nRet < 0)
		{
			return EC_RD_PROCESS_SPAWN;
		}
	}
	return RD_SUCCESS;
}

int32 os_process_wait(ProcessHandle hProcess)
{
	if (waitpid(hProcess, NULL, 0) < 0)
		return EC_RD_PROCESS_WAIT_ERROR;

	return RD_SUCCESS;
}

int32 os_process_terminate(ProcessHandle hProcess)
{
	if (kill(hProcess, SIGKILL) != 0)
	{
		return errno;
	}

	return RD_SUCCESS;
}

int32 os_process_get_curr_id()
{

	int nPid;
	nPid = (int)getpid();
	return nPid;
}

	
