/** 
* @file
* @brief		directory operation function
* <AUTHOR>
* @date			2009/10/13
* @version		Initial Version
* Create directory, pass through files under the directory.
*/

#include "os_dir.h"
#include "errcode/error_code.h"
#include "errcode/hd_error_code.h"
#include <sys/stat.h>
#include <dirent.h>
#include <sys/types.h>
#include <libgen.h>
#include <assert.h>
#include <string.h>
#include <stdlib.h>
#include <errno.h>

#define DIR_MAX_FILE_NAME_LEN	260

/**
* @brief		create directory
* @param[in]	szPathName the directory name
* @return		RD_SUCCESS if success; EC_RD_OS_DIR_CREATE, otherwise
* @version		2009/10/13 Chunfeng Shen initial version
*/ 
int32 os_dir_create(const char *szPathName)
{
	int nRet;
	assert(szPathName != NULL);

	nRet = mkdir(szPathName, S_IRWXU | S_IRWXG | S_IROTH | S_IXOTH);
	if (nRet != 0 && errno != EEXIST)
		return EC_RD_OS_DIR_CREATE;

	return RD_SUCCESS;
}

/**
* @brief		Find first file name under the directory, 
				using os_dir_find_next_file can get all files under a directory
* @param [in]	szPathName path name
* @param [out]	szFileName file name returned, this is only valid when function returns RD_SUCCESS
* @param [out]	pDir structure pointer which is useful when get next file
* @return		RD_SUCCESS, if success; error code if fail.
* @see			os_dir_find_next_file
* @version		2009/10/13 Chunfeng Shen Initial version
*/ 
int32 os_dir_find_first_file(const char *szPathName, char *szFileName, OSDIR *pDir)
{
	struct dirent *pDirent;
	assert(szPathName != NULL);
	assert(szFileName != NULL);
	assert(pDir != NULL);

	if (strlen(szPathName) < 1)
		return EC_RD_OS_DIR_INVALID_PATH_NAME;

	pDir->pDir = opendir(szPathName);
	if (NULL == pDir->pDir)
		return EC_RD_OS_DIR_FIND_FILE;
	
	errno = 0;
	while ((pDirent = readdir(pDir->pDir)) != NULL)
	{
		if (strcmp(pDirent->d_name, ".") == 0 ||
			strcmp(pDirent->d_name, "..") == 0)
			continue;

		strcpy(szFileName, pDirent->d_name);
		return RD_SUCCESS;
	}

	if (0 == errno)
	{
		closedir(pDir->pDir);
		return EC_RD_OS_DIR_FIND_NO_FILE;
	}

	closedir(pDir->pDir);
	return EC_RD_OS_DIR_FIND_FILE;
}

/**
* @brief		Find next file name under the directory
* @param [out]	szFileName file name returned, this is only valid when function returns RD_SUCCESS
* @param [in,out]	pDir structure pointer which is useful when get next file
* @return		RD_SUCCESS, if success; error if fail.
* @see			os_dir_find_first_file
* @note			this function must be called after calling os_dir_find_first_file
* @version		2009/10/13 Chunfeng Shen Initial version
*/ 
int32 os_dir_find_next_file(char *szFileName, const OSDIR *pDir)
{
	struct dirent *pDirent;
	int nRet;
	assert(szFileName != NULL);
	assert(pDir != NULL);

	while ((pDirent = readdir(pDir->pDir)) != NULL)
	{
		if (0 != errno)
		{
			nRet =errno;
			closedir(pDir->pDir);
			return nRet;
		}

		if (strcmp(pDirent->d_name, ".") == 0 ||
			strcmp(pDirent->d_name, "..") == 0)
			continue;

		strcpy(szFileName, pDirent->d_name);
		return RD_SUCCESS;
	}

	nRet = errno;
	closedir(pDir->pDir);

	if (0 == nRet)	// finish searching
	{
		return EC_RD_OS_DIR_FIND_NO_FILE;
	}
	else
	{
		return nRet;
	}
}

/**
* @brief		delete all files under directory
* @param [in]	szPathName the directory name
* @return		RD_SUCCESS, if success; Error code if fail
* @version		2009/10/13 Chunfeng Shen initial version
*/ 
int32  os_dir_delete_files(const char * szPathName)
{
	int nRet;
	int nLenPathName;
	char* szFileName;
	OSDIR dirFind;
	assert(szPathName != NULL);

	nLenPathName = strlen(szPathName);
	szFileName = (char*)malloc(nLenPathName + DIR_MAX_FILE_NAME_LEN);
	if (NULL == szFileName)
	{
		return EC_RD_OS_MEM_MALLOC;
	}

	strcpy(szFileName, szPathName);
	if (szPathName[nLenPathName - 1] != '/' && 
		szPathName[nLenPathName - 1] != '\\')
	{
		strcat(szFileName, "/");
		nLenPathName++;
	}

	nRet = os_dir_find_first_file(szPathName, szFileName + nLenPathName, &dirFind);
	if (nRet != RD_SUCCESS)
	{
		free(szFileName);
		if (EC_RD_OS_DIR_FIND_NO_FILE == nRet)
			return RD_SUCCESS;

		return nRet;
	}

	do 
	{
		if (remove(szFileName) != 0)
		{
			free(szFileName);
			return EC_RD_OS_FILE_DELETE;
		}
		nRet = os_dir_find_next_file(szFileName + nLenPathName, &dirFind);
	} while (nRet == RD_SUCCESS);
	free(szFileName);
	return RD_SUCCESS;
}

/**
* @brief		remove a directory
* @param [in]	szPathName the directory name
* @return		RD_SUCCESS, if success; EC_RD_OS_DIR_REMOVE if fail.
* @version		2009/10/13 Chunfeng Shen initial version
*/ 
int32 os_dir_remove(const char * szPathName)
{
	assert(szPathName != NULL);

	os_dir_delete_files(szPathName);
	if (rmdir(szPathName) != 0)
		return EC_RD_OS_DIR_REMOVE;

	return RD_SUCCESS;
}

/**
* @brief		get current process path
* @param [in]	szPathName  the pathname buffer pointer
* @param [in]	nSize		the pathname buffer size
* @return		the real path name size
* @note			the pathname size usually is the max path size
				this function is used only on Linux operating system
* @version		2009/10/29 Huang Songxin initial version
*/
int32 os_dir_get_curr_process_path(char* szPathName, int32 nSize)
{
	int nRet;
	assert(nSize > 0);
	assert(NULL != szPathName);
	
	nRet = readlink("/proc/self/exe", szPathName, nSize);
	szPathName[nRet] = '\0';
	return nRet;
}

