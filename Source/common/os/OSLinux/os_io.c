#include "os_io.h"
#include <termios.h>
#include <sys/ioctl.h>
#include <sys/time.h>
#include <sys/types.h>
#include <stdio.h>
#include <stdlib.h>
#include <fcntl.h>
#include <unistd.h>
#include <string.h>

int os_kb_hit()
{
	struct termios term, oterm;
	int fd = 0;
	int c = 0;
	/* get the terminal settings */
	tcgetattr(fd, &oterm);
	/* get a copy of the settings, which we modify */
	memcpy(&term, &oterm, sizeof(term));
	/* put the terminal in non-canonical mode, any reads timeout after 0.1 seconds or when a single character is read */
	term.c_lflag = term.c_lflag & (!ICANON);
	term.c_cc[VMIN] = 0;
	term.c_cc[VTIME] = 1;
	tcsetattr(fd, TCSANOW, &term);
	/* get input - timeout after 0.1 seconds or when one character is read. If timed out getchar() returns -1, otherwise it returns the character */
	c = getchar();
	/* reset the terminal to original state */
	tcsetattr(fd, TCSANOW, &oterm);
	/* if we retrieved a character, put it back on the input stream */
	if (c != -1)
		ungetc(c, stdin);
	/* return 1 if the keyboard was hit, or 0 if it was not hit */
	return (c!=-1);
}
