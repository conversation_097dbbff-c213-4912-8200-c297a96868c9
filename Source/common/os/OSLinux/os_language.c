/** 
 * @file
 * @brief		Language operation
 * <AUTHOR>
 * @date		2010/03/10
 * @version		Initial Version
 *
 */
#include "os_language.h"
#include "errcode/error_code.h"
#include "errcode/hd_error_code.h"
#include <iconv.h>
#include <stdlib.h>
#include <stdio.h>
#include <string.h>

int32 code_convert(const char *from_charset, const char *to_charset,const char *inbuf, int inlen, char *outbuf, int outlen)
{
	char *in,*out;
	size_t in_left,out_left,mutant,converted;

	in_left = inlen;
	out_left=outlen;
	in=(char *)inbuf; 
	out=(char *)outbuf;

	iconv_t oConv=iconv_open(to_charset,from_charset);
	if(oConv==(iconv_t)(-1))
	{
		return EC_RD_LANGUAGE_ICONV_OPEN;
	}

	mutant=iconv(
		oConv,
		&in,&in_left,
		&out,&out_left);
	iconv_close(oConv);
	if(mutant==(size_t)(-1))
	{
		return EC_RD_LANGUAGE_ICONV;
	}

	if(outlen < out_left)
	{
		return EC_RD_LANGUAGE_ICONV;
	}
	converted=outlen-out_left;
	outbuf[converted]='\0';

	return RD_SUCCESS;
}

int32 os_lang_conv_sys_to_utf8(const char *szSrc, char *szDest, int nDesLen)
{
	char* szEnv;
	int	  nSrcLen;
	int32 nRet = RD_SUCCESS;

	if ((NULL == szSrc) || (NULL == szDest) || (nDesLen < 1))
		return EC_RD_LANGUAGE_ILLEGAL_INPUT;
	
	memset(szDest, 0, nDesLen);
	*(szDest + nDesLen - 1) = '\0';
	nDesLen = nDesLen -1;

	nSrcLen = strlen(szSrc);
	if (0 == nSrcLen)
	{
		*(szDest) = '\0';
		return RD_SUCCESS;
	}

	szEnv = getenv("LANG");
	if (NULL == szEnv)
	{//get env var LANG failed
		if (nDesLen < nSrcLen)
			return EC_RD_LANGUAGE_ILLEGAL_INPUT;
		strcpy(szDest, szSrc);
		return RD_SUCCESS;
	}

	if(strcmp(szEnv, "zh_CN.UTF-8") == 0 || strcmp(szEnv, "zh.UTF-8") == 0 || strcmp(szEnv, "zh.utf8") == 0
		|| strcmp(szEnv, "zh.utf-8") == 0 || strcmp(szEnv, "zh_CN.utf-8") == 0 || strcmp(szEnv, "zh_CN.utf8") == 0)
	{
		if (nDesLen < nSrcLen)
			return EC_RD_LANGUAGE_ILLEGAL_INPUT;
		strcpy(szDest, szSrc);
	}
	else if(strcmp(szEnv, "zh_CN.GBK") == 0 || strcmp(szEnv, "zh.GBK") == 0 || strcmp(szEnv, "zh_CN.gbk") == 0
		|| strcmp(szEnv, "zh.gbk") == 0 || strcmp(szEnv, "zh_CN.hp15CN") == 0)
	{
		nRet = code_convert("gbk","utf8",szSrc, nSrcLen, szDest,nDesLen);
	}
	else if(strcmp(szEnv, "zh_CN.GB18030") == 0 || strcmp(szEnv, "zh.GB18030") == 0)
	{
		nRet = code_convert("GB18030","utf8",szSrc, nSrcLen, szDest,nDesLen);
	}	
	else if(strcmp(szEnv, "zh_CN.GB2312") == 0 || strcmp(szEnv, "zh.GB2312") == 0)
	{
		nRet = code_convert("GB2312","utf8",szSrc, nSrcLen, szDest,nDesLen);
	}
	else
	{
		if (nDesLen < nSrcLen)
			return EC_RD_LANGUAGE_ILLEGAL_INPUT;
		strcpy(szDest, szSrc);
	}
	return nRet;
}

int32 os_lang_conv_utf8_to_sys(const char *szSrc, char *szDest, int nDesLen)
{
	char* szEnv;
	int   nSrcLen;
	int32 nRet = RD_SUCCESS;

	if ((NULL == szSrc) || (NULL == szDest) || (nDesLen < 1))
		return EC_RD_LANGUAGE_ILLEGAL_INPUT;

	memset(szDest, 0, nDesLen);
	*(szDest + nDesLen - 1) = '\0';
	nDesLen = nDesLen -1;

	nSrcLen = strlen(szSrc);
	if (0 == nSrcLen)
	{
		*(szDest) = '\0';
		return RD_SUCCESS;
	}

	szEnv = getenv("LANG");
	if (NULL == szEnv)
	{//get env var LANG failed
		if (nDesLen < nSrcLen)
			return EC_RD_LANGUAGE_ILLEGAL_INPUT;
		strcpy(szDest, szSrc);
		return RD_SUCCESS;
	}
	if(strcmp(szEnv, "zh_CN.UTF-8") == 0 || strcmp(szEnv, "zh.UTF-8") == 0 || strcmp(szEnv, "zh.utf8") == 0
		|| strcmp(szEnv, "zh.utf-8") == 0 || strcmp(szEnv, "zh_CN.utf-8") == 0 || strcmp(szEnv, "zh_CN.utf8") == 0)
	{
		if (nDesLen < nSrcLen)
			return EC_RD_LANGUAGE_ILLEGAL_INPUT;
		strcpy(szDest, szSrc);
	}
	else if(strcmp(szEnv, "zh_CN.GBK") == 0 || strcmp(szEnv, "zh.GBK") == 0 || strcmp(szEnv, "zh_CN.gbk") == 0
		|| strcmp(szEnv, "zh.gbk") == 0 || strcmp(szEnv, "zh_CN.hp15CN") == 0 )
	{
		nRet = code_convert("utf8","gbk",szSrc, nSrcLen, szDest,nDesLen);
	}
	else if(strcmp(szEnv, "zh_CN.GB18030") == 0 || strcmp(szEnv, "zh.GB18030") == 0 || strcmp(szEnv, "zh_CN.gb18030") == 0) 
	{
		nRet = code_convert("utf8","GB18030",szSrc, nSrcLen, szDest,nDesLen);
	}
	else if(strcmp(szEnv, "zh_CN.GB2312") == 0 || strcmp(szEnv, "zh.GB2312") == 0)
	{
		nRet = code_convert("utf8","GB2312",szSrc, nSrcLen, szDest,nDesLen);
	}
	else 
	{
		if (nDesLen < nSrcLen)
			return EC_RD_LANGUAGE_ILLEGAL_INPUT;
		strcpy(szDest, szSrc);
	}
	return nRet;
}

int32 _hd_safe_strncpy(const char *szSrc, char *szDest, int nDesLen)
{
	int32 nRet = RD_SUCCESS;
	uint32 nSrcLen = strlen(szSrc);
	memset(szDest, 0x00, nDesLen);
	*(szDest + nDesLen - 1) = '\0';
	-- nDesLen;

	if (0 == nSrcLen)
	{
		*(szDest) = '\0';
		return RD_SUCCESS;
	}
	if (nDesLen < nSrcLen)
		return EC_RD_LANGUAGE_ILLEGAL_INPUT;
	strcpy(szDest, szSrc);
	return RD_SUCCESS;
}

int32 os_lang_conv_given_to_unicode(HD_OS_CHARSET charset, const char* szSrc, char* szDest, int nDesLen)
{
	int32 nRet = RD_SUCCESS;
	int32 nSrcLen = 0;
	if ((NULL == szSrc) || (NULL == szDest) || (nDesLen < 1))
		return EC_RD_LANGUAGE_ILLEGAL_INPUT;

	switch (charset)
	{
	case HD_OS_CHARSET_ANSI:
		nSrcLen = strlen(szSrc);
		nRet = code_convert("GB18030", "utf8", szSrc, nSrcLen, szDest, nDesLen);
		break;
	case HD_OS_CHARSET_UNICODE:
		nRet = _hd_safe_strncpy(szSrc, szDest, nDesLen);
		break;
	case HD_OS_CHARSET_SYS:
	default:
		nRet = os_lang_conv_sys_to_utf8(szSrc, szDest, nDesLen);
		break;
	}
	return nRet;
}

int32 os_lang_conv_unicode_to_given(HD_OS_CHARSET charset, const char* szSrc, char* szDest, int nDesLen)
{
	int32 nRet = RD_SUCCESS;
	int32 nSrcLen = 0;
	if ((NULL == szSrc) || (NULL == szDest) || (nDesLen < 1))
		return EC_RD_LANGUAGE_ILLEGAL_INPUT;

	switch (charset)
	{
	case HD_OS_CHARSET_ANSI:
		nSrcLen = strlen(szSrc);
		nRet = code_convert("utf8", "GB18030", szSrc, nSrcLen, szDest, nDesLen);
		break;
	case HD_OS_CHARSET_UNICODE:
		nRet = _hd_safe_strncpy(szSrc, szDest, nDesLen);
		break;
	case HD_OS_CHARSET_SYS:
	default:
		nRet = os_lang_conv_sys_to_utf8(szSrc, szDest, nDesLen);
		break;
	}
	return nRet;
}

int32 _os_lang_conv_ansi_to_unicode(const char *szSrc, char *szDest, int nDesLen)
{
	return os_lang_conv_sys_to_utf8(szSrc, szDest, nDesLen);
}

int32 _os_lang_conv_unicode_to_ansi(const char *szSrc, char *szDest, int nDesLen)
{
	return os_lang_conv_utf8_to_sys(szSrc, szDest, nDesLen);
}
