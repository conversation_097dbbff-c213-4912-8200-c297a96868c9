/** 
 * @file
 * @brief		Linux memory mapping operation
 * <AUTHOR>
 * @date		2009/10/21
 * @version		Initial Version
 *
 *
 */

#include "os.h"
#include "os_mem.h"
#include <unistd.h>
#include <assert.h>
#include <errno.h>
#include "errcode/error_code.h"
#include "errcode/hd_error_code.h"
#include <sys/ipc.h>
#include <sys/shm.h>
#include "os_file.h"


/**
* @brief		Create file mapping
* @param		[in] HANDLE hFile: file handle
* @param		[in] nSize: mapping size
* @param		[in] szMappingName: mapping name
* @return		handle mapping
* @version		2009/06/05 Songxin Huang Initial version
*/
int os_mem_filemap_create(HANDLE hFile, uint64 nSize, const char *szMappingName, FileMapHandle *pFileMap)
{
	assert (hFile != (HANDLE)HD_OS_INVALID_FILE_HANDLE);
	assert (pFileMap != NULL);

	*pFileMap = hFile;
	return RD_SUCCESS;
}

/**
* @brief		Map view of file
* @param		[in] HANDLE hFileMapping: file mapping handle
* @param		[in] nOffsetPos: map offset
* @param		[in] nNumMapBytes: map part size
* @return		map pointer
* @version		2009/06/05 Songxin Huang Initial version
*/
int os_mem_fileview_map(FileMapHandle hFileMapping, uint64 nOffsetPos, 
						size_t nNumMapBytes, void** pMapView)
{
	assert (hFileMapping!= INVALID_FILEMAP_HANDLE);
	assert(pMapView != NULL);
	*pMapView = mmap(NULL, nNumMapBytes, PROT_READ|PROT_WRITE, MAP_SHARED, hFileMapping, (uint32)nOffsetPos);
	if (*pMapView == MAP_FAILED)
		return errno;

	return RD_SUCCESS;
}


/**
* @brief		UnMap view of file
* @param		[in] pMapView: map pointer
* @param		[in] nNumMapBytes: map part size
* @return		the result of unmap
* @version		2009/06/05 Songxin Huang Initial version
*/

int os_mem_fileview_unmap(void* pMapView, size_t nNumMapBytes)
{
	assert(NULL != pMapView);
	if(munmap(pMapView, nNumMapBytes) == -1)
		return EC_RD_OS_FILE_UNMAP;
	else
		return RD_SUCCESS;
}

int32 os_mem_filemap_flushview(void* pBaseAddr, int64 nBytes)
{
	if (msync(pBaseAddr, (size_t)nBytes, MS_ASYNC) == 0)
	{
		return RD_SUCCESS;
	}
	else
	{
		return errno;
	}
}

/**
* @brief		close file mapping
* @param		[in] hFileMapping: file mapping handle
* @return		0 sucess -1 failed
* @version		2009/06/05 Songxin Huang Initial version
*/
int os_mem_filemap_close(FileMapHandle hFileMapping)
{
	return RD_SUCCESS;
}

/**
* @brief		Create shared memory
* @param [in]	nSize size of shared memory
* @param [in]	szShmName shared memory name
* @return		shared memory handle if success; HD_OS_INVALID_FILE_HANDLE, if fail
* @version		2009/10/29 Chunfeng Shen Initial version
*/
int32 os_mem_sharemem_create(uint32 nSize, const char *szShmName, MemMapHandle *pHandle)
{	
	int nRet;
	key_t key;
	MemMapHandle hMapping;
	HANDLE hFile;
	char szCMD[256]={0};
	/* create a file */
	nRet = os_file_open(szShmName, O_CREAT, &hFile);
	if (RD_SUCCESS != nRet)
		return nRet;
	os_file_close(hFile);

	key = ftok(szShmName, 0);
	if (key == (key_t)-1)
		return errno;

	hMapping = shmget(key, nSize,  IPC_CREAT | 0666);
	if (hMapping == -1)
	{
		sprintf(szCMD, "ipcrm -M %d", key);
		system(szCMD);
		hMapping = shmget(key, nSize,  IPC_CREAT | 0666);
		if (hMapping == -1)
		{
			return errno;
		}
	}
	*pHandle = hMapping;
	return RD_SUCCESS;
}

/**
* @brief		Map shared memory
* @param [in]	hMapping handle created by os_mem_sharemem_create
* @return		memory address if success; NULL if fail
* @version		2009/10/29 Chunfeng Shen Initial version
*/
int os_mem_sharemem_map(HANDLE hMapping, void** pShareMem)
{
	assert(pShareMem != NULL);
	*pShareMem = shmat(hMapping, NULL, 0);
	if ((void*)-1 == *pShareMem)
		return errno;

	return RD_SUCCESS;
}

/**
* @brief		Detach shared memory
* @param [in]	pShm shared memory address
* @return		RD_SUCCESS if success; error code if fail
* @version		2009/10/29 Chunfeng Shen Initial version
*/
int os_mem_sharemem_unmap(void* pShm)
{
	int nRet;
	nRet = shmdt(pShm);
	if(nRet == 0)
		return RD_SUCCESS;
	else
		return EC_RD_OS_MEM_SHAREMEM_UNMAP;
}

/**
* @brief		Close shared memory
* @param [in]	hMapping handle created by os_mem_sharemem_create
* @return		RD_SUCCESS if success; error code if fail
* @version		2009/10/29 Chunfeng Shen Initial version
*/
int os_mem_sharemem_close(MemMapHandle hMapping)
{
	if(shmctl(hMapping, IPC_RMID, NULL) == 0)
		return RD_SUCCESS;
	else
		return EC_RD_OS_MEM_SHAREMEM_CLOSE;
}

