/** 
 * @file
 * @brief		Linux Mutex rwlock semo operation
 * <AUTHOR>
 * @date		2009/06/12
 * @version		Initial Version
 *
 *
 */
#include "os_lock.h"
#include <assert.h>
#include <stdlib.h>
#include "errcode/error_code.h"
#include "errcode/hd_error_code.h"
#include <errno.h>
#include "semaphore.h"
#include <sys/sem.h>
#include <sys/shm.h>
#include "os_file.h"

#ifdef __hpux
#define pthread_spinlock_t       pthread_mutex_t
#define pthread_spin_init(KC_a, KC_b)           \
	pthread_mutex_init(KC_a, NULL)
#define pthread_spin_destroy(KC_a)              \
	pthread_mutex_destroy(KC_a)
#define pthread_spin_lock(KC_a)                 \
	pthread_mutex_lock(KC_a)
#define pthread_spin_trylock(KC_a)              \
	pthread_mutex_trylock(KC_a)
#define pthread_spin_unlock(KC_a)               \
	pthread_mutex_unlock(KC_a)
#endif

union semun {
	int				val;		/* Used by SETVAL only*/
	struct semid_ds	*buf;		/* Used by IPC_SET and IPC_STAT */
	unsigned short	*array;		/* Used by GETALL and SETALL */
};

int32 os_spinlock_init(SPINLOCK *pSpinLock)
{
	return pthread_spin_init(pSpinLock, PTHREAD_PROCESS_PRIVATE);
}

int32 os_spinlock_destroy(SPINLOCK *pSpinLock)
{
	return pthread_spin_destroy(pSpinLock);
}

int32 os_spinlock_acquire(SPINLOCK *pSpinLock)
{
	return pthread_spin_lock(pSpinLock);
}

int32 os_spinlock_release(SPINLOCK *pSpinLock)
{
	return pthread_spin_unlock(pSpinLock);
}

/**
* @brief		Initialize a mutex
* @return		Pointer of mutex handle
* @version		2009/10/15 Chunfeng Shen Initial version
* @return		pointer of mutex info if success; NULL, if fail
* @note			os_mutex_release must be called after calling this function 
				to release memory
*/ 
int os_mutex_init(MutexHandle *pMutexHandle)
{
	int nRet;
	pthread_mutex_t *pMutex = (pthread_mutex_t *)malloc(sizeof(pthread_mutex_t));
	if (NULL == pMutex)
	{
		return EC_RD_OS_MEM_MALLOC;
	}
	nRet = pthread_mutex_init(pMutex, NULL);
	if (0 != nRet)
	{
		free(pMutex);
		return nRet;
	}

	*pMutexHandle = pMutex;
	return RD_SUCCESS;	
}

/**
* @brief		Destroy a mutex
* @return		Pointer of mutex handle
* @param		pMutexInfo pointer of mutex info, it cannot be NULL
* @version		2009/10/15 Chunfeng Shen Initial version
*/ 
void os_mutex_destroy(MutexHandle hMutex)
{
	pthread_mutex_t *pMutex = hMutex;
	assert(pMutex != NULL);
	pthread_mutex_destroy(pMutex);
	free(pMutex);
}

// void inline os_spinlock_acquire(SpinlockHandle *pSpinLock)
// {
// 	asm volatile (
// 		"movb $0, %%ah;"
// 		"1:	movb $1, %%al;"	
// 		"lock cmpxchgb %%ah, (%%edx);"
// 		"jz 2f;"
// 		"rep; nop;"
// 		"jmp 1b;"
// 		"2:"
// 		:"+d"(pSpinLock)::"memory"
// 		);
// }
// 
// void inline os_spinlock_release(SpinlockHandle *pSpinLock)
// {
// 	asm (
// 		"lock incb %0;"
// 		:"+m"(*pSpinLock)
// 		);
// }

/**
* @brief		Initialize a read-write mutex
* @return		pointer of mutex info if success; NULL, if fail
* @note			thread_rw_mutex_destroy must be called after calling this function 
to release memory
* @version		2009/10/19 Chunfeng Shen Initial version
*/
int os_rwmutex_init(RwMutexHandle *pRwMutexHandle)
{

#ifdef ARM_LINUX
	int nRet;
	pthread_mutex_t *pMutex = malloc(sizeof(pthread_mutex_t));
	if (NULL == pMutex)
	{
		return EC_RD_OS_MEM_MALLOC;
	}

	nRet = pthread_mutex_init(pMutex, NULL);
	if (0 != nRet)
	{
		free(pMutex);
		return nRet;
	}

	*pRwMutexHandle = pMutex;
	return RD_SUCCESS;

#else
	int nRet;
	pthread_rwlock_t *pRwMutex = (pthread_rwlock_t *)malloc(sizeof(pthread_rwlock_t));
	if (NULL == pRwMutex)
	{
		return EC_RD_OS_MEM_MALLOC;
	}
	
	nRet = pthread_rwlock_init(pRwMutex, NULL);
	if (nRet != 0)
	{
		free(pRwMutex);
		return nRet;
	}

	*pRwMutexHandle = pRwMutex;
	return RD_SUCCESS;
#endif
}

/**
* @brief		Destroy a read-write mutex
* @param [in]	pointer of mutex info if success
* @version		2009/10/19 Chunfeng Shen Initial version
*/
void os_rwmutex_destroy(RwMutexHandle hRwMutex)
{
#ifdef ARM_LINUX
	pthread_mutex_t *pMutex = hRwMutex;
	assert(pMutex != NULL);
	pthread_mutex_destroy(pMutex);
	free(pMutex);
#else
	assert(hRwMutex != NULL);
	pthread_rwlock_destroy(hRwMutex);
	free(hRwMutex);
#endif
}

/**
* @brief		Acquire a read mutex
* @param [in, out]	pointer of mutex info
* @return		RD_SUCCESS, if success; error code if fail
* @version		2009/10/19 Chunfeng Shen Initial version
*/
int os_rwmutex_acquire_read(RwMutexHandle hRwMutex)
{
#ifdef ARM_LINUX
	int nRet;
	assert(hRwMutex != NULL);
	nRet = pthread_mutex_lock(hRwMutex);
	if (nRet != 0)
		return EC_RD_LOCK_ACQUIRE_READ;
	return RD_SUCCESS;
#else
	int nRet;
	assert(hRwMutex != NULL);
	nRet = pthread_rwlock_rdlock(hRwMutex);
	if (nRet != 0)
		return EC_RD_LOCK_ACQUIRE_READ;
	return RD_SUCCESS;
#endif
}

/**
* @brief		Acquire a write mutex
* @param [in, out]	pointer of mutex info
* @return		RD_SUCCESS, if success; error code if fail
* @version		2009/10/19 Chunfeng Shen Initial version
*/
int os_rwmutex_acquire_write(RwMutexHandle hRwMutex)
{
#ifdef ARM_LINUX
	int nRet;
	assert(hRwMutex != NULL);
	nRet = pthread_mutex_lock(hRwMutex);
	if (nRet != 0)
	{
		return EC_RD_LOCK_ACQUIRE_WRITE;
	}
	return RD_SUCCESS;
#else
	int nRet;
	assert(hRwMutex != NULL);
	nRet = pthread_rwlock_wrlock(hRwMutex);
	if (nRet != 0)
	{
		return EC_RD_LOCK_ACQUIRE_WRITE;
	}
	return RD_SUCCESS;
#endif
}

/**
* @brief		Release a read-write mutex
* @param [in,out]	pointer of mutex info
* @version		2009/10/19 Chunfeng Shen Initial version
*/
int os_rwmutex_release(RwMutexHandle hRwMutex)
{
#ifdef ARM_LINUX
	int nRet;
	assert(hRwMutex != NULL);
	nRet = pthread_mutex_unlock(hRwMutex);
	return nRet;
#else
	int nRet;
	assert(hRwMutex != NULL);	
	nRet = pthread_rwlock_unlock(hRwMutex);
	return nRet;
#endif
}

int os_proc_sema_init(const char* szSemaName, int nInitCount, SemaHandle *pSemaHandle)
{
	int nRet;
	key_t key;
	HANDLE hFile;
	union semun semctl_arg;
	struct sembuf op_proc_lock = {1, -1, SEM_UNDO};
	struct sembuf op_proc_unlock = {1, 1, SEM_UNDO};
	assert (pSemaHandle != NULL);

	/* create a file */
	nRet = os_file_open(szSemaName, O_CREAT, &hFile);
	if (RD_SUCCESS != nRet)
	{
		return nRet;
	}
	os_file_close(hFile);

	key = ftok(szSemaName, 0);
	if ((key_t)-1 == key)
	{
		return (int)errno;
	}

	*pSemaHandle = semget(key, 2, IPC_CREAT| IPC_EXCL | 0777);
	if (-1 == *pSemaHandle)
	{
		int nProcessNum;
		if (errno != EEXIST)	/* exist already */
		{
			return (int)errno;
		}

		*pSemaHandle = semget(key, 2, IPC_CREAT | 0777);
		if (-1 == *pSemaHandle)
		{
			if (EINVAL == errno)
			{
				char szCMD[256]={0};
				sprintf(szCMD, "ipcrm -S %d", key);
				printf("os_proc_sema_init semget failed szSemaName %s  exe cmd %s\n", szSemaName, szCMD);
				system(szCMD);
			}
			
			return (int)errno;
		}

		nProcessNum = semctl(*pSemaHandle, 1, GETVAL);
		if (0 == nProcessNum)
		{
			semctl_arg.val = nInitCount;
			nRet = semctl(*pSemaHandle, 0, SETVAL, semctl_arg);
			if (-1 == nRet)
			{
				return (int)errno;
			}
		}
	}
	else /* new semaphore */
	{
		/* set process number */
		semctl_arg.val = 0;
		nRet = semctl(*pSemaHandle, 1, SETVAL, semctl_arg);
		if (-1 == nRet)
		{
			nRet = (int)errno;
			goto error_return;
		}

		/* set sem number */
		semctl_arg.val = nInitCount;
		nRet = semctl(*pSemaHandle, 0, SETVAL, semctl_arg);
		if (-1 == nRet)
		{
			nRet = (int)errno;
			goto error_return;
		}
	}
	/* increase process number */
	nRet = semop(*pSemaHandle, &op_proc_unlock, 1);
	if (-1 == nRet)
	{
		return (int)errno;
	}

	nRet = semctl(*pSemaHandle, 0, GETVAL);
	nRet = semctl(*pSemaHandle, 1, GETVAL);
	return RD_SUCCESS;

error_return:
	semctl(*pSemaHandle, 0, IPC_RMID);
	semctl(*pSemaHandle, 1, IPC_RMID);
	return nRet;
}

int os_proc_sema_destroy(SemaHandle hSema)
{
	int nRet;
	int nSemNum;
	struct sembuf unlock = {1, -1, SEM_UNDO | IPC_NOWAIT};
	nRet = semop(hSema, &unlock, 1);
	if (-1 == nRet)
	{
		return (int)errno;
	}	

	nSemNum = semctl(hSema, 1, GETVAL);
	if (0 == nSemNum)
	{
		semctl(hSema, 0, IPC_RMID);
		semctl(hSema, 1, IPC_RMID);
	}
	return RD_SUCCESS;
}

int os_proc_sema_acquire(SemaHandle hSema)
{
	int nRet;
	struct sembuf lock = {0, -1, SEM_UNDO};

	nRet = semop(hSema, &lock, 1);
	if (-1 == nRet)
	{
		return (int)errno;
	}

	return RD_SUCCESS;
}

int os_proc_sema_release(SemaHandle hSema)
{
	int nRet;
	struct sembuf unlock = {0, 1, SEM_UNDO};

	nRet = semop(hSema, &unlock, 1);
	if (-1 == nRet)
	{
		return (int)errno;
	}

	return RD_SUCCESS;
}

int os_proc_sema_tryacquire(SemaHandle hSema)
{
	int nRet;
	struct sembuf lock = {0, -1, SEM_UNDO | IPC_NOWAIT};

	nRet = semop(hSema, &lock, 1);
	if (-1 == nRet)
	{
		if ((int)errno == EAGAIN)
			return EC_RD_SEMA_TRYACQUIRE;

		return (int)errno;
	}

	return RD_SUCCESS;
}


///**
//* @brief		release rwlock
//* @param		[in] db_rwlock_t *rw :pthread_rwlock_t              
//* @return		result
//* @version		2009/06/12 Songxin Huang Initial version
//*/ 
//
//int thread_rwlock_release(db_rwlock_t *rw)
//{   
//	//int nRet;
// //   nRet = pthread_rwlock_unlock(rw);
//
//	return nRet;
//	
//}
//
///**
//* @brief		destroy rwlock struct
//* @param		[in] db_rwlock_t *rw :pthread_rwlock_t              
//* @return		result
//* @version		2009/06/12 Songxin Huang Initial version
//*/
//int thread_rwlock_destroy(db_rwlock_t *rw)
//{
//	//return pthread_rwlock_destroy(rw);
//	return 0;
//}
//
//
//
//
//
////for thread mutex.
//
///**
//* @brief		init mutex
//* @param		[in] db_mutex_t *m :  mutex         
//* @return		result
//* @version		2009/06/12 Songxin Huang Initial version
//*/
//
//int thread_mutex_init(DB_mutex_t *m)
//{
//	pthread_mutex_init(m, NULL);
//	return 0;	
//}
//
///**
//* @brief		 mutex lock
//* @param		[in] db_mutex_t *m :  mutex         
//* @return		result
//* @version		2009/06/12 Songxin Huang Initial version
//*/
//
//int thread_mutex_acquire(DB_mutex_t *m)
//{
//   return pthread_mutex_lock(m);
//}
//
///**
//* @brief		mutex unlock
//* @param		[in] db_mutex_t *m :  mutex         
//* @return		result
//* @version		2009/06/12 Songxin Huang Initial version
//*/
//int thread_mutex_release(DB_mutex_t *m)
//{
//   return pthread_mutex_unlock(m);
//}
//
///**
//* @brief		destroy mutex
//* @param		[in] db_mutex_t *m :  mutex         
//* @return		result
//* @version		2009/06/12 Songxin Huang Initial version
//*/
//
//int thread_mutex_destroy(DB_mutex_t *m)
//{
//   return pthread_mutex_destroy(m);
//}
//
//
////for thread semaphere.
//
///**
//* @brief		init semaphore
//* @param		[in] db_sema_t *s :  semaphore   
//* @param		[in] count :  the initial value   
//* @param		[in] name :  NULL //used in WIN32 
//* @param		[in] max :  0 // used in WIN32 
//* @return		result
//* @version		2009/06/12 Songxin Huang Initial version
//*/
//
//int thread_semo_init(db_sema_t *s, u_int count, const char *name, int max)
//{
//	int nRet = 0;
//	nRet = sem_init(s, 0, count);
//	if(nRet != 0)
//		return -1;
//	else
//		return 0;
//	
//}
///**
//* @brief		acquire semaphore
//* @param		[in] db_sema_t *s :  semaphore         
//* @return		result
//* @version		2009/06/12 Songxin Huang Initial version
//*/
//
//int thread_semo_acquire(db_sema_t *s)
//{
//	assert(s != 0);
//	sem_wait(s);
//
//
///**
//* @brief		release semaphore
//* @param		[in] db_sema_t *s :  semaphore         
//* @return		result
//* @version		2009/06/12 Songxin Huang Initial version
//*/
//
//int thread_semo_release(db_sema_t *s)
//{
//	
//	assert(s != 0);
//	sem_post(s);
//	
//}
//
///**
//* @brief		acquire semaphore with timeout
//* @param		[in] db_sema_t *s :  semaphore     
//* @param		[in] timeval TimeOut :  the time
//* @return		result
//* @version		2009/06/12 Songxin Huang Initial version
//*/
//
//int thread_semo_acquire_timeout(db_sema_t *s, struct timeval TimeOut)
//{
//	long usec_timeout = 0;
//	assert(s != 0);
//	usec_timeout = TimeOut.tv_sec * 1000 * 1000 + TimeOut.tv_usec;
//	if(usec_timeout < 0)
//	{
//		sem_wait(s);
//		return 0;
//	}
//	else
//	{
//		struct timespec ts;
//		clock_gettime(CLOCK_REALTIME,&ts);
//		ts.tv_sec += TimeOut.tv_sec;
//		ts.tv_nsec += TimeOut.tv_usec * 1000;
//		sem_timedwait(s, &ts);
//		return 0;
//
//	}
//
//	
//}




//THREAD_RW_MUTEX_INFO* thread_rw_mutex_init()
//{
//	THREAD_RW_MUTEX_INFO* rw = (THREAD_MUTEX_INFO*)12;
//	//pthread_rwlock_init(rw, NULL);
//	return rw;
//}
//
//void thread_rw_mutex_destroy(THREAD_RW_MUTEX_INFO* pThreadRwMutexInfo)
//{
//	//pthread_rwlock_destroy(pThreadRwMutexInfo);
//}
//
//int thread_rw_mutex_acquire_read(THREAD_RW_MUTEX_INFO* pThreadRwMutexInfo)
//{
//	int nRet = RD_SUCCESS;
//    //nRet = pthread_rwlock_rdlock(pThreadRwMutexInfo);
//	return nRet;
//}
//
//int thread_rw_mutex_acquire_write(THREAD_RW_MUTEX_INFO* pThreadRwMutexInfo)
//{
//	int nRet = RD_SUCCESS;
//	//nRet = pthread_rwlock_wrlock(pThreadRwMutexInfo);
//	return nRet;
//}
//
//void thread_rw_mutex_release(THREAD_RW_MUTEX_INFO* pThreadRwMutexInfo)
//{  
//   // pthread_rwlock_unlock(pThreadRwMutexInfo);
//}
//int os_lock_spin_init(THREAD_MUTEX_INFO * lpspin_lock)
//{
//	spin_lock_init(lpspin_lock);
//	return RD_SUCCESS;
//
//}
//int os_lock_spin_destroy(THREAD_MUTEX_INFO* lock)
//{
//	return RD_SUCCESS;
//
//}
//HANDLE os_semaphore_init(int nInitCount, int nMaxCount, int key)
//{
//	return semget(key, 1, IPC_CREAT|0666);
//}
//int os_semaphore_destroy(HANDLE hSemaphore)
//{
//	semctl(hSemaphore, 0, IPC_RMID);
//	return RD_SUCCESS;
//}



int32 os_proc_mutex_init(const char* szMutexName, ProcMutexHandle* phMutex)
{
	key_t key;
	HANDLE hFile;
	int32 nRet = RD_SUCCESS;

	*phMutex = (ProcMutexHandle)malloc(sizeof(ProcMutexInfo));
	if (NULL == *phMutex)
	{
		return EC_RD_OS_MEM_MALLOC;
	}

	/* create a file */
	nRet = os_file_open(szMutexName, O_CREAT, &hFile);
	if (RD_SUCCESS != nRet)
		return nRet;
	os_file_close(hFile);

	key = ftok(szMutexName, 0);
	if (key == (key_t)-1)
	{
		return EC_RD_PROC_MUTEX_INIT;
	}
	(*phMutex)->shmid = shmget(key, sizeof(ProcMutex), IPC_CREAT | IPC_EXCL | 0666);
    int shm_exist = 0;
	if((*phMutex)->shmid < 0)
	{
        if (EEXIST != errno) {
            return EC_RD_PROC_MUTEX_INIT;
        } else {
            shm_exist = 1;
            usleep(1000);
            (*phMutex)->shmid = shmget(key, sizeof(ProcMutex), IPC_CREAT | 0666);
        }
	}
	(*phMutex)->pMutex = (ProcMutex *)shmat((*phMutex)->shmid, NULL, 0);
	if((*phMutex)->pMutex == (ProcMutex *)-1)
	{
		return EC_RD_PROC_MUTEX_INIT;
	}
    if (0 == shm_exist) {
        pthread_mutexattr_init(&((*phMutex)->attr));
        pthread_mutexattr_setpshared(&((*phMutex)->attr), PTHREAD_PROCESS_SHARED);
        pthread_mutex_init((*phMutex)->pMutex, &(*phMutex)->attr);
    }
	return RD_SUCCESS;
}

int32 os_proc_mutex_acquire(ProcMutexHandle hMutex)
{
	if (hMutex == NULL) 
		return EC_RD_PROC_MUTEX_NULL;
	pthread_mutex_lock(hMutex->pMutex);
	return RD_SUCCESS;
}

int32 os_proc_mutex_release(ProcMutexHandle hMutex)
{
	if (hMutex == NULL) 
		return EC_RD_PROC_MUTEX_NULL;
	pthread_mutex_unlock(hMutex->pMutex);
	return RD_SUCCESS;
}

int32 os_proc_mutex_destroy(ProcMutexHandle hMutex)
{
    struct shmid_ds shmds;
	if (hMutex == NULL) 
		return EC_RD_PROC_MUTEX_NULL;
    if (shmctl(hMutex->shmid, IPC_STAT, &shmds) == -1) {
        free(hMutex);
        return EC_RD_PROC_MUTEX_DESTROY;
    }
	if(shmctl(hMutex->shmid, IPC_RMID, NULL) == -1) {
		free(hMutex);
		return EC_RD_PROC_MUTEX_DESTROY;
	}
    // destroy the mutex only when the last process detaches it.
    if (1 == shmds.shm_nattch) {
        pthread_mutexattr_destroy(&(hMutex->attr));
        pthread_mutex_destroy((hMutex->pMutex));
    }
    if (shmdt(hMutex->pMutex) == -1) {
        free(hMutex);
        return EC_RD_PROC_MUTEX_DESTROY;
    }
    free(hMutex);
    return RD_SUCCESS;
}
