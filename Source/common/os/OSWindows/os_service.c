#include "os_service.h"
#include "error_code.h"
#include <Windows.h>
#include <process.h>
#include <stdio.h>

WCHAR g_szSvcName[256];
SERVICE_STATUS          gSvcStatus; 
SERVICE_STATUS_HANDLE   gSvcStatusHandle; 
HANDLE                  ghSvcStopEvent = NULL;
HANDLE					g_hWaitThread = (HANDLE)-1;
int*					g_pServiceExit = NULL;

int32 service_delete(const char *szSvcName)
{
	int32 nRet;
	SC_HANDLE schSCManager;
	SC_HANDLE schService;

	nRet = MultiByteToWideChar(CP_ACP, 0, szSvcName, (int)strlen(szSvcName)+1, g_szSvcName,   
		sizeof(g_szSvcName)/sizeof(g_szSvcName[0]));
	if (nRet == 0)
	{
		return GetLastError();
	}

	// Get a handle to the SCM database. 
	schSCManager = OpenSCManager( 
		NULL,                    // local computer
		NULL,                    // ServicesActive database 
		SC_MANAGER_ALL_ACCESS);  // full access rights 
	if (NULL == schSCManager) 
	{
		return GetLastError();
	}

	// Get a handle to the service.
	schService = OpenService( 
		schSCManager,       // SCM database 
		g_szSvcName,          // name of service 
		DELETE);            // need delete access 
	if (schService == NULL)
	{ 
		nRet = GetLastError();
		CloseServiceHandle(schSCManager);
		return nRet;
	}

	// Delete the service.
	if (! DeleteService(schService) ) 
	{
		return GetLastError();
	}

	CloseServiceHandle(schService); 
	CloseServiceHandle(schSCManager);

	printf("%s service is removed successfully.\n", szSvcName);
	return RD_SUCCESS;
}

int32 service_install(const char *szSvcName)
{
	int32 nRet;
	SC_HANDLE schSCManager;
	SC_HANDLE schService;
	TCHAR szPath[MAX_PATH];

	nRet = MultiByteToWideChar(CP_ACP, 0, szSvcName, (int)strlen(szSvcName)+1, 
		g_szSvcName, sizeof(g_szSvcName)/sizeof(g_szSvcName[0]));
	if (nRet == 0)
	{
		return GetLastError();
	}

	if( !GetModuleFileName( NULL, szPath, MAX_PATH ) )
	{
		return GetLastError();
	}

	// Get a handle to the SCM database. 
	schSCManager = OpenSCManager( 
		NULL,                    // local computer
		NULL,                    // ServicesActive database 
		SC_MANAGER_ALL_ACCESS);  // full access rights 
	if (NULL == schSCManager) 
	{
		return GetLastError();
	}

	// Create the service.
	schService = CreateService( 
		schSCManager,              // SCM database 
		(LPCTSTR)g_szSvcName,       // name of service 
		(LPCTSTR)g_szSvcName,       // service name to display 
		SERVICE_ALL_ACCESS,        // desired access 
		SERVICE_WIN32_OWN_PROCESS, // service type 
		SERVICE_DEMAND_START,      // start type 
		SERVICE_ERROR_NORMAL,      // error control type 
		szPath,                    // path to service's binary 
		NULL,                      // no load ordering group 
		NULL,                      // no tag identifier 
		NULL,                      // no dependencies 
		NULL,                      // LocalSystem account 
		NULL);                     // no password 
	if (schService == NULL) 
	{
		nRet = GetLastError();
		CloseServiceHandle(schSCManager);
		return nRet;
	}

	CloseServiceHandle(schService); 
	CloseServiceHandle(schSCManager);
	printf("%s service is installed successfully.\n", szSvcName);
	return RD_SUCCESS;
}

static VOID ReportSvcStatus( DWORD dwCurrentState,
					 DWORD dwWin32ExitCode,
					 DWORD dwWaitHint)
{
	static DWORD dwCheckPoint = 1;

	// Fill in the SERVICE_STATUS structure.
	gSvcStatus.dwCurrentState = dwCurrentState;
	gSvcStatus.dwWin32ExitCode = dwWin32ExitCode;
	gSvcStatus.dwWaitHint = dwWaitHint;

	if (dwCurrentState == SERVICE_START_PENDING)
	{
		gSvcStatus.dwControlsAccepted = 0;
	}
	else
	{
		gSvcStatus.dwControlsAccepted = SERVICE_ACCEPT_STOP;
	}

	if ( (dwCurrentState == SERVICE_RUNNING) ||
		(dwCurrentState == SERVICE_STOPPED) )
	{
		gSvcStatus.dwCheckPoint = 0;
	}
	else 
	{
		gSvcStatus.dwCheckPoint = dwCheckPoint++;
	}

	// Report the status of the service to the SCM.
	SetServiceStatus( gSvcStatusHandle, &gSvcStatus );
}


static VOID WINAPI SvcCtrlHandler( DWORD dwCtrl )
{
	// Handle the requested control code. 
	switch(dwCtrl) 
	{  
	case SERVICE_CONTROL_STOP: 

		// Signal the service to stop.
		*g_pServiceExit = true;
		ReportSvcStatus( SERVICE_STOPPED, NO_ERROR, 0 );
		return;

	case SERVICE_CONTROL_INTERROGATE: 
		// Fall through to send current status.
		break; 

	default: 
		break;
	} 

	ReportSvcStatus(gSvcStatus.dwCurrentState, NO_ERROR, 0);
}

static VOID WINAPI SvcMain( DWORD dwArgc, LPTSTR *lpszArgv )
{
	// Register the handler function for the service
	gSvcStatusHandle = RegisterServiceCtrlHandler( 
		g_szSvcName, 
		SvcCtrlHandler);
	if( !gSvcStatusHandle )
	{ 
		return; 
	} 

	// These SERVICE_STATUS members remain as set here
	gSvcStatus.dwServiceType = SERVICE_WIN32_OWN_PROCESS; 
	gSvcStatus.dwServiceSpecificExitCode = 0;    

	// Report initial status to the SCM
//	ReportSvcStatus( SERVICE_START_PENDING, NO_ERROR, 3000 );

	ReportSvcStatus( SERVICE_RUNNING, NO_ERROR, 0 );
	// Perform service-specific initialization and work.
//	SvcInit( dwArgc, lpszArgv );
}

static unsigned __stdcall wait_thread(void* pArg)
{
	// TO_DO: Add any additional services for the process to this table.
	SERVICE_TABLE_ENTRY DispatchTable[] = 
	{ 
		{ g_szSvcName, (LPSERVICE_MAIN_FUNCTION) SvcMain }, 
		{ NULL, NULL } 
	}; 

	// This call returns when the service has stopped. 
	// The process should simply terminate when the call returns.
	if (!StartServiceCtrlDispatcher( DispatchTable )) 
	{ 
		return 0;
	}

	return 0;
}

int32 service_begin(const char* szSvcName, int* pServiceExit)
{
	int32 nRet;

	nRet = MultiByteToWideChar(CP_ACP, 0, szSvcName, (int)strlen(szSvcName)+1, 
		g_szSvcName, sizeof(g_szSvcName)/sizeof(g_szSvcName[0]));
	if (nRet == 0)
	{
		return GetLastError();
	}

	g_hWaitThread = (HANDLE)_beginthreadex(NULL, 0, &wait_thread, 0, 0, NULL);
	if (g_hWaitThread == (HANDLE)-1)
	{
		return errno;
	}
	g_pServiceExit = pServiceExit;
	return RD_SUCCESS;
}

void service_end()
{
	WaitForSingleObject(g_hWaitThread, INFINITE);
}
