/** 
* @file
* @brief		directory operation function
* <AUTHOR>
* @date			2009/10/13
* @version		Initial Version
* Create directory, pass through files under the directory.
*/

#include "os_dir.h"
#include <stdio.h>
#include "error_code.h"
#include <assert.h>

/**
* @brief		Find first file name under the directory, 
				using os_dir_find_next_file can get all files under a directory
* @param [in]	szPathName path name
* @param [out]	szFileName file name returned, this is only valid when function returns RD_SUCCESS
* @param [out]	pDir structure pointer which is useful when get next file
* @return		RD_SUCCESS, if success; Error code if fail.
* @see			os_dir_find_next_file
* @version		2009/10/13 Chunfeng Shen Initial version
*/ 
OS_FUNEXPORT
int32 os_dir_find_first_file(const char *szPathName, char *szFileName, OSDIR *pDir)
{
	WIN32_FIND_DATAA FindData;
	char* szNewPathName;
	int32 nLenPathName;
	int32 nRet;

	if (NULL == szPathName || NULL == pDir || NULL == szFileName)
		return EC_RD_INVALID_INPUT_ARGUMENT;

	nLenPathName = (int32)strlen(szPathName);
	if (nLenPathName < 1)
	{
		return EC_RD_OS_DIR_INVALID_PATH_NAME;
	}
	szNewPathName = (char*)malloc(nLenPathName + 3);
	if (NULL == szNewPathName)
		return EC_RD_OS_MEM_MALLOC;
	strcpy(szNewPathName, szPathName);

	if (szPathName[nLenPathName - 1] != '*')
	{
		if (szPathName[nLenPathName-1] != '/' 
			&& szPathName[nLenPathName-1] != '\\')
			strcat(szNewPathName, "/*");
		else
			strcat(szNewPathName, "*");
	}

	pDir->hFindFile	= FindFirstFileA(szNewPathName, &FindData); 
	free(szNewPathName);

	if (INVALID_HANDLE_VALUE == pDir->hFindFile)
		return EC_RD_OS_DIR_FIND_FILE;

	if (strcmp(FindData.cFileName, ".") == 0)
	{
		if (FindNextFileA(pDir->hFindFile, &FindData) != TRUE)
		{
			if (FindClose(pDir->hFindFile) == FALSE)
			{
				nRet = (int32)GetLastError();
				return nRet;
			}
			return EC_RD_OS_DIR_FIND_FILE;
		}
	}
	if (strcmp(FindData.cFileName, "..") == 0)
	{
		if (FindNextFileA(pDir->hFindFile, &FindData) != TRUE)
		{
			if (FindClose(pDir->hFindFile) == FALSE)
			{
				nRet = (int32)GetLastError();
				return nRet;
			}
			return EC_RD_OS_DIR_FIND_NO_FILE;
		}
	}

	strcpy(szFileName, FindData.cFileName);

	return RD_SUCCESS;
}

/**
* @brief		Find next file name under the directory
* @param [out]	szFileName file name returned, this is only valid when function returns RD_SUCCESS
* @param [in,out]	pDir structure pointer which is useful when get next file
* @return		RD_SUCCESS, if success; EC_RD_OS_DIR_FIND_FILE if no file found.
* @see			os_dir_find_first_file
* @note			this function must be called after calling os_dir_find_first_file
* @note			memory of szFileName must be allocated in advance
* @version		2009/10/13 Chunfeng Shen Initial version
*/ 
int32 os_dir_find_next_file(char *szFileName, const OSDIR *pDir)
{
	WIN32_FIND_DATAA fdData;
	if (NULL == szFileName || NULL == pDir)
		return EC_RD_INVALID_INPUT_ARGUMENT;

	if (FindNextFileA(pDir->hFindFile, &fdData) != TRUE)
	{
		int32 nRet = (int32)GetLastError();
		if (FindClose(pDir->hFindFile) == FALSE)
			return (int32)GetLastError();

		if (nRet == ERROR_NO_MORE_FILES)
			return EC_RD_OS_DIR_FIND_NO_FILE;
		else
			return EC_RD_OS_DIR_FIND_FILE;
	}

	strcpy(szFileName, fdData.cFileName);

	return RD_SUCCESS;
}

/**
* @brief		create directory
* @param[in]	szPathName the directory name
* @return		RD_SUCCESS if success; EC_RD_OS_DIR_CREATE, otherwise
* @version		2009/10/13 Chunfeng Shen initial version
*/ 
int32 os_dir_create(const char *szPathName)
{
	int32 nRet;
	nRet = CreateDirectoryA(szPathName, NULL);

	if (FALSE == nRet && GetLastError() != ERROR_ALREADY_EXISTS)
	{
		return EC_RD_OS_DIR_CREATE;
	}

	return RD_SUCCESS;
}

/**
* @brief		delete all files under directory
* @param [in]	szPathName the directory name
* @return		RD_SUCCESS, if success; Error code if fail
* @version		2009/10/13 Chunfeng Shen initial version
*/ 
int32 os_dir_delete_files(const char *szPathName)
{
	int32 nRet;
	int32 nLenPathName;
	char* szFileName;
	OSDIR dirFind;
	if (szPathName == NULL)
		return EC_RD_INVALID_INPUT_ARGUMENT;
	
	nLenPathName = (int32)strlen(szPathName);
	szFileName = (char*)malloc(nLenPathName + DIR_MAX_PATH_NAME_LEN);
	if (NULL == szFileName)
		return EC_RD_OS_MEM_MALLOC;
	strcpy(szFileName, szPathName);
	if (szPathName[nLenPathName - 1] != '/' && 
		szPathName[nLenPathName - 1] != '\\')
	{
		strcat(szFileName, "/");
		nLenPathName++;
	}

	nRet = os_dir_find_first_file(szPathName, szFileName + nLenPathName, &dirFind);
	if (nRet != RD_SUCCESS)
	{
		free(szFileName);
		return RD_SUCCESS;
	}

	do 
	{
		if (remove(szFileName) != 0)
		{
			free(szFileName);
			return EC_RD_OS_FILE_DELETE;
		}
		nRet = os_dir_find_next_file(szFileName + nLenPathName, &dirFind);
	} while (nRet == RD_SUCCESS);
	free(szFileName);
	return RD_SUCCESS;
}

/**
* @brief		remove a directory
* @param [in]	szPathName the directory name
* @return		RD_SUCCESS, if success; EC_RD_OS_DIR_REMOVE if fail.
* @version		2009/10/13 Chunfeng Shen initial version
*/ 
int32 os_dir_remove(const char * szPathName)
{
	int32 nRet;
	assert(szPathName != NULL);

	nRet = os_dir_delete_files(szPathName);
	if (nRet != RD_SUCCESS)
		return nRet;
		
	if (RemoveDirectoryA(szPathName) == FALSE)
		return EC_RD_OS_DIR_REMOVE;

	return RD_SUCCESS;
}

/**
* @brief		get current process path
* @param [in]	szPathName  the pathname buffer pointer
* @param [in]	nSize		the pathname buffer size
* @return		the real path name size
* @version		2009/10/29 Huang Songxin initial version
* @note			the pathname size usually is the max path size
*/

int32 os_dir_get_curr_process_path(char* szPathName, int32 nSize)
{
	int32 nRet;
	assert(nSize > 0);
	assert(NULL != szPathName);

	nRet = (int32)GetModuleFileNameA(NULL, szPathName, nSize);
	return nRet;
}

