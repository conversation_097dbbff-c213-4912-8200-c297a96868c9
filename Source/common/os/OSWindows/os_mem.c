/** 
 * @file
 * @brief		memory mapping operation
 * <AUTHOR>
 * @date		2009/10/21
 * @version		Initial Version*/
#include "os_mem.h"
#include <windows.h>
#include <io.h>
#include <string.h>
#include <tchar.h>
#include <stdio.h>
#include "error_code.h"
#include "os_time.h"
#include "os_dir.h"
#include <assert.h>
#include "os_sa.h"

/**
* @brief		Create a file mapping
* @param		[in] HANDLE hHandle: file handle, which should be opened first
* @param		[in] const char *szMappingName: name of mapping
* @return		Mapping handle if success; NULL, if fail
* @version		2009/03/24 Chunfeng Shen Initial version
*/ 
int os_mem_filemap_create(HANDLE hFile, uint64 nSize, 
						const char *szMappingName, FileMapHandle *pFileMap)
{	
	int32 nRet = RD_SUCCESS;
	DWORD dwSizeHigh, dwSizeLow;
	SECURITY_ATTRIBUTES sa;
	SECURITY_DESCRIPTOR sd;

	assert(HD_OS_INVALID_FILE_HANDLE != hFile);
	assert(NULL != pFileMap);

	InitializeSecurityDescriptor(&sd, SECURITY_DESCRIPTOR_REVISION);
	SetSecurityDescriptorDacl(&sd,TRUE,NULL,FALSE);
	sa.nLength = sizeof( SECURITY_ATTRIBUTES);
	sa.bInheritHandle = FALSE;
	sa.lpSecurityDescriptor = &sd;

	*pFileMap = INVALID_FILEMAP_HANDLE;

	nRet = os_build_sa(&sa);
	if (RD_SUCCESS != nRet)
		return nRet;

	dwSizeHigh = (DWORD)(nSize >> 32);
	dwSizeLow = (DWORD)(nSize & 0xFFFFFFFF);

	*pFileMap = CreateFileMappingA(hFile, &sa, PAGE_READWRITE, 
		dwSizeHigh, dwSizeLow, (LPCSTR)szMappingName);
	if (*pFileMap == NULL)
	{
		nRet = GetLastError();
	}
	os_destory_sa(&sa);
	return nRet;
}


/**
* @brief		Mapping a part of a file
* @param		[in] HANDLE hFileMapping: file mapping handle
* @param		[in] uint64 nOffsetPos: of the file offset where the view begins
* @param		[in] uint32 nNumMapBytes: number of bytes for mapping
* @return		Starting address of the mapped view if success; NULL, if fail
* @version		2009/03/24 Chunfeng Shen Initial version
*/
int os_mem_fileview_map(FileMapHandle hFileMapping, uint64 nOffsetPos, 
						  size_t nNumMapBytes, void** pMapView)
{
	DWORD dwFileOffsetHigh;
	DWORD dwFileOffsetLow;

	assert(INVALID_FILEMAP_HANDLE != hFileMapping);
	assert(NULL != pMapView);
	
	dwFileOffsetHigh = (DWORD)(nOffsetPos >> 32);
	dwFileOffsetLow = (DWORD)(nOffsetPos & 0xFFFFFFFF);
	
	*pMapView = MapViewOfFile(hFileMapping, FILE_MAP_ALL_ACCESS, 
		dwFileOffsetHigh, dwFileOffsetLow, nNumMapBytes);

	if (*pMapView == NULL)
		return GetLastError();

	return RD_SUCCESS;
}

/**
* @brief		Unmapping a part of a file
* @param		[in] void* pMapView: pointer of a view
* @return		RD_SUCCESS if success; EC_RD_OS_FILE_UNMAP, if fail
* @version		2009/03/24 Chunfeng Shen Initial version
*/
int os_mem_fileview_unmap(void* pMapView, size_t nNumMapBytes)
{
	assert(NULL != pMapView);
	
	if (UnmapViewOfFile(pMapView) != 0)
		return RD_SUCCESS;
	else			// if fail
		return EC_RD_OS_FILE_UNMAP;
}

int32 os_mem_filemap_flushview(void* pBaseAddr, int64 nBytes)
{
	if (FlushViewOfFile(pBaseAddr, (SIZE_T)nBytes))
	{
		return RD_SUCCESS;
	}
	else
	{
		return GetLastError();
	}
}

/**
* @brief		Close file mapping
* @param		[in] HANDLE hFileMapping: file mapping handle
* @return		RD_SUCCESS if success; EC_RD_OS_FILE_CLOSE_MAP, if fail
* @version		2009/03/24 Chunfeng Shen Initial version
*/
int os_mem_filemap_close(FileMapHandle hFileMapping)
{
	assert (INVALID_FILEMAP_HANDLE != hFileMapping);
	
	if (CloseHandle(hFileMapping) != 0)	// success
		return RD_SUCCESS;
	else
		return EC_RD_OS_FILE_CLOSE_MAP;
}

/**
* @brief		Create shared memory
* @param [in]	nSize size of shared memory
* @param [in]	szShmName shared memory name
* @return		shared memory handle if success; INVALID_HANDLE, if fail
* @version		2009/10/29 Chunfeng Shen Initial version
*/
int32 os_mem_sharemem_create(uint32 nSize, const char *szShmName, MemMapHandle *pHandle)
{	
	int32 nRet = RD_SUCCESS;
	MemMapHandle hMapping;
	SECURITY_ATTRIBUTES sa;
	SECURITY_DESCRIPTOR sd;

	assert(szShmName != NULL);
	assert(nSize > 0);

	sa.lpSecurityDescriptor = &sd;
	nRet = os_build_sa(&sa);
	if (RD_SUCCESS != nRet) return nRet;
	
	hMapping = CreateFileMappingA(INVALID_HANDLE_VALUE, &sa, PAGE_READWRITE, 
		0, nSize, (LPSTR)szShmName);
	if (NULL == hMapping)
	{
		os_destory_sa(&sa);
		return GetLastError();
	}
	os_destory_sa(&sa);
	*pHandle = hMapping;
	return RD_SUCCESS;
}

/**
* @brief		Map shared memory
* @param [in]	hMapping handle created by os_mem_sharemem_create
* @return		memory address if success; NULL if fail
* @version		2009/10/29 Chunfeng Shen Initial version
*/
int os_mem_sharemem_map(MemMapHandle hMapping, void** pShareMem)
{
	assert(NULL != pShareMem);

	*pShareMem = (void*)MapViewOfFile(hMapping, FILE_MAP_WRITE | FILE_MAP_READ, 0, 0, 0);
	if (NULL == *pShareMem)
		return GetLastError();

	return RD_SUCCESS;
}

/**
* @brief		Detach shared memory
* @param [in]	pShm shared memory address
* @return		RD_SUCCESS if success; error code if fail
* @version		2009/10/29 Chunfeng Shen Initial version
*/
int os_mem_sharemem_unmap(void* pShm)
{
	assert(NULL != pShm);

	if (UnmapViewOfFile(pShm) != 0)
		return RD_SUCCESS;
	else			// if fail
		return EC_RD_OS_MEM_SHAREMEM_UNMAP;
}

/**
* @brief		Close shared memory
* @param [in]	hMapping handle created by os_mem_sharemem_create
* @return		RD_SUCCESS if success; error code if fail
* @version		2009/10/29 Chunfeng Shen Initial version
*/
int os_mem_sharemem_close(MemMapHandle hMapping)
{
	assert (INVALID_MEMMAP_HANDLE != hMapping);

	if (CloseHandle(hMapping) != 0)	// success
		return RD_SUCCESS;
	else
		return EC_RD_OS_MEM_SHAREMEM_CLOSE;
}

