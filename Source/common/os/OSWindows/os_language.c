/** 
 * @file
 * @brief		Language operation
 * <AUTHOR>
 * @date		2010/03/10
 * @version		Initial Version
 * @version  02/27/2014 G.L. modify version
 */

#include "os/os_language.h"
#include "error_code.h"
#include <stdlib.h>
/* 03-10-2015 g.l. remove #include ihyperdb.h */
#include <windows.h>
#include <stdio.h>

int32 _os_lang_conv_ansi_to_unicode(const char *szSrc, char *szDest, int nDesLen)
{
	return os_lang_conv_sys_to_utf8(szSrc, szDest, nDesLen);
}

int32 _os_lang_conv_unicode_to_ansi(const char *szSrc, char *szDest, int nDesLen)
{
	return os_lang_conv_utf8_to_sys(szSrc, szDest, nDesLen);
}

int32 os_lang_conv_sys_to_utf8(const char *szSrc, char *szDest, int nD<PERSON>)
{
	int32 nLen=0;
	char* szUnicode = NULL;
	int32 nLenUnicode = strlen(szSrc) * 3 + 10;
	szUnicode = (char*)malloc(nLenUnicode);	
	if (NULL == szUnicode)
	{
		return EC_RD_OS_MEM_MALLOC;
	}
	nLen = MultiByteToWideChar(CP_ACP, 0, szSrc, -1, NULL, 0);
	if(nLen > nLenUnicode)
	{
		free(szUnicode);
		return EC_RD_LANGUAGE_ICONV;
	}
	MultiByteToWideChar(CP_ACP, 0, szSrc, -1, (LPWSTR)szUnicode, nLen);
	nLen = WideCharToMultiByte(CP_UTF8, 0, (LPCWSTR)szUnicode, -1, NULL, 0, NULL, NULL);
	if(nLen > nDesLen)
	{
		free(szUnicode);
		return EC_RD_LANGUAGE_ICONV;
	}
	WideCharToMultiByte(CP_UTF8, 0, (LPCWSTR)szUnicode, -1, szDest, nLen, NULL, NULL);

	free(szUnicode);
	szUnicode = NULL;
	return RD_SUCCESS;
}

int32 _hd_safe_strncpy(const char *szSrc, char *szDest, int nDesLen)
{
	int32 nRet = RD_SUCCESS;
	int nSrcLen = 0;

	memset(szDest, 0x00, nDesLen);
	*(szDest + nDesLen - 1) = '\0';
	-- nDesLen;

	nSrcLen =  strlen(szSrc);
	if (0 == nSrcLen)
	{
		*(szDest) = '\0';
		return RD_SUCCESS;
	}
	if (nDesLen < nSrcLen)
		return EC_RD_LANGUAGE_ILLEGAL_INPUT;
	strcpy(szDest, szSrc);
	return RD_SUCCESS;
}

int32 os_lang_conv_utf8_to_sys(const char *szSrc, char *szDest, int nDesLen)
{
	int nLen=0;
	char* szUnicode = NULL;
	int32 nLenUnicode = strlen(szSrc) * 3 + 10;
	szUnicode = (char*)malloc(nLenUnicode);	
	if (NULL == szUnicode)
	{
		return EC_RD_OS_MEM_MALLOC;
	}
	nLen = MultiByteToWideChar(CP_UTF8, 0, szSrc, -1, NULL, 0);
	if(nLen > nLenUnicode)
	{
		free(szUnicode);
		return EC_RD_LANGUAGE_ICONV;
	}
	MultiByteToWideChar(CP_UTF8, 0, szSrc, -1, (LPWSTR)szUnicode, nLen);
	nLen = WideCharToMultiByte(CP_ACP, 0, (LPCWSTR)szUnicode, -1, NULL, 0, NULL, NULL);
	if(nLen > nDesLen)
	{
		free(szUnicode);
		return EC_RD_LANGUAGE_ICONV;
	}
	WideCharToMultiByte(CP_ACP, 0, (LPCWSTR)szUnicode, -1, szDest, nLen, NULL, NULL);

	free(szUnicode);
	szUnicode = NULL;
	return RD_SUCCESS;
}

int32 os_lang_conv_given_to_unicode(HD_OS_CHARSET charset, const char* szSrc, char* szDest, int nDesLen)
{
	int32 nRet = RD_SUCCESS;
	// verify first
	if ((NULL == szSrc) || (NULL == szDest) || (nDesLen < 1))
		return EC_RD_LANGUAGE_ILLEGAL_INPUT;

	switch (charset)
	{
	case HD_OS_CHARSET_ANSI:
		nRet = _os_lang_conv_ansi_to_unicode(szSrc, szDest, nDesLen);
		break;
	case HD_OS_CHARSET_UNICODE:
		nRet = _hd_safe_strncpy(szSrc, szDest, nDesLen);
		break;
	case HD_OS_CHARSET_SYS:
	default:
		nRet = os_lang_conv_sys_to_utf8(szSrc, szDest, nDesLen);
		break;
	}
	return nRet;
}

int32 os_lang_conv_unicode_to_given(HD_OS_CHARSET charset, const char* szSrc, char* szDest, int nDesLen)
{
	int32 nRet = RD_SUCCESS;
	switch (charset)
	{
	case HD_OS_CHARSET_ANSI:
		nRet = _os_lang_conv_unicode_to_ansi(szSrc, szDest, nDesLen);
		break;
	case HD_OS_CHARSET_UNICODE:
		nRet = _hd_safe_strncpy(szSrc, szDest, nDesLen);
		break;
	case HD_OS_CHARSET_SYS:
	default:
		nRet = os_lang_conv_utf8_to_sys(szSrc, szDest, nDesLen);
		break;
	}
	return nRet;
}
