#include "os_event.h"
#include "error_code.h"
#include <assert.h>
#include "stdlib.h"
#include "data_types.h"
#include "os_sa.h"

/**
* @brief		init event
* @param [in]	phEvent event handle pointer
* @param [in]	szEventName event name
* @return		RD_SUCCESS if success, else EC_RD_EVENT_INIT;
* @version		2009/10/19 Huang Songxin Initial version
*/ 

int32 os_event_init(EventHandle* pEvent, const char* szEventName)
{
	assert (pEvent != NULL);

	if (NULL == szEventName)
	{
		*pEvent = CreateEventA(NULL, FALSE, FALSE, NULL);
	}
	else
	{
		int32 nRet = RD_SUCCESS;
		SECURITY_ATTRIBUTES sa;
		SECURITY_DESCRIPTOR sd;

		sa.lpSecurityDescriptor = &sd;
		nRet = os_build_sa(&sa);
		if (RD_SUCCESS != nRet) return nRet;

		*pEvent = CreateEventA(&sa, FALSE, FALSE, (LPCSTR)szEventName);
		os_destory_sa(&sa);
	}
	if(*pEvent == NULL)
	{
		return GetLastError();
	}
	return RD_SUCCESS;
}

/**
* @brief		release event
* @param [in]	phEvent event handle pointer
* @param [in]	szEventName event name
* @return		RD_SUCCESS if success, else EC_RD_EVENT_RELEASE;
* @version		2009/10/19 Huang Songxin Initial version
*/
 
int32 os_event_destroy(EventHandle hEvent)
{
	int32 nRet;
	nRet = CloseHandle(hEvent);
	if(nRet != 0)
	{
		return RD_SUCCESS;
	}
	else
	{
		return EC_RD_EVENT_RELEASE;
	}
}
/**
* @brief		event wait with timeout
* @param [in]	hEvent, event handle
* @param [in]	timeout, the timeout, unit is millisecond
* @return		RD_SUCCESS if success, else EC_RD_EVENT_WAIT_TIMEOUT;
* @version		2010/5/10 Huang Songxin Initial version
*/

int32 os_event_timedwait(EventHandle hEvent, int32 nSecTimeout, int32 nMSecTimeout)
{
	int32 nRet;
	int32 nMSec;
	
	if(nSecTimeout >= 0x1FFFFFFF)
	{
		nRet = WaitForSingleObject(hEvent, INFINITE);
	}
	else
	{
		nMSec = nSecTimeout*1000 + nMSecTimeout;
		nRet = WaitForSingleObject(hEvent, nMSec);
	}
	switch(nRet)
	{
		case WAIT_ABANDONED:
			nRet = EC_RD_EVENT_MUTEX_NOT_RELEASE;
			break;
		case WAIT_OBJECT_0:
			nRet = RD_SUCCESS;
			break;
		case WAIT_TIMEOUT:
			nRet = EC_RD_EVENT_WAIT_TIMEOUT;
			break;
		case WAIT_FAILED:
			nRet = GetLastError();	
			break;
		default:
			nRet = RD_SUCCESS;		
			break;
	}

	return nRet;
}