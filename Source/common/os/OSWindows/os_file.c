/** 
 * @file
 * @brief		file operation function
 * <AUTHOR>
 * @date		2009/03/18
 * @version		Initial Version
 * HISTORY      DATE            OPERATION                    VERSION     OPERATOR
 *              2009-05-07     Add Function                    0.02        LiKai   
                2009-06-12     Add filelist function           0.03        Huang Songxin
 * Create file, read and write file.
 */
#include "os_file.h"
#include <windows.h>
#include <io.h>
#include <errno.h>
#include <string.h>
#include <tchar.h>
#include <stdio.h>
#include "error_code.h"
#include "os_time.h"
#include <assert.h>
/**
 * @brief		Open a file
 * @param [in]	szFileName file name
 * @param [in]	nMode O_RDONLY, O_WRONLY, O_RDWR; O_CREAT, O_TRUNC, O_EXCL
 * @return		File handle, if success; INVALID_HANDLE if fail.
 * @version		2009/03/17 Chunfeng Shen Initial version
 */

int32 os_file_open(const char *szFileName, int32 nMode, HANDLE* pFile)
{
	DWORD dwAccess, dwCreation;
	assert(szFileName != NULL);

	/* desired access mode */
	dwAccess = GENERIC_READ;
	if ((nMode & O_WRONLY) != 0)
		dwAccess = GENERIC_WRITE;
	else if ((nMode & O_RDWR) != 0)
		dwAccess = GENERIC_READ | GENERIC_WRITE;

	// creation disposition
	dwCreation = OPEN_EXISTING;
	if ((nMode & (O_CREAT | O_EXCL)) == (O_CREAT | O_EXCL))
		dwCreation = CREATE_NEW;
	else if ((nMode & (O_CREAT | O_TRUNC)) == (O_CREAT | O_TRUNC))
		dwCreation = CREATE_ALWAYS;
	else if ((nMode & O_CREAT) != 0)
		dwCreation = OPEN_ALWAYS;
	else if ((nMode & O_TRUNC) != 0)
		dwCreation = TRUNCATE_EXISTING;

	*pFile = CreateFileA(szFileName, dwAccess, FILE_SHARE_READ, NULL, 
		dwCreation, FILE_ATTRIBUTE_NORMAL, NULL);

	if (INVALID_HANDLE_VALUE == *pFile)
	{
		return (int32)GetLastError();
	}

	return RD_SUCCESS;
}

/**
 * @brief		Close a file handle
 * @param [in]	HANDLE hHandle: file handle
 * @return		RD_SUCCESS, if success; EC_RD_OS_FILE_CLOSE, otherwise
 * @version		2009/03/21 Chunfeng Shen Initial version
 */ 
 
int32 os_file_close(HANDLE hFile)
{
	assert(HD_OS_INVALID_FILE_HANDLE != hFile);

	if (CloseHandle(hFile) == FALSE)
		return (int32)GetLastError();

	return RD_SUCCESS;
}

/**
* @brief		Flush file cache to disk
* @param [in]	hHandle file handle
* @return		RD_SUCCESS, if flush success; EC_RD_OS_FILE_FLUSH, otherwise
* @version		2009/04/09 Chunfeng Shen Initial version
*/ 
 int32 os_file_flush(HANDLE hFile)
{
	assert(HD_OS_INVALID_FILE_HANDLE != hFile);
	
	if (FALSE == FlushFileBuffers(hFile))	/* fail */
	{
		return (int32)GetLastError();
	}

	return RD_SUCCESS;
}


/**
 * @brief		Seek file pointer which is 64 bits
 * @param [in]	hHandle file handle
 * @param [in]	nOffset offset
 * @param [in]	nRelative SEEK_SET | SEEK_CUR | SEEK_END
 * @return		RD_SUCCESS if success; EC_RD_OS_FILE_SEEK, if fail
 * @version		2009/03/17 Chunfeng Shen Initial version
 */ 
 int32 os_file_seek64(HANDLE hFile, int64 nOffset, uint32 nRelative)
{
	LARGE_INTEGER nTotalOffset;
	assert (hFile != HD_OS_INVALID_FILE_HANDLE);

	nTotalOffset.QuadPart = nOffset;
	nTotalOffset.LowPart = SetFilePointer(hFile, (LONG)nTotalOffset.LowPart,
		&(nTotalOffset.HighPart), (DWORD)nRelative);

	if ((nTotalOffset.LowPart == (DWORD)-1) && GetLastError() != NO_ERROR)
	{
		return (int32)GetLastError();
	}

	return RD_SUCCESS;
}

/**
 * @brief		write data to file
 * @param [in]	hHandle file handle
 * @param [in]	pBuf data buffer pointer
 * @param [in]	nByte number of written bytes
 * @return		nBytes if success; -1, if fail
 * @version		2009/03/21 Chunfeng Shen Initial version
 * HISTORY       
 *				DATE		VERSION     OPERATOR       OPERATION       
 *              2009/05/26    0.01	 zhangchaofeng  change the return from uint32 to int32 
 * change the type of nBytes from uint32 to int32  
 */ 
 int32 os_file_write(HANDLE hFile, const void *pBuf, int32 nBytesToWrite, int32 *pBytesWritten)
{
	BOOL bRet;
	DWORD nNumberOfBytesWrite;
	assert (hFile != (HANDLE)HD_OS_INVALID_FILE_HANDLE);
	assert (pBuf != NULL);
	assert (pBytesWritten != NULL);

	bRet = WriteFile(hFile, pBuf, (DWORD)nBytesToWrite, &nNumberOfBytesWrite, 0);
	if (bRet == FALSE)
		return (int32)GetLastError();

	*pBytesWritten = nNumberOfBytesWrite;
	if (nNumberOfBytesWrite != nBytesToWrite)
		return EC_RD_OS_FILE_WRITE;

	return RD_SUCCESS;
}

/**
* @brief		read file
* @param [in]	hHandle file handle
* @param [in]	pBuf data buffer pointer
* @param [in]	nBytes number of reading bytes
* @return		read bytes if success; -1, if fail
* @version		2009/03/21 Chunfeng Shen Initial version
*/ 
 int32 os_file_read(HANDLE hFile, void *pBuf, int32 nBytesToRead, int32 *pBytesRead)
{
	BOOL bRet;
	DWORD nNumberOfBytesRead;
	assert (hFile != (HANDLE)HD_OS_INVALID_FILE_HANDLE);
	assert (pBuf != NULL);
	assert (pBytesRead != NULL);

	bRet = ReadFile(hFile, pBuf, (DWORD)nBytesToRead, &nNumberOfBytesRead, 0);
	if (bRet == FALSE)
		return (int32)GetLastError();

	*pBytesRead = nNumberOfBytesRead;
	if (nNumberOfBytesRead != nBytesToRead)
		return EC_RD_OS_FILE_READ;

	return RD_SUCCESS;
}

/**
* @brief		seek and read file
* @param [in]	hHandle file handle
* @param [in]	nOffset offset
* @param [in]	pBuf data buffer pointer
* @param [in]	nBytes number of reading bytes
* @return		RD_SUCCESS, if success; EC_RD_OS_FILE_SEEK or EC_RD_OS_FILE_READ, if fail
* @version		2009/07/08 Chunfeng Shen Initial version
*/ 
 int32 os_file_seek_read(HANDLE hFile, int64 nOffset, void* pBuf, int32 nBytesToRead, int32 *pBytesRead)
{
	DWORD nNumberOfBytesRead;
	LARGE_INTEGER nTotalOffset;
	assert (hFile != HD_OS_INVALID_FILE_HANDLE);
	assert (pBuf != NULL);
	assert (pBytesRead != NULL);

	nTotalOffset.QuadPart = nOffset;
	nTotalOffset.LowPart = SetFilePointer(hFile, nTotalOffset.LowPart,
		&(nTotalOffset.HighPart), SEEK_SET);
	if ((nTotalOffset.LowPart == -1) && GetLastError() != NO_ERROR)
	{
		return (int32)GetLastError();
	}

	if (ReadFile(hFile, pBuf, (DWORD)nBytesToRead, &nNumberOfBytesRead, 0) == FALSE)
		return (int32)GetLastError();

	*pBytesRead = nNumberOfBytesRead;
	if (nBytesToRead != nNumberOfBytesRead)
		return EC_RD_OS_FILE_READ;

	return RD_SUCCESS;
}

/**
* @brief		seek and write file
* @param [in]	hHandle file handle
* @param [in]	nOffset offset
* @param [in]	pBuf data buffer pointer
* @param [in]	nBytes number of writing bytes
* @return		RD_SUCCESS, if success; EC_RD_OS_FILE_SEEK or EC_RD_OS_FILE_WRITE, if fail
* @version		2009/07/08 Chunfeng Shen Initial version
*/ 
 int32 os_file_seek_write(HANDLE hHandle, int64 nOffset, const void* pBuf, 
										int32 nBytesToWrite, int32 *pBytesWritten)
{
	DWORD nNumberOfBytesWritten;
	LARGE_INTEGER nTotalOffset;
	assert (hHandle != HD_OS_INVALID_FILE_HANDLE);
	assert (pBuf != NULL);
	assert (pBytesWritten != NULL);

	nTotalOffset.QuadPart = nOffset;
	nTotalOffset.LowPart = SetFilePointer(hHandle, nTotalOffset.LowPart,
		&(nTotalOffset.HighPart), SEEK_SET);
	if ((nTotalOffset.LowPart == -1) && GetLastError() != NO_ERROR)
	{
		return (int32)GetLastError();
	}

	if (WriteFile(hHandle, pBuf, nBytesToWrite, &nNumberOfBytesWritten, 0) == FALSE)
		return (int32)GetLastError();

	*pBytesWritten = nNumberOfBytesWritten;
	if (*pBytesWritten != nBytesToWrite)
		return EC_RD_OS_FILE_WRITE;

	return RD_SUCCESS;
}

/**
* @brief		Get page size
* @return		byte number of page size if success; -1, if fail
* @version		2009/03/24 Chunfeng Shen Initial version
*/ 
 int32 os_get_page_size(void)
{
	SYSTEM_INFO siSysInfo;
	GetSystemInfo (&siSysInfo);
	return (int32) siSysInfo.dwPageSize;
}

/**
* @brief		Get disk free space
* @param		[in] const void* szPathName: path name
  @param		[in] HANDLE hHandle: file system handle

* @return		free bytes of disk if success; -1, if fail
* @version		2009/03/21 Chunfeng Shen Initial version
             
*/ 
 int32 os_get_disk_free_space(const void* szPathName, int64 *pSize)
{
	int nRet = 0;
	ULARGE_INTEGER nFreeBytesAvailable;
	ULARGE_INTEGER nTotalNumberOfBytes;
	ULARGE_INTEGER nTotalNumberOfFreeBytes;
	nRet = GetDiskFreeSpaceEx((LPCWSTR)szPathName, &nFreeBytesAvailable, 
		&nTotalNumberOfBytes, &nTotalNumberOfFreeBytes);

	if (0 == nRet)	// error
		return GetLastError();

	*pSize = (int64)nFreeBytesAvailable.QuadPart;
	return RD_SUCCESS;
}

/**
* @brief		Get atomic write bytes
* @return		Minimal bytes when writing disk if success; -1, if fail
* @version		2009/03/21 Chunfeng Shen Initial version
*/ 
 int32 os_get_atomic_write_bytes(void)
{
	DWORD nSectorsPerCluster;
	DWORD nBytesPerSector;
	DWORD nNumberOfFreeClusters;
	DWORD nTotalNumberOfClusters;
	int nRet = GetDiskFreeSpace(NULL, &nSectorsPerCluster, &nBytesPerSector, 
		&nNumberOfFreeClusters, &nTotalNumberOfClusters);
	
	if (0 == nRet)	// if error
	{
		return -1;
	}

	return nSectorsPerCluster * nBytesPerSector;
}

/**
* @brief		Judge whether the file exists,or whether the file can be read or written
* @param [in]	szFileName: file name
* @param [in]	nMode: R_OK | W_OK | F_OK(File Existing)
* @return		0 if the file has the given mode;
				-1 if the named file does not exist or does not have the given mode
* @version		2009/03/23 Chunfeng Shen Initial version
*/ 


int32 os_file_access(const char* szFileName, int nMode)
{
	// Windows doesn't support checking X_OK(6)
	int nRet;
	nRet = _access(szFileName, nMode & 6);
	if (-1 == nRet)
	{
		if (EACCES == errno)
		{
			return EC_RD_SYS_PERMISSION_DENY;
		}
		else if (ENOENT == errno)
		{
			return EC_RD_SYS_NO_FILE_OR_DIR;
		}
		else if (EINVAL == errno)
		{
			return EC_RD_SYS_INVALID_ARGUMENT;
		}
	}

	return RD_SUCCESS;
}

/**
* @brief		truncate the file from the end of file
* @param [in]	hFile handle of the file
* @param [in]	nOffset truncate the file from nOffset position
* @return		RD_SUCCESS, if success;other means fail.
* @note			file lock should be applied to this function; and this file must be opened writable
* @version		2009/10/13 Chunfeng Shen Initial version
*/ 
 
int32 os_file_truncate(HANDLE hFile, int64 nOffset)
{
	LARGE_INTEGER nTotalOffset;
	assert (HD_OS_INVALID_FILE_HANDLE != hFile);

	nTotalOffset.QuadPart = nOffset;
	nTotalOffset.LowPart = SetFilePointer(hFile, nTotalOffset.LowPart,
		&(nTotalOffset.HighPart), SEEK_SET);
	if ((nTotalOffset.LowPart == -1) && GetLastError() != NO_ERROR)
	{
		return (int)GetLastError();
	}

	if (SetEndOfFile(hFile) == FALSE)
	{
		return (int)GetLastError();
	}

	return RD_SUCCESS;
}

/**
* @brief		Get File Size
* @param [in]	hHandle file handle
* @param [out]	pnFileSize file size
* @return		RD_SUCCESS if success; EC_RD_OS_FILE_SEEK, otherwise
* @version		2009/10/13 Chunfeng Shen  Initial version  Create
*/ 
 
int32 os_file_get_size(HANDLE hFile, int64* pnFileSize)
{
	LARGE_INTEGER nTotalOffset;
	assert (hFile != HD_OS_INVALID_FILE_HANDLE);
	nTotalOffset.QuadPart = 0;
	nTotalOffset.LowPart = SetFilePointer(hFile, 0,
		&(nTotalOffset.HighPart), SEEK_END);

	if ((nTotalOffset.LowPart == -1) && GetLastError() != NO_ERROR)
	{
		return (int)GetLastError();
	}
	*pnFileSize = nTotalOffset.QuadPart;

	return RD_SUCCESS;
}

/**
* @brief		get total physical memory size
* @param		[in] None
* @return		total physical memory size(K)
* @version		2009/05/26 zhangchaofeng  Initial version  Create     
*/ 
 
uint32 os_get_total_memory_size()
{
	//MEMORYSTATUSEX statex;

	//statex.dwLength = sizeof (statex);//must have

	//GlobalMemoryStatusEx (&statex);

	//return (uint32)(statex.ullTotalPhys/1024);
	return 10000000;
}

