#include "os_sa.h"
#include "error_code.h"
#include <assert.h>

int32 os_build_sa(SECURITY_ATTRIBUTES* pSA)
{
	int32 nRet = RD_SUCCESS;
	PSECURITY_DESCRIPTOR pSD;
	SID_IDENTIFIER_AUTHORITY siaWorld = SECURITY_WORLD_SID_AUTHORITY;
	DWORD  dwAclLength;
	PSID   psidEveryone = NULL;
	PACL   pDACL   = NULL;
	PACCESS_ALLOWED_ACE pACE = NULL;
	SECURITY_INFORMATION si = DACL_SECURITY_INFORMATION;

	assert(NULL != pSA);
	assert(NULL != pSA->lpSecurityDescriptor);

	pSA->bInheritHandle = false;
	pSA->nLength = sizeof(SECURITY_ATTRIBUTES);
	pSD = pSA->lpSecurityDescriptor;

	// initialize the security descriptor
	if (!InitializeSecurityDescriptor(pSD, SECURITY_DESCRIPTOR_REVISION))
	{
		nRet = GetLastError();
		goto err_goto;
	}

	// obtain a sid for the Authenticated Users Group
	if (!AllocateAndInitializeSid(&siaWorld, 1,	SECURITY_WORLD_RID, 0, 0, 0, 0, 0, 0, 0, &psidEveryone))
	{
		nRet = GetLastError();
		goto err_goto;
	}

	// NOTE:
	// 
	// The Authenticated Users group includes all user accounts that
	// have been successfully authenticated by the system. If access
	// must be restricted to a specific user or group other than 
	// Authenticated Users, the SID can be constructed using the
	// LookupAccountSid() API based on a user or group name.

	// calculate the DACL length
	dwAclLength = sizeof(ACL)
		+ sizeof(ACCESS_ALLOWED_ACE) - sizeof(DWORD) // add space for Authenticated Users group ACE
		+ GetLengthSid(psidEveryone);

	// allocate memory for the DACL
	if (NULL == (pDACL = (PACL)HeapAlloc(GetProcessHeap(), HEAP_ZERO_MEMORY, dwAclLength)))
	{
		nRet = GetLastError();
		goto err_goto;
	}

	// initialize the DACL
	if (!InitializeAcl(pDACL, dwAclLength, ACL_REVISION))
	{
		nRet = GetLastError();
		goto err_goto;
	}

	// add the Authenticated Users group ACE to the DACL with
	// GENERIC_READ, GENERIC_WRITE, and GENERIC_EXECUTE access
	if (!AddAccessAllowedAce(pDACL, ACL_REVISION, GENERIC_ALL, psidEveryone))
	{
		nRet = GetLastError();
		goto err_goto;
	}

	// set the DACL in the security descriptor
	if (!SetSecurityDescriptorDacl(pSD, TRUE, pDACL, FALSE))
	{
		nRet = GetLastError();
		goto err_goto;
	}

	FreeSid(psidEveryone);
	return RD_SUCCESS;

err_goto:
	if (psidEveryone) 
		FreeSid(psidEveryone);

	if (pDACL)
	{
		HeapFree(GetProcessHeap(), 0, pDACL);
	}
	pDACL = NULL;

	return nRet;
}


int32 os_destory_sa(SECURITY_ATTRIBUTES* pSA)
{
	BOOL bPresent;
	BOOL bDefault;
	PACL pDACL;

	assert(NULL != pSA);

	if (!GetSecurityDescriptorDacl(pSA->lpSecurityDescriptor, &bPresent, &pDACL, &bDefault))
	{
		return GetLastError();
	}
	if (bPresent && pDACL)
	{
		HeapFree(GetProcessHeap(), 0, pDACL);
	}
	return RD_SUCCESS;
}
