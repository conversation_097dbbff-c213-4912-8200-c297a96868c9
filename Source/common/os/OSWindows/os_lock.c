#include "os_lock.h"
#include "error_code.h"
#include <assert.h>
#include "stdlib.h"
#include "os_sa.h"

 int32 os_spinlock_init(SPINLOCK *pSpinLock)
{
	if (InitializeCriticalSectionAndSpinCount(pSpinLock, 4000))
		return RD_SUCCESS;
	else
		return GetLastError();
}

 int32 os_spinlock_destroy(SPINLOCK *pSpinLock)
{
	DeleteCriticalSection(pSpinLock);
	return RD_SUCCESS;
}

 int32 os_spinlock_acquire(SPINLOCK *pSpinLock)
{
	EnterCriticalSection(pSpinLock);
	return RD_SUCCESS;
}

 int32 os_spinlock_release(SPINLOCK *pSpinLock)
{
	LeaveCriticalSection(pSpinLock);
	return RD_SUCCESS;
}


int32 os_mutex_init(MutexHandle *pMutexHandle)
{
	int32 nRet;
	CRITICAL_SECTION *pCriticalSection;
	assert(pMutexHandle != NULL);

	pCriticalSection = (CRITICAL_SECTION*)malloc(sizeof(CRITICAL_SECTION));
	if (NULL == pCriticalSection)
	{
		nRet = EC_RD_OS_MEM_MALLOC;
		goto err_goto;
	}

	InitializeCriticalSection(pCriticalSection);
	*pMutexHandle = pCriticalSection;

	return RD_SUCCESS;

err_goto:
	free(pCriticalSection);
	return nRet;
}


void os_mutex_destroy(MutexHandle hMutex)
{
	assert(hMutex != NULL);
	DeleteCriticalSection(hMutex);
	free(hMutex);
}

/**
* @brief		Acquire a spin lock
* @param [in]	pSpinInfo pointer of spin lock info. Spin lock can only be
				acquired when *pSpinInfo is 1. Then *pSpinInfo will be set 0
* @version		2009/10/17 Chunfeng Shen Initial version
*/ 
/*
 void os_spinlock_acquire_pointer(char *pSpinInfo)
 {
 	__asm{
 		mov ah, 0h
 spinacquire:
 		mov al, 1h
 #ifdef WIN32		// 32bit CPU
 		mov ebx, dword ptr [pSpinInfo]
 		lock cmpxchg byte ptr [ebx], ah
 #endif
 #ifdef WIN64	// 64bit CPU
 		mov rbx, qword ptr [pSpinInfo]
 		lock cmpxchg byte ptr [rbx], ah
 #endif
 		jz spinacquire_fini
 		rep nop
 		jmp spinacquire
 spinacquire_fini:
 	}
 }
*/

/**
* @brief		release a spin lock
* @param [in, out]	pSpinInfo pointer of spin lock info. Spin lock will be set 1
* @version		2009/10/17 Chunfeng Shen Initial version
*/ 
// void os_spinlock_release(char *pSpinInfo)
// {
// 	__asm{
// #ifdef WIN32		// 32bit CPU
// 		mov eax, dword ptr [pSpinInfo]
// 		mov byte ptr [eax], 1h
// #endif
// #ifdef WIN64		// 64bit CPU
// 		mov rax, qword ptr [pSpinInfo]
// 		mov byte ptr [rax], 1h
// #endif
// 	}
// }


int32 os_rwmutex_init(RwMutexHandle *pRwMutexHandle)
{
	int32 nRet;
	RwMutexInfo *pRwMutexInfo;

	pRwMutexInfo = (RwMutexInfo*)malloc(sizeof(RwMutexInfo));
	if (NULL == pRwMutexInfo)
	{
		return EC_RD_OS_MEM_MALLOC;
	}

	pRwMutexInfo->hReadEvent = CreateEvent(NULL, TRUE, FALSE, NULL);	/* manual reset */
	if (NULL == pRwMutexInfo->hReadEvent)
	{
		nRet = (int32)GetLastError();
		goto err_goto;
	}

	pRwMutexInfo->hWriteEvent = CreateEvent(NULL, FALSE, FALSE, NULL);	/* auto reset */
	if (NULL == pRwMutexInfo->hWriteEvent)
	{
		nRet = (int32)GetLastError();
		goto err_goto;
	}

	InitializeCriticalSection(&pRwMutexInfo->lock);

	pRwMutexInfo->nNumReadersHoldLock = 0;
	pRwMutexInfo->nNumWaitReaders = 0;
	pRwMutexInfo->nNumWaitWriters = 0;

	*pRwMutexHandle = pRwMutexInfo;
	return RD_SUCCESS;

err_goto:
	if (NULL != pRwMutexInfo)
	{
		if (pRwMutexInfo->hReadEvent != NULL)
			CloseHandle(pRwMutexInfo->hReadEvent);
		if (pRwMutexInfo->hWriteEvent != NULL)
			CloseHandle(pRwMutexInfo->hWriteEvent);
		free(pRwMutexInfo);
	}
	return nRet;
}


void os_rwmutex_destroy(RwMutexHandle hRwMutex)
{
	RwMutexInfo *pRwMutex = hRwMutex;
	assert(NULL != pRwMutex);
	assert(NULL != pRwMutex->hReadEvent);
	assert(NULL != pRwMutex->hWriteEvent);

	DeleteCriticalSection(&pRwMutex->lock);
	CloseHandle(pRwMutex->hReadEvent);
	CloseHandle(pRwMutex->hWriteEvent);
	free(pRwMutex);
}


int32 os_rwmutex_acquire_read(RwMutexHandle hRwMutex)
{
	int32 nResult = 0;
	RwMutexInfo *pRwMutex = hRwMutex;
	assert (pRwMutex != NULL);

	EnterCriticalSection (&pRwMutex->lock);

	/* if write thread owns lock or a writing thread is waiting */
	while (pRwMutex->nNumReadersHoldLock < 0 
		|| pRwMutex->nNumWaitWriters > 0)
	{
		pRwMutex->nNumWaitReaders++;
		LeaveCriticalSection(&pRwMutex->lock);
		if(WaitForSingleObject(pRwMutex->hReadEvent, INFINITE) == WAIT_OBJECT_0)
			nResult = 0;
		else
			nResult = -1;
		if (-1 == nResult)
			return EC_RD_OS_LOCK_TIMEOUT;
		/*save CPU*/
		Sleep(10);
		EnterCriticalSection(&pRwMutex->lock);
		pRwMutex->nNumWaitReaders--;
	}

	pRwMutex->nNumReadersHoldLock++;
	LeaveCriticalSection(&pRwMutex->lock);

	return RD_SUCCESS;
}


int32 os_rwmutex_acquire_write(RwMutexHandle hRwMutex)
{
	RwMutexInfo *pRwMutex = hRwMutex;
	int32 nResult = 0;
	assert(pRwMutex != NULL);
	EnterCriticalSection(&pRwMutex->lock);

	/* if some reads or writer own the lock */
	while (pRwMutex->nNumReadersHoldLock != 0)
	{
		pRwMutex->nNumWaitWriters++;
		LeaveCriticalSection(&pRwMutex->lock);
		if (WaitForSingleObject(pRwMutex->hWriteEvent, INFINITE) == WAIT_OBJECT_0)
			nResult = 0;
		else
			nResult = -1;
		if (-1 == nResult)
			return EC_RD_OS_LOCK_TIMEOUT;

		EnterCriticalSection(&pRwMutex->lock);
		pRwMutex->nNumWaitWriters--;
	}

	pRwMutex->nNumReadersHoldLock = -1;
	LeaveCriticalSection(&pRwMutex->lock);

	return RD_SUCCESS;
}


int32 os_rwmutex_release(RwMutexHandle hRwMutex)
{
	RwMutexInfo* pRwMutex = hRwMutex;
	EnterCriticalSection(&pRwMutex->lock);

	if (pRwMutex->nNumReadersHoldLock > 0)		/* readers hold lock */
		pRwMutex->nNumReadersHoldLock--;
	else if (pRwMutex->nNumReadersHoldLock == -1)	/* Writer holds lock */
		pRwMutex->nNumReadersHoldLock = 0;
	else
	{
		LeaveCriticalSection(&pRwMutex->lock);
		return RD_SUCCESS;
	}

	/* wake up all waiters */
	if (pRwMutex->nNumWaitWriters > 0 &&
		pRwMutex->nNumReadersHoldLock == 0)		/* no reader holds lock and some writers are waiting for lock*/
	{
		SetEvent(pRwMutex->hWriteEvent);
	}
	else if (pRwMutex->nNumWaitReaders > 0 &&		/* no writers but readers are waiting */
		pRwMutex->nNumWaitWriters == 0)
	{
		SetEvent(pRwMutex->hReadEvent);
	}

	LeaveCriticalSection(&pRwMutex->lock);

	return RD_SUCCESS;
}



int32 os_proc_sema_init(const char* szSemaName, int32 nInitCount, SemaHandle *pSemaHandle)
{
	int32 nRet = RD_SUCCESS;
	SECURITY_ATTRIBUTES sa;
	SECURITY_DESCRIPTOR sd;

	sa.lpSecurityDescriptor = &sd;
	nRet = os_build_sa(&sa);
	if (RD_SUCCESS != nRet)
	{
		return nRet;
	}

	*pSemaHandle = CreateSemaphoreA(&sa, nInitCount, 0x7FFFFFFF, szSemaName);
	if (*pSemaHandle == NULL)
	{
		os_destory_sa(&sa);
		return (int32)GetLastError();
	}
	os_destory_sa(&sa);
	return RD_SUCCESS;
}


int32 os_proc_sema_destroy(SemaHandle hSema)
{
	if(hSema != INVALID_SEMA_HANDLE)
		CloseHandle(hSema);

	return RD_SUCCESS;
}


int32 os_proc_sema_acquire(SemaHandle hSema)
{
	DWORD dwWaitResult;
	if(hSema == INVALID_SEMA_HANDLE)
		return EC_RD_SEMA_ACQUIRE;
	dwWaitResult = WaitForSingleObject(hSema, INFINITE);
	if(dwWaitResult == WAIT_OBJECT_0)
	{
		return RD_SUCCESS;
	}
	else
	{
		return EC_RD_SEMA_ACQUIRE;
	}
}


int32 os_proc_sema_tryacquire(SemaHandle hSema)
{
	uint32 dwWaitResult;

	if(hSema == INVALID_SEMA_HANDLE)
		return EC_RD_SEMA_TRYACQUIRE;
	dwWaitResult = (uint32)WaitForSingleObject(hSema, 0L);
	if(dwWaitResult == WAIT_OBJECT_0)
	{
		return RD_SUCCESS;
	}
	else
	{
		return EC_RD_SEMA_TRYACQUIRE;
	}
}


int32 os_proc_sema_release(SemaHandle hSema)
{
	if(hSema == INVALID_SEMA_HANDLE)
		return EC_RD_SEMA_RELEASE;
	if (ReleaseSemaphore(hSema, 1, NULL) == FALSE)
		return EC_RD_SEMA_RELEASE;

	return RD_SUCCESS;
}

int32 os_proc_mutex_init(const char* szMutexName, ProcMutexHandle* phMutex)
{
	SECURITY_ATTRIBUTES sa;
	SECURITY_DESCRIPTOR sd;
	int32 nRet;

	assert (RD_SUCCESS == 0); //because GetLastError()

	sa.lpSecurityDescriptor = &sd;
	nRet = os_build_sa(&sa);
	if (RD_SUCCESS != nRet)
	{
		return nRet;
	}

	*phMutex = CreateMutexA(&sa, FALSE/*mutex not owned*/, szMutexName);  
	if (*phMutex == NULL)
	{
		os_destory_sa(&sa);
		return GetLastError(); 
	}
	os_destory_sa(&sa);
	return RD_SUCCESS;
}

int32 os_proc_mutex_acquire(ProcMutexHandle hMutex)
{
	if (hMutex == NULL) 
		return EC_RD_PROC_MUTEX_NULL;
	if ( WaitForSingleObject(hMutex, INFINITE) == WAIT_FAILED)
		return EC_RD_PROC_MUTEX_ACQUIRE;
	return RD_SUCCESS;
}

int32 os_proc_mutex_release(ProcMutexHandle hMutex)
{
	if (ReleaseMutex(hMutex))
	{
		return RD_SUCCESS;
	}
	return EC_RD_PROC_MUTEX_RELEASE;
}

int32 os_proc_mutex_destroy(ProcMutexHandle hMutex)
{
	if (CloseHandle(hMutex))
	{
		return RD_SUCCESS;
	}
	return EC_RD_PROC_MUTEX_DESTROY;
}