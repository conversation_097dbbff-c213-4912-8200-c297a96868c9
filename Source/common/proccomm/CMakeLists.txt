CMAKE_MINIMUM_REQUIRED(VERSION 3.10)

############FOR_MODIFIY_BEGIN#######################
PROJECT (drhdproccomm)
SET(TARGET_NAME drhdProcComm)

INCLUDE($ENV{DRDIR}CMakeCommon)

SET(LIB_TYPE SHARED)

#Setting Source Files
IF (CMAKE_SYSTEM_NAME MATCHES Windows)
	SET(SRCS ${SRCS} hdProcComm.c)	
ELSE (CMAKE_SYSTEM_NAME MATCHES Windows)
	SET(SRCS ${SRCS} hdProcComm_linux.cpp)
ENDIF (CMAKE_SYSTEM_NAME MATCHES Windows)

ADD_DEFINITIONS(-DCV6PROCCOMM_EXPORTS)
ADD_DEFINITIONS(-D_USRDLL)
ADD_DEFINITIONS(-D_CRT_SECURE_NO_WARNINGS)

#Setting Link Librarys
SET(LINK_LIBS ${LINK_LIBS} drhdOS)

############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)
