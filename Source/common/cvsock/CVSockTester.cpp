/**************************************************************
 *  Filename:    CVSockTester.cpp
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: CVSockTester.cpp.
 *
 *  @author:     lijingjing
 *  @version     07/01/2008  lijingjing  Initial Version
**************************************************************/

#include <common/OS.h>
#include "CVSockTester.h"
#include "common/CVNDK.h"
#include "gettext/libintl.h"

#define _(STRING) gettext(STRING)
CPPUNIT_TEST_SUITE_REGISTRATION(CCVSockTester);

#define DEFAULT_PORT	12345

CCVSockTester::CCVSockTester()
{

}

CCVSockTester::~CCVSockTester()
{
}

void CCVSockTester::setUp()
{
	memset(m_szIpAddress, 0, sizeof(m_szIpAddress));
	strcpy(m_szIpAddress, "127.0.0.1");
	m_nPort = DEFAULT_PORT;
	m_pCVSock = new CCVSock(m_szIpAddress, m_nPort, OnRecvCallback, NULL, NULL);
}

void CCVSockTester::tearDown()
{
	if (m_pCVSock != NULL)
	{
		delete m_pCVSock;
		m_pCVSock = NULL;
	}

	CVNDK_Finalize();
}

void CCVSockTester::testSetGetConnectStatus()
{
	printf("testSetGetConnectStatus\n");

	m_pCVSock->SetConnectStatus(DRV_CONNECT_FAILED);
	CPPUNIT_ASSERT(m_pCVSock->GetConnectStatus() == DRV_CONNECT_FAILED);
	m_pCVSock->SetConnectStatus(DRV_CONNECTED);
	CPPUNIT_ASSERT(m_pCVSock->GetConnectStatus() == DRV_CONNECTED);
	m_pCVSock->SetConnectStatus(DRV_CONNECTING);
	CPPUNIT_ASSERT(m_pCVSock->GetConnectStatus() == DRV_CONNECTING);
	m_pCVSock->SetConnectStatus(DRV_NOT_CONNECTED);
	CPPUNIT_ASSERT(m_pCVSock->GetConnectStatus() == DRV_NOT_CONNECTED);	
}

void CCVSockTester::testConnDisConnServer()
{
	printf("testConnDisConnServer\n");

	long nStatus = ICV_SUCCESS;
	int nConnectStatus = DRV_NOT_CONNECTED;

	m_pCVSock->ConnectServer();
	ACE_OS::sleep(3);
	nConnectStatus = m_pCVSock->GetConnectStatus();
	CPPUNIT_ASSERT(nConnectStatus == DRV_CONNECT_FAILED);

	nStatus = CVNDK_Init();
	nStatus = CVNDK_Listen(m_nPort);
	
	m_pCVSock->ConnectServer();
	ACE_OS::sleep(1);
	nConnectStatus = m_pCVSock->GetConnectStatus();
	CPPUNIT_ASSERT(nConnectStatus == DRV_CONNECTED);
	
	m_pCVSock->DisConnectServer();
	CPPUNIT_ASSERT(m_pCVSock->GetConnectStatus() == DRV_NOT_CONNECTED);
}

void CCVSockTester::testConnectThread()
{
	printf("testConnectThread\n");
	long nStatus = ICV_SUCCESS;
	
	CPPUNIT_ASSERT(m_pCVSock->GetConnectStatus() == DRV_NOT_CONNECTED);
	
	nStatus = CVNDK_Init();
	nStatus = CVNDK_Listen(m_nPort);

	m_pCVSock->ConnectServer();
	ACE_OS::sleep(1);
	CPPUNIT_ASSERT(m_pCVSock->GetConnectStatus() == DRV_CONNECTED);
}

void CCVSockTester::testShutdown()
{
	printf("testShutdown\n");
	
	long nStatus = ICV_SUCCESS;
	
	nStatus = CVNDK_Init();
	nStatus = CVNDK_Listen(m_nPort);
	
	m_pCVSock->DisConnectServer();
	CPPUNIT_ASSERT(m_pCVSock->GetConnectStatus() == DRV_NOT_CONNECTED);
}

void CCVSockTester::testSendData()
{
	printf("testSendData\n");
	
	long nStatus = ICV_SUCCESS;
	
	nStatus = CVNDK_Init();
	nStatus = CVNDK_Listen(m_nPort);
	
	m_pCVSock->ConnectServer();
	ACE_OS::sleep(1);
	CPPUNIT_ASSERT(m_pCVSock->GetConnectStatus() == DRV_CONNECTED);
	char buf[256];
	strcpy(buf, "send data 1");
	nStatus = m_pCVSock->SendData(buf, strlen(buf) + 1);
	CPPUNIT_ASSERT(nStatus == ICV_SUCCESS);
}

void OnRecvCallback(char *pszRecvBuf, int nRecvBytes, void* pParam)
{
	printf("OnRecvCallback\n");

	pszRecvBuf[nRecvBytes] = '\0';
	printf(_("Message received: %s\n"), pszRecvBuf);
}
