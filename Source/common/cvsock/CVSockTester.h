/**************************************************************
*  Filename:    CVSockTester.h
*  Copyright:   Shanghai Baosight Software Co., Ltd.
*
*  Description: CVSockTester.h.
*
*  @author:     lijingjing
*  @version     07/01/2008  lijingjing  Initial Version
**************************************************************/

#ifndef _CVSOCK_TESTER_H_
#define _CVSOCK_TESTER_H_

#define CPPUNIT_FRIEND_CLASS

#include <cppunit/extensions/HelperMacros.h>
#include "driversdk/CVSock.h"

class CCVSockTester : public CppUnit::TestFixture
{
	CPPUNIT_TEST_SUITE(CCVSockTester);
	CPPUNIT_TEST(testSetGetConnectStatus);
	CPPUNIT_TEST(testConnDisConnServer);
	CPPUNIT_TEST(testConnectThread);
	CPPUNIT_TEST(testShutdown);
	CPPUNIT_TEST(testSendData);
	CPPUNIT_TEST_SUITE_END();
	
public:
	CCVSockTester();
	~CCVSockTester();
	void setUp();
	void tearDown();

	void testSetGetConnectStatus();

	void testConnDisConnServer();		
	void testConnectThread();
	void testShutdown();

	void testSendData();

private:
	CCVSock *m_pCVSock;
	char m_szIpAddress[ICV_HOSTNAMESTRING_MAXLEN];
	int  m_nPort;
};

static void OnRecvCallback(char *pszRecvBuf, int nRecvBytes, void* pParam = NULL);

#endif  // _CVSOCK_TESTER_H_
