cmake_minimum_required(VERSION 2.6)

PROJECT (drsock)

INCLUDE($ENV{DualiCVSRCDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
#Setting Source Files
SET(SRCS ${SRCS} SockHandler.cpp CVSock.cpp CVSockImpl.cpp)
#Setting Target Name (executable file name | library name)
SET(TARGET_NAME drsock)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS ACE drcomm drlog drlogimpl)
IF(HPUX)
SET(LINK_LIBS ${LINK_LIBS} pthread)
ENDIF(HPUX)

############FOR_MODIFIY_END#########################
INCLUDE($ENV{DualiCVSRCDIR}CMakeCommonLib)

