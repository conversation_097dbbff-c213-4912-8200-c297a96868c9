/**************************************************************
 *  Filename:    LogHelper.inl
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Define some Macros to Access CVLog Easily.
 *
 *  @author:     chen<PERSON><PERSON><PERSON>
 *  @version     08/02/2010  chenzhiquan  Initial Version
**************************************************************/

//#ifdef _LOG_HELPER_H_
//#error	Not Compactable with LogHelper.h
//#endif//_LOG_HELPER_H_

#ifndef __CVLOG_H
#error	Include CVLog.h OutOf namespace first!
//#include "CVLog.h"
#endif//__CVLOG_H

#ifndef _LOG_HELPER_INL_
#define _LOG_HELPER_INL_

/*extern CCVLog CVLog;*/

#define LH_SET_LOG_FILE(logObj, X) logObj.SetLogFileNameThread(X)

// USEAGE: LH_LOG((LOG_LEVEL_XXXX, "Log Message %d", n, ...))
#define LH_LOG(logObj, X) logObj.LogMessage X

// USEAGE: LH_DEBUG("Debug Info")
//   Note: CAN NOT USE MULTIPLE ARGS
//	 NOT RECOMMANDED
#define LH_DEBUG(logObj, X) LH_LOG(logObj, (LOG_LEVEL_DEBUG,X))
#define LH_ERROR(logObj, X) LH_LOG(logObj, (LOG_LEVEL_ERROR,X))
#define LH_INFO(logObj, X)  LH_LOG(logObj, (LOG_LEVEL_INFO,X))
#define LH_WARN(logObj, X)  LH_LOG(logObj, (LOG_LEVEL_WARN,X))
#define LH_CRITICAL(logObj, X) LH_LOG(logObj, (LOG_LEVEL_CRITICAL,X))
#define LH_ERROR_CODE(logObj, X) logObj.LogErrMessage X

#endif//_LOG_HELPER_INL_
