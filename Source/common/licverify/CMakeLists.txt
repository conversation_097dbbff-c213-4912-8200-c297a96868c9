cmake_minimum_required(VERSION 3.10)
############FOR_MODIFIY_BEGIN#######################
#Setting Project Name
PROJECT (licverify)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} LicChecker.cpp LicSerializer.cpp EpassLicReader.cpp)

ADD_DEFINITIONS(-DLIC_VERIFY_NO_CV_LICENSE)

#REPEAT SET(SRCS ${SRCS} EpassLicReader.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME licverify)
#Setting library type used when build a library
SET(LIB_TYPE STATIC)

############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonLib)
IF(MSVC)
	if( CMAKE_SIZEOF_VOID_P EQUAL 8 )
		set_target_properties(${TARGET_NAME} PROPERTIES STATIC_LIBRARY_FLAGS "/machine:x64")
	endif( CMAKE_SIZEOF_VOID_P EQUAL 8 )
ENDIF(MSVC)
