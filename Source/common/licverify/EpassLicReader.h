#ifndef CV_EPASS_LIC_READER_H_

#define CV_EPASS_LIC_READER_H_

#include "common/LicSerializer.h"
#include "beagle/license.h"

class CEpassLicReader : public CLicSerializer
{
public:
	CEpassLicReader(const string &strFileName);
	
	virtual long ReadLicData();
	
	virtual long SaveLicData();; // Not Support

	virtual bool HostIDEnable();
	long GetExtendedAttr(const char* szKey, std::string& strValue);
protected:
	bool IsHardwareLic(LM_HANDLE handle);
private:
	//bool CEpassLicReader::LoadEpassLicense( const string &strEpassLicFileName )
	bool m_bNeedHostID;
};

#endif//CV_EPASS_LIC_READER_H_

