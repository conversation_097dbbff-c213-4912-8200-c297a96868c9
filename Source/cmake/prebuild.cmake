#设置环境变量
function(set_env)
    # 获取项目目录
    get_filename_component(PROJECT_DIR "${CMAKE_SOURCE_DIR}" DIRECTORY)

    #设置环境变量
    set(ENV{DRDIR} "${PROJECT_DIR}/Source/")
    set(ENV{LD_LIBRARY_PATH} "${PROJECT_DIR}/executable:${PROJECT_DIR}/library:$ENV{LD_LIBRARY_PATH}")
    
    # 输出状态信息
    # message("dr dir: $ENV{DRDIR}" )
    # message("dr library: $ENV{LD_LIBRARY_PATH}")
    message(STATUS "environment setting completed")
endfunction()

#获取操作系统
function(get_os_name OS_NAME)
    # 初始化默认值
    set(MYREPO "unknown")

    # 检查/etc/os-release文件是否存在
    if(EXISTS "/etc/os-release")
        # 读取文件内容
        file(READ "/etc/os-release" OS_RELEASE_CONTENT)

        # 使用正则表达式提取ID值
        string(REGEX MATCH "ID[\t ]*=[\t ]*\"?([^\"\n]+)" MATCHED ${OS_RELEASE_CONTENT})
        if(CMAKE_MATCH_1)
            set(OS_ID "${CMAKE_MATCH_1}")
            string(TOLOWER "${OS_ID}" OS_ID)  # 转换为小写确保匹配

            # 根据ID值判断发行版
            if(OS_ID STREQUAL "ubuntu")
                set(MYREPO "ubuntu20_4")
            elseif(OS_ID STREQUAL "rhel" OR OS_ID STREQUAL "centos")
                set(MYREPO "redhat7_8")
            endif()
        endif()
    endif()

    # 返回结果
    set(${OS_NAME} "${MYREPO}" PARENT_SCOPE)
endfunction()

#解压第三方库
function(extract_repo_archives)
    # 获取系统名称
    get_os_name(MYREPO)
    # 获取项目目录
    get_filename_component(PROJECT_DIR "${CMAKE_SOURCE_DIR}" DIRECTORY)

    # 构造仓库目录
    set(REPO_PATH "${PROJECT_DIR}/Repo/${MYREPO}")

    # 检查仓库目录是否存在
    if(NOT EXISTS "${REPO_PATH}")
        message(FATAL_ERROR "Can't find Repo directory: ${REPO_PATH}")
        return()
    endif()

    # 查找所有tar.gz文件（最多搜索3层目录）
    file(GLOB_RECURSE TAR_FILES 
        LIST_DIRECTORIES false
        RELATIVE "${REPO_PATH}"
        "${REPO_PATH}/*.tar.gz"
    )

    # 解压
    if(NOT TAR_FILES)
        message(WARNING "No .tar.gz files found in ${REPO_PATH}")
    else()
        foreach(TAR_FILE IN LISTS TAR_FILES)
            # 获取原始文件的绝对路径
            get_filename_component(FILE_PATH "${REPO_PATH}/${TAR_FILE}" ABSOLUTE)

            # 执行解压操作
            execute_process(
                COMMAND ${CMAKE_COMMAND} -E tar xzf "${FILE_PATH}"
                WORKING_DIRECTORY "${PROJECT_DIR}"
                OUTPUT_QUIET
                ERROR_QUIET
                RESULT_VARIABLE result
            )

            if(NOT result EQUAL 0)
                message(FATAL_ERROR "Failed to extract ${FILE_PATH}")
            endif()
        endforeach()
    endif()

    # 输出状态信息
    message(STATUS "tar completed")
endfunction()

#构建前准备
function(prebuild)
    #设置环境变量
    # set_env()

    #解压第三方库
    extract_repo_archives()
endfunction()