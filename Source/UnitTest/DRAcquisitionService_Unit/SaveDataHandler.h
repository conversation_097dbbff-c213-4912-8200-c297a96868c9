/**************************************************************
 *  Filename:    
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: 
 *
 *  @author:     yang<PERSON>
 *  @version     09/22/2018  yangqi  Initial Version
**************************************************************/

#if !defined(PDB_SAVE_DATA_HANDER_H_INCLUDED_)
#define PDB_SAVE_DATA_HANDER_H_INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include <ace/Null_Mutex.h>
#include <ace/Singleton.h>
#include "ace/Task.h"
#include "common/CVNDK.h"
#include "common/SimpleQueue.h"
#include "ace/Message_Block.h"
#include "proto/proto_driverapi_pdb.h"
#include "NetWrapper.h"

class CSaveDataHandler  : public ACE_Task_Base
{
friend class ACE_Singleton<CSaveDataHandler, ACE_Thread_Mutex>;
public:
	CSaveDataHandler(CNetWrapper* netWrapper);
	virtual ~CSaveDataHandler();

public:
	virtual int svc(void);
	long Activate();
	void ShutDown();

public:
	bool m_bExit;
	CNetWrapper* m_netWrapper;
};


#endif // !defined(PDB_SAVE_DATA_HANDER_H_INCLUDED_)
