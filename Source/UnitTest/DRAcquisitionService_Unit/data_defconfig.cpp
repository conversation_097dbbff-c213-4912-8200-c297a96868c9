#include "data_defconfig.h"
// #include <glog/logging.h>


std::shared_ptr<data_defconfig> data_defconfig::instance;
std::once_flag data_defconfig::initFlag;

const std::shared_ptr<Object> data_defconfig::getGlobalModel() const
{
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return GlobeObjects;
}
std::shared_ptr<data_defconfig> data_defconfig::getInstance(const std::string &filePath)
{
    if ("" == filePath)
    {
        // LOG(ERROR) << "No config file path provided, using default.";
        throw std::invalid_argument("Config file path cannot be empty.");
    }

    std::call_once(initFlag, [&]
                   { instance.reset(new data_defconfig(filePath)); });
    return instance;
}

data_defconfig::~data_defconfig()
{
}
data_defconfig::data_defconfig(const std::string &filePath)
{
    parsedata_defconfig(filePath);
}

void data_defconfig::parsedata_defconfig(const std::string &filePath)
{

    XMLDocument doc;
    XMLError result = doc.LoadFile(filePath.c_str());
    if (result != XML_SUCCESS)
    {
        // LOG(ERROR) << "Failed to load parsedata_defconfig file: " << filePath << " Error loading XML file: " << doc.ErrorName();
        return;
    }

    XMLElement *rootElement = doc.RootElement();
    if (rootElement == nullptr || std::strcmp(rootElement->Name(), "Data") != 0)
    {
        // LOG(ERROR) << "Invalid parsedata_defconfig file: " << filePath;
        return;
    }

    parseObject(rootElement, GlobeObjects);
}

void data_defconfig::parseObject(XMLElement *element, std::shared_ptr<Object> parentObj)
{
    for (XMLElement *objectElem = element->FirstChildElement("Object"); objectElem != nullptr; objectElem = objectElem->NextSiblingElement("Object"))
    {
        const char *name = objectElem->Attribute("name");
        const char *refmodel = objectElem->Attribute("refmodel");
        const char *desc = objectElem->Attribute("desc");

        if (!name)
        {
            std::cerr << "Error: Missing 'name' attribute in <Object> tag." << std::endl;
            continue;
        }

        auto obj = std::make_shared<Object>(name, desc ? desc : "");
        obj->refModel = refmodel ? refmodel : "";

        for (XMLElement *refElem = objectElem->FirstChildElement("Reference"); refElem != nullptr; refElem = refElem->NextSiblingElement("Reference"))
        {
            const char *tagName = refElem->Attribute("name");
            const char *tagDesc = refElem->Attribute("desc");
            const char *linkTag = refElem->Attribute("linkTag");
            int linkTagId = refElem->IntAttribute("linkTagId");
            const char *linkObject = refElem->Attribute("linkObject");

            if (linkObject)
            {

                obj->linkObjName = linkObject;
            }
            else
            {

                auto tag = std::make_shared<Tag>(
                    tagName ? std::string(tagName) : "",
                    "type",
                    tagDesc ? std::string(tagDesc) : "",
                    "",
                    linkTag ? std::string(linkTag) : "",
                    linkTagId);
                obj->addTag(tagName ? std::string(tagName) : "", tag);
            }
        }

        parentObj->addObject(name, obj);

        parseObject(objectElem, obj);
    }
}

void data_defconfig::resolveLinkObjects(std::shared_ptr<Object> rootObj)
{

    for (const auto &[name, obj] : rootObj->Objects)
    {
        if (!obj->linkObjName.empty())
        {
            auto linkedObj = rootObj->Objects[obj->linkObjName];
            if (linkedObj)
            {
                obj->addObject(obj->linkObjName, linkedObj);
            }
            else
            {
                std::cerr << "Error: linkObject '" << obj->linkObjName << "' not found." << std::endl;
            }
        }

        resolveLinkObjects(obj);
    }
}

void data_defconfig::parseData(XMLElement *rootElement)
{
    std::unique_lock<std::shared_mutex> lock(m_mutex);
    std::shared_ptr<Object> GlobeObjects = std::make_shared<Object>("root", "root");

    parseObject(rootElement, GlobeObjects);

    resolveLinkObjects(GlobeObjects);
}
