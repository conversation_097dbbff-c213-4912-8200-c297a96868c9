/**
 * @file config.h
 * @brief
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2024-08-27
 *
 * @copyright Copyright (c) 2024  by  ����
 *
 * @par �޸���־: ���ݶ��������ļ�������  ����Ϊ����ģʽ
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2024-08-27     <td>1.0     <td>wwk   <td>�޸�?
 * </table>
 */

#pragma once

#include <iostream>
#include <memory>
#include <string>
#include <mutex>
#include <vector>
#include <string>
#include <unordered_map>
#include "tinyxml2.h"
#include "data_tag.h"
#include "data_object.h"
#include <shared_mutex>

using namespace tinyxml2;

class data_defconfig
{
public:
    static std::shared_ptr<data_defconfig> getInstance(const std::string &filePath = "");
    ~data_defconfig();
    const std::shared_ptr<Object> getGlobalModel() const;

private:
    data_defconfig(const std::string &filePath);

    void parsedata_defconfig(const std::string &filePath);

    void parseObject(XMLElement *element, std::shared_ptr<Object> parentObj);

    void resolveLinkObjects(std::shared_ptr<Object> rootObj);

    void parseData(XMLElement *rootElement);

    static std::shared_ptr<data_defconfig> instance;

    static std::once_flag initFlag;

    mutable  std::shared_mutex m_mutex; 
    std::shared_ptr<Object> GlobeObjects = std::make_shared<Object>("root", "root");
};
