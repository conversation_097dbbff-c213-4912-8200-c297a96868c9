#include "data_localvariables.h"
// #include <glog/logging.h>

std::shared_ptr<data_localvariables> data_localvariables::instance;
std::once_flag data_localvariables::initFlag;

std::shared_ptr<data_localvariables> data_localvariables::getInstance(const std::string &filePath)
{
    if ("" == filePath)
    {
        // LOG(ERROR) << "No config file path provided, using default.";
        throw std::invalid_argument("Config file path cannot be empty.");
    }

    std::call_once(initFlag, [&]()
                   { instance.reset(new data_localvariables(filePath)); });
    return instance;
}

data_localvariables::~data_localvariables() {
};

const std::vector<std::string> &data_localvariables::get_local_variables() const
{
    std::shared_lock<std::shared_mutex> lock(m_mutex);
    return local_variables;
}

data_localvariables::data_localvariables(const std::string &filePath)
{
    parsedata_localvariables(filePath);
}

void data_localvariables::parsedata_localvariables(const std::string &filePath)
{
    XMLDocument doc;
    XMLError result = doc.LoadFile(filePath.c_str());
    if (result != XML_SUCCESS)
    {
        // LOG(ERROR) << "Failed to load parsedata_localvariables file: " << filePath << " Error loading XML file: " << doc.ErrorName();
        return;
    }

    XMLElement *root = doc.FirstChildElement("variables");
    std::unique_lock<std::shared_mutex> lock(m_mutex);
    if (root)
    {
        for (XMLElement *var = root->FirstChildElement("var"); var != nullptr; var = var->NextSiblingElement("var"))
        {
            const char *varName = var->Attribute("name");
            if (varName)
            {   
                
                local_variables.push_back(varName);
            }
        }
    }
}


void data_localvariables::print() const
{
    for (const auto& name : local_variables) {
        std::cout << "Variable name: " << name << std::endl;
    }
}