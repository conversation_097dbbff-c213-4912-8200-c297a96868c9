<?xml version="1.0" encoding="utf-8" ?>
<variables>
    <var name="tag1" type="DINT" desc="" unit="" linkTag="sim_0" linkTagId="2001" />
    <var name="tag2" type="DINT" desc="" unit="" linkTag="sim_1" linkTagId="2002" />
    <var name="tag3" type="DINT" desc="" unit="" linkTag="sim_2" linkTagId="2003" />
<!-- 本地变量可以不与驱动信息关联，并且可以引用对象数据，对象数据保存在DataDefinition.xml中，引用对象数据时，type为Object -->
    <var name="localvar" type="REAL" desc="" unit="" />
    <var name="object1" type="Object" />
<!-- 支持数组，arrayDimensions，最多3层嵌套 -->
    <var name="localvar" type="REAL" arrayDimensions="2,3,4" desc="" unit="" />
</variables>