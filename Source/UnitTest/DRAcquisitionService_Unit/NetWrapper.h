/**************************************************************
 *  Filename:    
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: 
 *
 *  @author:     ya<PERSON><PERSON>
 *  @version     09/22/2018  yangqi  Initial Version
**************************************************************/

#if !defined(PDB_NETWRAPPER_H_INCLUDED_)
#define PDB_NETWRAPPER_H_INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include <ace/Null_Mutex.h>
#include <ace/Singleton.h>
#include "ace/Task.h"
#include "common/CVNDK.h"
#include "common/SimpleQueue.h"
#include "ace/Message_Block.h"
#include "proto/proto_driverapi_pdb.h"
#include <map>
#include <string>


class CNetWrapper  : public ACE_Task_Base
{
friend class ACE_Singleton<CNetWrapper, ACE_Thread_Mutex>;
public:
	CNetWrapper();
	virtual ~CNetWrapper();

public:
	CSimpleThreadQueue<ACE_Message_Block*> m_dataQueue;

public:
	long InitializeNetwork();
	long UnInitializeNetwork();
	long SendCtrlToDriver(const char* szDriverName, const TProtoDriverAPICTRLMsg& ctrlMsg);
	bool IsDriverRegister(const char* szDriverName);
	static void* ThreadRegDirver(void* pArg);
	virtual int svc(void);
	long Activate();
	void ShutDown();

public:
	std::map<std::string, HQUEUE>m_mapDriverName2CliQueue;
	bool m_bExit;
	HQUEUE m_hLocalQueRecvData;
	HQUEUE m_hLocalQueRegDriver;	
	ACE_thread_t m_nThreadID;
	ACE_hthread_t m_hThreadHandle;
};

typedef ACE_Singleton<CNetWrapper, ACE_Thread_Mutex> CNetWrapperSingleton;


#endif // !defined(PDB_NETWRAPPER_H_INCLUDED_)
