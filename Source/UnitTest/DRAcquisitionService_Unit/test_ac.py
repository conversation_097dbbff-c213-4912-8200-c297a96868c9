import pytest
import time
import pdb
import os
import logging
import subprocess
import redis

# Import your functions
from allure_setup import *

# Example Jira key
JIRA_KEY = "DSFR-67"

# 定义相对路径
relative_path = os.path.join(os.path.dirname(__file__), "../../executable")

# 打印路径
logging.info(f'simulateDrv path: {os.path.join(relative_path, "simulateDrv")}')
logging.info(f'dracquisitionService path: {os.path.join(relative_path, "dracquisitionservice")}')

# 连接 Redis
try:
    redis_client = redis.StrictRedis(host='127.0.0.1', port=6380, db=0)
    # 检验连接是否正确
    redis_client.ping()
    logging.info("Connected to Redis successfully")
except redis.ConnectionError as e:
    logging.error(f"Failed to connect to Redis: {e}")
    raise

# 启动 simulateDrv 和 dracquisitionService 进程
dracquisition_service_process = subprocess.Popen(["./dracquisitionservice"], cwd=relative_path)

# 等待2秒
time.sleep(2)

simulate_drv_process = subprocess.Popen(["./simulateDrv"], cwd=relative_path)

# 再次等待2秒，确保进程完全启动
time.sleep(2)
@allure_setup("DSFR-67", is_async=False)
def test_jira_summary():
    # 从 Redis 中获取 STD.TEST_DINT 的值
    binary_value = redis_client.get('STD.TEST_DINT')
    if binary_value is None:
        logging.error("Failed to get STD.TEST_DINT from Redis")
        assert False, "Failed to get STD.TEST_DINT from Redis"

    # 将二进制值转换为十进制
    decimal_value = int.from_bytes(binary_value, byteorder='little')

    print(f"STD.TEST_DINT value: {decimal_value}")

    # 断言值是否为 7
    assert decimal_value == 7, f"Expected 7, but got {decimal_value}"

    
def teardown_module(module):
    simulate_drv_process.terminate()
    dracquisition_service_process.terminate()


# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_ac.py"])