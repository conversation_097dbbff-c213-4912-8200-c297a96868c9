#include "DSFConfig.h"
#include <iostream>
#include "common/cvcomm.hxx"
#include "ace/OS.h"


// DSFConfig类的构造函数
DSFConfig::DSFConfig()
{
}

DSFConfig::~DSFConfig()
{
}


/**
 * 向配置中添加一个对象
 * 
 * 此函数负责将一个DSFObject指针添加到配置的内部存储中。在添加之前，它会检查指针是否为NULL，并且检查是否已经存在具有相同名称的对象。如果存在同名对象，则不会添加新对象，并且会释放传入的指针。
 * 
 * @param obj 要添加到配置中的DSFObject指针
 * @return bool 表示对象是否被成功添加。true表示成功添加，false表示添加失败（如对象指针为NULL或同名对象已存在）
 * <AUTHOR>
 * @version 1.0
 */
bool DSFConfig::addObject(DSFObject* obj)
{
    // 检查传入的对象指针是否为NULL，如果是，则直接返回false
    if(obj == NULL)
        return false;

    // 检查是否已存在具有相同名称的对象
    if(m_objects.find(obj->getName()) != m_objects.end())
    {
        // 如果存在同名对象，输出错误信息，释放当前对象指针，并返回false
        std::cout<<"Object with name "<<obj->getName()<<" already exists"<<std::endl;
        delete obj;
        return false;
    }
    else
    {
        // 如果不存在同名对象，则将对象添加到集合中，并返回true
        m_objects[obj->getName()] = obj;
        return true;
    }
}

/**
 * 根据名称获取配置对象
 * 
 * 本函数尝试从已存储的对象中查找给定名称的对象如果找到，则返回该对象的指针；
 * 否则返回NULL这提供了一种根据名称安全访问配置对象的方式，避免了直接访问的潜在错误
 * 
 * @param name 要获取的配置对象的名称
 * @return DSFObject* 返回找到的配置对象的指针，如果未找到则返回NULL
 * <AUTHOR>
 * @version 1.0
 */
DSFObject* DSFConfig::getObject(std::string name)
{
    // 检查名为name的对象是否存在于m_objects中
    if(m_objects.find(name) == m_objects.end())
        return NULL;
    else
        return m_objects[name];
}


/**
 * 向配置中添加一个变量
 * 
 * 此函数尝试将一个DSFVariable指针添加到本地变量集合中如果变量已经存在，则打印错误消息并删除该指针
 * 
 * @param var 要添加的DSFVariable指针
 * @return 如果变量成功添加则返回true，否则返回false
 */
bool DSFConfig::addVariable(DSFVariable* var)
{
    // 检查传入的变量指针是否为空
    if(var == NULL)
    {
        // 如果为空，则返回false，表示添加失败
        return false;
    }

    // 检查本地变量集合中是否已存在同名变量
    if(m_localVariables.find(var->getName())!=m_localVariables.end())
    {
        // 如果存在同名变量，则打印错误消息
        std::cout<<"Variable with name "<<var->getName()<<" already exists"<<std::endl;
        // 删除传入的变量指针以避免内存泄漏
        delete var;
        // 返回false，表示添加失败
        return false;
    }
    else
    {
        // 如果不存在同名变量，则将变量添加到本地变量集合中
        m_localVariables[var->getName()] = var;
        // 返回true，表示添加成功
        return true;
    }
}

/**
 * 根据名称获取变量
 * 
 * 本函数尝试从配置中找到指定名称的变量如果找到，则返回该变量的指针；
 * 如果未找到，则返回NULL此函数用于根据变量名称检索配置中的变量信息
 * 
 * @param name 要获取的变量的名称
 * @return DSFVariable* 返回找到的变量的指针，如果未找到则返回NULL
 */
DSFVariable* DSFConfig::getVariable(std::string name)
{
    // 检查本地变量映射中是否存在指定名称的变量
    if(m_localVariables.find(name)!=m_localVariables.end())
        return m_localVariables[name];
    else
        return NULL;
}

void DSFConfig::printAllObjects()
{
    std::map<std::string, DSFObject*>::iterator it;
    for(it = m_objects.begin(); it != m_objects.end(); it++)
    {
        it->second->print();
        std::cout<<"--------------------------------------------------"<<std::endl;
    }
}

 void DSFConfig::printAllVariables()
 {
    std::map<std::string, DSFVariable*>::iterator it;
    for(it = m_localVariables.begin(); it != m_localVariables.end(); it++)
    {
        std::cout<<"Variable: "<<it->first<<std::endl;
    }
 }


DSF_VARIABLE_TYPE DSFConfig::getVariableType(const std::string& type_str)
{
    DSF_VARIABLE_TYPE type;

    if (type_str == "BOOL")
    {
        type = DSF_BOOL;
    }
    else if (type_str == "BYTE")
    {
        type = DSF_BYTE;
    }
    else if (type_str == "CHAR")
    {
        type = DSF_CHAR;
    }
    else if (type_str == "DWORD")
    {
        type = DSF_DWORD;
    }
    else if (type_str == "INT")
    {
        type = DSF_INT;
    }
    else if (type_str == "LINT")
    {
        type = DSF_LINT;
    }
    else if (type_str == "LREAL")
    {
        type = DSF_LREAL;
    }
    else if (type_str == "LWORD")
    {
        type = DSF_LWORD;
    }
    else if (type_str == "REAL")
    {
        type = DSF_REAL;
    }
    else if (type_str == "SINT")
    {
        type = DSF_SINT;
    }
    else if (type_str == "STRING")
    {
        type = DSF_STRING;
    }
    else if (type_str == "UDINT")
    {
        type = DSF_UDINT;
    }
    else if (type_str == "UDT")
    {
        type = DSF_UDT;
    }
    else if (type_str == "UINT")
    {
        type = DSF_UINT;
    }
    else if (type_str == "ULINT")
    {
        type = DSF_ULINT;
    }
    else if (type_str == "USINT")
    {
        type = DSF_USINT;
    }
    else if (type_str == "WORD")
    {
        type = DSF_WORD;
    }
    else if (type_str == "DINT")
    {
        type = DSF_DINT;
    }
    else
    {
        type = DSF_UNKNOWN;
    }
    
    return type;
}


 void DSFConfig::parseVariables(const char* filename) {
    XMLDocument doc;
    XMLError eResult = doc.LoadFile(filename);
    if (eResult != XML_SUCCESS) {
        std::cerr << "Failed to load XML file: " << filename << std::endl;
        return;
    }

    XMLElement* root = doc.FirstChildElement("variables");
    if (!root) {
        std::cerr << "No 'variables' element found." << std::endl;
        return;
    }

    for (XMLElement* varElem = root->FirstChildElement("var"); varElem != nullptr; varElem = varElem->NextSiblingElement("var")) 
    {
        if(varElem->Attribute("linkTag") == nullptr)
        {
            continue;
        }

        DSF_VARIABLE_TYPE type = getVariableType(varElem->Attribute("type"));
        if(type == DSF_UNKNOWN)
            continue;
        DSFVariable* var = new DSFVariable(varElem->Attribute("name"),type,atoi(varElem->Attribute("linkTagId")));
        addVariable(var);
    }
}


void DSFConfig::readObject(const XMLDocument& doc, const XMLElement* parentElement, DSFObject* parentObject,std::map<std::string, bool> objectIsEnabledMap) 
{
    for (const XMLElement* elem = parentElement->FirstChildElement(); elem != nullptr;elem = elem->NextSiblingElement() ) 
    {
        if (elem->Name() == std::string("Object")) 
        {
            //先看看是否已经读取过了，对象在配置文件中会有重复
            if(getObject(elem->Attribute("name")) == nullptr)
            {
                DSFObject* object = new DSFObject(elem->Attribute("name"));
                addObject(object);
                objectIsEnabledMap[object->getName()] = true;
                // 递归处理子元素
                readObject(doc, elem,object,objectIsEnabledMap);
            }
        } 
        else if (elem->Name() == std::string("Reference")) 
        {
            if(parentObject!=NULL)
            {
                if(elem->Attribute("linkObject")==NULL)
                {
                    const char* name = elem->Attribute("name");
                    if(getVariable(name)!=NULL)
                    {
                        //只要又一个变量不是本地的，这个对象就是无效的
                        if(getVariable(name)==NULL)
                        {
                            objectIsEnabledMap[parentObject->getName()] = false;
                        }
                        else
                        {
                            parentObject->addVariable(getVariable(name));
                        }
                    }
                }
            }
            
        }
    }
}


void DSFConfig::makeObjectTree(const XMLDocument& doc, const XMLElement* parentElement,DSFObject* parentObject, std::set<std::string>& completedObjectNames) 
{
    for (const XMLElement* elem = parentElement->FirstChildElement(); elem != nullptr;elem = elem->NextSiblingElement()) 
    {
        if (elem->Name() == std::string("Object")) 
        {
            if(parentObject != nullptr)
            {
                parentObject->addSubObject(getObject(elem->Attribute("name")));
            }

            if(completedObjectNames.find(elem->Attribute("name"))==completedObjectNames.end())
            {
                makeObjectTree(doc, elem, getObject(elem->Attribute("name")),completedObjectNames);
                //将子对象设置成完成建立对象树的状态
                completedObjectNames.insert(elem->Attribute("name"));
            }
        } 
        else if (elem->Name() == std::string("Reference")) 
        {
            if(elem->Attribute("linkObject")!=NULL)
            {
                if(parentObject != nullptr)
                {
                    parentObject->addSubObject(getObject(elem->Attribute("linkObject")));
                }
            }
        }
    }
}

void DSFConfig::parseObjects(const char* filename)
{
    XMLDocument doc;
    
    XMLError eResult = doc.LoadFile(filename);
    if (eResult != XML_SUCCESS) {
        cerr << "Failed to load XML file." << endl;
        return;
    }

    XMLElement* root = doc.FirstChildElement("Data");
    if (!root) {
        cerr << "Failed to find root element 'Data'." << endl;
        return;
    }

    std::map<std::string, bool> objectIsEnabledMap;
    readObject(doc, root, nullptr, objectIsEnabledMap);

    std::set<std::string> completedObjectNames;
    makeObjectTree(doc, root, nullptr, completedObjectNames);

    // 删除所有无效的对象
    for(auto& it: objectIsEnabledMap)
    {
        if(!it.second)
            m_objects.erase(it.first);
    }
}

void DSFConfig::clearConfig()
{
    for(auto it = m_objects.begin(); it != m_objects.end(); ++it)
    {
        delete it->second;
    }
    m_objects.clear();

    for(auto it = m_localVariables.begin(); it != m_localVariables.end(); ++it)
    {
        delete it->second;
    }
    m_localVariables.clear();
}



bool DSFConfig::loadConfig()
{
    clearConfig();
    std::cout<<"Loading config file..."<<std::endl;

    std::string m_strCfgFile = CVComm.GetCVProjCfgPath();
    std::cout<<m_strCfgFile<<std::endl;

    std::string strLocalVariablesFile = m_strCfgFile+ACE_DIRECTORY_SEPARATOR_STR+DSF_XML_DEFINE_LOCAL_VARIABLES;
    std::cout<<strLocalVariablesFile<<std::endl;

    parseVariables(strLocalVariablesFile.c_str());

    std::string strObjectsFile = m_strCfgFile+ACE_DIRECTORY_SEPARATOR_STR+DSF_XML_DEFINE_DATA_DEFINITION;
    std::cout<<strObjectsFile<<std::endl;

    printAllVariables();

    parseObjects(strObjectsFile.c_str());

    printAllObjects();

    return true;
}