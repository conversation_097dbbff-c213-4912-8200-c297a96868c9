#pragma once
#include <iostream>
#include <string>
#include <unordered_map>
#include "data_tag.h"

class Object
{

public:
    friend class data_defconfig;
    Object(const std::string &name, const std::string &desc = "")
        : name(name), desc(desc) {}

    void addTag(const std::string &name, const std::shared_ptr<Tag> &tag)
    {
        tags[name] = tag;
    }

    void addObject(const std::string &name, const std::shared_ptr<Object> &model)
    {
        Objects[name] = model;
    }

    void printObject(int level = 0)
    {
        std::string indent(level * 2, ' ');
        std::string subIndent = indent + "  ";

        std::cout << indent << "  Model: " << name << " (Description: " << desc << ")" << std::endl;
        std::cout << indent << "  Ref Model: " << refModel << "\n";
        std::cout << indent << "  linkObjName: " << linkObjName << "\n";
        std::cout << indent << "  desc: " << desc << "\n";
        for (const auto &tag : tags)
        {
            std::cout << indent << "  Tag Name: " << tag.second->getName() << "\n";
            std::cout << indent << "  Type: " << tag.second->getType() << "\n";
            std::cout << indent << "  Description: " << tag.second->getDesc() << "\n";
            std::cout << indent << "  Unit: " << tag.second->getUnit() << "\n";
            std::cout << indent << "  Link Tag: " << tag.second->getLinkTag() << "\n";
            std::cout << indent << "  Link Tag ID: " << tag.second->getLinkTagId() << "\n";
        }

        for (const auto &model : Objects)
        {
            model.second->printObject(level + 1);
        }
    }

private:
    std::string name;
    std::string refModel;
    std::string linkObjName;
    std::string desc;
    std::unordered_map<std::string, std::shared_ptr<Object>> Objects;
    std::unordered_map<std::string, std::shared_ptr<Tag>> tags;
};
