#include "DSFObject.h"
#include <iostream>
#include <vector>
#include <memory.h>
DSFObject::DSFObject(std::string name)
: m_name(name),m_alignment(4)
{
}

DSFObject::~DSFObject()
{
}

std::string DSFObject::getName()
{
	return m_name;
}

bool DSFObject::addVariable(DSFVariable* var)
{
    if(var==NULL)
        return false;
    
    //如果找不到就添加新变量
    if(m_variables.find(var->getName())==m_variables.end())
    {
	    m_variables[var->getName()]=var;
        return true;
    }
    else
    {
        delete var;
        return false;
    }    
}

DSFVariable* DSFObject::getVariable(std::string name)
{
    if(m_variables.find(name)!=m_variables.end())
    {
        return m_variables[name];
    }
    else
    {
        return NULL;
    }
}

void DSFObject::print()
{
    std::cout << "Object: " << m_name << std::endl;
    for(std::map<std::string, DSFVariable*>::iterator it=m_variables.begin(); it!=m_variables.end(); it++)
    {
        (*it).second->print();
    }
    for(std::map<std::string, DSFObject*>::iterator it=m_objects.begin(); it!=m_objects.end(); it++)
    {
        (*it).second->print();
    }
}

bool DSFObject::addSubObject(DSFObject* obj)
{
    if(obj==NULL)
        return false;
    
    if(m_objects.find(obj->getName())==m_objects.end())
    {
        m_objects[obj->getName()] = obj;
        return true;
    }
    else
    {
        delete obj;
        return false;
    }
}

//修改
//222->6
//242->10
int DSFObject::calculateSizeWithAlignment() {
    int current_offset = 0;  // 当前偏移量
    int max_alignment = 1;   // 最大对齐要求
    int total_size = 0;      // 结构体总大小

    for(auto& v:m_variables)
    {
        if(v.second->getVariableSize()<m_alignment)
        {
            current_offset+=m_alignment;
        }
        else
        {
            //如果遇到了大于等于4的变量，自动补齐
            if(current_offset%4!=0)
                current_offset = (current_offset /4)*4+4;
            current_offset+=v.second->getVariableSize();
        }
        cout << current_offset << endl;
    }
    return current_offset;
}


//FIXME:需要测试
void DSFObject::getObjectValue(DSFOUT char* value)
{
    char * p = value;
    for(auto& v:m_variables)
    {
        memcpy(p,v.second->getValue(),v.second->getVariableSize());
        p+=v.second->getVariableSize();
    }
    for(auto& v:m_objects)
    {
        getObjectValue(p);
        p+=v.second->getObjectSize();
    }
}

int DSFObject::getObjectSize()
{
    int current_offset = calculateSizeWithAlignment();

    if(current_offset%4!=0)
    {
        current_offset= (current_offset%4)*4+4;
    }

    for(auto& o:m_objects)
    {
        current_offset+=o.second->getObjectSize();
    }
    return current_offset;
}

 void DSFObject::setAlignment(int alignment)
 {
    m_alignment = alignment;
 }