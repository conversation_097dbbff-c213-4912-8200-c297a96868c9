
#pragma once

#include "tinyxml2.h"
#include <string>
#include <shared_mutex>
#include <mutex>
#include <iostream>
#include <memory>
#include <vector>
#include <unordered_map>


using namespace tinyxml2;


class data_localvariables
{
public:
    static std::shared_ptr<data_localvariables> getInstance(const std::string &filePath = "");
    const std::vector<std::string> &get_local_variables() const;
    void print() const;
    ~data_localvariables();

private:
    data_localvariables(const std::string &filePath);

    void parsedata_localvariables(const std::string &filePath);

    void parseData(XMLElement *rootElement);

    static std::shared_ptr<data_localvariables> instance;

    std::vector<std::string> local_variables;
    mutable  std::shared_mutex m_mutex; 
    static std::once_flag initFlag;
};
