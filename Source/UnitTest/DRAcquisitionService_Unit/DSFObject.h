#pragma once
#include <string>
#include <map>
#include "DSFVariable.h"

#define DSFOUT
#define DSFIN

class DSFObject
{
public:
    DSFObject(std::string name);
    ~DSFObject();

    std::string getName();

    bool addVariable(DSFVariable* var);
    DSFVariable* getVariable(std::string name);

    bool addSubObject(DSFObject* obj);

    void print();

    void getObjectValue(DSFOUT char* value);

    int getObjectSize();

    void setAlignment(int alignment);

public:
    int calculateSizeWithAlignment();

public:
    std::string m_name;//对象中唯一
    int m_alignment;//对象对齐大小
    std::map<std::string,DSFVariable*> m_variables;//所有单个变量
    std::map<std::string,DSFObject*> m_objects;//所有子对象
};
