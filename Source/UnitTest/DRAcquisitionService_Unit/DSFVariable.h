#pragma once
#include <string>
using namespace std;

enum DSF_VARIABLE_TYPE
{
    DSF_STRING = 0,//
    DSF_INT,//
    DSF_UINT,//
    DSF_REAL,//
    DSF_BOOL,//
    DSF_UDINT,//
    DSF_DINT,
    DSF_LREAL,//
    DSF_SINT,//
    DSF_USINT,//
    DSF_LINT,//
    DSF_ULINT,//
    DSF_BYTE,//
    DSF_WORD,//
    DSF_DWORD,//
    DSF_LWORD,//
    DSF_CHAR,//
    DSF_UDT,//

    DSF_UNKNOWN = 10000,
};

class DSFVariable 
{
public:
    std::string m_name;//变量中唯一
    int m_linkTagId;//对应pdscfg.db中的点ID
    DSF_VARIABLE_TYPE m_type;
    char* m_value;
public:
    DSFVariable(std::string name, DSF_VARIABLE_TYPE type, int linkTagId);

    std::string getName(){return m_name;}
    int getVariableSize();
    void* getValue();
    void setValue(void* value);
    ~DSFVariable();
    void print();
};
