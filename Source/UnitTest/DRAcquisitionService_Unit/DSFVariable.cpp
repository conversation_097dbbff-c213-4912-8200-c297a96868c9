#include "DSFVariable.h"
#include <iostream>
#include <memory.h>
DSFVariable::DSFVariable(std::string name, DSF_VARIABLE_TYPE type, int linkTagId)
: m_name(name),m_type(type),m_linkTagId(linkTagId)
{
    m_value = new char[getVariableSize()];
}


DSFVariable::~DSFVariable()
{
    if(m_value)
    {
        delete m_value;
        m_value = NULL;
    }
}


void DSFVariable::print()
{
    std::cout << "Variable: " << m_name << " LinkTagId: " << m_linkTagId << std::endl;
}


int DSFVariable::getVariableSize()
{
    switch (m_type)
    {
        case DSF_BOOL:
            return sizeof(bool);
        // case DSF_BYTE:
        //     return sizeof(byte);
        case DSF_CHAR:
            return sizeof(char);
        case DSF_SINT:
            return sizeof(char);
        case DSF_USINT:
            return sizeof(unsigned char);
        case DSF_WORD:
            return sizeof(unsigned short);
        case DSF_INT:
            return 2;//int16
        case DSF_UINT:
            return 2;
        case DSF_DWORD:
            return 4;//unsigned int32
        case DSF_UDINT:
            return 4;//unsigned int32
        case DSF_DINT:
            return 4;
        case DSF_REAL:
            return sizeof(float);
        case DSF_LREAL:
            return sizeof(double);
        case DSF_LWORD:
            return 8;//unsigned int64
        case DSF_LINT:
            return 8;//int64
        case DSF_ULINT:
            return 8;
        case DSF_UDT:
            return 4;
        default:
            return 0;
    }
}

void* DSFVariable::getValue()
{
    return m_value;
}

void DSFVariable::setValue(void* value)
{
    memcpy(m_value, value, getVariableSize());
}