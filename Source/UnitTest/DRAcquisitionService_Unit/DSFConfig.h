#pragma once
#include <map>
#include <string>
#include "DSFObject.h"
#include "DSFVariable.h"
#include "tinyxml2.h"
#include <set>
using namespace tinyxml2;
using namespace std;

class DSFConfig
{
public:
    DSFConfig();
    ~DSFConfig();
    
    bool loadConfig();

    bool addObject(DSFObject* obj);
    DSFObject* getObject(std::string name);

    bool addVariable(DSFVariable* var);
    DSFVariable* getVariable(std::string name);

    void printAllObjects();
    void printAllVariables();
public:
    void parseVariables(const char* filename);
    void readObject(const XMLDocument& doc, 
                    const XMLElement* parentElement, 
                    DSFObject* parentObject,
                    std::map<std::string, 
                    bool> objectIsEnabledMap);
    void makeObjectTree(const XMLDocument& doc, 
                    const XMLElement* parentElement,
                    DSFObject* parentObject,
                    std::set<std::string>& completedObjectNames); 
    void parseObjects(const char* filename);
    void clearConfig();
    DSF_VARIABLE_TYPE getVariableType(const std::string& name);

private:
    std::map<std::string,DSFObject*> m_objects;//所有对象
    std::map<std::string,DSFVariable*> m_localVariables;//本地变量表里的所有点
};
