#include <iostream>
#include <string>
#include <gtest/gtest.h>
#include "NetWrapper.h"
#include "SaveDataHandler.h"  // 包含 CSaveDataHandler 的头文件
#include "processdb/PDBConst.h"
#include <thread>
#include <chrono>
#include "DSFConfig.h"
#include "DSFObject.h"
#include "DSFVariable.h"
#include <map>
#include <filesystem>

// 测试 IsDriverRegister 方法
TEST(CNetWrapper, IsDriverRegister) {
    CNetWrapper netWrapper;

    // 初始状态：没有注册的驱动
    EXPECT_FALSE(netWrapper.IsDriverRegister("driver1"));

    // 注册一个驱动
    HQUEUE hQueue = reinterpret_cast<HQUEUE>(1);  // 假设队列句柄为 1
    netWrapper.m_mapDriverName2CliQueue["driver1"] = hQueue;

    // 检查已注册的驱动
    EXPECT_TRUE(netWrapper.IsDriverRegister("driver1"));

    // 检查未注册的驱动
    EXPECT_FALSE(netWrapper.IsDriverRegister("driver2"));
}

// 测试 DSFVariable 的 getVariableSize 函数
TEST(DSFVariableTest, GetVariableSize) {
    DSFVariable var("TestVariable", DSF_BOOL, 1);

    // 测试不同的数据类型
    EXPECT_EQ(var.getVariableSize(), sizeof(bool));
    var.m_type = DSF_CHAR;
    EXPECT_EQ(var.getVariableSize(), sizeof(char));
    // ... 对其他类型进行类似的测试
}



// 测试 DSFObject 的 addVariable 函数
TEST(DSFObjectTest, AddVariable) {
    DSFObject obj("TestObject");
    std::string varName = "TestVariable";
    DSF_VARIABLE_TYPE type = DSF_BOOL;
    int linkTagId = 1;
    DSFVariable* var = new DSFVariable(varName, type, linkTagId);

    EXPECT_TRUE(obj.addVariable(var));
    EXPECT_FALSE(obj.addVariable(new DSFVariable(varName, type, linkTagId))); // 尝试添加重复的变量
}

// 测试 DSFObject 的 getVariable 函数
TEST(DSFObjectTest, GetVariable) {
    DSFObject obj("TestObject");
    std::string varName = "TestVariable";
    DSF_VARIABLE_TYPE type = DSF_BOOL;
    int linkTagId = 1;
    DSFVariable* var = new DSFVariable(varName, type, linkTagId);
    obj.addVariable(var);

    DSFVariable* retrievedVar = obj.getVariable(varName);
    EXPECT_TRUE(retrievedVar != NULL);
    EXPECT_EQ(retrievedVar->getName(), varName);
}


// 测试 DSFObject 的 addSubObject 函数
TEST(DSFObjectTest, AddSubObject) {
    DSFObject obj("TestObject");
    DSFObject* subObj = new DSFObject("SubObject");

    EXPECT_TRUE(obj.addSubObject(subObj));
    EXPECT_FALSE(obj.addSubObject(new DSFObject("SubObject"))); // 尝试添加重复的对象
}

TEST(DSFObjectTest, BasicAlignment) {
    DSFObject obj("test");
    obj.setAlignment(4);

    // 添加一个小于4字节的变量
    //DSF_INT -> INT16 2
    DSFVariable* var1 = new DSFVariable("var1", DSF_INT, 1);
    EXPECT_TRUE(obj.addVariable(var1));

    //real -> float 4
    DSFVariable* var2 = new DSFVariable("var2", DSF_REAL, 2);
    EXPECT_TRUE(obj.addVariable(var2));

    EXPECT_EQ(obj.calculateSizeWithAlignment(), 8); // 验证计算结果
}
//4字节对齐
TEST(DSFObjectTest, MultipleVariables4) {
    DSFObject obj("test");
    obj.setAlignment(4);

    // 添加多个变量，包括小于和大于等于4字节的变量
    //LREAL -> double 8
    DSFVariable* var1 = new DSFVariable("var1", DSF_LREAL, 1);
    EXPECT_TRUE(obj.addVariable(var1));
    //BYTE -> unsigned char 1
    DSFVariable* var2 = new DSFVariable("var2", DSF_BYTE, 2);
    EXPECT_TRUE(obj.addVariable(var2));
    //WORD -> unsigned int16 2
    DSFVariable* var3 = new DSFVariable("var3", DSF_WORD, 3);
    EXPECT_TRUE(obj.addVariable(var3));
    

    EXPECT_EQ(obj.calculateSizeWithAlignment(), 16); // 验证计算结果
}
// //2字节对齐
// TEST(DSFObjectTest, MultipleVariables2) {
//     DSFObject obj("test");
//     obj.setAlignment(2);

//     // 添加多个变量，包括小于和大于等于4字节的变量
//     //LREAL -> double 8
//     DSFVariable* var1 = new DSFVariable("var1", DSF_LREAL, 1);
//     EXPECT_TRUE(obj.addVariable(var1));
//     //BYTE -> unsigned char 1
//     DSFVariable* var2 = new DSFVariable("var2", DSF_BYTE, 2);
//     EXPECT_TRUE(obj.addVariable(var2));
//     //WORD -> unsigned int16 2
//     DSFVariable* var3 = new DSFVariable("var3", DSF_WORD, 3);
//     EXPECT_TRUE(obj.addVariable(var3));
//     //DSF_INT -> INT16 2
//     DSFVariable* var4 = new DSFVariable("var4", DSF_INT, 4);
//     EXPECT_TRUE(obj.addVariable(var4));
    

//     EXPECT_EQ(obj.calculateSizeWithAlignment(), 14); // 验证计算结果
// }
// //8字节对齐
// TEST(DSFObjectTest, MultipleVariables8) {
//     DSFObject obj("test");
//     obj.setAlignment(4);

//     // 添加多个变量，包括小于和大于等于4字节的变量
//     //LREAL -> double 8
//     DSFVariable* var1 = new DSFVariable("var1", DSF_LREAL, 1);
//     EXPECT_TRUE(obj.addVariable(var1));
//     //BYTE -> unsigned char 1
//     DSFVariable* var2 = new DSFVariable("var2", DSF_BYTE, 2);
//     EXPECT_TRUE(obj.addVariable(var2));
//     //WORD -> unsigned int16 2
//     DSFVariable* var3 = new DSFVariable("var3", DSF_WORD, 3);
//     EXPECT_TRUE(obj.addVariable(var3));
//     //DSF_INT -> INT16 2
//     DSFVariable* var4 = new DSFVariable("var4", DSF_INT, 4);
//     EXPECT_TRUE(obj.addVariable(var4));
    

//     EXPECT_EQ(obj.calculateSizeWithAlignment(), 32); // 验证计算结果
// }
// //嵌套?
// TEST(DSFObjectTest, NestedObjects) {
//     DSFObject obj("test");
//     obj.setAlignment(4);

//     // 添加嵌套的对象
//     DSFObject nestedObj("nested");
//     //DSF_DINT -> int32 4
//     DSFVariable* nestedVar = new DSFVariable("nested_var", DSF_DINT, 1);
//     EXPECT_TRUE(nestedObj.addVariable(nestedVar));
//     EXPECT_TRUE(obj.addSubObject(&nestedObj));

//     EXPECT_EQ(obj.calculateSizeWithAlignment(), 12); // 验证计算结果
// }
// 测试 DSFConfig 的 addObject 函数
TEST(DSFConfigTest, AddObject) {
    DSFConfig config;
    std::string name = "TestObject";
    DSFObject* obj = new DSFObject(name);

    EXPECT_TRUE(config.addObject(obj));
    EXPECT_FALSE(config.addObject(new DSFObject(name))); // 尝试添加重复的对象
}

// 测试 DSFConfig 的 getObject 函数
TEST(DSFConfigTest, GetObject) {
    DSFConfig config;
    std::string name = "TestObject";
    DSFObject* obj = new DSFObject(name);
    config.addObject(obj);

    DSFObject* retrievedObj = config.getObject(name);
    EXPECT_TRUE(retrievedObj != NULL);
    EXPECT_EQ(retrievedObj->getName(), name);
}

// 测试 DSFConfig 的 addVariable 函数
TEST(DSFConfigTest, AddVariable) {
    DSFConfig config;
    std::string varName = "TestVariable";
    DSF_VARIABLE_TYPE type = DSF_BOOL;
    int linkTagId = 1;
    DSFVariable* var = new DSFVariable(varName, type, linkTagId);

    EXPECT_TRUE(config.addVariable(var));
    EXPECT_FALSE(config.addVariable(new DSFVariable(varName, type, linkTagId))); // 尝试添加重复的变量
}

// 测试 DSFConfig 的 getVariable 函数
TEST(DSFConfigTest, GetVariable) {
    DSFConfig config;
    std::string varName = "TestVariable";
    DSF_VARIABLE_TYPE type = DSF_BOOL;
    int linkTagId = 1;
    DSFVariable* var = new DSFVariable(varName, type, linkTagId);
    config.addVariable(var);

    DSFVariable* retrievedVar = config.getVariable(varName);
    EXPECT_TRUE(retrievedVar != NULL);
    EXPECT_EQ(retrievedVar->getName(), varName);
}


TEST(DSFConfigTest,ParseVariables){
    DSFConfig config;
    char currentPath[FILENAME_MAX];
    ssize_t count = readlink("/proc/self/exe", currentPath, sizeof(currentPath));
    ASSERT_TRUE(count != -1) << "Error getting current executable path";
    currentPath[count] = '\0'; // Null-terminate the path
    snprintf(currentPath + (strrchr(currentPath, '/') - currentPath), sizeof(currentPath) - (strrchr(currentPath, '/') - currentPath), "/../config/LocalVariable.xml");
    const char* xmlpath = currentPath;
    config.parseVariables(xmlpath);
    DSFVariable* var1 = config.getVariable("tag1");
    EXPECT_NE(var1, nullptr); // tag1 存在于配置中
    DSFVariable* var2 = config.getVariable("var2");
    EXPECT_EQ(var2, nullptr); // VAR2不存在于配置中
}


TEST(DSFConfigTest, MakeObjectTreeFromFile) {
    char currentPath[FILENAME_MAX];
    ssize_t count = readlink("/proc/self/exe", currentPath, sizeof(currentPath));
    ASSERT_TRUE(count != -1) << "Error getting current executable path";
    currentPath[count] = '\0'; // Null-terminate the path
    snprintf(currentPath + (strrchr(currentPath, '/') - currentPath), sizeof(currentPath) - (strrchr(currentPath, '/') - currentPath), "/../config/DataDefinition.xml");
    const char* xmlpath = currentPath;
    // 加载XML文件
    tinyxml2::XMLDocument doc;
    ASSERT_TRUE(doc.LoadFile(xmlpath) == tinyxml2::XML_SUCCESS);

    // 获取根元素
    const tinyxml2::XMLElement* rootElement = doc.FirstChildElement();
    ASSERT_NE(rootElement, nullptr);

    // 初始化一些辅助变量
    DSFConfig config;
    std::set<std::string> completedObjectNames;

    // 调用makeObjectTree函数
    config.makeObjectTree(doc, rootElement, nullptr, completedObjectNames);
    // 验证结果
    EXPECT_EQ(7, completedObjectNames.size());
}

// int main(){
//     tinyxml2::XMLDocument doc;
//     doc.LoadFile("/home/<USER>/wslvscodeproject/DSF/dr/Source/test_DRAcquisitionService/DataDefinition.xml");
//     // 获取根元素
//     const tinyxml2::XMLElement* rootElement = doc.FirstChildElement();

//     DSFConfig config;
//     // 初始化一些辅助变量
//     std::set<std::string> completedObjectNames;
//     std::map<std::string, bool> objectIsEnabledMap;

//     config.parseVariables("/home/<USER>/wslvscodeproject/DSF/dr/Source/test_DRAcquisitionService/LocalVariable.xml");
//     // Call the readObject function
//     config.readObject(doc, rootElement, nullptr, objectIsEnabledMap);
//     // 调用makeObjectTree函数
//     config.makeObjectTree(doc, rootElement, nullptr, completedObjectNames);
//     cout << completedObjectNames.size() << endl;
// }

