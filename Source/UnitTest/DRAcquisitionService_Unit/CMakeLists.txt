#CMakeLists.txt
cmake_minimum_required(VERSION 3.16)

project(servicebasetest VERSION 1.0)


set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED True)


set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} --coverage -O0 -g")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} --coverage -O0 -g")

# 获取用户的主目录
set(USER_HOME $ENV{HOME})
message(STATUS "USER_HOME" ${USER_HOME})
# 调用 find 命令找到 Source 文件夹
execute_process(
    COMMAND find ${USER_HOME} -type d -name "UnitTest"
    OUTPUT_VARIABLE FOUND_SOURCE_DIRS
    OUTPUT_STRIP_TRAILING_WHITESPACE
                    )
# 将找到的目录设置为变量
if(FOUND_SOURCE_DIRS)
    message(STATUS "Found Source directories: ${FOUND_SOURCE_DIRS}")
    set(SOURCE_DIRS ${FOUND_SOURCE_DIRS})
else()
    message(FATAL_ERROR "No Source directories found")
endif()
set(BINARY_DIR ${SOURCE_DIRS}/build)

add_custom_target(coverage_DRAcquisitionService_Unit
        COMMAND ${CMAKE_COMMAND} --build ${BINARY_DIR}
        COMMAND ${BINARY_DIR}/DRAcquisitionService_Unit/dracquisitiontest
        COMMAND lcov --capture --directory ${BINARY_DIR} --output-file ${BINARY_DIR}/coverage_DRAcquisitionService_Unit.info
        COMMAND lcov --remove ${BINARY_DIR}/coverage_DRAcquisitionService_Unit.info '/usr/*' --output-file ${BINARY_DIR}/coverage_DRAcquisitionService_Unit.info
        COMMAND genhtml ${BINARY_DIR}/coverage_DRAcquisitionService_Unit.info
                                        --output-directory ${BINARY_DIR}/coverage_DRAcquisitionService_Unit
                                        --title "Service Base Test Coverage"
                                        --legend --demangle-cpp
                                        --highlight
                                        --show-details
                                        --prefix ${BINARY_DIR}/../DRAcquisitionService_Unit
        COMMAND gcovr -r ${CMAKE_SOURCE_DIR}  --object-directory ${BINARY_DIR}/../DRAcquisitionService_Unit --xml --output ${BINARY_DIR}/test_code_DRAcquisitionService_Unit_coverage.xml
        WORKING_DIRECTORY ${BINARY_DIR}
        COMMENT "Generating coverage report..."
                                            )


# add_custom_target(coverage
# 	COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR}
# 	COMMAND ${CMAKE_BINARY_DIR}/dracquisitiontest
# 	COMMAND lcov --capture --directory ${CMAKE_BINARY_DIR} --output-file ${CMAKE_BINARY_DIR}/coverage.info
# 	COMMAND lcov --remove ${CMAKE_BINARY_DIR}/coverage.info '/usr/*' --output-file ${CMAKE_BINARY_DIR}/coverage.info
# 	COMMAND genhtml ${CMAKE_BINARY_DIR}/coverage.info 
# 					--output-directory ${CMAKE_BINARY_DIR}/coverage 
# 					--title "DRAcquisition Test Coverage" 
# 					--legend --demangle-cpp 
# 					--highlight 
# 					--show-details 
# 	COMMAND gcovr -r ${CMAKE_SOURCE_DIR} --object-directory ${CMAKE_BINARY_DIR} --xml --output ${CMAKE_BINARY_DIR}/test_code_coverage.xml
# 	WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
# 	COMMENT "Generating coverage report..."
# 			    )

add_executable(dracquisitiontest test_dracquisition.cpp NetWrapper.cpp DRAcquisitionService.cpp SaveDataHandler.cpp DSFConfig.cpp DSFObject.cpp DSFVariable.cpp)
target_include_directories(dracquisitiontest PUBLIC ${PROJECT_SOURCE_DIR}/../../../Source/common/servicebase/include)
target_include_directories(dracquisitiontest PUBLIC ${PROJECT_SOURCE_DIR}/../../../Source/common/cvcomm/include)
target_include_directories(dracquisitiontest PUBLIC ${PROJECT_SOURCE_DIR}/../../../Source/common/shmqueue/include)
target_include_directories(dracquisitiontest PUBLIC ${PROJECT_SOURCE_DIR}/../../../Source/common/cvlog/include)
target_include_directories(dracquisitiontest PUBLIC ${PROJECT_SOURCE_DIR}/../../../include)
target_include_directories(dracquisitiontest PUBLIC ${PROJECT_SOURCE_DIR}/../../../include/ace)
target_include_directories(dracquisitiontest PUBLIC ${PROJECT_SOURCE_DIR}/../../../include/common)
# target_include_directories(dracquisitiontest PUBLIC ${PROJECT_SOURCE_DIR}/../../Source/test/thirdparty/tinyxml2/include)
# target_include_directories(dracquisitiontest PUBLIC ${PROJECT_SOURCE_DIR}/../../Source/test/thirdparty/glog/include)
INCLUDE_DIRECTORIES(../../ext/netqueue/include)
#message("Output directory: ${PROJECT_SOURCE_DIR}../../Source/common/servicebase/include")


target_link_libraries(dracquisitiontest PRIVATE 
libgtest.a
libgtest_main.a
pthread

${PROJECT_SOURCE_DIR}/../../../library/libservicebasetest.a
${PROJECT_SOURCE_DIR}/../../../library/libdrlog.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_chrono.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_date_time.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_filesystem.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_regex.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_serialization.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_system.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_thread.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_wserialization.a
${PROJECT_SOURCE_DIR}/../../../library/libprotobuf.a
${PROJECT_SOURCE_DIR}/../../../library/libprotoc.a
${PROJECT_SOURCE_DIR}/../../../library/libcppsqlite.a
${PROJECT_SOURCE_DIR}/../../../library/libshmqueue.a
${PROJECT_SOURCE_DIR}/../../../executable/libdrhdProcComm.so
${PROJECT_SOURCE_DIR}/../../../executable/libdrlogimpl.so
${PROJECT_SOURCE_DIR}/../../../executable/libdrcomm.so
# ${PROJECT_SOURCE_DIR}/../../library/libgnuintl.so
${PROJECT_SOURCE_DIR}/../../../library/libiconv.so
${PROJECT_SOURCE_DIR}/../../../library/libintl.so
${PROJECT_SOURCE_DIR}/../../../library/libtinyxml.a
${PROJECT_SOURCE_DIR}/../../../library/libz.so
${PROJECT_SOURCE_DIR}/../../../library/libACE.so
${PROJECT_SOURCE_DIR}/../../../library/libglog.a
${PROJECT_SOURCE_DIR}/../../../library/libtinyxml2.so
${PROJECT_SOURCE_DIR}/../../../executable/libdrnetqueue.so


)






