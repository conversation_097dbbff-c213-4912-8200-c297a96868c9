#include <ace/Log_Msg.h>
#include <ace/Process_Semaphore.h>
#include <ace/Process_Mutex.h>
#include "processdb/PDBDef.h"
#include <ace/Time_Value.h>
#include "common/LogHelper.h"
#include "common/ServiceBase.h"
#include "common/cvGlobalHelper.h"
#include "gettext/libintl.h"
#include "stdio.h"
#include "NetWrapper.h"
#include "CommHelper.h"
#include "SaveDataHandler.h"
// #include "data_redis.h"
#define _(STRING) gettext(STRING)

//����manager����
CCVLog g_asLog;

class DRAcquisitionService:public CServiceBase
{
public:
	DRAcquisitionService() : CServiceBase ("DRAcquisitionService", false, "")
	{
	};

	virtual long Init(int argc, char* args[]);
	virtual void Refresh();
	virtual long Start();
	virtual void PrintStartUpScreen()
	{
		PrintHelpScreen();
	};
	virtual void PrintHelpScreen();
	virtual long Fini();
	virtual bool ProcessCmd(char c);
	//������־�ļ����ƣ���֤��ӡServiceBase�����־���������ļ���
	virtual void SetLogFileName()
	{
		// g_CVLogDrvCtrl.SetLogFileNameThread(DRIVER_CTRL_EXEC_NAME);
	}
	// ����������
	virtual void Misc();
	CSaveDataHandler* m_dataHandler; 
	CNetWrapper* m_netWrapper;
};

long DRAcquisitionService::Init( int argc, char* args[] )
{
	long nErr = ICV_SUCCESS;

    // auto redis = data_redis::getInstance("/var/redis/redis.sock");
    // auto redis = data_redis::getInstance("127.0.0.1", 6379);
    // std::string key = "test_key";
    // std::string value = "test_value";
    // std::string res = redis->dataBinSet(key, value);
    // std::cout << "Set result: " << res << std::endl;
    // std::string result = redis->dataBinGet(key);
    // std::cout << "Get result: " << result << std::endl;

	g_asLog.SetLogFileNameThread("DRAcquisitionService");
	m_netWrapper = new CNetWrapper();
	m_dataHandler = new CSaveDataHandler(m_netWrapper);

	CV_INFO(g_asLog,"m_dataHandler");
	m_dataHandler->Activate();
	CV_INFO(g_asLog,"m_dataHandler done");

	CV_INFO(g_asLog,"m_netWrapper");
	nErr = m_netWrapper->InitializeNetwork();
	CHECK_ERROR_AND_RETURN_FAIL(nErr);
	m_netWrapper->Activate();
	CV_INFO(g_asLog,"m_netWrapper done");


	return 0;
	// struct timeval timeout = { 1, 500000 }; // 1.5 seconds
	// STATERW_HostInitialize("",0,timeout,&g_pStateRW);

	// ACE_Time_Value timeValue = ACE_OS::gettimeofday();
	// TCV_TimeStamp timeStamp;
	// timeStamp.tv_sec = (uint32_t)timeValue.sec();
	// timeStamp.tv_usec = (uint32_t)timeValue.usec();
	// char szTime[ICV_HOSTNAMESTRING_MAXLEN] = {'\0'};
	// cvcommon::CastTimeToASCII(szTime, ICV_HOSTNAMESTRING_MAXLEN, timeStamp);
	// // ��ʼ��Ϊ��������
	// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
	// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
	// ACE_OS::snprintf(szStatus, sizeof(szStatus), "0;%s", szTime);
	// STATERW_SetStringCommand(&g_pStateRW, CV_STATUS_DRVCTRL_STATUS, szStatus);
	// // ����ʱ��
	// STATERW_SetStringCommand(&g_pStateRW, CV_STATUS_DRVCTRL_STARTTIME, szTime);

	// long lRet = g_MTManager.Init(argc, args);

	// if (lRet != ICV_SUCCESS)
	// {
	// 	// ��¼�쳣״̬
	// 	char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
	// 	memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
	// 	ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", lRet, szTime);
	// 	STATERW_SetStringCommand(&g_pStateRW, CV_STATUS_DRVCTRL_STATUS, szStatus);
	// }
	// return lRet;
}

void DRAcquisitionService::Refresh()
{
	// g_MTManager.Refresh();
	return;
}

long DRAcquisitionService::Start()
{
	// g_MTManager.Run();
	// return ICV_SUCCESS;
	m_netWrapper->IsDriverRegister("iplatdadriver");
	// m_netWrapper->SendCtrlToDriver("iplatdadriver", ctrlMsg);
	return 0;
}

void DRAcquisitionService::PrintHelpScreen()
{
	printf("+====================================================================+\n");
	printf(_("|                     <<Welcome to DRAcquisitionService>>					   |\n"));
	printf(_("|  You can entering the following commands to configure the service  |\n"));
	printf(_("|  q/Q:Quit														   |\n"));
	printf(_("|  Others:Print tips												   |\n"));
	printf("+====================================================================+\n");
}

long DRAcquisitionService::Fini()
{
	CV_INFO(g_asLog,"m_netWrapper->ShutDown()");
	m_netWrapper->ShutDown();
	CV_INFO(g_asLog,"m_netWrapper->ShutDown() done");


	CV_INFO(g_asLog,"m_netWrapper->UnInitializeNetwork()");
	m_netWrapper->UnInitializeNetwork();	
	CV_INFO(g_asLog,"m_netWrapper->UnInitializeNetwork() done");

	SAFE_DELETE(m_netWrapper);
	g_asLog.StopLogThread();
	return 0;
	// g_MTManager.Stop();
	// g_MTManager.Finalize();

	// if (g_pStateRW)
	// {
	// 	STATERW_HostUnInitialize(g_pStateRW);
	// 	g_pStateRW = NULL;
	// }
    // g_CVLogDrvCtrl.StopLogThread();
	// return ICV_SUCCESS;
}

bool DRAcquisitionService::ProcessCmd( char c )
{
	return false;
}

void DRAcquisitionService::Misc()
{

}

CServiceBase* g_pServiceHandler = new DRAcquisitionService();


