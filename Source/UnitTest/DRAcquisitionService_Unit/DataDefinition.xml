<?xml version="1.0" encoding="UTF-8"?>
<Data>
<!-- 每个Model可以实例化为多个Object
    name: 实例化对象名称
    refmodel: 被实例化的对象模型名称
    desc：对象描述
 -->
    <Object name="object1" refmodel="model1" desc="">
    <!-- 模型中的单个测点信息
        name: 对应模型中的member名称
        desc: 对象实例化对测点的描述
    -->
        <Reference name="tag1" desc="" />
        <Reference name="tag2" desc="" />
        <!-- 模型中有嵌套模型时，对嵌套的模型实例化，属性定义同上 -->
        <Object name="InnerObject1" refmodel="model2" desc="">
            <Reference name="tag3" desc="" />
            <Reference name="tag4" desc="" />
            <Reference name="tag5" desc="" />
            <Object name="InnerObject2" refmodel="model3" desc="">
                <Reference name="tag6" desc="" />
            </Object>
        </Object>
    </Object>
 
    <Object name="object2" refmodel="model1" desc="">
        <Reference name="tag1" desc="" />
        <Reference name="tag2" desc="" />
        <Object name="InnerObject1" refmodel="model2" desc="">
            <Reference name="tag3" desc="" />
            <Reference name="tag4" desc="" />
            <Reference name="tag5" desc="" />
            <Object name="InnerObject2" refmodel="model3" desc="">
                <Reference name="tag6" desc="" />
            </Object>
        </Object>
    </Object>
 
<!-- 模型中有嵌套时，实例对象可以分开，用linkObject标签将实例化的对象链接 -->
    <Object name="object3" refmodel="model1" desc="">
        <Reference name="tag1" desc="" />
        <Reference name="tag2" desc="" />
        <Reference name="model2" linkObject="object4" />
    </Object>
 
    <Object name="object4" refmodel="model2" desc="">
        <Reference name="tag3" desc="" />
        <Reference name="tag4" desc="" />
        <Reference name="tag5" desc="" />
        <Object name="InnerObject" refmodel="model3" desc="">
            <Reference name="tag6" desc="" />
        </Object>
    </Object>
 
    <!-- topic中支持直接定义单个测点，publisherIP用来标识发布这个topic的设备IP, publisherPort表示端口 -->
    <Topic name="topic1" publisherIP="*************" publisherPort="12345">
        <Object name="object1" />
        <member name="tag10" type="BOOL" desc="" unit="" />
    </Topic>
</Data>