import ctypes
import os
import logging
import subprocess
import redis
import pytest

# Import your functions
from allure_setup import *

# Example Jira key
JIRA_KEY = "DSFR-57"

# 配置日志记录，将日志写入文件
logging.basicConfig(level=logging.INFO, filename='test_deploy.log', filemode='w', format='%(asctime)s - %(levelname)s - %(message)s')

# 定义相对路径
relative_path = os.path.join(os.path.dirname(__file__), "../../executable")

# 加载共享库
lib_path = os.path.join(relative_path, "libDRDeploySDK.so")
example = ctypes.CDLL(lib_path)

# 定义 C++ 函数的参数和返回类型
example.WriteData.argtypes = [
    ctypes.c_char_p,       # const char* IPAddr
    ctypes.c_ushort,       # unsigned short port
    ctypes.c_char_p,       # const char* buf
    ctypes.c_uint,         # unsigned int len
    ctypes.c_int           # int timeout
]
example.WriteData.restype = ctypes.c_int  # 假设返回值是 int

def build_file_path(base_dir, filename):
    """构建完整的文件路径"""
    return os.path.join(base_dir, filename)

@allure_setup("DSFR-57", is_async=False)
def test_jira_summary():
    base_dir = os.path.join(os.path.dirname(__file__), "documents")  # 指定基础目录
    filename = "testzip.zip"  # 替换为你的文件名
    file_path = build_file_path(base_dir, filename)

    # 打开文件并读取内容
    try:
        with open(file_path, "rb") as file:
            buffer = file.read()
    except IOError as e:
        logging.error(f"Failed to open file {file_path}: {e}")
        assert False, f"Failed to open file {file_path}: {e}"

    file_size = len(buffer)

    # 启动 drdeployservice 进程
    drdeployservice_process = subprocess.Popen(["./drdeployservice"], cwd=relative_path)
    
    # 调用 C++ 函数 WriteData
    ip_addr = b"127.0.0.1"
    port = 60100
    timeout = 5000
    result = example.WriteData(ip_addr, port, buffer, file_size, timeout)
    assert result == 0, f"WriteData failed with result: {result}"
    logging.info(f"WriteData result: {result}")

    drdeployservice_process.terminate()


@allure_setup("DSFR-68", is_async=False)
def test_jira_summary_false():
    base_dir = os.path.join(os.path.dirname(__file__), "documents")  # 指定基础目录
    filename = "testzip.zip"  # 替换为你的文件名
    file_path = build_file_path(base_dir, filename)

    # 打开文件并读取内容
    try:
        with open(file_path, "rb") as file:
            buffer = file.read()
    except IOError as e:
        logging.error(f"Failed to open file {file_path}: {e}")
        assert False, f"Failed to open file {file_path}: {e}"

    file_size = len(buffer)

    # 启动 drdeployservice 进程
    drdeployservice_process = subprocess.Popen(["./drdeployservice"], cwd=relative_path)
    
    # 调用 C++ 函数 WriteData, 传入错误的 IP 与端口, 期望返回无法连接错误码200402
    ip_addr = b"***********"
    port = 34234
    timeout = 5000
    result = example.WriteData(ip_addr, port, buffer, file_size, timeout)
    assert result == 200402, f"WriteData failed with result: {result}"
    logging.info(f"WriteData result: {result}")

    # # 调用 C++ 函数 WriteData, 传入格式错误的IP地址, 期望返回格式错误200401
    # ip_addr = b"192.1.1"
    # port = 60100
    # timeout = 5000
    # result = example.WriteData(ip_addr, port, buffer, file_size, timeout)
    # assert result == 200401, f"WriteData failed with result: {result}"
    # logging.info(f"WriteData result: {result}")

    drdeployservice_process.terminate()
# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test_deploy.py"])