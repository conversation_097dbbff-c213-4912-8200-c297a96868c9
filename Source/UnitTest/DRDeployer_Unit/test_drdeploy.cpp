#include <iostream>
#include <string>
#include <fstream>
#include <gtest/gtest.h>
#include "CmdProcessTask.h"
#include "RecFileLink.h"

class CmdProcessTaskTest : public ::testing::Test {
protected:
    CCmdProcessTask* cmdProcessTask;
    void SetUp() override {
        // Setup any state here that is common to all tests.
        cmdProcessTask=new CCmdProcessTask();
    }

    void TearDown() override {
        // Cleanup after tests.
        delete cmdProcessTask;
    }
    
};

TEST_F(CmdProcessTaskTest, DecompressFileSuccess) {
    // 准备一个有效的压缩文件内容
   // 压缩包路径
    // const std::string zipFilePath = "../documents/config.zip";
    char currentPath[FILENAME_MAX];
    ssize_t count = readlink("/proc/self/exe", currentPath, sizeof(currentPath));
    ASSERT_TRUE(count != -1) << "Error getting current executable path";
    currentPath[count] = '\0'; // Null-terminate the path
    snprintf(currentPath + (strrchr(currentPath, '/') - currentPath), sizeof(currentPath) - (strrchr(currentPath, '/') - currentPath), "/../config/config.zip");
    const std::string zipFilePath = currentPath;

    // 读取压缩包内容
    std::ifstream file(zipFilePath, std::ios::binary);
    ASSERT_TRUE(file.is_open()) << "Failed to open the zip file: " << zipFilePath;

    // 获取文件大小
    file.seekg(0, std::ios::end);
    int len = static_cast<int>(file.tellg());
    file.seekg(0, std::ios::beg);

    // 分配内存并读取文件内容
    char *buf = new char[len + 1];
    file.read(buf, len);
    buf[len] = '\0';  // 确保字符串以null结尾
    file.close();

    // 调用DecompressFile方法
    EXPECT_EQ(CMD_PROCESS_TASK->DecompressFile(buf, len), 0);

    // 清理内存
    delete[] buf;

}

TEST_F(CmdProcessTaskTest, DecompressFileFailure_ZipOpenFail) {
    // 准备一个无效的ZIP文件内容
    char currentPath[FILENAME_MAX];
    ssize_t count = readlink("/proc/self/exe", currentPath, sizeof(currentPath));
    ASSERT_TRUE(count != -1) << "Error getting current executable path";
    currentPath[count] = '\0'; // Null-terminate the path
    snprintf(currentPath + (strrchr(currentPath, '/') - currentPath), sizeof(currentPath) - (strrchr(currentPath, '/') - currentPath), "/../config/damagedzip.zip");
    const std::string zipFilePath = currentPath;
    std::ifstream file(zipFilePath, std::ios::binary | std::ios::ate);
    std::streamsize size = file.tellg();
    char* buf = new char[size];
    file.seekg(0, std::ios::beg);
    file.read(buf, size);

    // 调用DecompressFile方法
    EXPECT_EQ(cmdProcessTask->DecompressFile(buf, size), EC_DSF_FAILTOOPENCFGFILE);

    // 清理内存
    delete[] buf;
}


TEST_F(CmdProcessTaskTest, DecompressFileFailure_FileIsEmpty) {
    // 准备一个空文件内容
    char *buf = new char[1];
    buf[0] = '\0';
    int len = 0;

    // 调用DecompressFile方法
    EXPECT_EQ(cmdProcessTask->DecompressFile(buf, len), EC_DSF_FAILTOOPENCFGFILE);

    // 清理内存
    delete[] buf;
}


TEST_F(CmdProcessTaskTest, DecompressFileFailure_ReadFail) {
    // 假设读取文件失败
    char *buf = nullptr;
    int len = 0;

    // 调用DecompressFile方法
    EXPECT_EQ(cmdProcessTask->DecompressFile(buf, len), EC_DSF_FAILTOOPENCFGFILE);
}

TEST_F(CmdProcessTaskTest, DecompressFileFailure_InvalidZipOrDirectory) {
    // 准备一个无效的ZIP文件（实际上是一个文本文件）
    char currentPath[FILENAME_MAX];
    ssize_t count = readlink("/proc/self/exe", currentPath, sizeof(currentPath));
    ASSERT_TRUE(count != -1) << "Error getting current executable path";
    currentPath[count] = '\0'; // Null-terminate the path
    snprintf(currentPath + (strrchr(currentPath, '/') - currentPath), sizeof(currentPath) - (strrchr(currentPath, '/') - currentPath), "/../config/invalidfile.txt");
    const std::string invalidFilePath = currentPath;
    // 读取无效的ZIP文件内容
    ifstream file(invalidFilePath, ios::binary | ios::ate);
    if (!file) {
        printf("Failed to open invalid ZIP file: %s\n", invalidFilePath.c_str());
        return;
    }
    std::streamsize size = file.tellg();
    char* buf = new char[size];
    file.seekg(0, ios::beg);
    file.read(buf, size);

    // 调用DecompressFile方法
    EXPECT_EQ(cmdProcessTask->DecompressFile(buf, size), EC_DSF_FAILTOOPENCFGFILE); // 期望返回非0值，表示失败

    // 清理
    delete[] buf;
}

// TEST_F(CmdProcessTaskTest, DecompressFileFailure_big) {
//     // 准备一个大的ZIP文件
//     std::string invalidFilePath = "../documents/invalidfile.txt";

//     // 读取无效的ZIP文件内容
//     ifstream file(invalidFilePath, ios::binary | ios::ate);
//     if (!file) {
//         printf("Failed to open invalid ZIP file: %s\n", invalidFilePath.c_str());
//         return;
//     }
//     std::streamsize size = file.tellg();
//     char* buf = new char[size];
//     file.seekg(0, ios::beg);
//     file.read(buf, size);

//     // 调用DecompressFile方法
//     EXPECT_EQ(cmdProcessTask->DecompressFile(buf, size), -1); // 期望返回非0值，表示失败

//     // 清理
//     delete[] buf;
// }
// TEST_F(CmdProcessTaskTest, DecompressFileFailure_CreateDirFail) {
//     // 准备一个有效的ZIP文件内容
//     std::string zipFilePath = "/home/<USER>/wslvscodeproject/DSF/dr/Source/test_DRDeployer/documents/testzip.zip";
//     std::ifstream file(zipFilePath, std::ios::binary | std::ios::ate);
//     std::streamsize size = file.tellg();
//     char* buf = new char[size];
//     file.seekg(0, std::ios::beg);
//     file.read(buf, size);

//     std::string targetFilePath = "/home/<USER>/wslvscodeproject/DSF/dr/Source/test_DRDeployer/config";

//     // 修改目标目录的权限，使其不可写
//     if (chmod(targetFilePath.c_str(), 0000) != 0) {
//         printf("Failed to change directory permissions: %s\n", strerror(errno));
//         delete[] buf;
//         return;
//     }

//     // 调用DecompressFile方法
//     EXPECT_EQ(cmdProcessTask->DecompressFile(buf, size), 0); // 期望返回非0值，表示失败

//     // 恢复权限并清理
//     if (chmod(targetFilePath.c_str(), 0777) != 0) {
//         printf("Failed to restore directory permissions: %s\n", strerror(errno));
//     }
//     delete[] buf;
// }


// TEST_F(CmdProcessTaskTest, DecompressFileFailure_FileIsDirectory) {
//     // 假设解压的目标是一个已存在的目录
//     char *buf = nullptr;
//     int len = 0;

//     // 调用DecompressFile方法
//     EXPECT_EQ(cmdProcessTask->DecompressFile(buf, len), 0); // 如果是目录则跳过，这里假设返回0表示成功
// }
#if 0
int main(int argc, char **argv) {
	    ::testing::InitGoogleTest(&argc, argv);
	    return RUN_ALL_TESTS();
}
#endif