#include "opcua_data_cache.h"
#include <gtest/gtest.h>

class OpcuaDataCacheTest : public ::testing::Test
{
  protected:
    void SetUp() override
    {
        cache = OpcuaDataCache::GetInstance();
    }

    void TearDown() override
    {
        cache = nullptr;
    }

    OpcuaDataCache *cache;
};

TEST_F(OpcuaDataCacheTest, SingletonTest)
{
    OpcuaDataCache *cache1 = OpcuaDataCache::GetInstance();
    ASSERT_NE(cache1, nullptr);
    OpcuaDataCache *cache2 = OpcuaDataCache::GetInstance();
    ASSERT_NE(cache2, nullptr);

    ASSERT_EQ(cache2, cache1);
}

TEST_F(OpcuaDataCacheTest, CacheDataTest)
{
    dsfapi::TagValue tagValue;
    tagValue.ReSet("test", 4);
    std::string strPubTagName = "std.test";

    // 缓存数据
    cache->CacheData(strPubTagName, std::move(tagValue));

    // 读取数据
    const dsfapi::TagValue *retrieved = cache->GetCacheData(strPubTagName);
    ASSERT_NE(retrieved, nullptr);
    ASSERT_STREQ(retrieved->Buffer(), "test");
}

TEST_F(OpcuaDataCacheTest, GetCacheData_NotExist)
{
    std::string strPubTagName = "std.test_notexist";
    const dsfapi::TagValue *retrieved = cache->GetCacheData(strPubTagName);
    EXPECT_EQ(retrieved, nullptr);
}

TEST_F(OpcuaDataCacheTest, EnqueueAndDequeueWriteData)
{
    auto writeData = std::make_shared<WriteData>();
    writeData->strPubTagName = "std.test";

    // 入队
    EXPECT_TRUE(cache->EnqueueWriteData(writeData));

    // 出队
    auto dequeuedData = cache->DequeueWriteData();
    ASSERT_NE(dequeuedData, nullptr);
    EXPECT_EQ(dequeuedData->strPubTagName, "std.test");
}

TEST_F(OpcuaDataCacheTest, DequeueWriteData_EmptyQueue)
{
    auto dequeuedData = cache->DequeueWriteData();
    EXPECT_EQ(dequeuedData, nullptr);
}
