#include "common/CVLog.h"
#include "opcua_data_cache.h"
#include "opcua_server.h"
#include <gtest/gtest.h>
#include <memory>
#include <thread>
CCVLog g_OpcuaServerLog;
class COpcuaServerTest : public ::testing::Test
{
  protected:
    void SetUp() override
    {
        pConnectInfo = std::make_shared<OpcuaConnectInfo>();
        pConnectInfo->nOpcuaPort = 4840;
        server = COpcuaServer::GetInstance();
        ASSERT_EQ(server->Init(pConnectInfo), 0) << "服务器初始化失败";
    }

    void TearDown() override
    {
        server->Finish();
    }

    COpcuaServer *server;
    std::shared_ptr<OpcuaConnectInfo> pConnectInfo;
};

// 测试初始化
TEST_F(COpcuaServerTest, InitTest)
{
    EXPECT_NE(server, nullptr) << "服务器实例为空";
}

// 测试添加变量
TEST_F(COpcuaServerTest, AddDataSourceVariableTest)
{
    OpcuaTagInfoPtr tagInfo = std::make_shared<OpcuaTagInfo>();
    tagInfo->strTagName = "TestTag";
    tagInfo->strPubTagName = "PubTestTag";
    tagInfo->nIntervalMs = 1000;
    EXPECT_FALSE(GetUaType(tagInfo));
    tagInfo->strType = "DINT";
    EXPECT_TRUE(GetUaType(tagInfo));
    EXPECT_EQ(server->AddDataSourceVariable(tagInfo), 0) << "添加变量失败";
}

// 测试服务器运行
TEST_F(COpcuaServerTest, RunTest)
{
    std::thread serverThread([&]() { server->Run(); });
    std::this_thread::sleep_for(std::chrono::seconds(1));
    server->Finish();
    serverThread.join();
    SUCCEED() << "服务器成功运行并退出";
}

// int main(int argc, char **argv)
// {
//     ::testing::InitGoogleTest(&argc, argv);
//     return RUN_ALL_TESTS();
// }
