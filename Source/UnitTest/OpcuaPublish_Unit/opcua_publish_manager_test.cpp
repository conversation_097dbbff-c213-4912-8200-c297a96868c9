#include "common/CVLog.h"
#include "opcua_publish_manager.h"
#include <gtest/gtest.h>
#include <memory>
#include <thread>

CCVLog g_OpcuaServerLog; // 避免日志相关编译错误

class COpcuaPublishManagerTest : public ::testing::Test
{
  protected:
    void SetUp() override
    {
        std::cout << "------------ SetUp" << std::endl;
        manager = std::make_unique<COpcuaPublishManager>(1000);
        ASSERT_NE(manager, nullptr) << "COpcuaPublishManager 实例化失败";
    }

    void TearDown() override
    {
        // manager->Shutdown();
        std::cout << "------------ TearDown" << std::endl;
    }

    std::unique_ptr<COpcuaPublishManager> manager;
};

// 测试 Init 方法
// 使用 Google Test 框架定义一个测试用例
// COpcuaPublishManagerTest 是测试夹具类，用于设置测试环境
// InitTest 是具体的测试用例名称
TEST_F(COpcuaPublishManagerTest, InitTest)
{
    // 调用 manager 对象的 Init 方法进行初始化
    // EXPECT_EQ 是 Google Test 提供的断言宏，用于检查两个值是否相等
    // 这里检查 Init 方法的返回值是否为 0，0 通常表示成功
    // 如果不相等，则输出错误信息 "服务器初始化失败"
    EXPECT_EQ(manager->Init(), 0) << "服务器初始化失败";
}

// 测试 Run 方法
TEST_F(COpcuaPublishManagerTest, RunTest)
{
    std::thread serverThread([&]() {
        EXPECT_EQ(manager->Init(), 0) << "服务器初始化失败";
        std::cout << "------------ Init" << std::endl;
        EXPECT_EQ(manager->Run(), 0) << "服务器运行失败";
        std::cout << "------------ Run" << std::endl;
    });
    std::this_thread::sleep_for(std::chrono::seconds(4));
    manager->Shutdown();
    std::cout << "------------ Shutdown" << std::endl;
    serverThread.join();
    std::cout << "------------ join" << std::endl;
}

// 测试 Shutdown 方法
TEST_F(COpcuaPublishManagerTest, ShutdownTest)
{
    manager->Shutdown();
    SUCCEED() << "服务器成功关闭";
}
