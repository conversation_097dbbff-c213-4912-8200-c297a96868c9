cmake_minimum_required(VERSION 3.10)
set(CMAKE_CXX_STANDARD 17)

project(OpcuaDataCacheTest)

SET(DR_DIR ${CMAKE_CURRENT_SOURCE_DIR}/../../..)
SET(SOURCE_DIR ${DR_DIR}/Source)
SET(LIBRARY_DIR ${DR_DIR}/library)
SET(EXECUTABLE_DIR ${DR_DIR}/executable)
message(STATUS "DR_DIR: ${DR_DIR}")
message(STATUS "SOURCE_DIR: ${SOURCE_DIR}")


# 启用 GoogleTests
find_package(GTest REQUIRED)
include_directories(${GTEST_INCLUDE_DIRS})

# 目标源文件
file(GLOB OPCUA_PUBLISH_SOURCE_CPP ${SOURCE_DIR}/DRDataPublish/OpcuaPublish/*.cpp)
list(REMOVE_ITEM OPCUA_PUBLISH_SOURCE_CPP ${SOURCE_DIR}/DRDataPublish/OpcuaPublish/opcua_publish_service.cpp)
message(STATUS "Collected CPP sources: ${OPCUA_PUBLISH_SOURCE_CPP}")

# 可执行程序
add_executable(opcua_data_cache_test opcua_data_cache_test.cpp)
add_executable(opcua_server_test opcua_server_test.cpp ${OPCUA_PUBLISH_SOURCE_CPP})
add_executable(opcua_publish_manager_test opcua_publish_manager_test.cpp ${OPCUA_PUBLISH_SOURCE_CPP})
# 依赖的头文件目录
target_include_directories(opcua_data_cache_test PRIVATE ${SOURCE_DIR}/DRDataPublish/OpcuaPublish)
target_include_directories(opcua_data_cache_test PRIVATE ${SOURCE_DIR}/DRSdk/dsfapi)
target_include_directories(opcua_data_cache_test PRIVATE ${DR_DIR}/include)

target_include_directories(opcua_server_test PRIVATE ${SOURCE_DIR}/DRDataPublish/OpcuaPublish)
target_include_directories(opcua_server_test PRIVATE ${SOURCE_DIR}/DRSdk/dsfapi)
target_include_directories(opcua_server_test PRIVATE ${SOURCE_DIR}/common/cvcomm/include)
target_include_directories(opcua_server_test PRIVATE ${SOURCE_DIR}/common/cvlog/include)
target_include_directories(opcua_server_test PRIVATE ${SOURCE_DIR}/common/servicebase/include)
target_include_directories(opcua_server_test PRIVATE ${SOURCE_DIR}/common/shmqueue/include)
target_include_directories(opcua_server_test PRIVATE ${DR_DIR}/include)
target_include_directories(opcua_server_test PRIVATE ${DR_DIR}/include/open62541pp)
target_include_directories(opcua_server_test PRIVATE ${DR_DIR}/include/common)
target_include_directories(opcua_server_test PRIVATE ${DR_DIR}/include/ace)

target_include_directories(opcua_publish_manager_test PRIVATE ${SOURCE_DIR}/DRDataPublish/OpcuaPublish)
target_include_directories(opcua_publish_manager_test PRIVATE ${SOURCE_DIR}/DRSdk/dsfapi)
target_include_directories(opcua_publish_manager_test PRIVATE ${SOURCE_DIR}/common/cvcomm/include)
target_include_directories(opcua_publish_manager_test PRIVATE ${SOURCE_DIR}/common/cvlog/include)
target_include_directories(opcua_publish_manager_test PRIVATE ${SOURCE_DIR}/common/servicebase/include)
target_include_directories(opcua_publish_manager_test PRIVATE ${SOURCE_DIR}/common/shmqueue/include)
target_include_directories(opcua_publish_manager_test PRIVATE ${DR_DIR}/include)
target_include_directories(opcua_publish_manager_test PRIVATE ${DR_DIR}/include/open62541pp)
target_include_directories(opcua_publish_manager_test PRIVATE ${DR_DIR}/include/common)
target_include_directories(opcua_publish_manager_test PRIVATE ${DR_DIR}/include/ace)
# 链接  
target_link_directories(opcua_data_cache_test PRIVATE ${LIBRARY_DIR} ${EXECUTABLE_DIR})
target_link_libraries(opcua_data_cache_test PRIVATE GTest::GTest GTest::Main pthread)

target_link_directories(opcua_server_test PRIVATE ${LIBRARY_DIR} ${EXECUTABLE_DIR})
target_link_libraries(opcua_server_test PRIVATE GTest::GTest GTest::Main pthread)
target_link_libraries(opcua_server_test PRIVATE ACE drlog drlogimpl drcomm drrmapi shmqueue open62541pp open62541 dsfapi pthread boost_thread boost_system)

target_link_directories(opcua_publish_manager_test PRIVATE ${LIBRARY_DIR} ${EXECUTABLE_DIR})
target_link_libraries(opcua_publish_manager_test PRIVATE GTest::GTest GTest::Main pthread)
target_link_libraries(opcua_publish_manager_test PRIVATE ACE drlog drlogimpl drcomm drrmapi shmqueue open62541pp open62541 dsfapi pthread boost_thread boost_system)

add_definitions(-DUNIT_TEST)
