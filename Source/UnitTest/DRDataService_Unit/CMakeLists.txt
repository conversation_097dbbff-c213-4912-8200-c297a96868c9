#CMakeLists.txt
cmake_minimum_required(VERSION 3.16)


project(servicebasetest VERSION 1.0)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED True)


set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} --coverage -O0 -g")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} --coverage -O0 -g")


# 获取用户的主目录
set(USER_HOME $ENV{HOME})

message(STATUS "USER_HOME" ${USER_HOME})

# 调用 find 命令找到 Source 文件夹
execute_process(
    COMMAND find ${USER_HOME} -type d -name "UnitTest"
    OUTPUT_VARIABLE FOUND_SOURCE_DIRS
    OUTPUT_STRIP_TRAILING_WHITESPACE
		    )

# 将找到的目录设置为变量
if(FOUND_SOURCE_DIRS)
    message(STATUS "Found Source directories: ${FOUND_SOURCE_DIRS}")
    set(SOURCE_DIRS ${FOUND_SOURCE_DIRS})
else()
    message(FATAL_ERROR "No Source directories found")
endif()


set(BINARY_DIR ${SOURCE_DIRS}/build)

add_custom_target(coverage_DRDataService_Unit
	COMMAND ${CMAKE_COMMAND} --build ${BINARY_DIR}
	COMMAND ${BINARY_DIR}/DRDataService_Unit/${PROJECT_NAME}
	COMMAND lcov --capture --directory ${BINARY_DIR} --output-file ${BINARY_DIR}/coverage_DRDataService_Unit.info
	COMMAND lcov --remove ${BINARY_DIR}/coverage_DRDataService_Unit.info '/usr/*' --output-file ${BINARY_DIR}/coverage_DRDataService_Unit.info
	COMMAND genhtml ${BINARY_DIR}/coverage_DRDataService_Unit.info 
					--output-directory ${BINARY_DIR}/coverage_DRDataService_Unit
					--title "Service Base Test Coverage" 
					--legend --demangle-cpp 
					--highlight 
					--show-details 
					--prefix ${BINARY_DIR}/../DRDataService_Unit
	COMMAND gcovr -r ${CMAKE_SOURCE_DIR}  --object-directory ${BINARY_DIR}/../DRDataService_Unit --xml --output ${BINARY_DIR}/test_code_DRDataService_Unit_coverage.xml
					#	COMMAND gcovr -r ${BINARY_DIR}/.. --object-directory ${BINARY_DIR}/../../DRDataService_Unit --xml --output ${BINARY_DIR}/test_code_DRDataService_Unit_coverage.xml
	WORKING_DIRECTORY ${BINARY_DIR}
	COMMENT "Generating coverage report..."
					    )



set(COMMON_INCLUDE_DIRS
    ${PROJECT_SOURCE_DIR}/../../../Source/common/servicebase/include
    ${PROJECT_SOURCE_DIR}/../../../Source/common/cvcomm/include
    ${PROJECT_SOURCE_DIR}/../../../Source/common/shmqueue/include
    ${PROJECT_SOURCE_DIR}/../../../Source/common/cvlog/include
    ${PROJECT_SOURCE_DIR}/../../../include
	${PROJECT_SOURCE_DIR}/../../../include/common
    ${PROJECT_SOURCE_DIR}/../../../include/ace
    ${PROJECT_SOURCE_DIR}/../../../Source/test/thirdparty/tinyxml2/inc
    ${PROJECT_SOURCE_DIR}/../../../Source/test/thirdparty/glog/inc
    ${PROJECT_SOURCE_DIR}/../../../Source/DRDataService/DRDataService
)

set(COMMON_LIBS
    libgtest.a
    pthread
	${PROJECT_SOURCE_DIR}/../../../library/libDSF.so
	${PROJECT_SOURCE_DIR}/../../../library/libfastrtps.so
	${PROJECT_SOURCE_DIR}/../../../library/libfastcdr.so
    ${PROJECT_SOURCE_DIR}/../../../library/libdrlog.a
    ${PROJECT_SOURCE_DIR}/../../../library/libshmqueue.a
    ${PROJECT_SOURCE_DIR}/../../../executable/libdrlogimpl.so
    ${PROJECT_SOURCE_DIR}/../../../library/libintl.so
    ${PROJECT_SOURCE_DIR}/../../../library/libtinyxml.a
	${PROJECT_SOURCE_DIR}/../../../executable/libdrcomm.so
    ${PROJECT_SOURCE_DIR}/../../../library/libACE.so
    ${PROJECT_SOURCE_DIR}/../../../library/libhiredis.a
    ${PROJECT_SOURCE_DIR}/../../../library/libglog.a
    ${PROJECT_SOURCE_DIR}/../../../library/libtinyxml2.so
	
)

file(GLOB DDS_SHM_HELLOWORLD_EXAMPLE_SOURCES_CPP "${PROJECT_SOURCE_DIR}/../../../Source/DRDataService/DRDataService/*.cpp")
# 排除 drds_services.cpp 文件
list(REMOVE_ITEM DDS_SHM_HELLOWORLD_EXAMPLE_SOURCES_CPP "${PROJECT_SOURCE_DIR}/../../../Source/DRDataService/DRDataService/drds_services.cpp")

message(STATUS "Collected CPP sources: ${DDS_SHM_HELLOWORLD_EXAMPLE_SOURCES_CPP}")

set(COMMON_SRC  ${DDS_SHM_HELLOWORLD_EXAMPLE_SOURCES_CPP})


function(add_dataservice_test test_name test_sources)
    add_executable(${test_name} ${COMMON_SRC} ${test_sources})
    target_include_directories(${test_name} PUBLIC ${COMMON_INCLUDE_DIRS})
    target_link_libraries(${test_name} PRIVATE ${COMMON_LIBS})
    add_test(NAME ${test_name} COMMAND ${test_name} --gtest_output=xml:${test_name}.xml)
endfunction()

add_dataservice_test(${PROJECT_NAME}  "main_test.cpp;drds_data_manager_test.cpp;drds_ngvs_config_test.cpp;")

add_definitions(-DUNIT_TEST)





#add_dependencies(coverage DataserviceTest)
#GTest::GTest GTest::Main pthread /home/<USER>/dr/library/libservicebase.a)

