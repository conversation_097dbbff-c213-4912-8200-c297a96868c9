#include "drds_data_manager.h"
#include <stdio.h>
#include <stdlib.h>
#include <iostream>
#include "common.h"
#include <gtest/gtest.h>


class DRDSDataManagerTest : public ::testing::Test{
protected:
 char currentPath[FILENAME_MAX];
 void SetUp() override {
        ssize_t count = readlink("/proc/self/exe", currentPath, sizeof(currentPath));
        currentPath[count] = '\0'; 
        snprintf(currentPath + (strrchr(currentPath, '/') - currentPath), sizeof(currentPath) - (strrchr(currentPath, '/') - currentPath), "/../../DRDataService_Unit/DRDataServiceTestConfig/ctrlcmd");
    }

};


TEST_F(DRDSDataManagerTest, getUDTData){
    DRDSDataManager manger(currentPath);

}


