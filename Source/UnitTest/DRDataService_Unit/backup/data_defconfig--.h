/**
 * @file config.h
 * @brief
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2024-08-27
 *
 * @copyright Copyright (c) 2024  by  宝信
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2024-08-27     <td>1.0     <td>wwk   <td>修改?
 * </table>
 */

#pragma once

#include <iostream>
#include <memory>
#include <string>
#include <mutex>
#include <vector>
#include <string>
#include <glog/logging.h>
#include <unordered_map>
#include "tinyxml2.h"
//#include <gtest/gtest.h>

using namespace tinyxml2;

class Tag_data
{
public:
    friend class Object;

    Tag_data(const std::string &name, const std::string &type,
             const std::string &desc = "", const std::string &unit = "",
             const std::string &linkTag = "", int linkTagId = -1)
        : name(name), type(type), desc(desc), unit(unit),
          linkTag(linkTag), linkTagId(linkTagId) {}

    const std::string &getName() const { return name; }
    const std::string &getType() const { return type; }
    const std::string &getDesc() const { return desc; }
    const std::string &getUnit() const { return unit; }
    const std::string &getLinkTag() const { return linkTag; }
    int getLinkTagId() const { return linkTagId; }

private:
    void setName(const std::string &name) { this->name = name;  }
    void setType(const std::string &type) { this->type = type; }
    void setDesc(const std::string &desc) { this->desc = desc; }
    void setUnit(const std::string &unit) { this->unit = unit; }
    void setLinkTag(const std::string &linkTag) { this->linkTag = linkTag; }
    void setLinkTagId(int linkTagId) { this->linkTagId = linkTagId; }
 
private:
    std::string name;
    std::string type;
    std::string desc;
    std::string unit;
    std::string linkTag;
    int linkTagId;
};

class Object
{

public:
    friend class data_defconfig;
    
    Object(const std::string &name, const std::string &desc = "")
        : name(name), desc(desc) {}

    void addTag(const std::string &name, const std::shared_ptr<Tag_data> &tag)
    {
        tags[name] = tag;
    }

    void addObject(const std::string &name, const std::shared_ptr<Object> &model)
    {
        Objects[name] = model;
    }

    void printObject(int level = 0)
    {
        std::string indent(level * 2, ' ');
        std::string subIndent = indent + "  ";

        std::cout << indent << "  Model: " << name << " (Description: " << desc << ")" << std::endl;
        std::cout << indent << "  Ref Model: " << refModel << "\n";
        std::cout << indent << "  linkObjName: " << linkObjName << "\n";
        std::cout << indent << "  desc: " << desc << "\n";
        for (const auto &tag : tags)
        {
            std::cout << indent << "  Tag Name: " << tag.second->getName() << "\n";
            std::cout << indent << "  Type: " << tag.second->getType() << "\n";
            std::cout << indent << "  Description: " << tag.second->getDesc() << "\n";
            std::cout << indent << "  Unit: " << tag.second->getUnit() << "\n";
            std::cout << indent << "  Link Tag: " << tag.second->getLinkTag() << "\n";
            std::cout << indent << "  Link Tag ID: " << tag.second->getLinkTagId() << "\n";
        }

        for (const auto &model : Objects)
        {
            model.second->printObject(level + 1);
        }
    }

private:
    std::string name;
    std::string refModel;
    std::string linkObjName;
    std::string desc;
    std::map<std::string, std::shared_ptr<Object>> Objects;
    std::map<std::string, std::shared_ptr<Tag_data>> tags;
};

class data_defconfig
{
public:
    static std::shared_ptr<data_defconfig> getInstance(const std::string &filePath = "")
    {
        if ("" == filePath)
        {
            LOG(ERROR) << "No config file path provided, using default.";
            return nullptr;
        }

        std::call_once(initFlag, [&]
                       { instance.reset(new data_defconfig(filePath)); });
        return instance;
    }

    ~data_defconfig() {
    };

    const std::shared_ptr<Object> getGlobalModel() const
    {
        return GlobeObjects;
    }

private:
    data_defconfig(const std::string &filePath)
    {
        parsedata_defconfig(filePath);
    }

    void parsedata_defconfig(const std::string &filePath)
    {

        XMLDocument doc;
        XMLError result = doc.LoadFile(filePath.c_str());
        if (result != XML_SUCCESS)
        {
            LOG(ERROR) << "Failed to load parsedata_defconfig file: " << filePath << " Error loading XML file: " << doc.ErrorName();
            return;
        }

        XMLElement *rootElement = doc.RootElement();
        if (rootElement == nullptr || std::strcmp(rootElement->Name(), "Data") != 0)
        {
            LOG(ERROR) << "Invalid parsedata_defconfig file: " << filePath;
            return;
        }

        parseObject(rootElement, GlobeObjects);
    }

    void parseObject(XMLElement *element, std::shared_ptr<Object> parentObj)
    {
        for (XMLElement *objectElem = element->FirstChildElement("Object"); objectElem != nullptr; objectElem = objectElem->NextSiblingElement("Object"))
        {
            const char *name = objectElem->Attribute("name");
            const char *refmodel = objectElem->Attribute("refmodel");
            const char *desc = objectElem->Attribute("desc");

            if (!name)
            {
                std::cerr << "Error: Missing 'name' attribute in <Object> tag." << std::endl;
                continue;
            }

            auto obj = std::make_shared<Object>(name, desc ? desc : "");
            obj->refModel = refmodel ? refmodel : "";

            for (XMLElement *refElem = objectElem->FirstChildElement("Reference"); refElem != nullptr; refElem = refElem->NextSiblingElement("Reference"))
            {
                const char *tagName = refElem->Attribute("name");
                const char *tagDesc = refElem->Attribute("desc");
                const char *linkTag = refElem->Attribute("linkTag");
                int linkTagId = refElem->IntAttribute("linkTagId");
                const char *linkObject = refElem->Attribute("linkObject");

                if (linkObject)
                {

                    obj->linkObjName = linkObject;
                }
                else
                {

                    auto tag = std::make_shared<Tag_data>(
                        tagName ? std::string(tagName) : "",
                        "type",
                        tagDesc ? std::string(tagDesc) : "",
                        "",
                        linkTag ? std::string(linkTag) : "",
                        linkTagId);
                    obj->addTag(tagName ? std::string(tagName) : "", tag);
                }
            }

            parentObj->addObject(name, obj);

            parseObject(objectElem, obj);
        }
    }

    void resolveLinkObjects(std::shared_ptr<Object> rootObj)
    {

        for (const auto &[name, obj] : rootObj->Objects)
        {
            if (!obj->linkObjName.empty())
            {
                auto linkedObj = rootObj->Objects[obj->linkObjName];
                if (linkedObj)
                {
                    obj->addObject(obj->linkObjName, linkedObj);
                }
                else
                {
                    std::cerr << "Error: linkObject '" << obj->linkObjName << "' not found." << std::endl;
                }
            }

            resolveLinkObjects(obj);
        }
    }

    void parseData(XMLElement *rootElement)
    {
        std::shared_ptr<Object> GlobeObjects = std::make_shared<Object>("root", "root");

        parseObject(rootElement, GlobeObjects);

        resolveLinkObjects(GlobeObjects);
    }

    static std::shared_ptr<data_defconfig> instance;
    static std::once_flag initFlag;
    std::shared_ptr<Object> GlobeObjects = std::make_shared<Object>("root", "root");
};

std::shared_ptr<data_defconfig> data_defconfig::instance;
std::once_flag data_defconfig::initFlag;
