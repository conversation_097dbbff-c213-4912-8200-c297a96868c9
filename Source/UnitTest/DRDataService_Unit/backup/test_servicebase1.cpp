#include <iostream>
#include <string>
#include <iostream>
#include <gtest/gtest.h>
//#include "test_servicebase.cpp"
#include "data_defconfig.h"
#include "data_modelconfig.h"
#include "data_nodeconfig.h"
#include "data_redis.h"
#include <hiredis/hiredis.h>

//Servicebase test
//long
// TEST(CRMServiceTEST,CRMServiceInitTEST){
// CRMService* g_pServiceHandler = new CRMService();
//     int argc =2;
//     char* args[] ={"test1","test2"};
//     long val1 = g_pServiceHandler->Init(argc,args);
//     EXPECT_EQ(val1,0)<< "Expected and actual values do not match val1:" << val1;

// }

// TEST(CRMServiceTEST,CRMServiceStartTEST){
// CRMService* g_pServiceHandler = new CRMService();
// long val1 = g_pServiceHandler->Start();
//     EXPECT_EQ(val1,0) << "Expected and actual values do not match val1:" << val1;
// }

// TEST(CRMServiceTEST,CRMServiceFiniTEST){
//     CRMService* g_pServiceHandler = new CRMService();
//     long val1 = g_pServiceHandler->Fini();
//      EXPECT_EQ(val1,0) << "Expected and actual values do not match val1:" << val1;
// }
// //bool
// TEST(CRMServiceTEST,CRMServiceProcessCmdTEST){
//     CRMService* g_pServiceHandler = new CRMService();
//     char c = 'q';
//     long val1 = g_pServiceHandler->ProcessCmd(c);
//      EXPECT_TRUE(val1) << "Expected and actual values do not match val1:" << val1;
// }

// TEST(CRMServiceTEST,CRMServicePrintHelpScreenTEST){
//     CRMService* g_pServiceHandler = new CRMService();
//     EXPECT_NO_THROW(g_pServiceHandler->PrintHelpScreen()) ;
// }
// //void
// TEST(CRMServiceTEST,CRMServiceRefreshTEST){
//     CRMService* g_pServiceHandler = new CRMService();
//     EXPECT_NO_THROW( g_pServiceHandler->Refresh()) ;
// }

// TEST(CRMServiceTEST,CRMServiceSetLogFileNameTEST){
//     CRMService* g_pServiceHandler = new CRMService();
//     EXPECT_NO_THROW( g_pServiceHandler->SetLogFileName()) ;
// }

// TEST(CRMServiceTEST,CRMServiceInteractiveLoopTEST){
//     CRMService* g_pServiceHandler = new CRMService();
//      EXPECT_NO_THROW( g_pServiceHandler->InteractiveLoop()) ;
// }

// TEST(CRMServiceTEST,CRMServiceLoadConfigTEST){
//     CRMService* g_pServiceHandler = new CRMService();
//     long val1 = g_pServiceHandler->LoadConfig();
//      EXPECT_EQ(val1,0) << "Expected and actual values do not match val1:" << val1;
// }

// TEST(CRMServiceTEST,CRMServiceParseOptionsTEST){
// CRMService* g_pServiceHandler = new CRMService();
//     int argc =2;
//     char* args[] ={"test1","test2"};
//     long val1 = g_pServiceHandler->ParseOptions(argc,args);
//     EXPECT_EQ(val1,0)<< "Expected and actual values do not match val1:" << val1;

// }




//class data_defconfig test

class data_defconfigTest1 : public ::testing::Test{
protected: //ģ�⹹��
    //std::shared_ptr<data_defconfig> p_def;
    data_defconfig* p_def;
    void SetUp() override{
    }
    //ģ������
     void TearDown() override{
       p_def = nullptr;
    }
};

//�����쳣·��
TEST_F(data_defconfigTest1,parsedata_defconfig1Test){
    EXPECT_THROW(p_def->parsedata_defconfig(""), std::invalid_argument) << "invaild path";;
}
//����xml��û��Data��ǩ
TEST_F(data_defconfigTest1,parsedata_defconfig2Test){
    char currentPath[FILENAME_MAX];
    ssize_t count = readlink("/proc/self/exe", currentPath, sizeof(currentPath));
    ASSERT_TRUE(count != -1) << "Error getting current executable path";
    currentPath[count] = '\0'; // Null-terminate the path
    snprintf(currentPath + (strrchr(currentPath, '/') - currentPath), sizeof(currentPath) - (strrchr(currentPath, '/') - currentPath), "/../config/DataDefinition1.xml");
    const std::string filePath = currentPath;
    EXPECT_THROW(p_def->parsedata_defconfig(filePath), std::invalid_argument) << "invaild path";
}


//������ȷ·��
TEST(data_defconfigTest,getInstance1Test){
    char currentPath[FILENAME_MAX];
    ssize_t count = readlink("/proc/self/exe", currentPath, sizeof(currentPath));
    ASSERT_TRUE(count != -1) << "Error getting current executable path";
    currentPath[count] = '\0'; // Null-terminate the path
    snprintf(currentPath + (strrchr(currentPath, '/') - currentPath), sizeof(currentPath) - (strrchr(currentPath, '/') - currentPath), "/../config/DataDefinitionDataService.xml");
    const std::string filePath = currentPath;
    auto p_conf =  data_defconfig::getInstance(filePath);
    p_conf->getGlobalModel()->printObject();
    //EXPECT_NE(p_conf->GlobeObjects,nullptr);
    EXPECT_NE(p_conf,nullptr);
}
//�����쳣·��
TEST(data_defconfigTest,getInstance2Test){
    EXPECT_THROW(data_defconfig::getInstance(""), std::invalid_argument);
}


//��������·��
TEST(data_defconfigTest,getInstanceTest){
    char currentPath[FILENAME_MAX];
    ssize_t count = readlink("/proc/self/exe", currentPath, sizeof(currentPath));
    ASSERT_TRUE(count != -1) << "Error getting current executable path";
    currentPath[count] = '\0'; // Null-terminate the path
    snprintf(currentPath + (strrchr(currentPath, '/') - currentPath), sizeof(currentPath) - (strrchr(currentPath, '/') - currentPath), "/../config/DataDefinition1.xml");
    const std::string filePath = currentPath;
    auto config = data_defconfig::getInstance(filePath) ;
    auto model = config->getGlobalModel();
    EXPECT_NE(model, nullptr) << "getGlobalModel returned a null pointer.";
    
    // ���Է��صĶ���״̬
    EXPECT_EQ(model->name, "root") << "The name of the global model object is not 'root'.";

    // ���Բ����ԣ���ε��÷�����ͬ�Ķ���
    auto model2 = config->getGlobalModel();
    EXPECT_EQ(model, model2) << "getGlobalModel did not return the same instance on multiple calls.";
}


//class data_modelconfig
class data_modelconfig1Test: public ::testing::Test{
protected: 
    data_modelconfig* p_def;
    //ģ�⹹��
    void SetUp() override{
    }
    //ģ������
     void TearDown() override{
       p_def = nullptr;
    }

};

//����ļ���������
//�ļ�·����ȷ���ļ�������������
TEST(data_modelconfigTest,getInstanceTest1){
    char currentPath[FILENAME_MAX];
    ssize_t count = readlink("/proc/self/exe", currentPath, sizeof(currentPath));
    ASSERT_TRUE(count != -1) << "Error getting current executable path";
    currentPath[count] = '\0'; // Null-terminate the path
    snprintf(currentPath + (strrchr(currentPath, '/') - currentPath), sizeof(currentPath) - (strrchr(currentPath, '/') - currentPath), "/../config/ModelDefinitionDataService.xml");
    const std::string filePath = currentPath;
    auto p_ptr = data_modelconfig::getInstance(filePath);
    p_ptr->getGlobalModel()->print();
    EXPECT_NE(p_ptr,nullptr) ;
}
//�ļ�·������ȷ,���ؿ�ָ��
TEST(data_modelconfigTest,getInstanceTest2){
   
    EXPECT_THROW(data_modelconfig::getInstance(""), std::invalid_argument);
}


class data_nodeconfig1Test :public ::testing::Test{
protected: 
    data_nodeconfig* p_def;
    //ģ�⹹��
    void SetUp() override{
    }
    //ģ������
     void TearDown() override{
       p_def = nullptr;
    }
};

//����ļ���������
//�ļ�·����ȷ���ļ�������������
TEST(data_nodeconfigTest,getInstanceTest1){
    char currentPath[FILENAME_MAX];
    ssize_t count = readlink("/proc/self/exe", currentPath, sizeof(currentPath));
    ASSERT_TRUE(count != -1) << "Error getting current executable path";
    currentPath[count] = '\0'; // Null-terminate the path
    snprintf(currentPath + (strrchr(currentPath, '/') - currentPath), sizeof(currentPath) - (strrchr(currentPath, '/') - currentPath), "/../config/DataDefinitionDataService.xml");
    const std::string sz_configFilePath = currentPath;
    auto p_ptr = data_nodeconfig::getInstance(sz_configFilePath);
    EXPECT_NE(p_ptr,nullptr) ;
    p_ptr->getNodeInfo().print();
    
}
//�ļ�·������ȷ�����ؿ�ָ��
TEST(data_nodeconfigTest,getInstanceTest2){
    EXPECT_THROW(data_nodeconfig::getInstance(""), std::invalid_argument);
}

// //Object
// //���Ի�ȡ�������Ƿ�ɹ�
// TEST_F(data_defconfigTest,getGlobalModelTest){
//     auto p_ptr = p_def->getGlobalModel();
//     //���صĲ��ǿ�ָ���������ȡ�ɹ�
//     EXPECT_NE(p_ptr,nullptr) ;
// }

class ObjectTest : public ::testing::Test{
 protected:
    Object* obj;
    std::shared_ptr<Tag> tag;
    std::shared_ptr<Object> model;
 //ģ�⹹��
    void SetUp() override{
        //model=new Object("LSP_F1");
    }
    //ģ������
     void TearDown() override{
       obj = nullptr;
        model= nullptr;;
    }
};

//addTag��addObject ������ȡ��
/*
    ��������Tag�㹦�ܣ����ڴ���˽�����ԣ����Խ��ͨ�����ô�ӡ����ȷ��
*/
TEST_F(ObjectTest,addTagTest){
   obj = new Object("LSP_F1");
   tag= std::make_shared<Tag>("LSP_F1","DI");
   EXPECT_NO_THROW(obj->addTag("LSP_F1",tag));
   obj = new Object("LSP_F2","2�Ż���");
   tag= std::make_shared<Tag>("LSP_F2","DI","2�Ż���","F1F2","LSP", 1);
   obj->printObject();

}
/*
    ��������model�����ܣ����ڴ���˽�����ԣ����Խ��ͨ�����ô�ӡ����ȷ��
*/
TEST_F(ObjectTest,addObjectTest){
   obj = new Object("LSP_F1");
   tag= std::make_shared<Tag>("LSP_F1","DI");
   model= std::make_shared<Object>("LSP_F1");
   //EXPECT_NO_THROW(obj->addTag("LSP_F1",tag));
   EXPECT_NO_THROW(obj->addObject("LSP_F1",model));
   obj->printObject();
}


//redis ����
TEST(data_redisTest,dataBinGetTest1){
    
}


// //data_modelconfig test
// class ModelTest : public ::testing::Test{
// protected:
//     Model* model;
//     //std::shared_ptr<Model> model;
// void SetUp() override{
//         model=new Model("LSP_F1");
//     }
//     //ģ������
//      void TearDown() override{
//         delete model;
//     }
// };

// //����findTagInModelû���ҵ��᷵��nullptr���ҵ���Ϊ�ǿ�
// //δ�ҵ�������model name ������
// TEST_F(ModelTest,findTagInModelTest1){
//     std::shared_ptr<Tag> tagptr = std::make_shared<Tag>("LSP","LSP");
//     model->addTag("LSP",tagptr);
//      std::shared_ptr<Model> modelptr= std::make_shared<Model>("LSP_F1");
//      model->addModel("LSP_F1",modelptr);
//     auto ptr = model->findTagInModel("LSP","LSP");
//      EXPECT_EQ(ptr,nullptr) ;
// }
// //�ҵ�������model name ���ڣ�tag ����
// TEST_F(ModelTest,findTagInModelTest2){
//     std::shared_ptr<Tag> tagptr = std::make_shared<Tag>("LSP","LSP");
//     model->addTag("LSP",tagptr);
//      std::shared_ptr<Model> modelptr= std::make_shared<Model>("LSP");
//      model->addModel("LSP_F1",modelptr);
//     auto ptr = model->findTagInModel("LSP_F1","LSP");
//      EXPECT_NE(ptr,nullptr) ;
// }
// //δ�ҵ�������model name ���ڣ�Tag������
// TEST_F(ModelTest,findTagInModelTest3){
//     std::shared_ptr<Tag> tagptr = std::make_shared<Tag>("LSP","LSP");
//     model->addTag("LSP",tagptr);
//      std::shared_ptr<Model> modelptr= std::make_shared<Model>("LSP_F1");
//      model->addModel("LSP",modelptr);
//     auto ptr = model->findTagInModel("LSP","LSP_F1");
//      EXPECT_EQ(ptr,nullptr) ;
// }
// //δ�ҵ�������model name �����ڣ�tag ������
// TEST_F(ModelTest,findTagInModelTest4){
//     std::shared_ptr<Tag> tagptr = std::make_shared<Tag>("LSP","LSP");
//     model->addTag("LSP",tagptr);
//     std::shared_ptr<Model> modelptr= std::make_shared<Model>("LSP_F1");
//     model->addModel("LSP",modelptr);
//     auto ptr = model->findTagInModel("SP","LSP1");
//      EXPECT_EQ(ptr,nullptr) ;
// }
// //�ҵ�����������ֵ��Ϊnullptr
// TEST_F(ModelTest,findModelByNameTest1){
//     std::shared_ptr<Tag> tagptr = std::make_shared<Tag>("LSP","LSP");
//     model->addTag("LSP",tagptr);
//     std::shared_ptr<Model> modelptr= std::make_shared<Model>("LSP_F1");
//     model->addModel("LSP_F1",modelptr);
//     auto ptr = model->findModelByName("LSP_F1");
//     EXPECT_NE(ptr,nullptr) ;
// }
// //Ϊ�ҵ�����������ֵΪnullptr
// TEST_F(ModelTest,findModelByNameTest2){
//     std::shared_ptr<Tag> p_tagptr = std::make_shared<Tag>("LSP","LSP");
//     model->addTag("LSP",p_tagptr);
//     std::shared_ptr<Model> p_modelptr= std::make_shared<Model>("LSP_F1");
//     model->addModel("LSP",p_modelptr);
//     auto p_ptr = model->findModelByName("SP");
//      EXPECT_EQ(p_ptr,nullptr) ;
// }


// //����ļ���������
// //�ļ�·����ȷ���ļ�������������
// TEST(data_modelconfigTest,getInstanceTest1){
//     auto p_ptr = data_modelconfig::getInstance("../config/ModelDefinition.xml");
//     p_ptr->getGlobalModel()->print();
//     EXPECT_NE(p_ptr,nullptr) ;
// }
// //�ļ�·������ȷ,���ؿ�ָ��
// TEST(data_modelconfigTest,getInstanceTest2){
//     auto p_ptr = data_modelconfig::getInstance("");
//     EXPECT_EQ(p_ptr,nullptr) ;
// }

// //data_nodeconfig test
// //����ļ���������
// //�ļ�·����ȷ���ļ�������������
// TEST(data_nodeconfigTest,getInstanceTest1){
//     const std::string sz_configFilePath = "../config/DataDefinition.xml";
//     auto p_ptr = data_nodeconfig::getInstance(sz_configFilePath);
//     EXPECT_NE(p_ptr,nullptr) ;
//     p_ptr->getNodeInfo().print();
    
// }
// //�ļ�·������ȷ�����ؿ�ָ��
// TEST(data_nodeconfigTest,getInstanceTest2){
//     const std::string sz_configFilePath = "";
//     auto p_ptr = data_nodeconfig::getInstance(sz_configFilePath);
//     EXPECT_EQ(p_ptr,nullptr) ;
//     //ptr->getNodeInfo().print();
    
// }



#if 0
int main(int argc, char **argv) {
	    ::testing::InitGoogleTest(&argc, argv);
	    return RUN_ALL_TESTS();
}
#endif
