/**
 * @file config.h
 * @brief
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2024-08-27
 *
 * @copyright Copyright (c) 2024  by  宝信
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2024-08-27     <td>1.0     <td>wwk   <td>修改?
 * </table>
 */

#pragma once

#include <iostream>
#include <memory>
#include <string>
#include <mutex>
#include <vector>
#include <string>
#include <glog/logging.h>

struct Driver
{
    std::string name;
    std::string path;
};

struct NodeConfig
{
    std::string name;
    std::string ipAddr;
    std::vector<std::string> ddsSubTopics;
    std::vector<std::string> ddsPubTopics;
    std::vector<Driver> drivers;

    void print()
    {

        std::cout << "Node Name: " << name << std::endl;
        std::cout << "IP Address: " << ipAddr << std::endl;

        std::cout << "DDS Subscribed Topics:" << std::endl;
        for (const auto &topic : ddsSubTopics)
        {
            std::cout << " - " << topic << std::endl;
        }

        std::cout << "DDS Published Topics:" << std::endl;
        for (const auto &topic : ddsPubTopics)
        {
            std::cout << " - " << topic << std::endl;
        }

        std::cout << "Drivers:" << std::endl;
        for (const auto &driver : drivers)
        {
            std::cout << " - Name: " << driver.name << ", Path: " << driver.path << std::endl;
        }
    }
};

class data_nodeconfig
{
public:
    static std::shared_ptr<data_nodeconfig> getInstance(const std::string &filePath = "")
    {
        if ("" == filePath)
        {
            LOG(ERROR) << "No config file path provided, using default.";
            return nullptr;
        }

        std::call_once(initFlag, [&]
                       { instance.reset(new data_nodeconfig(filePath)); });
        return instance;
    }

    ~data_nodeconfig() {
    };

    NodeConfig getNodeInfo() const
    {
        return nodeInfo;
    }
private:
    data_nodeconfig(const std::string &filePath)
    {
       parsedata_nodeconfig(filePath);
    }

    void parsedata_nodeconfig(const std::string &filePath)
    {
        XMLDocument doc;
        XMLError result = doc.LoadFile(filePath.c_str());
        if (result != XML_SUCCESS)
        {
            LOG(ERROR) << "Failed to load parsedata_nodeconfig file: " << filePath << " Error loading XML file: " << doc.ErrorName();
            return;
        }

        XMLElement *dsfNode = doc.FirstChildElement("DSFNode");
        if (dsfNode != nullptr)
        {
            nodeInfo.name = dsfNode->Attribute("name") ? dsfNode->Attribute("name") : "";
            nodeInfo.ipAddr = dsfNode->Attribute("IPAddr") ? dsfNode->Attribute("IPAddr") : "";

            // 解析 DDSSub 节点
            XMLElement *ddsSub = dsfNode->FirstChildElement("DDSSub");
            if (ddsSub)
            {
                for (XMLElement *topic = ddsSub->FirstChildElement("topic"); topic; topic = topic->NextSiblingElement("topic"))
                {
                    const char *topicName = topic->Attribute("name");
                    if (topicName)
                    {
                        nodeInfo.ddsSubTopics.push_back(topicName);
                    }
                }
            }

            // 解析 DDSPub 节点
            XMLElement *ddsPub = dsfNode->FirstChildElement("DDSPub");
            if (ddsPub)
            {
                for (XMLElement *topic = ddsPub->FirstChildElement("topic"); topic; topic = topic->NextSiblingElement("topic"))
                {
                    const char *topicName = topic->Attribute("name");
                    if (topicName)
                    {
                        nodeInfo.ddsPubTopics.push_back(topicName);
                    }
                }
            }

            // 解析 driver 节点
            XMLElement *driver = dsfNode->FirstChildElement("driver");
            if (driver)
            {
                for (XMLElement *drv = driver->FirstChildElement("driver"); drv; drv = drv->NextSiblingElement("driver"))
                {
                    const char *drvName = drv->Attribute("name");
                    const char *drvPath = drv->Attribute("path");
                    if (drvName && drvPath)
                    {
                        nodeInfo.drivers.push_back({drvName, drvPath});
                    }
                }
            }
        }
    }

    static std::shared_ptr<data_nodeconfig> instance;
    static std::once_flag initFlag;

    NodeConfig nodeInfo;
};

std::shared_ptr<data_nodeconfig> data_nodeconfig::instance;
std::once_flag data_nodeconfig::initFlag;
