cmake_minimum_required(VERSION 2.6)

PROJECT (rmservice)

INCLUDE($ENV{DualiCVSRCDIR}CMakeCommon)

############FOR_MODIFIY_BEGIN#######################
#Setting Source Files
SET(SRCS ${SRCS} test_servicebase)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME rmservice)
#Setting library type used when build a library
SET(LIB_TYPE SHARED)

SET(LINK_LIBS  cv6log cv6logimpl ACE cv6comm cppsqlite iconv tinyxml servicebase licverify License intl iconv shmqueue cv6netqueue)

IF(UNIX)
	if( CMAKE_SIZEOF_VOID_P EQUAL 4 )
		IF(CMAKE_SYSTEM MATCHES "Linux")
			SET(LINK_LIBS ${LINK_LIBS} SentinelKeys32)
			SET (CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -lnsl -lresolv ")
			SET (CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -lnsl -lresolv ")
		ENDIF(CMAKE_SYSTEM MATCHES "Linux")
	endif( CMAKE_SIZEOF_VOID_P EQUAL 4 )
	SET(LINK_LIBS ${LINK_LIBS} rt)
	IF(CMAKE_SYSTEM MATCHES "SunOS.*")
		SET(LINK_LIBS ${LINK_LIBS}  socket)
		SET (CMAKE_C_FLAGS "${CMAKE_C_FLAGS} -lnsl -lsocket -lresolv ")
		SET (CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -lnsl -lsocket -lresolv ")
	ENDIF(CMAKE_SYSTEM MATCHES "SunOS.*")
	IF(HPUX)
		SET(LINK_LIBS ${LINK_LIBS} pthread iconv)
	ENDIF(HPUX)
	SET (LINK_LIBS  ${LINK_LIBS} pthread)
	SET(LINK_LIBS  ${LINK_LIBS} boost_thread boost_system)
ENDIF(UNIX)

SET(SRCS ${SRCS} test_servicebase1)
SET(TARGET_NAME rmservicetest)

#MESSAGE(${LINK_LIBS})

#SET(CMAKE_CFG_INTDIR "")

#Setting Executable and Library Output Path
#SET(EXECUTABLE_OUTPUT_PATH ../../../${EXE_DIR})
#SET(LIBRARY_OUTPUT_PATH ../../../${LIB_DIR})

#Setting Include Directorys
#INCLUDE_DIRECTORIES(../../../Include)

#Setting Link Directorys
#LINK_DIRECTORIES(../../../${LIB_DIR})
############FOR_MODIFIY_END#########################

INCLUDE($ENV{DualiCVSRCDIR}CMakeCommonExec)
IF(MSVC)
	if( CMAKE_SIZEOF_VOID_P EQUAL 8 )
		set_target_properties(${TARGET_NAME} PROPERTIES STATIC_LIBRARY_FLAGS "/machine:x64")
	endif( CMAKE_SIZEOF_VOID_P EQUAL 8 )	
ENDIF(MSVC)


