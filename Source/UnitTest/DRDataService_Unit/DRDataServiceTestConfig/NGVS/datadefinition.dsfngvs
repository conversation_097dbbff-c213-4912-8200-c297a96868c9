{"UDT": [{"Name": "STD.MODEL_2", "Members": [{"MemberName": "M1", "MemberType": "STD.MODEL_1", "Offset": 0, "Length": 4}, {"MemberName": "I1", "MemberType": "INT", "Offset": 4, "Length": 2}], "Length": 8}, {"Name": "STD.MODEL_1", "Members": [{"MemberName": "T1", "MemberType": "INT", "Offset": 0, "Length": 2}, {"MemberName": "T2", "MemberType": "INT", "Offset": 2, "Length": 2}], "Length": 4}], "LocalVariables": [{"var_name": "STD.MV_1_M1_T1", "type": "INT", "DBName": "", "DBid": 1}, {"var_name": "STD.MV_1_M1_T2", "type": "INT", "DBName": "", "DBid": 2}, {"var_name": "STD.MV_1_I1", "type": "INT", "DBName": "", "DBid": 3}, {"var_name": "STD.MV_2_M1_T1", "type": "INT", "DBName": "", "DBid": 4}, {"var_name": "STD.MV_2_M1_T2", "type": "INT", "DBName": "", "DBid": 5}, {"var_name": "STD.MV_2_I1", "type": "INT", "DBName": "", "DBid": 6}, {"var_name": "STD.MV_3_M1_T1", "type": "INT", "DBName": "", "DBid": 7}, {"var_name": "STD.MV_3_M1_T2", "type": "INT", "DBName": "", "DBid": 8}, {"var_name": "STD.MV_3_I1", "type": "INT", "DBName": "", "DBid": 9}, {"var_name": "STD.MARRAY_WORD_1", "type": "WORD", "DBName": "", "DBid": 10}, {"var_name": "STD.MARRAY_WORD_2", "type": "WORD", "DBName": "", "DBid": 11}, {"var_name": "STD.MARRAY_WORD_3", "type": "WORD", "DBName": "", "DBid": 12}], "driver": [{"driver_name": "codesys", "path": "./drivers/codesys.xml"}, {"driver_name": "dsftxdrv", "path": "./drivers/dsftxdrv.xml"}], "LocalObjects": [{"Name": "STD.MV", "TypeOf": "ARRAY", "ElementType": "{\"Name\":\"STD.MODEL_2\",\"Range\":[{\"start\":0,\"end\":2}]}", "Length": 24}, {"Name": "STD.MARRAY_WORD", "TypeOf": "ARRAY", "ElementType": "{\"Name\":\"WORD\",\"Range\":[{\"start\":0,\"end\":2}]}", "Length": 6}, {"Name": "STD.INT_TEST", "TypeOf": "INT", "Length": 2}]}