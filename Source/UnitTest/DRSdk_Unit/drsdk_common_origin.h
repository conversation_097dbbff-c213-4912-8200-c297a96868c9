/*  Filename:    drsdk_common.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Define  common  structure, function and macro
 *
 *  @author:     wuzheqiang
 *	@version     09/10/2024	wuzheqiang	Initial Version
 **************************************************************/
#ifndef DRSDK_COMMON_H
#define DRSDK_COMMON_H

#include "data_types.h"
#include <chrono>
#include <ctime>
#include <iomanip>
#include <iostream>
#include <stdint.h>
#include <string.h>
#include <string>

namespace dsfapi
{
/*get cur second time_stamp*/
inline uint32 NowSecond()
{
    using namespace std::chrono;
    auto now = system_clock::now();

    auto seconds_since_epoch = duration_cast<seconds>(now.time_since_epoch()).count();

    return static_cast<uint32>(seconds_since_epoch);
}

/*get cur microseconds time_stamp str*/
inline std::string CurrentTimestampMicro()
{
    using namespace std::chrono;
    auto now = system_clock::now();

    auto in_time_t = system_clock::to_time_t(now);
    auto us = duration_cast<microseconds>(now.time_since_epoch()) % 1000000;
    std::tm buf;
    localtime_r(&in_time_t, &buf);
    std::ostringstream oss;
    oss << std::put_time(&buf, "%Y-%m-%d %H:%M:%S");
    oss << '.' << std::setfill('0') << std::setw(6) << us.count();
    return oss.str();
}

#define FILENAME (std::string(__FILE__).substr(std::string(__FILE__).find_last_of("/\\") + 1))

#define LOG_INFO std::cout << "[" << dsfapi::CurrentTimestampMicro() << "][" << FILENAME << ":" << __LINE__ << "]"
#define LOG_ERR std::cerr << "[" << dsfapi::CurrentTimestampMicro() << "][" << FILENAME << ":" << __LINE__ << "]"

enum SDKReqType : uint8
{
    SDK_CONTROL_DATA = 1,
    SDK_READ_DATA = 2,
    SDK_REG_TAG = 3,
    SDK_REG_TAG_DATA = 4,
};
struct DataHeader
{
    uint8 nType = 0; // request type. use SDKReqType
    uint8 nFlag = 0; // result flag. 0 success, 1 fail
    uint16 nLen = 0; // content data len
    uint32 nSeq = 0; // for async request

    std::string ToString()
    {
        std::stringstream ss;
        ss << "nType:" << (int)nType << ", nFlag:" << (int)nFlag << ", nLen:" << (int)nLen << ", nSeq:" << nSeq;
        return ss.str();
    }
};

struct DRSdkConfig
{
    std::string port;
};
} // namespace dsfapi
#endif