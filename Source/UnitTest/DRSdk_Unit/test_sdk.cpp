#include <iostream>
#include <string>
#include <vector>
#include <gtest/gtest.h>
// #include <gmock/gmock.h>
#include <thread>
#include <chrono>
#include <atomic>
#include <stdexcept>
#include "thread_pool.h"
#include "drsdk_common.h"
// #include "drsdk_manager.cpp"
// #include "drsdk_transport.h"


//对线程池 dsfapi/thread_pool.h的单元测试?
class ThreadPoolEnqueueTest : public ::testing::Test {
protected:
    void SetUp() override {
        pool = std::make_unique<dsfapi::ThreadPool>(4); // 创建一个包含4线程的线程池
    }

    void TearDown() override {
        pool.reset();
    }

    std::unique_ptr<dsfapi::ThreadPool> pool;
};

// 测试能否成功添加和执行简单任务?
TEST_F(ThreadPoolEnqueueTest, EnqueueSimpleTask) {
    auto future = pool->Enqueue([]() { return 1; });
    EXPECT_EQ(future.get(), 1);
}

// 测试带参数的任务
TEST_F(ThreadPoolEnqueueTest, EnqueueTaskWithArguments) {
    auto future = pool->Enqueue([](int a, int b) { return a + b; }, 1, 2);
    EXPECT_EQ(future.get(), 3);
}

// 测试无返回值的任务
TEST_F(ThreadPoolEnqueueTest, EnqueueVoidTask) {
    bool taskExecuted = false;
    auto future = pool->Enqueue([&taskExecuted]() { taskExecuted = true; });
    future.wait();
    EXPECT_TRUE(taskExecuted);
}

// 测试抛出异常的任务?
TEST_F(ThreadPoolEnqueueTest, EnqueueThrowingTask) {
    auto future = pool->Enqueue([]() -> int { throw std::runtime_error("Test exception"); });
    EXPECT_THROW(future.get(), std::runtime_error);
}

// 测试多个任务
TEST_F(ThreadPoolEnqueueTest, EnqueueMultipleTasks) {
    std::vector<std::future<int>> futures;
    for (int i = 0; i < 10; ++i) {
        futures.push_back(pool->Enqueue([i]() { return i * i; }));
    }

    for (int i = 0; i < 10; ++i) {
        EXPECT_EQ(futures[i].get(), i * i);
    }
}

// 测试停止后的行为
TEST_F(ThreadPoolEnqueueTest, EnqueueAfterStop) {
    pool->m_bStop.store(1);
    pool->m_QueueCv.notify_all();
    for (std::thread &worker : pool->m_vecWorkers)
    {
        worker.join();
    }
    auto future = pool->Enqueue([]() { return 42; });
    EXPECT_FALSE(future.valid());
}

// 测试长时间运行的任务
TEST_F(ThreadPoolEnqueueTest, EnqueueLongRunningTask) {
    auto start = std::chrono::steady_clock::now();
    auto future = pool->Enqueue([]() {
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
        return 4;
    });
    EXPECT_EQ(future.get(), 4);
    auto end = std::chrono::steady_clock::now();
    EXPECT_GE(std::chrono::duration_cast<std::chrono::milliseconds>(end - start).count(), 100);
}

//drsdk_manager.cpp中SplitString（）测试

class SplitStringTest : public ::testing::Test {
protected:
    void SetUp() override {
        // 如果需要在每个测试用例之前进行一些设置，可以在这里添加代码
    }

    void TearDown() override {
        // 如果需要在每个测试用例之后进行一些清理，可以在这里添加代码
    }
};

TEST_F(SplitStringTest, BasicSplit) {
    std::vector<std::string> result;
    dsfapi::SplitString("apple@@banana@@cherry", "@@", &result);
    
    ASSERT_EQ(result.size(), 3);
    EXPECT_EQ(result[0], "apple");
    EXPECT_EQ(result[1], "banana");
    EXPECT_EQ(result[2], "cherry");
}

TEST_F(SplitStringTest, EmptyString) {
    std::vector<std::string> result;
    dsfapi::SplitString("", "@@", &result);
    
    EXPECT_TRUE(result.empty());
}

TEST_F(SplitStringTest, NoDelimiter) {
    std::vector<std::string> result;
    dsfapi::SplitString("hello", "@@", &result);
    
    ASSERT_EQ(result.size(), 1);
    EXPECT_EQ(result[0], "hello");
}

TEST_F(SplitStringTest, ConsecutiveDelimiters) {
    std::vector<std::string> result;
    dsfapi::SplitString("a@@@@b@@@@@@c", "@@", &result);
    
    ASSERT_EQ(result.size(), 3);
    EXPECT_EQ(result[0], "a");
    EXPECT_EQ(result[1], "b");
    EXPECT_EQ(result[2], "c");
}

TEST_F(SplitStringTest, DelimiterAtStart) {
    std::vector<std::string> result;
    dsfapi::SplitString("@@start@@middle@@end", "@@", &result);
    
    ASSERT_EQ(result.size(), 3);
    EXPECT_EQ(result[0], "start");
    EXPECT_EQ(result[1], "middle");
    EXPECT_EQ(result[2], "end");
}

TEST_F(SplitStringTest, DelimiterAtEnd) {
    std::vector<std::string> result;
    dsfapi::SplitString("start@@middle@@end@@", "@@", &result);
    
    ASSERT_EQ(result.size(), 3);
    EXPECT_EQ(result[0], "start");
    EXPECT_EQ(result[1], "middle");
    EXPECT_EQ(result[2], "end");
}




// //对drsdk/drsdk_manager.cpp的测�?
// //Instance()
// TEST_F(DRSdkManagerTest, SingletonInstanceTest) {
//     dsfapi::DRSdkManager* instance1 = dsfapi::DRSdkManager::Instance();
//     dsfapi::DRSdkManager* instance2 = dsfapi::DRSdkManager::Instance();
//     ASSERT_NE(nullptr, instance1);
//     ASSERT_EQ(instance1, instance2);
// }


// class MockDRSdkManager : public dsfapi::DRSdkManager {
// public:
//     MOCK_METHOD3(WriteToSendBuffer, int32(const dsfapi::DataHeader&, const std::string&, size_t));
//     // 暴露 protected 成员以便测试
//     using DRSdkManager::m_mapRequest;
//     using DRSdkManager::m_nHeadSeq;
// };

// class DRSdkManagerTest : public ::testing::Test {
// protected:
//     std::unique_ptr<MockDRSdkManager> manager;

//     void SetUp() override {
//         manager = std::make_unique<MockDRSdkManager>();
//     }
// };

// TEST_F(DRSdkManagerTest, DrControlDataSuccess) {
//     // 准�?�测试数�?
//     std::string strTagName = "TestTag";
//     std::string strValue = "TestValue";
//     uint8 nWriteMode = 1;

//     // 模拟 WriteToSendBuffer 的�?�为
//     EXPECT_CALL(*manager, WriteToSendBuffer(::testing::_, ::testing::_, ::testing::_))
//         .WillOnce(::testing::Return(0));

//     // 模拟异�?�操作的结果
//     std::promise<std::string> promise;
//     promise.set_value("0"); // 设置成功结果
//     auto now = std::chrono::steady_clock::now();
//     dsfapi::SharedPromise sharedPromise;
//     dsfapi::SharedFuture sharedFuture = promise.get_future();
//     uint32 someValue = 0; // 或其他适当的�?
//     manager->m_mapRequest[0] = std::make_tuple(sharedPromise, sharedFuture, someValue);
//     // 调用�?测函�?
//     int32 result = manager->DrControlData(strTagName, strValue, nWriteMode);

//     // 验证结果
//     EXPECT_EQ(result, 0);
// }

// TEST_F(DRSdkManagerTest, DrControlDataWriteFailure) {
//     std::string strTagName = "TestTag";
//     std::string strValue = "TestValue";
//     uint8 nWriteMode = 1;

//     // 模拟 WriteToSendBuffer 失败
//     EXPECT_CALL(*manager, WriteToSendBuffer(::testing::_, ::testing::_, ::testing::_))
//         .WillOnce(::testing::Return(-1));

//     int32 result = manager->DrControlData(strTagName, strValue, nWriteMode);

//     EXPECT_EQ(result, -1);
// }

// TEST_F(DRSdkManagerTest, DrControlDataTimeout) {
//     std::string strTagName = "TestTag";
//     std::string strValue = "TestValue";
//     uint8 nWriteMode = 1;

//     EXPECT_CALL(*manager, WriteToSendBuffer(::testing::_, ::testing::_, ::testing::_))
//         .WillOnce(::testing::Return(0));

//     // 模拟超时情况
//     std::promise<std::string> promise;
//     auto future = promise.get_future();
//     manager->m_mapRequest[0] = std::make_tuple(std::chrono::steady_clock::now(), std::move(future));

//     int32 result = manager->DrControlData(strTagName, strValue, nWriteMode);

//     EXPECT_EQ(result, -1);
// }

// TEST_F(DRSdkManagerTest, DrControlDataNonZeroResponse) {
//     std::string strTagName = "TestTag";
//     std::string strValue = "TestValue";
//     uint8 nWriteMode = 1;

//     EXPECT_CALL(*manager, WriteToSendBuffer(::testing::_, ::testing::_, ::testing::_))
//         .WillOnce(::testing::Return(0));

//     // 模拟非零响应
//     std::promise<std::string> promise;
//     promise.set_value("1"); // 非零响应
//     manager->m_mapRequest[0] = std::make_tuple(std::chrono::steady_clock::now(), promise.get_future());

//     int32 result = manager->DrControlData(strTagName, strValue, nWriteMode);

//     EXPECT_EQ(result, -1);
// }



#if 0
int main(int argc, char **argv) {
	    ::testing::InitGoogleTest(&argc, argv);
	    return RUN_ALL_TESTS();
}
#endif