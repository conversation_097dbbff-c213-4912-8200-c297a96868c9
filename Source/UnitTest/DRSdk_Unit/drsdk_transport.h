/*  Filename:    drsdk_manager.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Define DrSdkTransport, use share memory to communicate with DataService.
 *
 *  @author:     wuzheqiang
 *	@version     09/10/2024	wuzheqiang	Initial Version
 **************************************************************/

#ifndef DRSDK_TRANSPORT_H
#define DRSDK_TRANSPORT_H
#include "drsdk_common.h"
#include "hdProcComm.h"
#include <mutex>

namespace dsfapi
{
class DrSdkTransport
{
  public:
    DrSdkTransport();
    virtual ~DrSdkTransport();

    /**
     * @brief		init DrSdkTransport
     * @return		RD_SUCCESS if success
     * @version		09/10/2024	wuzheqiang	Initial Version
     */
    int32 Init();

    /**
     * @brief		Close shm and queue
     * @return		RD_SUCCESS if success
     * @version		09/10/2024	wuzheqiang	Initial Version
     */
    int32 Close();

    /**
     * @brief		Send data to DataService
     * @param [in]	pSendBuf  buffer to send
     * @param [in]	nLenBuf   buffer length
     * @return		RD_SUCCESS if success
     * @version		09/10/2024	wuzheqiang	Initial Version
     */
    int32 SendData(const char *pSendBuf, int32 nLenBuf);

    /**
     * @brief		Send data to DataService
     * @param [in]	pSendBuf  buffer to recv
     * @param [in]	nLenBuf   received length
     * @return		RD_SUCCESS if success
     * @version		09/10/2024	wuzheqiang	Initial Version
     */
    int32 RecvData(char *pRecvBuf, int32 &pBufLen);

    int32 TestSendData(const char *pSendBuf, int32 nLenBuf);
    int32 TestRecvData(char *pRecvBuf, int32 &pBufLen);

  private:
    ProcCommHandle m_hProcComm = nullptr;
    ProcCommQueueHandle m_hDrsdkSendToDs = nullptr;
    ProcCommQueueHandle m_hDrsdkRecvFromDs = nullptr;
    std::mutex m_SendMutex;
    std::mutex m_RecvMutex;
};
} // namespace dsfapi
#endif