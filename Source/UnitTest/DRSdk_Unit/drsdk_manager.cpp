/*  Filename:    drsdk_manager.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Define DRSdkManager, It is a singleton, managing the processing of data
 *
 *  @author:     wuzheqiang
 *  @version:    09/10/2024	wuzheqiang	Initial Version
 **************************************************************/

#include "drsdk_manager.h"
#include "string.h"
namespace dsfapi
{
namespace
{
constexpr uint32 BUFFER_MAX_LENGTH = 1024;
constexpr uint32 SEND_BUFFER_MAX_LENGTH = BUFFER_MAX_LENGTH;
constexpr uint32 RECV_BUFFER_MAX_LENGTH = BUFFER_MAX_LENGTH;
constexpr uint32 MAX_THREAD_NUM = 10;
constexpr char ksDivideMark[] = "@@";
} // namespace

DRSdkManager *DRSdkManager::m_pInstance = nullptr;
std::mutex DRSdkManager::m_InstanceMtx;

DRSdkManager::~DRSdkManager()
{
}
/**
 * @brief		init DRSdkManager
 * @return		RD_SUCCESS if success
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
int32 DRSdkManager::Init()
{
    int32 nRet = 0;
    LOG_INFO << " DRSdkManager::Init()" << std::endl;
    m_pTransport = std::make_unique<DrSdkTransport>();
    nRet = m_pTransport->Init();
    if (0 != nRet)
    {
        LOG_ERR << "m_pTransport->Init() failed" << std::endl;
        return nRet;
    }

    m_pSendBuffer.reset(new char[SEND_BUFFER_MAX_LENGTH]);
    m_pRecvBuffer.reset(new char[RECV_BUFFER_MAX_LENGTH]);
    if (!m_pSendBuffer || !m_pRecvBuffer)
    {
        LOG_ERR << "m_pSendBuffer or m_pRecvBuffer is null" << std::endl;
        return -1;
    }

    m_pRecvThread.reset(new std::thread(&DRSdkManager::RecvThreadCallback, this));
    m_pThreadPool = std::make_unique<ThreadPool>(MAX_THREAD_NUM);

    return 0;
}

/**
 * @brief		Send control commands
 * @param [in]	strTagName  tag name
 * @param [in]	strValue   tag value
 * @param [in]	nWriteMode  write mode (gvs,ngvs)
 * @return		RD_SUCCESS if success
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
int32 DRSdkManager::DrControlData(const std::string &strTagName, const std::string &strValue, const uint8 nWriteMode)
{
    DataHeader tHead;
    tHead.nType = SDK_CONTROL_DATA;
    tHead.nLen = strTagName.size();
    tHead.nSeq = m_nHeadSeq.fetch_add(1);
    std::string strContent =
        strTagName + std::string(ksDivideMark) + strValue + std::string(ksDivideMark) + std::to_string(nWriteMode);
    int32 nRet = WriteToSendBuffer(tHead, strContent, strContent.size());
    if (0 != nRet)
    {
        LOG_INFO << "WriteToSendBuffer failed. nRet = " << nRet << std::endl;
        return nRet;
    }
    auto tuple = m_mapRequest[tHead.nSeq];
    auto future = std::get<1>(tuple);
    auto status = future.wait_for(std::chrono::seconds(2));
    if (status != std::future_status::ready)
    {
        LOG_INFO << "time_out: " << std::endl;
        return -1;
    }
    std::string sRes = future.get();
    LOG_INFO << "sRes: " << sRes << std::endl;
    if (static_cast<int32>(std::stoul(sRes)) != 0)
    {
        return -1;
    }
    return 0;
}

/**
 * @brief		Synchronize data reading
 * @param [in]	strTagName  tag name
 * @param [out]	pValue   tag value
 * @return		RD_SUCCESS if success
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
int32 DRSdkManager::DrReadData(const std::string &strTagName, std::string *pValue)
{

    DataHeader tHead;
    tHead.nType = SDK_READ_DATA;
    tHead.nLen = strTagName.size();
    tHead.nSeq = m_nHeadSeq.fetch_add(1);
    int32 nRet = WriteToSendBuffer(tHead, strTagName, strTagName.size());
    if (0 != nRet)
    {
        LOG_INFO << "WriteToSendBuffer failed. nRet = " << nRet << std::endl;
        return nRet;
    }

    auto tuple = m_mapRequest[tHead.nSeq];
    auto future = std::get<1>(tuple);
    auto status = future.wait_for(std::chrono::seconds(2));
    // TODO(wuzheqiang): m_mapRequest clear
    if (status != std::future_status::ready)
    {
        LOG_INFO << "time_out: " << std::endl;
        return -1;
    }
    std::string sRes = future.get();
    LOG_INFO << "sRes: " << sRes << std::endl;
    *pValue = sRes;

    return 0;
}

/**
 * @brief		register tag for async reading
 * @param [in]	strTagName  tag name
 * @param [in]	pCallBack   callback function ptr
 * @return		RD_SUCCESS if success
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
int32 DRSdkManager::DrAsyncRegTag(const std::string &strTagName, const Callback &pCallBack)
{
    { // 回调函数与标签关联
        std::lock_guard<std::mutex> lock(m_CallbacksMtx);
        auto iter = m_mapCallbacks.find(strTagName);
        if (iter != m_mapCallbacks.end())
        {
            auto &vecFunc = iter->second;
            vecFunc.emplace_back(pCallBack);
        }
        else
        {
            std::vector<Callback> vecFunc;
            vecFunc.emplace_back(pCallBack);
            m_mapCallbacks.emplace(strTagName, vecFunc);
        }
    }

    DataHeader tHead;
    tHead.nType = SDK_REG_TAG;
    tHead.nLen = strTagName.size();
    tHead.nSeq = m_nHeadSeq.fetch_add(1);
    int32 nRet = WriteToSendBuffer(tHead, strTagName, strTagName.size());
    if (0 != nRet)
    {
        LOG_INFO << "WriteToSendBuffer failed. nRet = " << nRet << std::endl;
        return nRet;
    }

    auto tuple = m_mapRequest[tHead.nSeq];
    auto future = std::get<1>(tuple);
    auto status = future.wait_for(std::chrono::seconds(2));
    if (status != std::future_status::ready)
    {
        LOG_INFO << "time_out: " << std::endl;
        return -1;
    }
    std::string sRes = future.get();
    LOG_INFO << "sRes: " << sRes << std::endl;
    if (static_cast<int32>(std::stoul(sRes)) != 0)
    {
        return -1;
    }
    return 0;
}

/**
 * @brief		Write content into  send_buffer
 * @param [in]	tHead   msg head
 * @param [in]	strContent   msg content
 * @param [in]	nLen  msg length
 * @return		RD_SUCCESS if success
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
int32 DRSdkManager::WriteToSendBuffer(const DataHeader &tHead, const std::string &strContent, const size_t nLen)
{
    std::lock_guard<std::mutex> lock(m_SendBufferMtx);
    int32 nRet = 0;
    memcpy(m_pSendBuffer.get(), &tHead, sizeof(tHead));
    memcpy(m_pSendBuffer.get() + sizeof(tHead), strContent.c_str(), nLen);

    int32 nLenBuf = sizeof(tHead) + strContent.size();
    nRet = m_pTransport->SendData(m_pSendBuffer.get(), nLenBuf);
    if (0 != nRet)
    {
        LOG_ERR << "send data failed. length = " << nLenBuf << ", nRet = " << nRet << std::endl;
        return nRet;
    }
    LOG_INFO << "send data success. length = " << nLenBuf << ", nRet = " << nRet << std::endl;
    SharedPromise call_promise = std::make_shared<Promise>();
    SharedFuture f(call_promise->get_future());
    m_mapRequest[tHead.nSeq] = std::make_tuple(call_promise, f, NowSecond());
    return 0;
}

/**
 * @brief		callback function that loops through to get data
 * @version		09/10/2024	wuzheqiang	Initial Version
 */

void DRSdkManager::RecvThreadCallback()
{
    while (true)
    {
        int32 buffer_len = RECV_BUFFER_MAX_LENGTH;
        int32 nRet = m_pTransport->RecvData(m_pRecvBuffer.get(), buffer_len);
        if (0 != nRet)
        {
            LOG_ERR << "recv data failed. nRet = " << nRet << std::endl;
        }
        LOG_INFO << "recv data. length = " << buffer_len << std::endl;

        DataHeader *pHead = (DataHeader *)m_pRecvBuffer.get();
        LOG_INFO << pHead->ToString() << std::endl;
        switch (pHead->nType)
        {
        case SDK_CONTROL_DATA: {
            auto tuple = m_mapRequest[pHead->nSeq];
            auto promise = std::get<0>(tuple);
            promise->set_value(std::string(m_pRecvBuffer.get() + sizeof(DataHeader), pHead->nLen));
            break;
        }
        case SDK_READ_DATA: {
            auto tuple = m_mapRequest[pHead->nSeq];
            auto promise = std::get<0>(tuple);
            promise->set_value(std::string(m_pRecvBuffer.get() + sizeof(DataHeader), pHead->nLen));
            break;
        }
        case SDK_REG_TAG: {
            auto tuple = m_mapRequest[pHead->nSeq];
            auto promise = std::get<0>(tuple);
            promise->set_value(std::string(m_pRecvBuffer.get() + sizeof(DataHeader), pHead->nLen));
            break;
        }
        case SDK_REG_TAG_DATA: {
            std::string strContent = std::string(m_pRecvBuffer.get() + sizeof(DataHeader), pHead->nLen);
            // TODO (wuzheqiang#2024-09-10): parse content
            std::string strTagName = strContent.substr(0, strContent.find_first_of("->"));
            std::string strValue = strContent.substr(strContent.find_first_of("->") + 2);

            auto iter = m_mapCallbacks.find(strTagName);
            if (iter != m_mapCallbacks.end())
            {
                auto &vecFunc = iter->second;
                for (auto &func : vecFunc)
                {
                    m_pThreadPool->Enqueue(func, strValue); // add task to thread pool and  async callback
                }
            }

            break;
        }
        default: {
            LOG_ERR << "pHead->nType not support. nType = " << pHead->nType << std::endl;
            break;
        }
        }
    }
}
} // namespace dsfapi