/*  Filename:    drsdk_manager.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Define DRSdkManager, It is a singleton, managing the processing of data
 *
 *  @author:     wuzheqiang
 *	@version	   09/10/2024	wuzheqiang	Initial Version
 **************************************************************/

#ifndef DRSDK_MANAGER_H
#define DRSDK_MANAGER_H
#include "drsdk_common.h"
#include "drsdk_transport.h"
#include "thread_pool.h"
#include <functional>
#include <future>
#include <memory>
#include <mutex>
#include <string>
#include <unordered_map>
#include <vector>

namespace dsfapi
{
using Callback = std::function<void(const std::string &)>;
using VecFuncs = std::vector<Callback>;
using MapFuncs = std::unordered_map<std::string, VecFuncs>;
using Promise = std::promise<std::string>;
using SharedPromise = std::shared_ptr<Promise>;
using SharedFuture = std::shared_future<std::string>;

class DRSdkManager
{
  public:
    ~DRSdkManager();

    /**
     * @brief		  init DRSdkManager
     * @return		RD_SUCCESS if success
     * @version		09/10/2024	wuzheqiang	Initial Version
     */
    int32 Init();

    /**
     * @brief		  Get singleton instance
     * @return		DRSdkManager *m_pInstance
     * @version		09/10/2024	wuzheqiang	Initial Version
     */
    static DRSdkManager *Instance();

    /**
     * @brief		Send control commands
     * @param [in]	strTagName  tag name
     * @param [in]	strValue   tag value
     * @param [in]	nWriteMode  write mode (gvs,ngvs)
     * @return		RD_SUCCESS if success
     * @version		09/10/2024	wuzheqiang	Initial Version
     */
    int32 DrControlData(const std::string &strTagName, const std::string &strValue, const uint8 nWriteMode);

    /**
     * @brief		Synchronize data reading
     * @param [in]	strTagName  tag name
     * @param [out]	pValue   tag value
     * @return		RD_SUCCESS if success
     * @version		09/10/2024	wuzheqiang	Initial Version
     */
    int32 DrReadData(const std::string &strTagName, std::string *pValue);

    /**
     * @brief		register tag for async reading
     * @param [in]	strTagName  tag name
     * @param [in]	pCallBack   callback function ptr
     * @return		RD_SUCCESS if success
     * @version		09/10/2024	wuzheqiang	Initial Version
     */
    int32 DrAsyncRegTag(const std::string &strTagName, const Callback &pCallBack);

  // private:！！！！！！！！此处注释为单元测试时的修改，单元测试结束后请取消注释
    DRSdkManager() = default;
    DRSdkManager(const DRSdkManager &) = delete;
    DRSdkManager &operator=(const DRSdkManager &) = delete;

    /**
     * @brief		callback function that loops through to get data
     * @version		09/10/2024	wuzheqiang	Initial Version
     */

    void RecvThreadCallback();
    /**
     * @brief		Write content into  send_buffer
     * @param [in]	tHead   msg head
     * @param [in]	strContent   msg content
     * @param [in]	nLen  msg length
     * @return		RD_SUCCESS if success
     * @version		09/10/2024	wuzheqiang	Initial Version
     */
    int32 WriteToSendBuffer(const DataHeader &tHead, const std::string &strContent, const size_t nLen);

  // private:！！！！！！！！此处注释为单元测试时的修改，单元测试结束后请取消注释
    DRSdkConfig m_tDrSdkConfig;
    std::unique_ptr<DrSdkTransport> m_pTransport = nullptr; // transport layer
    std::unique_ptr<std::thread> m_pRecvThread = nullptr;   // recv from DS thread

    std::atomic<uint32> m_nHeadSeq = {0};
    std::unordered_map<uint32, std::tuple<SharedPromise, SharedFuture, uint32>> m_mapRequest; // async requests
    std::unique_ptr<char[]> m_pRecvBuffer = nullptr;
    std::unique_ptr<char[]> m_pSendBuffer = nullptr;
    std::atomic<int32> m_nSendBufferLen = {0};
    std::mutex m_RecvBufferMtx;
    std::mutex m_SendBufferMtx;

    // call_back
    std::unique_ptr<ThreadPool> m_pThreadPool;
    MapFuncs m_mapCallbacks; // map tag name to callback function
    std::mutex m_CallbacksMtx;

    static DRSdkManager *m_pInstance; // singleton instance
    static std::mutex m_InstanceMtx;
};

} // namespace dsfapi
#endif