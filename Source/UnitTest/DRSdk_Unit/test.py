
import pytest
import asyncio
import pdb;
import os;
import logging

# Import your functions
from allure_setup import *

# Example Jira key

logging.info(f'这是测试用例的info')
logging.warning(f'这是测试用例的warning')
logging.error(f'这是测试用例的error')


# Use the allure_setup decorator
@allure_setup("DSFR-54", is_async=False)
def test_jira_summary1():
    summary, _ = get_jira_summary("DSFR-54")
    logging.info(f"Summary:{summary}")
    assert summary == "DRSDK中同步数据查询测试" # Replace with the actual expected summary


@allure_setup("DSFR-58", is_async=False)
def test_jira_summary2():
    summary, _ =  get_jira_summary("DSFR-58")
    logging.info(f"Summary:{summary}")
    assert summary == "DRSDK中init和uninit功能测试" # Replace with the actual expected summary

@allure_setup("DSFR-59", is_async=False)
def test_jira_summary3():
    summary, _ = get_jira_summary("DSFR-59")
    logging.info(f"Summary:{summary}")
    assert summary == "DRSDK中init和uninit功能异常处理测试" # Replace with the actual expected summary



@allure_setup("DSFR-61", is_async=False)
def test_jira_summary4():
    summary, _ = get_jira_summary("DSFR-61")
    logging.info(f"Summary:{summary}")
    assert summary == "DRSDK中异步数据回调查询功能测试" # Replace with the actual expected summary




# Running the tests
if __name__ == "__main__":
    pytest.main(["-vv", "-s", "test.py"])
  