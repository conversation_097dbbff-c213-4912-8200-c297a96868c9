/*  Filename:    drsdk_manager.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Define DrSdkTransport, use share memory to communicate with DataService.
 *
 *  @author:     wuzheqiang
 *	@version     09/10/2024	wuzheqiang	Initial Version
 **************************************************************/
#include "drsdk_transport.h"
#include "error_code.h"

namespace dsfapi
{
namespace
{
constexpr char *kShareMemName = "dsfapi-drdataservice";
constexpr int32 kShareMemSize = 32;        // MB
constexpr int32 kShareMemBlockSize = 4096; // KB
constexpr int32 kDrsdkSendQueueId = 1;
constexpr int32 kDrsdkRecvQueueId = 2;
} // namespace

DrSdkTransport::DrSdkTransport()
{
}

DrSdkTransport::~DrSdkTransport()
{
    Close();
}

int32 DrSdkTransport::Init()
{
    int32 nRet;
    nRet = proccomm_init(kShareMemName, kShareMemSize, kShareMemBlockSize, 0, &m_hProcComm);
    if (0 != nRet)
    {
        LOG_ERR << "proccomm_init failed, ret = " << nRet << std::endl;
        return nRet;
    }

    nRet = proccomm_reg(m_hProcComm, kDrsdkSendQueueId, &m_hDrsdkSendToDs);
    if (0 != nRet)
    {
        LOG_ERR << "proccomm_reg ALM_PBS_PROCCOMM_QUEUEID failed, ret = " << nRet << std::endl;
        return nRet;
    }

    nRet = proccomm_reg(m_hProcComm, kDrsdkRecvQueueId, &m_hDrsdkRecvFromDs);
    if (0 != nRet)
    {
        LOG_ERR << "proccomm_reg PBS_ALM_PROCCOMM_QUEUEID failed, ret = " << nRet << std::endl;
        return nRet;
    }
}
int32 DrSdkTransport::Close()
{
    if (m_hProcComm != NULL && m_hDrsdkSendToDs != NULL)
    {
        proccomm_unreg(m_hProcComm, kDrsdkSendQueueId, m_hDrsdkSendToDs);
        m_hDrsdkSendToDs = NULL;
    }
    if (m_hProcComm != NULL && m_hDrsdkRecvFromDs != NULL)
    {
        proccomm_unreg(m_hProcComm, kDrsdkRecvQueueId, m_hDrsdkRecvFromDs);
        m_hDrsdkRecvFromDs = NULL;
    }
    if (m_hProcComm != NULL)
    {
        proccomm_release(m_hProcComm);
        m_hProcComm = NULL;
    }
    return 0;
}

/**
 * @brief		Send data to DataService
 * @param [in]	pSendBuf  buffer to send
 * @param [in]	nLenBuf   buffer length
 * @return		RD_SUCCESS if success
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
int32 DrSdkTransport::SendData(const char *pSendBuf, int32 nLenBuf)
{
    std::lock_guard<std::mutex> lock(m_SendMutex);
    return proccomm_send(m_hDrsdkSendToDs, pSendBuf, nLenBuf);
}

/**
 * @brief		Send data to DataService
 * @param [in]	pSendBuf  buffer to recv
 * @param [in]	nLenBuf   received length
 * @return		RD_SUCCESS if success
 * @version		09/10/2024	wuzheqiang	Initial Version
 */
int32 DrSdkTransport::RecvData(char *pRecvBuf, int32 &pBufLen)
{
    std::lock_guard<std::mutex> lock(m_RecvMutex);
    return proccomm_recv(m_hDrsdkRecvFromDs, pRecvBuf, &pBufLen, true, INFINITE);
}

int32 DrSdkTransport::TestSendData(const char *pSendBuf, int32 nLenBuf)
{
    std::lock_guard<std::mutex> lock(m_SendMutex);
    return proccomm_send(m_hDrsdkRecvFromDs, pSendBuf, nLenBuf);
}
int32 DrSdkTransport::TestRecvData(char *pRecvBuf, int32 &pBufLen)
{
    std::lock_guard<std::mutex> lock(m_RecvMutex);
    int nRet = proccomm_recv(m_hDrsdkSendToDs, pRecvBuf, &pBufLen, true, INFINITE);
    if (EC_RD_PROCCOMM_NO_MEMSPACE == nRet)
    {
        LOG_ERR << "proccomm_recv EC_RD_PROCCOMM_NO_MEMSPACE! need buflen " << pBufLen << std::endl;
    }
    return nRet;
}

} // namespace dsfapi