#1.这是对drsdk/thread_pool的单元测试（完成）
#CMakeLists.txt
cmake_minimum_required(VERSION 3.16)


project(servicebasetest VERSION 1.0)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED True)

set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} --coverage -O0 -g")
set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} --coverage -O0 -g")

#obtan file name 
#get_filename_component(PROJECT_DIR_NAME $(PROJECT_SOURCE_DIR) NAME)

#message("Output directory name: ${PROJECT_DIR_NAME}")

# get_filename_component(PARENT_DIR ${PROJECT_SOURCE_DIR} DIRECTORY)
# get_filename_component(GRANDPARENT_DIR ${PARENT_DIR} DIRECTORY)

# get_filename_component(PROJECT_DIR_NAE ${PROJECT_SOURCE_DIR} NAME)
message(STATUS "CMAKE_BINARY_DIR: ${CMAKE_BINARY_DIR}")
message(STATUS "CMAKE_COMMAND: ${CMAKE_COMMAND}")


# 获取用户的主目录
set(USER_HOME $ENV{HOME})
message(STATUS "USER_HOME" ${USER_HOME})
# 调用 find 命令找到 Source 文件夹
execute_process(
    COMMAND find ${USER_HOME} -type d -name "UnitTest"
    OUTPUT_VARIABLE FOUND_SOURCE_DIRS
    OUTPUT_STRIP_TRAILING_WHITESPACE
                    )
# 将找到的目录设置为变量
if(FOUND_SOURCE_DIRS)
    message(STATUS "Found Source directories: ${FOUND_SOURCE_DIRS}")
    set(SOURCE_DIRS ${FOUND_SOURCE_DIRS})
else()
    message(FATAL_ERROR "No Source directories found")
endif()
set(BINARY_DIR ${SOURCE_DIRS}/build)

add_custom_target(coverage_DRSdk_Unit
        COMMAND ${CMAKE_COMMAND} --build ${BINARY_DIR}
        COMMAND ${BINARY_DIR}/DRSdk_Unit/sdktest
        COMMAND lcov --capture --directory ${BINARY_DIR} --output-file ${BINARY_DIR}/coverage_DRSdk_Unit.info
        COMMAND lcov --remove ${BINARY_DIR}/coverage_DRSdk_Unit.info '/usr/*' --output-file ${BINARY_DIR}/coverage_DRSdk_Unit.info
        COMMAND genhtml ${BINARY_DIR}/coverage_DRSdk_Unit.info
                                        --output-directory ${BINARY_DIR}/coverage_DRSdk_Unit
                                        --title "Service Base Test Coverage"
                                        --legend --demangle-cpp
                                        --highlight
                                        --show-details
                                        --prefix ${BINARY_DIR}/../DRSdk_Unit
        COMMAND gcovr -r ${CMAKE_SOURCE_DIR}  --object-directory ${BINARY_DIR}/../DRSdk_Unit --xml --output ${BINARY_DIR}/test_code_DRSdk_Unit_coverage.xml
        WORKING_DIRECTORY ${BINARY_DIR}
        COMMENT "Generating coverage report..."
                                            )

# set(BIN_DIR ${PROJECT_SOURCE_DIR}/../build/DRSdk_Unit)
# message(STATUS "BIN_DIR: ${BIN_DIR}")

# add_custom_target(coverage  
# 	COMMAND ${CMAKE_COMMAND} --build ${BIN_DIR}
# 	COMMAND ${BIN_DIR}/sdktest
# 	COMMAND lcov --capture --directory ${BIN_DIR} --output-file ${BIN_DIR}/coverage.info
# 	COMMAND lcov --remove ${BIN_DIR}/coverage.info '/usr/*' --output-file ${BIN_DIR}/coverage.info
# 	COMMAND genhtml ${BIN_DIR}/coverage.info --output-directory ${BIN_DIR}/coverage
# 	WORKING_DIRECTORY ${BIN_DIR}
# 	COMMENT "Generating coverage report..."
# 	    )
			    #
#set(TARGET_BUILD_DIR ${GRANDPARENT_DIR}/build/${PROJECT_DIR_NAME})



#file(MAKE_DIRECTORY ${TARGET_BUILD_DIR})
#
#set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${TARGET_BUILD_DIR})


#add_executable(MathFunctions MathFunctions.cpp)

#include_directories(${PROJECT_SOURCE_DIR}../../Source/common/servicebase/include)


add_executable(sdktest test_sdk.cpp)
target_include_directories(sdktest PUBLIC ${PROJECT_SOURCE_DIR}/../../../Source/common/servicebase/include)
target_include_directories(sdktest PUBLIC ${PROJECT_SOURCE_DIR}/../../../Source/common/cvcomm/include)
target_include_directories(sdktest PUBLIC ${PROJECT_SOURCE_DIR}/../../../Source/common/shmqueue/include)
target_include_directories(sdktest PUBLIC ${PROJECT_SOURCE_DIR}/../../../Source/common/cvlog/include)
target_include_directories(sdktest PUBLIC ${PROJECT_SOURCE_DIR}/../../../include)
target_include_directories(sdktest PUBLIC ${PROJECT_SOURCE_DIR}/../../../include/ace)
target_include_directories(sdktest PUBLIC ${PROJECT_SOURCE_DIR}/../../../include/ihd3)
#message("Output directory: ${PROJECT_SOURCE_DIR}../../Source/common/servicebase/include")

# find_library(S1_LIBRARY servicebase)
# find_library(S2_LIBRARY GTest)
# find_library(S3_LIBRARY Main)
# find_library(S4_LIBRARY pthread)
#${S1_LIBRARY} ${S2_LIBRARY} ${S3_LIBRARY} ${S4_LIBRARY}
#INCLUDE($ENV{DualiCVSRCDIR}CMakeCommonExec)
target_link_libraries(sdktest PRIVATE 
libgtest.a
libgtest_main.a
pthread

${PROJECT_SOURCE_DIR}/../../../library/libservicebasetest.a
${PROJECT_SOURCE_DIR}/../../../library/libdrlog.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_chrono.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_date_time.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_filesystem.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_regex.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_serialization.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_system.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_thread.a
${PROJECT_SOURCE_DIR}/../../../library/libboost_wserialization.a
${PROJECT_SOURCE_DIR}/../../../library/libprotobuf.a
${PROJECT_SOURCE_DIR}/../../../library/libprotoc.a
${PROJECT_SOURCE_DIR}/../../../library/libcppsqlite.a
${PROJECT_SOURCE_DIR}/../../../library/libshmqueue.a
${PROJECT_SOURCE_DIR}/../../../executable/libdrhdProcComm.so
${PROJECT_SOURCE_DIR}/../../../executable/libdrlogimpl.so
${PROJECT_SOURCE_DIR}/../../../executable/libdrcomm.so
${PROJECT_SOURCE_DIR}/../../../library/libgnuintl.so
${PROJECT_SOURCE_DIR}/../../../library/libiconv.so
${PROJECT_SOURCE_DIR}/../../../library/libintl.so
${PROJECT_SOURCE_DIR}/../../../library/libtinyxml.a
${PROJECT_SOURCE_DIR}/../../../library/libACE.so
)

#add_dependencies(coverage servicebasetest)
#GTest::GTest GTest::Main pthread /home/<USER>/dr/library/libservicebase.a)





# # 这是对drsdk_manager.cpp的单元测试内容（有bug版）
# #CMakeLists.txt
# cmake_minimum_required(VERSION 3.16)


# project(servicebasetest VERSION 1.0)

# set(CMAKE_CXX_STANDARD 14)
# set(CMAKE_CXX_STANDARD_REQUIRED True)

# set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} --coverage -O0 -g")
# set(CMAKE_C_FLAGS "${CMAKE_C_FLAGS} --coverage -O0 -g")

# # 添加这行来确保正确链接
# set(CMAKE_EXE_LINKER_FLAGS "${CMAKE_EXE_LINKER_FLAGS} -Wl,--no-as-needed")
# #obtan file name 
# #get_filename_component(PROJECT_DIR_NAME $(PROJECT_SOURCE_DIR) NAME)

# #message("Output directory name: ${PROJECT_DIR_NAME}")

# # get_filename_component(PARENT_DIR ${PROJECT_SOURCE_DIR} DIRECTORY)
# # get_filename_component(GRANDPARENT_DIR ${PARENT_DIR} DIRECTORY)

# # get_filename_component(PROJECT_DIR_NAME ${PROJECT_SOURCE_DIR} NAME)

# add_custom_target(coverage   
# 	COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR}
# 	COMMAND ${CMAKE_BINARY_DIR}/test_drsdk_manager
# 	COMMAND lcov --capture --directory ${CMAKE_BINARY_DIR} --output-file ${CMAKE_BINARY_DIR}/coverage.info
# 	COMMAND lcov --remove ${CMAKE_BINARY_DIR}/coverage.info '/usr/*' --output-file ${CMAKE_BINARY_DIR}/coverage.info
# 	COMMAND genhtml ${CMAKE_BINARY_DIR}/coverage.info --output-directory ${CMAKE_BINARY_DIR}/coverage
# 	WORKING_DIRECTORY ${CMAKE_BINARY_DIR}/test
# 	COMMENT "Generating coverage report..."
# 				    )

# #set(TARGET_BUILD_DIR ${GRANDPARENT_DIR}/build/${PROJECT_DIR_NAME})

# #file(MAKE_DIRECTORY ${TARGET_BUILD_DIR})
# #
# #set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${TARGET_BUILD_DIR})


# #add_executable(MathFunctions MathFunctions.cpp)

# #include_directories(${PROJECT_SOURCE_DIR}../../Source/common/servicebase/include)


# add_executable(test_drsdk_manager test_drsdk_manager.cpp)
# target_include_directories(test_drsdk_manager PUBLIC ${PROJECT_SOURCE_DIR}/../../Source/common/servicebase/include)
# target_include_directories(test_drsdk_manager PUBLIC ${PROJECT_SOURCE_DIR}/../../Source/common/cvcomm/include)
# target_include_directories(test_drsdk_manager PUBLIC ${PROJECT_SOURCE_DIR}/../../Source/common/shmqueue/include)
# target_include_directories(test_drsdk_manager PUBLIC ${PROJECT_SOURCE_DIR}/../../Source/common/cvlog/include)
# target_include_directories(test_drsdk_manager PUBLIC ${PROJECT_SOURCE_DIR}/../../include)
# target_include_directories(test_drsdk_manager PUBLIC ${PROJECT_SOURCE_DIR}/../../include/ace)
# #message("Output directory: ${PROJECT_SOURCE_DIR}../../Source/common/servicebase/include")

# # find_library(S1_LIBRARY servicebase)
# # find_library(S2_LIBRARY GTest)
# # find_library(S3_LIBRARY Main)
# # find_library(S4_LIBRARY pthread)
# #${S1_LIBRARY} ${S2_LIBRARY} ${S3_LIBRARY} ${S4_LIBRARY}
# #INCLUDE($ENV{DualiCVSRCDIR}CMakeCommonExec)
# find_package(GTest REQUIRED)
# include_directories(${GTEST_INCLUDE_DIRS})
# target_link_libraries(test_drsdk_manager PRIVATE 
# # libgtest.a
# # libgtest_main.a
# # pthread
# GTest::GTest
# GTest::Main

# libgmock.a
# libgmock_main.a
# pthread
# # /home/<USER>/dr/library/libservicebase.a 
# # /home/<USER>/dr/library/libcv6hdproccomm.so 
# # /home/<USER>/dr/library/libcv6comm.so 
# # /home/<USER>/dr/library/libcv6log.a
# # /home/<USER>/dr/library/libcv6logimpl.so

# # /home/<USER>/Workspaces/dr/library/libservicebasetest.a
# # /home/<USER>/Workspaces/dr/library/liblicverify.a
# # /home/<USER>/Workspaces/dr/library/libcv6log.a
# # /home/<USER>/Workspaces/dr/library/libboost_chrono.a
# # /home/<USER>/Workspaces/dr/library/libboost_date_time.a
# # /home/<USER>/Workspaces/dr/library/libboost_filesystem.a
# # /home/<USER>/Workspaces/dr/library/libboost_regex.a
# # /home/<USER>/Workspaces/dr/library/libboost_serialization.a
# # /home/<USER>/Workspaces/dr/library/libboost_system.a
# # /home/<USER>/Workspaces/dr/library/libboost_thread.a
# # /home/<USER>/Workspaces/dr/library/libboost_wserialization.a
# # /home/<USER>/Workspaces/dr/library/libLicense.a
# # /home/<USER>/Workspaces/dr/library/libprotobuf.a
# # /home/<USER>/Workspaces/dr/library/libprotoc.a
# # /home/<USER>/Workspaces/dr/library/libhiredis.a
# # /home/<USER>/Workspaces/dr/library/libcppsqlite.a
# # /home/<USER>/Workspaces/dr/library/libshmqueue.a
# # /home/<USER>/Workspaces/dr/executable/libcv6hdProcComm.so
# # /home/<USER>/Workspaces/dr/executable/libcv6logimpl.so
# # /home/<USER>/Workspaces/dr/executable/libcv6comm.so
# # #/home/<USER>/Workspaces/dr/executable/libcv6fcomm.so
# # #/home/<USER>/Workspaces/dr/executable/libdsfcomm.so
# # /home/<USER>/Workspaces/dr/library/libgnuintl.so
# # /home/<USER>/Workspaces/dr/library/libiconv.so
# # /home/<USER>/Workspaces/dr/library/libintl.so
# # /home/<USER>/Workspaces/dr/library/libhdOS.so
# # /home/<USER>/Workspaces/dr/library/libtinyxml.a
# # /home/<USER>/Workspaces/dr/executable/libcv6rmapi.so
# # #/home/<USER>/dr/library/
# # /home/<USER>/Workspaces/dr/library/libACE.so
# ${PROJECT_SOURCE_DIR}/../../library/libservicebasetest.a
# ${PROJECT_SOURCE_DIR}/../../library/liblicverify.a
# ${PROJECT_SOURCE_DIR}/../../library/libcv6log.a
# ${PROJECT_SOURCE_DIR}/../../library/libboost_chrono.a
# ${PROJECT_SOURCE_DIR}/../../library/libboost_date_time.a
# ${PROJECT_SOURCE_DIR}/../../library/libboost_filesystem.a
# ${PROJECT_SOURCE_DIR}/../../library/libboost_regex.a
# ${PROJECT_SOURCE_DIR}/../../library/libboost_serialization.a
# ${PROJECT_SOURCE_DIR}/../../library/libboost_system.a
# ${PROJECT_SOURCE_DIR}/../../library/libboost_thread.a
# ${PROJECT_SOURCE_DIR}/../../library/libboost_wserialization.a
# ${PROJECT_SOURCE_DIR}/../../library/libLicense.a
# ${PROJECT_SOURCE_DIR}/../../library/libprotobuf.a
# ${PROJECT_SOURCE_DIR}/../../library/libprotoc.a
# ${PROJECT_SOURCE_DIR}/../../library/libhiredis.a
# ${PROJECT_SOURCE_DIR}/../../library/libcppsqlite.a
# ${PROJECT_SOURCE_DIR}/../../library/libshmqueue.a
# ${PROJECT_SOURCE_DIR}/../../executable/libcv6hdProcComm.so
# ${PROJECT_SOURCE_DIR}/../../executable/libcv6logimpl.so
# ${PROJECT_SOURCE_DIR}/../../executable/libcv6comm.so
# ${PROJECT_SOURCE_DIR}/../../library/libgnuintl.so
# ${PROJECT_SOURCE_DIR}/../../library/libiconv.so
# ${PROJECT_SOURCE_DIR}/../../library/libintl.so
# ${PROJECT_SOURCE_DIR}/../../library/libhdOS.so
# ${PROJECT_SOURCE_DIR}/../../library/libtinyxml.a
# ${PROJECT_SOURCE_DIR}/../../executable/libcv6rmapi.so
# ${PROJECT_SOURCE_DIR}/../../library/libACE.so
# ${PROJECT_SOURCE_DIR}/../../executable/libdrsdk.so
# ${PROJECT_SOURCE_DIR}/../../executable/libcv6hdProcComm.so

# )
# include(GoogleTest)
# gtest_discover_tests(test_drsdk_manager)

# #add_dependencies(coverage servicebasetest)
# #GTest::GTest GTest::Main pthread /home/<USER>/dr/library/libservicebase.a)









