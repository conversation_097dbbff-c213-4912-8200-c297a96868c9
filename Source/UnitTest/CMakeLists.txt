cmake_minimum_required(VERSION 3.10)

# 设置项目名称
project(UnitTest)

# 启用测试
enable_testing()

# 指定二进制文件所在的文件夹
set(TEST_FOLDERS
	${CMAKE_SOURCE_DIR}/DRAcquisitionService_Unit
	${CMAKE_SOURCE_DIR}/DRDataService_Unit
	${CMAKE_SOURCE_DIR}/DRDeployer_Unit
	${CMAKE_SOURCE_DIR}/DRSdk_Unit
	${CMAKE_SOURCE_DIR}/OpcuaPublish_Unit
			)

# 设置构建目录
set(CMAKE_BINARY_DIR ${CMAKE_SOURCE_DIR}/build)

# 定义子项目
set(SUBPROJECTS
	DRAcquisitionService_Unit
	DRDataService_Unit
	DRDeployer_Unit
	DRSdk_Unit
	OpcuaPublish_Unit
		    # 更多文件夹
		    )
# 为每个子项目添加子目录
foreach(subproject ${SUBPROJECTS})
        add_subdirectory(${subproject} ${CMAKE_BINARY_DIR}/${subproject})
endforeach()


# 添加目标，运行所有二进制文件并生成报告
#add_custom_target(run_tests_and_generate_reports
#	    COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR}
	        
	        # 运行所有测试二进制文件
		#	    COMMAND ${TEST_BINS}   # 逐个运行这些二进制文件
		        
	    # 使用 lcov 收集覆盖率数据
	    #	    COMMAND lcov --capture --directory ${CMAKE_BINARY_DIR} --output-file ${CMAKE_BINARY_DIR}/coverage.info
	    #COMMAND lcov --remove ${CMAKE_BINARY_DIR}/coverage.info '/usr/*' --output-file ${CMAKE_BINARY_DIR}/coverage.info

	    # 使用 genhtml 生成 HTML 格式的覆盖率报告
	    #COMMAND genhtml ${CMAKE_BINARY_DIR}/coverage.info --output-directory ${CMAKE_BINARY_DIR}/coverage
	    #COMMENT "Running tests and generating coverage reports..."
	    #				    )

	    
# 指定二进制文件所在的文件夹
set(TEST_FOLDERS
	${CMAKE_BINARY_DIR}/DRAcquisitionService_Unit
	${CMAKE_BINARY_DIR}/DRDataService_Unit
	${CMAKE_BINARY_DIR}/DRDeployer_Unit
	${CMAKE_BINARY_DIR}/DRSdk_Unit
    )    
	    
	    
# 查找所有文件夹中的二进制文件
set(TEST_BINS 
        ${TEST_FOLDERS}/dracquisitiontest
        ${TEST_FOLDERS}/servicebasetest
        ${TEST_FOLDERS}/drdeploytest
        ${TEST_FOLDERS}/sdktest
				    )

			    #add_custom_target(coverage
			    #	        COMMAND ${CMAKE_COMMAND} --build ${CMAKE_BINARY_DIR}
			    #	COMMAND ${TEST_BINS}
			    #    COMMAND lcov --capture --directory ${CMAKE_BINARY_DIR} --output-file ${CMAKE_BINARY_DIR}/coverage.info
			    #	        COMMAND lcov --remove ${CMAKE_BINARY_DIR}/coverage.info '/usr/*' --output-file ${CMAKE_BINARY_DIR}/coverage.info
			    #        COMMAND genhtml ${CMAKE_BINARY_DIR}/coverage.info
			    #                  --output-directory ${CMAKE_BINARY_DIR}/coverage
			    #                  --title "DRAcquisition Test Coverage"
			    #                  --legend --demangle-cpp
			    #                  --highlight
			    #                  --show-details
			    #	        COMMAND gcovr -r ${CMAKE_SOURCE_DIR} --object-directory ${CMAKE_BINARY_DIR} --xml --output ${CMAKE_BINARY_DIR}/test_code_coverage.xml
			    #WORKING_DIRECTORY ${CMAKE_BINARY_DIR}
			    #   COMMENT "Generating coverage report..."
			    #     )

