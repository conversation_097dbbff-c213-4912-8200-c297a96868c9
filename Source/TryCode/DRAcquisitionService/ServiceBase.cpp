#include <ace/Time_Value.h>
#include <ace/OS_NS_unistd.h>
#include "ace/OS_Memory.h"
#include "ace/OS_NS_sys_time.h"
// #include "common/LicChecker.h"
// #include "common/ServiceBase.h"
// #include "common/LogHelper.h"
// #include "errcode/ErrCode_iCV_Common.hxx"
// #include "gettext/libintl.h"
// #include "common/cvcomm.hxx"
// #include "common/cvGlobalHelper.h"
#include "ace/OS_NS_time.h"
#include <locale.h>
#include "ace/Signal.h"
// #include "../shmqueue/include/common/ProcessQueue.h"
#include "ServiceBase.h"

#define _(STRING) gettext(STRING)
#ifdef _WIN32
#ifndef NO_EXCEPTION_ATTACHER
#include "common/RegularDllExceptionAttacher.h"

#pragma comment(lib, "ExceptionReport.lib")
#pragma comment(lib, "dbghelp.lib")

// global member to capture exception so as to analyze 
static CRegularDllExceptionAttacher	g_RegDllExceptAttacher;
#endif//#ifndef NO_EXCEPTION_ATTACHER
#endif

#define ICV_SUCCESS 0
extern CServiceBase* g_pServiceHandler;

#define  TIME_CHECKHARDDOC_SPAN		(30 * 60)

// namespace common{namespace service_base{

CServiceBase::CServiceBase(const char* szProcName, bool bVerifyProgLicense, const char *szProcLicTag)
: 
m_cvProcController((char *)szProcName)
, m_strProcName(szProcName)
{
	// m_bVerifyProgLicense = bVerifyProgLicense;
	if (szProcLicTag)
	{
		this->m_strProcTagName = szProcLicTag;
	}
	// m_pLicChecker = NULL;
	m_bShutDown = false;
	m_bInitSuccess = false;
	// m_nLicStatus = CV_LICENSE_INVALID;
	m_bExitWhenInitFailed = false;
	// m_tvValid = ACE_Time_Value::zero;

	// m_pProcessQue = NULL;

// 	const char* szRunTimePath = CVComm.GetCVRunTimeDataPath();
// 	if (NULL == szRunTimePath)
// 	{
// 		printf("error CServiceBase::CServiceBase CVComm.GetCVRunTimeDataPath return NULL");
// 		m_pProcessQue = NULL;

// 	}
// 	else
// 	{
// 		string strRunTimePath = szRunTimePath;
// 		ACE_OS::mkdir((strRunTimePath).c_str());
// 		strRunTimePath += ACE_DIRECTORY_SEPARATOR_STR PROCESSMGR_RUNTIME_DIR_NAME;
// 		ACE_OS::mkdir((strRunTimePath).c_str()); 

// #ifdef _WIN32
// 		m_pProcessQue = new CProcessQueue((char *)strRunTimePath.c_str(), (char *)szProcName); 
// #else
// 		std::string strProcName(szProcName);
// 		transform(strProcName.begin(), strProcName.end(), strProcName.begin(),(int (*)(int))tolower);
// 		m_pProcessQue = new CProcessQueue((char*)(strRunTimePath.c_str()), (char*)(strProcName.c_str())); 
// #endif	
// 		m_bUseHardDog = false; // 只有硬件狗才检查是否被拔掉
// 		m_tmLastCheckHardDogValidTime = 0;
// 		m_tmLastCheckHardDogTime = 0;

// 		InitI18N();
	// }
}

CServiceBase::~CServiceBase()
{
	// if(m_pProcessQue)
	// {
	// 	delete m_pProcessQue;
	// 	m_pProcessQue = NULL;
	// }
}

long CServiceBase::CheckProcLicense()
{
	return 0;
// 	long lRet = ICV_SUCCESS;
	
// 	//"AMSupport",包含"Agent"的服务和禁止了许可校验的进程统一处理，不进行校验
// 	if ( !m_bVerifyProgLicense || (m_strProcTagName=="AMSupport") ||(m_strProcTagName.find("Agent") != string::npos ))
// 	{
// 		//m_bVerifyProgLicense为false，则永远不会自动停止服务，可以不设置过期时间了
// 		m_bVerifyProgLicense = false;
// 		m_nLicStatus = CV_LICENSE_NORMAL;
// 		return lRet;
// 	}

// 	//检查许可证是否授权该服务
// //	string strValue;
// // 	m_pLicChecker->CheckLicenseInfo();
// // 	m_pLicChecker->GetExetendedLicInfo(m_strProcTagName.c_str(), strValue);
// // 	if (strValue == "0")
// // 	{
// // 		LH_LOG((*m_pCVLog), (LOG_LEVEL_CRITICAL, _("%s not included in license!"), m_strProcName.c_str()));
// // 		//服务未授权，则始终过期
// // 		m_tvValid = ACE_OS::gettimeofday() + ACE_Time_Value( CV_LICENSE_TEMPORARY_VALIDATE, 0 );
// // 		m_nLicStatus = CV_LICENSE_INVALID;
// // 		return lRet;
// // 	}
	
// 	//检查许可证是否过期
// 	m_nLicStatus = m_pLicChecker->GetTimeValid(m_tvValid);

// 	switch(m_nLicStatus)
// 	{
// 	case CV_LICENSE_LIMITED:	//受限许可证，允许运行2小时
// 		LH_LOG((*m_pCVLog), (LOG_LEVEL_WARN, _("$$$ 2-hour-trail license $$$")));				//(60 * 60 * 2)  // 2 hr temporary
// 		m_tvValid = ACE_OS::gettimeofday() + ACE_Time_Value( CV_LICENSE_TEMPORARY_VALIDATE, 0 );
// 		break;
// 	case CV_LICENSE_NORMAL:		//正常许可证，不可能过期，不记录
// 		break;
// 	case CV_LICENSE_OUT_OF_DATE:
// 		{
// 			//记录事件
// 			string strMsg = _("expired license, valid to:");
// 			char szTimeValid[ICV_DATESTRING_MAXLEN+ICV_SCANTIMESTRING_MAXLEN] = {'\0'};
// 			CastTime2Buffer(szTimeValid, sizeof(szTimeValid), m_tvValid);
// 			strMsg += szTimeValid;
// 			lRet = RecordEvent(strMsg.c_str());
// 			if (lRet != ICV_SUCCESS)
// 			{
// 				LH_LOG((*m_pCVLog), (LOG_LEVEL_ERROR, _("Failed to record the event of license expired! [ErrorCode=%d]"), lRet));
// 			}

// 			//过期许可证 转为 2小时受限许可证
// 			m_tvValid = ACE_OS::gettimeofday() + ACE_Time_Value( CV_LICENSE_TEMPORARY_VALIDATE, 0 );
// 			LH_LOG((*m_pCVLog), (LOG_LEVEL_CRITICAL, _("$$$ Expired license, into 2-hour-trail $$$")));
// 		}
// 		break;
// 	default:
// 		//无效的许可证状态 转为 2小时受限许可证
// 		m_tvValid = ACE_OS::gettimeofday() + ACE_Time_Value( CV_LICENSE_TEMPORARY_VALIDATE, 0 );
// 		LH_LOG((*m_pCVLog), (LOG_LEVEL_CRITICAL, _("$$$ Invalid license, into 2-hour-trail $$$")));
// 	}

	// return lRet;
}

long CServiceBase::DoService(int argc, char* args[])
{
	// return 0;
	// m_pCVLog = new CCVLog();
	// if(m_pCVLog == NULL)
	// {
	// 	return -1;
	// }
	// SetLogFileName();

	// // assure only one process is running
	// if(this->m_cvProcController.SetProcessAliveFlag() != ICV_SUCCESS) TODO
	if(this->m_cvProcController.SetProcessAliveFlag() != ICV_SUCCESS)
		return -1;

	// 是否接收键盘输入
	this->m_cvProcController.SetConsoleParam(argc,args);
	
	// ACE_NEW_NORETURN(m_pLicChecker, CLicChecker);
	// if (m_pLicChecker == NULL)
	// {
	// 	LH_LOG((*m_pCVLog), (LOG_LEVEL_CRITICAL, _("Error Molloc CLicChecker")));
	// 	return EC_ICV_COMM_FAILTO_ALLOCMEMORY;
	// }

	// // 第一次检查检测许可证
	// m_pLicChecker->PrintLicenseInfo();
	// CheckProcLicense();
	// time(&m_tmLastCheckHardDogTime);
	// m_bUseHardDog = m_pLicChecker->IsHardwareLic();
	// if(m_bUseHardDog)
	// {
	// 	time(&m_tmLastCheckHardDogValidTime); // 获取硬件狗有效的时间
	// }

	// // 先清除服务管理器队列中剩余的那些消息
	// // 放在这里而不是InteractiveLoop中的原因：下面的Init调用可能时间过长，如果用户先发停止消息
	// // 然后Init结束，在InteractiveLoop中会丢掉这次的停止消息。因此要放在这里
	// long nDequeResult = ICV_SUCCESS;
	// while(m_pProcessQue && nDequeResult == ICV_SUCCESS)
	// {
	// 	char szRecBuffer[PROCESSQUEUE_DEFAULT_RECORD_LENGHT] = {0};
	// 	long nRecLen = 0;
	// 	// 获取到最后一个队列内容，前面的记录全部都抛掉
	// 	nDequeResult = m_pProcessQue->DeQueue(szRecBuffer, &nRecLen);
	// }

	long nErr = ICV_SUCCESS;

	// // 初始化服务
	nErr = this->Init(argc, args);
	if (nErr != ICV_SUCCESS)
	{ 
		// LH_LOG((*m_pCVLog), (LOG_LEVEL_CRITICAL, _("Error Initializing %s with Error Code %d"), m_strProcName.c_str(), nErr));
		m_bInitSuccess = false;
		//不返回错误是为了防止备CvProcMgr.exe反复启动
		//如果要求 错误必退出 或者 错误码是：同时启动两个相同进程，则退出重启
		// if (nErr == EC_ICV_PDB_SERVICE_EXIST || m_bExitWhenInitFailed)
		// {
		// 	return nErr;
		// }
		
	}
	else
	{
		m_bInitSuccess = true;
	}	
	
	// //如果未过期，则开始服务
	// if( !IsOutOfDate() )
	// {
	// 	// 如果服务初始化成功，则启动服务
	// 	if (m_bInitSuccess)
	// 	{
	// 		nErr = this->Start();
	// 		if (nErr != ICV_SUCCESS)
	// 		{
	// 			LH_LOG((*m_pCVLog), (LOG_LEVEL_CRITICAL, _("Error Starting %s with Error Code %d"), m_strProcName.c_str(), nErr));
	// 			return nErr;
	// 		}
	// 	}
	// }
	// else
	// {
	// 	//如果许可已过期，则停止服务
	// 	m_bShutDown = true;
	// }
		
	// //主循环：接收控制台输入，检测是否过期，过期后停止服务
	this->RunInteractiveLoop();

	// //如果之前没有停止服务，则停止服务
	// if (!m_bShutDown)
	// {
	// 	Finish(true);
	// }
	// if (NULL != m_pCVLog)
	// {
	// 	delete m_pCVLog;
	// }
	// return nErr;
}

void CServiceBase::RunInteractiveLoop()
{
	std::cout << ">>>" << std::flush;

	char c;
	m_bLoopContinued = true;
	bool bFinished = false;
	this->PrintHelpScreen();

	do 
	{
	// 	// 如果该服务需要验证许可，且使用硬件狗方式。定期检查硬件许可证是否被拔掉或更换
	// 	if(m_bVerifyProgLicense && m_bUseHardDog)
	// 	{
	// 		time_t tmNow;
	// 		time(&tmNow);
	// 		if(labs((long)(tmNow - m_tmLastCheckHardDogTime)) >= TIME_CHECKHARDDOC_SPAN) // 每10分钟检查一次
	// 		{
	// 			CheckProcLicense();
	// 			if(!m_pLicChecker->IsHardwareLic())
	// 			{
	// 				// 如果之前
	// 				if(labs((long)(tmNow - m_tmLastCheckHardDogValidTime)) > 3 * TIME_CHECKHARDDOC_SPAN) // 无效时间超过3个周期
	// 				{
	// 					m_pCVLog->LogMessage(LOG_LEVEL_CRITICAL, _("Process exits without watchdog timer for %d seconds"), 3 * TIME_CHECKHARDDOC_SPAN);
	// 					m_bLoopContinued = false;
	// 					m_bShutDown = true;
						
	// 					string strMsg = _("Process exits without watchdog timer:");
	// 					char szTimeValid[ICV_DATESTRING_MAXLEN+ICV_SCANTIMESTRING_MAXLEN] = {'\0'};
	// 					CastTime2Buffer(szTimeValid, sizeof(szTimeValid), m_tvValid);
	// 					strMsg += szTimeValid;
	// 					long lRet = RecordEvent(strMsg.c_str());
	// 					if (lRet != ICV_SUCCESS)
	// 						LH_LOG((*m_pCVLog), (LOG_LEVEL_ERROR, _("Failed to record the event of lost license! [ErrorCode=%d]"), lRet));
						
	// 					break;
	// 				}
	// 				else
	// 					m_pCVLog->LogMessage(LOG_LEVEL_CRITICAL, _("Watchdog timer not found, and please check whether watchdog is plugged in or tight, otherwise, process will exit soon!"));

	// 			}
	// 			else // 更新最新检查有效的时间
	// 				m_tmLastCheckHardDogValidTime = tmNow;

	// 			m_tmLastCheckHardDogTime = tmNow;
	// 		} // if(labs(tmNow - m_tmLastCheckHardDogTime)
	// 	}

		if(m_cvProcController.CV_KbHit())
		{
			c = getchar();

			if (!this->ProcessCmd(c))
			{
				switch(c)
				{
				case 'Q':
				case 'q':
					std::cout << "Exiting..."<< std::endl;
					m_bLoopContinued = false;
					break;
				case 0x0a: // "return"
					std::cout << ">>>" << std::flush;
					continue;
				case 'H':
				case 'h':
				default:
					this->PrintHelpScreen();
					break;
				}

				Interactive(c);
			}
		}

		// 每次稍微等一点时间，避免进入太频繁
		ACE_OS::sleep(ACE_Time_Value(0,100000)); // 100 ms
	// 	// 尝试从任务管理器接收消息
	// 	if(m_pProcessQue)
	// 	{
	// 		char szRecBuffer[PROCESSQUEUE_DEFAULT_RECORD_LENGHT] = {0};
	// 		long nRecLen = 0;
	// 		bool bFoundRecord = false;
	// 		// 获取到最后一个队列内容，前面的记录全部都抛掉
	// 		int nResult = m_pProcessQue->DeQueue(szRecBuffer, &nRecLen);
	// 		if(nResult == ICV_SUCCESS)
	// 			bFoundRecord = true;

	// 		// 如果有记录则继续取最后一条记录
	// 		while(bFoundRecord)
	// 		{
	// 			nResult = m_pProcessQue->DeQueue(szRecBuffer, &nRecLen);
	// 			if(nResult != ICV_SUCCESS)
	// 				break;
	// 		}

	// 		if(bFoundRecord)
	// 		{
	// 			int nType = PROCESSMGR_CMDTYPE_REFRESH;
	// 			time_t tmNow;
	// 			memcpy(&nType, szRecBuffer, sizeof(int));
	// 			memcpy(&tmNow, szRecBuffer + sizeof(int), sizeof(time_t));
	// 			char szTime[PROCESSQUEUE_DEFAULT_RECORD_LENGHT] = {0};
	// 			ACE_OS::strftime(szTime, sizeof(szTime), "%Y-%m-%d %H:%M:%S", localtime(&tmNow));
	// 			if(nType == PROCESSMGR_CMDTYPE_REFRESH)
	// 			{
	// 				m_pCVLog->LogMessage(LOG_LEVEL_WARN, _("Refresh command received from project manager, time:%s"), szTime);
	// 				Refresh();
	// 			}
	// 			else if(nType == PROCESSMGR_CMDTYPE_STOP)
	// 			{
	// 				m_pCVLog->LogMessage(LOG_LEVEL_WARN, _("Stop command received from project manager, time:%s"),	szTime);
	// 				Shutdown();
	// 			}
	// 			else
	// 			{
	// 				m_pCVLog->LogMessage(LOG_LEVEL_WARN, _("Comannd %d received from project manager, neither refresh nor stop command, time %s"), nType, szTime);
	// 			}
	// 		}
	// 	}

	// 	//如果需要停止服务，则停止服务
	// 	if (m_bShutDown)
	// 	{
	// 		//如果服务还没有停止成功，则继续停止
	// 		if (!bFinished)
	// 		{
	// 			bFinished = Finish(bFinished);
	// 		}
	// 	}
	// 	else
	// 	{ 
	// 		//如果过期，则通知停止服务
	// 		if ( IsOutOfDate() )
	// 		{
	// 			LH_LOG((*m_pCVLog), (LOG_LEVEL_CRITICAL, _("$$$ Expired or invalid license, please update your license, otherwise you have 2-hour-trail! $$$")));
	// 			m_bShutDown = true;
	// 			//如果是正式许可证过期，记录事件
	// 			if (m_nLicStatus == CV_LICENSE_NORMAL)
	// 			{
	// 				string strMsg = _("Expired license, valid to:");
	// 				char szTimeValid[ICV_DATESTRING_MAXLEN+ICV_SCANTIMESTRING_MAXLEN] = {'\0'};
	// 				CastTime2Buffer(szTimeValid, sizeof(szTimeValid), m_tvValid);
	// 				strMsg += szTimeValid;
	// 				long lRet = RecordEvent(strMsg.c_str());
	// 				if (lRet != ICV_SUCCESS)
	// 				{
	// 					LH_LOG((*m_pCVLog), (LOG_LEVEL_ERROR, _("Failed to record the event of expired license! [ErrorCode=%d]"), lRet));
	// 				}
	// 			}
	// 		}
	// 	}

	// 	Misc();

	} while( m_bLoopContinued );
}

bool CServiceBase::ProcessCmd( char c )
{
	return false;
}

void CServiceBase::Misc()
{

}

void CServiceBase::PrintHelpScreen()
{
	std::cout << "H/h: Help\n";
	std::cout << "Q/q: Exit\n" << std::endl;	
}

void CServiceBase::PrintStartUpScreen()
{
	std::cout << "****************************************\n";
	std::cout << "**   " << m_strProcName << " Started!\n";
	std::cout << "****************************************\n";
	std::cout << std::endl;	
}

long CServiceBase::GetLicenseAttr( const char* szKey, std::string& strValue )
{
	// if (m_pLicChecker)
	// {
	// 	return m_pLicChecker->GetExetendedLicInfo(szKey, strValue);
	// }
	// return EC_ICV_COMM_BASENO;
	return 0;
}

// 默认处理方式为重新启动
void CServiceBase::Refresh()
{
	// m_bLoopContinued = false;
	// m_bShutDown = true;
	// LH_LOG((*m_pCVLog), (LOG_LEVEL_INFO, _("Refresh message received. Restarting...")));
}

void CServiceBase::Shutdown()
{
	// m_bLoopContinued = false;
	// m_bShutDown = true;
	// LH_LOG((*m_pCVLog), (LOG_LEVEL_INFO, _("Stop message received. Stopping....")));
}

void CServiceBase::Interactive(char c)
{

}

bool CServiceBase::Finish( bool bFinished )
{
	// LH_INFO((*m_pCVLog), (LOG_LEVEL_INFO, _("Service prepared to be finished...")));
	// if (m_bInitSuccess)
	// {
	// 	this->Fini();
	// }
	// LH_INFO((*m_pCVLog), (LOG_LEVEL_INFO, _("Service finished.")));
	// bFinished = true;
	// return bFinished;
	return true;
}

void CServiceBase::InitI18N()
{
	// setlocale(LC_ALL, "");
	// const char* szCVEnv = CVComm.GetCVEnv();
	// if (NULL == szCVEnv || 0 == strcmp(szCVEnv, ""))
	// {
	// 	LH_LOG((*m_pCVLog), (LOG_LEVEL_WARN, _("get icv env var failed.")));
	// 	return;
	// }
	// std::string strPackage(szCVEnv);
	// strPackage += "/i18n/";


	// bindtextdomain(m_strProcName.c_str(), strPackage.c_str());
	// textdomain(m_strProcName.c_str());
	return;
}

long CServiceBase::RecordEvent( const char* szEventMsg )
{
	// return ICV_SUCCESS;
	return 0;
}

void CServiceBase::SetLogFileName(  )
{

}

bool CServiceBase::IsOutOfDate()
{
	//如果不需要检测，永远不过期
	// if (!m_bVerifyProgLicense)
	// {
	// 	return false;
	// }
	// //如果绝对时间没超过过期时间，不过期
	// else if (ACE_OS::gettimeofday() < ACE_Time_Value(m_tvValid))
	// {
	// 	return false;
	// }
	// //否则，过期
	// else
	// {
	// 	return true;
	// }
	return 0;
	
}

// }}//namespace common{namespace service_base{

void SIGPIPE_Handler (int signo) {
	/*LH_LOG(m_pCVLog, (LOG_LEVEL_WARN, _("SIGPIPE signaled!")));*/
}

/**
 *  Main Function.
 *
 *  @param  -[in,out]  int  argc: [comment]
 *  @param  -[in,out]  ACE_TCHAR*  args[]: [comment]
 *
 *  @version     05/19/2008  chenzhiquan  Initial Version.
 */
int main(int argc, char* args[])
{
	ACE_Sig_Action sig_action;
	sig_action.handler(SIGPIPE_Handler);
	sig_action.register_action(SIGPIPE);


	long lRet = g_pServiceHandler->DoService(argc, args);

	if (NULL != g_pServiceHandler)
	{
		delete g_pServiceHandler;
	}
	return static_cast<int>(lRet);
}
