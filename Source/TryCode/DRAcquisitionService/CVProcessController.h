#ifndef _CV_CONTROLLER_OF_PROCESS_
#define _CV_CONTROLLER_OF_PROCESS_
// #include "common/cvcomm.hxx"
#define CVCOMM_API

namespace cvcommon
{
	class CCVProcessControllerImpl;
}

class CVCOMM_API CCVProcessController
{
public:
	CCVProcessController(const char *szProcessName);
	~CCVProcessController();

public:
	bool IsProcessQuitSignaled();
	long SetProcessAliveFlag();
	bool SetConsoleParam(int argc, char* argvs[]);	// 是否接收键盘输入
	bool CV_KbHit ();
	
private:
	cvcommon::CCVProcessControllerImpl*	m_pImpl;
};

#endif
