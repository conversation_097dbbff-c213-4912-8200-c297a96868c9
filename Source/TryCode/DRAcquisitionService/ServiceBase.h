#ifndef _CV_COMMON_SERVICE_BASE_H_
#define _CV_COMMON_SERVICE_BASE_H_

#include <iostream>
using namespace std;
#include "CVProcessController.h"
// #include "common/CV_Time.h"
// #include "common/ProcessQueue.h"
// #include "common/CVLog.h"
// #include "common/LogHelper.h"

// #define PROCESSMGR_RUNTIME_DIR_NAME			ACE_TEXT("PROCESSMGR") // 通知队列对应的共享内存的相对目录前缀
// #define PROCESSMGR_CMDTYPE_REFRESH			1			// 通知刷新命令类型
// #define PROCESSMGR_CMDTYPE_STOP				2			// 停止运行命令类型

// class CLicChecker;

// namespace common{namespace service_base{

class CServiceBase
{
public:
	CServiceBase (const char* szProcName, bool bVerifyProgLicense, const char *szProcLicTag);
	virtual ~CServiceBase();
	virtual long Init(int argc, char* args[]) = 0;
	virtual long Start() = 0;
	virtual void PrintStartUpScreen();
	virtual void PrintHelpScreen();
	virtual long Fini() = 0;

	virtual void Refresh();

	//设置日志文件名称，保证打印ServiceBase里的日志到期望的文件中
	virtual void SetLogFileName();
	//记录事件的虚函数，默认什么事都不做
	virtual long RecordEvent(const char* szEventMsg);

	void RunInteractiveLoop();

	bool Finish( bool bFinished );

	virtual void Interactive(char c);

	void Shutdown();

	virtual bool ProcessCmd(char c);
	// 其它的杂项
	virtual void Misc();

	long DoService(int argc, char* args[]);

	long GetLicenseAttr(const char* szKey, std::string& strValue);

	void InitI18N();
	
	void ExitWhenInitFailed(bool bExit = true){ m_bExitWhenInitFailed = bExit; }

	// CCVLog *m_pCVLog;
protected:
	long DoFinish();
	long CheckProcLicense();
	bool IsOutOfDate();
private:
	// 进程启停控制器
	CCVProcessController m_cvProcController;
	std::string m_strProcName;
	std::string m_strProcTagName;

	// CLicChecker *m_pLicChecker;
	// bool m_bVerifyProgLicense;
	// int m_nLicStatus;
	// TCV_TimeStamp m_tvValid;
	unsigned long m_tvCountDown;

	bool m_bShutDown;
	bool m_bLoopContinued;
	bool m_bInitSuccess;
	bool m_bExitWhenInitFailed;
	
	// CProcessQueue * m_pProcessQue;
	bool m_bUseHardDog;
	// time_t	m_tmLastCheckHardDogValidTime; // 上次检查时有有效硬件狗的检查时间
	// time_t	m_tmLastCheckHardDogTime; // 上次检查时硬件狗的时间
};

// }}//namespace common{namespace service_base{

// using common::service_base::CServiceBase;

#endif//_CV_COMMON_SERVICE_BASE_H_
