cmake_minimum_required(VERSION 3.10)
project(DRAcquisitionService)

# set(CMAKE_CXX_STANDARD 11)

# find_package(Threads REQUIRED)

add_executable(DRAS DRAcquisitionService.cpp ServiceBase.cpp CVProcessController.cpp)

# target_link_libraries(DRAS Threads::Threads)

target_include_directories(DRAS 
    PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/../../../include/ace)

target_link_libraries(DRAS 
    PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/../../../library/ace/libACE.so)


# target_link_libraries(DRAS ${CMAKE_THREAD_LIBS_INIT})