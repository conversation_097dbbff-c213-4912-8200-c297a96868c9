/**************************************************************
 *  Filename:    
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: 
 *
 *  @author:     yang<PERSON>
 *  @version     09/22/2018  yangqi  Initial Version
**************************************************************/

#include <ace/Process_Semaphore.h>
#include <ace/Time_Value.h>
#include <ace/OS_NS_unistd.h>
#include "ServiceBase.h"

// #include "common/stl_inc.h"
// #include "common/LogHelper.h"
// #include "processdb/PDBDef.h"
// #include "common/CVProcessController.h"
// #include "common/ServiceBase.h"

// #include "common/cvRedisAccess.h"
// #include "common/cvRedisDefine.h"
// #include "common/cvGlobalHelper.h"

// #include "gettext/libintl.h"
// #include "common/EvtAlmDef.h"
// #include "eventalarm/EvtAcceptor.h"

// #include "ProcessServer.h"

// #define _(STRING) gettext(STRING)
// STATERWINSTANCE g_pStateRW;

// #ifdef _WIN32
// #include "common/RegularDllExceptionAttacher.h"
// #pragma comment(lib, "ExceptionReport.lib")
// #pragma comment(lib, "dbghelp.lib")

// // global member to capture exception so as to analyze 
// CRegularDllExceptionAttacher	g_RegDllExceptAttacher;
// #endif

// CCVLog g_pdbLog;
// extern double g_dTotalScanCount;
// extern double g_dTotalOverrunCount;
// extern bool g_bPerfTest;
// extern CProcessServer* g_pProcessServer;
/**
 *  @brief    (CtrlHandler ONLY works in WIN32). 
 *  (!!! IMPORTANT !!! SetConsoleCtrlHandler would kill all child-processes before CtrlHandler is called).
 */
// #ifdef _WIN32
// BOOL CtrlHandler(DWORD fdwCtrlType) 
// { 
//     switch (fdwCtrlType) 
//     { 
//         // Handle the CTRL+C signal. 
//         case CTRL_C_EVENT: 
//             return true; 
 
//         // CTRL+CLOSE
//         case CTRL_CLOSE_EVENT: 
//             return true; 
 
//         // Pass other signals to the next handler. 
//         case CTRL_BREAK_EVENT: 
//         case CTRL_LOGOFF_EVENT: 
//         case CTRL_SHUTDOWN_EVENT: 
//         default: 
//             return false; 
//     } 
// } 
// #endif

class CPdbService:public CServiceBase
{
public:
	CPdbService() : CServiceBase ("PbScanner", true, "")
	{
		//初始化失败时程序退出，因为偶尔进程间通信注册queue失败，退出后再由procmgr重启
		ExitWhenInitFailed();
	};

	virtual long Init(int argc, char* args[]);
	//virtual void Refresh();
	virtual long Start();
	virtual void PrintStartUpScreen()
	{
		PrintHelpScreen();
	};
	virtual void PrintHelpScreen();
	virtual long Fini();
	virtual bool ProcessCmd(char c);
	// 其它的杂项
	virtual void Misc();
	//设置日志文件名称，保证打印ServiceBase里的日志到期望的文件中
	virtual void SetLogFileName()
	{		
	}
	//记录事件的虚函数，默认什么事都不做
	virtual long RecordEvent(const char* szEventMsg);
};

long CPdbService::RecordEvent(const char* szEventMsg)
{
    return 0;
	// T_EventMsg EvtMsg;
	// //scada名称
	// if (CVComm.GetLocalScadaNameFromCfg())
	// {
	// 	Safe_CopyString(EvtMsg.szEventNodeName, CVComm.GetLocalScadaNameFromCfg(), sizeof(EvtMsg.szEventNodeName));
	// }

	// Safe_CopyString(EvtMsg.szAppName, PBSCANNER_EXEC_NAME, sizeof(EvtMsg.szAppName));
	// Safe_CopyString(EvtMsg.szMsg, szEventMsg, sizeof(EvtMsg.szMsg));
	// EvtMsg.tmFire = ACE_OS::gettimeofday();

	// long lSendEventRet = Evt_SendEventMsg(EvtMsg, "",0);
	// CV_CHECK_FAIL_LOG(g_pdbLog, lSendEventRet, lSendEventRet,"Send Event Failed, RetCode:%d", lSendEventRet);
	
	// return lSendEventRet;
}

long CPdbService::Init( int argc, char* args[] )
{
    return 0;
	// struct timeval timeout = { 1, 500000 }; // 1.5 seconds
	// g_pStateRW = NULL;
	// STATERW_HostInitialize("",0,timeout,&g_pStateRW);

	// ACE_Time_Value timeValue = ACE_OS::gettimeofday();
	// TCV_TimeStamp timeStamp;
	// timeStamp.tv_sec = (uint32_t)timeValue.sec();
	// timeStamp.tv_usec = (uint32_t)timeValue.usec();
	// char szTime[ICV_HOSTNAMESTRING_MAXLEN] = {'\0'};

	// g_pdbLog.SetLogFileNameThread(PBSCANNER_EXEC_NAME);
	// cvcommon::CastTimeToASCII(szTime, ICV_HOSTNAMESTRING_MAXLEN, timeStamp);
	// // 初始记为正常启动
	// char szStatus[ICV_HOSTNAMESTRING_MAXLEN];
	// memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
	// ACE_OS::snprintf(szStatus, sizeof(szStatus), "0;%s", szTime);
	// STATERW_SetStringCommand(&g_pStateRW, CV_STATUS_PDB_STATUS, szStatus);
	// // 启动时刻
	// STATERW_SetStringCommand(&g_pStateRW, CV_STATUS_PDB_STARTTIME, szTime);


	// long nErr = g_pProcessServer->Init(argc, const_cast<const char ** >(args));
	// if (nErr != ICV_SUCCESS)
	// {
	// 	CV_ERROR(g_pdbLog, nErr, "g_pProcessServer->Init");
	// 	memset(szStatus, 0x00, ICV_HOSTNAMESTRING_MAXLEN);
	// 	ACE_OS::snprintf(szStatus, sizeof(szStatus), "%d;%s", nErr, szTime);
	// 	STATERW_SetStringCommand(&g_pStateRW, CV_STATUS_PDB_STATUS, szStatus);
	// }

	// return nErr;
}

//不实现该方法，继承基类的方法
//void CPdbService::Refresh()
//{
//	CV_INFO(g_pdbLog, "Received Config Refresh Msg");
//}

void CPdbService::PrintHelpScreen()
{
	printf("\n");
	printf("+=====================================================================+\n");
	printf(("|  <<Welcome to PBScanner! >>											|\n"));
	printf(("|  You can configure this service by entering the following commands  |\n"));
	printf(("|  q/Q:Quit															|\n"));
	printf(("|  Others:Print tips													|\n"));
	printf("+======================================================================+\n");
}

long CPdbService::Fini()
{
    return 0;
	// g_pProcessServer->Shutdown();

	// if (g_pStateRW)
	// {
	// 	STATERW_HostUnInitialize(g_pStateRW);
	// 	g_pStateRW = NULL;
	// }
	// //停止日志
	// g_pdbLog.StopLogThread();
	// return ICV_SUCCESS;
}

long CPdbService::Start()
{
	return 0;
}

bool CPdbService::ProcessCmd( char c )
{
	return false;
}

void CPdbService::Misc()
{
}

CServiceBase* g_pServiceHandler = new CPdbService();
