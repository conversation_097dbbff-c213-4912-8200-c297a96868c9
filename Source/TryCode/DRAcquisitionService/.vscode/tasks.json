{"version": "2.0.0", "tasks": [{"type": "cmake", "label": "CMake: configure", "command": "configure", "preset": "${command:cmake.activeConfigurePresetName}", "problemMatcher": [], "detail": "CMake template configure task"}, {"type": "cmake", "label": "CMake: build", "command": "build", "targets": ["all"], "preset": "${command:cmake.activeBuildPresetName}", "group": "build", "problemMatcher": [], "detail": "CMake template build task"}]}