#include "log_to_file.h"
#include <errno.h>
#include <fcntl.h>

#ifndef _WIN32
#include <sys/stat.h>
#else
#include <direct.h>
#endif

#include <ace/OS_NS_stdio.h>

#include <sys/types.h>
#include "date_time.h"
#include <list>
// #include "cvGlobalHelper.h"

namespace cvlog
{
#define CUTE_INVALID_HANDLE   -1
#define CUTE_MAX_FILENAME_LEN  255

log_to_file::log_to_file()
  : log_handle_(CUTE_INVALID_HANDLE),
  current_file_size_(0),
  output_entry_(0)
  //old_output_entry_(0)
{
  last_generate_period_file_time_ = 0;
  type_ = output_entry::om_files;
}
log_to_file::~log_to_file()
{
  if (this->output_entry_)
    delete this->output_entry_;
  this->output_entry_ = 0;

  //if (this->old_output_entry_)
  //  delete this->old_output_entry_;
  //this->old_output_entry_ = 0;

  this->close();
}
int log_to_file::open(const output_entry *cfg)
{
  if (cfg == 0) return -1;

  // opened.
  if (this->output_entry_) return -1;

  // Save strategy.
  this->output_entry_ = new output_entry;
  *this->output_entry_ = *cfg;

  date_time dt(::time(0) + 24*3600);

  this->last_generate_period_file_time_ = dt.date();//::time(0);

  return this->reset();
}
int log_to_file::create_file(void)
{
  // Check and create dir
#ifndef _WIN32
	if (::mkdir(this->output_entry_->log_dir_.c_str(), 0755) == -1)
#else
	if (::mkdir(this->output_entry_->log_dir_.c_str()) == -1)
#endif
  {
    if (errno != EEXIST) return -1;
    errno = 0;
  }
  // Get file size. If the file is exsit and its size is out of 
  // configure, then create a new one
  this->close();
  if (this->output_entry_->generate_file_period_ <= 0)
  {
	  this->file_name_ = this->output_entry_->log_dir_ + '/' + this->build_filename();
	struct stat bf;
	  ::memset(&bf, 0, sizeof(bf));
	  if (::stat(this->file_name_.c_str(), &bf) == 0)
	  {
	    this->current_file_size_ = bf.st_size;
		this->check_file_size();
	  }
  }
  else
  {
	  this->file_name_ = this->output_entry_->log_dir_ + '/' + this->build_filename();
  }
  // Open file
  this->log_handle_ = ::open(this->file_name_.c_str(),   O_CREAT | O_RDWR | O_APPEND,  0644);
  if (this->log_handle_ == CUTE_INVALID_HANDLE) // create file failed
  {
    printf("create_file open(%s) with errno: %d, msg: %s\n", this->file_name_.c_str(), errno, strerror(errno));

	//vc10���п��и�ȱ�ݣ��ļ���С����4G����ļ���ʧ�ܣ����ȴ�ʩ�ǽ�֮ǰ����־�ļ�������,������������־������ʱ���룬
	//Ȼ����ԭ�����������´�һ�����ļ�
	std::string tgt = this->build_rename_filename();
	int ret = ::rename(this->file_name_.c_str(), tgt.c_str());
	if (ret != 0) 
	{
		printf("rolloever_files:%d rename(%s, %s) with errno: %d, msg: %s\n", __LINE__, this->file_name_.c_str(), tgt.c_str(), errno, strerror(errno));
	}
	else
	{
		//�������ɹ����ٴγ������´��ļ�
		printf("rename(%s to %s) ok\n",  this->file_name_.c_str(), tgt.c_str());
		this->log_handle_ = ::open(this->file_name_.c_str(), 	O_CREAT | O_RDWR | O_APPEND, 	0644);
		 if (this->log_handle_ == CUTE_INVALID_HANDLE) // create file failed
		 {
			  printf("create_file open(%s) with errno: %d, msg: %s\n", this->file_name_.c_str(), errno, strerror(errno));
			   return -1;
		 }
	}   
  }
  return 0;
}

bool log_to_file::is_valid_log_handle()
{
    return this->log_handle_ != CUTE_INVALID_HANDLE;
}

void log_to_file::reopen_file()
{
    this->log_handle_ = ::open(this->file_name_.c_str(), 
                             O_CREAT | O_RDWR | O_APPEND, 
                             0644);
    if (CUTE_INVALID_HANDLE == this->log_handle_) {
        printf("reopen_file open(%s) with errno: %d, msg: %s", this->file_name_.c_str(), errno, strerror(errno));
    }
}

int log_to_file::close(void)
{
  if (this->log_handle_ != CUTE_INVALID_HANDLE)
    ::close(this->log_handle_);
  this->log_handle_ = CUTE_INVALID_HANDLE;
  return 0;
}

int log_to_file::reset(void)
{
  // Build file name
  //this->build_filename();

  this->close();

  if (this->output_entry_->generate_file_period_ > 0)
  {
	  //��Ŀ¼������ļ����һ�飬�Ӷ�ɾ�����ļ�
	  std::string strPrefix = this->output_entry_->file_name_ + "_";
	  size_t nPrefixLen = strPrefix.size();
	  std::list<std::string> fileNameList;
	  //date_time dt( ::time(0) - 24 * 3600 * this->output_entry_->generate_file_period_ );

	  std::string strKeepFileName = build_filename( ::time(0) - 24 * 3600 * this->output_entry_->generate_file_period_);

	  long lRet = 0;
    // todo CVFileHelper::ListFilesInDir(this->output_entry_->log_dir_.c_str(), fileNameList);
	  for (std::list<std::string>::iterator it = fileNameList.begin();
		   it != fileNameList.end(); it++)
	  {
		  if((*it).substr(0, nPrefixLen) == strPrefix)
		  {
			 if ((*it) < strKeepFileName )
			 {
				 //todo CVFileHelper::DeleteAFile(it->c_str(), this->output_entry_->log_dir_.c_str());
			 }
		  }
	  }
	  
  }
  

  return this->create_file();
}
void log_to_file::update_config(const output_entry *entry)
{
  if (this->output_entry_ == 0)
  {
    //this->output_entry_ = new output_entry;
	open(entry);
	return;
  }

  output_entry old_output_entry_ = *this->output_entry_;
  *this->output_entry_ = *entry;
  if ((this->output_entry_->generate_file_period_
       != old_output_entry_.generate_file_period_)
      ||
      (this->output_entry_->file_name_ != old_output_entry_.file_name_)
      ||
      (this->output_entry_->log_dir_ != old_output_entry_.log_dir_)
     )
  {
    this->reset();
  }
}
int log_to_file::log(const char *log_msg, int len, int , time_t now/* = 0*/)
{
  if (this->log_handle_ == CUTE_INVALID_HANDLE)
    return -1;

  if (this->output_entry_->generate_file_period_ > 0)
	  // 1. Check a_file_a_period is on or not
  {
	  // Check time
	  if (this->generate_period_file(now) != 0)
		  return -1;
  }else if (this->check_file_size()) // 1. Check file size
  {
    this->close();
    this->log_handle_ = ::open(this->file_name_.c_str(), 
                               O_CREAT | O_RDWR | O_APPEND, 
                               0644);
    if (CUTE_INVALID_HANDLE == this->log_handle_) {
        printf("log open(%s) with errno: %d, msg: %s", this->file_name_.c_str(), errno, strerror(errno));
    }
  }//else 

  // 3. Record the log msg
  int result = 0;
  do
  {
    if ((result = ::write(this->log_handle_, log_msg, len)) <= 0)
    {
      if (errno == ENOSPC)
        return 0;
      else if (errno == EINTR)
        continue;
      this->close();
      return -1;
    }
  }while (0);
  this->current_file_size_ += result;
  return result;
}
int log_to_file::check_file_size(void)
{
  if (this->current_file_size_ >= 
      this->output_entry_->single_file_size_)
  {
    // Backup file, switch file

    if (this->output_entry_->rolloever_> 0)
    {
      this->rolloever_files();
    }else
    {
      ::unlink(this->file_name_.c_str());
    }
    this->current_file_size_ = 0;
    return 1;
  }
  return 0;
}
int log_to_file::rolloever_files()
{
  char src[CUTE_MAX_FILENAME_LEN + 1] = {0};
  char tgt[CUTE_MAX_FILENAME_LEN + 1] = {0};
  ACE_OS::snprintf(src, CUTE_MAX_FILENAME_LEN, 
	  "%s%d",
	  this->file_name_.c_str(),
	  this->output_entry_->rolloever_);
  int ret = ::unlink(src);
  if (-1 == ret) {
    printf("rolloever_files:%d unlink(%s) with errno: %d, msg: %s", __LINE__, src, errno, strerror(errno));
  }

  for (int i = this->output_entry_->rolloever_ - 1; 
       i >= 1; 
       --i)
  {
    ACE_OS::snprintf(src, CUTE_MAX_FILENAME_LEN, 
               "%s%d",
               this->file_name_.c_str(),
               i);
    ACE_OS::snprintf(tgt, CUTE_MAX_FILENAME_LEN, 
               "%s%d",
               this->file_name_.c_str(),
               i + 1);
    ret = ::rename(src, tgt);
    if (ret != 0) {
        printf("rolloever_files:%d rename(%s, %s) with errno: %d, msg: %s", __LINE__, src, tgt, errno, strerror(errno));
    }
    ::memset(src, 0, sizeof(CUTE_MAX_FILENAME_LEN));
    ::memset(tgt, 0, sizeof(CUTE_MAX_FILENAME_LEN));
  }
  ::memset(tgt, 0, sizeof(CUTE_MAX_FILENAME_LEN));
  if (this->log_handle_ != CUTE_INVALID_HANDLE)
  {
	  ::close(this->log_handle_);
	  this->log_handle_ = CUTE_INVALID_HANDLE;
  }

  ACE_OS::snprintf(tgt, CUTE_MAX_FILENAME_LEN, 
             "%s%d",
             this->file_name_.c_str(),
             1);
  ret = ::rename(this->file_name_.c_str(), tgt);
  if (ret != 0) {
	  printf("rolloever_files:%d rename(%s, %s) with errno: %d, msg: %s", __LINE__, this->file_name_.c_str(), tgt, errno, strerror(errno));
  }
  return 0;
}
std::string log_to_file::build_filename(time_t now)
{
  //this->file_name_.clear();

	std::string strFileName;

  if (this->output_entry_->generate_file_period_ > 0)
  {
	  if (now == 0)
	  {
		  now = ::time(0);
	  }
	  char szTmp[16];
	  
	  date_time dt(now);
	  strFileName = this->output_entry_->file_name_ 
		  + "_"
		  + dt.date_to_str(szTmp, sizeof(szTmp))
		  + ".log";
  } 
  else
  {
	  strFileName = this->output_entry_->file_name_ 
		  + ".log";
  }
  return strFileName;
}

std::string log_to_file::build_rename_filename(time_t now)
{
	std::string strFileName;
	if (now == 0)
	{
		now = ::time(0);
	}
	char szYMD[16];
	char szHMS[16];

	date_time dt(now);
	sprintf(szHMS, "%02d-%02d-%02d", dt.hour(), dt.minut(), dt.sec());
	strFileName = 
		this->output_entry_->log_dir_ 
		+ '/'
		+this->output_entry_->file_name_ 
		+ "_"
		+ dt.date_to_str(szYMD, sizeof(szYMD))
		+"-"
		+szHMS
		+ ".log";
	return strFileName;
}

int log_to_file::generate_period_file(time_t now)
{
  if (now == 0) now = ::time(0);
  //if (difftime(now, this->last_generate_period_file_time_) 
  //    >= this->output_entry_->generate_file_period_)   // next period
  if (now > this->last_generate_period_file_time_)  
  {
    this->close();
    //if (this->output_entry_->rolloever_> 0)
	//{
      //this->rolloever_files();
	//}
    //else
	//{
      //::unlink(this->file_name_.c_str());
	//}
	// delete outdated file

	std::string strDeleteFileName = this->output_entry_->log_dir_ + '/' + this->build_filename( now - 24 * 3600 * (this->output_entry_->generate_file_period_ + 1));

	::unlink(strDeleteFileName.c_str());

    this->file_name_ = this->output_entry_->log_dir_ + '/' + this->build_filename(now);
    this->log_handle_ = ::open(this->file_name_.c_str(), 
                               O_CREAT | O_RDWR | O_APPEND, 
                               0644);
	date_time dt(this->last_generate_period_file_time_ + 25 * 3600);
    this->last_generate_period_file_time_ = dt.date();

    if (this->log_handle_ == CUTE_INVALID_HANDLE) // create file failed
    {
      return -1;
    }
  }
  return 0;
}

} // namespace ndk

