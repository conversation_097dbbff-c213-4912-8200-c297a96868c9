/**************************************************************
 *  Filename:    TimerSyncTask.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: 定时同步触发线程，定时接收日志消息并写日志.
 *
**************************************************************/
// TimerSyncTask.h: interface for the CTimerSyncTask class.
//
//////////////////////////////////////////////////////////////////////

#if !defined(AFX_TIMERSYNCTASK_H__4DBFF65B_599F_4D37_A965_39424D87424D__INCLUDED_)
#define AFX_TIMERSYNCTASK_H__4DBFF65B_599F_4D37_A965_39424D87424D__INCLUDED_

#if _MSC_VER > 1000
#pragma once
#endif // _MSC_VER > 1000

#include "ace/OS_NS_unistd.h"
#include "ace/Task.h"
#include "ace/Singleton.h"
// #include "errcode/error_code.h"
#include "SimpleQueue.h"

#define RW_QUEUE			0  
#define QUEUE_MAXNUM		100000

typedef struct tagLogCmd
{
	int nLevel;
	std::string strLog;

}LogCmd;

class CCVLog;
class CCVLogImp;
class CTimerSyncTask : public ACE_Task<ACE_MT_SYNCH>
{
	friend class ACE_Singleton<CTimerSyncTask, ACE_Thread_Mutex>;
public:
	CTimerSyncTask();
	virtual ~CTimerSyncTask();
	void SetCVLogImpl(CCVLogImp *pLogImp);

	virtual int svc();
	virtual int close (u_long flags = 0);

	virtual int putq(LogCmd &Msg)
	{
		int nCount = m_RWQueue.size();
		if (m_RWQueue.size()<QUEUE_MAXNUM)
			return m_RWQueue.enqueue(Msg);
		else
			return 0;
	}

	int GetQueueSize()
	{
		return m_RWQueue.size();
	}
	bool m_bTimerSyncQuit;
	CCVLogImp *m_pLogImp;
private:

	virtual int getq(LogCmd &Msg, ACE_Time_Value *tv = 0)
	{
		if (tv)
		{
			ACE_Time_Value tvAbsolute = ACE_OS::gettimeofday() + *tv;
			return m_RWQueue.dequeue(Msg, &tvAbsolute);
		}
		else
		{
			return m_RWQueue.dequeue(Msg, tv);
		}
	}

	CSimpleThreadQueue<LogCmd> m_RWQueue;	// 读写消息队列
	
};

//定时消息生成线程
typedef ACE_Singleton<CTimerSyncTask, ACE_Thread_Mutex> TimerSyncTask;
#define TIMESYNC_TASK TimerSyncTask::instance()

#endif // !defined(AFX_TIMERSYNCTASK_H__4DBFF65B_599F_4D37_A965_39424D87424D__INCLUDED_)
