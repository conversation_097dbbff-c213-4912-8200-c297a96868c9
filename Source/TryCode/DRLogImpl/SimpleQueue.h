/**************************************************************
 *  Filename:    SimpleQueue.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Simple Thread Queue.
 *
 *  @author:     ch<PERSON><PERSON><PERSON><PERSON>
 *  @version     06/06/2008  chenzhiquan  Initial Version
**************************************************************/
#ifndef _SIMPLE_THREAD_QUEUE_H_
#define _SIMPLE_THREAD_QUEUE_H_
#if _MSC_VER > 1000
#pragma once
#endif
#include <ace/Atomic_Op.h>
#include <ace/Thread_Mutex.h>
#include <ace/Condition_Thread_Mutex.h>
#include <ace/Method_Request.h>
//#include "stl_inc.h"
#include "error_code.h"
#include <list>



// #ifdef WIN32
// #	if defined(ETIME) && (ETIME != ERROR_SEM_TIMEOUT)
// #		undef ETIME
// #	endif
// #   if !defined (ETIME)
// #     define ETIME                  ERROR_SEM_TIMEOUT
// #   endif /* !ETIME */
// #endif/*WIN32*/

template<class Type>
class CSimpleThreadQueue
{
public:
	typedef typename std::list<Type>::iterator QueuePosition;

public:
	CSimpleThreadQueue() : m_cond(m_mutex), m_size(0) {};
	  ~CSimpleThreadQueue(){};
	  
	// Enqueue Item
	int enqueue(Type item);

	// Dequeue Item.
	int dequeue(Type &item, ACE_Time_Value *tv = 0);

	// Enqueue Head
	int enqueue_head(Type item);

	// Return Queue Size
	size_t size();

	void clear();
protected:
	std::list<Type> m_list;		// List To Store Obj.
	ACE_Thread_Mutex m_mutex; // Thread Mutex
	ACE_Condition_Thread_Mutex m_cond; // Thread Condition
	//ACE_Atomic_Op<ACE_Thread_Mutex, size_t> m_size;
	long m_size;
};

/**
*  Dequeue Item.
*
*  @return pointer of ACE_Method_Request object.
*
*  @version     07/19/2007  chenzhiquan  Initial Version.
*/
template<class Type>
int CSimpleThreadQueue<Type>::dequeue(Type &item, ACE_Time_Value *tv)
{	
	while (true)
	{
		m_mutex.acquire();
		if (! m_list.empty() )
			break;
		
		int nErr = m_cond.wait(tv);
		m_mutex.release();
		if (nErr == -1)
		{
			return EC_ICV_COMM_SIMPLE_QUEUE_TIMEOUT;
		}
	}
	
	item = m_list.front();
	m_list.pop_front();
	-- m_size;
	//-- queued_action_count_;
	m_mutex.release();

	return ICV_SUCCESS;
}

/**
*  Enqueue Head.
*
*  @param  -[in]  Type*  item: [ item to enqueue]
*
*  @version     07/19/2007  lijingjing  Initial Version.
*/
template<class Type>
int CSimpleThreadQueue<Type>::enqueue_head(Type item)
{
	m_mutex.acquire();
	m_list.push_front(item);
	++ m_size;
	m_cond.signal();
	m_mutex.release();
	//++ total_action_count_;
	//++ queued_action_count_;
	
	return ICV_SUCCESS;
}


/**
*  Enqueue Item.
*
*  @param  -[in]  Type*  item: [ item to enqueue]
*
*  @version     07/19/2007  chenzhiquan  Initial Version.
*/
template<class Type>
int CSimpleThreadQueue<Type>::enqueue(Type item)
{
	m_mutex.acquire();
	m_list.push_back(item);
	++ m_size;
	m_cond.signal();
	m_mutex.release();
		
	return ICV_SUCCESS;
}

/**
 *  Return Queue Size.
 *
 *
 *  @version     06/08/2008  chenzhiquan  Initial Version.
 */
template<class Type>
size_t CSimpleThreadQueue<Type>::size()
{
//	size_t nSize;
//	mutex_.acquire();
//	nSize = list_.size();
//	mutex_.release();
//	return size();
	//return m_size.value(); //m_list.size();
	return m_size;
}

/**
 *  Clear Queue .
 *
 *
 *  @version     06/08/2008  wangxiao  Initial Version.
 */
template<class Type>
void  CSimpleThreadQueue<Type>::clear()
{
	m_mutex.acquire();
	m_list.clear();
	m_mutex.release();
}

#endif//_SIMPLE_THREAD_QUEUE_H_
