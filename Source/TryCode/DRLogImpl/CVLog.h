/**************************************************************
 *  Filename:    CVLog.h
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: create.
 *
 *  @author:     <PERSON><PERSON><PERSON>
 *  @version     03/13/2008  zhaohui  Initial Version
 *  @version     05/19/2008  zhao<PERSON>  change CVLog from dll to lib .
 *  @version     06/25/2008  caichunlei 添加LogErrMessage接口 .
**************************************************************/

#ifndef __CVLOG_H
#define __CVLOG_H

#ifdef _WIN32
#	ifdef cv6logimpl_EXPORTS
#		define CVLog_API __declspec(dllexport)
#	else
#		define CVLog_API __declspec(dllimport)
#		pragma comment(lib,"cv6logimpl.lib")
#	endif
#else//_WIN32
#	define CVLog_API
#endif//_WIN32

#define ICV_LONGFILENAME_MAXLEN			1024		// 文件全路径的最大长度(实际长度受限于操作系统，windows下文件夹长度小于249，文件名全路径长度小于260)
#define ICV_SHORTFILENAME_MAXLEN		256			// 文件全名称(不含路径)的最大长度
// USER SPECIFIED DATATYPES
#define DT_SIZE_TIMESTR		24		// strlen("2008-01-01 12:12:12.000") + 1
//copy from cv_datatype.h



#define MAX_LOG_SIZE 4096

enum CV_LOG_LEVEL
{
	LOG_LEVEL_TRACE = 1,
	LOG_LEVEL_DEBUG = 2,
	LOG_LEVEL_INFO = 4,
	LOG_LEVEL_WARN = 8,
	LOG_LEVEL_ERROR = 16,
	LOG_LEVEL_CRITICAL = 32,
	LOG_LEVEL_NOTICE = 256,
};

class CCVLogImp;

class CVLog_API CCVLog
{
public:
	CCVLog();
	~CCVLog();
	//设置日志输出文件名，也作为日志类别标识，不调用此方法默认为General
	bool SetLogFileName(const char *szLogFileName); 
	//线程写日志的接口，必须自己调用StopLogThread()
	bool SetLogFileNameThread(const char *szLogFileName);
	//线程停止接口
	bool StopLogThread();
	//记录一条日志
	void LogMessage(int nLogLevel, const char *szFormat, ...);
	//记录一条日志
	void NewLogMessage(int nLogLevel, int nErrCode, int nLineNum, const char* szFuncName,  const char *szFormat, ...);
	//记录包含错误代码的日志信息
	void LogErrMessage(int nErrCode, const char *szFormat, ...);
	//修改LogLevel,返回原来的LogLevel
	CV_LOG_LEVEL SwitchLogLevel(CV_LOG_LEVEL nNewLevel);
	void SetLogOnMonitor(bool bLogOnMonitor);
private:
	CCVLogImp*	m_pCVLogImp;
};

#define CV_LOG(X,LEVLE,ERR, ...) {X.NewLogMessage(LEVLE, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);}
#define CV_TRACE(X, ...) {X.NewLogMessage(LOG_LEVEL_TRACE, 0, __LINE__, __FUNCTION__, __VA_ARGS__);}
#define CV_DEBUG(X, ...) {X.NewLogMessage(LOG_LEVEL_DEBUG, 0, __LINE__, __FUNCTION__, __VA_ARGS__);}
#define CV_INFO(X, ...) {X.NewLogMessage(LOG_LEVEL_INFO, 0, __LINE__, __FUNCTION__, __VA_ARGS__);}
#define CV_WARN(X,ERR, ...) {X.NewLogMessage(LOG_LEVEL_WARN, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);}
#define CV_ERROR(X,ERR, ...) {X.NewLogMessage(LOG_LEVEL_ERROR, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);}
#define CV_CRITICAL(X,ERR, ...) {X.NewLogMessage(LOG_LEVEL_CRITICAL, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);}


#define CV_CHECK_NULL_LOG(LOGOBJ, X, ERR, ...){\
	if (NULL == (X)){\
	LOGOBJ.NewLogMessage(LOG_LEVEL_ERROR, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);\
	}\
}


#define CV_CHECK_NULL_LOG_RETURN(LOGOBJ, X,ERR, ...){\
	if (NULL == (X)){\
	LOGOBJ.NewLogMessage(LOG_LEVEL_ERROR, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);\
	return;\
	}\
}

#define CV_CHECK_NULL_LOG_RETURN_ERR(LOGOBJ, X, ERR, ...){\
	if (NULL == (X)){\
	LOGOBJ.NewLogMessage(LOG_LEVEL_ERROR, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);\
	return ERR;\
	}\
}

#define CV_CHECK_NULL_LOG_CONTINUE(LOGOBJ, X, ERR, ...){\
	if (NULL == (X)){\
	LOGOBJ.NewLogMessage(LOG_LEVEL_ERROR, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);\
	continue;\
	}\
}

#define CV_CHECK_NULL_RETURN(X){\
	if (NULL == (X)){\
	return;\
	}\
}

#define CV_CHECK_NULL_RETURN_ERR(X, ERR){\
	if (NULL == (X)){\
	return ERR;\
	}\
}

#define CV_CHECK_NULL_CONTINUE(X){\
	if (NULL == (X)){\
	continue;\
	}\
}

#define CV_CHECK_FAIL_LOG(LOGOBJ, X, ERR, ...){\
	if (0 != (X)){\
	LOGOBJ.NewLogMessage(LOG_LEVEL_ERROR, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);\
	}\
}

#define CV_CHECK_FAIL_LOG_RETURN(LOGOBJ, X, ERR, ...){\
	if (0 != (X)){\
	LOGOBJ.NewLogMessage(LOG_LEVEL_ERROR, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);\
	return;\
	}\
}

#define CV_CHECK_FAIL_LOG_RETURN_ERR(LOGOBJ, X, ERR, ...){\
	if (0 != (X)){\
	LOGOBJ.NewLogMessage(LOG_LEVEL_ERROR, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);\
	return ERR;\
	}\
}

#define CV_CHECK_FAIL_LOG_CONTINUE(LOGOBJ, X, ERR, ...){\
	if (0 != (X)){\
	LOGOBJ.NewLogMessage(LOG_LEVEL_ERROR, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);\
	continue;\
	}\
}

#define CV_CHECK_FAIL_RETURN(X){\
	if (0 != (X)){\
	return;\
	}\
}

#define CV_CHECK_FAIL_RETURN_ERR(X, ERR){\
	if (0 != (X)){\
	return ERR;\
	}\
}

#define CV_CHECK_FAIL_CONTINUE(X){\
	if (0 != (X)){\
	continue;\
	}\
}

#define CV_CHECK_TRUE_LOG(LOGOBJ, X, ERR, ...){\
	if ((X)){\
	LOGOBJ.NewLogMessage(LOG_LEVEL_ERROR, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);\
	}\
}

#define CV_CHECK_TRUE_LOG_LEVEL(LOGOBJ,LEVEL, X, ERR, ...){\
	if ((X)){\
	LOGOBJ.NewLogMessage(LEVEL, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);\
	}\
}

#define CV_CHECK_TRUE_LOG_RETURN(LOGOBJ, X, ERR, ...){\
	if ((X)){\
	LOGOBJ.NewLogMessage(LOG_LEVEL_ERROR, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);\
	return;\
	}\
}

#define CV_CHECK_TRUE_LOG_RETURN_ERR(LOGOBJ, X, ERR, ...){\
	if ((X)){\
	LOGOBJ.NewLogMessage(LOG_LEVEL_ERROR, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);\
	return ERR;\
	}\
}

#define CV_CHECK_TRUE_LOG_CONTINUE(LOGOBJ, X, ERR, ...){\
	if ((X)){\
	LOGOBJ.NewLogMessage(LOG_LEVEL_ERROR, ERR, __LINE__, __FUNCTION__, __VA_ARGS__);\
	continue;\
	}\
}

#define CV_CHECK_TRUE_RETURN(X){\
	if ((X)){\
	return;\
	}\
}

#define CV_CHECK_TRUE_RETURN_ERR(X, ERR){\
	if ((X)){\
	return ERR;\
	}\
}

#define CV_CHECK_TRUE_CONTINUE(X){\
	if ((X)){\
	continue;\
	}\
}
//extern CCVLogImp CVLog;

#endif //!defined(__CVLOG_H)


