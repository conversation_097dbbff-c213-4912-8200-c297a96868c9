cmake_minimum_required(VERSION 3.10)

#Setting Project Name
PROJECT (cv6logimpl)

############FOR_MODIFIY_BEGIN#######################
# INCLUDE($ENV{DualiCVSRCDIR}CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} CVLogImpl.cpp 
				log_to_file.cpp 
	)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME cv6logimpl)
#Setting library type used when build a library
#SET(LIB_TYPE STATIC)
SET(LIB_TYPE SHARED)

# SET(LINK_LIBS ACE cv6comm tinyxml intl) 后续看下怎么写的

add_library(${TARGET_NAME} ${LIB_TYPE} ${SRCS})

target_include_directories(${TARGET_NAME}
    PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/../../../include/ace
	PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/../../../include/tinyxml
)

target_link_libraries(${TARGET_NAME}
    PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/../../../library/ace/libACE.so
	PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/../../../library/libtinyxml.so
)