#ifndef _CV_TIME_STAMP_HEADER_
#define _CV_TIME_STAMP_HEADER_

#include <stdio.h>
#include <string>
#include <time.h>
#include <string.h>
#ifdef _WIN32
#	include <winsock2.h>
#else
#	include <stdint.h>
#	include <sys/types.h>
#	include <sys/time.h>
#endif /*_WIN32*/

//typedef timeval TCV_TimeStamp;

struct TCV_TimeStamp
{
#ifdef _WIN32
	UINT32 tv_sec;         /* seconds */
	UINT32 tv_usec;        /* and microseconds */
#else
	uint32_t tv_sec;         /* seconds */
	uint32_t tv_usec;        /* and microseconds */
#endif//_WIN32

	/// Returns the value of the object as a timeval.
	operator timeval () const
	{
		timeval tv;
		tv.tv_sec = tv_sec;
		tv.tv_usec = tv_usec;
		return tv;
	}

	TCV_TimeStamp() : tv_usec(0), tv_sec(0)
	{
	}


	TCV_TimeStamp(const timeval &tv)
	{
		this->tv_sec = tv.tv_sec;
		this->tv_usec = tv.tv_usec;
	}

	/// Assign @ tv to this
	TCV_TimeStamp &operator = (const timeval &tv)
	{
		this->tv_sec = tv.tv_sec;
		this->tv_usec = tv.tv_usec;
		return *this;
	}
};

class CEATimeStampConvert
{
public:
	static void string2time(std::string& buffer, TCV_TimeStamp& ts)
	{
		const char* szTime = buffer.c_str();
		struct tm tmValue;
		memset(&tmValue, 0x00, sizeof(struct tm));

		int msec = 0;
		if (strlen(szTime) > 0)
		{
			sscanf(szTime, "%d-%d-%d %d:%d:%d.%d", &tmValue.tm_year, &tmValue.tm_mon,
				&tmValue.tm_mday, &tmValue.tm_hour, &tmValue.tm_min, &tmValue.tm_sec, &msec);

			tmValue.tm_year -= 1900;
			tmValue.tm_mon -= 1;

			//ts.tv_sec = (long)mktime(&tmValue); 
			ts.tv_sec = mktime(&tmValue);
			ts.tv_usec = msec;
		}
	}

	static void time2string(const TCV_TimeStamp& tcvTime, std::string& strTime)
	{
		time_t temp = tcvTime.tv_sec;
		//struct tm *pTmValue = localtime((time_t*)(&tcvTime.tv_sec)); 
		// 32bit的tcvTime.tv_sec强转为time_t(32bit,64bit都可能)调用localtime时有问题，要转换一次
		struct tm *pTmValue = localtime((time_t*)(&temp));
		char szTime[128] = { '\0' };
		sprintf(szTime, "%d-%d-%d %d:%d:%d.%d", pTmValue->tm_year + 1900, pTmValue->tm_mon + 1,
			pTmValue->tm_mday, pTmValue->tm_hour, pTmValue->tm_min, pTmValue->tm_sec, tcvTime.tv_usec / 1000);

		strTime = szTime;
	}
};
#endif // _CV_TIME_STAMP_HEADER_
