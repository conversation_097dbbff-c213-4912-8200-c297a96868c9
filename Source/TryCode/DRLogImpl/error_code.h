
/*
*  encoding: GBK, TAB: 4
*  This file is auto genereated by ec_gen_h.py tool,
*
*  please do not edit it manually.
*
*  author: wuhaochi
*
*  Generated time: 2012-06-25 13:30:27.188000
*
*/
#ifndef _ERROR_CODE_H_
#define _ERROR_CODE_H_

#define ICV_SUCCESS 									0 // 执行正确
#define EC_ICV_CC_XMLREQUESTERROR   					11001 // 请求XML消息格式错误
#define EC_ICV_CC_XMLRESPONSEERROR  					11002 // 响应XML消息格式错误
#define EC_ICV_CC_REQUESTOBJECTTYPENOTFOUND 			11050 // 客户端请求的对象类型未找到
#define EC_ICV_CC_REQUESTOBJECTNAMENOTFOUND 			11051 // 客户端请求的对象名称未找到
#define EC_ICV_CC_FAIL_TO_ADD_OBJECT					11052 // 增加对象失败
#define EC_ICV_CC_CANCEL								11151 // 客户端取消操作
#define EC_ICV_CC_OVERMARGIN							11152 // 客户端输入的数据超限
#define EC_ICV_CC_NOFILE								11153 // 本地没找到配置文件（驱动/扩展配置）
#define EC_ICV_CC_NOWINDOW  							11154 // 没找到目标窗口
#define EC_ICV_CC_NOAUTH								11155 // 用户没有权限
#define EC_ICV_CC_MSXMLCRTINSTANCEFAIL  				11156 // MSXML创建实例失败
#define EC_ICV_CC_LOADXMLFAIL   						11157 // 加载XML失败
#define EC_ICV_CC_PATHNAMETOOLLONG  					11201 // 路径名太长
#define EC_ICV_CC_CANNOTWRITEFILE   					11202 // 不能写入文件
#define EC_ICV_CC_PATHNAMECONVERTFAIL   				11203 // 路径名转换失败
#define EC_ICV_CC_FILENOTEXIST  						11204 // 远程接口中传入的文件不存在
#define EC_ICV_CC_IPINVALID 							11205 // IP地址非法
#define EC_ICV_CC_FILEREADFAILED						11206 // 读取文件失败
#define EC_ICV_CC_FAILTOLOADDLL 						11207 // 加载动态库失败
#define EC_ICV_CC_FAILTOGETFUNADDR  					11208 // 获取动态库函数地址失败
#define EC_ICV_CC_VERSIONNOTCOMPATIBLE  				11209 // 版本号不兼容
#define EC_ICV_CC_PARAMINVALID  						11210 // 参数非法
#define EC_ICV_CC_FAILTOREGLOCALQUE 					11211 // 注册本地Queue失败
#define EC_ICV_CC_FAILTOREGREMOTEQUE					11212 // 注册CVNDK的CVNDK_RegRemoteQueue返回NULL
#define EC_ICV_CC_PACKPARSERNOSECTIONS  				11213 // 解析后的包没有分区
#define EC_ICV_CC_REQUESTOBJECTNOTEXIST 				11214 // 请求对象不存在
#define EC_ICV_CC_REQUESTOERATIONINVALID				11215 // 请求操作非法
#define EC_ICV_CC_FAILTOSTARTTASK   					11216 // 不能启动线程
#define EC_ICV_CC_PACKET_INVALIDLENGHT  				11217 // 传递的数据包长度非法
#define EC_ICV_CC_PACKET_RECVTAILERWRONG				11218 // 接收到的数据包尾部错误
#define EC_ICV_CC_PACKET_NOSECTION  					11219 // 数据包中包含的节的个数为0
#define EC_ICV_CC_SECAREANOTEXIST   					11220 // 安全区不存在
#define EC_ICV_CC_INVALIDTAGTYPE						11221 // 数据类型非法
#define EC_ICV_CC_INVALIDQUERYCOUNT 					11222 // 请求个数非法
#define EC_ICV_CC_INVALIDALARMTYPE  					11223 // 报警类型非法
#define EC_ICV_CC_INVALIDATTRIBUTETYPE  				11224 // 属性类型非法
#define EC_ICV_CC_FAILTOADDCLASSATTRIBS 				11225 // 执行操作中,增加类属性时失败
#define EC_ICV_CC_UNKNOWNCHECKOUTSTATUS 				11226 // 签出状态未知
#define EC_ICV_CC_REQUEST_TYPE_NOTDEFINED   			11227 // 请求类型未定义
#define EC_ICV_CC_INVALIDDEPLOYNUM  					11228 // 生成配置文件的个数超限
#define EC_ICV_CC_NOSUPPORTHMI  						11229 // 目前PDBCfg不支持为HMI生成插件
#define EC_ICV_CC_TOOMANYCLSQUERYLOOP   				11230 // 查询类属性时循环次数太多(超过8次)
#define EC_ICV_CC_NOPARENTCLASS 						11231 // 配置库中未查找到应有的父类
#define EC_ICV_CC_CANNOTLOADDRIVERCFGFILE   			11232 // 不能加载驱动的XML配置文件
#define EC_ICV_CC_FAILTOCREATEFILEPATH  				11233 // 创建目录路径失败
#define EC_ICV_CC_FAILTODELETEFILEPATH  				11234 // 删除目录路径失败
#define EC_ICV_CC_FAILTORENAMEFILEPATH  				11235 // 重命名目录路径失败
#define EC_ICV_CC_FAILTODELETEFILE  					11236 // 删除文件路径失败
#define EC_ICV_CC_FILEPATHNOTEXIST  					11237 // 文件路径不存在
#define EC_ICV_CC_QUERYNODEISNOTONE 					11238 // 部署时,查询到的节点个数不是1个
#define EC_ICV_CC_ANOTHERDEPLOYEXECUTING				11239 // 另外一个部署正在进行中
#define EC_ICV_CC_FILEEMPTY 							11240 // 远程接口中传入的文件为空
#define EC_ICV_CC_COMPRESSFAIL  						11241 // 压缩文件失败
#define EC_ICV_CC_UNCOMPRESSFAIL						11242 // 解压文件失败
#define EC_ICV_CC_FAILTOCOPYDIR                         11243 // 拷贝文件目录失败
#define EC_ICV_CC_DBFAIL								11301 // 配置库错误:操作失败
#define EC_ICV_CC_DBINTERNAL							11302 // 配置库错误:内部错误
#define EC_ICV_CC_DBPERM								11303 // 配置库错误:没有权限进行此操作
#define EC_ICV_CC_DBABORT   							11304 // 配置库错误:回调函数请求失败
#define EC_ICV_CC_DBBUSY								11305 // 配置库错误:数据库文件加锁
#define EC_ICV_CC_DBLOCKED  							11306 // 配置库错误:数据库中的表加锁
#define EC_ICV_CC_DBNOMEM   							11307 // 配置库错误:malloc()失败
#define EC_ICV_CC_DBREADONLY							11308 // 配置库错误:试图写入一个只读的数据库
#define EC_ICV_CC_DBINTERRUPT   						11309 // 配置库错误:由sqlite_interrupt()导致的操作终止
#define EC_ICV_CC_DBIOERR   							11310 // 配置库错误:I/O错误
#define EC_ICV_CC_DBCORRUPT 							11311 // 配置库错误:磁盘映像文件不合适
#define EC_ICV_CC_DBNOTFOUND							11312 // 配置库错误:表或者记录没有找到
#define EC_ICV_CC_DBFULL								11313 // 配置库错误:由于数据库已满，插入失败
#define EC_ICV_CC_DBCANTOPEN							11314 // 配置库错误:不能打开数据库文件
#define EC_ICV_CC_DBPROTOCOL							11315 // 配置库错误:数据库锁协议失败
#define EC_ICV_CC_DBEMPTY   							11316 // 配置库错误:数据库表为空
#define EC_ICV_CC_DBSCHEMA  							11317 // 配置库错误:数据库表改变
#define EC_ICV_CC_DBTOOBIG  							11318 // 配置库错误:太多的数据写入一张表中
#define EC_ICV_CC_DBCONSTRAINT  						11319 // 配置库错误:配置库错误:由于限制冲突，失败
#define EC_ICV_CC_DBMISMATCH							11320 // 数据类型不匹配
#define EC_ICV_CC_DBMISUSE  							11321 // 配置库错误:不正当的库使用
#define EC_ICV_CC_DBNOLFS   							11322 // 配置库错误:使用的OS方法，在此数据库上不支持
#define EC_ICV_CC_DBAUTH								11323 // 配置库错误:认证拒绝
#define EC_ICV_CC_DBFORMAT  							11324 // 配置库错误:数据库格式错误
#define EC_ICV_CC_DBRANGE   							11325 // 配置库错误:sqlite_bind的第二个参数不在范围内
#define EC_ICV_CC_DBNOTADB  							11326 // 配置库错误:打开的不是数据库文件
#define EC_ICV_CC_DBROW 								11328 // 配置库错误:sqlite_step() 执行还有另外路径
#define EC_ICV_CC_DBDONE								11329 // 配置库错误:sqlite3_step() 结束执行
#define EC_ICV_CC_DBCPPSQLITE_ERROR 					11330 // 配置库错误:CPPsqliteError
#define EC_ICV_CC_DBUNKNOWN_ERROR   					11331 // 配置库错误:未知错误
#define EC_ICV_CC_DBNONAME  							11332 // 没有名字传递进去或者是空名字
#define EC_ICV_CC_DBNAMEISNULL  						11333 // 名字指针为空
#define EC_ICV_CC_DBNAMETOOLONG 						11334 // 增加的名字长度太长
#define EC_ICV_CC_DBNAMETHESAME 						11335 // 替代的变量的名字相等
#define EC_ICV_CC_DBERRORNUM							11336 // 错误的数字
#define EC_ICV_CC_AUTOCREATENAMEERROR   				11337 // 自动生成名字错误
#define EC_ICV_CC_DBNOPATH  							11338 // 没有路径传递进去
#define EC_ICV_CC_DBNOOPERATOR  						11339 // 没有操作者传递进去
#define EC_ICV_CC_DBERROROCXNAME						11340 // 传入的列别非法
#define EC_ICV_CC_DBERROROCXISNULL  					11341 // 传入的类别为空
#define EC_ICV_CC_DBNOALMAREA   						11342 // 错误的报警区名字或此报警区不存在
#define EC_ICV_CC_DBBUFTOOSHORT 						11343 // 缓冲区太短
#define EC_ICV_CC_SCADAISNULL   						11344 // 传入的SCADA名字为空
#define EC_ICV_CC_OPERATORISNULL						11345 // 传入的操作者为空
#define EC_ICV_CC_DBNOSCADA 							11346 // 数据库中找不到对应的SCADA记录
#define EC_ICV_CC_DBHISTAGISNULL						11347 // 传入的历史趋势变量名字为空
#define EC_ICV_CC_DBHISGRPNAMEISNULL					11348 // 传入的历史趋势组名为空
#define EC_ICV_CC_DBTAGTYPEERROR						11349 // 变量类型错误
#define EC_ICV_CC_DBNOHISTRDGRP 						11350 // 数据库中找不到对应的历史组记录
#define EC_ICV_CC_DBTAGISNULL   						11351 // 传入的变量名称为空
#define EC_ICV_CC_DBNOTAG   							11352 // 数据库中找不到对应的变量记录
#define EC_ICV_CC_DBSYSISNULL   						11353 // 传入的子系统名称为空
#define EC_ICV_CC_DBOBJECTISNULL						11354 // 数据库签出时发生错误
#define EC_ICV_CC_DBCHKOUTERROR 						11355 // 请求的标志位非法
#define EC_ICV_CC_DBNOSUBSYS							11356 // 配置库中无对应的子系统记录
#define EC_ICV_CC_DBNOOBJECT							11357 // 配置库中无对应的对象记录
#define EC_ICV_CC_CLASSISNULL   						11358 // 传入的类名为空
#define EC_ICV_CC_DBNOCLASS 							11359 // 配置库中无类的相关记录
#define EC_ICV_CC_DBNOEXTENDCFG 						11360 // 配置库中无扩展配置的相关记录或插入不成功
#define EC_ICV_CC_EXTENDCFGISNULL   					11361 // 传入的扩展名空
#define EC_ICV_CC_EXCFGANDOCXISNULL 					11362 // 传入的扩展名和类别名同时为空
#define EC_ICV_CC_SITEISNULL							11363 // 传入的节点名为空
#define EC_ICV_CC_ALMAREAISNULL 						11364 // 传入的报警区名字为空
#define EC_ICV_CC_DEVICEISNULL  						11365 // 传入的设备名字为空
#define EC_ICV_CC_DRIVERISNULL  						11366 // 传入的驱动名字为空
#define EC_ICV_CC_DBNOSCADADRIVER   					11367 // 传入的SCADA驱动名字为空
#define EC_ICV_CC_DBNODEVICE							11368 // 数据库中找不到对应的设备记录
#define EC_ICV_CC_DBNODRIVER							11369 // 数据库中找不到对应的驱动记录
#define EC_ICV_CC_DBNOELFFILE   						11370 // 数据库中找不到对应的图库精灵记录
#define EC_ICV_CC_ELFFILEISNULL 						11371 // 图库精灵名称为空
#define EC_ICV_CC_HISTRDGRPISNULL   					11372 // 趋势数据库名称为空
#define EC_ICV_CC_HMIISNULL 							11373 // HMI名称为空
#define EC_ICV_CC_DBNOHMI   							11374 // 数据库中找不到相应的HMI记录
#define EC_ICV_CC_ZTFILEISNULL  						11375 // 组态画面文件名为空
#define EC_ICV_CC_DBNOZTFILE							11376 // 数据库中找不到相应的组态画面文件记录
#define EC_ICV_CC_ERRORIOADDRFORMAT 					11377 // 错误的IO地址格式
#define EC_ICV_CC_COPYHMIISFAILED   					11378 // 拷贝HMI时发生错误
#define EC_ICV_CC_DBCHECKOUT_ERROR  					11379 // 记录已经有人签出
#define EC_ICV_CC_DBOPERATOR_ERROR  					11380 // 不合适的操作者或记录未被签出
#define EC_ICV_CC_DBPROJMODEQUERY_ERROR 				11381 // 工程模式错误
#define EC_ICV_CC_CLSATTRISNULL 						11382 // 类的属性名称为空
#define EC_ICV_CC_DBERRORATTRTYPE   					11383 // 错误的属性类型
#define EC_ICV_CC_DBERRORTAGTYPE						11384 // 错误的变量类型或变量类型不匹配
#define EC_ICV_CC_COPYSCADAISFAILED 					11385 // 拷贝SCADA时发生错误
#define EC_ICV_CC_NAMELENISTOOSHORT 					11386 // 传入的名字长度太短
#define EC_ICV_CC_ERRORDELACTION						11387 // 不能删除已经签出的记录
#define EC_ICV_CC_ERRORREPACTION						11388 // 不能替换已经签出的记录
#define EC_ICV_CC_ERRORDELCLASS1						11389 // 不能删除已经有子类的类
#define EC_ICV_CC_ERRORDELCLASS2						11390 // 不能删除已经实例化的类
#define EC_ICV_CC_DRIVERINSCADABEENCHECKOUT 			11391 // 该配置在节点中有签出，因此不能修改或删除
#define EC_ICV_CC_DBTAGADDRTOOLONG  					11392 // 地址太长，因此不能修改驱动
#define EC_ICV_CC_CANNOTMODIFYHAVEINSTANCE  			11393 // 不能签出已经实例化的类
#define EC_ICV_CC_CANNOTCHKINNODE   					11394 // 子节点有签出，不能修改节点名称，因此不能签入
#define EC_ICV_CC_TAGNAMEINVALIDATRPLS  				11395 // 替换后变量名称不合法
#define EC_ICV_CC_CLASSATTRIBNAMEEXIST  				11396 // 类自定义属性名重复
#define EC_ICV_CC_CANNOTADDINSTANCEATCHKOUT 			11397 // 类被签出不能实例化
#define EC_ICV_CC_INTERNALHASCHKOUT 					11398 // 子节点有签出
#define EC_ICV_CC_CANNOTDELNODE 						11399 // 子节点有签出，不能删除该节点
#define EC_ICV_CC_CANNOTCHKINSCADAASCENTER  			11400 // 已经有SCADA节点配置了中央权限服务节点，因此不能签入该节点
#define EC_ICV_CC_NULL_XML_NODE 						11401 // 无效的XML节点
#define EC_ICV_CC_QUERY_NO_PARENT   					11402 // 查询父节点信息失败
#define EC_ICV_CC_INHERIT_SELFATTR_MODIFY   			11403 // 继承的自定义属性不能修改基本信息
#define EC_ICV_CC_INHERIT_SELFATTR_DEL  				11404 // 继承的自定义属性不能删除
#define EC_ICV_CC_NOT_FIND_SELF_ATTR					11405 // 找不到对应的自定义属性
#define EC_ICV_CC_IHD_SVR_EXISTED						11406 // iHD服务器名称已存在
#define EC_ICV_CC_IHD_SVR_MODEL_NULL					11407 // iHD服务器模型为空
#define EC_ICV_CC_IHD_SVR_NOT_EXISTED					11408 // iHD服务器不存在
#define EC_ICV_CC_IHD_SVR_INVALID_NAME					11409 // iHD服务器名称不合法
#define EC_ICV_CC_IHD_SVR_INVALID_MAINIP				11410 // iHD服务器主机IP不合法
#define EC_ICV_CC_IMV_SVR_EXISTED						11406 // iMV服务器名称已存在
#define EC_ICV_CC_IMV_SVR_MODEL_NULL					11407 // iMV服务器模型为空
#define EC_ICV_CC_IMV_SVR_NOT_EXISTED					11408 // iMV服务器不存在
#define EC_ICV_CC_IMV_SVR_INVALID_NAME					11409 // iMV服务器名称不合法
#define EC_ICV_CC_IMV_SVR_INVALID_IP					11410 // iMV服务器主机IP不合法
#define EC_ICV_CC_INVALIDCHECKINCOUNT					11411 // 签入个数不合法
#define EC_ICV_CC_DBCHECKOUT_BY_ANOTHER_OPERATOR		11412 // 已被其他用户签出
#define EC_ICV_CC_IP_VOILATION_ON_IDENTICAL_USER		11413 // 同一用户在不同的IP地址客户端上进行签入或取消签出操作
#define EC_ICV_CC_UNVALID_ZTSCRIPTNAME					11414 // 不合法的组态脚本名
#define EC_ICV_CC_UNVALID_WEBPKGNAME					11415 // 不合法的WEB用户升级包名
#define EC_ICV_CC_FREE_DISK_SPACE_NOT_ENOUGH			11416 // 剩余磁盘空间不足
#define EC_ICV_CC_QUERY_SCADA_INTRAIP_FAILED			11417 // 查询scada内网IP失败
#define EC_ICV_CC_ABSPATH_EXCEEDS_OSLIMIT				11418 // 文件绝对路径超过了操作系统的限制
#define EC_ICV_CC_OBJECT_SUBSYS_NAME_CONFLICT			11419 // 对象名与子系统名冲突
#define EC_ICV_CCTV_FAILTOREGLOCALQUE   				12001 // 注册CVNDK的CVNDK_RegLocalQueue返回NULL
#define EC_ICV_CCTV_FAILTOREGREMOTEQUE  				12002 // 注册CVNDK的CVNDK_RegRemoteQueue返回NULL
#define EC_ICV_CCTV_VIDEOSERVICEISRUNNING   			12003 // 已经打开了一个服务
#define EC_ICV_CCTV_FILEPATHNAMETOOLONG 				12004 // 文件路径名太长
#define EC_ICV_CCTV_FAILTOLOADXMLCFGFILE				12005 // 加载XML文件失败
#define EC_ICV_CCTV_FAILTOPARSEXMLCFGFILE   			12006 // 解析XML配置文件失败
#define EC_ICV_CCTV_LOCALSVRIPNOTMATCH  				12007 // 在所有服务器信息中,根据本地IP未发现有匹配的服务器
#define EC_ICV_CCTV_FAILTOGETDRVDLLFUNC 				12008 // 调用导出函数失败
#ifndef EC_ICV_CCTV_PARSERBUFFERTOOSHORT
#define EC_ICV_CCTV_PARSERBUFFERTOOSHORT				12009 // 解析时发现传入的缓冲区太少
#endif
#ifndef EC_ICV_CCTV_FUNCPARAMINVALID
#define EC_ICV_CCTV_FUNCPARAMINVALID					12010 // 函数参数非法
#endif
#define EC_ICV_CCTV_FAILTONEWMEM						12011 // 内存分配时不成功
#define EC_ICV_CCTV_UNKWNCMDCODE						12012 // 未知的控制命令号
#define EC_ICV_CCTV_NOTIMPLEMENTEDCMDCODE   			12013 // 本版本尚未实现的控制命令
#define EC_ICV_CCTV_CANNOTFINDCAMBYID   				12014 // 根据摄像头号不能找到对应的摄像头信息
#define EC_ICV_CCTV_NOCTRLDEVWITHCAM					12015 // 摄像头没有对应的控制设备
#define EC_ICV_CCTV_CAMRELATEDDEVDRVNOTSTART			12016 // 摄像头对应的设备驱动没有启动
#define EC_ICV_CCTV_CANNOTFINDPRODUCT   				12017 // 没有找到摄像头对应的设备驱动产品型号
#define EC_ICV_CCTV_CAMERANOTFOUND  					12018 // 没有找到摄像头
#define EC_ICV_CCTV_FAILTOGENZONEBUFFER 				12019 // 生成分区信息失败
#define EC_ICV_CCTV_NOAUTH  							12020 // 没有授权
#define EC_ICV_CCTV_DELETEDMODCOUNTINVALID  			12021 // 删除模式时个数非法
#define EC_ICV_CCTV_FAILTOPARSEMODENAME 				12022 // 修改模式时, 解析旧模式名称以及新模式信息异常
#define EC_ICV_CCTV_DELETEDPSPCOUNTINVALID  			12023 // 删除预置位时个数非法
#define EC_ICV_CCTV_FAILTOGETDRVFUNC					12024 // 导出函数指针为空
#define EC_ICV_CCTV_MONITORNOTFOUND 					12025 // 没有找到监视器
#define EC_ICV_CCTV_LINKDEVICENOTFOUND  				12026 // 没有找到对应的连接设备
#define EC_ICV_CCTV_VIDEOSERVERNOTFOUND 				12027 // 没有找到对应的视频服务
#define EC_ICV_CCTV_RETMSGTOOSHORT  					12028 // 返回的消息太短
#define EC_ICV_CCTV_INFONOTCHANGED  					12029 // 根据时间戳比较, 信息没有改变过(模式或预置位)
#define EC_ICV_CCTV_RETBUFTOOSHORT  					12030 // 客户端接收到的服务端缓冲区太短
#define EC_ICV_CCTV_RCVAWRONGRESPONSE   				12031 // 客户端接收到的命令返回和要等待的返回不同
#define EC_ICV_CCTV_NOSDKWITHCAM						12032 // 摄像头没有对应的SDK
#define EC_ICV_CCTV_SHOULDNOTENTERPATH  				12033 // 不应该进入的路径
#define EC_ICV_CCTV_ONLYDELETEONEACHTIME				12034 // 每次只能删除一条
#define EC_ICV_CCTV_PSPALREADYEXIST 					12035 // 预置位已经存在(增加时)
#define EC_ICV_CCTV_PSPNOTEXIST 						12036 // 预置位不存在(删除时)
#define EC_ICV_CCTV_DEVALREADYEXIST 					12037 // 设备已经存在(增加时)
#define EC_ICV_CCTV_DEVNOTEXIST 						12038 // 设备不存在(删除时)
#define EC_ICV_CCTV_DEVTYPEALREADYEXIST 				12039 // 设备类型已经存在(增加时)
#define EC_ICV_CCTV_DEVTYPENOTEXIST 					12040 // 设备类型不存在(删除时)
#define EC_ICV_CCTV_SERVERALREADYEXIST  				12041 // 服务已经存在(增加时)
#define EC_ICV_CCTV_SERVERNOTEXIST  					12042 // 服务不存在(删除时)
#define EC_ICV_CCTV_PRODUCTALREADYEXIST 				12043 // 产品型号已经存在(增加时)
#define EC_ICV_CCTV_PRODUCTNOTEXIST 					12044 // 产品型号不存在(删除时)
#define EC_ICV_CCTV_ZONEALREADYEXIST					12045 // 分区已经存在(增加时)
#define EC_ICV_CCTV_ZONENOTEXIST						12046 // 分区不存在(删除时)
#define EC_ICV_CCTV_RECVBUFFTOOSHORT					12047 // 接收到的命令缓冲区太短
#define EC_ICV_CCTV_CANNOTFINDVIDEOSCREEN   			12048 // 未找到视频VideoScreen
#define EC_ICV_CCTV_FAILTOSENDBOTHSERVER				12049 // 发送给主备服务器都失败了
#define EC_ICV_CCTV_FAILTOEXEDB 						12050 // 数据库操作失败
#define EC_ICV_CCTV_FAILTOCREATEDB  					12051 // 创建数据库失败
#define EC_ICV_CCTV_VIDEOWINDOWNOTEXIST 				12052 // 不存在当前窗口
#define EC_ICV_CCTV_VIDEOSERVERNOTEXIST 				12053 // 不存在指定的视频窗口
#define EC_ICV_CCTV_CAMERANOTEXIST  					12054 // 不存在指定ID或名称的摄像头
#define EC_ICV_CCTV_PLAYIDNOTEXIST  					12055 // 不存在指定的播放
#define EC_ICV_CCTV_FAILTOPARSEMODEBUFF 				12056 // 解析模式发生错误
#define EC_ICV_CCTV_MONITORNOTEXIST 					12057 // 不存在指定ID或名称的监视器
#define EC_ICV_CCTV_ADDMODEBUFFTOOLONG  				12058 // 增加模式时发现缓冲区太长
#define EC_ICV_CCTV_MODENAMENOTEXIST					12059 // 模式名称不存在
#define EC_ICV_CCTV_MODENAMISNULL   					12060 // 模式名称为空
#define EC_ICV_CCTV_VIDEODEVICENOTEXIST 				12061 // 摄像设备对象未找到
#define EC_ICV_CCTV_SDKNOTLOADED						12062 // 客户端插件SDK未加载成功
#define EC_ICV_CCTV_FAILTOPLAYCAMERA					12063 // 播放画面失败
#define EC_ICV_CCTV_CLIENTNOTSTARTED					12064 // 客户端还没有开始运行
#define EC_ICV_CCTV_IPINVALID   						12065 // IP重复
#define EC_ICV_CCTV_FAILTODISCCAMERA					12066 // 断开摄像头失败
#define EC_ICV_CCTV_FAILTOLOGOUT						12067 // 注销DVR失败
#define EC_ICV_CCTV_ROWCOL_EXCEEDWNDBOUND   			12068 // 该窗口行列号超过当前的布局最大行列号
#define EC_ICV_CCTV_ROWCOL_CMBWNDNOTEXIST   			12069 // 该窗口标号为复合窗口范围, 但尚未创建
#define EC_ICV_CCTV_CODEFLOW_NOTINRANGE 				12070 // 码流参数超过限制
#define EC_ICV_CCTV_NOTCOMBINEWND   					12071 // 不是合并窗口
#define EC_ICV_CCTV_FAILTOSNAPPIC   					12072 // 从文件读取图片内容出错
#define EC_ICV_CCTV_FUNCINVALID 						12073 // 无效或错误的功能
#define EC_ICV_CCTV_CANNOTGETCTRLSVRBYCAM   			12074 // 根据摄像头信息（ID或名称）不能找到对应的控制服务器
#define EC_ICV_CCTV_FAILTOSWITCHCMDTOBOTHSVR			12075 // 本服务转发给主备服务都失败了
#define EC_ICV_CCTV_OPERATIONCANCED 					12076 // 客户端在进行确认时取消了操作
#define EC_ICV_CCTV_LAYOUTROWCOLINVALID 				12077 // 传入的布局行列值非法
#define EC_ICV_CCTV_PRIORLOW							12079 // 用户权限低于锁定用户
#define EC_ICV_CCTV_PRIOREQUAL  						12080 // 用户权限和锁定用户相同
#define EC_ICV_CCTV_FREEEMPTYOBJ						12081 // 解除锁定失败，对象没有被锁定
#define EC_ICV_CCTV_NOTIMPLEMENTED  					12082 // 尚未实现的功能
#define EC_ICV_CCTV_LOADDLLFAILED   					12083 // 加载驱动失败
#define EC_ICV_CCTV_BMPTOJPGFAILED  					12084 // BmpTOJPG失败
#define EC_ICV_CCTV_DOWNLOADERR 						12085 // 从硬盘录像机下载文件失败
#define EC_ICV_CCTV_SNAPTYPENOTEXIST					12086 // 抓拍类型不存在
#define EC_ICV_CCTV_STOPSAVEVIDEOFAILED 				12087 // 停止保存录像失败
#define EC_ICV_CCTV_DIR_PATH_NOT_EXIST  				12088 // 文件夹路径不存在
#define EC_ICV_CCTV_SNAPPICNOTEXIST 					12089 // 抓拍图片不存在
#define EC_ICV_CCTV_PLAYINTURNNOTPERMIT 				12090 // 依次显示视频窗口不允许，原因是存在合并窗口等
#define EC_ICV_CCTV_RECORDISNOTEXIST					12091 // 录像信息不存在
#define EC_ICV_CCTV_TOOSMALLREMAINHARDSPACE 			12092 // 磁盘分区剩余空间太小
#define EC_ICV_CCTV_ISMAXPICCOUNT   					12093 // 抓拍图片已经是最大个数
#define EC_ICV_CCTV_MONITORNOLINKDEV					12094 // 监视器没有连接设备
#define EC_ICV_CCTV_ADDIN_ERROR 						12100 // 插件错误，请查看日志
#define EC_ICV_CCTV_DRIVER_ERROR						12101 // 驱动错误，请查看日志
#define EC_ICV_CCTV_SQLITE_ERROR						12102 // SQLite数据库错误，请查看日志
#define EC_ICV_CCTV_EXISTCYCLEMON   					12103 // 已存在的监视器轮询
#define EC_ICV_CCTV_NOTEXISTCYCLEMON					12104 // 不存在的监视器轮询
#define EC_ICV_CCTV_VERSIONERR  						12105 // 客户端和服务端版本不一致
#define EC_ICV_CCTV_GET_DATA_FAILED 					12106 // 从内存中(list、map)获取数据失败
#define EC_ICV_CCTV_CLIENT_UNREGISTERED 				12107 // 客户端未注册
#define EC_ICV_CCTV_HISDEV_NOEXIST  					12108 // 找不到摄像头关联的历史录像设备
#define EC_ICV_CCTV_MATRIX_NOEXIST  					12109 // 找不到摄像头关联的矩阵设备
#define EC_ICV_CCTV_DEV_NOEXIST 						12110 // 找不到矩阵关联的编码器或监视器等设备
#define EC_ICV_CCTV_DEVUSER_NOEXIST 					12111 // 找不到占用该编码器设备的用户
#define EC_ICV_CCTV_MATTOMON_NOEXIST					12112 // 找不到矩阵关联的监视器
#define EC_ICV_CCTV_FAILTOINITDVR   					12201 // DVR初始化失败
#define EC_ICV_CCTV_FAILTOEXITDVR   					12202 // DVR退出失败
#define EC_ICV_CCTV_FAILTOREBOOTDVR 					12203 // DVR重起失败
#define EC_ICV_CCTV_FAILTOSHUTDOWNDVR   				12204 // DVR关闭失败
#define EC_ICV_CCTV_FAILTOPLAYVIDEO 					12205 // DVR实时播放失败
#define EC_ICV_CCTV_FAILTOSTOPREALPLAY  				12206 // DVR断开实时播放失败
#define EC_ICV_CCTV_FAILTOSNAPPICTRUE   				12207 // DVR抓拍图片失败
#define EC_ICV_CCTV_FAILTOGETVIDEOEFFECT				12208 // DVR获取画面质量失败
#define EC_ICV_CCTV_FAILTOSETVIDEOEFFECT				12209 // DVR调整画面质量失败
#define EC_ICV_CCTV_FAILTOSETDEVTIME					12210 // DVR校时失败
#define EC_ICV_CCTV_FAILTOLOGINDVR  					12211 // 登录DVR失败
#define EC_ICV_CCTV_FAILTOLOGOUTDVR 					12212 // 注销DVR失败
#define EC_ICV_CCTV_FAILTOFINDVIDEOFILE 				12213 // 查找指定通道指定时间段的所有远程文件信息失败
#define EC_ICV_CCTV_FAILTOPLAYBACK  					12214 // 回放录像失败
#define EC_ICV_CCTV_FAILTOSTOPPLAYBACK  				12215 // 停止回放录像失败
#define EC_ICV_CCTV_FAILTOPAUSEPLAYBACK 				12216 // 暂停回放录像失败
#define EC_ICV_CCTV_FAILTOCONTINUEPLAYBACK  			12217 // 继续回放录像失败
#define EC_ICV_CCTV_FAILTOGETPLAYBACKINFO   			12218 // 获取回放录像信息
#define EC_ICV_CCTV_FAILTOSETPLAYBACKPOS				12219 // 设置回放录像位置
#define EC_ICV_CCTV_FAILTOSNAPPLAYBACK  				12220 // 回放录像时抓拍失败
#define EC_ICV_CCTV_FAILTOSAVEPLAYBACK  				12221 // 回放录像时保存失败
#define EC_ICV_CCTV_FAILTOSTOPSAVEPLAYBACK  			12222 // 回放录像时停止保存失败
#define EC_ICV_CCTV_FAILTODOWNLOADVIDEO 				12223 // 下载录像失败
#define EC_ICV_CCTV_FAILTOSTOPDOWNLOADVIDEO 			12224 // 停止下载录像失败
#define EC_ICV_CCTV_FAILTOGETDOWNLOADPOS				12225 // 查询下载录像位置失败
#define EC_ICV_CCTV_FAILTOLENSCONTROL   				12226 // 镜头控制失败
#define EC_ICV_CCTV_FAILTOPRESETCONTROL 				12227 // 设置/调用预置位失败
#define EC_ICV_CCTV_FAILTOAUXCONTROL					12228 // 附加设备控制失败
#define EC_ICV_CCTV_FAILTOPANCONTROL					12229 // 云台控制失败
#define EC_ICV_CCTV_FAILTOGENERALCONTROL				12230 // 调用通用接口失败
#define EC_ICV_CCTV_FAILTOPLAYFAST  					12231 // 加快回放录像失败
#define EC_ICV_CCTV_FAILTOPLAYSLOW  					12232 // 减慢回放录像失败
#define EC_ICV_DW_START_NO  							12301 // 大屏系统错误码开始编号
#define EC_ICV_DW_INVALID_PARAMETER 					12302 // 无效的参数
#define EC_ICV_DW_DEVICE_REGISTERED 					12303 // 设备已被注册
#define EC_ICV_DW_DEVICE_NOT_REGISTERED 				12304 // 设备未被注册
#define EC_ICV_DW_DEVICE_NOTEXIST   					12305 // 指定的设备不存在
#define EC_ICV_DW_CYCLENAME_ISEXIST 					12306 // 轮询名称已经存在
#define EC_ICV_DW_LAYOUTNAME_ISEXIST					12307 // 布局名称已经存在
#define EC_ICV_DW_CLOSE_WINDOW_FAILED   				12308 // 关闭窗口失败
#define EC_ICV_DW_OPEN_WINDOW_FAILED					12309 // 打开窗口失败
#define EC_ICV_DW_SHUTDOWN_FAILED   					12310 // 关闭大屏失败
#define EC_ICV_DW_STARTUP_FAILED						12311 // 打开大屏失败
#define EC_ICV_DW_NO_RESULT_ATTRIBUTE   				12312 // 返回结果中缺少结果属性
#define EC_ICV_DW_WINDOW_NOT_EXIST  					12313 // 未找到指定窗口
#define EC_ICV_DW_MOVE_WINDOW_FAILED					12314 // 移动窗口失败
#define EC_ICV_DW_LAYOUT_NOT_EXIST  					12315 // 指定的布局不存在
#define EC_ICV_DW_CYCLE_NOT_EXIST   					12316 // 指定的轮询不存在
#define EC_ICV_DW_CYCLE_IN_USE  						12317 // 指定的轮询正在被使用
#define EC_ICV_DW_QUERY_INFORMATION_EMPTY   			12318 // 查询的信息不存在
#define EC_ICV_DW_INVALID_XML_ATTRIBUTE 				12319 // 返回的xml信息中属性值错误
#define EC_ICV_DW_WINDOW_NOT_INCYCLE					12320 // 指定的窗口未匹配轮询
#define EC_ICV_DW_REFRESH_REMOVE_FAILED 				12321 // 刷新布局内容时删除出错
#define EC_ICV_DW_REFRESH_ADD_FAILED					12322 // 刷新布局内容时添加出错
#define EC_ICV_DW_REFRESH_BOTH_FAILED   				12323 // 刷新布局内容时添加和删除都出错
#define EC_ICV_DW_SIGNAME_NOT_FOUND 					12324 // 找不到指定的信号源名称
#define EC_ICV_DW_CFGPATH_NOTEXIST  					12331 // 获取配置文件路径失败
#define EC_ICV_DW_CREATE_THREAD_FAILED  				12332 // 创建线程失败
#define EC_ICV_DW_LACK_MEMORY   						12333 // 内存不足
#define EC_ICV_DW_SERVER_ALREADY_STARTED				12334 // 大屏服务已经启动
#define EC_ICV_DW_TIMER_SCHEDULE_FAILED 				12335 // 注册定时器失败
#define EC_ICV_DW_SERVICE_NOT_CONNECTED 				12336 // 大屏服务无法连接
#define EC_ICV_DW_DEVICE_REGISTER_FAILED				12337 // 设备注册失败
#define EC_ICV_DW_DEVICE_UNREGISTER_FAILED  			12338 // 设备注销失败
#define EC_ICV_DW_UNKNOWN_REQUEST   					12339 // 未知的请求类型
#define EC_ICV_DW_LOAD_XML_FAILED   					12340 // 打开XML文件失败
#define EC_ICV_DW_LICENCE_ERROR 						12341 // 许可证验证失败
#define EC_ICV_DW_NO_OPERATE_AUTH   					12342 // 没有操作权限
#define EC_ICV_DW_DEVICE_IS_LOCKED  					12343 // 已经被lock，无权限进行操作
#define EC_ICV_DW_AUTH_INFO_ERROR   					12344 // 权限信息不正确
#define EC_ICV_DW_OFFLINE_STATUS						12345 // 当前为离线编辑状态，不能操作设备
#define EC_ICV_DW_DEVICE_NOT_LOGIN  					12346 // 没有登录大屏设备
#define EC_ICV_DW_NO_ENABLE_PORT						12347 // 没有可用的大屏端口
#define EC_ICV_DW_WIN_OVER_BORDER   					12348 // 窗口不在大屏内
#define EC_ICV_DW_USERNAME_NOT_SPEC 					12349 // 没有指定用户名
#define EC_ICV_DW_INITQUEUEFAILED   					12351 // 注册本地Q失败
#define EC_ICV_DW_LISTENFAILED  						12352 // 监听端口失败
#define EC_ICV_DW_RECV_TIMEOUT  						12353 // 接收数据超时
#define EC_ICV_DW_SEND_FAILED   						12354 // 网络发送错误
#define EC_ICV_QUEUE_NOT_EXIST  						12355 // 通信队列不存在
#define EC_ICV_DW_IPADDR_IS_NULL						12356 // IP地址长度为零
#define EC_ICV_DW_RELEASEQUEUE_FAILED   				12357 // 释放QUEUE出错
#define EC_ICV_DW_DB_OPERATION_FAILED   				12361 // 数据库操作失败
#define EC_ICV_DW_CFGDB_OPEN_FAILED 					12362 // 打开配置数据库失败
#define EC_ICV_DW_INFODB_OPEN_FAILED					12363 // 打开信息数据库失败
#define EC_ICV_DW_INFODB_CREATEFAILED   				12364 // 创建信息数据库失败
#define EC_ICV_DW_CREATE_TABLE_FAILED   				12365 // 创建数据库表失败
#define EC_ICV_DW_DBLOCK_ACQUIRE_FAILED 				12366 // 获取互斥体失败
#define EC_ICV_DW_LOAD_DRIVER_FAILED					12371 // 加载驱动DLL失败
#define EC_ICV_DW_DRIVER_FUNC_FAILED					12372 // 加载驱动导出函数失败
#define EC_ICV_DW_DRIVER_NOT_INIT   					12373 // 大屏设备驱动未初始化
#define EC_ICV_DW_DRIVER_CLOSEDWISFAILED				12374 // 关闭大屏设备驱动失败
#define EC_ICV_DW_DRIVER_OPENDWISFAILED 				12375 // 打开大屏设备驱动失败
#define EC_ICV_DW_DRIVER_LOGOUTISFAILED 				12376 // 登出大屏设备驱动失败
#define EC_ICV_DW_DRIVER_LOGINISFAILED  				12377 // 登录大屏设备驱动失败
#define EC_ICV_DW_DRIVER_NOUSER 						12378 // 没有找到用户名，配置文件里没有配置用户名
#define EC_ICV_DW_DRIVER_CLOSEWNDFAILED 				12379 // 关闭窗口失败
#define EC_ICV_DW_DRIVER_OPENWNDFAILED  				12380 // 打开窗口失败
#define EC_ICV_DW_DRIVER_GETWNDFAILED   				12381 // 获取窗口信息失败
#define EC_ICV_DW_DRIVER_MOVEWNDFAILED  				12382 // 移动大屏窗口失败
#define EC_ICV_DW_RGB_MATRIX_SWITCH_FAILED  			12383 // 切换RGB矩阵失败
#define EC_ICV_DW_DRIVER_MATHCSIGERROR  				12384 // 匹配信号源失败
#define EC_ICV_DW_DRIVER_APPLYMODEFAILED				12385 // 应用大屏模式失败
#define EC_ICV_DW_DRIVER_NOWALLATTR 					12386 // 无法取得大屏幕的属性信息
#define EC_ICV_DW_DRIVER_NOTFINDINPUTID 				12387 // 无法取得大屏幕的端口信息
#define EC_ICV_DW_SWITCH_VIDEO_FAILED   				12388 // 切换视频信号失败
#define EC_ICV_DW_GET_VIDEO_FAILED  					12389 // 取得视频信号失败(大屏设备发送失败或者接受失败)
#define EC_ICV_DW_GET_SIMPCAMO_FAILED   				12390 // 取得简易的视频信号失败
#define EC_ICV_DW_FAILED								12391 // 返回未知错误
#define EC_ICV_DW_HALF_SUCCESS  						12392 // 部分成功
#define EC_ICV_DW_NET_FAILED							12393 // 网络错误
#define EC_ICV_DW_NOSETTINGNOOPERATION  				12394 // 无属性设置，无操作
#define EC_ICV_DW_CTRLNOREGISTER						12395 // 控件未注册
#define LED_INVALID_PARAM   							12402 // 无效参数
#define LED_QUERY_NO_DATA   							12403 // 数据库查询不到数据
#define LED_DB_UNINITED 								12404 // 数据库未初始化
#define LED_GET_BLOB_FILED_ERROR						12405 // 获取blob字段信息失败
#define LED_CREATE_TABLE_ERROR  						12406 // 创建表失败
#define LED_SCHEME_NAME_DUPLICATE   					12407 // 预案名重复
#define LED_SCHEME_IN_USE   							12408 // 预案正在被使用
#define LED_US_WORDS_IN_USE 							12409 // 常用语正在被使用
#define LED_THE_SAME_TIMESTAMP  						12410 // 时间戳相等,配置无需更新
#define LED_BUF_NOT_ENOUGH  							12411 // 分配的缓冲区长度不够
#define LED_GET_DATA_FAILED 							12412 // 从内存中(list、map)获取数据失败
#define LED_INVALID_DISPLAY_TYPE						12413 // 无效的显示信息类型
#define LED_INVALID_NUMBER  							12414 // 无效数目
#define LED_INVALID_STR_SIZE							12415 // 无效字符串长度
#define LED_INVALID_ITEM_TYPE   						12416 // 无效的预案项类型
#define LED_INVALID_PIC_SIZE							12417 // 无效的图片大小
#define LED_NEW_MEM_ERROR   							12418 // 分配内存失败
#define LED_DEV_CTRL_TASK_NULL  						12419 // 设备未关联到设备控制线程
#define LED_MSG_PARSE_LEN_ERROR 						12420 // 消息解析时，长度错误
#define LED_MEM_MAP_FILE_FAILED 						12421 // 操作内存映射文件失败
#define LED_FILE_NAME_TOO_LONG  						12422 // 文件名过长
#define LED_LOAD_XML_FILE_ERROR 						12423 // 加载xml文件错误
#define LED_PARSE_XML_FILE_ERROR						12424 // 解析xml文件错误
#define LED_LOCAL_SERV_UNCONFIGED   					12425 // 本地服务未定义
#define LED_SERVICE_ALREADY_RUNNING 					12426 // 服务端已经运行
#define LED_LOAD_DLL_FAILED 							12427 // 加载dll失败
#define LED_APPLY_SCHEME_FAILED 						12428 // 应用预案失败
#define LED_APPLY_TEXT_FAILED   						12429 // 应用文本失败
#define LED_APPLY_PIC_FAILED							12430 // 应用图片失败
#define LED_CLIENT_UNREGISTERED 						12431 // 客户端未注册
#define LED_CVNDK_REG_QUEUE_FAILED  					12432 // 注册queue失败
#define LED_CLIENT_REGISTERED   						12433 // 客户端已经注册
#define LED_GET_RTD_PATH_ERROR  						12434 // 获取rtd路径失败
#define LED_CREATE_FILE_ERROR   						12435 // 创建文件失败
#define LED_PARAM_TOO_LONG  							12436 // 参数太长
#define LED_INVALID_EFFECT  							12437 // 特效配置错误
#define LED_PIC_NAME_DUPLICATE  						12438 // 图片名称重复
#define LED_CONFIG_NAME_DUPLICATE   					12439 // 配置表名称重复
#define LED_APPEND_TEXT_FAILED  						12440 // 追加文本失败
#define EC_ICV_LED_END_NO   							12500 // LED系统错误码结束编号
#define EC_ICV_DWCFG_START_NO   						12501 // 大屏系统配置错误码开始编号
#define EC_ICV_DW_DBFAIL								12502 // 操作失败
#define EC_ICV_DW_DBINTERNAL							12503 // 内部错误
#define EC_ICV_DW_DBPERM								12504 // 没有权限进行此操作
#define EC_ICV_DW_DBABORT   							12505 // 回调函数请求失败
#define EC_ICV_DW_DBBUSY								12506 // 数据库文件加锁
#define EC_ICV_DW_DBLOCKED  							12507 // 数据库中的表加锁
#define EC_ICV_DW_DBNOMEM   							12508 // malloc()失败
#define EC_ICV_DW_DBREADONLY							12509 // 试图写入一个只读的数据库
#define EC_ICV_DW_DBINTERRUPT   						12510 // 由sqlite_interrupt()导致的操作终止
#define EC_ICV_DW_DBIOERR   							12511 // I/O错误
#define EC_ICV_DW_DBCORRUPT 							12512 // 数据库磁盘映像文件不合适
#define EC_ICV_DW_DBNOTFOUND							12513 // 表或者记录没有找到
#define EC_ICV_DW_DBFULL								12514 // 由于数据库已满，插入失败
#define EC_ICV_DW_DBCANTOPEN							12515 // 不能打开数据库文件
#define EC_ICV_DW_DBPROTOCOL							12516 // 数据库锁协议失败
#define EC_ICV_DW_DBEMPTY   							12517 // 数据库表为空
#define EC_ICV_DW_DBSCHEMA  							12518 // 数据库表改变
#define EC_ICV_DW_DBTOOBIG  							12519 // 太多的数据写入一张表中
#define EC_ICV_DW_DBCONSTRAINT  						12520 // 由于限制冲突，失败
#define EC_ICV_DW_DBMISMATCH							12521 // 数据类型不匹配
#define EC_ICV_DW_DBMISUSE  							12522 // 不正当的库使用
#define EC_ICV_DW_DBNOLFS   							12523 // 使用的OS方法，在此数据库上不支持
#define EC_ICV_DW_DBAUTH								12524 // 认证拒绝
#define EC_ICV_DW_DBFORMAT  							12525 // 数据库格式错误
#define EC_ICV_DW_DBRANGE   							12526 // sqlite_bind的第二个参数不在范围内
#define EC_ICV_DW_DBROW 								12527 // sqlite_step()执行还有另外路径
#define EC_ICV_DW_DBDONE								12528 // sqlite3_step()结束执行
#define EC_ICV_DW_DBCPPSQLITE_ERROR 					12529 // 操作CppSqlite失败
#define EC_ICV_DW_DBUNKNOWN_ERROR   					12530 // 没有定义的错误
#define EC_ICV_DW_INIT_FAIL 							12601 // 大屏初始化失败
#define EC_ICV_DW_Exit_FAIL 							12602 // 大屏退出失败
#define EC_ICV_DW_SWITCHVIDEOSIG_FAIL   				12603 // 切换视频信号失败
#define EC_ICV_DW_DECODER_BADIP 						12604 // 无效解码器IP
#define EC_ICV_DW_DECODER_BADPORT   					12605 // 无效解码器的端口
#define EC_ICV_DW_DECODER_CONNECT   					12606 // 连接解码器失败
#define EC_ICV_DW_DECODER_BADCFGSTR 					12607 // 解码器的配置字符串不正确
#define EC_ICV_DW_ENCODER_BADCFGSTR 					12608 // 编码器的配置字符串不正确
#define EC_ICV_DW_ENCODER_CONNECT   					12609 // 编码器连接不正常
#define EC_ICV_DW_DISCONNECT_FROM_ENCODER   			12610 // 断开编码器出现异常
#define EC_ICV_COMM_GETENVFAIL  						13000 // 获取iplat-BA的环境变量失败
#define EC_ICV_COMM_PATHTOOLONG 						13001 // 路径信息超过定义的最大长度
#define EC_ICV_COMM_PEER_IP_NOT_SPEC					13002 // 对等节点IP地址获取失败
#define EC_ICV_COMM_INVALIDPARA 						13003 // 传入参数非法
#define EC_ICV_COMM_FAILTOGETCVCONFIGPATH   			13004 // 获取CVConfig路径失败
#define EC_ICV_COMM_FAILTOOPENCONFIGFILE				13005 // 打开配置文件失败
#define EC_ICV_COMM_FAILTOGETXMLNODE					13006 // 获取XML节点失败
#define EC_ICV_COMM_SCADA_CFG_ERROR 					13007 // 获取/解析SCADALst.xml文件失败
#define EC_ICV_COMM_UNKNOWN_SCADANAME   				13008 // 未知SCADA名称
#define EC_ICV_COMM_FAILTO_ALLOCMEMORY  				13009 // 分配内存失败
#ifndef EC_ICV_CVNDK_INITFAILED
#define EC_ICV_CVNDK_INITFAILED 						13010 // CVNDK初始化失败
#endif
#ifndef EC_ICV_CVNDK_LISTENFAILED
#define EC_ICV_CVNDK_LISTENFAILED   					13011 // CVNDK监听端口失败
#endif
#ifndef EC_ICV_CVNDK_SENDFAILED
#define EC_ICV_CVNDK_SENDFAILED 						13012 // 发送数据失败
#endif
#ifndef EC_ICV_CVNDK_RECVFAILED
#define EC_ICV_CVNDK_RECVFAILED 						13013 // 接收数据失败
#endif
#ifndef EC_ICV_CVNDK_FINALIZEFAILED
#define EC_ICV_CVNDK_FINALIZEFAILED 					13014 // 终止CVNDK服务失败
#endif
#ifndef EC_ICV_CVNDK_QUEUEERROR
#define EC_ICV_CVNDK_QUEUEERROR 						13015 // CVNDK中的Queue相关错误
#endif
#ifndef EC_ICV_CVNDK_CONNECTFAILED
#define EC_ICV_CVNDK_CONNECTFAILED  					13016 // 连接失败
#endif
#ifndef EC_ICV_CVNDK_GET_PORT_FAILED
#define EC_ICV_CVNDK_GET_PORT_FAILED					13017 // 获取指定应用的端口失败
#endif
#ifndef EC_ICV_CVNDK_RECV_TIMEOUT
#define EC_ICV_CVNDK_RECV_TIMEOUT   					13018 // 接收数据超时
#endif
#ifndef EC_ICV_CVNDK_NOT_INIT
#define EC_ICV_CVNDK_NOT_INIT   						13019 // CVNDK尚未初始化
#endif
#ifndef EC_ICV_CVNDK_INVALID_PARAMETER
#define EC_ICV_CVNDK_INVALID_PARAMETER  				13020 // 无效的参数传递
#endif
#ifndef EC_ICV_CVNDK_MALLOC_FAILED
#define EC_ICV_CVNDK_MALLOC_FAILED  					13021 // 分配空间失败
#endif
#ifndef EC_ICV_CVNDK_PUTQ_FAILED
#define EC_ICV_CVNDK_PUTQ_FAILED						13022 // 数据发送到全局写Queue失败
#endif
#ifndef EC_ICV_CVNDK_LOCAL_QUEUE_NOT_FOUND
#define EC_ICV_CVNDK_LOCAL_QUEUE_NOT_FOUND  			13023 // 本地Queue未查找到
#endif
#ifndef EC_ICV_CVNDK_CONNECT_NOT_EXIST
#define EC_ICV_CVNDK_CONNECT_NOT_EXIST  				13024 // 与远端Queue的连接不存在
#endif
#ifndef EC_ICV_CVNDK_ACQUIRE_MUTEX_FAILED
#define EC_ICV_CVNDK_ACQUIRE_MUTEX_FAILED   			13025 // 获取全局锁失败
#endif
#define EC_ICV_SHMQUEUE_INVALID_PARAM   				13039 // 传入参数非法
#define EC_ICV_BDBQUEUE_OPEN_DB_FILE_FAILED 			13040 // 打开文件失败
#define EC_ICV_BDBQUEUE_DB_NOT_INITIALIZED  			13041 // 文件未打开
#define EC_ICV_BDBQUEUE_NOT_FOUND   					13042 // 文件未找到
#define EC_ICV_BDBQUEUE_BUFFER_TOO_LONG 				13043 // 插入过长的Buffer
#define EC_ICV_BDBQUEUE_DB_IO_ERROR 					13044 // 读写DB时异常
#define EC_ICV_SHMQUEUE_ACQUIRE_MUTEX_FAILED			13045 // 获取锁失败
#define EC_ICV_SHMQUEUE_MALLOC_MMAP_FAILED  			13046 // 分配共享内存失败
#define EC_ICV_SHMQUEUE_WAKEDUP 						13047 // 队列强制唤醒
#define EC_ICV_SHMQUEUE_QUEUE_INFO_MISMATCH 			13048 // 队列信息不匹配
#define EC_ICV_SHMQUEUE_CALL_BACK_FAILED				13049 // 调用CallBack方法异常
#define EC_ICV_COMM_SIMPLE_QUEUE_TIMEOUT				13050 // 读取CSimpleThreadQueue队列超时
#define EC_ICV_COMM_FAILTO_GET_RTD_PATH 				13060 // 获取RTD目录失败
#define EC_ICV_COMM_BUFFERTOOSHORT  					13061 // 缓冲区太小
#define EC_ICV_DATA_QUALITY_BAD 						13062 // 数据质量bad
#define EC_ICV_SQLITE_FILE_OPEN_FAILED  				13070 // 打开sqlite文件失败
#define EC_ICV_SQLITE_CREATE_TABLE_FAILED   			13071 // 创建sqlite 表失败
#define EC_ICV_SQLITE_UPDATE_RECORD_FAILED  			13072 // 插入sqlite数据失败
#define EC_ICV_EXPRESSION_INVALID   					13080 // 表达式不合法，不能包含特殊符号，表达式不能包含空或者以数字开头
#define EC_ICV_EXPRESSION_INVALID2  					13081 // 表达式不合法，对象模式下类的属性名不能以A_及F_开头
#define EC_ICV_EXPRESSION_INVALID3  					13082 // 表达式不合法，设备模式下的tag名称不能以F_及A_开头
#define EC_ICV_VALIDATE_LICENSE_FILE_FAILED 			13090 // 许可证文件读取/验证失败
#define EC_ICV_CONFIG_FILE_ERROR						13091 // 配置文件错误
#define EC_ICV_REGISTRY_OPERATE_FAILED  				13095 // 操作注册表失败
#define EC_ICV_PM_FAILTOLOADFILE						13096 // 打开文件失败
#define EC_ICV_PM_FAILTOPARSEFILE   					13097 // 解析文件失败
#define EC_ICV_PM_FAILTOGETXMLNODE  					13098 // 文件格式错误
#define EC_ICV_CRS_NORMALFAILED 						13200 // 目前暂未辨识出的错误
#define EC_ICV_CRS_EXCEPTION							13201 // 异常
#define EC_ICV_CRS_CFGFILEERROR 						13202 // 配置文件错误
#define EC_ICV_CRS_LOADDLLFAIL  						13203 // 加载动态库失败
#define EC_ICV_CRS_NOTFINDSCHEME						13204 // 根据预案ID找不到对应的预案
#define EC_ICV_CRS_NOTFINDSCHEMEITEMS   				13205 // 根据Index和ItemID找不到对应的预案项信息
#define EC_ICV_CRS_REGLOCALQUEFAIL  					13206 // 注册联动服务本地QUEUE失败
#define EC_ICV_CRS_EXISTSAMEINFO						13207 // 列表中存在相同的信息
#define EC_ICV_CRS_PORTCFGERROR 						13208 // 获取配置文件中端口信息失败
#define EC_ICV_CRS_INITACTIONFAIL   					13209 // 动作模块对应的动态库初始化失败
#define EC_ICV_CRS_GETDLLFUNCFAIL   					13210 // 获取动态库中函数失败
#define EC_ICV_CRS_PARAMERROR   						13211 // 传入参数错误
#define EC_ICV_CRS_OUTOFMEMORY  						13212 // 申请内存失败
#define EC_ICV_CRS_SETSCHETIMERFAIL 					13213 // 设定定时期失败
#define EC_ICV_CRS_FINDTRIGGERIDFAIL					13214 // 处理手动触发请求时找不到触发源ID对应的信息
#define EC_ICV_CRS_SCHEMETYPENOTSUPPORT 				13215 // 预案类型联动服务不支持
#define EC_ICV_CRS_ADDITEMSTOSCHEMEFAIL 				13216 // 向预案中添加预案项信息失败
#define EC_ICV_CRS_ADDSCHEMETOCURRLISTFAIL  			13217 // 向当前执行的预案列表中中添加预案信息失败
#define EC_ICV_CRS_GETHQUEUEIPANDIDFAIL 				13218 // 根据HQUEUE获取IP、ID信息失败
#define EC_ICV_CRS_NOTACTIVE							13219 // 本机是非活动主机
#define EC_ICV_CRS_SCHEMEALREADYCONFIRM 				13220 // 预案已确认
#define EC_ICV_CRS_SCHEMEITEMCANTCANCEL 				13221 // 不能确认取消的预案项
#define EC_ICV_CRS_SCHEMEITEMALREADYCFM 				13222 // 预案项已确认
#define EC_ICV_CRS_CFMUSERNORIGHT   					13223 // 确认预案项的用户没有权限
#define EC_ICV_CRS_SCHEMEITEMCFMSTATEERROR  			13224 // 用户操作的预案项确认状态错误
#define EC_ICV_CRS_FAILTOINITTRIGGER					13225 // 触发源插件初始化错误
#define EC_ICV_CRS_FAILTOCALLBACKRIGGER 				13226 // 通知触发源结果失败
#define EC_ICV_CRS_FAILTOCALLBACKACTION 				13227 // 通知动作结果失败
#define EC_ICV_CRS_FAILTOLOADMODULE 					13228 // 加载模块失败
#define EC_ICV_CRS_INVALIDPARAM 						13229 // 传入参数非法
#define EC_ICV_CRS_INVALIDNODENAME  					13230 // 节点名非法
#define EC_ICV_CRS_FAILTOSPAWNPROCESS   				13231 // 启动进程失败
#define EC_ICV_CRS_FAILTOOPENZTPAGE 					13232 // 打开组态画面失败
#define EC_ICV_CRS_FAILTOREMOVESCHEME   				13233 // 从当前列表中删除预案失败
#define EC_ICV_CRS_FAILTOCONFIRM_ITEM   				13234 // 确认预案项失败
#define EC_ICV_CRS_VARPROXY_VAR_EXISTS  				13250 // 变量已注册
#define EC_ICV_CRS_VARPROXY_VAR_NOTEXISTS   			13251 // 变量未注册
#define EC_ICV_CRS_FAILTOLOADCONFIG 					13260 // 读取配置文件失败
#define EC_ICV_CRS_WRONGREQUEST 						13261 // 非法请求
#define EC_ICV_CRS_FAILTOSTARTTASK  					13262 // 启动任务失败
#define EC_ICV_CRS_XMLREQUESTERROR  					13263 // XML请求失败
#define EC_ICV_CRS_FAILTOREADDB 						13264 // 读不到sqlite文件
#define EC_ICV_CRS_FAILTOPUTQUEUE   					13265 // 队列插入失败
#define EC_ICV_CRS_FAILTOMODIFYDB   					13266 // 修改数据库失败
#define EC_ICV_CRS_TRIGGER_NOTEXISTS					13269 // 触发源不存在
#define EC_ICV_CRS_SCHEME_NOTEXISTS 					13270 // 预案不存在
#define EC_ICV_CRS_ITEM_NOTEXISTS   					13271 // 预案项不存在
#define EC_ICV_CRS_ITEMTYPE_NOTEXISTS   				13272 // 预案项类型不存在
#define EC_ICV_CRS_SCHEME_REPEATEXEC					13273 // 预案重复执行
#define EC_ICV_CRS_FAILTOFIND_ACTIONTASK				13274 // 找不到预案项对应的任务指针
#define EC_ICV_CRS_FAILTOGET_ACTIONFUNCADDR 			13275 // 找不到动作模块执行函数地址
#define EC_ICV_CRS_SHCEMEITEM_TIMEOUT   				13276 // 预案项执行超时
#define EC_ICV_CRS_SHCEMEITEM_CANCELED  				13277 // 预案项被取消
#define EC_ICV_CRS_FAILTOSENDACTIONTOCLIENT 			13278 // 发送给客户端动作失败
#define EC_ICV_CRS_UNKOWNCLIENTREQUEST  				13279 // 未知的客户端请求
#define EC_ICV_CRS_CLIENTREMOVED						13280 // 客户端超时被删除
#define EC_ICV_CRS_CLIENTNOTEXIST   					13281 // 客户端不存在
#define EC_ICV_CRS_NOTIMPLEMENTEDYET					13282 // 尚未实现的功能
#define EC_ICV_CRS_CONFIRMUSER_WRONG					13283 // 确认者错误
#define EC_ICV_CRS_SCHEME_UNMANUTRIGGER 				13284 // 预案不可手动执行
#define EC_ICV_CRS_SCHEME_EMPTY 						13285 // 预案未包含任何预案项
#define EC_ICV_CRS_FAILTOPACK_STATUS					13286 // 对当前状态打包时出错
#define EC_ICV_CRS_FAILTOUNPACK_STATUS  				13287 // 对当前状态解包时出错
#define EC_ICV_CRS_SCHEME_UNTERMINATABLE				13288 // 预案不可终止
#define EC_ICV_CRS_TRIGGER_EMPTY 						13289 // 触发源未包含任何预案
#define EC_ICV_CRS_API_BASE_NO  						13300 // 客户端API错误代码起始
#define EC_ICV_CRS_API_ACTION_CLIENT_EXIST  			13301 // 动作客户端重复定义
#define EC_ICV_CRS_FAILTOREGREMOTEQUE   				13302 // 注册远程队列失败
#define EC_ICV_CRS_XMLRESPONSEERROR 					13304 // 解析XML返回失败
#define EC_ICV_CRS_QUERY_TIMEOUT						13305 // 查询超时
#define EC_ICV_CRS_TRIGGER_ALREADY_EXISTS               13306//触发源已经存在
#define EC_ICV_CRS_CATEGORY_NOTEXISTS					13307//预案分类不存在

#define EC_ICV_AMSERVICE_UNKNOW_FAILURE 				14000 // 未知错误
#define EC_ICV_AM_ENCODE_FAILURE						14001 // 编码异常错误
#define EC_ICV_AM_DECODE_FAILURE						14002 // 解码异常错误
#define EC_ICV_AM_DECODE_UNKNOW_PACKAGE 				14003 // 未知格式电文包
#define EC_ICV_AM_DECODE_INVALID_PACKAGE_BEGINSIGN  	14004 // 电文包头部起始标志错误
#define EC_ICV_AM_DECODE_INVALID_PACKAGE_DATA   		14005 // 电文包数据解码错误
#define EC_ICV_AM_DECODE_INVALID_PACKAGE_ENDSIGN		14006 // 电文包头部结束标志错误
#define EC_ICV_AM_DECODE_INVALID_RECV_DATAHEADER		14007 // 错误接收电文头部
#define EC_ICV_AM_DECODE_INVALID_RECV_DATA  			14008 // 错误接收电文数据
#define EC_ICV_AM_INVALID_LICENSEINFO   				14009 // 非法许可证信息
#define EC_ICV_AM_AMSERVICE_BEYOND_LOGIN_MAXCOUNT   	14010 // 超过登录最大数目
#define EC_ICV_AM_AMSERVICE_INVALID_PACKAGE_HEADER  	14011 // 电文头部错误
#define EC_ICV_AM_AMSERVICE_INVALID_LIST_COUNT  		14012 // 数据列表个数不大于零
#define EC_ICV_AM_AMSERVICE_INVALID_LIST_OFFSET 		14013 // 数据列表偏移量不正确
#define EC_ICV_AM_AMSERVICE_INVALID_DATA_COUNT  		14014 // 电文纪录分段个数错误
#define EC_ICV_AM_AMSERVICE_INVALID_BUFFER_LEN  		14015 // 电文数据长度异常
#define EC_ICV_AM_AMSERVICE_INVALID_RECV_DATA   		14016 // 接收数据异常
#define EC_ICV_AM_AMSERVICE_INVALID_SERVERINFO  		14017 // 服务端(IP地址、端口)信息异常
#define EC_ICV_AM_AMSERVICE_INVALID_CENTERSERVER		14018 // 非法中心服务器
#define EC_ICV_AM_AMSERVICE_INVALID_SERVERTYPE  		14019 // 非法服务器类型
#define EC_ICV_AM_AMSERVICE_SERVER_INACTIVE 			14020 // 服务非活动
#define EC_ICV_AM_AMSERVICE_PUTSLOWREQ_QUEUE_ERROR  	14021 // 写入到SLOWREQ_TASK的队列失败
#define EC_ICV_AM_AMSERVICE_PUTINSTREQ_QUEUE_ERROR  	14022 // 写入到INSTREQ_TASK的队列失败
#define EC_ICV_AM_AMSERVICE_PUTTIMERS_QUEUE_ERROR   	14023 // 写入到TIMESYNC_TASK的队列失败
#define EC_ICV_AM_AMSERVICE_PUTCENT_QUEUE_ERROR 		14024 // 写入到CENSYNSEND_TASK的队列失败
#define EC_ICV_AM_AMSERVICE_PUTEXTCMD_QUEUE_ERROR   	14025 // 写入到EXTCMDREQ_TASK的队列失败
#define EC_ICV_AM_ACE_WRITE_GUARD_RETURN_ERROR  		14026 // 执行ACE_WRITE_GUARD_RETURN失败
#define EC_ICV_AM_ACE_READ_GUARD_RETURN_ERROR   		14027 // 执行ACE_READ_GUARD_RETURN失败
#define EC_ICV_AM_AMSERVICE_EXTDLL_NOTFOUND 			14028 // 扩展动态库AM_EXTSVC_ExtCommand接口没有找到
#define EC_ICV_AM_AMSERVICE_EXTDLL_RELEASE_NOTFOUND 	14029 // 扩展动态库AM_EXT_Command_Release接口没有找到
#define EC_ICV_AM_AMSVR_INVALID_USERNAME_LEN			14030 // 非法用户登陆名字符串长度
#define EC_ICV_AM_AMSVR_INVALID_PASSWORD_LEN			14031 // 非法用户口令字符串长度
#define EC_ICV_AM_AMSVR_INVALID_FULLNAME_LEN			14032 // 非法用户全名字符串长度
#define EC_ICV_AM_AMSVR_INVALID_DESCRIPTION_LEN 		14033 // 非法用户描述字符串长度
#define EC_ICV_AM_AMSVR_INVALID_COUNT_LEN   			14034 // 非法个数长度
#define EC_ICV_AM_AMSVR_INVALID_OFFSET_LEN  			14035 // 非法偏移量长度
#define EC_ICV_AM_AMSVR_INVALID_LOGINUSER_INFO  		14040 // 非法登录用户信息
#define EC_ICV_AM_AMSVR_INVALID_NODENAME				14041 // 非法节点名称
#define EC_ICV_AM_AMSVR_INVALID_SESSIONID   			14042 // 非法登录用户票据
#define EC_ICV_AM_AMSVR_LOGINUSER_NOTEXIST  			14043 // 登录用户不存在
#define EC_ICV_AM_AMSERVICE_PUTRM_QUEUE_ERROR   		14044 // 写入到RMSYNC_TASK的队列失败
#define EC_ICV_AM_AMSERVICE_EXPORT_NOCHANGE 			14045 // 导出内容没有更新
#define EC_ICV_AM_AMSERVICE_LOGIN_UPTO_LIMIT			14046 // 登录用户数满
#define EC_ICV_AM_AMSRV_LOGINUSER_EXIST 				14047 // 当前用户已登录，不能重复登录
#define EC_ICV_AMAPI_PARAMERROR 						14100 // 输入参数错误
#define EC_ICV_AMAPI_UNKNOW 							14101 // 函数未知错误
#define EC_ICV_AMAPI_PASSWORDERROR  					14102 // 用户口令错误
#define EC_ICV_AMAPI_INVALID_GROUPNAME  				14110 // 非法群组名称
#define EC_ICV_AMAPI_INVALID_USER_OF_GROUP  			14111 // 用户不属于该群组
#define EC_ICV_AMAPI_INVALID_USER_SESSIONID 			14120 // 非法用户SESSIONID
#define EC_ICV_AMAPI_INVALID_USER_LOGINNAME 			14121 // 非法用户登录名
#define EC_ICV_AMAPI_INVALID_USER_PASSWORD  			14122 // 非法用户口令
#define EC_ICV_AMAPI_INVALID_USER_FULLNAME  			14123 // 非法用户全名
#define EC_ICV_AMAPI_INVALID_USER_DESCRIPTION   		14124 // 非法用户描述
#define EC_ICV_AMAPI_INVALID_USER_IPADDRESS 			14125 // 非法用户登录IP地址
#define EC_ICV_AMAPI_INVALID_USER_NODENAME  			14126 // 非法用户登录节点名
#define EC_ICV_AMAPI_INVALID_RESOURCE_TYPE  			14130 // 非法资源类型
#define EC_ICV_AMAPI_INVALID_RESOURCELABEL  			14131 // 非法资源标签
#define EC_ICV_AMRAPI_UNKNOW_ERROR  					14201 // 未知错误(一般不会出现)
#define EC_ICV_AMRAPI_INVALID_PARAM 					14202 // 参数错误，主要是参数指针为空
#define EC_ICV_AMRAPI_INIT_SHAREDMEM_FAILURE			14203 // 共享内存初始化失败
#define EC_ICV_AMRAPI_CREATE_SHAREDMEM_FAILURE  		14204 // 共享内存创建失败
#define EC_ICV_AMRAPI_SENDANDRECV_MESSAGE_ERROR 		14205 // 公共的发送和接收消息调用失败
#define EC_ICV_AMRAPI_INVALID_SERVER_IP 				14206 // 非法服务器IP地址
#define EC_ICV_AMRAPI_INVALID_SERVER_PORT   			14207 // 非法服务器端口号
#define EC_ICV_AMRAPI_NOT_ENOUGH_MEMORY 				14208 // 无足够的内存
#define EC_ICV_AMRAPI_INVALID_APPNAME   				14209 // 非法应用程序名
#define EC_ICV_AMRAPI_SERVERINFO_NOT_FOUND  			14210 // 无法找到指定服务器信息
#define EC_ICV_AMRAPI_SERVERINFO_NOT_INIT   			14211 // 服务信息未初始化
#define EC_ICV_AMRAPI_RECV_PTR_IS_NULL  				14212 // 接收收到的数据指针为空
#define EC_ICV_AMRAPI_INVALID_IPADDRESS 				14213 // 非法IP地址
#define EC_ICV_AMRAPI_INVALID_IPPORT					14214 // 非法IP端口
#define EC_ICV_AMRAPI_INVALID_SERVERINFO				14215 // 非法服务信息
#define EC_ICV_AMRAPI_INVALID_BAKSERVER_IP  			14216 // 非法备机服务器IP地址
#define EC_ICV_AMRAPI_INVALID_BAKSERVER_PORT			14217 // 非法备机服务器端口号
#define EC_ICV_AMRAPI_INVALID_GROUP_NAME				14220 // 非法群组名称
#define EC_ICV_AMRAPI_INVALID_GROUP_DESC				14221 // 非法群组描述
#define EC_ICV_AMRAPI_INVALID_GROUP_INFO				14222 // 非法群组信息
#define EC_ICV_AMRAPI_INVALID_GROUP_COUNT   			14223 // 非法群组列表个数
#define EC_ICV_AMRAPI_INVALID_GROUP_OFFSET  			14224 // 非法群组偏移量
#define EC_ICV_AMRAPI_INVALID_GROUP_DATA				14225 // 非法群组数据
#define EC_ICV_AMRAPI_USER_PASSWORD_ERROR   			14241 // 用户口令错误
#define EC_ICV_AMRAPI_USER_PASSWORD_NOTCHANGE   		14242 // 相同的用户口令
#define EC_ICV_AMRAPI_INVALID_USER_LOGINNAME			14243 // 非法用户登录名称
#define EC_ICV_AMRAPI_INVALID_USER_FULLNAME 			14244 // 非法用户全名
#define EC_ICV_AMRAPI_INVALID_USER_PASSWORD 			14245 // 非法用户口令
#define EC_ICV_AMRAPI_INVALID_USER_NEWPASSWORD  		14246 // 非法用户新口令
#define EC_ICV_AMRAPI_INVALID_USER_DESC 				14247 // 非法用户描述
#define EC_ICV_AMRAPI_INVALID_USER_COUNT				14248 // 非法用户列表个数
#define EC_ICV_AMRAPI_INVALID_USER_OFFSET   			14249 // 非法用户偏移量
#define EC_ICV_AMRAPI_INVALID_USER_SESSIONID			14250 // 非法用户票据
#define EC_ICV_AMRAPI_INVALID_USER_NAMELIST 			14251 // 非法用户名称列表
#define EC_ICV_AMRAPI_INVALID_RESOURCE_LABEL			14260 // 非法资源标签
#define EC_ICV_AMRAPI_INVALID_RESOURCE_NAME 			14261 // 非法资源名称
#define EC_ICV_AMRAPI_INVALID_RESOURCE_DESC 			14262 // 非法资源描述
#define EC_ICV_AMRAPI_INVALID_RESOURCE_INFO 			14263 // 非法资源信息
#define EC_ICV_AMRAPI_INVALID_RESOURCE_TYPE 			14264 // 非法资源类型
#define EC_ICV_AMRAPI_INVALID_RESOURCE_LOCKABEL 		14265 // 非法资源锁定状态
#define EC_ICV_AMRAPI_INVALID_RESOURCE_COUNT			14266 // 非法资源列表个数
#define EC_ICV_AMRAPI_INVALID_RESOURCE_OFFSET   		14267 // 非法资源偏移量
#define EC_ICV_AMRAPI_INVALID_RESAUTH_LABEL 			14280 // 非法资源标签
#define EC_ICV_AMRAPI_INVALID_RESAUTH_BEHAVIOR  		14281 // 非法资源权限
#define EC_ICV_AMRAPI_INVALID_RESAUTH_COUNT 			14282 // 非法资源权限列表个数
#define EC_ICV_AMRAPI_INVALID_RESAUTH_OFFSET			14283 // 非法资源权限列表偏移量
#define EC_ICV_AMRAPI_RESAUTH_GM_NOT_AUTH   			14284 // 无群组管理权限
#define EC_ICV_AMRAPI_RESAUTH_UM_NOT_AUTH   			14285 // 无用户管理权限
#define EC_ICV_AMRAPI_RESAUTH_RM_NOT_AUTH   			14286 // 无资源管理权限
#define EC_ICV_AMRAPI_INVALID_RESAUTH_SIZE  			14287 // 非法资源的长度
#define EC_ICV_AMRAPI_INVALID_EXPORT_TIME   			14290 // 非法配置更改时间
#define EC_ICV_AMRAPI_INVALID_IMPORT_DATA   			14291 // 非法导入数据
#define EC_ICV_AMRAPI_INVALID_IMPORT_DATALEN			14292 // 非法导入数据长度
#define EC_ICV_AMRAPI_INVALID_EXPORT_DATALEN			14293 // 非法导出数据长度
#define EC_ICV_AMRAPI_INVALID_EXPORT_DATA   			14294 // 非法导出数据
#define EC_ICV_AMRAPI_INVALID_EXTCMD_ID 				14300 // 非法外部扩展ID
#define EC_ICV_AMRAPI_INVALID_EXTCMD_REQUEST			14301 // 非法外部扩展请求数据
#define EC_ICV_AMRAPI_INVALID_EXTCMD_REQUEST_LEN		14302 // 非法外部扩展请求数据长度
#define EC_ICV_AMRAPI_INVALID_EXTCMD_RESPONSE   		14303 // 非法外部扩展接收数据
#define EC_ICV_AMRAPI_INVALID_EXTCMD_RESPONSE_LEN   	14304 // 非法外部扩展接收数据长度
#define EC_ICV_AMRAPI_INVALID_LOGGEDUSERINFO			14310 // 非法登录用户列表
#define EC_ICV_AMRAPI_INVALID_LOGGEDUSERCOUNT   		14311 // 非法登录用户个数
#define EC_ICV_AMRAPI_INVALID_NODENAME  				14312 // 非法节点名称
#define EC_ICV_AMRAPI_INVALID_LOGGEDUSEROFFSET  		14313 // 非法登录用户偏移量
#define EC_ICV_AMRAPI_INVALID_LICENSE_PTR   			14314 // 非法许可证指针
#define EC_ICV_AUTH_NO_SVRIP							14315 // 权限服务IP获取失败，请检查是否配置访问SCADA
#define EC_ICV_AUTH_NO_HMICFG							14316 // 配置文件HMICfg.xml未下载成功，请检查IE设置
#define EC_ICV_AUTH_NO_SCADALST							14317 // 配置文件SCADALst.xml未下载成功，请检查IE设置
#define EC_ICV_DA_GENERAL_ERROR 						15000 // 一般I/O错误.命令被拒绝
#define EC_ICV_DA_INIT_SHARED_MEM_FAILURE   			15001 // 共享内存初始化失败
#define EC_ICV_DA_SHARED_MEM_NOT_INIT   				15002 // 共享内存未初始化
#define EC_ICV_DA_MEM_WRITEPAST 						15003 // 地址超出共享内存范围
#define EC_ICV_DA_INVALID_PARAMETER 					15004 // 非法参数
#define EC_ICV_DA_INVALID_BLOCK 						15005 // 非法的数据块
#define EC_ICV_DA_AREA_NO_MORE_ITEMS					15006 // 队列中已没有记录
#define EC_ICV_DA_RESET_CTLBLOCK_ERROR  				15007 // 重置CBlock信息发生错误
#define EC_ICV_DA_ADD_DEVICE_CONFIG_FAILURE 			15008 // 添加设备信息出错
#define EC_ICV_DA_ADD_DATABLOCK_CONFIG_FAILURE  		15009 // 添加数据块信息出错
#define EC_ICV_DA_FIND_DEVICE_CONFIG_FAILURE			15010 // 查找设备信息出错
#define EC_ICV_DA_FIND_DATABLOCK_CONFIG_FAILURE 		15011 // 查找数据块信息出错
#define EC_ICV_DA_DEVICE_CONFIG_OVERLAP 				15012 // 设备配置已经添加
#define EC_ICV_DA_DATABLOCK_CONFIG_OVERLAP  			15013 // 数据块配置已经添加
#define EC_ICV_DA_DRIVERINFO_MAP_FAILURE				15014 // 创建驱动信息区MAP失败，或驱动信息区MAP不存在
#define EC_ICV_DA_TOO_MANY_DEVICES  					15015 // 添加的设备数超过限制
#define EC_ICV_DA_TOO_MANY_DATABLOCKS   				15016 // 添加的数据块数超过限制
#define EC_ICV_DA_CONFIG_OVERLAP						15017 // 驱动配置出现重复
#define EC_ICV_DA_IO_DLL_NOT_FOUND  					15018 // 驱动框架所需DLL未找到
#define EC_ICV_DA_IO_SYMBOL_NOT_FOUND   				15019 // 驱动DLL中未找到导出函数
#define EC_ICV_DA_DRIVER_RESTART						15020 // 驱动重启
#define EC_ICV_DA_DATA_QUALITY_NOT_GOOD 				15021 // 数据质量不是好的
#define EC_ICV_DA_INVALID_IO_ADDR   					15022 // 无效的I/O地址
#define EC_ICV_DA_DDA_SEND_ERROR						15023 // 写控制命令错误
#define EC_ICV_DA_UNKNOWN_EGU_TYPE  					15024 // EGU类型错误
#define EC_ICV_DA_DATA_TYPE_ERROR   					15025 // 数据类型错误
#define EC_ICV_DA_INVALID_SIG_CONDITION 				15026 // 非法信号条件
#define EC_ICV_DA_WRITE_OVERFLOW						15027 // 写值溢出
#define EC_ICV_DA_LATCH_DATA							15028 // 获取到Latch数据
#define EC_ICV_DA_DDA_NOTINIT   						15029 // 未调用过DDA初始化接口
#define EC_ICV_DA_DEVICE_EXISTS 						15030 // 同名设备已存在
#define EC_ICV_DA_DEVICE_NOT_FOUND  					15031 // 不存在所给名称的设备
#define EC_ICV_DA_DATABLOCK_EXISTS  					15032 // 数据块设备已存在
#define EC_ICV_DA_DATABLOCK_NOT_FOUND   				15033 // 数据块设备不存在
#define EC_ICV_SYNCDRV_TIMER_REGISTER_FAILED			15034 // 定时器注册失败
#define EC_ICV_SYNCDRV_DATAREQ_SEND_FAILED  			15035 // 发送数据同步请求失败
#define EC_ICV_SYNCDRV_MESSAGE_TYPE_INVALID 			15036 // 无效请求/消息类型
#define EC_ICV_DA_CVUDP_OPEN_PORT_FAILED				15037 // CVUDP打开端口失败
#define EC_ICV_DA_CVUDP_REGISTER_FAILED 				15038 // CVUDP注册reactor失败
#define EC_ICV_DA_CVUDP_INIT_FAILED 					15039 // CVUDP接口初始化失败
#define EC_ICV_DA_IO_SEND_ERROR 						15040 // IO数据发送错误
#define EC_ICV_DA_IO_CONNECT_FAILED 					15041 // 与设备建立连接失败
#define EC_ICV_DA_IO_SOCKET_NOT_EXIST   				15042 // 与设备的socket连接不存在
#define EC_ICV_DA_NULL_POINTER  						15043 // 空指针
#define EC_ICV_DA_DRIVER_STOP   						15044 // 驱动已经停止
#define EC_ICV_DA_EXCEPTION_ALREADY_EXISTS  			15045 // 异常数据已经注册过
#define EC_ICV_DA_EXCEPTION_NOT_FOUND   				15046 // 未找到需要注销的异常数据
#define EC_ICV_DA_EXCEPTION_LENGTH_ERROR				15047 // 异常数据长度不正确
#define EC_ICV_DA_DEVICE_NOT_SWITCH 					15048 // 设备没有切换成功
#define EC_ICV_DA_CVTCP_INIT_FAILED 					15049 // CVTCP接口初始化失败
#define EC_ICV_DA_SYNC_NO_DATA_AVAILABLE				15050 // 未提供驱动同步数据
#define EC_ICV_DA_NOT_IN_SCAN_CYCLE 					15051 // 数据块未到扫描周期
#define EC_ICV_DA_DATABLOCK_IN_READ_STATE   			15052 // 数据块读消息尚在处理中
#define EC_ICV_DA_ADDRESS_CANNOT_WRITE  				15053 // 地址不支持写入数据
#define EC_ICV_DA_NO_NEED_TO_READ_WRITE 				15054 // 单连接设备不需要读取或写入
#define EC_ICV_DA_WRITEMSG_LENGTH_ERROR 				15055 // 写入设备数据长度小于0，或长度超过最大允许字节数
#define EC_ICV_DA_IOSYNC_NOT_INIT   					15056 // 驱动数据同步模块尚未初始化
#define EC_ICV_DA_IOSYNC_INIT_ALREADY   				15057 // 驱动数据同步模块已经初始化过
#define EC_ICV_DA_IOSYNC_BIND_FAILURE   				15058 // 网络端口绑定不成功
#define EC_ICV_DA_IOSYNC_FIND_FAILURE   				15059 // 查找不成功
#define EC_ICV_DA_CONFIG_FILE_INVALID   				15060 // 无效的配置文件，无法解析配置文件或者文件不存在
#define EC_ICV_DA_CONFIG_FILE_STRUCTURE_ERROR   		15061 // 配置文件结构错误，不符合配置文件格式要求
#define EC_ICV_DA_CONFIG_ATTRIBUTE_ERROR				15062 // 配置文件属性错误
#define EC_ICV_DA_NO_RESPONSE_DATA_AVAILABLE			15063 // 没有设备响应数据
#define EC_ICV_DA_RESPONSE_ERROR						15064 // 设备响应出错
#define EC_ICV_DA_UNKNOWN_DATABLOCK_TYPE				15065 // 未知的数据块类型
#define EC_ICV_DA_GUARD_RETURN  						15066 // 获取互斥锁失败
#define EC_ICV_DA_CONFIG_FILE_SAVE_FAILURE  			15067 // 保存配置文件失败
#define EC_ICV_SYNCDRV_STATUS_SWITCH_FAILED 			15068 // 状态切换失败
#define EC_ICV_SYNCDRV_DIAGRAM_FORMAT_INVALID   		15069 // 电文格式有误
#define EC_ICV_DA_RECV_MODE_IS_ASYNC					15070 // 接收方式为异步接收
#define EC_ICV_DA_RECV_TIMEOUT  						15071 // 接收数据超时
#define EC_ICV_DA_PACKET_NOT_ENTIRE 					15072 // 获取到的设备响应只是部分数据包，不完整，需要拆包解包
#define EC_ICV_DA_NOT_ENOUGH_SPACE  					15073 // 缓冲区空间不够
#define EC_ICV_DA_RECV_BUF_EMPTY						15074 // 设备的接收缓冲区为空
#define EC_ICV_DA_ADDR_PARSE_FAILED 					15075 // 解析地址时出错
#define EC_ICV_DA_DATABASE_RW_EXCEPTION 				15076 // 数据库读写异常
#define EC_ICV_DA_DLL_DRV_FRAME_VER_MISMATCH			15077 // DLL架构驱动版本号与框架不符
#define EC_ICV_SYNCDRV_INIT_NOT_DONE					15078 // 初始工作未完成，尚无法控制
#define EC_ICV_SYNCDRV_REG_PEER_FAILED  				15079 // 注册对等中心失败
#define EC_ICV_104_DEVCONNECT_FAIL  					15080 // 设备连接失败
#define EC_ICV_104_DEVCONNECT_RECONNECT 				15081 // 设备重新连接
#define EC_ICV_104_DEVCONNECT_SWITCH					15082 // 设备连接切换
#define EC_ICV_104_SEND_CALLUP_FAIL 					15083 // 发送总召唤失败
#define EC_ICV_104_SEND_SFRAME_FAIL 					15084 // 发送S帧电文失败
#define EC_ICV_104_SEND_TESTR_FAIL  					15085 // 发送测试电文失败
#define EC_ICV_104_SEND_SPYK_FAIL   					15086 // 发送单点遥控失败
#define EC_ICV_104_SEND_DPYK_FAIL   					15087 // 发送双点遥控失败
#define EC_ICV_104_RECV_HEAD_MISMATCH   				15088 // 接收电文电文头不符合规约
#define EC_ICV_104_RECV_MSGLENTH_MISMATH				15089 // 接收电文长度不符
#define EC_ICV_104_RECV_CONFIRM_FAIL					15090 // 接收确认电文失败
#define EC_ICV_104_SENDCOUNT_MISMATCH   				15091 // 发送计数不匹配
#define EC_ICV_104_RECVCOUNT_MISMATCH   				15092 // 接收计数不匹配
#define EC_ICV_104_CFGFILE_HAS_NOT_DEV  				15093 // 配置文件中无设备
#define EC_ICV_104_CFGFILE_HAS_NOT_TAG  				15094 // 配置文件中无变量
#define EC_ICV_104_READVALUE_TAG_DONT_EXSIT 			15095 // 读取的变量不存在
#define EC_ICV_104_WRITEVALUE_TAG_DONT_EXSIT			15096 // 写入的变量不存在
#define EC_ICV_104_WRITEVALUE_TAG_HASNT_RIGHT   		15097 // 变量没有可写的权限
#define EC_ICV_SYNCDRV_CONFIG_OPEN_FAILED   			15098 // 打开配置文件失败
#define EC_ICV_SYNCDRV_CONFIG_FORMAT_INVALID			15099 // 配置文件缺少属性或格式不正确
#define EC_ICV_PDB_SUCCESS_WITH_INFO					15101 // 操作成功完成
#define EC_ICV_PDB_INVALID_HANDLE   					15102 // 无效的句柄
#define EC_ICV_PDB_INVALID_PARAMETER					15103 // 无效的参数
#define EC_ICV_PDB_NO_PERMISSION						15104 // 没有权限
#define EC_ICV_PDB_RETURN_SUCCESS_WITH_WARN 			15105 // 执行成功但有警告
#define EC_ICV_PDB_RETURN_NORMAL_FAILED 				15106 // 执行失败
#define EC_ICV_PDB_SQLITE_DB_OPEN_FAILED				15107 // 打开配置数据库失败
#define EC_ICV_PDB_SQLITE_DB_CONFIG_ERROR   			15108 // 配置数据库异常
#define EC_ICV_PDB_RDB_TABLE_NOTF   					15109 // 数据表不存在
#define EC_ICV_PDB_PB_ERROR_TYPE						15110 // 错误的过程变量类型
#define EC_ICV_PDB_PB_NONEXIST  						15111 // 过程变量不存在
#define EC_ICV_PDB_PB_NONEXIST_FIELD					15112 // 变量属性不存在
#define EC_ICV_PDB_SERVICE_EXIST						15113 // 同时启动两个相同进程
#define EC_ICV_PDB_DB_CREATE_FAIL   					15114 // 创建实时数据库Berkeley文件失败
#define EC_ICV_PDB_SCADA_CFG_ERROR  					15115 // SCADACfg.xml结构错误
#define EC_ICV_PDB_NOT_IMPLEMENTED  					15116 // 方法未实现
#define EC_ICV_PDB_RDA_NOT_IN_GROUP 					15117 // LRDA/RDA Group中未找到所取的NTF
#define EC_ICV_PDB_NODE_NAME_NOEXIST					15118 // 节点名称不存在
#define EC_ICV_PDB_REQUEST_TIMEOUT  					15119 // 操作超时
#define EC_ICV_PDB_FIELD_READ_ONLY  					15120 // 变量属性只读
#define EC_ICV_PDB_OUTPUT_DISABLE   					15121 // 禁止输出状态
#define EC_ICV_PDB_NO_CTRL_PERMISSION   				15122 // 没有控制权限
#define EC_ICV_PDB_NO_ALM_ACK_PERMISSION				15123 // 没有报警确认权限
#define EC_ICV_PDB_ALM_TYPE_ERROR   					15124 // 报警类型错误
#define EC_ICV_PDB_ALM_ALREADY_ACKED					15125 // 报警已被确认
#define EC_ICV_PDB_ALM_NOEXIST  						15126 // 报警不存在
#define EC_ICV_RDA_PB_NOTREG							15127 // 过程变量未注册
#define EC_ICV_RDA_PB_NOTLOOKUPED   					15128 // 过程变量初始错误码，等待获取数据中
#define EC_ICV_PDB_DRV_INIT_EXCEPTION   				15129 // 加载驱动异常
#define EC_ICV_PDB_DRV_DLL_OPEN_FAILED  				15130 // 打开驱动动态库失败
#define EC_ICV_PDB_DRV_DLL_SYMBOL_NOT_FOUND 			15131 // 驱动中未找到所需接口函数
#define EC_ICV_PDB_DRV_RETURN_TYPE_MISMATCH 			15132 // 驱动返回的数据类型不匹配
#define EC_ICV_PDB_DRV_NOT_FOUND						15133 // 未知驱动
#define EC_ICV_PDB_NET_PENDING  						15134 // 操作已通过网络发送，等待操作结果中
#define EC_ICV_PDB_NOSERVER 							15135 // 未找到SCADA主机
#define EC_ICV_PDB_PB_ALREADY_EXIST 					15136 // 指定的过程变量已经存在
#define EC_ICV_PDB_TAG_IPN_INVALID  					15137 // 无效的Tag 句柄，需要重新注册Tag
#define EC_ICV_PDB_KEY_NOT_FOUND						15138 // 主键未找到
#define EC_ICV_PDB_VERSION_MISMATCH 					15139 // 环境版本不一致
#define EC_ICV_PDB_VERS 								15140 // PDB版本不匹配
#define EC_ICV_PDB_SCADA_NAME_NOT_FOUND 				15141 // 节点名称获取失败
#define EC_ICV_PDB_RT_DIR_CREATE_FAIL   				15142 // 创建运行时数据目录失败
#define EC_ICV_PDB_CTRL_TABLE_CREATE_FAIL   			15143 // 创建控制队列
#define EC_ICV_PDB_CREAT_LOCAL_QUEUE_FAIL   			15144 // NETQUEUE创建本地QUEUE失败
#define EC_ICV_PDB_PORTCFGERROR   						15145 // 从配置获取端口错误
#define EC_ICV_PDB_DRIVER_NOT_REGISTER				15146// 驱动未连接或注册
#define EC_ICV_PDB_OVERRIDE								15147// 该变量启用了超驰
#define EC_ICV_PDB_DEVEICELST							15148// 加载配置文件DeviceLst.xml失败
#define EC_ICV_PDB_CONTROL_NOT_ENABLE							15149// 禁止控制,控制总开关关闭

#define EC_ICV_RDA_STUB_PORT_OPEN_FAIL  				15150 // 打开监听端口失败
#define EC_ICV_RDA_STUB_PORT_ACCEPT_FAIL				15151 // 打开监听端口失败
#define EC_ICV_RDA_EVENT_ID_NOT_FOUND   				15152 // Event_Map中未找到对应的EventID
#define EC_ICV_RDA_EVENT_TYPE_MISS_MATCH				15153 // Event_Map中EventType不符合
#define EC_ICV_RDA_HGROUP_INVALID   					15154 // 无效的Group_Handler
#define EC_ICV_RDA_NTF_NOT_REGISTERED   				15155 // 所访问的过程变量注册失败，或者未注册
#define EC_ICV_RDA_BUFFER_LENGTH_NOT_ENOUGH 			15156 // 所提供的缓存区大小不够
#define EC_ICV_RDA_SVR_NOT_CONNECTED					15157 // 服务端未连接
#define EC_ICV_PDB_ALMCATE_FILEFORM_BAD 				15158 // 报警分类配置文件格式错误
#define EC_ICV_PDB_TAGALMCATE_NOT_FOUND 				15159 // 变量所属报警分类未找到
#define EC_ICV_EMPTY_RESULT_SET 						15161 // 查询结果为空
#define EC_ICV_ENUM_END_OF_ARRAY						15162 // 已到达查询结果队列尾
#define EC_ICV_DBE_MODEL_VERSION_OPEN_FAIL  			15180 // 打开过程数据引擎Version失败
#define EC_ICV_DBE_MODEL_VERSION_MALLOC_FAIL			15181 // 过程数据引擎分配Version空间失败
#define EC_ICV_PDB_ACCESS_BEYOND_BOUND  				15182 // 过程数据访问越界
#define EC_ICV_PDB_MEMORYMAP_CREATE_FAIL				15183 // 共享内存创建失败
#define EC_ICV_PDB_SEGMENT_NOT_EXIST					15184 // 共享内存端不存在
#define EC_ICV_PDBENGINE_MALLOC_FAIL					15185 // 共享内存分配失败
#define EC_ICV_PDBENGINE_NAME_CACHE_BIND_FAIL   		15186 // 过程数据访问引擎绑定失败
#define EC_ICV_PDBENGINE_PB_NAME_EXIST  				15187 // 过程数据块名称已存在
#define EC_ICV_PDBENGINE_NAME_CACHE_FAIL				15188 // 过程数据块名称引擎出错
#define EC_ICV_EA_INVALID_REDIS							15189 // redis连接错误
#define EC_ICV_EA_INVALID_RABBITMQ						15190 //

#define EC_ICV_EA_ALARMAREAS_ERROR  					15201 // 报警区数目错误：太大或太小
#define EC_ICV_EA_ALARMSECAREAS_ERROR   				15202 // 安全区数目错误：太大或太小
#define EC_ICV_EA_ALARMNODE_ERROR   					15203 // 报警结点名错误：为空
#define EC_ICV_EA_ALARMVALUE_ERROR  					15204 // 报警值错误：为空
#define EC_ICV_EA_ALARMSTATUS_ERROR 					15205 // 报警状态错误
#define EC_ICV_EA_EVENTAPPNAME_ERROR					15206 // 事件的应用程序名错误：为空
#define EC_ICV_EA_VERIFIEDALARM_ERROR   				15207 // 确认报警错误：确认者为空
#define EC_ICV_EA_FILTERCONTENT_ERROR   				15208 // 过滤内容错误：为空
#define EC_ICV_EA_FILTERTYPE_ERROR  					15209 // 过滤类型错误
#define EC_ICV_EA_STRINGNULL_ERROR  					15210 // 字符串为空错误
#define EC_ICV_EA_ALARMTAG_ERROR						15211 // 报警Tag错误:为空
#define EC_ICV_EA_REGISTER_ERROR						15212 // 注册失败
#define EC_ICV_EA_NODENOTINLIST_ERROR   				15213 // 队列中不存在该节点
#define EC_ICV_EA_GETDATAFROMACTIVECOMPUTER_ERROR   	15214 // 无法从活动服务器获得数据
#define EC_ICV_EA_GETUSERNAME_ERROR 					15215 // 获得用户名错误
#define EC_ICV_EA_CREATEEVENTTABLE_ERROR				15216 // 生成Event Table错误
#define EC_ICV_EA_CREATEAlARMTABLE_ERROR				15217 // 生成Alarm Table错误
#define EC_ICV_EA_ARRAYNULL_ERROR   					15218 // 数组为空错误
#define EC_ICV_EA_ACKALARM_ERROR						15219 // 确认报警失败
#define EC_ICV_EA_SCADANAMENULL_ERROR   				15220 // SCADA名字为空错误
#define EC_ICV_EA_MAINIPNULL_ERROR  					15221 // Main IP为空错误
#define EC_ICV_EA_BACKUPIPNULL_ERROR					15222 // Backup IP为空错误
#define EC_ICV_EA_TABLENAME_ERROR   					15223 // 表名错误
#define EC_ICV_EA_SHM_READFILTERMSG_ERROR   			15224 // 获取到的过滤类型错误
#define EC_ICV_EA_GETDATAFRMSHAREDQUEUE_ERROR   		15225 // 从BDB队列中获取到数据错误：太长或太短
#define EC_ICV_EA_EVENTNODE_ERROR   					15226 // 事件结点错误：为空
#define EC_ICV_EA_ALARMSTOREOBJECTPTRNULL_ERROR 		15227 // AlarmStore类对象指针为空
#define EC_ICV_EA_REMOVERECFRMQUEUE_ERROR   			15228 // 从BDB队列中删除前几个记录错误
#define EC_ICV_EA_RECORDNOTFOUND_ERROR  				15229 // 找不到对应的记录
#define EC_ICV_EA_HALARMPTRNULL_ERROR   				15231 // 客户端报警指针为空
#define EC_ICV_EA_USERNOAUTH_ERROR  					15232 // 用户没有确认权限
#define EC_ICV_EA_ELMROOTNULL_ERROR 					15233 // xml根节点为空错误
#define EC_ICV_EA_DATAINVALID_ERROR 					15234 // 数据无效错误
#define EC_ICV_EA_ALARMID_ERROR 						15235 // 报警ID有错：为空
#define EC_ICV_EA_NOAUTHTODELETEALARM_ERROR 			15236 // 没有权限去删除报警
#define EC_ICV_EA_NOAUTHTOACKALARM_ERROR				15237 // 没有权限去确认报警
#define EC_ICV_EA_REGALM_ALLFAILTOSENDTOSCADA   		15238 // 发送报警确认、删除或注册给SCADA时失败
#define EC_ICV_EA_INVALIDE_QUERY_HANDLE 				15239 // 无效的查询句柄
#define EC_ICV_EA_FAILURE   							15240 // FAILURE错误
#define EC_ICV_EA_ADD_QUERYCOND_ERROR   				15241 // 添加查询条件失败
#define EC_ICV_EA_REMOVE_QUERYCOND_ERROR				15242 // 删除查询条件失败
#define EC_ICV_EA_NO_QUERYRECORD_LEFT   				15243 // 没有记录供查询
#define EC_ICV_EA_INVALIDE_QUERY_CONDITION  			15244 // 查询条件无效
#define EC_ICV_EA_PROTO_FORMAT_ERROR					15245 // protocol buffer 格式有误
#define EC_ICV_EA_PROTO_TRANSFORM_ERROR 				15246 // protocol buffer 序列化/解析有误
#define EC_ICV_EA_DB_QUERY_OPERATION_FAIL   			15247 // 数据库查询操作失败
#define EC_ICV_EA_DB_FIELD_NOT_COINCIDE 				15248 // 数据库字段数不一致
#define EC_ICV_EA_ARCHIVE_DB_UNAVALIBLE 				15249 // 尚无归档数据库
#define EC_ICV_EA_ARCHIVE_DB_CREATE_FAILED  			15250 // 归档数据库创建失败
#define EC_ICV_EA_FILEPATHNOTEXIST  					15251 // 目录不存在
#define EC_ICV_EA_ARCHIVE_DB_NOT_FINISHED   			15252 // 过期记录尚未归档完毕
#define EC_ICV_EA_DB_WRITE_OPERATION_FAIL   			15253 // 数据库写操作失败
#define EC_ICV_EA_ALARMSTATUS_UNCHANGED					15254 // 报警状态不变
#define EC_ICV_EA_FAILED_APPLY_MEMORY					15255 // 报警申请内存空间失败
#define EC_ICV_EA_ALARMPTRNULL_ERROR 					15256 // 类对象指针为空
#define EC_ICV_EA_DATA_TIME_LAG							15257 // 数据更新时间滞后于alarmserver存储的数据时间
#define EC_ICV_EA_CALL_BACK_FAILED						15258 // 调用CallBack方法异常
#define EC_ICV_EA_INVALIDE_USER_DEF_MSG					15259 // 第三方报警插入信息错误
#define EC_ICV_EA_LOAD_CONFIG   						15260 // 读取配置文件失败	tuzi
#define EC_ICV_EA_CONFIG_FILE_STRUCTURE_ERROR			15261 // 配置文件结构错误，不符合配置文件格式要求
#define EC_ICV_EA_SCADALIST_EMPTY   					15262 // otheralarm的scadalist为空
#define EC_ICV_EA_SERALM_LIBALM_SAME					15263 // alarmserver存的报警与alarmlib存的报警内容一致
#define EC_ICV_EA_SHMQUEUE_INIT_ERROR   				15299 // 初始化共享内存Queue失败


#define EC_ICV_HT_DIR_NOTFOUND  						15301 // 目录不存在
#define EC_ICV_HT_DIR_CREATE							15302 // 目录创建失败
#define EC_ICV_HT_FILE_CREATE   						15303 // 文件创建失败
#define EC_ICV_HT_FILE_OPEN 							15304 // 打开文件失败
#define EC_ICV_HT_FILE_EXISTENCE						15305 // 文件不存在
#define EC_ICV_HT_FILE_READ 							15306 // 文件读取失败
#define EC_ICV_HT_FILE_WRITE							15307 // 文件写入失败
#define EC_ICV_HT_FILE_FTELL							15308 // 文件指针移动失败
#define EC_ICV_HT_FILE_CORRUPT  						15309 // 文件格式不正确
#define EC_ICV_HT_FILE_DELETE   						15310 // 文件删除失败
#define EC_ICV_HT_NETWORK_INIT  						15311 // 初始化网络失败
#define EC_ICV_HT_NETWORK_CONN  						15312 // 连接主机失败
#define EC_ICV_HT_NETWORK_SEND  						15313 // 发送数据失败
#define EC_ICV_HT_NETWORK_RECV  						15314 // 读取数据失败
#define EC_ICV_HT_PEER_DOWN 							15315 // 对等节点计算机关闭
#define EC_ICV_HT_CLIENT_TOO_MANY_TAGS  				15320 // tag数量超限
#define EC_ICV_HT_CLIENT_INVALID_TAG_NUMBER 			15321 // 无效的tag数量
#define EC_ICV_HT_CLIENT_INVALID_TAG_NAME   			15322 // 无效的tag点名称
#define EC_ICV_HT_CLIENT_INVALID_TIMESTAMP  			15323 // 无效的起止时间戳
#define EC_ICV_HT_CLIENT_INVALID_TIMEINTERV 			15324 // 无效的时间间隔
#define EC_ICV_HT_CLIENT_INVALID_TIMEOUT				15325 // 无效的超时时间
#define EC_ICV_HT_CLIENT_CONNECT_SERVER 				15327 // 连接服务端失败
#define EC_ICV_HT_CLIENT_ILLEGAL_SCADA  				15328 // 非法的scada名称
#define EC_ICV_HT_CLIENT_SCADA_OFFLINE  				15329 // scada节点掉线
#define EC_ICV_HT_LOAD_CONFIG   						15330 // 读取配置文件失败
#define EC_ICV_HT_CONFIG_CORRUPT						15331 // 配置文件损坏
#define EC_ICV_HT_SQLITE								15332 // 数据库操作错误
#define EC_ICV_HT_SQLITE_NO_RECORD  					15333 // 未找到数据记录
#define EC_ICV_HT_QUERY_BAD_COMMAND 					15334 // 错误的查询命令
#define EC_ICV_HT_QUERY_NO_TAG  						15335 // 查询中不包含Tag点
#define EC_ICV_HT_PROCESSDB 							15337 // 操作过程数据库失败
#define EC_ICV_HT_BAD_TAGNAME   						15338 // 非法的tag点名称
#define EC_ICV_HT_SYNC_BAD_COMMAND  					15339 // 非法的同步命令
#define EC_ICV_HT_NEW   								15340 // 分配内存失败
#define EC_ICV_HT_THREAD								15341 // 创建线程失败
#define EC_ICV_HT_NOT_FOUND 							15342 // MemCache文件未找到
#define EC_ICV_HT_TIME_OUT  							15343 // 操作超时
#define EC_ICV_HT_SHM_CREATE							15344 // 创建共享内存失败
#define EC_ICV_HT_MUTEX_ACQUIRE 						15345 // 获取锁失败
#define EC_ICV_DA_WRITE_T1TIMEOUT						15346 // 控制时间超时
#define EC_ICV_DA_WRITE_T2TIMEOUT						15347 // 控制时间超时
#define EC_ICV_HISTARCH_RTDCOL_NO_NEWRECORD 			15400 // no new data collected
#define EC_ICV_HISTARCH_RTDCOL_TAG_REGFAILED			15401 // fail to register rda tag to group
#define EC_ICV_HISTARCH_RTDCOL_GRP_DEFFAILED			15402 // fail to define an rda group
#define EC_ICV_HISTARCH_RTDCOL_TAG_REPEATED 			15403 // tag already registered
#define EC_ICV_HISTARCH_RTDCOL_MUT_AQFAILED 			15404 // mutable exclusive object acquiring failed
#define EC_ICV_HISTARCH_RTDCOL_TAG_NOTFOUND 			15405 // fail to find a specific tag
#define EC_ICV_HISTARCH_RTDCOL_NO_SOURCETAG 			15406 // fail to get tags from hyper db
#define EC_ICV_HISTARCH_RTDCOL_TIMER_REGFAILED  		15407 // fail to register a timer in a timer queue
#define EC_ICV_DA_EX_QUEUE_EXIST						15500 // 队列已存在
#define EC_ICV_DA_EX_QUEUE_NOT_EXIST					15501 // 队列不存在
#define EC_ICV_DA_EX_CREATE_QUEUE_FAILED				15502 // 队列创建失败
#define EC_ICV_DA_EX_RELEASE_QUEUE_FAILED   			15503 // 队列释放失败
#define EC_ICV_DA_EX_RECV_TIMEOUT   					15504 // 队列获取超时
#define EC_ICV_DA_EX_QUIT   							15505 // 队列退出
#define EC_ICV_DA_EX_DEQUEUE_FAILED 					15506 // 出队列失败
#define EC_ICV_DA_EX_ENQUEUE_FAILED 					15507 // 入队列失败
#define EC_ICV_DA_EX_FULL_QUEUE 						15508 // 队列已满
#define EC_ICV_DA_ADDRESSLEN_TOOLONG   					15509 // IO地址长度过长
#define EC_ICV_DA_ADDRESS_BEGINFROMDIGITAL   			15510 // 非法名称:以数字开头
#define EC_ICV_DA_ADDRESS_SEGNUM_TOOSMALL   			15511 // 拆分出的地址段不足
#define EC_ICV_DA_ADDRESS_SEGNUM_TOOMUCH				15512 // 拆分出的地址段过多
#define EC_ICV_DA_DEVICENAME_EMPTY   					15513 // 设备名为空
#define EC_ICV_DA_IOADDRESS_EMPTY   					15514 // 设备地址为空
#define EC_ICV_DA_IOADDRESS_EXCEEDLIMIT					15515 // 设备地址超限
#define EC_ICV_DA_IOADDRESS_BITOFFSET_NOTEMPTY			15516 // 非数字量不存在位偏移量
#define EC_ICV_DA_IOADDRESS_DIGITALTAG_NOBITOFFSET		15517 // 数字量缺少位偏移量
#define EC_ICV_DA_IOADDRESS_BITOFFSET_EXCEEDLIMIT		15518 // 位偏移量超出允许范围
#define EC_ICV_DA_IOADDRESS_BYTEOFFSET_EXCEEDLIMIT		15519 // 字节偏移量超出允许范围
#define EC_ICV_DA_IOADDRESS_DATALENGTH_TOOSMALL			15520 // 数据块长度过小
#define EC_ICV_DA_IOADDRESS_DATALENGTH_TOOMUCH			15521 // 数据块长度过大
#define EC_ICV_ODBC_FAILTOREGISTEREVENTALARM			15601 // 注册事件报警失败
#define EC_ICV_ODBC_FAILTOCREATECACHETABLE  			15602 // 创建缓存表失败
#define EC_ICV_ODBC_FAILTOINSERTCACHETABLE  			15603 // 插入缓存记录失败
#define EC_ICV_ODBC_FAILTOOPENCACHEFILE 				15604 // 打开缓存文件失败
#define EC_ICV_ODBC_FAILTOQUERYCACHERECORD  			15605 // 查询缓存记录失败
#define EC_ICV_ODBC_FAILTOOPENODBCDATACFG   			15606 // 打开配置文件失败
#define EC_ICV_ODBC_ODBCDNOTCONFIGED					15607 // 没有配置ODBC
#define EC_ICV_ODBC_FAILTOINITDB						15608 // 初始化数据库失败
#define EC_ICV_ODBC_NEEDRECONNECTDB 					15609 // 需要重连数据库
#define EC_ICV_ODBC_FAILTODEFINEAGROUPINPDB 			15610 // 定义Group失败
#define EC_ICV_ODBC_PDBNOTSTARTED   					15611 // PDB没有启动
#define EC_ICV_ODBC_FAILTOREGISTERGROUP 				15612 // 注册过程数据库的组失败
#define EC_ICV_ODBC_ODBCDATAISRUNNING   				15613 // 已经打开了一个配置服务
#define EC_ICV_ODBC_FAILTOSTARTTASK 					15614 // 启动ODBC任务失败
#define EC_ICV_ODBC_XMLREQUESTERROR 					15615 // 解析配置文件XML失败
#define EC_ICV_ODBC_INVALIDTAGTYPE  					15616 // 配置文件中TAGType类型非法
#define EC_ICV_ODBC_SQLEXECFAILED   					15617 // 数据库连接失败或sql执行失败
#define EC_ICV_ODBC_INVALID_DATASOURCE  				15618 // 不存在的数据源名称
#define EC_ICV_ODBC_DATASOURCE_EXIST					15619 // 同名的数据源已经存在
#define EC_ICV_ODBC_RULE_HANDLE_EXIST   				15620 // 规则句柄已经存在
#define EC_ICV_ODBC_RULE_HANDLE_NOT_EXIST   			15621 // 规则句柄不存在
#define EC_ICV_ODBC_RULE_PLUGIN_OPEN_FAILED 			15622 // SQLTransfer插件目录打开失败
#define EC_ICV_ODBC_DLL_FUNC_NOT_EXIST  				15623 // EC_ICV_ODBC_DLL_FUNC_NOT_EXIST
#define EC_ICV_ODBC_NAMED_FUNC_NOT_EXIST				15624 // 获取方法失败，方法未找到
#define EC_ICV_ODBC_NAMED_FUNC_EXIST					15625 // 同名方法的已经存在
#define EC_ICV_ODBC_TIMER_REGISTER_FAILED   			15626 // 注册定时器失败
#define EC_ICV_ODBC_EVENT_REGISTER_FAILED   			15627 // 注册事件失败
#define EC_ICV_ODBC_ALARM_REGISTER_FAILED   			15628 // 注册报警失败
#define EC_ICV_ODBC_PROVIDER_HANDLE_NOT_EXIST   		15629 // Provider句柄不存在
#define EC_ICV_ODBC_PYTHON_EXECUTED_EXCEPTION   		15630 // python语句异常
#define EC_ICV_ODBC_PYTHON_MODULE_NOT_EXSIST			15631 // python模块不存在
#define EC_ICV_ODBC_PYTHON_NONE_CONFIG  				15632 // python脚本配置不存在
#define EC_ICV_ODBC_FAILTOREMOVECACHERECORD 			15633 // 删除缓存记录失败
#define EC_ICV_ODBC_THREADQUEUE_OVERFLOW				15634 // 线程队列过大，溢出
#define EC_ICV_DRVCTRL_FAILTOSTARTTASK  				15701 // STARTTASK失败
#define EC_ICV_DRVCTRL_XMLREQUESTERROR  				15702 // XML请求失败
#define EC_ICV_DRVCTRL_OUTOFMEMORY  					15703 // 申请内存失败
#define EC_ICV_DRVCTRL_REGLOCALQUEFAIL  				15704 // 注册DrvCtrl服务本地QUEUE失败
#define EC_ICV_DRVCTRL_PORTCFGERROR 					15705 // 获取配置文件中端口信息失败
#define EC_ICV_DRVCTRL_WRONGREQUEST 					15706 // REQUEST非法
#define EC_ICV_DRVCTRL_WRONGRESPONSE					15707 // RESPONSE非法
#define EC_ICV_DRVCTRL_DRVTABLE_UNEXIST 				15708 // Driver Table未创建
#define EC_ICV_DRVCTRL_DRV_BOOTED   					15709 // 驱动己启动
#define EC_ICV_DRVCTRL_DRV_UNEXIST  					15710 // 驱动不存在
#define EC_ICV_DRVCTRL_DEVICE_NOT_FOUND 				15711 // 配置文件中找不到设备
#define EC_ICV_DRVCTRL_NO_DATAGRAM_INSCOPE  			15712 // 未找到指定时间范围内的报文记录
#define EC_ICV_DRVCTRL_MODI_LOADCFG_FAIL				15713 // 修改日志配置文件失败
#define EC_ICV_DRVCTRL_OPEN_DGRMFILE_FAIL   			15714 // 不能成功打开数据报文件
#define EC_ICV_DRVCTRL_DGRMFILE_READ_DONE   			15715 // 数据报文件EOF
#define EC_ICV_DRVCTRL_DGRMFILE_FORMAT_ERROR			15716 // 数据报文件格式错误
#define EC_ICV_DRVCTRL_DGRMFILE_NEED_CONTINUE   		15717 // 本次无效，需继续下一次读取
#define EC_ICV_DRVCTRL_FAILTOREGREMOTEQUE   			15718 // 注册远程Queue失败
#define EC_ICV_DRVCTRL_FAILTOREGLOCALQUE				15719 // 注册本地Queue失败
#define EC_ICV_DRVCTRL_DDA_UNAVAILABLE  				15720 // 没有可用的DDA接口
#define EC_ICV_RMSERVICE_CONFIG_FILE_INVALID			16001 // 无效的配置文件，无法解析配置文件或者文件不存在
#define EC_ICV_RMSERVICE_CONFIG_FILE_STRUCTURE_ERROR	16002 // 配置文件结构错误，不符合配置文件格式要求
#define EC_ICV_RMSERVICE_CONFIG_FILE_UNKOWN_PEER_COMM   16003 // 使用了未定义的通讯类型
#define EC_ICV_RMSERVICE_CONFIG_FILE_NO_UDP_PORT_SPEC   16004 // 未指定UDP端口信息
#define EC_ICV_RMSERVICE_CONFIG_FILE_NO_UDP_IP_SPEC 	16005 // 未指定IP信息
#define EC_ICV_RMSERVICE_NO_UDP_PORT_AVALIBLE   		16006 // 打开UDP端口全部失败，无UDP端口可用
#define EC_ICV_RMSERVICE_SERVICE_UNAVALIBLE 			16007 // RMService服务未处于活动状态
#define EC_ICV_RMSERVICE_SERVICE_UNACTIVE   			16008 // 冗余状态未非活动状态，包括UNAVALIBLE
#define EC_ICV_RMSERVICE_INIT_SHARED_MEM_FAILURE		16012 // 共享内存初始化失败
#define EC_ICV_RMSERVICE_SHARED_MEM_NOT_INIT			16013 // 共享内存未初始化
#define EC_ICV_RMSERVICE_NO_HEART_BEAT_RECVED   		16014 // 未接收到心跳信息
#define EC_ICV_RMSERVICE_UNKOWN_STATUS_CTRL_CMD 		16015 // 未知的强制切换状态指令
#define EC_ICV_RMSERVICE_RESTART_PING_THREAD	 		16016 // 重启ping线程
#define EC_ICV_RMSERVICE_PING_HANDLE_TIME_OUT	 		16017 // ping handle timeout
#define EC_ICV_RMDS_CONFIG_FILE_STRUCTURE_ERROR 		16020 // 配置文件结构错误，不符合配置文件格式要求
#define EC_ICV_RMDS_SQLITE_OBJ_NOT_INIT 				16021 // SQLite对象未初始化
#define EC_ICV_RMDS_SQLITE_TABLE_NOT_EXISTS 			16022 // SQLite对象未初始化
#define EC_ICV_RMDS_API_NOT_INITIALIZED 				16023 // RMAPI未初始化
#define EC_ICV_RMDS_API_FAIL_ACQUIRE_LOCK   			16024 // 获取锁操作失败
#define EC_ICV_RMDS_API_CALL_BACK_ALREADY_REGGED		16025 // CallBack函数重复定义
#define EC_ICV_RMDS_API_GET_INPUT_QUEUE_ERROR   		16026 // 获取输入BDBQueue失败
#define EC_ICV_RMSERVICE_OBJ_EXISTED					16027 // 冗余对象己存在
#define EC_ICV_RMSERVICE_OBJ_UNEXISTED  				16028 // 冗余对象不存在
#define EC_ICV_RMSERVICE_OBJ_TBLFULL					16029 // 冗余对象记录表己满
#define EC_ICV_RMSERVICE_ERR_MSG						16030 // 错误的冗余消息
#define EC_ICV_104_LOAD_SYMBOL_FAILED   				16031 // 加载SOE Transfer动态库失败
#define EC_ICV_PM_PROJECT_NOT_EXIST						16050 // 工程不存在
#define EC_ICV_PM_SAVE_FILE_FAILURE						16051 // 服务管理器保存文件失败
#define EC_ICV_PM_PROJECT_NAME_EXISTS					16052 // 工程名称重复
#define EC_ICV_PM_PROJECT_PATH_EXISTS					16053 // 工程路径重复
#define EC_ICV_PM_INVALID_PROJECT_CFG				    16054 // 无效的工程配置
#define EC_ICV_PM_PROJECTS_TEMPLATE_NOT_EXIST			16055 // 工程模板不存在
#define EC_ICV_PM_INVALID_PACKET                        16066 // 无效的远程工程管理请求包
#define EC_ICV_PM_LOAD_BACKUPRESTORE_FAILURE            16067 // 加载备份恢复插件失败
#define EC_ICV_PM_BACKUP_PROJECT_FAILURE                16068 // 备份工程失败
#define EC_ICV_PM_BUFFER_TOO_SMALL                      16069 // 分配的缓冲区太小不足
#define EC_ICV_PM_NO_ACTIVE_PROJECT                     16070 // 没有活动工程
#define EC_ICV_SOAP_UNVALID_SOCKET						16071 // SOAP服务无效的套接字

//driver
#define EC_ICV_DRIVER_SEND_FAILURE						18000 // 发送请求失败
#define EC_ICV_DRIVER_RECV_FAILURE						18001 // 接收请求失败
#define EC_ICV_DEVICE_NOEXIST							18002 // 找不到设备
#define EC_DATABLOCK_NOEXIST							18003 // 找不到数据块
#define EC_DATATYPE_NOEXIST								18004 // 不存在的数据类型
#define EC_DISCONNECT									18005 // 连接断开
#define EC_CONNECTIONISNULL								18006 // 未连接
#define EC_ICV_DRIVER_INVALID_PARAMETER					18007 // 不恰当的参数
#define EC_ICV_DRIVER_API_NULL_PTR                      18009 // 驱动API传入指针为空
#define EC_ICV_DRIVER_API_ZERO_NUMBER                   18010 // 驱动API传入点数为零
#define EC_ICV_DRIVER_API_SAVEDATA_FAILED				18011 // 驱动API上传点失败
#define EC_ICV_DRIVER_API_SETCALLBACK_FAILD             18012 // 驱动API设置写控制回调失败
#define EC_ICV_DRIVER_API_LOADNODETABLE_FAILED          18013 // 驱动API获取节点列表失败
#define EC_ICV_DRIVER_API_MULTI_GETLOACLSCADA_FAILED    18014 // 驱动API多连接设备获取本地SCADA信息失败
#define EC_ICV_DRIVER_API_INVAILED_NODE_COUNT           18015 // 驱动API非法的SCADA节点个数
#define EC_ICV_DRIVER_API_REG_DRIVER_FAILED             18016 // 驱动API注册驱动失败
#define EC_ICV_DRIVER_API_SEND_DATA_FAILED              18017 // 发送数据失败
#define EC_ICV_TAGGROUP_INVALID_PARAMETER               18018 // 不恰当的自主块连接参数

//proto
#define EC_ICV_PROTO_LEN						18100 // 协议长度不正确
#define EC_ICV_PROTO_VALUE						18101 // 协议内容不正确

/************************************************************************/
/*                   CVRDA    (18200 ~ 18300)                      */
/************************************************************************/
#define EC_RD_CVRDA_LOAD_SCADALIST_FAILED			18200 // 加载配置文件失败
#define EC_RD_CVRDA_NODE_NOT_EXIST			18201 // 节点不存在
#define EC_RD_CVRDA_PARAMETER_INVALID			18202 // 参数不合法
#define EC_RD_CVRDA_RECIEVE_MSG_FAILED			18203 // 接收消息失败
#define EC_RD_CVRDA_QUERY_NTF_FAILED			18204 // 查询tag点失败
#define EC_RD_CVRDA_SCADA_NULL					18205 // Scada未找到
#define EC_RD_CVRDA_BUFFER_LEN					18206 // 缓冲区长度不够

//ihd

#define EC_RD_BASENO					110000
/************************************************************************/
/*                   ProcComm    (116100 ~ 116200)                      */
/************************************************************************/
#define EC_RD_PROCCOMM_BASENO			(EC_RD_BASENO + 6100)
#define EC_RD_PROCCOMM_NO_IDLE			(EC_RD_PROCCOMM_BASENO + 1)//进程通信没有空闲块 //There is no free block for inter process communication
#define EC_RD_PROCCOMM_SHAREMEM_CREATE	(EC_RD_PROCCOMM_BASENO + 2)//进程通信创建共享内存失败 //Create shared memory failed for inter process communication
#define EC_RD_PROCCOMM_REG_FAIL			(EC_RD_PROCCOMM_BASENO + 3)//进程通信注册失败 //Register inter process communication failed
#define EC_RD_PROCCOMM_MEM_MALLOC		(EC_RD_PROCCOMM_BASENO + 4)//进程通信malloc内存失败 //Malloc memory failed for inter process communication
#define EC_RD_PROCCOMM_NO_SHAREMEM		(EC_RD_PROCCOMM_BASENO + 5)//进程通信不存在共享内存 //There is no shared memory for inter process communication
#define EC_RD_PROCCOMM_NO_MEMSPACE		(EC_RD_PROCCOMM_BASENO + 6)//进程通信没有空闲内存空间 //There is no free memory space for inter process communication
#define EC_RD_PROCCOMM_RECV_QUIT		(EC_RD_PROCCOMM_BASENO + 7)//进程通信接收退出 //The inter process communication received quit message
#define EC_RD_PROCCOMM_RECV_TIMEOUT		(EC_RD_PROCCOMM_BASENO + 8)//进程通信接收时间超时 //The inter process communication received timeout
#define EC_RD_PROCCOMM_INVALID_SHAREMEM_NAME		(EC_RD_PROCCOMM_BASENO + 9)//进程通信不合法的共享内存名 //Invalid shared memory name for inter process communication
#define EC_RD_PROCCOMM_COMMONBLOCK_WRITE		(EC_RD_PROCCOMM_BASENO + 10)//进程通信写入公共块失败 //Write common block failed for inter process communication
#define EC_RD_PROCCOMM_COMMONBLOCK_READ		(EC_RD_PROCCOMM_BASENO + 11)//进程通信读取公共块失败 //Read common block failed for inter process communication
#define EC_RD_PROCCOMM_INVALID_INPUT		(EC_RD_PROCCOMM_BASENO + 12)//进程通信输入参数错误 //Invalid input parameter for inter process communication


#endif
