cmake_minimum_required(VERSION 3.10)

PROJECT (cvdriver)

SET(SRCS ${SRCS} main.cpp)

#Setting Target Name (executable file name | library name)
SET(TARGET_NAME cvdriver)
#Setting library type used when build a library
#SET(LIB_TYPE STATIC)

# SET(LINK_LIBS ACE cv6log cv6logimpl)

# IF(UNIX)
# 	IF(HPUX)
# 		SET(LINK_LIBS ${LINK_LIBS} pthread)
# 	ENDIF(HPUX)
# ELSE(UNIX)
# 	SET(LINK_LIBS ${LINK_LIBS} exceptionreport dbghelp)
# ENDIF(UNIX)

add_executable(${TARGET_NAME} ${LIB_TYPE} ${SRCS})

target_include_directories(${TARGET_NAME}
    PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/../../../include/ace
)

target_link_libraries(${TARGET_NAME}
    PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/../../../library/ace/libACE.so
)