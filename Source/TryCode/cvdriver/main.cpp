/**************************************************************
 *  Filename:    Server.cpp
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: 每个驱动都通过该壳加载executable目录下的cvdrivercommon.dll,通过cvdrivercommon.dll加载驱动dll运行.
 *
 *  @author:     shijunpu
 *  @version     07/26/2012  shijunpu  Initial Version
**************************************************************/
#ifndef WIN32
#include "ace/ACE.h"
#include "ace/DLL.h"
#else
#include <windows.h>
#endif

#include <stdio.h>
#include <string>
#ifdef WIN32
#ifndef NO_EXCEPTION_ATTACHER
#include "common/RegularDllExceptionAttacher.h"
#pragma comment(lib, "ExceptionReport.lib")
#pragma comment(lib, "dbghelp.lib")
// global member to capture exception so as to analyze 
static CRegularDllExceptionAttacher	g_RegDllExceptAttacher;
#endif//#ifndef NO_EXCEPTION_ATTACHER
#endif

using namespace std;

#define LOAD_LIBRARY_FAILED               -1
#define EXPORT_SYMBOL_FROM_LIBRARY_FAILED -2

#define CVDRIVER_COMMON_DLL_NAME		"cvdrivercommon"
typedef int (*PFN_Main)(int argc, char** argv);

#ifndef MAX_PATH
#define MAX_PATH		1024
#endif // MAX_PATH
int main(int argc, char** argv)
{
	char szOldCurDir[MAX_PATH] = {0};

#ifdef WIN32
	char szDriverDir[MAX_PATH] = {0};
	char szDrive[MAX_PATH] = {0};
	char szDriverCmmonDllName[MAX_PATH] = {0};
	_splitpath(argv[0], szDrive, szDriverDir, NULL, NULL);
	_snprintf(szDriverCmmonDllName, MAX_PATH - 1, "%s%s..\\..\\", szDrive, szDriverDir);

	::GetCurrentDirectory(sizeof(szOldCurDir),szOldCurDir);
	if(!::SetCurrentDirectory(szDriverCmmonDllName))
		printf("设置工作目录为：%s失败！\n", szDriverCmmonDllName);
	else
		printf("设置工作目录为：%s成功！\n", szDriverCmmonDllName);

	memset(szDriverCmmonDllName, 0, sizeof(szDriverCmmonDllName));
	_snprintf(szDriverCmmonDllName, MAX_PATH - 1, "%s%s..\\..\\%s.dll", szDrive, szDriverDir, CVDRIVER_COMMON_DLL_NAME);

	HMODULE hModule = LoadLibrary(szDriverCmmonDllName);
	if (NULL == hModule)
	{
		printf("Can not load the driver foundation DLL:%s, return code:%d.", CVDRIVER_COMMON_DLL_NAME, GetLastError());//不能加载驱动基础DLL %s,错误码:%d
		return LOAD_LIBRARY_FAILED;
	}

	//::SetCurrentDirectory(szOldCurDir);
	PFN_Main pfnMain = (PFN_Main)GetProcAddress(hModule, "dll_main_i"); 
	if(pfnMain == NULL)
	{
		printf("驱动基础DLL获取函数失败 %s, 错误码:%d", CVDRIVER_COMMON_DLL_NAME, GetLastError());
		return EXPORT_SYMBOL_FROM_LIBRARY_FAILED;
	}

	pfnMain(argc, argv);
	FreeLibrary(hModule);//暂时注释by hsx，不注释驱动会退不出，暂时未找到原因
#else
	ACE_OS::getcwd(szOldCurDir, sizeof(szOldCurDir));
	printf("\nCurrentWorkDir: %s ", szOldCurDir);

	string strFullPath = CVDRIVER_COMMON_DLL_NAME;
	
	// 尝试取进程的全路径名称
	char szDrvExePath[MAX_PATH] = {0};
	int  nExePathCount = ACE_OS::readlink( "/proc/self/exe", szDrvExePath, sizeof(szDrvExePath) );
	if ( nExePathCount < 0 || nExePathCount >= sizeof(szDrvExePath))
	{
		printf("\nFailed to get DrvExePath by readlink(/proc/self/exe)");
	}
	else
	{
		szDrvExePath[nExePathCount] = '\0'; // like /icv/Executable/drivers/modbus/modbus, because driverdll same name as exe,
		// use this name directly
		strFullPath = szDrvExePath;
		int nPos = strFullPath.find_last_of('/');
		if(nPos >= 0)
			strFullPath = strFullPath.substr(0, nPos); // /icv/Executable/drivers/modbus
		strFullPath += "/../../";
		strFullPath += CVDRIVER_COMMON_DLL_NAME;
	}

	ACE_DLL hDrvComonDll;
	long nErr = hDrvComonDll.open(strFullPath.c_str());
	if (nErr != 0)
	{
		printf("Load %s failed!", strFullPath.c_str());
		ACE_Time_Value tvSleep;
		tvSleep.msec(5000);
		ACE_OS::sleep(tvSleep);
		return LOAD_LIBRARY_FAILED;
	}
	printf("Load %s successfull!", strFullPath.c_str());

	//ACE_OS::chdir(szOldCurDir);
	PFN_Main pfnMain = (PFN_Main)hDrvComonDll.symbol("dll_main_i"); 
	if(pfnMain == NULL)
	{
		printf("驱动基础DLL获取函数失败 %s", CVDRIVER_COMMON_DLL_NAME);
		ACE_Time_Value tvSleep;
		tvSleep.msec(5000);
		ACE_OS::sleep(tvSleep);
		return EXPORT_SYMBOL_FROM_LIBRARY_FAILED;
	}
	
	pfnMain(argc, argv);
	hDrvComonDll.close();
#endif

	return 0;
};
