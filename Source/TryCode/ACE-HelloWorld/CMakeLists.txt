cmake_minimum_required(VERSION 3.5.0)
project(ACE-Helloworld VERSION 0.1.0 LANGUAGES C CXX)

# include_directories(
#     ~/ACE_INSTALL/include
# )

# link_directories(
#     ~/ACE_INSTALL/lib
# )

add_executable(ACE-Helloworld main.cpp)

target_include_directories(ACE-Helloworld
    PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/../../../include/ace
)

target_link_libraries(ACE-Helloworld
    PUBLIC ${CMAKE_CURRENT_SOURCE_DIR}/../../../library/ace/libACE.so
)

include(CTest)
enable_testing()

