#include "ace/Log_Msg.h"
#include "ace/Task.h"


class MyTask : public ACE_Task_Base {
    int count=0;
public:
    int svc() override {
        count++;
        // 任务的主体逻辑
        ACE_DEBUG((LM_DEBUG, ACE_TEXT("task run %d times\n"),count));
        return 0;
    }
};
 
int ACE_TMAIN(int argc, char* argv[])
{
    ACE_DEBUG((LM_DEBUG, ACE_TEXT("Hello World!\n")));

    MyTask task;
    task.activate();

    task.wait();

    return 0;
}