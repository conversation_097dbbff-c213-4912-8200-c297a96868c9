#include "Monitor_ngvs_config.h"
#include <filesystem>
#include <fstream>
extern CCVLog g_DSFDRSourceMonitorLog;

#define DSFNGVS_EXTENSION_LENGTH 8
#define DSFNGVS_EXTENSION ".dsfngvs"
#define EXCLUDED_FILENAME "datadefinition.dsfngvs"

void NgvsParse(const std::string &path, std::unordered_map<std::string, SourceMonitor::TopicInfo> &TopicInfoFromNGVS)
{
    int foundFilesCount = 0; // ï¿½ï¿½ï¿½ï¿½ï¿½Òµï¿½ï¿½ï¿½ï¿½Ä¼ï¿½ï¿½ï¿½ï¿½ï¿½
    try
    {
        {
            if (!std::filesystem::exists(path + "/NGVS"))
            {
                CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Error: NGVS directory path is not exist!")
                return;
            }
            if (!std::filesystem::is_directory(path + "/NGVS"))
            {
                CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Error: NGVS directory path is not directory!")
                return;
            }
        }
    }
    catch (const std::exception &e)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "NGVS directory wrong! Error: %s", e.what());
    }

    for (const auto &entry : std::filesystem::directory_iterator(path + "/NGVS"))
    {
        if (entry.is_regular_file())
        {
            const auto &filename = entry.path().filename().string();
            if ((filename.size() >= DSFNGVS_EXTENSION_LENGTH && filename.substr(filename.size() - DSFNGVS_EXTENSION_LENGTH) == DSFNGVS_EXTENSION) 
            && (filename != EXCLUDED_FILENAME))
            {
                ParseFromFile(entry.path().string(), TopicInfoFromNGVS);
            }
            foundFilesCount++;
        }
    }

    if (foundFilesCount == 0)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Error: No dsfngvs files found in NGVS directory!")
    }
    CV_INFO(g_DSFDRSourceMonitorLog, "NGVS config loaded and get TopicInfoFromNGVS successfully!");
}

void ParseFromFile(const std::string &filename, std::unordered_map<std::string, SourceMonitor::TopicInfo> &TopicInfoFromNGVS)
{
    try
    {
        std::ifstream file(filename);
        if (!file.is_open())
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Failed to open file: %s", filename.c_str());
            return;
        }

        nlohmann::json j;
        file >> j;

        // ï¿½ï¿½ï¿½ï¿½ NGVS ï¿½ï¿½ï¿½ï¿½
        if (j.contains("RecvNGVS"))
        {
            for (const auto &recv : j["RecvNGVS"])
            {
                SourceMonitor::TopicInfo TempTopicInfo;
                // if (!recv.contains("Name") || !recv.contains("NGVSVarNameSpace") || !recv.contains("SendNGVSName") ||
                //     !recv.contains("SendIPAddress") || !recv.contains("SendPort") || !recv.contains("LocalIPAddress") ||
                //     !recv.contains("LocalPort") || !recv.contains("PubWhenChange") || !recv.contains("PubCycle"))
                if (!recv.contains("Name") || !recv.contains("NGVSVarNameSpace") || !recv.contains("SendNGVSName") ||
                    !recv.contains("SendIPAddress") || !recv.contains("SendPort") || !recv.contains("LocalIPAddress") ||
                    !recv.contains("LocalPort"))
                {
                    CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Invalid RecvNGVS format.");
                    continue;
                }
                // 这里是临时处理，前端暂时下发的文件里不包含PubWhenChange、PubCycle这两个字段，因此我将其默认设置为true
                if(!recv.contains("PubWhenChange") || !recv.contains("PubCycle"))
                {
                    TempTopicInfo.IsPubWhenchange = true;
                    TempTopicInfo.PubcycleInterval = 1000;
                }
                else
                {
                    TempTopicInfo.IsPubWhenchange = recv["PubWhenChange"];
                    TempTopicInfo.PubcycleInterval = recv["PubCycle"];
                }
                TempTopicInfo.Name = recv["Name"];
                TempTopicInfo.NGVSVarNameSpace = recv["NGVSVarNameSpace"];
                TempTopicInfo.SendNGVSName = recv["SendNGVSName"];
                TempTopicInfo.SendIPAddress = recv["SendIPAddress"];
                TempTopicInfo.SendPort = recv["SendPort"];
                TempTopicInfo.RecIPAddress = recv["LocalIPAddress"];
                TempTopicInfo.RecPort = recv["LocalPort"];
                // TempTopicInfo.IsPubWhenchange = recv["PubWhenChange"];
                // TempTopicInfo.PubcycleInterval = recv["PubCycle"];
                TopicInfoFromNGVS[TempTopicInfo.Name] = TempTopicInfo;
            }
        }
        else if (j.contains("SendNGVS"))
        {
            for (const auto &send : j["SendNGVS"])
            {
                SourceMonitor::TopicInfo TempTopicInfo;
                if (!send.contains("Name") || !send.contains("NGVSVarNameSpace") || !send.contains("LocalIPAddress") ||
                    !send.contains("LocalPort") || !send.contains("PubWhenChange") || !send.contains("PubCycle"))
                {
                    CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Invalid SendNGVS format.");
                    continue;
                }
                // 这里是临时处理，前端暂时下发的文件里不包含PubWhenChange、PubCycle这两个字段，因此我将其默认设置为true
                if(!send.contains("PubWhenChange") || !send.contains("PubCycle"))
                {
                    TempTopicInfo.IsPubWhenchange = true;
                    TempTopicInfo.PubcycleInterval = 1000;
                }
                else
                {
                    TempTopicInfo.IsPubWhenchange = send["PubWhenChange"];
                    TempTopicInfo.PubcycleInterval = send["PubCycle"];
                }
                TempTopicInfo.Name = send["Name"];
                TempTopicInfo.NGVSVarNameSpace = send["NGVSVarNameSpace"];
                // 发送方的Name就是SendNGVSName
                TempTopicInfo.SendNGVSName = send["Name"];
                TempTopicInfo.SendIPAddress = send["LocalIPAddress"];
                TempTopicInfo.SendPort = send["LocalPort"];
                TempTopicInfo.RecIPAddress = "";
                TempTopicInfo.RecPort = 0;
                // TempTopicInfo.IsPubWhenchange = send["PubWhenChange"];
                // TempTopicInfo.PubcycleInterval = send["PubCycle"];
                TopicInfoFromNGVS[TempTopicInfo.Name] = TempTopicInfo;
            }
        }
        else
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, -1, "wrong file: %s", filename.c_str());
        }

        file.close();
    }
    catch (const std::exception &e)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Wrong NGVS file! Error: %s", e.what());
    }
    catch (...)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "LoadConfig failed,unknow exception");
    }
}
