#ifndef _SOURCE_MONITOR_DEF_H
#define _SOURCE_MONITOR_DEF_H

//错误码
#define SM_SUCCESS      0
#define SM_ERROR        -1

#define DIED        -1
#define SLAVE       0
#define MASTER      1 

// 冗余配置 为单机版or冗余版
#define SM_NODE_MODE_EXCEPTION          -1
#define SM_SINGLE_NODE_MODE             0
#define SM_REDUNDANT_NODE_MODE          1

// 冗余配置文件中IP个数
#define SM_SINGLE_NODE_MODE_IP_COUNT    1
#define SM_REDUNDANT_NODE_MODE_IP_COUNT 2


#define CHECK_NULL_RETURN_ERR(X) \
	{if(!X) return EC_ICV_RMSERVICE_CONFIG_FILE_STRUCTURE_ERROR;}

#define SM_RM_CFG_XML_NAME              ACE_TEXT("/RMService.xml")
#define SM_NGVS_DIRECTORY_NAME          ACE_TEXT("/NGVS")
#define SM_DSF_SETTING_XML_NAME         ACE_TEXT("/dsfSetting.xml")
//Attr Names.

// For RM Service Config.
// Node Names.
#define RM_SVC_CFG_XML_ROOT_NODE		ACE_TEXT("RMService")
#define RM_SVC_CFG_XML_PEER_COMM_NODE	ACE_TEXT("PeerCommunicator")
#define RM_SVC_CFG_XML_PEER_LIST_NODE	ACE_TEXT("PeerList")
#define RM_SVC_CFG_XML_PEER_NODE		ACE_TEXT("Peer")
#define RM_SVC_CFG_XML_IP_NODE			ACE_TEXT("IP")

// Node Names.
#define DSF_NODE			            ACE_TEXT("dsf")

// Attr Names.
#define RM_SVC_CFG_XML_SEQUENCE_ATTR	ACE_TEXT("sequence")
#define DSF_NODE_NAME			        ACE_TEXT("name")

// Version File Name.
#define DSF_VERSION_FILE_NAME           ACE_TEXT("/../version.txt")

// TopicInfo
#define SM_TOPIC_STATUS_TAG_PREFIX 		ACE_TEXT(".SYS::")
#define SM_TOPIC_STATUS_TAG_SUFFIX 		ACE_TEXT(".LAST_TIME")
#define SM_TOPIC_STATUS_INEXIST         ACE_TEXT("-2")
#define SM_TOPIC_STATUS_GET_ERROR         ACE_TEXT("-1")
#define SM_TOPIC_STATUS_INACTIVE         ACE_TEXT("0")
#define SM_TOPIC_STATUS_ACTIVE         ACE_TEXT("1")
#define SM_REDIS_GET_COMMOND_ERROR     -100
#define SM_REDIS_KEY_NOT_EXIST		   -101
#define SM_REDIS_WRONG_VALUE_TYPE	   -102


// TopicInfo
#define SM_TOPIC_STATUS_TAG_PREFIX 		ACE_TEXT(".SYS::")
#define SM_TOPIC_STATUS_TAG_SUFFIX 		ACE_TEXT(".LAST_TIME")
#define SM_TOPIC_STATUS_INEXIST         ACE_TEXT("-2")
#define SM_TOPIC_STATUS_GET_ERROR       ACE_TEXT("-1")
#define SM_TOPIC_STATUS_INACTIVE        ACE_TEXT("0")
#define SM_TOPIC_STATUS_ACTIVE          ACE_TEXT("1")

// RESTFUL
#define SM_HTTP_STATUS_OK               200

#define SM_HTTP_STATUS_BAD_REQUEST      400
#define SM_HTTP_STATUS_NOT_FOUND        404  

#define SM_HTTP_STATUS_INTERNAL_ERROR   500  //json构造/解析错误



#endif