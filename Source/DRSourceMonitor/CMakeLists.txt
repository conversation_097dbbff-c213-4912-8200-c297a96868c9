
cmake_minimum_required(VERSION 3.10)
set(CMAKE_CXX_STANDARD 17)
############FOR_MODIFIY_BEGIN#######################
PROJECT (DRSourceMonitor)

INCLUDE($ENV{DRDIR}CMakeCommon)

#Setting Source Files
SET(SRCS ${SRCS} mongoose.c
     NetworkUtils.cc
     cJSON.c
     common.cpp
     Monitor_ngvs_config.cpp
     SourceMonitor.cpp)
#Setting Target Name (executable file name | library name)
SET(TARGET_NAME dsfsourcemonitor)
#Setting library type used when build a library
#SET(LIB_TYPE SHARED)

SET(LINK_LIBS 
          servicebase
          drpmrapi 
          drvctrlSDK 
          shmqueue  licverify License 
          statgrab 
          sqlite3 
          pthread 
          tinyxml2
          fastrtps
          fastcdr
          drrmapi
          hiredis
          )
############FOR_MODIFIY_END#########################
INCLUDE($ENV{DRDIR}CMakeCommonExec)
