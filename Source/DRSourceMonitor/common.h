// common.h
#ifndef COMMON_H
#define COMMON_H

#include <string>
#include <iomanip>
#include <atomic>
#include <dirent.h>
#include <algorithm>
#include <cstdlib>
#include <cstring>
#include <fstream>
#include <iostream>
#include <sstream>
#include <vector>
#include <thread>
#include <chrono>
#include <iconv.h>

#include "common/CVLog.h"
#include "data_types.h"
#include <sqlite/sqlite3.h>
#include <nlohmann/json.hpp>
#include "statgrab.h"
#include "data_types.h"
#include "../drivers/drvctrl/drvctrlSDK/DRDrvctrlApi.h"
#include "common/ServiceBase.h"
#include <mutex>
#include <tinyxml2.h>
#include "tinyxml/tinyxml.h"
#include <filesystem>
#include "SourceMonitorDef.h"


extern std::mutex map_mutex; 
extern CCVLog g_DSFDRSourceMonitorLog;



class SourceMonitor
{
public:
    // 系统信息结构体
    struct SystemSource
    {
        std::string time_stamp;   // 时间戳
        std::string cpu_usage;    // CPU使用率
        std::string mem_usage;    // 内存使用情况
        std::string network_rx;   // 网络下载速率
        std::string network_tx;   // 网络上传速率
        std::string disk_space;   // 磁盘空间使用情况
        std::string disk_percent; // 磁盘使用百分比
    };

    struct ProcessSource
    {
        int Pid;
        std::string ProcessName;
        std::string Timestamp;
        std::string CpuUsage;
        std::string MemUsage;
        std::string IOUsage;
        std::string ThreadCount;  
        std::string Status;
    };

    struct DriverSource
    {
        int Pid;
        std::string DriverName;
        std::string Timestamp;
        std::string CpuUsage;
        std::string MemUsage;
        std::string IOUsage;
        std::string ThreadCount;
        std::string Status;
    };

    struct TopicInfo
    {
        std::string Name;
        std::string NGVSVarNameSpace;
        std::string SendNGVSName;
        std::string SendIPAddress;
        int SendPort;
        bool IsPubWhenchange;
        uint32_t PubcycleInterval;
        std::string RecIPAddress;
        int RecPort;
        std::string status;
    };

    struct IOInfo {
    long readBytes = 0;
    long writeBytes = 0;
    };

    SourceMonitor()
    {
        if (sg_init(1) != 0)
        {
            throw std::runtime_error("Failed to initialize statgrab");
        }
        sg_snapshot();
    }

    ~SourceMonitor()
    {
        sg_shutdown();
    }
    // void dataWriteThread();
    static int getProcessID(const std::string &processName);
    static bool getProcessRealTimeInfo(int pid, std::string &cpuUsage, std::string &memUsage, std::string &ioUsage, std::string &netUsage);

    // 获取所有系统信息
    int32 getSystemInfo(SystemSource &info);

    // 获取当前时间戳
    static int32 getCurTime(std::string &time);

    // 格式化网络速率
    std::string fomatSpeed(double speed);

private:
    // 获取CPU使用率
    int32 getCpuUsage(std::string &usage);

    // 获取内存使用情况
    int32 getMemoryUsage(std::string &usage);

    // 获取网络流量
    int32 getNetworkTraffic(std::string &rx_rate, std::string &tx_rate);

    // 获取系统磁盘占用率
    int32 getDiskUsage(std::string &space, std::string &percent);

    // static std::string getMemoryUsage();
    static void getNetworkUsage(std::string &rx_rate, std::string &tx_rate);
    static std::string getDiskUsage();
    
};

std::string convertToUTF8(const std::string &input);

std::string drvStatusToString(DRV_STATUS_E status);

// std::string extractLastName(const std::string& topicName);

bool checkConfigChange(std::string configPath);

bool checkNGVSChange(std::string configPath);

bool getNodeCfg(const std::string filePath, std::vector<std::string> &nodeList, std::string &nodeName, std::string &nodeSequence);

bool TopicIsExist(const std::string &a, SourceMonitor::TopicInfo &b);

std::string GetCurrentTime();

#endif

