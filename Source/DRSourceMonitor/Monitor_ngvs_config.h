#include <fstream>
#include <iostream>
#include <nlohmann/json.hpp>
#include <string>
#include <unordered_map>
#include <vector>
#include "common/CVLog.h"
#include <cstring>
#include <thread>
#include "common.h"

void NgvsParse(const std::string &path, std::unordered_map<std::string, SourceMonitor::TopicInfo> &TopicInfoFromNGVS);

void ParseFromFile(const std::string& filename, std::unordered_map<std::string, SourceMonitor::TopicInfo> &TopicInfoFromNGVS);

