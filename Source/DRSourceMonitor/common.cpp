// common.cpp
#include "common.h"
#include <filesystem>

std::mutex map_mutex;
CCVLog g_DSFDRSourceMonitorLog;

std::map<pid_t, std::pair<std::chrono::steady_clock::time_point, SourceMonitor::IOInfo>> prevIOData;

std::string convertToUTF8(const std::string &input)
{
    // 打开转换描述符，假设原始编码是 GB2312
    iconv_t cd = iconv_open("UTF-8", "GB2312");
    if (cd == (iconv_t)-1)
    {
        throw std::runtime_error("iconv_open failed");
    }

    size_t inBytesLeft = input.size();
    size_t outBytesLeft = inBytesLeft * 4; // UTF-8 编码可能需要更多空间
    std::string output(outBytesLeft, '\0');

    char *inBuf = const_cast<char *>(input.data());
    char *outBuf = &output[0];

    // 执行编码转换
    if (iconv(cd, &inBuf, &inBytesLeft, &outBuf, &outBytesLeft) == (size_t)-1)
    {
        iconv_close(cd);
        throw std::runtime_error("iconv failed");
    }

    // 关闭转换描述符
    iconv_close(cd);

    // 调整输出字符串大小
    output.resize(output.size() - outBytesLeft);
    return output;
}

std::string drvStatusToString(DRV_STATUS_E status)
{
    switch (status)
    {
    case DRV_UNKNOW:
        return "Unknown";
    case DRV_INITIAL_STATE:
        return "Initial State";
    case DRV_START_STATE:
        return "Start State";
    case DRV_STOP_STATE:
        return "Stop State";
    case DRV_STOP_AND_EXIT_STATE:
        return "Stop and Exit State";
    case DRV_START_FAIL_STATE:
        return "Start Fail State";
    case DRV_DRIVER_UNEXIST:
        return "Driver Unexist";
    default:
        return "Invalid State";
    }
}

// 获取进程 PID（通过进程名）自测使用，将来可以直接从进程管理器获取id
int SourceMonitor::getProcessID(const std::string &processName)
{
    DIR *dir = opendir("/proc");
    if (!dir)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Open proc failed while get processID!!!");
        return 1;
    }
    struct dirent *entry;
    while ((entry = readdir(dir)) != nullptr)
    {
        // 检查是否是数字（即 PID）
        if (entry->d_type == DT_DIR &&
            std::all_of(entry->d_name, entry->d_name + std::strlen(entry->d_name), ::isdigit))
        {
            std::string pid = entry->d_name;
            std::ifstream cmdlineFile("/proc/" + pid + "/cmdline");
            if (cmdlineFile.is_open())
            {
                std::string cmdline;
                std::getline(cmdlineFile, cmdline);

                // 替换cmdline中的所有'\0'为' '，因为命令行参数之间是以'\0'分隔的
                std::replace(cmdline.begin(), cmdline.end(), '\0', ' ');

                // 检查cmdline中是否包含目标进程名
                if (cmdline.find(processName) != std::string::npos)
                {
                    closedir(dir);
                    return std::stoi(pid);
                }
            }
        }
    }
    closedir(dir);
    return -1; // 未找到目标进程
}

// 获取所有系统信息
int32 SourceMonitor::getSystemInfo(SourceMonitor::SystemSource &info)
{
    int32 ret = 0;
    ret |= getCurTime(info.time_stamp);
    ret |= getCpuUsage(info.cpu_usage);
    ret |= getMemoryUsage(info.mem_usage);
    ret |= getNetworkTraffic(info.network_rx, info.network_tx);
    ret |= getDiskUsage(info.disk_space, info.disk_percent);
    return ret;
}

// 获取当前时间戳
int32 SourceMonitor::getCurTime(std::string &time)
{
    auto now = std::chrono::system_clock::now();
    auto in_time_t = std::chrono::system_clock::to_time_t(now);

    std::stringstream ss;
    ss << std::put_time(std::localtime(&in_time_t), "%Y-%m-%d %H:%M:%S");

    time = ss.str();
    return 0;
}

// 格式化网络速率
std::string SourceMonitor::fomatSpeed(double speed)
{
    std::stringstream ss;
    ss << std::fixed << std::setprecision(2);

    if (speed < 1000)
    {
        ss << speed << " B/s";
    }
    else if (speed < 1000000)
    {
        ss << (speed / 1000.0) << " KB/s";
    }
    else if (speed < 1000000000)
    {
        ss << (speed / 1000000.0) << " MB/s";
    }
    else
    {
        ss << (speed / 1000000000.0) << " GB/s";
    }

    return ss.str();
}

// 获取系统CPU使用率(libstatgrab)
int32 SourceMonitor::getCpuUsage(std::string &usage)
{
    sg_cpu_percents *cpu_stats = sg_get_cpu_percents(NULL);
    std::stringstream ss;
    ss << std::fixed << std::setprecision(2);

    if (cpu_stats != NULL)
    {
        ss << (100.0 - cpu_stats->idle) << "%";
        usage = ss.str();
        return 0;
    }
    usage = "N/A";
    return -1;
}

// 获取系统内存使用情况(libstatgrab)
int32 SourceMonitor::getMemoryUsage(std::string &usage)
{
    sg_mem_stats *mem_stats = sg_get_mem_stats(NULL);
    std::stringstream ss;
    ss << std::fixed << std::setprecision(2);

    if (mem_stats != NULL)
    {
        double total_gi = mem_stats->total / (1024.0 * 1024.0 * 1024.0);
        double used_gi = (mem_stats->total - mem_stats->free) / (1024.0 * 1024.0 * 1024.0);
        ss << used_gi << "Gi/" << total_gi << "Gi";
        usage = ss.str();
        return 0;
    }
    usage = "N/A";
    return -1;
}

// 获取系统网络流量(libstatgrab)
int32 SourceMonitor::getNetworkTraffic(std::string &rx_rate, std::string &tx_rate)
{
    static bool is_first_run = true;

    size_t num_entries;
    sg_network_io_stats *net_stats = sg_get_network_io_stats_diff(&num_entries);

    if (net_stats != NULL && num_entries > 0)
    {
        if (is_first_run)
        {
            rx_rate = tx_rate = "0 B/s";
            is_first_run = false;
            return 0;
        }

        double rx = 0, tx = 0;
        for (size_t i = 0; i < num_entries; i++)
        {
            rx += net_stats[i].rx;
            tx += net_stats[i].tx;
        }

        rx_rate = fomatSpeed(rx);
        tx_rate = fomatSpeed(tx);
        return 0;
    }
    rx_rate = tx_rate = "N/A";
    return -1;
}

// 获取进程的 CPU 和内存占用率（通过 top 指令解析）
bool SourceMonitor::getProcessRealTimeInfo(int pid, std::string &cpuUsage, std::string &memUsage, std::string &ioUsage, std::string &threadCount)
{
    std::string command = "top -b -n 1 -p " + std::to_string(pid) + " | awk '$1 == " + std::to_string(pid) + "'";
    FILE *pipe = popen(command.c_str(), "r");
    if (!pipe)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Failed to run top command!!!");
        // std::cerr << "Failed to run top command!" << std::endl;
        return false;
    }
    char buffer[256];
    std::string result;
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr)
    {
        result += buffer;
    }
    pclose(pipe);
    // 解析 top 的输出
    std::istringstream iss(result);
    std::string token;
    int column = 0;
    while (iss >> token)
    {
        column++;
        if (column == 9)
        { // 第9列是CPU占用率
            cpuUsage = token;
        }
        else if (column == 6)
        { // 第10列是内存占用率
            double temp = 0;
            if (token.back() == 'g' || token.back() == 'G')
            {
                temp = std::stod(token) * 1024;
            }
            else if (token.back() == 'm' || token.back() == 'M')
            {
                temp = std::stod(token);
            }
            else if (token.back() == 'k' || token.back() == 'K')
            {
                temp = std::stod(token) / 1024;
            }
            else
            {
                temp = std::stod(token) / 1024; // 默认按KB算
            }
            std::ostringstream stream;
            stream << std::fixed << std::setprecision(2) << temp;
            memUsage = stream.str();
        }
    }

    // 获取IO速率
    IOInfo currentInfo;
    std::ifstream ioFile("/proc/" + std::to_string(pid) + "/io");
    if (!ioFile.is_open())
    {
        std::cout << "无法打开 /proc/" << pid << "/io" << std::endl;
    }

    std::string line1;
    while (std::getline(ioFile, line1))
    {
        if (line1.find("read_bytes:") == 0)
        {
            std::istringstream ioStream(line1);
            std::string key;
            ioStream >> key >> currentInfo.readBytes;
        }
        else if (line1.find("write_bytes:") == 0)
        {
            std::istringstream ioStream(line1);
            std::string key;
            ioStream >> key >> currentInfo.writeBytes;
        }
    }
    ioFile.close();

    auto now = std::chrono::steady_clock::now();
    if (prevIOData.find(pid) != prevIOData.end())
    {
        auto prevTime = prevIOData[pid].first;
        auto prevInfo = prevIOData[pid].second;

        double timeDiff = std::chrono::duration<double>(now - prevTime).count(); // 秒

        if (timeDiff > 0)
        {
            double readRate = (currentInfo.readBytes - prevInfo.readBytes) / timeDiff;
            double writeRate = (currentInfo.writeBytes - prevInfo.writeBytes) / timeDiff;
            double temp = (readRate + writeRate) / 1024;
            std::ostringstream stream;
            stream << std::fixed << std::setprecision(2) << temp;
            ioUsage = stream.str();
        }
    }
    else
    {
        CV_DEBUG(g_DSFDRSourceMonitorLog, "首次采样进程 %d，IO 速率数据为0", pid);
        // std::cout << "首次采样进程 " << pid << "，无 IO 速率数据。" << std::endl;
        ioUsage = "0";
    }
    // 更新存储的历史 IO 数据
    prevIOData[pid] = {now, currentInfo};

    // TOTEST获取线程数量
    std::ifstream status_file("/proc/" + std::to_string(pid) + "/status");
    std::string line2;
    int threads = -1;

    while (std::getline(status_file, line2))
    {
        if (line2.find("Threads:") == 0)
        {
            sscanf(line2.c_str(), "Threads: %d", &threads);
            break;
        }
    }
    threadCount = std::to_string(threads);

    return true;
}

// 获取系统的 磁盘使用情况旧的
int32_t SourceMonitor::getDiskUsage(std::string &space, std::string &percent)
{
    std::string command = "df -h / | grep / | awk '{print $3\" \"$5}'";
    FILE *pipe = popen(command.c_str(), "r");
    if (!pipe)
    {
        space = "Error";
        percent = "Error";
        return -1;
    }

    char buffer[128];
    std::string result;
    while (fgets(buffer, sizeof(buffer), pipe) != nullptr)
    {
        result += buffer;
    }
    pclose(pipe);

    result.erase(result.find_last_not_of(" \n\r\t") + 1);

    // 分割结果到两个字符串中
    size_t pos = result.find(" ");
    if (pos != std::string::npos)
    {
        space = result.substr(0, pos);
        percent = result.substr(pos + 1);
    }

    return 0;
}

bool checkConfigChange(std::string configPath)
{
    if (!std::filesystem::is_directory(configPath))
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Error: config file path is not directory: %s", configPath.c_str());
        return false;
    }

    // 静态变量，存储上一次的修改时间
    static std::filesystem::file_time_type lastModTime;
    static std::string lastModTimeStr;
    // 获取当前修改时间
    auto currentModTime = std::filesystem::last_write_time(configPath);
    std::string currentModTimeStr = GetCurrentTime();

    // 如果是第一次调用，初始化并返回 true（认为有修改）
    static bool firstCall = true;
    if (firstCall)
    {
        lastModTime = currentModTime;
        lastModTimeStr = currentModTimeStr;
        CV_DEBUG(g_DSFDRSourceMonitorLog, "config directory change firstModTime is %s", lastModTimeStr.c_str());
        firstCall = false;
        return true;
    }

    // 比较当前修改时间和上一次记录的时间
    if (currentModTime != lastModTime)
    {
        CV_DEBUG(g_DSFDRSourceMonitorLog, "config directory change lastModTime is %s", lastModTimeStr.c_str(), "----currentModTime is %s", currentModTimeStr.c_str());
        lastModTime = currentModTime; // 更新记录
        lastModTimeStr = currentModTimeStr;
        return true; // 被修改过
    }
    else
    {
        return false; // 未被修改
    }
}

bool checkNGVSChange(std::string configPath)
{
    if (!std::filesystem::is_directory(configPath + SM_NGVS_DIRECTORY_NAME))
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Error: dsfngvs file path is not directory: %s", (configPath + SM_NGVS_DIRECTORY_NAME).c_str());
        return false;
    }

    // 构造 NGVS 文件夹的完整路径
    std::string NGVSDirectoryPath = configPath + SM_NGVS_DIRECTORY_NAME;
    // 静态变量，存储上一次的修改时间
    static std::filesystem::file_time_type lastModTime;
    static std::string lastModTimeStr;
    // 获取当前修改时间
    auto currentModTime = std::filesystem::last_write_time(NGVSDirectoryPath);
    std::string currentModTimeStr = GetCurrentTime();

    // 如果是第一次调用，初始化并返回 true（认为有修改）
    static bool firstCall = true;
    if (firstCall)
    {
        lastModTime = currentModTime;
        lastModTimeStr = currentModTimeStr;
        CV_DEBUG(g_DSFDRSourceMonitorLog, "NGVS change firstModTime is %s", lastModTimeStr.c_str());
        firstCall = false;
        return true;
    }

    // 比较当前修改时间和上一次记录的时间
    if (currentModTime != lastModTime)
    {
        CV_DEBUG(g_DSFDRSourceMonitorLog, "NGVS change lastModTime is %s", lastModTimeStr.c_str(), "----currentModTime is %s", currentModTimeStr.c_str());
        lastModTime = currentModTime; // 更新记录
        lastModTimeStr = currentModTimeStr;
        return true; // 被修改过
    }
    else
    {
        return false; // 未被修改
    }
}

// 获取本节点的相关信息
// 1. 获取本机的冗余情况以及对应IP
// 2. 获取本节点DSFSTATION名称
bool getNodeCfg(const std::string filePath, std::vector<std::string> &nodeList, std::string &nodeName, std::string &nodeSequence)
{
    try
    {
        std::string RMCfgPath = filePath + SM_RM_CFG_XML_NAME;
        nodeList.clear();
        // 加载 XML 文件

        TiXmlDocument doc1(RMCfgPath.c_str());
        if (!doc1.LoadFile())
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, EC_ICV_RMSERVICE_CONFIG_FILE_INVALID, "Error: Failed to load XML file: %s ", RMCfgPath.c_str());
            return false;
        }

        // 2.Get Root Element.
        TiXmlElement *pElmRoot;
        pElmRoot = doc1.FirstChildElement(RM_SVC_CFG_XML_ROOT_NODE);
        CHECK_NULL_RETURN_ERR(pElmRoot);

        // 3.Load Sequnce.
        CHECK_NULL_RETURN_ERR(pElmRoot->Attribute(RM_SVC_CFG_XML_SEQUENCE_ATTR));
        nodeSequence = pElmRoot->Attribute(RM_SVC_CFG_XML_SEQUENCE_ATTR);

        // 定位到 PeerList 节点
        TiXmlElement *peerListElement = doc1.FirstChildElement(RM_SVC_CFG_XML_ROOT_NODE)
                                            ->FirstChildElement(RM_SVC_CFG_XML_PEER_COMM_NODE)
                                            ->FirstChildElement(RM_SVC_CFG_XML_PEER_LIST_NODE);
        if (!peerListElement)
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, EC_ICV_RMSERVICE_CONFIG_FILE_STRUCTURE_ERROR, "Error: PeerList element not found in XML file.");
            return false;
        }

        // 遍历 Peer 节点
        for (TiXmlElement *peerElement = peerListElement->FirstChildElement(RM_SVC_CFG_XML_PEER_NODE); peerElement != nullptr; peerElement = peerElement->NextSiblingElement(RM_SVC_CFG_XML_PEER_NODE))
        {
            TiXmlElement *ipElement = peerElement->FirstChildElement(RM_SVC_CFG_XML_IP_NODE);
            if (ipElement && ipElement->GetText())
            {
                nodeList.push_back(ipElement->GetText());
            }
        }

        std::string dsfSettingPath = filePath + SM_DSF_SETTING_XML_NAME;
        // 加载 XML 文件
        TiXmlDocument doc2(dsfSettingPath.c_str());
        if (!doc2.LoadFile())
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, EC_ICV_RMSERVICE_CONFIG_FILE_INVALID, "Error: Failed to load XML file: %s", dsfSettingPath.c_str());
            return false;
        }

        // 定位到 DSF 节点
        TiXmlElement *dsfElement = doc2.FirstChildElement(DSF_NODE);
        if (!dsfElement)
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Error: dsf element not found in XML file.");
            return false;
        }

        // 获取 DSF 节点的名称
        nodeName = dsfElement->Attribute(DSF_NODE_NAME);
        if (nodeName.empty())
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Error: get DSF node name failed");
            return false;
        }
    }
    catch (const std::exception &e)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Exception: %s", e.what());
    }
    catch (...)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Unknown error occurred while parsing XML file.");
    }

    return true;
}

// 辅助函数：不区分大小写的字符串比较
bool TopicIsExist(const std::string &a, SourceMonitor::TopicInfo &b)
{
    // 1. 将a转换为大写以便不区分大小写比较
    std::string upperA = a;
    std::transform(upperA.begin(), upperA.end(), upperA.begin(),
                   [](unsigned char c)
                   { return std::toupper(c); });

    // 2. 构建预期的匹配字符串
    std::string expectedTopic = b.SendNGVSName + "_" + b.SendIPAddress;

    // 3. 将预期字符串转换为大写
    std::transform(expectedTopic.begin(), expectedTopic.end(), expectedTopic.begin(),
                   [](unsigned char c)
                   { return std::toupper(c); });

    // 4. 比较两个字符串
    std::cout << "环网上存在的（经过大小写处理: " << upperA << "------------map里缓存的: " << expectedTopic << std::endl;
    return upperA == expectedTopic;
}

// 获取当前时间的字符串表示，精确到毫秒，不包含日期
std::string GetCurrentTime()
{
    auto now = std::chrono::system_clock::now();
    auto in_time_t = std::chrono::system_clock::to_time_t(now);
    auto milliseconds = std::chrono::duration_cast<std::chrono::milliseconds>(now.time_since_epoch()) % 1000;

    std::stringstream ss;
    ss << std::put_time(std::localtime(&in_time_t), "%H:%M:%S");
    ss << '.' << std::setfill('0') << std::setw(3) << milliseconds.count();
    return ss.str();
}
