
/*  Filename:    SourceMonitor.cpp
 *  Copyright:   Shanghai Baosight Software Co., Ltd.
 *
 *  Description: Define  common  structure, function and macro
 *
 *  @author:     lijiayuan
 *	@version     12/20/2024	lijiayuan	Initial Version
 **************************************************************/

#include "SourceMonitor.h"
#include "Monitor_ngvs_config.h"
#include "NetworkUtils.h"
#include "SourceMonitorDef.h"
#include "common.h"
#include "common/CVLog.h"
#include "common/RMAPI.h"
#include "common/cvGlobalHelper.h"
#include "mongoose.h"
#include <future>
#include <nlohmann/json.hpp>
#include <sys/stat.h>
#include <unordered_map>

std::string ttime;
using json = nlohmann::json;

// 全局变量
extern CCVLog g_DSFDRSourceMonitorLog;
DSFSourceMonitor *g_pServiceHandler = new DSFSourceMonitor();
std::unordered_map<std::string, std::string> ESTopicNameToRealTopicName;
std::unordered_map<std::string, SourceMonitor::TopicInfo> g_TopicInfoFromNGVS; // 这里的first不是前端下发的TopicName，是真实的realTopicName，如果前端下发了名称需要做一个转换才能进来查询
std::string g_strConfigPath = CVComm.GetCVProjCfgPath();
std::string g_strExePath = CVComm.GetCVEnv();
std::string g_strLicensePath = g_strExePath + "/config/license.xml";
constexpr char REDIS_DEFAULT_UNIX_SOCKNAME[] = "redis.sock";

// 函数前置声明
int32 GetTopicStatusFromRedis(std::string topicName, std::string realTopicName, redisContext *context);

// 读取XML配置文件
Config readConfig(const std::string &filename)
{
    Config config;
    tinyxml2::XMLDocument doc;
    try
    {
        if (doc.LoadFile(filename.c_str()) != tinyxml2::XML_SUCCESS)
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Failed to load config file!!!");
        }

        auto root = doc.FirstChildElement("config");
        if (root)
        {
            auto db = root->FirstChildElement("database");
            if (db)
            {
                config.retention_days = db->FirstChildElement("retention_days")->IntText();
            }

            auto monitoring = root->FirstChildElement("monitoring");
            if (monitoring)
            {
                config.system_interval = monitoring->FirstChildElement("system_interval")->IntText();
                config.process_interval = monitoring->FirstChildElement("process_interval")->IntText();
            }
        }
    }
    catch (const std::exception &e)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "readConfig wrong is %s", e.what());
    }
    catch (...)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "LoadConfig failed,unknow exception");
    }

    return config;
}

// 数据库文件名
const std::string DB_FILE = "../projects/defaultproject/log/Dsf_Source_History.db";

// 初始化数据库并创建表
void DSFSourceMonitor::init_db()
{
    char *err_msg = nullptr;

    // 打开数据库
    if (sqlite3_open(DB_FILE.c_str(), &m_db) != SQLITE_OK)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Can't open database: %s", sqlite3_errmsg(m_db));
        sqlite3_close(m_db);
        return;
    }

    // 创建所需的三个表(包含每一列的表头)
    const char *sql = R"(
        CREATE TABLE IF NOT EXISTS SysSource (
            Timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            CpuUsage TEXT,
            MemUsage TEXT,
            DiskUsage TEXT
        );

        CREATE TABLE IF NOT EXISTS ProcessSource (
            ProcessName TEXT,
            Pid INTEGER,
            Timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            CpuUsage TEXT,
            MemUsage TEXT,
            IOUsage TEXT,
            ThreadCount TEXT,
            Status TEXT
        );

        CREATE TABLE IF NOT EXISTS DriverSource (
            DriverName TEXT,
            Pid INTEGER,
            Timestamp DATETIME DEFAULT CURRENT_TIMESTAMP,
            CpuUsage TEXT,
            MemUsage TEXT,
            IOUsage TEXT,
            ThreadCount TEXT,
            Status TEXT
        );
    )";

    if (sqlite3_exec(m_db, sql, nullptr, nullptr, &err_msg) != SQLITE_OK)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "SQL create table error: %s", err_msg);
        sqlite3_free(err_msg);
        sqlite3_close(m_db);
        return;
    }

    sqlite3_close(m_db);
}

enum class SQLType
{
    READ, // 查询
    WRITE // 插入/更新/删除
};

// 线程安全的队列
std::queue<std::tuple<SQLType, std::string, std::promise<std::string>>> sqlQueue;
std::queue<std::string> resultQueue; // 存储查询结果
std::mutex queueMutex;
std::condition_variable queueCondVar;

void DSFSourceMonitor::dataWriteThread()
{
    // 打开数据库
    if (sqlite3_open(DB_FILE.c_str(), &m_db) != SQLITE_OK)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Can't open database: %s", sqlite3_errmsg(m_db));
        return;
    }
    while (!m_bStop)
    {
        std::unique_lock<std::mutex> lock(queueMutex);
        queueCondVar.wait(lock, [this]
                          { return m_bStop || !sqlQueue.empty(); });
        if (m_bStop)
            break;

        // 取出任务
        auto [type, query, promise] = std::move(sqlQueue.front());
        sqlQueue.pop();
        // using TaskType = std::tuple<SQLType, std::string, std::promise<std::string>>;
        // TaskType task = std::move(sqlQueue.front());      // 获取队列头部任务
        // sqlQueue.pop();
        // SQLType type = std::get<0>(task);  // 第一个元素是SQLType
        // std::string query = std::get<1>(task);
        // auto promise = std::move(std::get<2>(task));

        lock.unlock();

        if (type == SQLType::READ)
        {
            // 处理 SELECT 查询
            sqlite3_stmt *stmt;
            json resultJson;

            if (sqlite3_prepare_v2(m_db, query.c_str(), -1, &stmt, nullptr) == SQLITE_OK)
            {
                while (sqlite3_step(stmt) == SQLITE_ROW)
                {
                    std::string timestamp = reinterpret_cast<const char *>(sqlite3_column_text(stmt, 0));
                    double cpuUsage = sqlite3_column_double(stmt, 1);
                    double memUsage = sqlite3_column_double(stmt, 2);
                    double ioUsage = sqlite3_column_double(stmt, 3);
                    int netUsage = sqlite3_column_int(stmt, 4);

                    // 添加到 JSON 结构
                    resultJson["timestamps"].push_back(timestamp);
                    resultJson["cpu_usage"].push_back(cpuUsage);
                    resultJson["memory_usage"].push_back(memUsage);
                    resultJson["io_usage"].push_back(ioUsage);
                    resultJson["thread_count"].push_back(netUsage);
                }
                sqlite3_finalize(stmt);
            }
            else
            {
                resultJson["error"] = sqlite3_errmsg(m_db);
            }
            // 传递 JSON 结果
            promise.set_value(resultJson.dump());
        }

        else if (type == SQLType::WRITE)
        {
            // 处理 INSERT/UPDATE/DELETE
            char *errMsg = nullptr;
            if (sqlite3_exec(m_db, query.c_str(), nullptr, nullptr, &errMsg) == SQLITE_OK)
            {
                promise.set_value("0"); // 成功返回 0
            }
            else
            {
                sqlite3_free(errMsg);
                promise.set_value("-1"); // 失败返回 -1
            }
        }
    }
    // 关闭数据库
    sqlite3_close(m_db);
    CV_DEBUG(g_DSFDRSourceMonitorLog, "Closed database successfully");
}

// // 写入系统相关状态的历史记录(写入DB) 返回0表明写入数据库正确, 1表明有问题,看打印结果查找问题
// int32 writeSystemData()
// {
//     std::string sql;
//     SourceMonitor monitor;
//     SourceMonitor::SystemSource sysinfo;

//     int32 nRet = monitor.getSystemInfo(sysinfo);
//     if (nRet != 0)
//     {
//         CV_ERROR(g_DSFDRSourceMonitorLog, nRet, "get system info failed!!!");
//         return 1;
//     }

//     sql = "INSERT INTO SysSource (Timestamp, CpuUsage, MemUsage, DiskUsage) VALUES ('" + sysinfo.time_stamp + "', '"
//     + sysinfo.cpu_usage + "', '" + sysinfo.mem_usage + "', '" + sysinfo.disk_space + " (" + sysinfo.disk_percent +
//     ")')";

//     // 将待写入的sql语句放入队列
//     std::lock_guard<std::mutex> lock(queueMutex);
//     sqlQueue.push(sql);
//     queueCondVar.notify_one();

//     return 0;
// }

// 写入进程相关状态的历史记录(写入DB)
std::map<std::string, SourceMonitor::ProcessSource> g_ProcessRealTimeInfo;
std::map<std::string, SourceMonitor::DriverSource> g_DriverRealTimeInfo;
int32 writeProcessData()
{
    std::string sql;
    std::vector<ProcessInfo> ProcessInfoList;

    sql = "INSERT INTO ProcessSource (ProcessName, Pid, Timestamp, CpuUsage, MemUsage, IOUsage, ThreadCount, Status) "
          "VALUES ";
    int32 lRet = PMRAPI_GetProcessInfo("127.0.0.1", 1000, ProcessInfoList);
    if (lRet != 0)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, lRet, "write Process Data ProcessManager Get ProcessList Failed!!!");
        return 1;
    }
    if (ProcessInfoList.size() == 0) // none of processes in config
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "None of processes in config, Please configure it correctly!!!");
        return 1;
    }
    for (int i = 0; i < ProcessInfoList.size(); ++i)
    {

        // 先获取名字和Pid
        SourceMonitor::ProcessSource processRealTimeInfo;
        processRealTimeInfo.ProcessName = ProcessInfoList[i].moduleName;
        processRealTimeInfo.Pid = SourceMonitor::getProcessID(processRealTimeInfo.ProcessName);

        if (processRealTimeInfo.Pid == -1)
        {
            // 获取当前时间戳
            SourceMonitor::getCurTime(processRealTimeInfo.Timestamp);
            // 获取直接将内容设置为空
            processRealTimeInfo.CpuUsage = " ";
            processRealTimeInfo.MemUsage = " ";
            processRealTimeInfo.IOUsage = " ";
            processRealTimeInfo.ThreadCount = " ";
            // 状态由进程管理器获取，此处先自行设置，YES表示正常启动，NO表示异常未启动
            processRealTimeInfo.Status = "NO";
        }
        else
        {
            // 获取当前时间戳
            SourceMonitor::getCurTime(processRealTimeInfo.Timestamp);

            // 获取进程详细信息
            SourceMonitor::getProcessRealTimeInfo(processRealTimeInfo.Pid, processRealTimeInfo.CpuUsage,
                                                  processRealTimeInfo.MemUsage, processRealTimeInfo.IOUsage,
                                                  processRealTimeInfo.ThreadCount);

            processRealTimeInfo.Status = "YES";
        }

        // 更新维护最新的系统进程详细信息
        g_ProcessRealTimeInfo[processRealTimeInfo.ProcessName] = processRealTimeInfo;
        if (i > 0)
            sql += ",";
        sql += "('" + processRealTimeInfo.ProcessName + "', " + std::to_string(processRealTimeInfo.Pid) + ", '" +
               processRealTimeInfo.Timestamp + "', '" + processRealTimeInfo.CpuUsage + "', '" +
               processRealTimeInfo.MemUsage + "', '" + processRealTimeInfo.IOUsage + "', '" +
               processRealTimeInfo.ThreadCount + "','" + processRealTimeInfo.Status + "')";
    }
    sql += ";";

    std::promise<std::string> promise;
    std::future<std::string> future = promise.get_future();

    {
        std::lock_guard<std::mutex> lock(queueMutex);
        sqlQueue.emplace(SQLType::WRITE, std::move(sql), std::move(promise));
        queueCondVar.notify_one();
    }

    return std::stoi(future.get());
}

// 写入驱动相关状态的历史记录(写入DB)
int32 writeDriverData()
{
    std::string sql;
    int count = 0;

    std::map<std::string, DRV_STATUS_E> drvsInfoMap;
    sql = "INSERT INTO DriverSource (DriverName, Pid, Timestamp, CpuUsage, MemUsage, IOUsage, ThreadCount, Status) "
          "VALUES ";
    if (!GetStatusDrvAll(drvsInfoMap))
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Get all driver status failed!!!");
    }
    if (drvsInfoMap.size() == 0) // none of drivers in config
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "None of drivers in config, Please configure it correctly!!!");
    }
    for (const auto &entry : drvsInfoMap)
    {
        SourceMonitor::DriverSource driverInfo;
        // 先获取名字和Pid
        driverInfo.DriverName = entry.first;

        // 该进程运行正常
        if (entry.second == 1)
        {
            driverInfo.Pid = SourceMonitor::getProcessID(driverInfo.DriverName);
            // 获取当前时间戳
            SourceMonitor::getCurTime(driverInfo.Timestamp);

            // 获取Cpu和Mem
            SourceMonitor::getProcessRealTimeInfo(driverInfo.Pid, driverInfo.CpuUsage, driverInfo.MemUsage,
                                                  driverInfo.IOUsage, driverInfo.ThreadCount);

            // 获取状态
            driverInfo.Status = drvStatusToString(entry.second);
        }

        // 该进程不存在，要记录但记录为空，status为-1
        else
        {
            // 获取当前时间戳
            SourceMonitor::getCurTime(driverInfo.Timestamp);
            // 获取直接将内容设置为空
            driverInfo.Pid = -1;
            driverInfo.CpuUsage = " ";
            driverInfo.MemUsage = " ";
            driverInfo.IOUsage = " ";
            driverInfo.ThreadCount = " ";
            // 状态由驱动管理器获取，此处先自行设置，YES表示正常启动，NO表示异常未启动
            driverInfo.Status = drvStatusToString(entry.second);
        }
        g_DriverRealTimeInfo[driverInfo.DriverName] = driverInfo;
        if (count > 0)
            sql += ",";
        sql += "('" + driverInfo.DriverName + "', " + std::to_string(driverInfo.Pid) + ", '" + driverInfo.Timestamp +
               "', '" + driverInfo.CpuUsage + "', '" + driverInfo.MemUsage + "', ' " + driverInfo.IOUsage + "', '" +
               driverInfo.ThreadCount + "','" + driverInfo.Status + "')";
        count++;
    }
    sql += ";";

    // 将待写入的sql语句放入队列
    std::promise<std::string> promise;
    std::future<std::string> future = promise.get_future();

    {
        std::lock_guard<std::mutex> lock(queueMutex);
        sqlQueue.emplace(SQLType::WRITE, std::move(sql), std::move(promise));
        queueCondVar.notify_one();
    }

    return std::stoi(future.get());
}

// // 保留配置天数内的历史记录，超出时限的记录定时清除
void cleanOldData(int retention_days)
{
    // 计算7天前的时间戳
    std::time_t now = std::time(nullptr);
    now -= (retention_days * 60); // 转换为秒
    std::stringstream ss;
    ss << std::put_time(std::localtime(&now), "%Y-%m-%d %H:%M:%S");
    std::string old_timestamp = ss.str();
    // 删除旧数据的SQL语句
    std::string sql;
    // sql = "DELETE FROM SysSource WHERE Timestamp < '" + old_timestamp + "';";
    sql = "DELETE FROM ProcessSource WHERE Timestamp < '" + old_timestamp + "'; ";
    sql += "DELETE FROM DriverSource WHERE Timestamp < '" + old_timestamp + "';";
    // 将待写入的sql语句放入队列
    std::promise<std::string> promise;
    std::future<std::string> future = promise.get_future();

    {
        std::lock_guard<std::mutex> lock(queueMutex);
        sqlQueue.emplace(SQLType::WRITE, std::move(sql), std::move(promise));
        queueCondVar.notify_one();
    }
}

// 获取系统的详细信息并生成 JSON 格式 (实时获取传回前端restful服务)
int32 getSysInfo(std::string &sys_info)
{
    // 获取实时系统资源信息返回给前端
    SourceMonitor monitor;
    SourceMonitor::SystemSource info;
    json sysObj;
    if (!monitor.getSystemInfo(info))
    {
        // 添加各个字段到 JSON 对象
        sysObj["cpu"] = info.cpu_usage;
        sysObj["memory"] = info.mem_usage;

        // 构造网络速率字符串
        sysObj["rx_rate"] = info.network_rx;
        sysObj["tx_rate"] = info.network_tx;
        std::string disk_used = info.disk_space + " used " + info.disk_percent + " used";
        sysObj["disk"] = disk_used;

        // 生成 JSON 字符串
        sys_info = sysObj.dump();
    }
    else
    {
        sysObj["status"] = "error";
        sys_info = sysObj.dump();
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "获取实时系统资源信息异常");
        return -1;
    }
    return 0;
}

// std::set<std::string> RawTopic;
// using namespace eprosima::fastdds::dds;
// using eprosima::fastrtps::types::ReturnCode_t;

// DSFTopicMonitor::DSFTopicMonitor() : mp_participant(nullptr), mp_subscriber(nullptr), m_listener(this)
// {
// }

// bool DSFTopicMonitor::init()
// {
//     // Do not enable entities on creation
//     DomainParticipantFactoryQos factory_qos;
//     factory_qos.entity_factory().autoenable_created_entities = false;
//     DomainParticipantFactory::get_instance()->set_qos(factory_qos);

//     DomainParticipantQos pqos;

//     // 黄灿哥给的plc封装的一些配置
//     eprosima::fastrtps::rtps::Locator_t locator;
//     locator.kind = LOCATOR_KIND_UDPv4;
//     locator.port = 8100;
//     eprosima::fastrtps::rtps::IPLocator::setIPv4(locator, "***********");
//     pqos.wire_protocol().default_multicast_locator_list.push_back(locator);
//     pqos.wire_protocol().builtin.metatrafficMulticastLocatorList.push_back(locator);
//     pqos.name("Participant_sub");
//     pqos.transport().use_builtin_transports = true; // 启用默认的传输

//     StatusMask par_mask = StatusMask::subscription_matched() << StatusMask::data_available();
//     mp_participant = DomainParticipantFactory::get_instance()->create_participant(1, pqos, &m_listener, par_mask);

//     if (mp_participant == nullptr)
//     {
//         return false;
//     }
//     if (mp_participant->enable() != ReturnCode_t::RETCODE_OK)
//     {
//         DomainParticipantFactory::get_instance()->delete_participant(mp_participant);
//         return false;
//     }

//     // CREATE THE COMMON READER ATTRIBUTES
//     qos_ = DATAREADER_QOS_DEFAULT;
//     qos_.reliability().kind = RELIABLE_RELIABILITY_QOS;

//     return true;
// }

// DSFTopicMonitor::~DSFTopicMonitor()
// {
//     for (const auto &it : topics_)
//     {
//         mp_subscriber->delete_datareader(it.first);
//         mp_participant->delete_topic(it.second);
//     }
//     if (mp_subscriber != nullptr)
//     {
//         mp_participant->delete_subscriber(mp_subscriber);
//     }

//     DomainParticipantFactory::get_instance()->delete_participant(mp_participant);
//     topics_.clear();
//     readers_.clear();
//     datas_.clear();
// }

// void DSFTopicMonitor::SubListener::on_subscription_matched(DataReader *, const SubscriptionMatchedStatus &info)
// {
//     if (info.current_count_change == 1)
//     {
//         n_matched = info.total_count;
//         std::cout << "Subscriber matched" << std::endl;
//     }
//     else if (info.current_count_change == -1)
//     {
//         n_matched = info.total_count;
//         std::cout << "Subscriber unmatched" << std::endl;
//     }
//     else
//     {
//         std::cout << info.current_count_change
//                   << " is not a valid value for SubscriptionMatchedStatus current count change" << std::endl;
//     }
// }

// void DSFTopicMonitor::SubListener::on_data_available(DataReader *reader)
// {
//     auto dit = subscriber_->datas_.find(reader);

//     if (dit != subscriber_->datas_.end())
//     {
//         eprosima::fastrtps::types::DynamicData_ptr data = dit->second;
//         SampleInfo info;
//         if (reader->take_next_sample(data.get(), &info) == ReturnCode_t::RETCODE_OK)
//         {
//             if (info.instance_state == ALIVE_INSTANCE_STATE)
//             {
//                 eprosima::fastrtps::types::DynamicType_ptr type = subscriber_->readers_[reader];
//                 this->n_samples++;
//                 std::cout << "Received data of type " << type->get_name() << std::endl;
//                 eprosima::fastrtps::types::DynamicDataHelper::print(data);
//             }
//         }
//     }
// }

// void DSFTopicMonitor::SubListener::on_type_discovery(DomainParticipant *,
//                                                      const eprosima::fastrtps::rtps::SampleIdentity &,
//                                                      const eprosima::fastrtps::string_255 &topic_name,
//                                                      const eprosima::fastrtps::types::TypeIdentifier *,
//                                                      const eprosima::fastrtps::types::TypeObject *,
//                                                      eprosima::fastrtps::types::DynamicType_ptr dyn_type)
// {
//     // std::cout << "Discovered type: " << dyn_type->get_name() << " from topic " << topic_name << std::endl;
//     std::map<eprosima::fastrtps::types::MemberId, eprosima::fastrtps::types::DynamicTypeMember *> members;
//     dyn_type->get_all_members(members);
//     // for (auto &temp : members)
//     // {
//     //     std::cout << temp.first << std::endl;
//     // }
//     RawTopic.insert(topic_name.to_string());
//     received_type_ = dyn_type;
//     reception_flag_.store(true);
//     types_cv_.notify_one();
// }

// void DSFTopicMonitor::run()
// {
//     std::cout << "Subscriber running." << std::endl;
//     std::unique_lock<std::mutex> lock(m_listener.types_mx_);
//     // 等待 2 秒，如果 2 秒内条件满足，则继续执行；否则，超时返回
//     if (m_listener.types_cv_.wait_for(lock, std::chrono::seconds(2),
//                                       [&]()
//                                       { return m_listener.reception_flag_.load(); }))
//     {
//     }
//     else
//     {
//         // 超时，将 flag 置为 true 并退出
//         m_listener.reception_flag_.store(true);
//         CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Timeout, none of related topic exiting.");
//         // std::cout << "Timeout, setting flag to true and exiting." << std::endl;
//     }
//     std::this_thread::sleep_for(std::chrono::seconds(1));
// }

// void DSFTopicMonitor::run(uint32_t number)
// {
//     std::cout << "Subscriber running until " << number << "samples have been received" << std::endl;
//     std::unique_lock<std::mutex> lock(m_listener.types_mx_);
//     m_listener.types_cv_.wait(lock, [&]()
//                               { return m_listener.reception_flag_.exchange(false); });
//     while (number > this->m_listener.n_samples)
//     {
//         std::this_thread::sleep_for(std::chrono::milliseconds(500));
//     }
// }

// // 将环网上的rawtopic解析获取发送方的topicName获取发送方名称的详细信息
// void getSendTopicName(std::vector<std::string> &SendTopicName)
// {
//     DSFTopicMonitor mysub;
//     if (mysub.init())
//     {
//         mysub.run();
//     }
//     for (auto &temp : RawTopic)
//     {
//         std::string tempName;
//         // 找到最后一个下划线的位置
//         size_t last_underscore = temp.rfind('_');
//         if (last_underscore == std::string::npos)
//         {
//             CV_ERROR(g_DSFDRSourceMonitorLog, -1, "getSendTopicName: RowTopicName is Changeed or Invalid! Please checkout!");
//             return;
//         }

//         // 找到倒数第二个下划线的位置
//         size_t second_last_underscore = temp.rfind('_', last_underscore - 1);
//         if (second_last_underscore == std::string::npos)
//         {
//             CV_ERROR(g_DSFDRSourceMonitorLog, -1, "getSendTopicName: RowTopicName is Changeed or Invalid! Please checkout!");
//             return;
//         }

//         // // 找到倒数第三个下划线的位置
//         // size_t third_last_underscore = temp.rfind('_', second_last_underscore - 1);
//         // if (third_last_underscore == std::string::npos)
//         // {
//         //     CV_ERROR(g_DSFDRSourceMonitorLog, -1, "getSendTopicName: RowTopicName is Changeed or Invalid! Please checkout!");
//         //     return;
//         // }
//         // 提取 name
//         // tempName = temp.substr(6, third_last_underscore - 6); // 跳过 "Topic_"
//         tempName = temp.substr(6, second_last_underscore - 6); // 跳过 "Topic_"
//         SendTopicName.push_back(tempName);
//     }
// }

// 获取本地维护的节点信息返回给前端（不会出现超时情况）
void DSFSourceMonitor::getNodeProcessInfo(const std::string NodeIp, std::string &NodeInfo)
{
    json root = json::array();
    json NodeObj;
    json Processes = json::array();

    long nRMStatus = DIED;
    int32 lRet = GetRMStatus(&nRMStatus);
    if (lRet != 0)
    {
        m_curNodeProcessInfo.node_status = "";
        m_curNodeProcessInfo.node_type = "";
        CV_ERROR(g_DSFDRSourceMonitorLog, lRet, "NodeInfo Get RMStatus failed!!!");
        return;
    }
    if (nRMStatus == DIED || nRMStatus == SLAVE)
    {
        m_curNodeProcessInfo.node_status = "Stopped";
        m_curNodeProcessInfo.node_type = "Slave";
    }
    else if (nRMStatus == MASTER)
    {
        m_curNodeProcessInfo.node_status = "Running";
        m_curNodeProcessInfo.node_type = "Master";
    }

    NodeObj["node_status"] = m_curNodeProcessInfo.node_status;
    NodeObj["node_type"] = m_curNodeProcessInfo.node_type;
    NodeObj["node_name"] = m_curNodeProcessInfo.node_name;
    NodeObj["node_rm"] = m_curNodeProcessInfo.node_rm;
    NodeObj["node_rm_sequence"] = m_curNodeProcessInfo.node_rm_sequence;
    NodeObj["node_rm_ip"] = m_curNodeProcessInfo.node_rm_ip;

    for (const auto &tProcess : m_curNodeProcessInfo.process_info)
    {
        json ProcessObj;
        ProcessObj["proc_name"] = tProcess.moduleName;
        ProcessObj["proc_description"] = convertToUTF8(tProcess.dispName);
        ProcessObj["proc_type"] = tProcess.m_ntype == 1 ? "System" : "Application";
        if (tProcess.status == 1)
            ProcessObj["proc_status"] = "Running";
        else
            ProcessObj["proc_status"] = "Stopped";
        Processes.push_back(ProcessObj);
    }

    for (const auto &entry : m_drvsInfoMap)
    {
        json DriverObj;
        DriverObj["proc_name"] = entry.first;
        DriverObj["proc_description"] = convertToUTF8(m_drvsDescMap[entry.first]);
        DriverObj["proc_type"] = "Driver";
        if (drvStatusToString(entry.second) == "Start State")
            DriverObj["proc_status"] = "Running";
        else
            DriverObj["proc_status"] = "Stopped";
        Processes.push_back(DriverObj);
    }
    NodeObj["processes"] = Processes;
    root.push_back(NodeObj);

    // 将 JSON 转换为字符串
    NodeInfo = root.dump();
}

void DSFSourceMonitor::CycleGetNodeProcessInfo(CurNodeProcessInfo &curNodeProcessInfo)
{
    // 静态变量保存上一次的future
    // static std::future<void> fut_rm;
    static std::future<void> fut_proc;
    static std::future<void> fut_drv;
    static std::future<void> fut_desc;

    while (!m_bStop)
    {
        // // 1. 冗余状态
        // if (!fut_rm.valid() || fut_rm.wait_for(std::chrono::seconds(0)) == std::future_status::ready)
        // {
        //     fut_rm = std::async(std::launch::async, [this, &curNodeProcessInfo]()
        //                         {
        //         long nRMStatus = DIED;
        //         int32 lRet = GetRMStatus(&nRMStatus);
        //         if (lRet != 0) {
        //             curNodeProcessInfo.node_status = "";
        //             curNodeProcessInfo.node_type = "";
        //             CV_ERROR(g_DSFDRSourceMonitorLog, lRet, "NodeInfo Get RMStatus failed!!!");
        //             return;
        //         }
        //         if (nRMStatus == DIED || nRMStatus == SLAVE) {
        //             curNodeProcessInfo.node_status = "Stopped";
        //             curNodeProcessInfo.node_type = "Slave";
        //         } else if (nRMStatus == MASTER) {
        //             curNodeProcessInfo.node_status = "Running";
        //             curNodeProcessInfo.node_type = "Master";
        //         } });
        // }
        // 1. 进程信息
        if (!fut_proc.valid() || fut_proc.wait_for(std::chrono::seconds(0)) == std::future_status::ready)
        {
            fut_proc = std::async(std::launch::async, [this, &curNodeProcessInfo]()
                                  {
                std::vector<ProcessInfo> process_info;
                int32 lRet = PMRAPI_GetProcessInfo("127.0.0.1", 1000, process_info);
                if (lRet == 0)
                    curNodeProcessInfo.process_info = process_info;
                else
                    {
                        // curNodeProcessInfo.process_info.clear();
                        CV_ERROR(g_DSFDRSourceMonitorLog, lRet, "NodeInfo Get ProcessInfo failed!!!");
                    } });
        }
        // 2. 驱动状态
        if (!fut_drv.valid() || fut_drv.wait_for(std::chrono::seconds(0)) == std::future_status::ready)
        {
            fut_drv = std::async(std::launch::async, [this]()
                                 {
                if (!GetStatusDrvAll(m_drvsInfoMap, "127.0.0.1", 55007, 1000))
                    {
                        m_drvsInfoMap.clear();
                        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "NodeInfo Get All DrvStatus failed!!!");
                    } });
        }
        // 3. 驱动描述
        if (!fut_desc.valid() || fut_desc.wait_for(std::chrono::seconds(0)) == std::future_status::ready)
        {
            fut_desc = std::async(std::launch::async, [this]()
                                  {
                if (!GetDescriptionDrvAll(m_drvsDescMap))
                    {
                        m_drvsDescMap.clear();
                        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "NodeInfo Get All DrvDescription failed!!!");
                    } });
        }

        // 2. node_rm 相关信息（同步赋值即可）
        curNodeProcessInfo.node_name = m_nodeCfg.nodeName;
        if (SM_SINGLE_NODE_MODE_IP_COUNT == m_nodeCfg.nodeList.size())
        {
            curNodeProcessInfo.node_rm = SM_SINGLE_NODE_MODE;
            curNodeProcessInfo.node_rm_sequence = m_nodeCfg.sequence;
            curNodeProcessInfo.node_rm_ip = m_nodeCfg.nodeList[0];
        }
        else if (SM_REDUNDANT_NODE_MODE_IP_COUNT == m_nodeCfg.nodeList.size())
        {
            curNodeProcessInfo.node_rm = SM_REDUNDANT_NODE_MODE;
            curNodeProcessInfo.node_rm_sequence = m_nodeCfg.sequence;
            curNodeProcessInfo.node_rm_ip = m_nodeCfg.nodeList[0] + "/" + m_nodeCfg.nodeList[1];
        }
        else
        {
            curNodeProcessInfo.node_rm = SM_NODE_MODE_EXCEPTION;
            curNodeProcessInfo.node_rm_ip = "";
        }

        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

/**
 * @brief 转换前端下发的topic名称
 * @version 版本号（如 1.0.0）
 * <AUTHOR>
 * @date 2025/04/23
 * @param 前端下发的需要查询的topicName数组（命名空间与名称之间以::分割）
 * @return 将前端下发的需要查询的topicName数组(将::转换成.便于查询)
 */
int32 getRealTopicName(std::string &topicName)
{
    // 将前端下发的topicname中的 "::" 换为 "."
    size_t pos = 0;
    while ((pos = topicName.find("::", pos)) != std::string::npos)
    {
        topicName.replace(pos, 2, ".");
        pos += 1; // 避免无限循环（因为 "." 比 "::" 短）
    }
    std::transform(topicName.begin(), topicName.end(), topicName.begin(),
                   [](unsigned char c)
                   { return std::toupper(c); });
}

/**
 * @brief 将前端下发的topic名称在map里查询对应发送接收ip、端口信息，再判断环网上是否存在，组成json返回给前端
 * @version 版本号（如 1.0.0）
 * <AUTHOR>
 * @date 2025/04/23
 * @param SendTopicName：环网上发送方的topicName
 *        topic_info：返回给前端的topic信息
 *        body：前端下发的请求体，包含需要查询的topicName
 * @return 将前端下发的需要查询的topicName的相关信息返回
 */
int32 topicToJson(std::string &topic_info, std::string body)
{
    json root = json::array();

    // 对前端下发的topicNameList进行解析，转为vector
    std::vector<std::string> topicNamesFromES;

    auto jsonTopicNames = json::parse(body);

    if (!jsonTopicNames.contains("TopicName"))
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Json From ES is wrong!");
        json processObj;
        processObj["topic_name"] = "";
        processObj["pattern"] = "";
        processObj["pub_ip"] = "";
        processObj["pub_port"] = "";
        processObj["rec_ip"] = "";
        processObj["rec_port"] = "";
        processObj["status"] = SM_TOPIC_STATUS_INACTIVE;
        root.push_back(processObj);
        topic_info = root.dump();
        return -1;
    }

    // 连接 Redis unix socket
    const char *szRedisPath = CVComm.GetDsfRedisPath(); // 替换为实际路径
    if (NULL == szRedisPath)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Redis path is null!");
        json processObj;
        processObj["topic_name"] = "";
        processObj["pattern"] = "";
        processObj["pub_ip"] = "";
        processObj["pub_port"] = "";
        processObj["rec_ip"] = "";
        processObj["rec_port"] = "";
        processObj["status"] = SM_TOPIC_STATUS_INACTIVE;
        root.push_back(processObj);
        topic_info = root.dump();
        return -1;
    }

    bool bRedisIsConnect = false;
    std::string strRedisSockPath;
    strRedisSockPath = szRedisPath;
    strRedisSockPath += ACE_DIRECTORY_SEPARATOR_STR;
    strRedisSockPath += REDIS_DEFAULT_UNIX_SOCKNAME;

    struct timeval timeout = {3, 0}; // 3秒超时
    redisContext *context = redisConnectUnixWithTimeout(strRedisSockPath.c_str(), timeout);
    if (context == nullptr || context->err)
    {
        if (context)
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Redis connection error: %s", context->errstr);
            redisFree(context);
        }
        else
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Can't allocate redis context");
        }
        bRedisIsConnect = false;
    }
    else
    {
        bRedisIsConnect = true;
    }

    topicNamesFromES = jsonTopicNames["TopicName"].get<std::vector<std::string>>();

    for (auto &EStopicName : topicNamesFromES)
    {
        int IsMatch = 0;
        // 返回给前端的名称要和他下发的一致，因此用temp保存一下
        std::string realTopicName = EStopicName;
        getRealTopicName(realTopicName);

        // 如果前端下发的name不在NGVS文件里存在，则表明前端未下载，返回status为-2
        if (g_TopicInfoFromNGVS.find(realTopicName) == g_TopicInfoFromNGVS.end())
        {
            json processObj;
            processObj["topic_name"] = EStopicName;
            processObj["pattern"] = "";
            processObj["pub_ip"] = "";
            processObj["pub_port"] = "";
            processObj["rec_ip"] = "";
            processObj["rec_port"] = "";
            processObj["status"] = SM_TOPIC_STATUS_INEXIST;
            root.push_back(processObj);
        }
        else
        {
            // redis未连接为false
            if (!bRedisIsConnect)
            {
                CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Redis is not connected!");
                json processObj;
                processObj["topic_name"] = EStopicName;
                processObj["pattern"] = "";
                processObj["pub_ip"] = "";
                processObj["pub_port"] = "";
                processObj["rec_ip"] = "";
                processObj["rec_port"] = "";
                processObj["status"] = SM_TOPIC_STATUS_INACTIVE;
                root.push_back(processObj);
                topic_info = root.dump();
                return -1;
            }
            if (g_TopicInfoFromNGVS[realTopicName].IsPubWhenchange == true)
            {
                // 因为记录上一次收到topic的时间无法判断是否变化，所以这里直接将状态置为active
                g_TopicInfoFromNGVS[realTopicName].status = SM_TOPIC_STATUS_ACTIVE;
            }
            else
            {
                int lRet = GetTopicStatusFromRedis(EStopicName, realTopicName, context);
                if (lRet != 0)
                {
                    CV_ERROR(g_DSFDRSourceMonitorLog, lRet, "CheckTopicStatusFromRedis failed!!!");
                }
            }

            if (g_TopicInfoFromNGVS[realTopicName].RecIPAddress == "")
            {
                json processObj;
                processObj["topic_name"] = EStopicName;
                processObj["pattern"] = "Publisher";
                processObj["pub_ip"] = g_TopicInfoFromNGVS[realTopicName].SendIPAddress;
                processObj["pub_port"] = g_TopicInfoFromNGVS[realTopicName].SendPort;
                processObj["rec_ip"] = "";
                processObj["rec_port"] = "";
                processObj["status"] = g_TopicInfoFromNGVS[realTopicName].status;
                root.push_back(processObj);
            }
            else
            {
                json processObj;
                processObj["topic_name"] = EStopicName;
                processObj["pattern"] = "Receiver";
                processObj["pub_ip"] = g_TopicInfoFromNGVS[realTopicName].SendIPAddress;
                processObj["pub_port"] = g_TopicInfoFromNGVS[realTopicName].SendPort;
                processObj["rec_ip"] = g_TopicInfoFromNGVS[realTopicName].RecIPAddress;
                processObj["rec_port"] = g_TopicInfoFromNGVS[realTopicName].RecPort;
                processObj["status"] = g_TopicInfoFromNGVS[realTopicName].status;
                root.push_back(processObj);
            }
        }
    }
    if (context)
    {
        redisFree(context);
    }
    topic_info = root.dump(); // 使用 json 的 dump() 方法将 json 转为字符串
    return 0;
}
// 暂时模拟进程管理器接口和驱动管理器接口
long StartAllDrv(const char *szHostIp)
{
    std::cout << "Start all driver successfully!" << std::endl;
    return 0;
}
long StopAllDrv(const char *szHostIp)
{
    std::cout << "Stop all driver successfully!" << std::endl;
    return 0;
}

int32 processControl(std::string NodeIp, std::string Control, std::string &Message, std::string ProcessName = "",
                     std::string ProcessType = "")
{
    // 如果前端下发进程名和类型为空，则是对本节点所有进程进行启停控制
    int32 lRet;
    json root;
    if (ProcessName == "")
    {
        if (Control == "run")
        {
            // TODO:这里的全部driver的启停接口，还是模拟的，之后要替换成DrVctrl提供的。
            lRet = PMRAPI_StartAllProcess(NodeIp.c_str());
            lRet = StartAllDrv(NodeIp.c_str());
            root["status"] = "success";
            root["message"] = "All process on this node start successfully!";
        }
        else if (Control == "stop")
        {
            lRet = StopAllDrv(NodeIp.c_str());
            lRet = PMRAPI_StopAllProcess(NodeIp.c_str());
            root["status"] = "success";
            root["message"] = "All process on this node stop successfully!";
        }
        if (lRet != 0)
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, -1, "ProcessManager NodeControl Failed!!!");
            root["status"] = "failed";
            root["message"] = "Node control failed!";
            return 1;
        }
    }
    // 对某个进程进行控制
    else
    {
        if (Control == "run")
        {
            if (ProcessType == "Driver")
            {
                lRet = StartDrv(ProcessName, NodeIp.c_str());
                root["status"] = "success";
                root["message"] = ProcessType + " " + ProcessName + " start successfully!";
            }
            else
            {
                lRet = PMRAPI_StartProcess(NodeIp.c_str(), ProcessName.c_str());
                root["status"] = "success";
                root["message"] = ProcessType + " " + ProcessName + " start successfully!";
            }
        }
        else if (Control == "stop")
        {
            if (ProcessType == "Driver")
            {
                lRet = StopDrv(ProcessName, NodeIp.c_str());
                root["status"] = "success";
                root["message"] = ProcessType + " " + ProcessName + " stop successfully!";
            }
            else
            {
                lRet = PMRAPI_StopProcess(NodeIp.c_str(), ProcessName.c_str());
                root["status"] = "success";
                root["message"] = ProcessType + " " + ProcessName + " stop successfully!";
            }
        }
        if (lRet != 0)
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, -1, "ProcessManager ProcessControl Failed!!!");
            root["status"] = "failed";
            root["message"] = ProcessType + " " + ProcessName + " control successfully!";
            return 1;
        }
    }
    // 将 JSON 转换为字符串
    Message = root.dump(); // 使用 nlohmann::json 的 dump() 方法将 json 转为字符串
    return 0;
}

int32 RMSwitch(std::string &Message)
{
    // 因为更改冗余状态只下发给活动主机，非活动机被冗余服务自动拉起变为主机，完成主备切换
    long sataus = 0;
    int32 result = SetRMStatus(sataus);
    json response;
    if (result != 0)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "RedundancyService Switch Failed!!!");
        response["status"] = "failed";
        response["message"] = "switch rmstatus failed!";
        Message = response.dump();
        return 1;
    }
    CV_INFO(g_DSFDRSourceMonitorLog, "RedundancyService Switch Success!!!");
    response["status"] = "success";
    response["message"] = "switch rmstatus successfully!";
    Message = response.dump();
    return 0;
}

std::chrono::time_point<std::chrono::system_clock> GetCurrentTimePoint()
{
    return std::chrono::system_clock::now();
}

bool IsTimeIntervalGreaterThanOneSecond(const std::chrono::time_point<std::chrono::system_clock> &start,
                                        const std::chrono::time_point<std::chrono::system_clock> &end)
{
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(end - start);
    return duration.count() > 1;
}

bool IsTimeIntervalGreaterThanFiveSecond(const std::chrono::time_point<std::chrono::system_clock> &start,
                                         const std::chrono::time_point<std::chrono::system_clock> &end)
{
    auto duration = std::chrono::duration_cast<std::chrono::seconds>(end - start);
    return duration.count() > 5;
}

int32 getProcessHisSource(std::string &ProcessName, std::string &ProcessType, std::string &StartTime,
                          std::string &EndTime, std::string &IntervalTime, std::string &ProcessHisSource)
{
    // TODO 要用上IntervalTime参数
    if (ProcessName == "" || ProcessType == "" || StartTime == "" || EndTime == "")
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1,
                 "ProcessName or ProcessType or StartTime or EndTime or IntervalTime is empty!");
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "ProcessName or ProcessType is wrong!");
        json resultJson;
        resultJson = {{"timestamps", "Json from ES is wrong!"},
                      {"cpu_usage", "Json from ES is wrong!"},
                      {"memory_usage", "Json from ES is wrong!"},
                      {"io_usage", "Json from ES is wrong!"},
                      {"thread_count", "Json from ES is wrong!"}};
        ProcessHisSource = resultJson.dump();
        return 1;
    }
    std::string query;

    // 构造 SQL 语句
    if (ProcessType == "System")
    {
        query = "SELECT Timestamp, CpuUsage, MemUsage, IOUsage, ThreadCount "
                "FROM ProcessSource WHERE ProcessName = '" +
                ProcessName + "' AND Timestamp BETWEEN '" + StartTime + "' AND '" + EndTime + "' ORDER BY Timestamp;";
    }
    else if (ProcessType == "Driver")
    {
        query = "SELECT Timestamp, CpuUsage, MemUsage, IOUsage, ThreadCount "
                "FROM DriverSource WHERE DriverName = '" +
                ProcessName + "' AND Timestamp BETWEEN '" + StartTime + "' AND '" + EndTime + "' ORDER BY Timestamp;";
    }

    // 创建 promise & future
    std::promise<std::string> promise;
    std::future<std::string> future = promise.get_future();

    // 把 SQL 任务放入队列
    {
        std::lock_guard<std::mutex> lock(queueMutex);
        sqlQueue.emplace(SQLType::READ, std::move(query), std::move(promise));
        queueCondVar.notify_one();
    }

    // 等待查询结果
    ProcessHisSource = future.get();
    return 0;
}

int32 getProcessRealTimeSource(std::string &ProcessName, std::string &ProcessType, std::string &ProcessRealTimeSource)
{
    json resultJson;
    if (ProcessType == "System")
    {
        resultJson = {{"timestamps", g_ProcessRealTimeInfo[ProcessName].Timestamp},
                      {"cpu_usage", g_ProcessRealTimeInfo[ProcessName].CpuUsage},
                      {"memory_usage", g_ProcessRealTimeInfo[ProcessName].MemUsage},
                      {"io_usage", g_ProcessRealTimeInfo[ProcessName].IOUsage},
                      {"thread_count", g_ProcessRealTimeInfo[ProcessName].ThreadCount}};
    }
    else if (ProcessType == "Driver")
    {
        resultJson = {{"timestamps", g_DriverRealTimeInfo[ProcessName].Timestamp},
                      {"cpu_usage", g_DriverRealTimeInfo[ProcessName].CpuUsage},
                      {"memory_usage", g_DriverRealTimeInfo[ProcessName].MemUsage},
                      {"io_usage", g_DriverRealTimeInfo[ProcessName].IOUsage},
                      {"thread_count", g_DriverRealTimeInfo[ProcessName].ThreadCount}};
    }
    if (resultJson.empty())
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "ProcessName or ProcessType is wrong!");
        resultJson = {{"timestamps", "ProcessName or ProcessType is wrong!"},
                      {"cpu_usage", "ProcessName or ProcessType is wrong!"},
                      {"memory_usage", "ProcessName or ProcessType is wrong!"},
                      {"io_usage", "ProcessName or ProcessType is wrong!"},
                      {"thread_count", "ProcessName or ProcessType is wrong!"}};
        ProcessRealTimeSource = resultJson.dump();
        return 1;
    }
    ProcessRealTimeSource = resultJson.dump();
    return 0;
}

// 读取文件内容
std::string readFileContent(const std::string &filePath)
{
    std::ifstream file(filePath);
    if (!file.is_open())
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "readFileContent: Can not open license.xml")
        return "";
    }
    std::stringstream buffer;
    buffer << file.rdbuf();
    return buffer.str();
}

int32 LicUpload(std::string &Message)
{
    // 读取 XML 文件内容
    long lRet = g_pServiceHandler->CheckLicense();
    // 构建 JSON 响应
    json jsonResponse;
    if (lRet == CV_LICENSE_NONE_LICENSE)
    {
        jsonResponse["status"] = "NONE_LICENSE";
        jsonResponse["content_type"] = "";
        jsonResponse["file_name"] = "";
        jsonResponse["content"] = "";
        jsonResponse["message"] = "";
    }
    else
    {
        std::string xmlContent = readFileContent(g_strLicensePath);

        jsonResponse["status"] = "success";
        jsonResponse["content_type"] = "application/xml";
        jsonResponse["file_name"] = "license.xml";
        jsonResponse["content"] = xmlContent;
        jsonResponse["message"] = "license upload success!";
    }

    // 将 JSON 转换为字符串
    Message = jsonResponse.dump();
    return 0;
}

int32 checkTempLicenseFromES(std::string &tempLicenseStatus)
{
    long lRet = g_pServiceHandler->CheckLicense();
}

// 先判断原有license是否存在，不存在则直接保存前端下发的license，若存在则先将原有license改为licensebak.xml，
// 保存前端下发license，将该license进行校验，如果校验是NORMOL和LIMITIED，则覆盖，即删除原有licensebak.xml，
// 若校验是INVALID或者OUT_OF_DATE，都不覆盖，删除当前license.xml并且将原有licensebak.xml改回license.xml，

int32 LicDownload(std::string FileContent, std::string &Message)
{
    std::string licenseBakPath = g_strExePath + "/config/licensebak.xml";
    bool isExist = CVFileHelper::IsFileExist(g_strLicensePath.c_str());
    if (isExist)
    {
        // 如果 license.xml 存在，则先将其重命名为 licensebak.xml
        std::rename(g_strLicensePath.c_str(), licenseBakPath.c_str());
    }
    // 然后保存前端下发的 license
    std::ofstream outFile(g_strLicensePath);
    if (!outFile.is_open())
    {
        Message = "Failed to open file: " + g_strLicensePath;
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, Message.c_str());
        return -1; // 返回错误码
    }
    outFile << FileContent;
    outFile.close();

    // 检查写入是否成功
    if (outFile.fail())
    {
        Message = "Failed to write to file: " + g_strLicensePath;
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, Message.c_str());
        return -1; // 返回错误码
    }

    // 校验 license
    long lRet = g_pServiceHandler->CheckLicense();
    json jsonResponse;

    switch (lRet)
    {
    case CV_LICENSE_NORMAL:
        if (isExist)
            std::remove(licenseBakPath.c_str());
        jsonResponse = {{"status", "NORMAL"}, {"message", "license download success"}};
        break;
    case CV_LICENSE_LIMITED:
        if (isExist)
            std::remove(licenseBakPath.c_str());
        jsonResponse = {{"status", "LIMITED"}, {"message", "license download success"}};
        break;
    case CV_LICENSE_INVALID:
        jsonResponse = {{"status", "INVALID"}, {"message", "license download refused"}};
        std::remove(g_strLicensePath.c_str());
        if (isExist)
            std::rename(licenseBakPath.c_str(), g_strLicensePath.c_str());
        Message = jsonResponse.dump();
        return -2;
    case CV_LICENSE_OUT_OF_DATE:
        jsonResponse = {{"status", "OUT_OF_DATE"}, {"message", "license download refused"}};
        std::remove(g_strLicensePath.c_str());
        if (isExist)
            std::rename(licenseBakPath.c_str(), g_strLicensePath.c_str());
        Message = jsonResponse.dump();
        return -2; // 返回错误码
    }

    Message = jsonResponse.dump();
    return 0; // 返回成功码
}

int32 DownloadXML(std::string &fileContent)
{
    // 指定要下载的 XML 文件路径
    std::string LicFilePath = "./config/license.xml";

    // 打开文件
    std::ifstream xmlFile(LicFilePath);
    if (!xmlFile.is_open())
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "cannot open license.xml")
        return -1;
    }

    // 读取文件内容
    std::stringstream buffer;
    buffer << xmlFile.rdbuf();
    fileContent = buffer.str();
    xmlFile.close();
    return 0;
}

int32 GetDSFRVersion(std::string &Version)
{
    const std::string versionFilePath = g_strExePath + DSF_VERSION_FILE_NAME;
    std::ifstream file(versionFilePath);
    if (!file.is_open())
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, EC_DSF_SM_OPEN_FILE_FAILED, "Error: Unable to open file: %s", versionFilePath.c_str());
        return EC_DSF_SM_OPEN_FILE_FAILED; // 返回错误码
    }
    if (std::getline(file, Version))
    {
        file.close();
        return EC_DSF_SM_SUCCESS; // 返回正确码
    }
    else
    {
        file.close();
        CV_ERROR(g_DSFDRSourceMonitorLog, EC_DSF_SM_READ_FILE_ERROR, "Error: Failed to read version from file: %s", versionFilePath.c_str());
        return EC_DSF_SM_READ_FILE_ERROR;
    }
}

bool handleConnectionErrors(const std::string &operation, redisContext *context)
{
    if (context == nullptr || context->err)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog,
                 EC_DSF_SM_REDIS_GET_COMMOND_ERROR,
                 "Redis error during operation '%s': %s",
                 operation,
                 (context ? context->errstr : "Unknown error"));
        return false;
    }
    return true;
}

int32 GetTopicStatusFromRedis(std::string EStopicName, std::string realTopicName, redisContext *context)
{
    // TODO 用topicName组装出redis中key的格式，然后将读出的时间和现在的时间作差
    try
    {
        std::string nodeName = g_pServiceHandler->getNodeName();
        std::string key = nodeName + SM_TOPIC_STATUS_TAG_PREFIX + realTopicName + SM_TOPIC_STATUS_TAG_SUFFIX;
        redisReply *reply =
            (redisReply *)redisCommand(context, "GET %s", key.c_str());
        if (reply == nullptr || context->err)
        {
            handleConnectionErrors("GET", context);
            g_TopicInfoFromNGVS[realTopicName].status = SM_TOPIC_STATUS_INACTIVE;
            return EC_DSF_SM_REDIS_GET_COMMOND_ERROR;
        }

        // 新增类型检查
        if (reply->type == REDIS_REPLY_NIL)
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, EC_DSF_SM_REDIS_KEY_NOT_EXIST, "Get Topic Status Failed!!! Key '%s' not found in Redis", key.c_str());
            g_TopicInfoFromNGVS[realTopicName].status = SM_TOPIC_STATUS_INACTIVE;
            freeReplyObject(reply);
            return EC_DSF_SM_REDIS_KEY_NOT_EXIST; // 或返回其他默认值
        }

        // 确保是字符串类型且 str 不为空
        if (reply->type != REDIS_REPLY_STRING || reply->str == nullptr)
        {
            CV_ERROR(g_DSFDRSourceMonitorLog, EC_DSF_SM_REDIS_WRONG_VALUE_TYPE, "Invalid reply type or empty string");
            g_TopicInfoFromNGVS[realTopicName].status = SM_TOPIC_STATUS_INACTIVE;
            freeReplyObject(reply);
            return EC_DSF_SM_REDIS_WRONG_VALUE_TYPE;
        }

        uint64_t value = 0;
        memcpy(&value, reply->str, sizeof(uint64_t));
        FREE_REDIS_REPLY(reply);
        auto now = std::chrono::system_clock::now();
        auto curTimeMs = std::chrono::duration_cast<std::chrono::milliseconds>(
                             now.time_since_epoch())
                             .count();

        // 计算阈值
        uint64_t threshold = (g_TopicInfoFromNGVS[realTopicName].PubcycleInterval * 2 <= 1000) ? 1000 : g_TopicInfoFromNGVS[realTopicName].PubcycleInterval * 2;

        // 计算时间差（使用整数运算）
        uint64_t timeDiff = (curTimeMs > value) ? (curTimeMs - value) : (value - curTimeMs);
        // 判断状态，如果topic发送/接收周期小于500ms，则查询的时候当前时间与最后一次发送/接收时间相差1s以上，则判断该topic非活跃
        if (timeDiff > threshold)
        {
            g_TopicInfoFromNGVS[realTopicName].status = SM_TOPIC_STATUS_INACTIVE;
            CV_DEBUG(g_DSFDRSourceMonitorLog, "Topic %s Last Update Time is %llu, now is %llu, time diff is %llu, threshold is %llu",EStopicName.c_str(), value, curTimeMs, timeDiff, threshold);
        }
        else
        {
            g_TopicInfoFromNGVS[realTopicName].status = SM_TOPIC_STATUS_ACTIVE;
            CV_DEBUG(g_DSFDRSourceMonitorLog, "Topic %s Last Update Time is %llu, now is %llu, time diff is %llu, threshold is %llu",EStopicName.c_str(), value, curTimeMs, timeDiff, threshold);
        }
        return 0;
    }
    catch (const std::exception &e)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "GetTopicStatusFromRedis wrong is %s", e.what());
    }
    catch (...)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "GetTopicStatusFromRedis failed,unknow exception");
    }
}

// HTTP 事件处理器
static void ev_handler(struct mg_connection *c, int ev, void *ev_data)
{
    if (ev == MG_EV_HTTP_MSG)
    {
        struct mg_http_message *hm = (struct mg_http_message *)ev_data;

        if (mg_match(hm->uri, mg_str("/Monitor/ProcessSource"), NULL))
        {
            string ProcessRealTimeSource;
            std::string body(hm->body.buf, hm->body.len);
            CV_DEBUG(g_DSFDRSourceMonitorLog, "/Monitor/ProcessSource GET request JSON from ES is %s", body.c_str());
            std::string ProcessName;
            std::string ProcessType;

            auto ESJsonStr = json::parse(body);
            if (!ESJsonStr.contains("ProcessName") || !ESJsonStr.contains("ProcessType"))
            {
                // TODO json中不包含这个值就报错
                CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Invalid JSON request.");
                ProcessName = "";
                ProcessType = "";
                json resultJson;
                resultJson = {{"timestamps", "error JSON request"},
                              {"cpu_usage", "error JSON request"},
                              {"memory_usage", "error JSON request"},
                              {"io_usage", "error JSON request"},
                              {"thread_count", "error JSON request"}};
                ProcessRealTimeSource = resultJson.dump();
                mg_http_reply(c, 200,
                              "Content-Type: application/json\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "%s\n", ProcessRealTimeSource.c_str());
                CV_DEBUG(g_DSFDRSourceMonitorLog, "ProcessRealTimeSource is %s", ProcessRealTimeSource.c_str());
            }
            else
            {
                ProcessName = ESJsonStr["ProcessName"].get<std::string>();
                ProcessType = ESJsonStr["ProcessType"].get<std::string>();
                if (!getProcessRealTimeSource(ProcessName, ProcessType, ProcessRealTimeSource))
                {
                    // 返回 JSON 响应
                    mg_http_reply(c, 200,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", ProcessRealTimeSource.c_str());
                    CV_DEBUG(g_DSFDRSourceMonitorLog, "ProcessRealTimeSource is %s", ProcessRealTimeSource.c_str());
                }
                else
                {
                    // 返回 JSON 响应
                    mg_http_reply(c, 500,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", ProcessRealTimeSource.c_str());
                    CV_ERROR(g_DSFDRSourceMonitorLog, -1, ProcessRealTimeSource.c_str());
                }
            }
        }

        // 返回指定时间内、指定进程的所有历史信息
        else if (mg_match(hm->uri, mg_str("/Monitor/ProcessHisSource"), NULL))
        {
            std::string body(hm->body.buf, hm->body.len);
            CV_DEBUG(g_DSFDRSourceMonitorLog, "/Monitor/ProcessHisSource GET request JSON from ES is %s", body.c_str());
            std::string ProcessName;
            std::string ProcessType;
            std::string StartTime;
            std::string EndTime;
            std::string IntervalTime;
            // 解析 JSON 请求体
            auto ESJsonStr = json::parse(body);
            if (!ESJsonStr.contains("ProcessName") || !ESJsonStr.contains("ProcessType") ||
                !ESJsonStr.contains("StartTime") || !ESJsonStr.contains("EndTime") ||
                !ESJsonStr.contains("IntervalTime"))
            {
                // TODO json中不包含这个值就报错
                CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Invalid JSON request.");
                ProcessName = "";
                ProcessType = "";
                StartTime = "";
                EndTime = "";
                IntervalTime = "";
            }
            else
            {
                ProcessName = ESJsonStr["ProcessName"].get<std::string>();
                ProcessType = ESJsonStr["ProcessType"].get<std::string>();
                StartTime = ESJsonStr["StartTime"].get<std::string>();
                EndTime = ESJsonStr["EndTime"].get<std::string>();
                IntervalTime = ESJsonStr["IntervalTime"].get<std::string>();
            }

            std::string ProcessHisSource;
            if (!getProcessHisSource(ProcessName, ProcessType, StartTime, EndTime, IntervalTime, ProcessHisSource))
            {
                // 返回 JSON 响应
                mg_http_reply(c, 200,
                              "Content-Type: application/json\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "%s\n", ProcessHisSource.c_str());
                CV_DEBUG(g_DSFDRSourceMonitorLog, "ProcessHisSource is %s", ProcessHisSource.c_str());
            }
            else
            { // 返回 JSON 响应
                mg_http_reply(c, 500,
                              "Content-Type: application/json\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "%s\n", ProcessHisSource.c_str());
                CV_ERROR(g_DSFDRSourceMonitorLog, -1, ProcessHisSource.c_str());
            }
        }

        else if (mg_match(hm->uri, mg_str("/Monitor/SysSource"), NULL))
        {
            // 获取系统信息
            std::string sys_info;
            getSysInfo(sys_info);
            // 返回 JSON 响应
            mg_http_reply(c, 200,
                          "Content-Type: application/json\r\n"
                          "Access-Control-Allow-Origin: *\r\n",
                          "%s\n", sys_info.c_str());
            CV_DEBUG(g_DSFDRSourceMonitorLog, "sys_info is %s", sys_info.c_str());
        }

        else if (mg_match(hm->uri, mg_str("/Monitor/TopicInfo"), NULL))
        {
            if (mg_strcasecmp(hm->method, mg_str("POST")) == 0)
            {
                std::string body(hm->body.buf, hm->body.len);
                CV_DEBUG(g_DSFDRSourceMonitorLog, "/Monitor/TopicInfo POST request JSON from ES is %s", body.c_str());
                std::string topic_info;

                // // 获取环网上的Topic名称
                // std::vector<std::string> SendTopicName;
                // getSendTopicName(SendTopicName);

                // 7.15 TODO 前端查询的时候，去redis中查询对应的点获取最后一次得到该topic的时间，与现在的时间作差，得到前端TopicName和其对应的status
                if (!topicToJson(topic_info, body))
                {
                    mg_http_reply(c, 200,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", topic_info.c_str());
                    CV_DEBUG(g_DSFDRSourceMonitorLog, "topic_info is %s", topic_info.c_str());
                }
                else
                {
                    mg_http_reply(c, 400,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", topic_info.c_str());
                    CV_DEBUG(g_DSFDRSourceMonitorLog, "topic_info is %s", topic_info.c_str());
                }
            }
            else
            {
                mg_http_reply(c, 404,
                              "Content-Type: application/json\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "{\"error\": \"Not a POST request!\"}\n");
            }
        }
        else if (mg_match(hm->uri, mg_str("/Monitor/NodeStatus"), NULL))
        {
            if (mg_strcasecmp(hm->method, mg_str("POST")) == 0)
            {
                // ttime=GetCurrentTime();
                // std::cout<<"节点状态监视start time is "<<ttime<<std::endl;

                std::string body(hm->body.buf, hm->body.len);
                CV_DEBUG(g_DSFDRSourceMonitorLog, "/Monitor/NodeStatus POST request JSON from ES is %s", body.c_str());
                // 对前端下发的NodeIP进行解析，转为string
                std::string NodeIp;

                auto ESJsonStr = json::parse(body);
                if (!ESJsonStr.contains("NodeIp"))
                {
                    // TODO json中不包含这个值就报错
                    CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Invalid JSON request.");
                    NodeIp = "";
                }
                else
                {
                    NodeIp = ESJsonStr["NodeIp"].get<std::string>();
                }

                std::string NodeInfo;
                g_pServiceHandler->getNodeProcessInfo(NodeIp, NodeInfo);

                mg_http_reply(c, 200,
                              "Content-Type: application/json\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "%s\n", NodeInfo.c_str());
                CV_DEBUG(g_DSFDRSourceMonitorLog, "NodeInfo is %s", NodeInfo.c_str());
            }
            else
            {
                mg_http_reply(c, 404,
                              "Content-Type: application/json\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "{\"error\": \"Not a POST request!\"}\n");
                CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Not a POST request!");
            }
        }

        else if (mg_match(hm->uri, mg_str("/Monitor/NodeControl"), NULL))
        {
            if (mg_strcasecmp(hm->method, mg_str("POST")) == 0)
            {
                std::string body(hm->body.buf, hm->body.len);
                CV_DEBUG(g_DSFDRSourceMonitorLog, "/Monitor/NodeControl POST request JSON from ES is %s", body.c_str());
                std::string NodeIp;
                std::string ProcessName;
                std::string ProcessType;
                std::string Control;
                auto ESJsonStr = json::parse(body);
                if (!ESJsonStr.contains("NodeIp") || !ESJsonStr.contains("ProcessName") ||
                    !ESJsonStr.contains("ProcessType") || !ESJsonStr.contains("Control"))
                {
                    // TODO json中不包含这个值就报错
                    CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Invalid JSON request.");
                    NodeIp = "";
                    ProcessName = "";
                    ProcessType = "";
                    Control = "";
                }
                else
                {
                    NodeIp = ESJsonStr["NodeIp"].get<std::string>();
                    ProcessName = ESJsonStr["ProcessName"].get<std::string>();
                    ProcessType = ESJsonStr["ProcessType"].get<std::string>();
                    Control = ESJsonStr["Control"].get<std::string>();
                }

                std::string Message;
                int32 lRet = processControl(NodeIp, Control, Message, ProcessName, ProcessType);
                if (lRet != 0)
                {
                    mg_http_reply(c, 404,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", Message.c_str());
                    CV_ERROR(g_DSFDRSourceMonitorLog, -1, Message.c_str());
                }
                else
                {
                    mg_http_reply(c, 200,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", Message.c_str());
                    CV_DEBUG(g_DSFDRSourceMonitorLog, "Message is %s", Message.c_str());
                }
            }
            else
            {
                mg_http_reply(c, 404,
                              "Content-Type: application/json\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "{\"error\": \"Not a POST request!\"}\n");
                CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Not a POST request!");
            }
        }

        else if (mg_match(hm->uri, mg_str("/Monitor/RMSwitch"), NULL))
        {
            if (mg_strcasecmp(hm->method, mg_str("GET")) == 0)
            {
                std::string Message;
                int32 lRet = RMSwitch(Message);
                if (lRet != 0)
                {
                    mg_http_reply(c, 404,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", Message.c_str());
                    CV_ERROR(g_DSFDRSourceMonitorLog, -1, Message.c_str());
                }
                else
                {
                    mg_http_reply(c, 200,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", Message.c_str());
                    CV_DEBUG(g_DSFDRSourceMonitorLog, "Message is %s", Message.c_str());
                }
            }
            else
            {
                mg_http_reply(c, 404,
                              "Content-Type: application/json\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "{\"error\": \"Not a GET request!\"}\n");
                CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Not a GET request!");
            }
        }

        else if (mg_match(hm->uri, mg_str("/Monitor/LicUpload"), NULL))
        {
            if (mg_strcasecmp(hm->method, mg_str("GET")) == 0)
            {
                std::string body(hm->body.buf, hm->body.len);
                CV_DEBUG(g_DSFDRSourceMonitorLog, "/Monitor/LicUpload GET request JSON from ES is %s", body.c_str());
                std::string Message;

                int32 lRet = LicUpload(Message);

                if (lRet != 0)
                {
                    mg_http_reply(c, 404,
                                  "Content-Type: application/json; charset=utf-8\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", Message.c_str());
                    CV_ERROR(g_DSFDRSourceMonitorLog, -1, Message.c_str());
                }
                else
                {
                    mg_http_reply(c, 200,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", Message.c_str());
                    CV_DEBUG(g_DSFDRSourceMonitorLog, "Message is %s", Message.c_str());
                }
            }
            else
            {
                mg_http_reply(c, 404,
                              "Content-Type: application/json\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "{\"error\": \"Not a GET request!\"}\n");
                CV_ERROR(g_DSFDRSourceMonitorLog, -1, "/Monitor/LicUpload Not a GET request!");
            }
        }

        else if (mg_match(hm->uri, mg_str("/Monitor/LicDownload"), NULL))
        {
            if (mg_strcasecmp(hm->method, mg_str("POST")) == 0)
            {
                std::string body(hm->body.buf, hm->body.len);
                CV_DEBUG(g_DSFDRSourceMonitorLog, "/Monitor/LicDownload POST request JSON from ES is %s", body.c_str());
                std::string Message, FileContent;
                auto ESJsonStr = json::parse(body);
                if (!ESJsonStr.contains("FileContent"))
                {
                    // TODO json中不包含这个值就报错
                    CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Invalid JSON request.");
                    FileContent = "";
                }
                else
                {
                    FileContent = ESJsonStr["FileContent"].get<std::string>();
                }

                // license先下发，再校验
                int32 lRet = LicDownload(FileContent, Message);

                if (lRet == 0)
                {
                    // 下载许可证正常并校验完毕，通知进程重启服务
                    long ret = PMRAPI_Init();
                    ret = PMRAPI_RestartAllProcess("127.0.0.1");
                    mg_http_reply(c, 200,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", Message.c_str());
                    CV_DEBUG(g_DSFDRSourceMonitorLog, "Message is %s", Message.c_str());
                }
                else if (lRet == -2)
                {
                    // 下载的许可证非法或者过期
                    mg_http_reply(c, 200,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", Message.c_str());
                    CV_DEBUG(g_DSFDRSourceMonitorLog, "Message is %s", Message.c_str());
                }
                else
                {
                    json jsonResponse = {{"status", "failed"}, {"message", "license download failed"}};
                    Message = jsonResponse.dump();
                    mg_http_reply(c, 500,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", Message.c_str());
                    CV_ERROR(g_DSFDRSourceMonitorLog, -1, Message.c_str());
                }
            }
            else
            {
                mg_http_reply(c, 404,
                              "Content-Type: application/json\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "{\"error\": \"Not a POST request!\"}\n");
                CV_ERROR(g_DSFDRSourceMonitorLog, -1, "/Monitor/LicDownload Not a GET request!");
            }
        }

        else if (mg_match(hm->uri, mg_str("/Monitor/Validate"), NULL))
        {
            if (mg_strcasecmp(hm->method, mg_str("GET")) == 0)
            {
                std::string body(hm->body.buf, hm->body.len);
                CV_DEBUG(g_DSFDRSourceMonitorLog, "/Monitor/Validate GET request JSON from ES is %s", body.c_str());
                std::string Message;
                long lRet = static_cast<DSFSourceMonitor *>(g_pServiceHandler)->CheckLicense();
                json jsonResponse;
                switch (lRet)
                {
                case CV_LICENSE_NORMAL:
                    jsonResponse = {{"status", "NORMAL"}, {"message", "license check success"}};
                    break;
                case CV_LICENSE_INVALID:
                    jsonResponse = {{"status", "INVALID"}, {"message", "license check success"}};
                    break;
                case CV_LICENSE_OUT_OF_DATE:
                    jsonResponse = {{"status", "OUT_OF_DATE"}, {"message", "license check success"}};
                    break;
                case CV_LICENSE_LIMITED:
                    jsonResponse = {{"status", "LIMITED"}, {"message", "license check success"}};
                    break;
                case CV_LICENSE_NONE_LICENSE:
                    jsonResponse = {{"status", "NONE_LICENSE"}, {"message", "license check success"}};
                    break;
                }

                Message = jsonResponse.dump();
                if (lRet < 0 || lRet > 4)
                {
                    mg_http_reply(c, 404,
                                  "Content-Type: application/json; charset=utf-8\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", Message.c_str());
                    CV_ERROR(g_DSFDRSourceMonitorLog, -1, Message.c_str());
                }
                else
                {
                    mg_http_reply(c, 200,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "%s\n", Message.c_str());
                    CV_DEBUG(g_DSFDRSourceMonitorLog, "Message is %s", Message.c_str());
                }
            }
            else
            {
                mg_http_reply(c, 404,
                              "Content-Type: application/json\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "{\"error\": \"Not a GET request!\"}\n");
                CV_ERROR(g_DSFDRSourceMonitorLog, -1, "/Monitor/Validate Not a GET request!");
            }
        }

        else if (mg_match(hm->uri, mg_str("/Monitor/DownloadXML"), NULL))
        {
            if (mg_strcasecmp(hm->method, mg_str("GET")) == 0)
            {
                std::string fileContent;
                DownloadXML(fileContent);

                // 返回文件内容作为 HTTP 响应
                mg_http_reply(c, 200,
                              "Content-Type: application/xml\r\n"
                              "Content-Disposition: attachment; filename=\"license.xml\"\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "%s", fileContent.c_str());
                CV_DEBUG(g_DSFDRSourceMonitorLog, "Message is %s", fileContent.c_str());
            }
            else
            {
                // 如果不是 GET 请求，返回错误信息
                mg_http_reply(c, 404,
                              "Content-Type: application/json\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "{\"error\": \"Not a GET request!\"}\n");
                CV_ERROR(g_DSFDRSourceMonitorLog, -1, "/Monitor/DownloadXML Not a GET request!");
            }
        }

        else if (mg_match(hm->uri, mg_str("/Monitor/GetDSFRVersion"), NULL))
        {
            if (mg_strcasecmp(hm->method, mg_str("GET")) == 0)
            {
                std::string Version, Message;
                int32 lRet = GetDSFRVersion(Version);
                if (ICV_SUCCESS != lRet)
                {
                    // 如果获取版本失败，返回错误信息
                    mg_http_reply(c, SM_HTTP_STATUS_NOT_FOUND,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "{\"error\": \"Failed to get DSF version!\"}\n");
                    CV_ERROR(g_DSFDRSourceMonitorLog, -1, "Failed to get DSF version!");
                    return;
                }
                try
                {
                    json response;
                    response["Version"] = Version;
                    Message = response.dump();
                }
                catch (const std::exception &e)
                {
                    // 如果 JSON 解析失败，返回错误信息
                    mg_http_reply(c, SM_HTTP_STATUS_INTERNAL_ERROR,
                                  "Content-Type: application/json\r\n"
                                  "Access-Control-Allow-Origin: *\r\n",
                                  "{\"error\": \"Failed to parse version!\"}\n");
                    CV_ERROR(g_DSFDRSourceMonitorLog, SM_HTTP_STATUS_INTERNAL_ERROR, "Failed to parse version: %s", e.what());
                    return;
                }
                // 返回文件内容作为 HTTP 响应
                mg_http_reply(c, SM_HTTP_STATUS_OK,
                              "Content-Type: application/xml\r\n"
                              "Content-Disposition: attachment; filename=\"license.xml\"\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "%s", Message.c_str());
                CV_DEBUG(g_DSFDRSourceMonitorLog, "/Monitor/GetDSFRVersion Message is %s", Message.c_str());
            }
            else
            {
                // 如果不是 GET 请求，返回错误信息
                mg_http_reply(c, SM_HTTP_STATUS_NOT_FOUND,
                              "Content-Type: application/json\r\n"
                              "Access-Control-Allow-Origin: *\r\n",
                              "{\"error\": \"Not a GET request!\"}\n");
                CV_ERROR(g_DSFDRSourceMonitorLog, SM_HTTP_STATUS_NOT_FOUND, "/Monitor/GetDSFRVersion Not a GET request!");
            }
        }

        else
        {
            // 如果不是 /api/processes 请求，返回 404 错误
            mg_http_reply(c, 404,
                          "Content-Type: application/json\r\n"
                          "Access-Control-Allow-Origin: *\r\n",
                          "{\"error\": \"Not Found\"}\n");
        }
    }
}

DSFSourceMonitor::DSFSourceMonitor() : CServiceBase("DSFSourceMonitor", false, "DSFSourceMonitor")
{
    ExitWhenInitFailed();
}

long DSFSourceMonitor::Init(int argc, char *args[])
{
    return 0;
}

void DSFSourceMonitor::Refresh()
{
}

long DSFSourceMonitor::CheckLicense()
{
    // TODO
    if (!CVFileHelper::IsFileExist(g_strLicensePath.c_str()))
    {
        CV_INFO(g_DSFDRSourceMonitorLog, "NO License.xml!");
        return 4;
    }
    TCV_TimeStamp timeValid;
    std::unique_ptr<CLicChecker> LicChecker = std::make_unique<CLicChecker>(); // 自动管理内存
    return LicChecker->GetTimeValid(timeValid);
}

void DSFSourceMonitor::DRSourceMonitorStop()
{
    m_bStop = true;
    queueCondVar.notify_all();
}

long DSFSourceMonitor::SourceMonitorInit()
{
    std::thread t1(&DSFSourceMonitor::SourceMonitorStart, this);
    t1.detach();
    return 0;
}

void DSFSourceMonitor::SourceMonitorStart()

{
    // std::map<std::string, DRV_STATUS_E> drvsInfoMap;
    // auto
    // starttime2=std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count();
    // bool result = GetStatusDrvAll(drvsInfoMap, "127.0.0.1", 55007, 1000);
    // auto
    // endtime2=std::chrono::duration_cast<std::chrono::seconds>(std::chrono::system_clock::now().time_since_epoch()).count();
    // std::cout << "Get RM status cost time is: " << endtime2-starttime2 << "ss"<<result<< std::endl;
    // return;
    // DSFSourceMonitor monitor;
    const char *pCVCfgPath = CCVComm::GetInstance().GetCVProjCfgPath();
    if (NULL == pCVCfgPath)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "GetCVProjCfgPath failed");
    }
    m_strConfigFileName = std::string(pCVCfgPath) + std::string("/") + std::string("SourceMonitorConfig.xml");
    CV_INFO(g_DSFDRSourceMonitorLog, "ConfigFileName is :%s", m_strConfigFileName.c_str());
    m_config = readConfig(m_strConfigFileName);

    // 初始化数据库
    init_db();

    // 初始化进程管理API
    const char *agentIP = "127.0.0.1";
    long lRet = PMRAPI_InitEx(agentIP, 55004);
    if (lRet != 0)
    {
        CV_ERROR(g_DSFDRSourceMonitorLog, -1, "PMRAPI_InitEx failed");
    }

    // 启动 dataWriteThread 线程
    std::thread dataWrite(&DSFSourceMonitor::dataWriteThread, this);
    dataWrite.detach();
    // 创建HTTP服务器线程
    std::thread httpThread([this]()
                           {
        struct mg_mgr mgr;
        mg_mgr_init(&mgr);

        mg_http_listen(&mgr, "0.0.0.0:55020", ev_handler, NULL);

        while (!m_bStop)
        {
            mg_mgr_poll(&mgr, 1000);
        }

        mg_mgr_free(&mgr); });
    httpThread.detach();

    std::thread GetNameMapFromNGVS([this]
                                   {
        while (!m_bStop)
        {
            if(checkConfigChange(g_strConfigPath))
            {
                // 如果NGVS文件夹更新了，则调用函数更新NameMap
                if (checkNGVSChange(g_strConfigPath))
                {
                    g_TopicInfoFromNGVS.clear();
                    NgvsParse(g_strConfigPath, g_TopicInfoFromNGVS);
                    for (auto &temp : g_TopicInfoFromNGVS)
                    {
                        CV_DEBUG(g_DSFDRSourceMonitorLog, "NGVSNameFromES: %s---SendNGVSName: %s", temp.first.c_str(),
                                temp.second.SendNGVSName.c_str());
                    }
                }

                // 获取node相关配置信息
                if(getNodeCfg(g_strConfigPath, m_nodeCfg.nodeList, m_nodeCfg.nodeName, m_nodeCfg.sequence))
                {
                    CV_DEBUG(g_DSFDRSourceMonitorLog, "getNodeCfg path: %s success!", g_strConfigPath.c_str());
                    CV_DEBUG(g_DSFDRSourceMonitorLog, "NodeName: %s", m_nodeCfg.nodeName.c_str());
                    CV_DEBUG(g_DSFDRSourceMonitorLog, "NodeCount: %d", m_nodeCfg.nodeList.size());
                    CV_DEBUG(g_DSFDRSourceMonitorLog, "sequence: %s", m_nodeCfg.sequence.c_str());
                    for (auto temp : m_nodeCfg.nodeList)
                    {
                        CV_DEBUG(g_DSFDRSourceMonitorLog, "NodeIp: %s", temp.c_str());
                    }
                }
                else
                {
                    CV_ERROR(g_DSFDRSourceMonitorLog, -1, "getNodeCfg path: %s failed!", g_strConfigPath.c_str());
                }
            }
            std::this_thread::sleep_for(std::chrono::milliseconds(500));
        } });
    GetNameMapFromNGVS.detach();

    std::thread([this]()
                { CycleGetNodeProcessInfo(m_curNodeProcessInfo); })
        .detach();

    // auto lastSysTime = std::chrono::steady_clock::now();
    auto lastProcTime = std::chrono::steady_clock::now();
    auto lastDrvTime = std::chrono::steady_clock::now();
    auto lastCleanTime = std::chrono::steady_clock::now();

    while (!m_bStop)
    {
        auto now = std::chrono::steady_clock::now();
        // if (std::chrono::duration_cast<std::chrono::seconds>(now - lastSysTime).count() >= m_config.system_interval)
        // {
        //     writeSystemData();
        //     lastSysTime = now;
        // }

        if (std::chrono::duration_cast<std::chrono::seconds>(now - lastProcTime).count() >= m_config.process_interval)
        {
            writeProcessData();
            lastProcTime = now;
        }

        if (std::chrono::duration_cast<std::chrono::seconds>(now - lastDrvTime).count() >= m_config.process_interval)
        {
            writeDriverData();
            lastDrvTime = now;
        }

        if (std::chrono::duration_cast<std::chrono::seconds>(now - lastCleanTime).count() >= m_config.retention_days
        * 24 * 60 * 60)
        {
            cleanOldData(m_config.retention_days);
            lastCleanTime = now;
        }

        std::this_thread::sleep_for(std::chrono::seconds(1));
    }
}

long DSFSourceMonitor::Start()
{
    SourceMonitorInit();
    return 0;
}

void DSFSourceMonitor::PrintStartUpScreen()
{
    PrintHelpScreen();
}

void DSFSourceMonitor::PrintHelpScreen()
{
    printf("\n");
    printf("+=================================================================+"
           "\n");
    printf("|              <<Welcome to DSFSourceMonitor! >>                    "
           "|\n");
    printf("|  You can configure this service by entering following commands  "
           "|\n");
    printf("|                                                                 "
           "|\n");
    printf("|  q/Q: Quit                                                      "
           "|\n");
    printf("|  Others: Print tips                                             "
           "|\n");
    printf("+=================================================================+"
           "\n");
}

long DSFSourceMonitor::Fini()
{
    // 清理资源
    PMRAPI_FiniEx();
    sqlite3_close(m_db);
    g_DSFDRSourceMonitorLog.StopLogThread();
    DRSourceMonitorStop();
    return ICV_SUCCESS;
}

bool DSFSourceMonitor::ProcessCmd(char c)
{
    return 0;
}

void DSFSourceMonitor::Misc()
{
}

void DSFSourceMonitor::SetLogFileName()
{
    g_DSFDRSourceMonitorLog.SetLogFileNameThread("DSFSourceMonitor");
}
