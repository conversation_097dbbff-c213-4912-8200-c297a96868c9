#ifndef DSFTopicMonitor_H_
#define DSFTopicMonitor_H_

#include <fastdds/dds/domain/DomainParticipant.hpp>
#include <fastdds/dds/domain/DomainParticipantListener.hpp>
#include <fastdds/dds/subscriber/DataReader.hpp>
#include <fastdds/dds/subscriber/qos/DataReaderQos.hpp>
#include <fastrtps/subscriber/SampleInfo.h>
#include <fastrtps/rtps/common/Types.h>
#include <fastdds/dds/domain/DomainParticipantFactory.hpp>
#include <fastdds/dds/subscriber/SampleInfo.hpp>
#include <fastdds/dds/subscriber/Subscriber.hpp>
#include <fastrtps/attributes/ParticipantAttributes.h>
#include <fastrtps/attributes/SubscriberAttributes.h>
#include <fastrtps/types/DynamicDataFactory.h>
#include <fastrtps/types/DynamicDataHelper.hpp>
#include <fastrtps/types/TypeIdentifier.h>
#include <fastrtps/types/TypeObject.h>

#include <atomic>
#include <condition_variable>
#include <map>
#include <chrono>
#include <csignal>
#include <cstring>
#include <ctime>
#include <dirent.h>
#include <fstream>
#include <iomanip> // 引入 iomanip 用于小数精度控制
#include <iostream>
#include <sstream>
#include <string>
#include <thread>
#include <cmath>

#include <hiredis/hiredis.h>
#include <unistd.h>
#include <vector>
#include <queue>
#include <mutex>
#include <sqlite/sqlite3.h>

#include "../DRProcessmgr/DRPmrapi/PMRAPI.h"
#include "../drivers/drvctrl/drvctrlSDK/DRDrvctrlApi.h"

#define FREE_REDIS_REPLY(reply)                                                \
  do {                                                                         \
    if (nullptr != (reply)) {                                                  \
      freeReplyObject(reply);                                                  \
    }                                                                          \
  } while (false)

class DSFTopicMonitor
{
public:

    DSFTopicMonitor();

    virtual ~DSFTopicMonitor();

    //!Initialize the subscriber
    bool init();

    //!RUN the subscriber
    void run();

    //!Run the subscriber until number samples have been received.
    void run(
            uint32_t number);

    //! Initialize all required entities for data transmission
    void initialize_entities();

private:

    eprosima::fastdds::dds::DomainParticipant* mp_participant;

    eprosima::fastdds::dds::Subscriber* mp_subscriber;

    std::map<eprosima::fastdds::dds::DataReader*, eprosima::fastdds::dds::Topic*> topics_;

    std::map<eprosima::fastdds::dds::DataReader*, eprosima::fastrtps::types::DynamicType_ptr> readers_;

    std::map<eprosima::fastdds::dds::DataReader*, eprosima::fastrtps::types::DynamicData_ptr> datas_;

    eprosima::fastrtps::SubscriberAttributes att_;

    eprosima::fastdds::dds::DataReaderQos qos_;

public:

    class SubListener
        :  public eprosima::fastdds::dds::DomainParticipantListener
    {
    public:

        SubListener(
                DSFTopicMonitor* sub)
            : n_matched(0)
            , n_samples(0)
            , subscriber_(sub)
        {
        }

        ~SubListener() override
        {
        }

        void on_data_available(
                eprosima::fastdds::dds::DataReader* reader) override;

        void on_subscription_matched(
                eprosima::fastdds::dds::DataReader* reader,
                const eprosima::fastdds::dds::SubscriptionMatchedStatus& info) override;

        void on_type_discovery(
                eprosima::fastdds::dds::DomainParticipant* participant,
                const eprosima::fastrtps::rtps::SampleIdentity& request_sample_id,
                const eprosima::fastrtps::string_255& topic,
                const eprosima::fastrtps::types::TypeIdentifier* identifier,
                const eprosima::fastrtps::types::TypeObject* object,
                eprosima::fastrtps::types::DynamicType_ptr dyn_type) override;

        int n_matched;

        uint32_t n_samples;

        std::mutex types_mx_;

        std::condition_variable types_cv_;

        eprosima::fastrtps::types::DynamicType_ptr received_type_;

        std::atomic<bool> reception_flag_{false};

        DSFTopicMonitor* subscriber_;

    }
    m_listener;

};

#endif /* DSFTopicMonitor_H_ */




#ifndef _DRSOURCEMONITOR_H_
#define _DRSOURCEMONITOR_H_

#include "common/ServiceBase.h"
#include <atomic>

// 配置结构体
struct Config
{
    int retention_days;
    int system_interval;
    int process_interval;
};

struct CurNodeProcessInfo {
    std::string node_type;
    std::string node_status;
    std::string node_name;
    int node_rm;
    std::string node_rm_sequence;
    std::string node_rm_ip;
    std::vector<ProcessInfo> process_info;
    // 清空所有成员变量
    void Clear() {
        node_type.clear();
        node_status.clear();
        node_name.clear();
        node_rm = 0;  // int 类型重置为 0
        node_rm_sequence.clear();
        node_rm_ip.clear();
        process_info.clear();  // 清空 vector
    }
};

class DSFSourceMonitor : public CServiceBase
{
private:
    struct DSFNodeCfg{
        std::string sequence;
        std::vector<std::string> nodeList;
        std::string nodeName;
    };

public:
    DSFSourceMonitor();
    std::string getNodeName(){
        return m_nodeCfg.nodeName;
    };
    void SourceMonitorStart();
    void getNodeProcessInfo(const std::string NodeIp, std::string &NodeInfo);
    void CycleGetNodeProcessInfo(CurNodeProcessInfo &curNodeProcessInfo);
    long SourceMonitorInit();
    void init_db();
    void dataWriteThread();
    long CheckLicense();
    void DRSourceMonitorStop();
    virtual long Init(int argc, char* args[]);
	virtual void Refresh();
	virtual long Start();
	virtual void PrintStartUpScreen();
	virtual void PrintHelpScreen();
	virtual long Fini();
	virtual bool ProcessCmd(char c);
	// 其它的杂项
	virtual void Misc();
	//设置日志文件名称，保证打印ServiceBase里的日志到期望的文件中
	virtual void SetLogFileName();
private:
  std::atomic<bool> m_bStop{false};
  std::string m_strConfigFileName;
  
  Config m_config;
  sqlite3 *m_db;
  DSFNodeCfg m_nodeCfg;
  CurNodeProcessInfo m_curNodeProcessInfo;
  std::map<std::string, DRV_STATUS_E> m_drvsInfoMap;
  std::map<std::string, std::string> m_drvsDescMap;
};


#endif // End of #ifndef _DRSOURCEMONITOR_H_

