#Setting Executable and Library Output Path

INCLUDE($ENV{DRDIR}AddVersionResource)
# SET(LIB_TYPE "SHARED")
set(CMAKE_EXE_LINKER_FLAGS "-no-pie")  ## License need 

IF(CMAKE_MFC_FLAG)
	IF(CMAKE_MFC_FLAG MATCHES 1)
		SET(CMAKE_CXX_FLAGS_RELEASE  "/MT /O2 /Ob2 /D NDEBUG")
		SET(CMAKE_C_FLAGS_RELEASE 	 "/MT /O2 /Ob2 /D NDEBUG")
		SET(CMAKE_CXX_FLAGS_DEBUG  "/D_DEBUG /MTd /Zi /Ob0 /Od /RTC1")
		SET(CMAKE_C_FLAGS_DEBUG   "/D_DEBUG /MTd /Zi /Ob0 /Od /RTC1")
		SET(CMAKE_CXX_FLAGS_RELWITHDEBINFO  "/MT /Zi /Od /Ob1 /D NDEBUG")
		SET(CMAKE_C_FLAGS_RELWITHDEBINFO  "/MT /Zi /Od /Ob1 /D NDEBUG")
		SET(CMAKE_CXX_FLAGS_MINSIZEREL  "/MT /O1 /Ob1 /D NDEBUG")
		SET(CMAKE_C_FLAGS_MINSIZEREL  "/MT /O1 /Ob1 /D NDEBUG")
	ELSE(CMAKE_MFC_FLAG MATCHES 1)
		ADD_DEFINITIONS(-D_AFXDLL)
	ENDIF(CMAKE_MFC_FLAG MATCHES 1)
	
	ADD_EXECUTABLE(${TARGET_NAME} WIN32 ${SRCS})
		
ELSE(CMAKE_MFC_FLAG)
IF(SUBSYSWIN)
ADD_EXECUTABLE(${TARGET_NAME} WIN32 ${SRCS})
ELSE(SUBSYSWIN)
ADD_EXECUTABLE(${TARGET_NAME} ${SRCS})
ENDIF(SUBSYSWIN)
ENDIF(CMAKE_MFC_FLAG)

# AddLinuxVersionResouce
IF(UNIX)
	SET_TARGET_PROPERTIES(${TARGET_NAME} PROPERTIES SOVERSION ${TARGET_API_VERSION} VERSION ${TARGET_VERSION} )
ENDIF(UNIX)

IF(UNIX)
	SET(LINK_LIBS ${LINK_LIBS} rt drlogimpl) #TO BE DELETED WHEN MODIFIED CVLOG!!!!!
	IF   (CMAKE_SYSTEM MATCHES "AIX*")
		SET(LINK_LIBS ${LINK_LIBS} pthread iconv)
	ELSEIF (CMAKE_SYSTEM MATCHES "SunOS.*")
		#no iconv	
	ELSE (CMAKE_SYSTEM MATCHES "AIX*")
		SET(LINK_LIBS ${LINK_LIBS} iconv)
	ENDIF(CMAKE_SYSTEM MATCHES "AIX*")
ELSE(UNIX)
	#add /MP (MultiProcessor) Option to increase building speed!
	SET_TARGET_PROPERTIES( ${TARGET_NAME} PROPERTIES COMPILE_FLAGS "/MP" ) 
ENDIF(UNIX)

TARGET_LINK_LIBRARIES(${TARGET_NAME} ${LINK_LIBS})
IF(TARGET_VERSION)
	SET_TARGET_PROPERTIES(${TARGET_NAME} PROPERTIES VERSION ${TARGET_VERSION})
ENDIF(TARGET_VERSION)

IF(MSVC)
   SET_TARGET_PROPERTIES(${TARGET_NAME} PROPERTIES LINK_FLAGS "/ignore:4099")
ENDIF(MSVC)

#INSTALL(TARGETS ${TARGET_NAME} 
#		RUNTIME DESTINATION ${CMAKE_CURRENT_BINARY_DIR}/${EXE_DIR})
