/**
 * @file NetworkUtils.h
 * @brief
 * <AUTHOR> (<EMAIL>)
 * @version 1.0
 * @date 2024-11-05
 *
 * @copyright Copyright (c) 2024  by  宝信
 *
 * @par 修改日志:
 * <table>
 * <tr><th>Date       <th>Version <th>Author  <th>Description
 * <tr><td>2024-11-05     <td>1.0     <td>wwk   <td>修改?
 * </table>
 */
#pragma once
#include <vector>
#include <string>

#ifdef _WIN32
#ifdef DLL_EXPORTS
#define DLL_API extern "C" __declspec(dllexport) 
#else
#define DLL_API extern "C" __declspec(dllimport) 
#endif
#else
#define DLL_API extern "C"
#endif

DLL_API  bool getLocalIPAddresses(std::vector<std::string> &ipAddresses);
