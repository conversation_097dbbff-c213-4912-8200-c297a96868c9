#include "NetworkUtils.h"
#include <iostream>

#ifdef _WIN32
    #include <winsock2.h>
    #include <iphlpapi.h>
    #include <ws2tcpip.h>
    #pragma comment(lib, "ws2_32.lib")
    #pragma comment(lib, "iphlpapi.lib")
#else
    #include <ifaddrs.h>
    #include <netinet/in.h>
    #include <arpa/inet.h>
    #include <cstring>
#endif

bool getLocalIPAddresses(std::vector<std::string> &ipAddresses)
{
    ipAddresses.clear(); // Clear the vector to ensure it's empty before adding new addresses.

#ifdef _WIN32
    WSADATA wsaData;
    if (WSAStartup(MAKEWORD(2, 2), &wsaData) != 0)
    {
        std::cerr << "WSAStartup failed." << std::endl;
        return false;
    }

    ULONG family = AF_INET; // Only IPv4 addresses
    ULONG flags = GAA_FLAG_INCLUDE_PREFIX;
    ULONG bufferSize = 15000;
    IP_ADAPTER_ADDRESSES *adapterAddresses = (IP_ADAPTER_ADDRESSES *)malloc(bufferSize);

    if (GetAdaptersAddresses(family, flags, nullptr, adapterAddresses, &bufferSize) == ERROR_BUFFER_OVERFLOW)
    {
        free(adapterAddresses);
        adapterAddresses = (IP_ADAPTER_ADDRESSES *)malloc(bufferSize);
    }

    if (GetAdaptersAddresses(family, flags, nullptr, adapterAddresses, &bufferSize) == NO_ERROR)
    {
        for (IP_ADAPTER_ADDRESSES *adapter = adapterAddresses; adapter != nullptr; adapter = adapter->Next)
        {
            for (IP_ADAPTER_UNICAST_ADDRESS *unicast = adapter->FirstUnicastAddress; unicast != nullptr; unicast = unicast->Next)
            {
                char ip[INET_ADDRSTRLEN];
                if (unicast->Address.lpSockaddr->sa_family == AF_INET)
                {
                    sockaddr_in *sa_in = (sockaddr_in *)unicast->Address.lpSockaddr;
                    inet_ntop(AF_INET, &(sa_in->sin_addr), ip, INET_ADDRSTRLEN);

                    // Filter out loopback addresses (127.x.x.x)
                    if (strncmp(ip, "127", 3) != 0)
                    {
                        ipAddresses.push_back(ip);
                    }
                }
            }
        }
    }
    else
    {
        free(adapterAddresses);
        WSACleanup();
        return false;
    }

    free(adapterAddresses);
    WSACleanup();
    return true;

#else // Linux / macOS
    struct ifaddrs *interfaces = nullptr;
    if (getifaddrs(&interfaces) == -1)
    {
        perror("getifaddrs");
        return false;
    }

    struct ifaddrs *temp_addr = interfaces;
    while (temp_addr != nullptr)
    {
        if (temp_addr->ifa_addr && temp_addr->ifa_addr->sa_family == AF_INET)
        { // Only IPv4 addresses
            char ip[INET_ADDRSTRLEN];
            void *addr = &((struct sockaddr_in *)temp_addr->ifa_addr)->sin_addr;
            inet_ntop(AF_INET, addr, ip, INET_ADDRSTRLEN);

            // Filter out loopback addresses (127.x.x.x)
            if (strncmp(ip, "127", 3) != 0)
            {
                ipAddresses.push_back(ip);
            }
        }
        temp_addr = temp_addr->ifa_next;
    }

    freeifaddrs(interfaces);
    return true;
#endif
}