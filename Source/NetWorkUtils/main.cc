#include "NetworkUtils.h"
#include <iostream>

int main(int argc, char* argv[]) {
    std::cout << "++++: " << argv[0] << std::endl;
    std::vector<std::string> ipAddresses;
    if (getLocalIPAddresses(ipAddresses)) {
        std::cout << "IP Addresses:" << std::endl;
        for (const auto& ip : ipAddresses) {
            std::cout << "  " << ip << std::endl;
        }
    } else {
        std::cerr << "Failed to retrieve IP addresses." << std::endl;
    }
    return 0;
}


