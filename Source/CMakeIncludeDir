SET(DRDIR $ENV{DRDIR})

INCLUDE_DIRECTORIES(${DRDIR}/ext/commonheaders/include)
INCLUDE_DIRECTORIES(${DRDIR}/ext/lzma/include)
INCLUDE_DIRECTORIES(${DRDIR}/ext/md5)
INCLUDE_DIRECTORIES(${DRDIR}/ext/tinyxml/include)
INCLUDE_DIRECTORIES(${DRDIR}/ext/netqueue/include)

INCLUDE_DIRECTORIES(${DRDIR}/common/cvcomm/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/cppsqlite/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/cviobuffer/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/cvlog/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/epass2lite/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/exceptionreport/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/libexpr/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/licverify/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/cvrpc/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/servicebase/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/sqlitepersist/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/cppunit)
INCLUDE_DIRECTORIES(${DRDIR}/common/procmngr/cvprocessmgr/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/procmngr/pmrapi/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/redundancy/rmapi/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/rmdatasync/rmdsapi/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/shmqueue/include)
INCLUDE_DIRECTORIES(${DRDIR}/common/cvsock/include)
INCLUDE_DIRECTORIES(${DRDIR}/authority/amsvrlib/include)

INCLUDE_DIRECTORIES(${DRDIR}/configcenter/ccservice/ccbaselib/include)
INCLUDE_DIRECTORIES(${DRDIR}/configcenter/ccservice/ccgenmd5/include)
INCLUDE_DIRECTORIES(${DRDIR}/configcenter/ccservice/ccpacketparser/include)

INCLUDE_DIRECTORIES(${DRDIR}/driversdk/commdrvcfgloader/include)
INCLUDE_DIRECTORIES(${DRDIR}/driversdk/cvdrvcfgloader/include)
INCLUDE_DIRECTORIES(${DRDIR}/driversdk/cvsock/include)
INCLUDE_DIRECTORIES(${DRDIR}/driversdk/cvcommunicate/include)
INCLUDE_DIRECTORIES(${DRDIR}/driversdk/cvtcp/include)
INCLUDE_DIRECTORIES(${DRDIR}/driversdk/cvudp/include)
INCLUDE_DIRECTORIES(${DRDIR}/driversdk/drvbase/devicebase/include)
INCLUDE_DIRECTORIES(${DRDIR}/driversdk/iodatasync/iodatasync/include)
INCLUDE_DIRECTORIES(${DRDIR}/driversdk/iodrvtempl/iodrvt/include)
INCLUDE_DIRECTORIES(${DRDIR}/driversdk/cvdriverframe/cvdrivercommon/include)

INCLUDE_DIRECTORIES(${DRDIR}/eventalarm/eaacceptor/include)
INCLUDE_DIRECTORIES(${DRDIR}/eventalarm/easervice/eaapi/include)

INCLUDE_DIRECTORIES(${DRDIR}/htd/htdapi/include)

INCLUDE_DIRECTORIES(${DRDIR}/multimedia/dwservice/dwapi/include)
INCLUDE_DIRECTORIES(${DRDIR}/multimedia/led/ledbaselib/include)
INCLUDE_DIRECTORIES(${DRDIR}/multimedia/videoservice/videodblibrary/include)
INCLUDE_DIRECTORIES(${DRDIR}/multimedia/dwdrv/saijing/sunkingdriver/include)

INCLUDE_DIRECTORIES(${DRDIR}/pdb/drvctrl/drvctrlrapi/include)
INCLUDE_DIRECTORIES(${DRDIR}/pdb/pbscanner/rdalib/include)
INCLUDE_DIRECTORIES(${DRDIR}/pdb/pbscanner/pdbcommon/include)
INCLUDE_DIRECTORIES(${DRDIR}/pdb/pbscanner/pdbinner/include)
INCLUDE_DIRECTORIES(${DRDIR}/pdb/pbscanner/pdbbase/include)
INCLUDE_DIRECTORIES(${DRDIR}/pdb/pbscanner/cvrda/include)
INCLUDE_DIRECTORIES(${DRDIR}/pdb/pbscanner/pbschema/include)
INCLUDE_DIRECTORIES(${DRDIR}/pdb/pbscanner/pdbengine/include)
INCLUDE_DIRECTORIES(${DRDIR}/pdb/pbscanner/lrda/include)
INCLUDE_DIRECTORIES(${DRDIR}/pdb/pbscanner/caexprlib/include)
INCLUDE_DIRECTORIES(${DRDIR}/pdb/pbscanner/almplugins/almbase/include)
INCLUDE_DIRECTORIES(${DRDIR}/pdb/pbscanner/cvbyteorder/include)
INCLUDE_DIRECTORIES(${DRDIR}/pdb/driverapi/include)

INCLUDE_DIRECTORIES(${DRDIR}/eventalarm/eaprotocol)
INCLUDE_DIRECTORIES(${DRDIR}/eventalarm/event)
INCLUDE_DIRECTORIES(${DRDIR}/../include/eventalarm)

INCLUDE_DIRECTORIES(${DRDIR}/Response/crsdblibrary/include)
INCLUDE_DIRECTORIES(${DRDIR}/Response/plugins/include)
INCLUDE_DIRECTORIES(${DRDIR}/Response/crservice)

INCLUDE_DIRECTORIES(${DRDIR}/transfer/sqltransferframe/include)

INCLUDE_DIRECTORIES(${DRDIR}/dbproxy/sqlapiwrapper/include)
INCLUDE_DIRECTORIES(${DRDIR}/../include)
INCLUDE_DIRECTORIES(${DRDIR}/../include/ihd3)
INCLUDE_DIRECTORIES(${DRDIR}/../include/common)
INCLUDE_DIRECTORIES(${DRDIR}/../include/rabbitmq)
INCLUDE_DIRECTORIES(${DRDIR}/../include/opcua/uastack)
INCLUDE_DIRECTORIES(${DRDIR}/../include/opcua/uabase)
INCLUDE_DIRECTORIES(${DRDIR}/../include/opcua/uaclient)
INCLUDE_DIRECTORIES(${DRDIR}/../include/opcua/uamodels)
INCLUDE_DIRECTORIES(${DRDIR}/../include/opcua/uapki)
INCLUDE_DIRECTORIES(${DRDIR}/../include/opcua/uaserver)
INCLUDE_DIRECTORIES(${DRDIR}/../include/opcua/xmlparser)
INCLUDE_DIRECTORIES(${DRDIR}/../include/common/os)
INCLUDE_DIRECTORIES(${DRDIR}/../include/qt)
INCLUDE_DIRECTORIES(${DRDIR}/../include/qt/QtCore)
INCLUDE_DIRECTORIES(${DRDIR}/../include/qt/QtGui)
INCLUDE_DIRECTORIES(${DRDIR}/../include/qt/QtXml)
